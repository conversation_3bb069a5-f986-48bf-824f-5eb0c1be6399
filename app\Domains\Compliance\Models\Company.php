<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * نموذج الشركة للامتثال
 * يدير بيانات الشركة المتعلقة بالامتثال القانوني
 */
class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'name_ar',
        'name_en',
        'company_type',
        'industry',
        'size',
        'registration_number',
        'tax_number',
        'vat_number',
        'ice_number', // المغرب
        'cr_number', // السعودية
        'trn_number', // الإمارات
        'cnss_number', // المغرب
        'gosi_number', // السعودية
        'trade_license',
        'address',
        'city',
        'country_code',
        'phone',
        'email',
        'website',
        'annual_revenue',
        'employee_count',
        'registration_date',
        'fiscal_year_end',
        'accounting_method',
        'currency',
        'is_active',
        'compliance_status',
        'risk_level',
        'last_compliance_check',
        'metadata',
    ];

    protected $casts = [
        'registration_date' => 'date',
        'fiscal_year_end' => 'date',
        'annual_revenue' => 'decimal:2',
        'is_active' => 'boolean',
        'last_compliance_check' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * أنواع الشركات
     */
    const COMPANY_TYPES = [
        'llc' => 'شركة ذات مسؤولية محدودة',
        'corporation' => 'شركة مساهمة',
        'partnership' => 'شراكة',
        'sole_proprietorship' => 'مؤسسة فردية',
        'branch' => 'فرع',
        'representative_office' => 'مكتب تمثيلي',
        'free_zone' => 'منطقة حرة',
    ];

    /**
     * أحجام الشركات
     */
    const COMPANY_SIZES = [
        'micro' => 'متناهية الصغر',
        'small' => 'صغيرة',
        'medium' => 'متوسطة',
        'large' => 'كبيرة',
        'enterprise' => 'مؤسسة',
    ];

    /**
     * حالات الامتثال
     */
    const COMPLIANCE_STATUSES = [
        'compliant' => 'ملتزم',
        'non_compliant' => 'غير ملتزم',
        'partially_compliant' => 'ملتزم جزئياً',
        'under_review' => 'قيد المراجعة',
        'pending' => 'قيد الانتظار',
    ];

    /**
     * مستويات المخاطر
     */
    const RISK_LEVELS = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
    ];

    /**
     * العلاقة مع الدول
     */
    public function countries(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'company_countries')
                    ->withPivot(['is_primary', 'registration_date', 'status'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع أنشطة الامتثال
     */
    public function complianceActivities(): HasMany
    {
        return $this->hasMany(ComplianceActivity::class);
    }

    /**
     * العلاقة مع تنبيهات الامتثال
     */
    public function complianceAlerts(): HasMany
    {
        return $this->hasMany(ComplianceAlert::class);
    }

    /**
     * العلاقة مع المعاملات الضريبية
     */
    public function taxTransactions(): HasMany
    {
        return $this->hasMany(TaxTransaction::class);
    }

    /**
     * العلاقة مع الإقرارات الضريبية
     */
    public function taxReturns(): HasMany
    {
        return $this->hasMany(TaxReturn::class);
    }

    /**
     * العلاقة مع الفواتير الإلكترونية
     */
    public function electronicInvoices(): HasMany
    {
        return $this->hasMany(ElectronicInvoice::class);
    }

    /**
     * العلاقة مع مساهمات الضمان الاجتماعي
     */
    public function socialSecurityContributions(): HasMany
    {
        return $this->hasMany(SocialSecurityContribution::class);
    }

    /**
     * الحصول على الدولة الأساسية
     */
    public function getPrimaryCountry(): ?Country
    {
        return $this->countries()->wherePivot('is_primary', true)->first();
    }

    /**
     * التحقق من العمل في دولة معينة
     */
    public function operatesInCountry(string $countryCode): bool
    {
        return $this->countries()->whereHas('country', fn($q) => $q->where('code', $countryCode))->exists();
    }

    /**
     * الحصول على حالة الامتثال الحالية
     */
    public function getCurrentComplianceStatus(): array
    {
        $activities = $this->complianceActivities()
            ->whereIn('status', ['pending', 'in_progress', 'overdue'])
            ->get();

        $alerts = $this->complianceAlerts()
            ->active()
            ->get();

        $overdueCount = $activities->where('status', 'overdue')->count();
        $criticalAlerts = $alerts->where('severity', 'critical')->count();

        $status = 'compliant';
        if ($criticalAlerts > 0 || $overdueCount > 5) {
            $status = 'non_compliant';
        } elseif ($overdueCount > 0 || $alerts->where('severity', 'high')->count() > 0) {
            $status = 'partially_compliant';
        }

        return [
            'status' => $status,
            'pending_activities' => $activities->where('status', 'pending')->count(),
            'in_progress_activities' => $activities->where('status', 'in_progress')->count(),
            'overdue_activities' => $overdueCount,
            'active_alerts' => $alerts->count(),
            'critical_alerts' => $criticalAlerts,
            'last_updated' => now(),
        ];
    }

    /**
     * حساب نقاط الامتثال
     */
    public function calculateComplianceScore(): float
    {
        $countries = $this->countries;
        if ($countries->isEmpty()) {
            return 100.0;
        }

        $totalScore = 0;
        $totalWeight = 0;

        foreach ($countries as $country) {
            $countryScore = $this->calculateCountryComplianceScore($country);
            $weight = $this->getCountryWeight($country);
            
            $totalScore += $countryScore * $weight;
            $totalWeight += $weight;
        }

        return $totalWeight > 0 ? round($totalScore / $totalWeight, 2) : 100.0;
    }

    /**
     * حساب نقاط امتثال دولة محددة
     */
    protected function calculateCountryComplianceScore(Country $country): float
    {
        $activities = $this->complianceActivities()
            ->where('country_id', $country->id)
            ->get();

        if ($activities->isEmpty()) {
            return 100.0;
        }

        $totalImpact = 0;
        $achievedImpact = 0;

        foreach ($activities as $activity) {
            $impact = $activity->calculateComplianceImpact();
            $totalImpact += abs($impact);
            
            if ($activity->status === 'completed') {
                $achievedImpact += abs($impact);
            } elseif ($activity->status === 'overdue') {
                $achievedImpact -= abs($impact) * 0.5; // خصم للأنشطة المتأخرة
            }
        }

        $score = $totalImpact > 0 ? ($achievedImpact / $totalImpact) * 100 : 100;
        return max(0, min(100, $score));
    }

    /**
     * الحصول على وزن الدولة
     */
    protected function getCountryWeight(Country $country): float
    {
        $pivot = $country->pivot;
        
        if ($pivot && $pivot->is_primary) {
            return 1.0;
        }

        // حساب الوزن بناءً على حجم العمليات
        return 0.5; // مثال بسيط
    }

    /**
     * الحصول على المعرفات الضريبية لدولة معينة
     */
    public function getTaxIdentifiers(string $countryCode): array
    {
        return match ($countryCode) {
            'MA' => [
                'tax_number' => $this->tax_number,
                'ice_number' => $this->ice_number,
                'cnss_number' => $this->cnss_number,
            ],
            'SA' => [
                'vat_number' => $this->vat_number,
                'cr_number' => $this->cr_number,
                'gosi_number' => $this->gosi_number,
            ],
            'AE' => [
                'trn_number' => $this->trn_number,
                'trade_license' => $this->trade_license,
            ],
            default => [
                'tax_number' => $this->tax_number,
                'vat_number' => $this->vat_number,
            ],
        };
    }

    /**
     * التحقق من اكتمال البيانات المطلوبة
     */
    public function hasRequiredDataForCountry(string $countryCode): bool
    {
        $requiredFields = $this->getRequiredFieldsForCountry($countryCode);
        
        foreach ($requiredFields as $field) {
            if (empty($this->$field)) {
                return false;
            }
        }

        return true;
    }

    /**
     * الحصول على الحقول المطلوبة لدولة معينة
     */
    protected function getRequiredFieldsForCountry(string $countryCode): array
    {
        return match ($countryCode) {
            'MA' => ['name', 'tax_number', 'ice_number', 'address'],
            'SA' => ['name', 'vat_number', 'cr_number', 'address'],
            'AE' => ['name', 'trn_number', 'trade_license', 'address'],
            'KW' => ['name', 'registration_number', 'address'],
            'QA' => ['name', 'registration_number', 'address'],
            'JO' => ['name', 'tax_number', 'address'],
            'EG' => ['name', 'tax_number', 'address'],
            'TN' => ['name', 'tax_number', 'address'],
            'DZ' => ['name', 'tax_number', 'address'],
            default => ['name', 'registration_number', 'address'],
        };
    }

    /**
     * تحديث حالة الامتثال
     */
    public function updateComplianceStatus(): void
    {
        $currentStatus = $this->getCurrentComplianceStatus();
        $complianceScore = $this->calculateComplianceScore();
        
        $this->update([
            'compliance_status' => $currentStatus['status'],
            'risk_level' => $this->calculateRiskLevel($complianceScore, $currentStatus),
            'last_compliance_check' => now(),
        ]);
    }

    /**
     * حساب مستوى المخاطر
     */
    protected function calculateRiskLevel(float $complianceScore, array $complianceStatus): string
    {
        if ($complianceStatus['critical_alerts'] > 0 || $complianceScore < 60) {
            return 'critical';
        } elseif ($complianceStatus['overdue_activities'] > 3 || $complianceScore < 75) {
            return 'high';
        } elseif ($complianceStatus['overdue_activities'] > 0 || $complianceScore < 90) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * الحصول على ملخص الشركة
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->company_type,
            'size' => $this->size,
            'countries' => $this->countries->pluck('code')->toArray(),
            'compliance_status' => $this->compliance_status,
            'risk_level' => $this->risk_level,
            'compliance_score' => $this->calculateComplianceScore(),
            'employee_count' => $this->employee_count,
            'annual_revenue' => $this->annual_revenue,
            'last_compliance_check' => $this->last_compliance_check?->format('Y-m-d'),
        ];
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('countries', fn($q) => $q->where('code', $countryCode));
    }

    public function scopeByComplianceStatus($query, string $status)
    {
        return $query->where('compliance_status', $status);
    }

    public function scopeByRiskLevel($query, string $riskLevel)
    {
        return $query->where('risk_level', $riskLevel);
    }

    public function scopeBySize($query, string $size)
    {
        return $query->where('size', $size);
    }

    public function scopeHighRisk($query)
    {
        return $query->whereIn('risk_level', ['high', 'critical']);
    }

    public function scopeNonCompliant($query)
    {
        return $query->whereIn('compliance_status', ['non_compliant', 'partially_compliant']);
    }

    public function scopeNeedsAttention($query)
    {
        return $query->where(function ($q) {
            $q->whereIn('compliance_status', ['non_compliant', 'partially_compliant'])
              ->orWhereIn('risk_level', ['high', 'critical'])
              ->orWhere('last_compliance_check', '<', now()->subDays(30));
        });
    }
}
