<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\InvoiceItem;
use App\Domains\Accounting\Services\SmartInvoicingService;
use App\Domains\Accounting\Requests\StoreInvoiceRequest;
use App\Domains\Accounting\Requests\UpdateInvoiceRequest;
use App\Domains\Accounting\Resources\InvoiceResource;
use App\Domains\Accounting\Resources\InvoiceCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

/**
 * Invoice Controller
 * تحكم في الفواتير
 */
class InvoiceController extends Controller
{
    protected SmartInvoicingService $invoicingService;

    public function __construct(SmartInvoicingService $invoicingService)
    {
        $this->invoicingService = $invoicingService;
        $this->middleware('auth');
    }

    /**
     * عرض قائمة الفواتير
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Invoice::class);

        $query = Invoice::with(['customer', 'items', 'payments'])
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('invoice_number', 'like', "%{$search}%")
                          ->orWhere('reference_number', 'like', "%{$search}%")
                          ->orWhereHas('customer', function ($q) use ($search) {
                              $q->where('name', 'like', "%{$search}%");
                          });
                });
            })
            ->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            })
            ->when($request->customer_id, function ($q, $customerId) {
                $q->where('customer_id', $customerId);
            })
            ->when($request->date_from, function ($q, $dateFrom) {
                $q->where('invoice_date', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($q, $dateTo) {
                $q->where('invoice_date', '<=', $dateTo);
            })
            ->when($request->overdue, function ($q) {
                $q->where('due_date', '<', now())
                  ->whereIn('status', ['sent', 'viewed']);
            });

        $invoices = $query->orderBy('created_at', 'desc')
                         ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new InvoiceCollection($invoices),
            'message' => 'تم جلب الفواتير بنجاح'
        ]);
    }

    /**
     * إنشاء فاتورة جديدة
     */
    public function store(StoreInvoiceRequest $request): JsonResponse
    {
        $this->authorize('create', Invoice::class);

        DB::beginTransaction();
        try {
            $invoice = $this->invoicingService->createInvoice($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new InvoiceResource($invoice->load(['customer', 'items', 'payments'])),
                'message' => 'تم إنشاء الفاتورة بنجاح'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل فاتورة محددة
     */
    public function show(Invoice $invoice): JsonResponse
    {
        $this->authorize('view', $invoice);

        $invoice->load([
            'customer',
            'items.product',
            'payments',
            'journalEntries',
            'creator',
            'updater'
        ]);

        return response()->json([
            'success' => true,
            'data' => new InvoiceResource($invoice),
            'message' => 'تم جلب تفاصيل الفاتورة بنجاح'
        ]);
    }

    /**
     * تحديث فاتورة
     */
    public function update(UpdateInvoiceRequest $request, Invoice $invoice): JsonResponse
    {
        $this->authorize('update', $invoice);

        DB::beginTransaction();
        try {
            $invoice = $this->invoicingService->updateInvoice($invoice, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new InvoiceResource($invoice->load(['customer', 'items', 'payments'])),
                'message' => 'تم تحديث الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف فاتورة
     */
    public function destroy(Invoice $invoice): JsonResponse
    {
        $this->authorize('delete', $invoice);

        if (in_array($invoice->status, ['paid', 'partially_paid'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف فاتورة مدفوعة'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // حذف العناصر
            $invoice->items()->delete();
            
            // حذف الفاتورة
            $invoice->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إرسال فاتورة
     */
    public function sendInvoice(Invoice $invoice): JsonResponse
    {
        $this->authorize('send', $invoice);

        try {
            $result = $this->invoicingService->sendInvoice($invoice);

            return response()->json([
                'success' => true,
                'data' => [
                    'invoice' => new InvoiceResource($invoice->fresh()),
                    'sent_at' => $result['sent_at'],
                    'sent_to' => $result['sent_to'],
                ],
                'message' => 'تم إرسال الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اعتماد فاتورة
     */
    public function approveInvoice(Invoice $invoice): JsonResponse
    {
        $this->authorize('approve', $invoice);

        try {
            $invoice = $this->invoicingService->approveInvoice($invoice);

            return response()->json([
                'success' => true,
                'data' => new InvoiceResource($invoice),
                'message' => 'تم اعتماد الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء اعتماد الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إلغاء فاتورة
     */
    public function cancelInvoice(Invoice $invoice): JsonResponse
    {
        $this->authorize('cancel', $invoice);

        try {
            $invoice = $this->invoicingService->cancelInvoice($invoice);

            return response()->json([
                'success' => true,
                'data' => new InvoiceResource($invoice),
                'message' => 'تم إلغاء الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * نسخ فاتورة
     */
    public function duplicateInvoice(Invoice $invoice): JsonResponse
    {
        $this->authorize('duplicate', $invoice);

        try {
            $newInvoice = $this->invoicingService->duplicateInvoice($invoice);

            return response()->json([
                'success' => true,
                'data' => new InvoiceResource($newInvoice->load(['customer', 'items'])),
                'message' => 'تم نسخ الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء نسخ الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء PDF للفاتورة
     */
    public function generatePDF(Invoice $invoice): JsonResponse
    {
        $this->authorize('downloadPdf', $invoice);

        try {
            $invoice->load(['customer', 'items']);
            
            $pdf = Pdf::loadView('accounting::invoices.pdf', compact('invoice'));
            $filename = "invoice_{$invoice->invoice_number}.pdf";
            
            // حفظ الملف
            $path = "invoices/{$invoice->id}/{$filename}";
            Storage::disk('public')->put($path, $pdf->output());

            return response()->json([
                'success' => true,
                'data' => [
                    'download_url' => Storage::disk('public')->url($path),
                    'filename' => $filename,
                ],
                'message' => 'تم إنشاء PDF بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على مدفوعات الفاتورة
     */
    public function getInvoicePayments(Invoice $invoice): JsonResponse
    {
        $this->authorize('viewPayments', $invoice);

        $payments = $invoice->payments()
            ->with(['paymentMethod', 'creator'])
            ->orderBy('payment_date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'invoice' => [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'total_amount' => $invoice->total_amount,
                    'paid_amount' => $invoice->paid_amount,
                    'remaining_amount' => $invoice->total_amount - $invoice->paid_amount,
                ],
                'payments' => $payments,
            ],
            'message' => 'تم جلب مدفوعات الفاتورة بنجاح'
        ]);
    }

    /**
     * تسجيل دفعة للفاتورة
     */
    public function recordPayment(Request $request, Invoice $invoice): JsonResponse
    {
        $this->authorize('recordPayment', $invoice);

        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string',
            'reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();
        try {
            $payment = $this->invoicingService->recordPayment($invoice, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'payment' => $payment,
                    'invoice' => new InvoiceResource($invoice->fresh()),
                ],
                'message' => 'تم تسجيل الدفعة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على الفواتير المتأخرة
     */
    public function getOverdueInvoices(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Invoice::class);

        $overdueInvoices = Invoice::with(['customer'])
            ->where('due_date', '<', now())
            ->whereIn('status', ['sent', 'viewed'])
            ->orderBy('due_date')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new InvoiceCollection($overdueInvoices),
            'message' => 'تم جلب الفواتير المتأخرة بنجاح'
        ]);
    }

    /**
     * الحصول على الفواتير المتكررة
     */
    public function getRecurringInvoices(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Invoice::class);

        $recurringInvoices = Invoice::with(['customer'])
            ->where('is_recurring', true)
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new InvoiceCollection($recurringInvoices),
            'message' => 'تم جلب الفواتير المتكررة بنجاح'
        ]);
    }

    /**
     * إرسال فواتير متعددة
     */
    public function bulkSendInvoices(Request $request): JsonResponse
    {
        $this->authorize('send', Invoice::class);

        $request->validate([
            'invoice_ids' => 'required|array',
            'invoice_ids.*' => 'exists:invoices,id',
        ]);

        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($request->invoice_ids as $invoiceId) {
            try {
                $invoice = Invoice::find($invoiceId);
                $this->authorize('send', $invoice);
                
                $result = $this->invoicingService->sendInvoice($invoice);
                $results[] = [
                    'invoice_id' => $invoiceId,
                    'status' => 'success',
                    'sent_at' => $result['sent_at'],
                ];
                $successCount++;

            } catch (\Exception $e) {
                $results[] = [
                    'invoice_id' => $invoiceId,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                ];
                $failureCount++;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'results' => $results,
                'summary' => [
                    'total' => count($request->invoice_ids),
                    'success' => $successCount,
                    'failed' => $failureCount,
                ],
            ],
            'message' => "تم إرسال {$successCount} فاتورة بنجاح، فشل في إرسال {$failureCount} فاتورة"
        ]);
    }

    /**
     * الحصول على تحليلات الفواتير
     */
    public function getInvoiceAnalytics(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Invoice::class);

        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $analytics = $this->invoicingService->getInvoiceAnalytics($dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => $analytics,
            'message' => 'تم جلب تحليلات الفواتير بنجاح'
        ]);
    }
}
