<?php

namespace App\Domains\Projects\Policies;

use App\Models\User;
use App\Domains\Projects\Models\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة المشاريع
 * تحديد الأذونات والصلاحيات للمشاريع
 */
class ProjectPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي مشروع
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyPermission([
            'view-projects',
            'manage-projects',
            'admin-projects'
        ]) || $user->hasRole(['admin', 'project-manager', 'team-lead']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المشروع
     */
    public function view(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم رؤية جميع المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'manage-projects']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه رؤية مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق يمكنهم رؤية المشروع
        if ($project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // العميل يمكنه رؤية مشروعه
        if ($project->client_id && $user->client_id === $project->client_id) {
            return true;
        }

        // المشاريع العامة يمكن لأي شخص رؤيتها
        if ($project->visibility === 'PUBLIC' && $user->hasPermission('view-projects')) {
            return true;
        }

        // المشاريع المرئية للفريق
        if ($project->visibility === 'TEAM' && 
            $user->hasPermission('view-projects') &&
            $this->isInSameDepartment($user, $project)) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء مشروع
     */
    public function create(User $user): bool
    {
        return $user->hasAnyPermission([
            'create-projects',
            'manage-projects',
            'admin-projects'
        ]) || $user->hasRole(['admin', 'project-manager']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث المشروع
     */
    public function update(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم تحديث جميع المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'manage-projects']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه تحديث مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات التحديث
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('update', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف المشروع
     */
    public function delete(User $user, Project $project): bool
    {
        // فقط المدراء والمشرفون يمكنهم حذف المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'delete-projects']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه حذف مشروعه إذا لم يبدأ بعد
        if ($project->project_manager_id === $user->id && 
            $project->status === 'PLANNING' &&
            $project->tasks()->count() === 0) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استعادة المشروع
     */
    public function restore(User $user, Project $project): bool
    {
        return $user->hasAnyPermission(['admin-projects', 'restore-projects']) || 
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف المشروع نهائياً
     */
    public function forceDelete(User $user, Project $project): bool
    {
        return $user->hasPermission('force-delete-projects') || 
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة فريق المشروع
     */
    public function manageTeam(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع الفرق
        if ($user->hasAnyPermission(['admin-projects', 'manage-project-teams']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة فريقه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات إدارة الفريق
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('manage_team', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة مهام المشروع
     */
    public function manageTasks(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع المهام
        if ($user->hasAnyPermission(['admin-projects', 'manage-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة مهام مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق يمكنهم إدارة المهام
        if ($project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات المالية
     */
    public function viewFinancials(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم رؤية جميع البيانات المالية
        if ($user->hasAnyPermission(['admin-projects', 'view-project-financials']) || 
            $user->hasRole(['admin', 'super-admin', 'finance-manager'])) {
            return true;
        }

        // مدير المشروع يمكنه رؤية البيانات المالية لمشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // العميل يمكنه رؤية البيانات المالية لمشروعه
        if ($project->client_id && $user->client_id === $project->client_id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات مالية
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('view_financials', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة البيانات المالية
     */
    public function manageFinancials(User $user, Project $project): bool
    {
        // فقط المدراء والمشرفون المالييون يمكنهم إدارة البيانات المالية
        if ($user->hasAnyPermission(['admin-projects', 'manage-project-financials']) || 
            $user->hasRole(['admin', 'super-admin', 'finance-manager'])) {
            return true;
        }

        // مدير المشروع مع صلاحيات مالية
        if ($project->project_manager_id === $user->id && 
            $user->hasPermission('manage-project-budget')) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التقارير
     */
    public function viewReports(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم رؤية جميع التقارير
        if ($user->hasAnyPermission(['admin-projects', 'view-project-reports']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه رؤية تقارير مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // العميل يمكنه رؤية تقارير مشروعه
        if ($project->client_id && $user->client_id === $project->client_id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات التقارير
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('view_reports', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة المخاطر
     */
    public function manageRisks(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع المخاطر
        if ($user->hasAnyPermission(['admin-projects', 'manage-project-risks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة مخاطر مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات إدارة المخاطر
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('manage_risks', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة الوثائق
     */
    public function manageDocuments(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع الوثائق
        if ($user->hasAnyPermission(['admin-projects', 'manage-project-documents']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة وثائق مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق يمكنهم إدارة الوثائق
        if ($project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تغيير حالة المشروع
     */
    public function changeStatus(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم تغيير حالة جميع المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'change-project-status']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه تغيير حالة مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات تغيير الحالة
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('change_status', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير البيانات
     */
    public function export(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم تصدير جميع البيانات
        if ($user->hasAnyPermission(['admin-projects', 'export-project-data']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه تصدير بيانات مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // العميل يمكنه تصدير بيانات مشروعه
        if ($project->client_id && $user->client_id === $project->client_id) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه أرشفة المشروع
     */
    public function archive(User $user, Project $project): bool
    {
        // فقط المدراء والمشرفون يمكنهم أرشفة المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'archive-projects']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه أرشفة مشروعه إذا كان مكتملاً
        if ($project->project_manager_id === $user->id && 
            $project->status === 'COMPLETED') {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه نسخ المشروع
     */
    public function duplicate(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم نسخ جميع المشاريع
        if ($user->hasAnyPermission(['admin-projects', 'duplicate-projects']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه نسخ مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء الفريق مع صلاحيات النسخ
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('duplicate', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة التكامل
     */
    public function manageIntegrations(User $user, Project $project): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع التكاملات
        if ($user->hasAnyPermission(['admin-projects', 'manage-project-integrations']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة تكاملات مشروعه
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من كون المستخدم في نفس القسم
     */
    protected function isInSameDepartment(User $user, Project $project): bool
    {
        // يمكن تطوير هذا بناءً على هيكل الأقسام في النظام
        return $user->department_id === $project->projectManager?->department_id;
    }

    /**
     * التحقق من صلاحيات خاصة للمشروع
     */
    public function hasSpecialPermission(User $user, Project $project, string $permission): bool
    {
        // التحقق من الصلاحيات العامة
        if ($user->hasPermission($permission)) {
            return true;
        }

        // التحقق من صلاحيات عضو الفريق
        $teamMember = $project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array($permission, $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من إمكانية الوصول للمشروع بناءً على الحالة
     */
    public function canAccessBasedOnStatus(User $user, Project $project): bool
    {
        // المشاريع الملغاة لا يمكن الوصول إليها إلا للمدراء
        if ($project->status === 'CANCELLED') {
            return $user->hasAnyPermission(['admin-projects', 'view-cancelled-projects']) || 
                   $user->hasRole(['admin', 'super-admin']);
        }

        // المشاريع المؤرشفة لا يمكن الوصول إليها إلا للمدراء ومدير المشروع
        if ($project->status === 'ARCHIVED') {
            return $user->hasAnyPermission(['admin-projects', 'view-archived-projects']) || 
                   $user->hasRole(['admin', 'super-admin']) ||
                   $project->project_manager_id === $user->id;
        }

        return true;
    }

    /**
     * التحقق من إمكانية التعديل بناءً على الحالة
     */
    public function canModifyBasedOnStatus(User $user, Project $project): bool
    {
        // المشاريع المكتملة أو الملغاة لا يمكن تعديلها إلا للمدراء
        if (in_array($project->status, ['COMPLETED', 'CANCELLED', 'ARCHIVED'])) {
            return $user->hasAnyPermission(['admin-projects', 'modify-completed-projects']) || 
                   $user->hasRole(['admin', 'super-admin']);
        }

        return true;
    }
}
