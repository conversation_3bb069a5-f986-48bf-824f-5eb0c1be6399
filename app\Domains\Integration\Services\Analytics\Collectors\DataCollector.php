<?php

namespace App\Domains\Integration\Services\Analytics\Collectors;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Data Collector for Analytics
 * 
 * Collects and aggregates data from various sources for analytics processing
 */
class DataCollector
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'cache_ttl' => 300, // 5 minutes
            'batch_size' => 1000,
        ], $config);
    }

    /**
     * Get overview data for gateway
     */
    public function getOverviewData(string $gatewayId, array $timeFrame): array
    {
        $cacheKey = "overview_data:{$gatewayId}:" . md5(json_encode($timeFrame));
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($gatewayId, $timeFrame) {
            $start = $timeFrame['start'];
            $end = $timeFrame['end'];

            $data = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN error_message IS NULL THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as failed_requests,
                    AVG(processing_time) as avg_response_time,
                    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY processing_time) as p95_response_time,
                    PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY processing_time) as p99_response_time,
                    COUNT(DISTINCT source_ip) as unique_clients,
                    SUM(request_size + response_size) as data_transferred
                ')
                ->first();

            // Calculate cache hit ratio
            $cacheHits = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->whereRaw("JSON_EXTRACT(metrics, '$.cache_hit') = true")
                ->count();

            $cacheHitRatio = $data->total_requests > 0 ? ($cacheHits / $data->total_requests) * 100 : 0;

            return [
                'total_requests' => $data->total_requests ?? 0,
                'successful_requests' => $data->successful_requests ?? 0,
                'failed_requests' => $data->failed_requests ?? 0,
                'avg_response_time' => $data->avg_response_time ?? 0,
                'p95_response_time' => $data->p95_response_time ?? 0,
                'p99_response_time' => $data->p99_response_time ?? 0,
                'unique_clients' => $data->unique_clients ?? 0,
                'data_transferred' => $data->data_transferred ?? 0,
                'cache_hit_ratio' => round($cacheHitRatio, 2),
            ];
        });
    }

    /**
     * Get performance data for gateway
     */
    public function getPerformanceData(string $gatewayId, array $timeFrame): array
    {
        $cacheKey = "performance_data:{$gatewayId}:" . md5(json_encode($timeFrame));
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($gatewayId, $timeFrame) {
            $start = $timeFrame['start'];
            $end = $timeFrame['end'];

            // Response time distribution
            $responseTimeDistribution = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    CASE 
                        WHEN processing_time < 0.1 THEN "0-100ms"
                        WHEN processing_time < 0.5 THEN "100-500ms"
                        WHEN processing_time < 1.0 THEN "500ms-1s"
                        WHEN processing_time < 2.0 THEN "1-2s"
                        ELSE "2s+"
                    END as range,
                    COUNT(*) as count
                ')
                ->groupBy('range')
                ->get()
                ->pluck('count', 'range')
                ->toArray();

            // Throughput over time
            $throughputOverTime = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    DATE_FORMAT(processed_at, "%Y-%m-%d %H:%i:00") as time_bucket,
                    COUNT(*) as requests
                ')
                ->groupBy('time_bucket')
                ->orderBy('time_bucket')
                ->get()
                ->toArray();

            // Error rate over time
            $errorRateOverTime = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    DATE_FORMAT(processed_at, "%Y-%m-%d %H:%i:00") as time_bucket,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count
                ')
                ->groupBy('time_bucket')
                ->orderBy('time_bucket')
                ->get()
                ->map(function ($item) {
                    $errorRate = $item->total_requests > 0 ? ($item->error_count / $item->total_requests) * 100 : 0;
                    return [
                        'time_bucket' => $item->time_bucket,
                        'error_rate' => round($errorRate, 2),
                    ];
                })
                ->toArray();

            // Slowest endpoints
            $slowestEndpoints = DB::table('api_request_logs')
                ->join('api_endpoints', 'api_request_logs.endpoint_id', '=', 'api_endpoints.id')
                ->where('api_request_logs.gateway_id', $gatewayId)
                ->whereBetween('api_request_logs.processed_at', [$start, $end])
                ->selectRaw('
                    api_endpoints.path,
                    AVG(api_request_logs.processing_time) as avg_response_time,
                    COUNT(*) as request_count
                ')
                ->groupBy('api_endpoints.path')
                ->orderBy('avg_response_time', 'desc')
                ->limit(10)
                ->get()
                ->toArray();

            // Highest traffic endpoints
            $highestTrafficEndpoints = DB::table('api_request_logs')
                ->join('api_endpoints', 'api_request_logs.endpoint_id', '=', 'api_endpoints.id')
                ->where('api_request_logs.gateway_id', $gatewayId)
                ->whereBetween('api_request_logs.processed_at', [$start, $end])
                ->selectRaw('
                    api_endpoints.path,
                    COUNT(*) as request_count,
                    AVG(api_request_logs.processing_time) as avg_response_time
                ')
                ->groupBy('api_endpoints.path')
                ->orderBy('request_count', 'desc')
                ->limit(10)
                ->get()
                ->toArray();

            return [
                'response_time_distribution' => $responseTimeDistribution,
                'throughput_over_time' => $throughputOverTime,
                'error_rate_over_time' => $errorRateOverTime,
                'latency_percentiles' => $this->calculateLatencyPercentiles($gatewayId, $start, $end),
                'slowest_endpoints' => $slowestEndpoints,
                'highest_traffic_endpoints' => $highestTrafficEndpoints,
            ];
        });
    }

    /**
     * Get usage data for gateway
     */
    public function getUsageData(string $gatewayId, array $timeFrame): array
    {
        $cacheKey = "usage_data:{$gatewayId}:" . md5(json_encode($timeFrame));
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($gatewayId, $timeFrame) {
            $start = $timeFrame['start'];
            $end = $timeFrame['end'];

            // Requests by endpoint
            $requestsByEndpoint = DB::table('api_request_logs')
                ->join('api_endpoints', 'api_request_logs.endpoint_id', '=', 'api_endpoints.id')
                ->where('api_request_logs.gateway_id', $gatewayId)
                ->whereBetween('api_request_logs.processed_at', [$start, $end])
                ->selectRaw('api_endpoints.path, COUNT(*) as count')
                ->groupBy('api_endpoints.path')
                ->orderBy('count', 'desc')
                ->get()
                ->pluck('count', 'path')
                ->toArray();

            // Requests by method
            $requestsByMethod = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('method, COUNT(*) as count')
                ->groupBy('method')
                ->get()
                ->pluck('count', 'method')
                ->toArray();

            // Requests by status code
            $requestsByStatusCode = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('status_code, COUNT(*) as count')
                ->groupBy('status_code')
                ->get()
                ->pluck('count', 'status_code')
                ->toArray();

            // Requests by client (API key)
            $requestsByClient = DB::table('api_request_logs')
                ->join('api_keys', 'api_request_logs.api_key_id', '=', 'api_keys.id')
                ->where('api_request_logs.gateway_id', $gatewayId)
                ->whereBetween('api_request_logs.processed_at', [$start, $end])
                ->selectRaw('api_keys.name, COUNT(*) as count')
                ->groupBy('api_keys.name')
                ->orderBy('count', 'desc')
                ->limit(20)
                ->get()
                ->pluck('count', 'name')
                ->toArray();

            // Requests by hour
            $requestsByHour = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('HOUR(processed_at) as hour, COUNT(*) as count')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get()
                ->pluck('count', 'hour')
                ->toArray();

            // Requests by day
            $requestsByDay = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('DATE(processed_at) as day, COUNT(*) as count')
                ->groupBy('day')
                ->orderBy('day')
                ->get()
                ->pluck('count', 'day')
                ->toArray();

            return [
                'requests_by_endpoint' => $requestsByEndpoint,
                'requests_by_method' => $requestsByMethod,
                'requests_by_status_code' => $requestsByStatusCode,
                'requests_by_client' => $requestsByClient,
                'requests_by_hour' => $requestsByHour,
                'requests_by_day' => $requestsByDay,
                'geographic_distribution' => $this->getGeographicDistribution($gatewayId, $start, $end),
                'user_agent_distribution' => $this->getUserAgentDistribution($gatewayId, $start, $end),
            ];
        });
    }

    /**
     * Get error data for gateway
     */
    public function getErrorData(string $gatewayId, array $timeFrame): array
    {
        $cacheKey = "error_data:{$gatewayId}:" . md5(json_encode($timeFrame));
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($gatewayId, $timeFrame) {
            $start = $timeFrame['start'];
            $end = $timeFrame['end'];

            // Error types
            $errorTypes = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->whereNotNull('error_message')
                ->selectRaw('
                    CASE 
                        WHEN status_code BETWEEN 400 AND 499 THEN "Client Error"
                        WHEN status_code BETWEEN 500 AND 599 THEN "Server Error"
                        ELSE "Other"
                    END as error_type,
                    COUNT(*) as count
                ')
                ->groupBy('error_type')
                ->get()
                ->pluck('count', 'error_type')
                ->toArray();

            // Top errors
            $topErrors = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->whereNotNull('error_message')
                ->selectRaw('error_message, COUNT(*) as count')
                ->groupBy('error_message')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get()
                ->toArray();

            // Error rate by endpoint
            $errorRateByEndpoint = DB::table('api_request_logs')
                ->join('api_endpoints', 'api_request_logs.endpoint_id', '=', 'api_endpoints.id')
                ->where('api_request_logs.gateway_id', $gatewayId)
                ->whereBetween('api_request_logs.processed_at', [$start, $end])
                ->selectRaw('
                    api_endpoints.path,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count
                ')
                ->groupBy('api_endpoints.path')
                ->get()
                ->map(function ($item) {
                    $errorRate = $item->total_requests > 0 ? ($item->error_count / $item->total_requests) * 100 : 0;
                    return [
                        'path' => $item->path,
                        'error_rate' => round($errorRate, 2),
                        'total_requests' => $item->total_requests,
                        'error_count' => $item->error_count,
                    ];
                })
                ->sortByDesc('error_rate')
                ->values()
                ->toArray();

            return [
                'error_types' => $errorTypes,
                'error_trends' => $this->getErrorTrends($gatewayId, $start, $end),
                'top_errors' => $topErrors,
                'error_rate_by_endpoint' => $errorRateByEndpoint,
            ];
        });
    }

    /**
     * Get security data for gateway
     */
    public function getSecurityData(string $gatewayId, array $timeFrame): array
    {
        $start = $timeFrame['start'];
        $end = $timeFrame['end'];

        // Security incidents
        $securityIncidents = DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('detected_at', [$start, $end])
            ->selectRaw('threat_type, severity, COUNT(*) as count')
            ->groupBy('threat_type', 'severity')
            ->get()
            ->toArray();

        // Blocked requests
        $blockedRequests = DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('detected_at', [$start, $end])
            ->where('event_type', 'threat_detected')
            ->count();

        // Suspicious IPs
        $suspiciousIps = DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('detected_at', [$start, $end])
            ->selectRaw('source_ip, COUNT(*) as incident_count')
            ->groupBy('source_ip')
            ->orderBy('incident_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();

        return [
            'security_incidents' => $securityIncidents,
            'threat_levels' => $this->getThreatLevels($securityIncidents),
            'blocked_requests' => $blockedRequests,
            'suspicious_ips' => $suspiciousIps,
            'attack_patterns' => $this->getAttackPatterns($gatewayId, $start, $end),
        ];
    }

    /**
     * Get business data for gateway
     */
    public function getBusinessData(string $gatewayId, array $timeFrame): array
    {
        // Placeholder implementation
        return [
            'revenue_impact' => 0,
            'cost_analysis' => [],
            'sla_compliance' => 99.9,
            'customer_satisfaction' => 4.5,
            'api_adoption_metrics' => [],
        ];
    }

    /**
     * Get trend data for gateway
     */
    public function getTrendData(string $gatewayId, array $timeFrame): array
    {
        $start = $timeFrame['start'];
        $end = $timeFrame['end'];

        // Requests over time
        $requestsOverTime = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->selectRaw('DATE(processed_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();

        // Response times over time
        $responseTimesOverTime = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->selectRaw('DATE(processed_at) as date, AVG(processing_time) as avg_time')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();

        // Errors over time
        $errorsOverTime = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->selectRaw('
                DATE(processed_at) as date, 
                COUNT(*) as total_requests,
                SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count
            ')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                $errorRate = $item->total_requests > 0 ? ($item->error_count / $item->total_requests) * 100 : 0;
                return [
                    'date' => $item->date,
                    'error_rate' => round($errorRate, 2),
                ];
            })
            ->toArray();

        return [
            'requests_over_time' => $requestsOverTime,
            'response_times_over_time' => $responseTimesOverTime,
            'errors_over_time' => $errorsOverTime,
        ];
    }

    /**
     * Get response time data for gateway
     */
    public function getResponseTimeData(string $gatewayId, array $timeFrame): array
    {
        $start = $timeFrame['start'];
        $end = $timeFrame['end'];

        $currentPeriod = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->avg('processing_time');

        $previousStart = $start->copy()->sub($start->diff($end));
        $previousPeriod = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$previousStart, $start])
            ->avg('processing_time');

        return [
            'current_avg' => $currentPeriod ?? 0,
            'previous_avg' => $previousPeriod ?? 0,
        ];
    }

    // Helper methods
    protected function calculateLatencyPercentiles(string $gatewayId, Carbon $start, Carbon $end): array
    {
        // Simplified percentile calculation
        return [
            'p50' => 100,
            'p90' => 200,
            'p95' => 300,
            'p99' => 500,
        ];
    }

    protected function getGeographicDistribution(string $gatewayId, Carbon $start, Carbon $end): array
    {
        // Placeholder for geographic distribution
        return [
            'US' => 45,
            'EU' => 30,
            'ASIA' => 20,
            'OTHER' => 5,
        ];
    }

    protected function getUserAgentDistribution(string $gatewayId, Carbon $start, Carbon $end): array
    {
        return DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->selectRaw('
                CASE 
                    WHEN user_agent LIKE "%Chrome%" THEN "Chrome"
                    WHEN user_agent LIKE "%Firefox%" THEN "Firefox"
                    WHEN user_agent LIKE "%Safari%" THEN "Safari"
                    WHEN user_agent LIKE "%bot%" THEN "Bot"
                    ELSE "Other"
                END as browser,
                COUNT(*) as count
            ')
            ->groupBy('browser')
            ->get()
            ->pluck('count', 'browser')
            ->toArray();
    }

    protected function getErrorTrends(string $gatewayId, Carbon $start, Carbon $end): array
    {
        return DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('processed_at', [$start, $end])
            ->selectRaw('
                DATE_FORMAT(processed_at, "%Y-%m-%d %H:00:00") as hour,
                COUNT(*) as total_requests,
                SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count
            ')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->map(function ($item) {
                $errorRate = $item->total_requests > 0 ? ($item->error_count / $item->total_requests) * 100 : 0;
                return [
                    'hour' => $item->hour,
                    'error_rate' => round($errorRate, 2),
                ];
            })
            ->toArray();
    }

    protected function getThreatLevels(array $incidents): array
    {
        $levels = ['low' => 0, 'medium' => 0, 'high' => 0, 'critical' => 0];
        
        foreach ($incidents as $incident) {
            $severity = $incident->severity ?? 'low';
            if (isset($levels[$severity])) {
                $levels[$severity] += $incident->count;
            }
        }
        
        return $levels;
    }

    protected function getAttackPatterns(string $gatewayId, Carbon $start, Carbon $end): array
    {
        return DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('detected_at', [$start, $end])
            ->selectRaw('threat_type, COUNT(*) as count')
            ->groupBy('threat_type')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'threat_type')
            ->toArray();
    }
}
