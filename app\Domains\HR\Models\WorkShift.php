<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Carbon\Carbon;

/**
 * نموذج الورديات
 * إدارة شاملة لأنظمة العمل والورديات المختلفة
 */
class WorkShift extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_en',
        'code',
        'description',
        'shift_type',
        'department_id',
        
        // Timing
        'start_time',
        'end_time',
        'break_start_time',
        'break_end_time',
        'working_hours',
        'break_duration',
        
        // Days Configuration
        'working_days',
        'is_weekend_shift',
        'is_night_shift',
        'is_rotating_shift',
        'rotation_cycle_days',
        
        // Flexibility
        'flexible_start_time',
        'flexible_end_time',
        'core_hours_start',
        'core_hours_end',
        'minimum_hours_required',
        'maximum_hours_allowed',
        
        // Overtime Rules
        'overtime_eligible',
        'overtime_rate_multiplier',
        'overtime_threshold_hours',
        'double_overtime_threshold',
        'weekend_overtime_rate',
        'holiday_overtime_rate',
        
        // Attendance Rules
        'grace_period_minutes',
        'late_threshold_minutes',
        'early_departure_threshold',
        'auto_clock_out_hours',
        'require_manager_approval',
        
        // Location and Remote Work
        'location_required',
        'allowed_locations',
        'remote_work_allowed',
        'hybrid_work_allowed',
        'gps_tracking_enabled',
        'geofence_radius_meters',
        
        // Compensation
        'shift_allowance',
        'night_shift_allowance',
        'weekend_allowance',
        'holiday_allowance',
        'hazard_allowance',
        
        // Status and Metadata
        'is_active',
        'effective_from',
        'effective_to',
        'created_by',
        'updated_by',
        'metadata',
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'break_start_time' => 'datetime:H:i',
        'break_end_time' => 'datetime:H:i',
        'core_hours_start' => 'datetime:H:i',
        'core_hours_end' => 'datetime:H:i',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'working_hours' => 'decimal:2',
        'break_duration' => 'decimal:2',
        'minimum_hours_required' => 'decimal:2',
        'maximum_hours_allowed' => 'decimal:2',
        'overtime_rate_multiplier' => 'decimal:2',
        'overtime_threshold_hours' => 'decimal:2',
        'double_overtime_threshold' => 'decimal:2',
        'weekend_overtime_rate' => 'decimal:2',
        'holiday_overtime_rate' => 'decimal:2',
        'grace_period_minutes' => 'integer',
        'late_threshold_minutes' => 'integer',
        'early_departure_threshold' => 'integer',
        'auto_clock_out_hours' => 'integer',
        'rotation_cycle_days' => 'integer',
        'geofence_radius_meters' => 'integer',
        'shift_allowance' => 'decimal:2',
        'night_shift_allowance' => 'decimal:2',
        'weekend_allowance' => 'decimal:2',
        'holiday_allowance' => 'decimal:2',
        'hazard_allowance' => 'decimal:2',
        'working_days' => 'array',
        'allowed_locations' => 'array',
        'is_weekend_shift' => 'boolean',
        'is_night_shift' => 'boolean',
        'is_rotating_shift' => 'boolean',
        'flexible_start_time' => 'boolean',
        'flexible_end_time' => 'boolean',
        'overtime_eligible' => 'boolean',
        'require_manager_approval' => 'boolean',
        'location_required' => 'boolean',
        'remote_work_allowed' => 'boolean',
        'hybrid_work_allowed' => 'boolean',
        'gps_tracking_enabled' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * أنواع الورديات
     */
    const SHIFT_TYPES = [
        'REGULAR' => 'عادية',
        'FLEXIBLE' => 'مرنة',
        'ROTATING' => 'دوارة',
        'SPLIT' => 'مقسمة',
        'COMPRESSED' => 'مضغوطة',
        'NIGHT' => 'ليلية',
        'WEEKEND' => 'نهاية أسبوع',
        'ON_CALL' => 'استدعاء',
        'REMOTE' => 'عن بُعد',
        'HYBRID' => 'مختلطة',
    ];

    /**
     * أيام الأسبوع
     */
    const WEEKDAYS = [
        'SUNDAY' => 'الأحد',
        'MONDAY' => 'الاثنين',
        'TUESDAY' => 'الثلاثاء',
        'WEDNESDAY' => 'الأربعاء',
        'THURSDAY' => 'الخميس',
        'FRIDAY' => 'الجمعة',
        'SATURDAY' => 'السبت',
    ];

    /**
     * الموظفون المعينون لهذه الوردية
     */
    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class, 'employee_shifts')
                    ->withPivot(['assigned_date', 'effective_from', 'effective_to', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * سجلات الحضور لهذه الوردية
     */
    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceRecord::class, 'shift_id');
    }

    /**
     * القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * حساب ساعات العمل الفعلية (مع طرح الاستراحة)
     */
    public function getActualWorkingHoursAttribute(): float
    {
        $totalHours = $this->working_hours;
        
        if ($this->break_duration) {
            $totalHours -= $this->break_duration;
        }

        return $totalHours;
    }

    /**
     * التحقق من كون الوردية ليلية
     */
    public function isNightShift(): bool
    {
        if ($this->is_night_shift) {
            return true;
        }

        // إذا كانت الوردية تبدأ بعد 6 مساءً أو تنتهي قبل 6 صباحاً
        $startHour = Carbon::parse($this->start_time)->hour;
        $endHour = Carbon::parse($this->end_time)->hour;

        return $startHour >= 18 || $endHour <= 6;
    }

    /**
     * التحقق من كون الوردية تمتد لليوم التالي
     */
    public function spansMultipleDays(): bool
    {
        return Carbon::parse($this->start_time) > Carbon::parse($this->end_time);
    }

    /**
     * حساب إجمالي البدلات
     */
    public function getTotalAllowancesAttribute(): float
    {
        return $this->shift_allowance + 
               $this->night_shift_allowance + 
               $this->weekend_allowance + 
               $this->holiday_allowance + 
               $this->hazard_allowance;
    }

    /**
     * التحقق من أن اليوم يوم عمل في هذه الوردية
     */
    public function isWorkingDay(string $dayOfWeek): bool
    {
        return in_array(strtoupper($dayOfWeek), $this->working_days ?? []);
    }

    /**
     * التحقق من أن الوقت ضمن ساعات العمل الأساسية
     */
    public function isWithinCoreHours(Carbon $time): bool
    {
        if (!$this->core_hours_start || !$this->core_hours_end) {
            return true;
        }

        $coreStart = Carbon::parse($this->core_hours_start);
        $coreEnd = Carbon::parse($this->core_hours_end);
        $checkTime = Carbon::parse($time->format('H:i'));

        return $checkTime->between($coreStart, $coreEnd);
    }

    /**
     * حساب معدل العمل الإضافي للوقت المحدد
     */
    public function getOvertimeRate(Carbon $date, float $hours): float
    {
        $baseRate = $this->overtime_rate_multiplier ?? 1.5;

        // معدل مضاعف للعمل الإضافي الطويل
        if ($this->double_overtime_threshold && $hours > $this->double_overtime_threshold) {
            $baseRate = 2.0;
        }

        // معدل نهاية الأسبوع
        if ($date->isWeekend() && $this->weekend_overtime_rate) {
            $baseRate = $this->weekend_overtime_rate;
        }

        // معدل العطل الرسمية
        if ($this->isHoliday($date) && $this->holiday_overtime_rate) {
            $baseRate = $this->holiday_overtime_rate;
        }

        return $baseRate;
    }

    /**
     * التحقق من كون التاريخ عطلة رسمية
     */
    protected function isHoliday(Carbon $date): bool
    {
        // يمكن تطوير هذه الدالة للتحقق من العطل الرسمية
        // من جدول منفصل أو خدمة خارجية
        return false;
    }

    /**
     * تعيين موظف للوردية
     */
    public function assignEmployee(int $employeeId, array $options = []): bool
    {
        $data = array_merge([
            'assigned_date' => now(),
            'effective_from' => now(),
            'is_active' => true,
        ], $options);

        $this->employees()->attach($employeeId, $data);
        
        return true;
    }

    /**
     * إلغاء تعيين موظف من الوردية
     */
    public function unassignEmployee(int $employeeId): bool
    {
        $this->employees()->updateExistingPivot($employeeId, [
            'effective_to' => now(),
            'is_active' => false,
        ]);

        return true;
    }

    /**
     * الحصول على الموظفين النشطين في الوردية
     */
    public function getActiveEmployees()
    {
        return $this->employees()
                    ->wherePivot('is_active', true)
                    ->wherePivot('effective_from', '<=', now())
                    ->where(function ($query) {
                        $query->wherePivotNull('effective_to')
                              ->orWherePivot('effective_to', '>=', now());
                    });
    }

    /**
     * إنشاء جدول حضور للفترة المحددة
     */
    public function generateAttendanceSchedule(Carbon $startDate, Carbon $endDate): array
    {
        $schedule = [];
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            if ($this->isWorkingDay($currentDate->format('l'))) {
                $schedule[] = [
                    'date' => $currentDate->format('Y-m-d'),
                    'shift_id' => $this->id,
                    'scheduled_check_in' => $currentDate->copy()->setTimeFromTimeString($this->start_time),
                    'scheduled_check_out' => $currentDate->copy()->setTimeFromTimeString($this->end_time),
                ];
            }
            $currentDate->addDay();
        }

        return $schedule;
    }

    /**
     * نطاق للورديات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('effective_to')
                          ->orWhere('effective_to', '>=', now());
                    });
    }

    /**
     * نطاق حسب نوع الوردية
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('shift_type', $type);
    }

    /**
     * نطاق للورديات الليلية
     */
    public function scopeNightShifts($query)
    {
        return $query->where('is_night_shift', true);
    }

    /**
     * نطاق للورديات المرنة
     */
    public function scopeFlexible($query)
    {
        return $query->where('shift_type', 'FLEXIBLE');
    }

    /**
     * نطاق للورديات التي تدعم العمل عن بُعد
     */
    public function scopeRemoteEnabled($query)
    {
        return $query->where('remote_work_allowed', true);
    }

    /**
     * نطاق حسب القسم
     */
    public function scopeForDepartment($query, int $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }
}
