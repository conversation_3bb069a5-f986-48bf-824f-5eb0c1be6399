<?php

namespace App\Domains\Accounting\Listeners;

use App\Domains\Accounting\Events\JournalEntryCreated;
use App\Domains\Accounting\Models\Account;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * Update Account Balance Listener
 * مستمع تحديث أرصدة الحسابات
 */
class UpdateAccountBalance implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * معالجة الحدث
     */
    public function handle(JournalEntryCreated $event): void
    {
        try {
            $journalEntry = $event->journalEntry;

            // تحديث أرصدة الحسابات المتأثرة
            foreach ($journalEntry->lines as $line) {
                $account = Account::find($line->account_id);
                if ($account) {
                    $account->updateCurrentBalance();
                    
                    Log::info('تم تحديث رصيد الحساب', [
                        'account_id' => $account->id,
                        'account_code' => $account->account_code,
                        'new_balance' => $account->current_balance,
                        'journal_entry_id' => $journalEntry->id,
                    ]);
                }
            }

            // تحديث أرصدة الحسابات الأب إذا لزم الأمر
            $this->updateParentAccountBalances($journalEntry);

        } catch (\Exception $e) {
            Log::error('خطأ في تحديث أرصدة الحسابات', [
                'journal_entry_id' => $event->journalEntry->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // إعادة رمي الاستثناء لإعادة المحاولة
            throw $e;
        }
    }

    /**
     * تحديث أرصدة الحسابات الأب
     */
    protected function updateParentAccountBalances($journalEntry): void
    {
        $parentAccountIds = [];

        foreach ($journalEntry->lines as $line) {
            $account = Account::find($line->account_id);
            if ($account && $account->parent_account_id) {
                $parentAccountIds[] = $account->parent_account_id;
            }
        }

        // إزالة المكررات
        $parentAccountIds = array_unique($parentAccountIds);

        foreach ($parentAccountIds as $parentId) {
            $parentAccount = Account::find($parentId);
            if ($parentAccount) {
                $parentAccount->updateCurrentBalance();
            }
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(JournalEntryCreated $event, \Throwable $exception): void
    {
        Log::error('فشل في تحديث أرصدة الحسابات', [
            'journal_entry_id' => $event->journalEntry->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
