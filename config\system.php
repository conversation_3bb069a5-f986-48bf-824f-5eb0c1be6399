<?php

return [
    /*
    |--------------------------------------------------------------------------
    | System Configuration
    |--------------------------------------------------------------------------
    |
    | تكوين النظام العام للمؤسسة
    |
    */

    'name' => env('SYSTEM_NAME', 'نظام إدارة المؤسسات المتكامل'),
    'version' => '1.0.0',
    'environment' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Architecture Configuration
    |--------------------------------------------------------------------------
    */
    'architecture' => [
        'pattern' => 'modular_monolith',
        'microservices_ready' => true,
        'domain_driven_design' => true,
        'event_driven' => true,
        'cqrs_enabled' => false,
        'event_sourcing' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Enabled Domains
    |--------------------------------------------------------------------------
    */
    'domains' => [
        'accounting' => [
            'enabled' => env('DOMAIN_ACCOUNTING_ENABLED', true),
            'priority' => 1,
            'dependencies' => ['shared'],
        ],
        'taxation' => [
            'enabled' => env('DOMAIN_TAXATION_ENABLED', true),
            'priority' => 2,
            'dependencies' => ['shared', 'accounting'],
        ],
        'hr' => [
            'enabled' => env('DOMAIN_HR_ENABLED', true),
            'priority' => 3,
            'dependencies' => ['shared'],
        ],
        'projects' => [
            'enabled' => env('DOMAIN_PROJECTS_ENABLED', true),
            'priority' => 4,
            'dependencies' => ['shared', 'hr', 'crm'],
        ],
        'crm' => [
            'enabled' => env('DOMAIN_CRM_ENABLED', true),
            'priority' => 5,
            'dependencies' => ['shared', 'hr'],
        ],
        'support' => [
            'enabled' => env('DOMAIN_SUPPORT_ENABLED', true),
            'priority' => 6,
            'dependencies' => ['shared', 'hr', 'crm'],
        ],
        'ecommerce' => [
            'enabled' => env('DOMAIN_ECOMMERCE_ENABLED', true),
            'priority' => 7,
            'dependencies' => ['shared', 'crm', 'accounting'],
        ],
        'email' => [
            'enabled' => env('DOMAIN_EMAIL_ENABLED', true),
            'priority' => 8,
            'dependencies' => ['shared'],
        ],
        'payments' => [
            'enabled' => env('DOMAIN_PAYMENTS_ENABLED', true),
            'priority' => 9,
            'dependencies' => ['shared', 'accounting'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Multi-tenancy Configuration
    |--------------------------------------------------------------------------
    */
    'multi_tenancy' => [
        'enabled' => env('MULTI_TENANCY_ENABLED', false),
        'strategy' => 'single_database', // single_database, multi_database, multi_schema
        'tenant_identification' => 'subdomain', // subdomain, domain, header, parameter
        'automatic_tenant_creation' => false,
        'tenant_isolation' => [
            'data' => true,
            'cache' => true,
            'queues' => true,
            'storage' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization & Internationalization
    |--------------------------------------------------------------------------
    */
    'localization' => [
        'default_locale' => env('DEFAULT_LOCALE', 'ar'),
        'fallback_locale' => env('FALLBACK_LOCALE', 'en'),
        'supported_locales' => ['ar', 'en', 'fr'],
        'rtl_locales' => ['ar'],
        'auto_detect_locale' => true,
        'store_user_locale' => true,
        'timezone_detection' => true,
        'currency_detection' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Regional Settings
    |--------------------------------------------------------------------------
    */
    'regional' => [
        'default_country' => env('DEFAULT_COUNTRY', 'SA'),
        'default_currency' => env('DEFAULT_CURRENCY', 'SAR'),
        'default_timezone' => env('DEFAULT_TIMEZONE', 'Asia/Riyadh'),
        'supported_countries' => ['SA', 'AE', 'EG', 'MA', 'KW', 'QA', 'BH', 'OM'],
        'supported_currencies' => ['SAR', 'AED', 'EGP', 'MAD', 'KWD', 'QAR', 'BHD', 'OMR'],
        'multi_currency_support' => true,
        'exchange_rate_provider' => 'fixer', // fixer, openexchangerates, currencylayer
        'auto_update_rates' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'two_factor_auth' => [
            'enabled' => env('2FA_ENABLED', false),
            'required_for_roles' => ['admin', 'financial_manager'],
            'methods' => ['totp', 'sms', 'email'],
        ],
        'password_policy' => [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => true,
            'expiry_days' => 90,
            'history_count' => 5,
        ],
        'session_security' => [
            'timeout_minutes' => 480, // 8 hours
            'concurrent_sessions' => 3,
            'ip_validation' => false,
            'user_agent_validation' => true,
        ],
        'audit_logging' => [
            'enabled' => true,
            'log_all_requests' => false,
            'log_sensitive_data' => false,
            'retention_days' => 2555, // 7 years
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'caching' => [
            'enabled' => true,
            'default_ttl' => 3600,
            'tags_enabled' => true,
            'compression' => true,
        ],
        'database' => [
            'query_caching' => true,
            'slow_query_logging' => true,
            'slow_query_threshold' => 1000, // milliseconds
            'connection_pooling' => true,
        ],
        'file_storage' => [
            'compression' => true,
            'cdn_enabled' => false,
            'lazy_loading' => true,
        ],
        'api' => [
            'rate_limiting' => true,
            'response_caching' => true,
            'compression' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup & Recovery
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily',
        'retention_days' => 90,
        'include_files' => true,
        'compression' => true,
        'encryption' => true,
        'cloud_storage' => false,
        'verification' => true,
        'notification' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Analytics
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => true,
        'performance_monitoring' => true,
        'error_tracking' => true,
        'user_analytics' => true,
        'business_metrics' => true,
        'real_time_alerts' => true,
        'dashboard_enabled' => true,
        'external_services' => [
            'sentry' => env('SENTRY_ENABLED', false),
            'new_relic' => env('NEW_RELIC_ENABLED', false),
            'datadog' => env('DATADOG_ENABLED', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Configuration
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'api_gateway' => [
            'enabled' => false,
            'rate_limiting' => true,
            'authentication' => 'sanctum',
            'documentation' => true,
        ],
        'webhooks' => [
            'enabled' => true,
            'retry_attempts' => 3,
            'timeout_seconds' => 30,
            'signature_verification' => true,
        ],
        'message_queue' => [
            'enabled' => true,
            'driver' => 'database', // database, redis, sqs
            'retry_attempts' => 3,
            'batch_processing' => true,
        ],
        'search_engine' => [
            'enabled' => false,
            'driver' => 'elasticsearch', // elasticsearch, algolia, meilisearch
            'indexing' => 'real_time',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance & Legal
    |--------------------------------------------------------------------------
    */
    'compliance' => [
        'gdpr' => [
            'enabled' => false,
            'data_retention_days' => 2555,
            'right_to_be_forgotten' => true,
            'data_portability' => true,
            'consent_management' => true,
        ],
        'audit_requirements' => [
            'enabled' => true,
            'immutable_logs' => true,
            'digital_signatures' => false,
            'retention_years' => 7,
        ],
        'data_classification' => [
            'enabled' => true,
            'levels' => ['public', 'internal', 'confidential', 'restricted'],
            'encryption_required' => ['confidential', 'restricted'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development & Testing
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_mode' => env('APP_DEBUG', false),
        'profiling' => env('PROFILING_ENABLED', false),
        'test_data_seeding' => env('TEST_DATA_SEEDING', false),
        'api_documentation' => true,
        'code_coverage' => env('CODE_COVERAGE_ENABLED', false),
        'feature_flags' => [
            'enabled' => true,
            'driver' => 'database', // database, config, external
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => true,
        'channels' => ['mail', 'database', 'sms', 'push', 'slack'],
        'default_channel' => 'database',
        'queue_notifications' => true,
        'rate_limiting' => true,
        'templates' => [
            'customizable' => true,
            'multi_language' => true,
            'versioning' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Management
    |--------------------------------------------------------------------------
    */
    'files' => [
        'max_upload_size' => 100 * 1024 * 1024, // 100MB
        'allowed_extensions' => [
            'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'],
            'videos' => ['mp4', 'avi', 'mov', 'wmv'],
            'audio' => ['mp3', 'wav', 'ogg'],
            'archives' => ['zip', 'rar', '7z'],
        ],
        'virus_scanning' => false,
        'automatic_optimization' => true,
        'version_control' => true,
        'access_control' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    */
    'api' => [
        'version' => 'v1',
        'rate_limiting' => [
            'enabled' => true,
            'requests_per_minute' => 60,
            'burst_limit' => 100,
        ],
        'authentication' => [
            'default' => 'sanctum',
            'methods' => ['sanctum', 'passport', 'jwt'],
        ],
        'documentation' => [
            'enabled' => true,
            'auto_generation' => true,
            'interactive' => true,
        ],
        'versioning' => [
            'strategy' => 'header', // header, url, parameter
            'header_name' => 'API-Version',
        ],
    ],
];
