<?php

return [
    /*
    |--------------------------------------------------------------------------
    | <PERSON><PERSON><PERSON><PERSON> Connect - Advanced Integration Configuration
    |--------------------------------------------------------------------------
    |
    | Enterprise-grade configuration for the advanced integration system
    | supporting microservices, API gateways, and external integrations
    |
    */

    'api_gateway' => [
        'default_environment' => env('API_GATEWAY_ENV', 'production'),
        'enable_distributed_tracing' => env('API_GATEWAY_TRACING', true),
        'enable_circuit_breaker' => env('API_GATEWAY_CIRCUIT_BREAKER', true),
        'enable_auto_scaling' => env('API_GATEWAY_AUTO_SCALING', true),
        'enable_blue_green_deployment' => env('API_GATEWAY_BLUE_GREEN', false),
        'enable_canary_deployment' => env('API_GATEWAY_CANARY', false),
        
        'load_balancer' => [
            'default_algorithm' => env('LB_ALGORITHM', 'adaptive_weighted'),
            'health_check_interval' => env('LB_HEALTH_CHECK_INTERVAL', 30),
            'max_connections_per_endpoint' => env('LB_MAX_CONNECTIONS', 1000),
            'connection_timeout' => env('LB_CONNECTION_TIMEOUT', 30),
            'enable_session_affinity' => env('LB_SESSION_AFFINITY', false),
            'enable_geographic_routing' => env('LB_GEOGRAPHIC_ROUTING', true),
        ],

        'circuit_breaker' => [
            'failure_threshold' => env('CB_FAILURE_THRESHOLD', 50),
            'request_volume_threshold' => env('CB_REQUEST_VOLUME_THRESHOLD', 20),
            'sleep_window_ms' => env('CB_SLEEP_WINDOW', 5000),
            'timeout_ms' => env('CB_TIMEOUT', 1000),
            'max_concurrent_requests' => env('CB_MAX_CONCURRENT', 10),
            'error_threshold_percentage' => env('CB_ERROR_THRESHOLD', 50),
            'metrics_rolling_window_ms' => env('CB_METRICS_WINDOW', 10000),
            'metrics_rolling_buckets' => env('CB_METRICS_BUCKETS', 10),
        ],

        'rate_limiting' => [
            'default_algorithm' => env('RL_ALGORITHM', 'sliding_window_counter'),
            'default_limit' => env('RL_DEFAULT_LIMIT', 100),
            'default_window' => env('RL_DEFAULT_WINDOW', 60),
            'enable_hierarchical' => env('RL_HIERARCHICAL', true),
            'enable_geographic' => env('RL_GEOGRAPHIC', true),
            'enable_adaptive' => env('RL_ADAPTIVE', true),
            'burst_multiplier' => env('RL_BURST_MULTIPLIER', 2),
        ],

        'caching' => [
            'default_ttl' => env('CACHE_DEFAULT_TTL', 300),
            'enable_distributed' => env('CACHE_DISTRIBUTED', true),
            'enable_compression' => env('CACHE_COMPRESSION', true),
            'compression_threshold' => env('CACHE_COMPRESSION_THRESHOLD', 1024),
            'cache_key_prefix' => env('CACHE_KEY_PREFIX', 'hesabiai_connect'),
            'invalidation_strategy' => env('CACHE_INVALIDATION', 'ttl'),
        ],

        'security' => [
            'enable_waf' => env('SECURITY_WAF', true),
            'enable_ddos_protection' => env('SECURITY_DDOS', true),
            'enable_threat_detection' => env('SECURITY_THREAT_DETECTION', true),
            'enable_behavioral_analysis' => env('SECURITY_BEHAVIORAL', true),
            'min_ip_reputation_score' => env('SECURITY_MIN_IP_SCORE', 50),
            'blocked_countries' => explode(',', env('SECURITY_BLOCKED_COUNTRIES', '')),
            'threat_detection_threshold' => env('SECURITY_THREAT_THRESHOLD', 0.7),
            'behavioral_anomaly_threshold' => env('SECURITY_BEHAVIORAL_THRESHOLD', 0.8),
            
            'threat_score_weights' => [
                'ip_reputation' => 0.2,
                'behavioral_anomaly' => 0.3,
                'ml_threat' => 0.4,
                'security_flags' => 0.1,
            ],

            'policies' => [
                'require_https' => env('SECURITY_REQUIRE_HTTPS', true),
                'validate_origin' => env('SECURITY_VALIDATE_ORIGIN', false),
                'enable_cors' => env('SECURITY_ENABLE_CORS', true),
                'enable_csrf_protection' => env('SECURITY_CSRF', false),
                'max_request_size' => env('SECURITY_MAX_REQUEST_SIZE', 10485760), // 10MB
            ],
        ],

        'monitoring' => [
            'enable_real_time' => env('MONITORING_REAL_TIME', true),
            'enable_metrics_collection' => env('MONITORING_METRICS', true),
            'enable_alerting' => env('MONITORING_ALERTING', true),
            'enable_anomaly_detection' => env('MONITORING_ANOMALY', true),
            'metrics_retention_days' => env('MONITORING_RETENTION', 30),
            'alert_cooldown_minutes' => env('MONITORING_ALERT_COOLDOWN', 5),
            
            'thresholds' => [
                'performance' => [
                    'response_time_warning' => env('PERF_RESPONSE_TIME_WARNING', 1000),
                    'response_time_critical' => env('PERF_RESPONSE_TIME_CRITICAL', 5000),
                    'memory_usage_warning' => env('PERF_MEMORY_WARNING', 80),
                    'memory_usage_critical' => env('PERF_MEMORY_CRITICAL', 95),
                    'cpu_usage_warning' => env('PERF_CPU_WARNING', 80),
                    'cpu_usage_critical' => env('PERF_CPU_CRITICAL', 95),
                ],
                'business' => [
                    'error_rate_warning' => env('BIZ_ERROR_RATE_WARNING', 5),
                    'error_rate_critical' => env('BIZ_ERROR_RATE_CRITICAL', 10),
                    'sla_compliance_warning' => env('BIZ_SLA_WARNING', 95),
                    'sla_compliance_critical' => env('BIZ_SLA_CRITICAL', 90),
                ],
            ],

            'health_score_weights' => [
                'cpu_usage' => 0.2,
                'memory_usage' => 0.2,
                'error_rate' => 0.3,
                'response_time_p95' => 0.2,
                'sla_compliance' => 0.1,
            ],
        ],

        'auto_scaling' => [
            'enable' => env('AUTO_SCALING_ENABLE', false),
            'min_instances' => env('AUTO_SCALING_MIN', 2),
            'max_instances' => env('AUTO_SCALING_MAX', 10),
            'target_cpu_utilization' => env('AUTO_SCALING_CPU_TARGET', 70),
            'target_memory_utilization' => env('AUTO_SCALING_MEMORY_TARGET', 80),
            'scale_up_cooldown' => env('AUTO_SCALING_UP_COOLDOWN', 300),
            'scale_down_cooldown' => env('AUTO_SCALING_DOWN_COOLDOWN', 600),
            'scale_up_threshold' => env('AUTO_SCALING_UP_THRESHOLD', 80),
            'scale_down_threshold' => env('AUTO_SCALING_DOWN_THRESHOLD', 30),
        ],

        'deployment' => [
            'strategy' => env('DEPLOYMENT_STRATEGY', 'rolling'),
            'blue_green' => [
                'enable_health_check' => true,
                'health_check_timeout' => 300,
                'traffic_shift_duration' => 600,
                'rollback_on_failure' => true,
            ],
            'canary' => [
                'initial_traffic_percentage' => 5,
                'traffic_increment' => 10,
                'increment_interval' => 300,
                'success_threshold' => 99.5,
                'error_threshold' => 1.0,
            ],
        ],
    ],

    'external_integrations' => [
        'default_timeout' => env('EXT_INT_TIMEOUT', 30),
        'default_retry_attempts' => env('EXT_INT_RETRY_ATTEMPTS', 3),
        'default_retry_delay' => env('EXT_INT_RETRY_DELAY', 5),
        'enable_circuit_breaker' => env('EXT_INT_CIRCUIT_BREAKER', true),
        'enable_rate_limiting' => env('EXT_INT_RATE_LIMITING', true),
        'enable_caching' => env('EXT_INT_CACHING', true),
        'enable_encryption' => env('EXT_INT_ENCRYPTION', true),
        
        'health_check' => [
            'interval' => env('EXT_INT_HEALTH_INTERVAL', 300),
            'timeout' => env('EXT_INT_HEALTH_TIMEOUT', 10),
            'failure_threshold' => env('EXT_INT_HEALTH_FAILURE_THRESHOLD', 3),
            'success_threshold' => env('EXT_INT_HEALTH_SUCCESS_THRESHOLD', 2),
        ],

        'sync' => [
            'default_frequency' => env('EXT_INT_SYNC_FREQUENCY', 'hourly'),
            'batch_size' => env('EXT_INT_BATCH_SIZE', 100),
            'max_concurrent_syncs' => env('EXT_INT_MAX_CONCURRENT', 5),
            'enable_incremental' => env('EXT_INT_INCREMENTAL', true),
            'enable_conflict_resolution' => env('EXT_INT_CONFLICT_RESOLUTION', true),
        ],

        'providers' => [
            'shopify' => [
                'api_version' => '2023-10',
                'rate_limit' => 40,
                'burst_limit' => 80,
                'webhook_verification' => true,
            ],
            'salla' => [
                'api_version' => 'v2',
                'rate_limit' => 60,
                'burst_limit' => 120,
                'webhook_verification' => true,
            ],
            'zatca' => [
                'environment' => env('ZATCA_ENV', 'production'),
                'compliance_level' => env('ZATCA_COMPLIANCE', 'simplified'),
                'certificate_validation' => true,
            ],
            'dgi_morocco' => [
                'environment' => env('DGI_ENV', 'production'),
                'tax_year' => env('DGI_TAX_YEAR', date('Y')),
                'declaration_type' => env('DGI_DECLARATION_TYPE', 'monthly'),
            ],
        ],
    ],

    'transformation' => [
        'enable_caching' => env('TRANSFORM_CACHING', true),
        'default_cache_ttl' => env('TRANSFORM_CACHE_TTL', 300),
        'track_lineage' => env('TRANSFORM_LINEAGE', true),
        'lineage_retention_hours' => env('TRANSFORM_LINEAGE_RETENTION', 24),
        'enable_validation' => env('TRANSFORM_VALIDATION', true),
        'enable_sanitization' => env('TRANSFORM_SANITIZATION', true),
        'enable_enrichment' => env('TRANSFORM_ENRICHMENT', true),
        'max_pipeline_steps' => env('TRANSFORM_MAX_STEPS', 20),
        'execution_timeout' => env('TRANSFORM_TIMEOUT', 30),
    ],

    'mcp' => [
        'enable' => env('MCP_ENABLE', true),
        'max_tokens_per_request' => env('MCP_MAX_TOKENS', 4000),
        'max_requests_per_minute' => env('MCP_MAX_REQUESTS', 60),
        'daily_token_limit' => env('MCP_DAILY_LIMIT', 100000),
        'enable_tool_calling' => env('MCP_TOOL_CALLING', true),
        'enable_resource_access' => env('MCP_RESOURCE_ACCESS', true),
        'enable_prompt_templates' => env('MCP_PROMPT_TEMPLATES', true),
        'security_level' => env('MCP_SECURITY_LEVEL', 'high'),
        
        'allowed_tools' => explode(',', env('MCP_ALLOWED_TOOLS', 'create_invoice,get_company_stats,send_notification')),
        'blocked_tools' => explode(',', env('MCP_BLOCKED_TOOLS', 'delete_data,system_shutdown')),
        
        'rate_limiting' => [
            'algorithm' => 'token_bucket',
            'burst_multiplier' => 2,
            'enable_adaptive' => true,
        ],
    ],

    'webhooks' => [
        'default_timeout' => env('WEBHOOK_TIMEOUT', 30),
        'max_retries' => env('WEBHOOK_MAX_RETRIES', 3),
        'retry_delay' => env('WEBHOOK_RETRY_DELAY', 5),
        'enable_signature_verification' => env('WEBHOOK_VERIFY_SIGNATURE', true),
        'signature_algorithm' => env('WEBHOOK_SIGNATURE_ALGO', 'sha256'),
        'enable_rate_limiting' => env('WEBHOOK_RATE_LIMITING', true),
        'rate_limit' => env('WEBHOOK_RATE_LIMIT', 100),
        'enable_logging' => env('WEBHOOK_LOGGING', true),
        'log_retention_days' => env('WEBHOOK_LOG_RETENTION', 30),
    ],

    'analytics' => [
        'enable_real_time' => env('ANALYTICS_REAL_TIME', true),
        'enable_predictive' => env('ANALYTICS_PREDICTIVE', true),
        'enable_business_intelligence' => env('ANALYTICS_BI', true),
        'data_retention_days' => env('ANALYTICS_RETENTION', 365),
        'aggregation_intervals' => ['1m', '5m', '15m', '1h', '6h', '1d', '1w', '1M'],
        'enable_anomaly_detection' => env('ANALYTICS_ANOMALY', true),
        'enable_forecasting' => env('ANALYTICS_FORECASTING', true),
    ],

    'compliance' => [
        'enable_gdpr' => env('COMPLIANCE_GDPR', true),
        'enable_pci_dss' => env('COMPLIANCE_PCI_DSS', false),
        'enable_sox' => env('COMPLIANCE_SOX', false),
        'enable_hipaa' => env('COMPLIANCE_HIPAA', false),
        'data_retention_policy' => env('COMPLIANCE_DATA_RETENTION', 'gdpr'),
        'audit_log_retention' => env('COMPLIANCE_AUDIT_RETENTION', 2555), // 7 years
        'enable_data_anonymization' => env('COMPLIANCE_ANONYMIZATION', true),
        'enable_right_to_be_forgotten' => env('COMPLIANCE_RIGHT_TO_FORGET', true),
    ],

    'performance' => [
        'enable_optimization' => env('PERFORMANCE_OPTIMIZATION', true),
        'enable_compression' => env('PERFORMANCE_COMPRESSION', true),
        'enable_minification' => env('PERFORMANCE_MINIFICATION', true),
        'enable_cdn' => env('PERFORMANCE_CDN', false),
        'cdn_provider' => env('PERFORMANCE_CDN_PROVIDER', 'cloudflare'),
        'cache_strategy' => env('PERFORMANCE_CACHE_STRATEGY', 'multi_layer'),
        'enable_http2' => env('PERFORMANCE_HTTP2', true),
        'enable_http3' => env('PERFORMANCE_HTTP3', false),
    ],

    'disaster_recovery' => [
        'enable' => env('DR_ENABLE', false),
        'backup_frequency' => env('DR_BACKUP_FREQUENCY', 'daily'),
        'backup_retention' => env('DR_BACKUP_RETENTION', 30),
        'enable_cross_region' => env('DR_CROSS_REGION', false),
        'rto_minutes' => env('DR_RTO', 60), // Recovery Time Objective
        'rpo_minutes' => env('DR_RPO', 15), // Recovery Point Objective
        'enable_automated_failover' => env('DR_AUTO_FAILOVER', false),
    ],

    'cost_optimization' => [
        'enable' => env('COST_OPTIMIZATION', true),
        'enable_resource_scheduling' => env('COST_RESOURCE_SCHEDULING', true),
        'enable_spot_instances' => env('COST_SPOT_INSTANCES', false),
        'enable_reserved_instances' => env('COST_RESERVED_INSTANCES', true),
        'cost_alerts_threshold' => env('COST_ALERT_THRESHOLD', 1000),
        'budget_monthly' => env('COST_BUDGET_MONTHLY', 5000),
    ],
];
