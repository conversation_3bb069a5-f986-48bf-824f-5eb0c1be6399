<?php

namespace App\Domains\Compliance\Events;

use App\Domains\Compliance\Models\GovernmentIntegration;
use App\Domains\Compliance\Models\Company;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إكمال التقديم الحكومي
 */
class GovernmentSubmissionCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public GovernmentIntegration $integration,
        public Company $company,
        public string $operation,
        public array $submissionData,
        public array $response,
        public bool $success = true
    ) {}

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'system_name' => $this->integration->system_name,
            'country_code' => $this->integration->country->code,
            'company_id' => $this->company->id,
            'operation' => $this->operation,
            'success' => $this->success,
            'response_data' => $this->response,
            'submitted_at' => now(),
        ];
    }
}
