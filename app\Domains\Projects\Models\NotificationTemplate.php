<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قالب الإشعار - Notification Template
 */
class NotificationTemplate extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'type',
        'subject_template',
        'body_template',
        'variables',
        'channels',
        'is_active',
        'language',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'variables' => 'array',
        'channels' => 'array',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }
}
