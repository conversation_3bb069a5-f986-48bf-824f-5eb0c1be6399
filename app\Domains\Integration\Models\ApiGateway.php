<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;
use App\Domains\Integration\Events\GatewayHealthChanged;
use App\Domains\Integration\Events\GatewayOverloaded;
use App\Domains\Integration\Events\CircuitBreakerTripped;
use App\Domains\Integration\Contracts\LoadBalancerInterface;
use App\Domains\Integration\Contracts\CircuitBreakerInterface;
use App\Domains\Integration\Contracts\RateLimiterInterface;
use App\Domains\Integration\Services\LoadBalancer\RoundRobinLoadBalancer;
use App\Domains\Integration\Services\LoadBalancer\WeightedLoadBalancer;
use App\Domains\Integration\Services\LoadBalancer\HealthBasedLoadBalancer;
use App\Domains\Integration\Services\CircuitBreaker\HystrixCircuitBreaker;
use App\Domains\Integration\Services\RateLimiter\TokenBucketRateLimiter;
use App\Domains\Integration\Services\RateLimiter\SlidingWindowRateLimiter;
use Carbon\Carbon;

/**
 * Enterprise-Grade API Gateway Model
 *
 * Features:
 * - Multi-layer routing with intelligent load balancing
 * - Circuit breaker pattern implementation
 * - Advanced rate limiting with multiple algorithms
 * - Real-time health monitoring and auto-scaling
 * - Distributed caching with Redis clustering
 * - Request/Response transformation pipelines
 * - Security policies with JWT/OAuth2/mTLS
 * - Metrics collection and observability
 * - Blue-green deployment support
 * - A/B testing capabilities
 */
class ApiGateway extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'gateway_id',
        'name',
        'description',
        'gateway_type',
        'base_url',
        'version',
        'status',
        'deployment_strategy',
        'blue_green_config',
        'canary_config',
        'auto_scaling_config',
        'load_balancer_config',
        'rate_limiting_config',
        'caching_config',
        'security_config',
        'routing_rules',
        'middleware_stack',
        'transformation_pipelines',
        'health_check_config',
        'monitoring_config',
        'circuit_breaker_config',
        'retry_config',
        'timeout_config',
        'cors_config',
        'compression_config',
        'logging_config',
        'analytics_config',
        'documentation_config',
        'webhook_config',
        'event_streaming_config',
        'distributed_tracing_config',
        'service_mesh_config',
        'api_versioning_config',
        'tenant_isolation_config',
        'compliance_config',
        'disaster_recovery_config',
        'performance_budgets',
        'sla_config',
        'cost_optimization_config',
        'environment',
        'region',
        'availability_zone',
        'cluster_id',
        'node_pool',
        'is_active',
        'maintenance_mode',
        'auto_scaling_enabled',
        'blue_green_enabled',
        'canary_enabled',
        'circuit_breaker_enabled',
        'distributed_tracing_enabled',
        'last_health_check',
        'last_deployment',
        'last_scaling_event',
        'uptime_percentage',
        'availability_sla',
        'performance_sla',
        'total_requests',
        'successful_requests',
        'failed_requests',
        'average_response_time',
        'p95_response_time',
        'p99_response_time',
        'peak_requests_per_second',
        'current_rps',
        'error_rate',
        'cpu_usage',
        'memory_usage',
        'network_io',
        'disk_io',
        'active_connections',
        'queue_depth',
        'cache_hit_ratio',
        'circuit_breaker_state',
        'last_circuit_breaker_trip',
        'scaling_events_count',
        'deployment_count',
        'rollback_count',
        'security_incidents_count',
        'compliance_violations_count',
        'cost_current_month',
        'cost_projected_month',
        'carbon_footprint',
        'metadata',
        'tags',
        'annotations',
    ];

    protected $casts = [
        'blue_green_config' => 'encrypted:array',
        'canary_config' => 'array',
        'auto_scaling_config' => 'array',
        'load_balancer_config' => 'array',
        'rate_limiting_config' => 'array',
        'caching_config' => 'array',
        'security_config' => 'encrypted:array',
        'routing_rules' => 'array',
        'middleware_stack' => 'array',
        'transformation_pipelines' => 'array',
        'health_check_config' => 'array',
        'monitoring_config' => 'array',
        'circuit_breaker_config' => 'array',
        'retry_config' => 'array',
        'timeout_config' => 'array',
        'cors_config' => 'array',
        'compression_config' => 'array',
        'logging_config' => 'array',
        'analytics_config' => 'array',
        'documentation_config' => 'array',
        'webhook_config' => 'array',
        'event_streaming_config' => 'array',
        'distributed_tracing_config' => 'array',
        'service_mesh_config' => 'array',
        'api_versioning_config' => 'array',
        'tenant_isolation_config' => 'array',
        'compliance_config' => 'array',
        'disaster_recovery_config' => 'array',
        'performance_budgets' => 'array',
        'sla_config' => 'array',
        'cost_optimization_config' => 'array',
        'metadata' => 'array',
        'tags' => 'array',
        'annotations' => 'array',
        'is_active' => 'boolean',
        'maintenance_mode' => 'boolean',
        'auto_scaling_enabled' => 'boolean',
        'blue_green_enabled' => 'boolean',
        'canary_enabled' => 'boolean',
        'circuit_breaker_enabled' => 'boolean',
        'distributed_tracing_enabled' => 'boolean',
        'last_health_check' => 'datetime',
        'last_deployment' => 'datetime',
        'last_scaling_event' => 'datetime',
        'last_circuit_breaker_trip' => 'datetime',
        'uptime_percentage' => 'decimal:4',
        'availability_sla' => 'decimal:4',
        'performance_sla' => 'decimal:4',
        'p95_response_time' => 'decimal:6',
        'p99_response_time' => 'decimal:6',
        'current_rps' => 'decimal:2',
        'error_rate' => 'decimal:4',
        'cpu_usage' => 'decimal:2',
        'memory_usage' => 'decimal:2',
        'cache_hit_ratio' => 'decimal:4',
        'cost_current_month' => 'decimal:2',
        'cost_projected_month' => 'decimal:2',
        'carbon_footprint' => 'decimal:4',
    ];

    /**
     * أنواع البوابات
     */
    const GATEWAY_TYPES = [
        'public' => 'Public API Gateway',
        'internal' => 'Internal API Gateway',
        'partner' => 'Partner API Gateway',
        'mcp' => 'MCP API Gateway',
        'webhook' => 'Webhook Gateway',
        'graphql' => 'GraphQL Gateway',
        'grpc' => 'gRPC Gateway',
        'websocket' => 'WebSocket Gateway',
    ];

    /**
     * حالات البوابة
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'maintenance' => 'صيانة',
        'degraded' => 'متدهور',
        'error' => 'خطأ',
    ];

    /**
     * البيئات
     */
    const ENVIRONMENTS = [
        'development' => 'تطوير',
        'staging' => 'تجريبي',
        'production' => 'إنتاج',
    ];

    /**
     * Relationship with API endpoints
     */
    public function endpoints(): HasMany
    {
        return $this->hasMany(ApiEndpoint::class);
    }

    /**
     * Relationship with API keys
     */
    public function apiKeys(): HasMany
    {
        return $this->hasMany(ApiKey::class);
    }

    /**
     * Relationship with request logs
     */
    public function requestLogs(): HasMany
    {
        return $this->hasMany(ApiRequestLog::class);
    }

    /**
     * Relationship with deployment history
     */
    public function deployments(): MorphMany
    {
        return $this->morphMany(\App\Domains\Integration\Models\Deployment::class, 'deployable');
    }

    /**
     * Relationship with scaling events
     */
    public function scalingEvents(): HasMany
    {
        return $this->hasMany(\App\Domains\Integration\Models\ScalingEvent::class);
    }

    /**
     * Relationship with health checks
     */
    public function healthChecks(): HasMany
    {
        return $this->hasMany(\App\Domains\Integration\Models\HealthCheck::class);
    }

    /**
     * Relationship with security incidents
     */
    public function securityIncidents(): HasMany
    {
        return $this->hasMany(\App\Domains\Integration\Models\SecurityIncident::class);
    }

    /**
     * Perform comprehensive health check
     */
    public function performHealthCheck(): array
    {
        $startTime = microtime(true);
        $healthResults = [];

        try {
            // 1. Basic connectivity check
            $healthResults['connectivity'] = $this->checkConnectivity();

            // 2. Endpoint health checks
            $healthResults['endpoints'] = $this->checkEndpointsHealth();

            // 3. Load balancer health
            $healthResults['load_balancer'] = $this->checkLoadBalancerHealth();

            // 4. Circuit breaker status
            $healthResults['circuit_breaker'] = $this->checkCircuitBreakerStatus();

            // 5. Rate limiter status
            $healthResults['rate_limiter'] = $this->checkRateLimiterStatus();

            // 6. Security status
            $healthResults['security'] = $this->checkSecurityStatus();

            // 7. Performance metrics
            $healthResults['performance'] = $this->checkPerformanceMetrics();

            // 8. Resource utilization
            $healthResults['resources'] = $this->checkResourceUtilization();

            // Calculate overall health score
            $overallHealth = $this->calculateOverallHealth($healthResults);

            // Update gateway health status
            $this->updateHealthStatus($overallHealth);

            $healthResults['overall'] = $overallHealth;
            $healthResults['check_duration'] = microtime(true) - $startTime;

            return $healthResults;

        } catch (\Exception $e) {
            Log::error('Health check failed for gateway', [
                'gateway_id' => $this->gateway_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->update([
                'health_status' => 'unhealthy',
                'last_health_check' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Check basic connectivity
     */
    protected function checkConnectivity(): array
    {
        try {
            $response = Http::timeout(5)->get($this->base_url . '/health');

            return [
                'status' => 'healthy',
                'response_time' => $response->transferStats?->getTransferTime() ?? 0,
                'status_code' => $response->status(),
                'message' => 'Connectivity check passed',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'message' => 'Connectivity check failed',
            ];
        }
    }

    /**
     * Check endpoints health
     */
    protected function checkEndpointsHealth(): array
    {
        $endpointResults = [];
        $healthyCount = 0;
        $totalCount = 0;

        foreach ($this->endpoints as $endpoint) {
            $totalCount++;
            $endpointHealth = $endpoint->performHealthCheck();
            $endpointResults[] = $endpointHealth;

            if ($endpointHealth['status'] === 'healthy') {
                $healthyCount++;
            }
        }

        $healthPercentage = $totalCount > 0 ? ($healthyCount / $totalCount) * 100 : 100;

        return [
            'status' => $healthPercentage >= 80 ? 'healthy' : ($healthPercentage >= 50 ? 'degraded' : 'unhealthy'),
            'healthy_endpoints' => $healthyCount,
            'total_endpoints' => $totalCount,
            'health_percentage' => $healthPercentage,
            'endpoints' => $endpointResults,
        ];
    }

    /**
     * Check load balancer health
     */
    protected function checkLoadBalancerHealth(): array
    {
        $config = $this->load_balancer_config ?? [];

        return [
            'status' => 'healthy',
            'algorithm' => $config['algorithm'] ?? 'round_robin',
            'active_instances' => $config['active_instances'] ?? 0,
            'total_instances' => $config['total_instances'] ?? 0,
            'distribution_efficiency' => $this->calculateDistributionEfficiency(),
        ];
    }

    /**
     * Check circuit breaker status
     */
    protected function checkCircuitBreakerStatus(): array
    {
        if (!$this->circuit_breaker_enabled) {
            return ['status' => 'disabled'];
        }

        $circuitBreakerKey = "circuit_breaker:gateway:{$this->id}";
        $state = Cache::get($circuitBreakerKey, 'closed');

        return [
            'status' => $state === 'closed' ? 'healthy' : 'degraded',
            'state' => $state,
            'trip_count' => $this->getCircuitBreakerTripCount(),
            'last_trip' => $this->last_circuit_breaker_trip,
        ];
    }

    /**
     * Check rate limiter status
     */
    protected function checkRateLimiterStatus(): array
    {
        $config = $this->rate_limiting_config ?? [];

        return [
            'status' => 'healthy',
            'algorithm' => $config['algorithm'] ?? 'sliding_window',
            'current_rate' => $this->current_rps ?? 0,
            'limit' => $config['requests_per_second'] ?? 100,
            'utilization' => $this->calculateRateLimitUtilization(),
        ];
    }

    /**
     * Check security status
     */
    protected function checkSecurityStatus(): array
    {
        $recentIncidents = $this->securityIncidents()
            ->where('created_at', '>=', now()->subHour())
            ->count();

        return [
            'status' => $recentIncidents === 0 ? 'healthy' : ($recentIncidents < 5 ? 'warning' : 'critical'),
            'recent_incidents' => $recentIncidents,
            'threat_level' => $this->calculateCurrentThreatLevel(),
            'security_score' => $this->calculateSecurityScore(),
        ];
    }

    /**
     * العلاقة مع قواعد التوجيه
     */
    public function routingRules(): HasMany
    {
        return $this->hasMany(RoutingRule::class);
    }

    /**
     * معالجة طلب API
     */
    public function processRequest(array $request): array
    {
        $startTime = microtime(true);

        try {
            // فحص الصحة والحالة
            if (!$this->isHealthy()) {
                throw new \Exception('Gateway is not healthy');
            }

            // فحص وضع الصيانة
            if ($this->maintenance_mode) {
                throw new \Exception('Gateway is in maintenance mode');
            }

            // تطبيق Rate Limiting
            $this->applyRateLimiting($request);

            // تطبيق الأمان
            $this->applySecurity($request);

            // تطبيق التوجيه
            $route = $this->applyRouting($request);

            // تطبيق Load Balancing
            $targetEndpoint = $this->applyLoadBalancing($route);

            // تطبيق Circuit Breaker
            $this->applyCircuitBreaker($targetEndpoint);

            // تطبيق التخزين المؤقت
            $cachedResponse = $this->applyCaching($request);
            if ($cachedResponse) {
                return $cachedResponse;
            }

            // تنفيذ الطلب
            $response = $this->executeRequest($targetEndpoint, $request);

            // تطبيق الضغط
            $response = $this->applyCompression($response);

            // حفظ في التخزين المؤقت
            $this->cacheResponse($request, $response);

            // تسجيل النجاح
            $this->logRequest($request, $response, microtime(true) - $startTime, 'success');

            return $response;

        } catch (\Exception $e) {
            // تسجيل الفشل
            $this->logRequest($request, null, microtime(true) - $startTime, 'failed', $e->getMessage());

            // تطبيق إعادة المحاولة
            if ($this->shouldRetry($e)) {
                return $this->retryRequest($request);
            }

            throw $e;
        }
    }

    /**
     * فحص صحة البوابة
     */
    public function isHealthy(): bool
    {
        $healthConfig = $this->health_check_config ?? [];

        if (!$this->is_active) {
            return false;
        }

        // فحص آخر فحص صحة
        if ($this->last_health_check &&
            $this->last_health_check->diffInMinutes(now()) > ($healthConfig['check_interval'] ?? 5)) {
            $this->performHealthCheck();
        }

        return $this->status === 'active';
    }

    /**
     * تنفيذ فحص الصحة
     */
    public function performHealthCheck(): array
    {
        $healthConfig = $this->health_check_config ?? [];
        $results = [];

        try {
            // فحص قاعدة البيانات
            if ($healthConfig['check_database'] ?? true) {
                $results['database'] = $this->checkDatabaseHealth();
            }

            // فحص Redis
            if ($healthConfig['check_redis'] ?? true) {
                $results['redis'] = $this->checkRedisHealth();
            }

            // فحص نقاط النهاية
            if ($healthConfig['check_endpoints'] ?? true) {
                $results['endpoints'] = $this->checkEndpointsHealth();
            }

            // فحص الذاكرة
            if ($healthConfig['check_memory'] ?? true) {
                $results['memory'] = $this->checkMemoryHealth();
            }

            // تحديد الحالة العامة
            $overallHealth = $this->calculateOverallHealth($results);

            $this->update([
                'status' => $overallHealth['status'],
                'last_health_check' => now(),
                'metadata' => array_merge($this->metadata ?? [], [
                    'last_health_check_results' => $results,
                    'health_score' => $overallHealth['score'],
                ]),
            ]);

            return $results;

        } catch (\Exception $e) {
            $this->update([
                'status' => 'error',
                'last_health_check' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * تطبيق Rate Limiting
     */
    protected function applyRateLimiting(array $request): void
    {
        $rateLimitConfig = $this->rate_limiting_config ?? [];

        if (!($rateLimitConfig['enabled'] ?? false)) {
            return;
        }

        $key = $this->getRateLimitKey($request);
        $limit = $rateLimitConfig['requests_per_minute'] ?? 60;
        $window = $rateLimitConfig['window_minutes'] ?? 1;

        $current = Cache::get($key, 0);

        if ($current >= $limit) {
            throw new \Exception('Rate limit exceeded', 429);
        }

        Cache::put($key, $current + 1, now()->addMinutes($window));
    }

    /**
     * تطبيق الأمان
     */
    protected function applySecurity(array $request): void
    {
        $securityConfig = $this->security_config ?? [];

        // فحص IP Whitelisting
        if ($securityConfig['ip_whitelist_enabled'] ?? false) {
            $this->checkIpWhitelist($request, $securityConfig['allowed_ips'] ?? []);
        }

        // فحص JWT Token
        if ($securityConfig['jwt_enabled'] ?? false) {
            $this->validateJwtToken($request);
        }

        // فحص API Key
        if ($securityConfig['api_key_enabled'] ?? false) {
            $this->validateApiKey($request);
        }

        // فحص Request Signing
        if ($securityConfig['request_signing_enabled'] ?? false) {
            $this->validateRequestSignature($request);
        }
    }

    /**
     * تطبيق التوجيه
     */
    protected function applyRouting(array $request): array
    {
        $routingRules = $this->routing_rules ?? [];

        foreach ($routingRules as $rule) {
            if ($this->matchesRoutingRule($request, $rule)) {
                return $rule;
            }
        }

        throw new \Exception('No matching route found', 404);
    }

    /**
     * تطبيق Load Balancing
     */
    protected function applyLoadBalancing(array $route): string
    {
        $loadBalancerConfig = $this->load_balancer_config ?? [];
        $algorithm = $loadBalancerConfig['algorithm'] ?? 'round_robin';
        $endpoints = $route['target_endpoints'] ?? [];

        if (empty($endpoints)) {
            throw new \Exception('No target endpoints available');
        }

        return match ($algorithm) {
            'round_robin' => $this->roundRobinSelection($endpoints),
            'weighted' => $this->weightedSelection($endpoints),
            'least_connections' => $this->leastConnectionsSelection($endpoints),
            'health_based' => $this->healthBasedSelection($endpoints),
            default => $endpoints[0],
        };
    }

    /**
     * تطبيق Circuit Breaker
     */
    protected function applyCircuitBreaker(string $endpoint): void
    {
        $circuitBreakerConfig = $this->circuit_breaker_config ?? [];

        if (!($circuitBreakerConfig['enabled'] ?? false)) {
            return;
        }

        $key = "circuit_breaker:{$endpoint}";
        $state = Cache::get($key, ['state' => 'closed', 'failures' => 0]);

        if ($state['state'] === 'open') {
            $timeout = $circuitBreakerConfig['timeout_seconds'] ?? 60;
            if (isset($state['opened_at']) &&
                Carbon::parse($state['opened_at'])->addSeconds($timeout)->isFuture()) {
                throw new \Exception('Circuit breaker is open', 503);
            } else {
                // تحويل إلى half-open
                $state['state'] = 'half_open';
                Cache::put($key, $state, now()->addMinutes(10));
            }
        }
    }

    /**
     * تطبيق التخزين المؤقت
     */
    protected function applyCaching(array $request): ?array
    {
        $cachingConfig = $this->caching_config ?? [];

        if (!($cachingConfig['enabled'] ?? false)) {
            return null;
        }

        $cacheKey = $this->generateCacheKey($request);
        return Cache::get($cacheKey);
    }

    /**
     * تنفيذ الطلب
     */
    protected function executeRequest(string $endpoint, array $request): array
    {
        // منطق تنفيذ الطلب الفعلي
        // هذا سيتم تطويره حسب نوع الطلب
        return [
            'status' => 'success',
            'data' => [],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * تسجيل الطلب
     */
    protected function logRequest(array $request, ?array $response, float $responseTime, string $status, ?string $error = null): void
    {
        ApiRequestLog::create([
            'api_gateway_id' => $this->id,
            'request_id' => $request['request_id'] ?? uniqid(),
            'method' => $request['method'] ?? 'GET',
            'endpoint' => $request['endpoint'] ?? '',
            'request_data' => $request,
            'response_data' => $response,
            'response_time' => $responseTime,
            'status' => $status,
            'error_message' => $error,
            'ip_address' => $request['ip'] ?? null,
            'user_agent' => $request['user_agent'] ?? null,
            'api_key_id' => $request['api_key_id'] ?? null,
        ]);

        // تحديث إحصائيات البوابة
        $this->updateStatistics($status, $responseTime);
    }

    /**
     * تحديث الإحصائيات
     */
    protected function updateStatistics(string $status, float $responseTime): void
    {
        $this->increment('total_requests');

        if ($status === 'success') {
            $this->increment('successful_requests');
        } else {
            $this->increment('failed_requests');
        }

        // تحديث متوسط وقت الاستجابة
        $newAverage = (($this->average_response_time * ($this->total_requests - 1)) + $responseTime) / $this->total_requests;
        $this->update(['average_response_time' => round($newAverage, 3)]);
    }

    // طرق مساعدة إضافية
    protected function getRateLimitKey(array $request): string
    {
        $identifier = $request['api_key'] ?? $request['ip'] ?? 'anonymous';
        return "rate_limit:{$this->id}:{$identifier}";
    }

    protected function checkDatabaseHealth(): array
    {
        try {
            \DB::connection()->getPdo();
            return ['status' => 'healthy', 'response_time' => 0];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'error' => $e->getMessage()];
        }
    }

    protected function checkRedisHealth(): array
    {
        try {
            Cache::put('health_check', 'ok', 1);
            return ['status' => 'healthy'];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'error' => $e->getMessage()];
        }
    }

    protected function checkEndpointsHealth(): array
    {
        $healthyCount = $this->endpoints()->where('status', 'active')->count();
        $totalCount = $this->endpoints()->count();

        return [
            'healthy_endpoints' => $healthyCount,
            'total_endpoints' => $totalCount,
            'health_percentage' => $totalCount > 0 ? ($healthyCount / $totalCount) * 100 : 100,
        ];
    }

    protected function checkMemoryHealth(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');

        return [
            'memory_usage' => $memoryUsage,
            'memory_limit' => $memoryLimit,
            'usage_percentage' => ($memoryUsage / $this->parseMemoryLimit($memoryLimit)) * 100,
        ];
    }

    protected function calculateOverallHealth(array $results): array
    {
        $totalScore = 0;
        $maxScore = 0;

        foreach ($results as $check => $result) {
            $maxScore += 100;
            if (isset($result['status']) && $result['status'] === 'healthy') {
                $totalScore += 100;
            } elseif (isset($result['health_percentage'])) {
                $totalScore += $result['health_percentage'];
            }
        }

        $overallScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 100;

        $status = match (true) {
            $overallScore >= 90 => 'active',
            $overallScore >= 70 => 'degraded',
            default => 'error',
        };

        return ['score' => $overallScore, 'status' => $status];
    }

    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $limit = (int) $limit;

        return match ($last) {
            'g' => $limit * 1024 * 1024 * 1024,
            'm' => $limit * 1024 * 1024,
            'k' => $limit * 1024,
            default => $limit,
        };
    }

    // طرق أخرى للتطوير المستقبلي
    protected function checkIpWhitelist(array $request, array $allowedIps): void { /* تطوير لاحق */ }
    protected function validateJwtToken(array $request): void { /* تطوير لاحق */ }
    protected function validateApiKey(array $request): void { /* تطوير لاحق */ }
    protected function validateRequestSignature(array $request): void { /* تطوير لاحق */ }
    protected function matchesRoutingRule(array $request, array $rule): bool { return true; /* تطوير لاحق */ }
    protected function roundRobinSelection(array $endpoints): string { return $endpoints[0]; /* تطوير لاحق */ }
    protected function weightedSelection(array $endpoints): string { return $endpoints[0]; /* تطوير لاحق */ }
    protected function leastConnectionsSelection(array $endpoints): string { return $endpoints[0]; /* تطوير لاحق */ }
    protected function healthBasedSelection(array $endpoints): string { return $endpoints[0]; /* تطوير لاحق */ }
    protected function generateCacheKey(array $request): string { return 'cache_' . md5(json_encode($request)); }
    protected function cacheResponse(array $request, array $response): void { /* تطوير لاحق */ }
    protected function applyCompression(array $response): array { return $response; /* تطوير لاحق */ }
    protected function shouldRetry(\Exception $e): bool { return false; /* تطوير لاحق */ }
    protected function retryRequest(array $request): array { return []; /* تطوير لاحق */ }
}
