<?php

namespace App\Domains\Accounting\Console\Commands;

use Illuminate\Console\Command;

class ProcessRecurringInvoicesCommand extends Command
{
    protected $signature = 'accounting:process-recurring-invoices';
    protected $description = 'Process recurring invoices';

    public function handle()
    {
        $this->info('Processing recurring invoices...');
        return self::SUCCESS;
    }
}
