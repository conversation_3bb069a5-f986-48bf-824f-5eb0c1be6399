<?php

namespace App\Domains\Projects\Events;

use App\Domains\Projects\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث تغيير حالة المهمة
 */
class TaskStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Task $task;
    public string $oldStatus;
    public string $newStatus;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Task $task, string $oldStatus, string $newStatus)
    {
        $this->task = $task;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    /**
     * الحصول على القنوات التي يجب بث الحدث عليها
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('project.' . $this->task->project_id),
            new PrivateChannel('task.' . $this->task->id),
        ];

        // إضافة قناة المكلف
        if ($this->task->assignee_id) {
            $channels[] = new PrivateChannel('user.' . $this->task->assignee_id);
        }

        // إضافة قناة المبلغ
        if ($this->task->reporter_id) {
            $channels[] = new PrivateChannel('user.' . $this->task->reporter_id);
        }

        // إضافة قناة مدير المشروع
        if ($this->task->project->project_manager_id) {
            $channels[] = new PrivateChannel('user.' . $this->task->project->project_manager_id);
        }

        // إضافة قنوات المتابعين
        foreach ($this->task->watchers as $watcher) {
            $channels[] = new PrivateChannel('user.' . $watcher->id);
        }

        return $channels;
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'task.status.changed';
    }

    /**
     * البيانات المرسلة مع الحدث
     */
    public function broadcastWith(): array
    {
        return [
            'task' => [
                'id' => $this->task->id,
                'title' => $this->task->title,
                'task_number' => $this->task->task_number,
                'old_status' => $this->oldStatus,
                'new_status' => $this->newStatus,
                'project_id' => $this->task->project_id,
                'project_name' => $this->task->project->name,
                'assignee' => $this->task->assignee?->name,
                'updated_at' => $this->task->updated_at->toISOString(),
            ],
            'message' => "تم تغيير حالة المهمة '{$this->task->title}' من {$this->oldStatus} إلى {$this->newStatus}",
            'timestamp' => now()->toISOString(),
        ];
    }
}
