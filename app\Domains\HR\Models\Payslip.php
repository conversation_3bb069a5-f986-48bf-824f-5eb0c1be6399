<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج كشف الراتب - نظام الرواتب المتقدم متعدد القوانين
 */
class Payslip extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'employee_id',
        'payroll_period_id',
        'pay_period_start',
        'pay_period_end',
        'pay_date',
        'payment_method',
        'bank_account_id',
        
        // Basic Salary Components
        'basic_salary',
        'gross_salary',
        'net_salary',
        'total_earnings',
        'total_deductions',
        'total_benefits',
        
        // Detailed Breakdowns
        'earnings_breakdown',
        'deductions_breakdown',
        'benefits_breakdown',
        'tax_breakdown',
        'social_security_breakdown',
        
        // Attendance & Time
        'working_days',
        'attended_days',
        'absent_days',
        'late_days',
        'overtime_hours',
        'leave_days_taken',
        
        // Allowances
        'housing_allowance',
        'transport_allowance',
        'meal_allowance',
        'communication_allowance',
        'other_allowances',
        
        // Deductions
        'income_tax',
        'social_security_employee',
        'social_security_employer',
        'medical_insurance',
        'loan_deductions',
        'advance_deductions',
        'other_deductions',
        
        // Bonuses & Incentives
        'performance_bonus',
        'attendance_bonus',
        'overtime_bonus',
        'commission',
        'annual_bonus_prorated',
        
        // End of Service
        'end_of_service_provision',
        'leave_encashment',
        
        // Status & Processing
        'status',
        'calculated_at',
        'approved_by',
        'approved_at',
        'paid_at',
        'payment_reference',
        
        // Digital Features
        'qr_code',
        'digital_signature',
        'pdf_path',
        'email_sent_at',
        
        // Currency & Exchange
        'currency',
        'exchange_rate',
        'base_currency_amount',
        
        // Compliance & Reporting
        'tax_year',
        'tax_month',
        'social_security_number',
        'bank_file_reference',
        'government_reporting_data',
        
        'notes',
        'metadata',
    ];

    protected $casts = [
        'pay_period_start' => 'date',
        'pay_period_end' => 'date',
        'pay_date' => 'date',
        'calculated_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
        'email_sent_at' => 'datetime',
        
        // Amounts
        'basic_salary' => 'decimal:2',
        'gross_salary' => 'decimal:2',
        'net_salary' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'total_benefits' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'base_currency_amount' => 'decimal:2',
        
        // Allowances
        'housing_allowance' => 'decimal:2',
        'transport_allowance' => 'decimal:2',
        'meal_allowance' => 'decimal:2',
        'communication_allowance' => 'decimal:2',
        'other_allowances' => 'decimal:2',
        
        // Deductions
        'income_tax' => 'decimal:2',
        'social_security_employee' => 'decimal:2',
        'social_security_employer' => 'decimal:2',
        'medical_insurance' => 'decimal:2',
        'loan_deductions' => 'decimal:2',
        'advance_deductions' => 'decimal:2',
        'other_deductions' => 'decimal:2',
        
        // Bonuses
        'performance_bonus' => 'decimal:2',
        'attendance_bonus' => 'decimal:2',
        'overtime_bonus' => 'decimal:2',
        'commission' => 'decimal:2',
        'annual_bonus_prorated' => 'decimal:2',
        
        // End of Service
        'end_of_service_provision' => 'decimal:2',
        'leave_encashment' => 'decimal:2',
        
        // Time
        'overtime_hours' => 'decimal:2',
        
        // Arrays
        'earnings_breakdown' => 'array',
        'deductions_breakdown' => 'array',
        'benefits_breakdown' => 'array',
        'tax_breakdown' => 'array',
        'social_security_breakdown' => 'array',
        'government_reporting_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * حالات كشف الراتب
     */
    const STATUSES = [
        'DRAFT' => 'مسودة',
        'CALCULATED' => 'محسوب',
        'REVIEWED' => 'مراجع',
        'APPROVED' => 'معتمد',
        'PAID' => 'مدفوع',
        'CANCELLED' => 'ملغي',
        'DISPUTED' => 'متنازع عليه',
        'FINALIZED' => 'نهائي',
    ];

    /**
     * طرق الدفع
     */
    const PAYMENT_METHODS = [
        'BANK_TRANSFER' => 'تحويل بنكي',
        'CASH' => 'نقداً',
        'CHECK' => 'شيك',
        'DIGITAL_WALLET' => 'محفظة رقمية',
        'PAYROLL_CARD' => 'بطاقة راتب',
    ];

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * فترة الراتب
     */
    public function payrollPeriod(): BelongsTo
    {
        return $this->belongsTo(PayrollPeriod::class);
    }

    /**
     * الحساب البنكي
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * الشخص الذي اعتمد الكشف
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * تفاصيل الدفع
     */
    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PayslipPayment::class);
    }

    /**
     * اعتماد كشف الراتب
     */
    public function approve(Employee $approver): bool
    {
        if ($this->status !== 'CALCULATED' && $this->status !== 'REVIEWED') {
            return false;
        }

        $this->update([
            'status' => 'APPROVED',
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * وضع علامة مدفوع
     */
    public function markAsPaid(array $paymentData = []): bool
    {
        if ($this->status !== 'APPROVED') {
            return false;
        }

        $this->update([
            'status' => 'PAID',
            'paid_at' => now(),
            'payment_reference' => $paymentData['reference'] ?? null,
        ]);

        // إنشاء سجل دفع
        if (!empty($paymentData)) {
            $this->paymentDetails()->create($paymentData);
        }

        // إرسال إشعار للموظف
        $this->employee->notify(new \App\Notifications\PayslipPaid($this));

        return true;
    }

    /**
     * إنهاء كشف الراتب
     */
    public function finalize(): bool
    {
        if ($this->status !== 'PAID') {
            return false;
        }

        $this->update(['status' => 'FINALIZED']);

        // إرسال للمحاسبة
        $this->sendToAccounting();

        return true;
    }

    /**
     * إرسال للمحاسبة
     */
    protected function sendToAccounting(): void
    {
        $accountingService = app(\App\Domains\Accounting\Services\DynamicAccountingEngine::class);
        
        // إنشاء قيد محاسبي للراتب
        $entryData = [
            'description' => "راتب {$this->employee->full_name} - {$this->pay_period_start->format('Y-m')}",
            'entry_date' => $this->pay_date,
            'currency' => $this->currency,
            'source_type' => 'PAYSLIP',
            'source_id' => $this->id,
            'lines' => $this->generateAccountingLines(),
        ];

        $accountingService->createJournalEntry($entryData);
    }

    /**
     * توليد خطوط القيد المحاسبي
     */
    protected function generateAccountingLines(): array
    {
        $lines = [];

        // مصروف الرواتب (مدين)
        $lines[] = [
            'account_code' => '6110', // مصروف رواتب
            'debit_amount' => $this->gross_salary,
            'credit_amount' => 0,
            'description' => 'مصروف راتب',
        ];

        // الراتب الصافي (دائن)
        $lines[] = [
            'account_code' => '2110', // رواتب مستحقة الدفع
            'debit_amount' => 0,
            'credit_amount' => $this->net_salary,
            'description' => 'راتب صافي مستحق',
        ];

        // الضرائب المستقطعة (دائن)
        if ($this->income_tax > 0) {
            $lines[] = [
                'account_code' => '2120', // ضرائب مستحقة الدفع
                'debit_amount' => 0,
                'credit_amount' => $this->income_tax,
                'description' => 'ضريبة دخل مستقطعة',
            ];
        }

        // الضمان الاجتماعي (دائن)
        if ($this->social_security_employee > 0) {
            $lines[] = [
                'account_code' => '2130', // ضمان اجتماعي مستحق
                'debit_amount' => 0,
                'credit_amount' => $this->social_security_employee,
                'description' => 'ضمان اجتماعي موظف',
            ];
        }

        return $lines;
    }

    /**
     * توليد PDF
     */
    public function generatePDF(): string
    {
        $pdfService = app(\App\Domains\HR\Services\PayslipPDFService::class);
        $pdfPath = $pdfService->generate($this);
        
        $this->update(['pdf_path' => $pdfPath]);
        
        return $pdfPath;
    }

    /**
     * توليد QR Code
     */
    public function generateQRCode(): string
    {
        $qrData = [
            'employee_id' => $this->employee->employee_number,
            'employee_name' => $this->employee->full_name,
            'pay_period' => $this->pay_period_start->format('Y-m'),
            'net_salary' => $this->net_salary,
            'pay_date' => $this->pay_date->format('Y-m-d'),
            'payslip_id' => $this->id,
            'verification_hash' => $this->generateVerificationHash(),
        ];

        $qrCode = base64_encode(json_encode($qrData));
        $this->update(['qr_code' => $qrCode]);
        
        return $qrCode;
    }

    /**
     * توليد hash للتحقق
     */
    protected function generateVerificationHash(): string
    {
        $data = $this->employee_id . $this->pay_period_start . $this->net_salary . $this->pay_date;
        return hash('sha256', $data . config('app.key'));
    }

    /**
     * إرسال بالبريد الإلكتروني
     */
    public function sendByEmail(): bool
    {
        if (!$this->pdf_path) {
            $this->generatePDF();
        }

        if (!$this->qr_code) {
            $this->generateQRCode();
        }

        try {
            $this->employee->notify(new \App\Notifications\PayslipGenerated($this));
            
            $this->update(['email_sent_at' => now()]);
            
            return true;
        } catch (\Exception $e) {
            \Log::error('فشل في إرسال كشف الراتب بالبريد', [
                'payslip_id' => $this->id,
                'employee_id' => $this->employee_id,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * تصدير لملف البنك
     */
    public function exportToBankFile(): array
    {
        return [
            'employee_bank_account' => $this->employee->iban ?? $this->employee->bank_account_number,
            'employee_name' => $this->employee->full_name,
            'amount' => $this->net_salary,
            'currency' => $this->currency,
            'reference' => "SAL-{$this->employee->employee_number}-{$this->pay_period_start->format('Ym')}",
            'description' => "راتب {$this->pay_period_start->format('Y/m')}",
        ];
    }

    /**
     * حساب الضرائب السنوية المتراكمة
     */
    public function calculateYearToDateTax(): float
    {
        return static::where('employee_id', $this->employee_id)
            ->where('tax_year', $this->tax_year)
            ->where('status', 'FINALIZED')
            ->sum('income_tax');
    }

    /**
     * حساب الراتب السنوي المتراكم
     */
    public function calculateYearToDateGross(): float
    {
        return static::where('employee_id', $this->employee_id)
            ->where('tax_year', $this->tax_year)
            ->where('status', 'FINALIZED')
            ->sum('gross_salary');
    }

    /**
     * الحصول على ملخص كشف الراتب
     */
    public function getSummary(): array
    {
        return [
            'employee' => [
                'name' => $this->employee->full_name,
                'employee_number' => $this->employee->employee_number,
                'position' => $this->employee->position->title,
                'department' => $this->employee->department->name,
            ],
            'period' => [
                'start_date' => $this->pay_period_start,
                'end_date' => $this->pay_period_end,
                'pay_date' => $this->pay_date,
                'working_days' => $this->working_days,
                'attended_days' => $this->attended_days,
            ],
            'earnings' => [
                'basic_salary' => $this->basic_salary,
                'allowances' => $this->housing_allowance + $this->transport_allowance + $this->meal_allowance,
                'bonuses' => $this->performance_bonus + $this->attendance_bonus,
                'overtime' => $this->overtime_bonus,
                'gross_total' => $this->gross_salary,
            ],
            'deductions' => [
                'income_tax' => $this->income_tax,
                'social_security' => $this->social_security_employee,
                'medical_insurance' => $this->medical_insurance,
                'loans' => $this->loan_deductions,
                'other' => $this->other_deductions,
                'total_deductions' => $this->total_deductions,
            ],
            'net_pay' => $this->net_salary,
            'year_to_date' => [
                'gross' => $this->calculateYearToDateGross(),
                'tax' => $this->calculateYearToDateTax(),
            ],
        ];
    }

    /**
     * نطاق للفترة
     */
    public function scopeForPeriod($query, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
    {
        return $query->whereBetween('pay_period_start', [$startDate, $endDate]);
    }

    /**
     * نطاق للموظف
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق للسنة الضريبية
     */
    public function scopeForTaxYear($query, int $year)
    {
        return $query->where('tax_year', $year);
    }

    /**
     * نطاق للكشوف المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->whereIn('status', ['APPROVED', 'PAID', 'FINALIZED']);
    }

    /**
     * نطاق للكشوف المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->whereIn('status', ['PAID', 'FINALIZED']);
    }

    /**
     * نطاق للكشوف المرسلة بالبريد
     */
    public function scopeEmailSent($query)
    {
        return $query->whereNotNull('email_sent_at');
    }

    /**
     * نطاق حسب طريقة الدفع
     */
    public function scopeByPaymentMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }
}
