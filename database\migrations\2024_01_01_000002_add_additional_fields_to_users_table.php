<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Personal Information
            $table->string('username')->unique()->nullable()->after('name');
            
            // Account Type
            $table->enum('account_type', [
                'freelancer', 
                'sme', 
                'enterprise', 
                'accounting_office', 
                'mcp_partner'
            ])->nullable()->after('email_verified_at');
            
            // Location Information
            $table->string('country', 2)->nullable()->after('account_type');
            $table->string('city')->nullable()->after('country');
            $table->string('currency')->nullable()->after('city');
            
            // Company Information
            $table->string('company_name')->nullable()->after('currency');
            $table->string('commercial_register')->nullable()->after('company_name');
            $table->string('tax_id')->nullable()->after('commercial_register');
            $table->string('website')->nullable()->after('tax_id');
            $table->string('employees_count')->nullable()->after('website');
            $table->string('industry')->nullable()->after('employees_count');
            
            // Additional fields
            $table->boolean('is_admin')->default(false)->after('industry');
            $table->string('subscription_plan')->default('basic')->after('is_admin');
            $table->timestamp('subscription_expires_at')->nullable()->after('subscription_plan');
            $table->json('preferences')->nullable()->after('subscription_expires_at');
            $table->timestamp('last_login_at')->nullable()->after('preferences');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'username',
                'account_type',
                'country',
                'city',
                'currency',
                'company_name',
                'commercial_register',
                'tax_id',
                'website',
                'employees_count',
                'industry',
                'is_admin',
                'subscription_plan',
                'subscription_expires_at',
                'preferences',
                'last_login_at',
                'last_login_ip',
            ]);
        });
    }
};
