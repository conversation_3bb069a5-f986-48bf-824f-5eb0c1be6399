<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث بدء المزامنة
 * يتم إطلاقه عند بدء عملية المزامنة
 */
class SyncStarted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public ECommerceSyncLog $syncLog;
    public array $syncOptions;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(ECommerceIntegration $integration, ECommerceSyncLog $syncLog, array $syncOptions = [])
    {
        $this->integration = $integration;
        $this->syncLog = $syncLog;
        $this->syncOptions = $syncOptions;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'sync_log_id' => $this->syncLog->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'sync_type' => $this->syncLog->sync_type,
            'entity_type' => $this->syncLog->entity_type,
            'sync_direction' => $this->syncLog->sync_direction,
            'sync_mode' => $this->syncLog->sync_mode,
            'started_at' => $this->syncLog->started_at,
            'sync_options' => $this->syncOptions,
        ];
    }
}
