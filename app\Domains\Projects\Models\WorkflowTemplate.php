<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قالب سير العمل - Workflow Template
 */
class WorkflowTemplate extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'template_id',
        'name',
        'description',
        'type',
        'steps',
        'conditions',
        'is_parallel',
    ];

    protected $casts = [
        'steps' => 'array',
        'conditions' => 'array',
        'is_parallel' => 'boolean',
    ];

    public function template(): BelongsTo
    {
        return $this->belongsTo(ProjectTemplate::class, 'template_id');
    }
}
