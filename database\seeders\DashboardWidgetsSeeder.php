<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DashboardWidget;

class DashboardWidgetsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $widgets = [
            // ودجات المدير العام
            [
                'name' => 'نظرة عامة على مؤشرات الأداء',
                'type' => 'kpi_overview',
                'component' => 'KPIOverviewWidget',
                'description' => 'عرض شامل لمؤشرات الأداء الرئيسية للشركة',
                'category' => 'analytics',
                'required_permissions' => ['general_manager'],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 6, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'kpis' => [
                        'type' => 'array',
                        'items' => ['revenue', 'profit', 'projects', 'satisfaction'],
                        'default' => ['revenue', 'profit', 'projects', 'satisfaction']
                    ],
                    'period' => [
                        'type' => 'select',
                        'options' => ['daily', 'weekly', 'monthly', 'quarterly'],
                        'default' => 'monthly'
                    ]
                ],
                'sort_order' => 1
            ],
            [
                'name' => 'مخطط الإيرادات',
                'type' => 'revenue_chart',
                'component' => 'RevenueChartWidget',
                'description' => 'تحليل اتجاهات الإيرادات والتنبؤات',
                'category' => 'analytics',
                'required_permissions' => ['general_manager', 'accountant'],
                'default_size' => ['w' => 8, 'h' => 6],
                'min_size' => ['w' => 6, 'h' => 4],
                'max_size' => ['w' => 12, 'h' => 8],
                'configuration_schema' => [
                    'chart_type' => [
                        'type' => 'select',
                        'options' => ['line', 'bar', 'area'],
                        'default' => 'line'
                    ],
                    'period' => [
                        'type' => 'select',
                        'options' => ['7d', '30d', '90d', '1y'],
                        'default' => '30d'
                    ],
                    'show_forecast' => [
                        'type' => 'boolean',
                        'default' => true
                    ]
                ],
                'sort_order' => 2
            ],
            [
                'name' => 'حالة المشاريع',
                'type' => 'project_status',
                'component' => 'ProjectStatusWidget',
                'description' => 'نظرة عامة على حالة جميع المشاريع',
                'category' => 'projects',
                'required_permissions' => ['general_manager', 'project_manager'],
                'default_size' => ['w' => 4, 'h' => 6],
                'min_size' => ['w' => 3, 'h' => 4],
                'max_size' => ['w' => 6, 'h' => 8],
                'configuration_schema' => [
                    'view_type' => [
                        'type' => 'select',
                        'options' => ['list', 'grid', 'chart'],
                        'default' => 'chart'
                    ],
                    'status_filter' => [
                        'type' => 'array',
                        'items' => ['active', 'completed', 'on_hold', 'cancelled'],
                        'default' => ['active', 'completed']
                    ]
                ],
                'sort_order' => 3
            ],

            // ودجات المحاسب
            [
                'name' => 'النظرة المالية العامة',
                'type' => 'financial_overview',
                'component' => 'FinancialOverviewWidget',
                'description' => 'ملخص شامل للوضع المالي',
                'category' => 'financial',
                'required_permissions' => ['general_manager', 'accountant'],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 8, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'metrics' => [
                        'type' => 'array',
                        'items' => ['cash_flow', 'receivables', 'payables', 'profit_loss'],
                        'default' => ['cash_flow', 'receivables', 'payables']
                    ]
                ],
                'sort_order' => 4
            ],
            [
                'name' => 'الحسابات المدينة',
                'type' => 'accounts_receivable',
                'component' => 'AccountsReceivableWidget',
                'description' => 'متابعة الفواتير والمبالغ المستحقة',
                'category' => 'financial',
                'required_permissions' => ['general_manager', 'accountant'],
                'default_size' => ['w' => 6, 'h' => 6],
                'min_size' => ['w' => 4, 'h' => 4],
                'max_size' => ['w' => 8, 'h' => 8],
                'configuration_schema' => [
                    'aging_periods' => [
                        'type' => 'array',
                        'items' => ['current', '30_days', '60_days', '90_days', 'over_90'],
                        'default' => ['current', '30_days', '60_days', '90_days']
                    ]
                ],
                'sort_order' => 5
            ],

            // ودجات مدير المشروع
            [
                'name' => 'نظرة عامة على المشاريع',
                'type' => 'project_overview',
                'component' => 'ProjectOverviewWidget',
                'description' => 'ملخص شامل لجميع المشاريع المسندة',
                'category' => 'projects',
                'required_permissions' => ['general_manager', 'project_manager'],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 8, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'view_mode' => [
                        'type' => 'select',
                        'options' => ['summary', 'detailed', 'timeline'],
                        'default' => 'summary'
                    ]
                ],
                'sort_order' => 6
            ],
            [
                'name' => 'مخطط جانت',
                'type' => 'gantt_chart',
                'component' => 'GanttChartWidget',
                'description' => 'عرض الجدول الزمني للمشاريع والمهام',
                'category' => 'projects',
                'required_permissions' => ['general_manager', 'project_manager'],
                'default_size' => ['w' => 12, 'h' => 8],
                'min_size' => ['w' => 8, 'h' => 6],
                'max_size' => ['w' => 12, 'h' => 10],
                'configuration_schema' => [
                    'time_scale' => [
                        'type' => 'select',
                        'options' => ['days', 'weeks', 'months'],
                        'default' => 'weeks'
                    ],
                    'show_dependencies' => [
                        'type' => 'boolean',
                        'default' => true
                    ]
                ],
                'sort_order' => 7
            ],

            // ودجات مشرف الدعم
            [
                'name' => 'نظرة عامة على التذاكر',
                'type' => 'ticket_overview',
                'component' => 'TicketOverviewWidget',
                'description' => 'ملخص حالة تذاكر الدعم الفني',
                'category' => 'support',
                'required_permissions' => ['general_manager', 'support_supervisor'],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 8, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'priority_filter' => [
                        'type' => 'array',
                        'items' => ['low', 'medium', 'high', 'critical'],
                        'default' => ['medium', 'high', 'critical']
                    ]
                ],
                'sort_order' => 8
            ],
            [
                'name' => 'وقت الاستجابة',
                'type' => 'response_time',
                'component' => 'ResponseTimeWidget',
                'description' => 'متابعة أوقات الاستجابة لتذاكر الدعم',
                'category' => 'support',
                'required_permissions' => ['general_manager', 'support_supervisor'],
                'default_size' => ['w' => 6, 'h' => 6],
                'min_size' => ['w' => 4, 'h' => 4],
                'max_size' => ['w' => 8, 'h' => 8],
                'configuration_schema' => [
                    'chart_type' => [
                        'type' => 'select',
                        'options' => ['line', 'bar', 'gauge'],
                        'default' => 'gauge'
                    ]
                ],
                'sort_order' => 9
            ],

            // ودجات الموارد البشرية
            [
                'name' => 'نظرة عامة على الموظفين',
                'type' => 'employee_overview',
                'component' => 'EmployeeOverviewWidget',
                'description' => 'ملخص شامل لبيانات الموظفين',
                'category' => 'hr',
                'required_permissions' => ['general_manager', 'hr_manager'],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 8, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'metrics' => [
                        'type' => 'array',
                        'items' => ['total_employees', 'attendance', 'performance', 'satisfaction'],
                        'default' => ['total_employees', 'attendance', 'performance']
                    ]
                ],
                'sort_order' => 10
            ],

            // ودجات عامة
            [
                'name' => 'ودجت الترحيب',
                'type' => 'welcome_widget',
                'component' => 'WelcomeWidget',
                'description' => 'رسالة ترحيب ونصائح سريعة',
                'category' => 'utilities',
                'required_permissions' => [],
                'default_size' => ['w' => 12, 'h' => 4],
                'min_size' => ['w' => 6, 'h' => 3],
                'max_size' => ['w' => 12, 'h' => 6],
                'configuration_schema' => [
                    'show_tips' => [
                        'type' => 'boolean',
                        'default' => true
                    ],
                    'show_weather' => [
                        'type' => 'boolean',
                        'default' => false
                    ]
                ],
                'sort_order' => 11
            ],
            [
                'name' => 'مهامي',
                'type' => 'my_tasks',
                'component' => 'MyTasksWidget',
                'description' => 'قائمة المهام المسندة للمستخدم',
                'category' => 'productivity',
                'required_permissions' => [],
                'default_size' => ['w' => 6, 'h' => 6],
                'min_size' => ['w' => 4, 'h' => 4],
                'max_size' => ['w' => 8, 'h' => 8],
                'configuration_schema' => [
                    'show_completed' => [
                        'type' => 'boolean',
                        'default' => false
                    ],
                    'sort_by' => [
                        'type' => 'select',
                        'options' => ['due_date', 'priority', 'created_at'],
                        'default' => 'due_date'
                    ]
                ],
                'sort_order' => 12
            ],
            [
                'name' => 'النشاط الحديث',
                'type' => 'recent_activity',
                'component' => 'RecentActivityWidget',
                'description' => 'آخر الأنشطة والتحديثات في النظام',
                'category' => 'communication',
                'required_permissions' => [],
                'default_size' => ['w' => 6, 'h' => 6],
                'min_size' => ['w' => 4, 'h' => 4],
                'max_size' => ['w' => 8, 'h' => 8],
                'configuration_schema' => [
                    'activity_types' => [
                        'type' => 'array',
                        'items' => ['projects', 'invoices', 'tickets', 'employees'],
                        'default' => ['projects', 'invoices', 'tickets']
                    ],
                    'limit' => [
                        'type' => 'number',
                        'min' => 5,
                        'max' => 20,
                        'default' => 10
                    ]
                ],
                'sort_order' => 13
            ],
            [
                'name' => 'التقويم',
                'type' => 'calendar',
                'component' => 'CalendarWidget',
                'description' => 'عرض الأحداث والمواعيد القادمة',
                'category' => 'productivity',
                'required_permissions' => [],
                'default_size' => ['w' => 6, 'h' => 6],
                'min_size' => ['w' => 4, 'h' => 4],
                'max_size' => ['w' => 8, 'h' => 8],
                'configuration_schema' => [
                    'view_mode' => [
                        'type' => 'select',
                        'options' => ['month', 'week', 'agenda'],
                        'default' => 'month'
                    ],
                    'show_weekends' => [
                        'type' => 'boolean',
                        'default' => true
                    ]
                ],
                'sort_order' => 14
            ],
            [
                'name' => 'تتبع الوقت',
                'type' => 'time_tracking',
                'component' => 'TimeTrackingWidget',
                'description' => 'متابعة الوقت المنفق على المهام والمشاريع',
                'category' => 'productivity',
                'required_permissions' => [],
                'default_size' => ['w' => 4, 'h' => 4],
                'min_size' => ['w' => 3, 'h' => 3],
                'max_size' => ['w' => 6, 'h' => 6],
                'configuration_schema' => [
                    'auto_start' => [
                        'type' => 'boolean',
                        'default' => false
                    ],
                    'show_daily_total' => [
                        'type' => 'boolean',
                        'default' => true
                    ]
                ],
                'sort_order' => 15
            ]
        ];

        foreach ($widgets as $widget) {
            DashboardWidget::updateOrCreate(
                ['type' => $widget['type']],
                $widget
            );
        }

        $this->command->info('Dashboard widgets seeded successfully!');
    }
}
