<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج فئة التدريب
 */
class TrainingCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'color',
        'icon',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * البرامج التدريبية في هذه الفئة
     */
    public function trainingPrograms(): HasMany
    {
        return $this->hasMany(TrainingProgram::class, 'category_id');
    }

    /**
     * الحصول على عدد البرامج النشطة
     */
    public function getActiveProgramsCountAttribute(): int
    {
        return $this->trainingPrograms()->where('status', 'ACTIVE')->count();
    }

    /**
     * فحص إذا كانت الفئة نشطة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Scope للفئات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للترتيب
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
