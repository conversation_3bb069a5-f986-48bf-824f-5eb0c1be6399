<?php

namespace App\Domains\ECommerce\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommercePlatform;
use App\Domains\ECommerce\Services\ECommerceIntegrationService;
use App\Domains\ECommerce\Services\ECommerceSyncService;
use App\Domains\ECommerce\Factories\ECommercePlatformDriverFactory;
use App\Domains\ECommerce\Requests\CreateIntegrationRequest;
use App\Domains\ECommerce\Requests\UpdateIntegrationRequest;
use App\Domains\ECommerce\Resources\ECommerceIntegrationResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * تحكم في تكاملات التجارة الإلكترونية
 */
class ECommerceIntegrationController extends Controller
{
    protected ECommerceIntegrationService $integrationService;
    protected ECommerceSyncService $syncService;

    public function __construct(
        ECommerceIntegrationService $integrationService,
        ECommerceSyncService $syncService
    ) {
        $this->integrationService = $integrationService;
        $this->syncService = $syncService;
    }

    /**
     * عرض قائمة التكاملات
     */
    public function index(Request $request): JsonResponse
    {
        $integrations = ECommerceIntegration::with(['platform', 'store'])
            ->when($request->platform_id, function ($query, $platformId) {
                return $query->where('platform_id', $platformId);
            })
            ->when($request->status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => ECommerceIntegrationResource::collection($integrations),
            'meta' => [
                'total' => $integrations->total(),
                'per_page' => $integrations->perPage(),
                'current_page' => $integrations->currentPage(),
                'last_page' => $integrations->lastPage(),
            ],
        ]);
    }

    /**
     * عرض تكامل محدد
     */
    public function show(ECommerceIntegration $integration): JsonResponse
    {
        $integration->load(['platform', 'store', 'syncLogs' => function ($query) {
            $query->latest()->take(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => new ECommerceIntegrationResource($integration),
        ]);
    }

    /**
     * إنشاء تكامل جديد
     */
    public function store(CreateIntegrationRequest $request): JsonResponse
    {
        try {
            $integration = $this->integrationService->createIntegration($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء التكامل بنجاح',
                'data' => new ECommerceIntegrationResource($integration),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء التكامل',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * تحديث تكامل موجود
     */
    public function update(UpdateIntegrationRequest $request, ECommerceIntegration $integration): JsonResponse
    {
        try {
            $integration = $this->integrationService->updateIntegration($integration, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث التكامل بنجاح',
                'data' => new ECommerceIntegrationResource($integration),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث التكامل',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * حذف تكامل
     */
    public function destroy(ECommerceIntegration $integration): JsonResponse
    {
        try {
            $this->integrationService->deleteIntegration($integration);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف التكامل بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف التكامل',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * اختبار الاتصال مع المنصة
     */
    public function testConnection(ECommerceIntegration $integration): JsonResponse
    {
        try {
            $result = ECommercePlatformDriverFactory::testConnection($integration);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم الاتصال بنجاح' : 'فشل في الاتصال',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في اختبار الاتصال',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * بدء المزامنة
     */
    public function sync(Request $request, ECommerceIntegration $integration): JsonResponse
    {
        $request->validate([
            'sync_type' => 'required|in:full,incremental,products,orders,customers',
            'options' => 'array',
        ]);

        try {
            $syncJob = $this->syncService->startSync($integration, $request->sync_type, $request->options ?? []);

            return response()->json([
                'success' => true,
                'message' => 'تم بدء المزامنة بنجاح',
                'data' => [
                    'sync_job_id' => $syncJob->id,
                    'status' => $syncJob->status,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في بدء المزامنة',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * إيقاف المزامنة
     */
    public function stopSync(ECommerceIntegration $integration): JsonResponse
    {
        try {
            $this->syncService->stopSync($integration);

            return response()->json([
                'success' => true,
                'message' => 'تم إيقاف المزامنة بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إيقاف المزامنة',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * الحصول على حالة المزامنة
     */
    public function syncStatus(ECommerceIntegration $integration): JsonResponse
    {
        $status = $this->syncService->getSyncStatus($integration);

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }

    /**
     * الحصول على سجلات المزامنة
     */
    public function syncLogs(Request $request, ECommerceIntegration $integration): JsonResponse
    {
        $logs = $integration->syncLogs()
            ->when($request->status, function ($query, $status) {
                return $query->where('is_successful', $status === 'success');
            })
            ->when($request->entity_type, function ($query, $entityType) {
                return $query->where('entity_type', $entityType);
            })
            ->latest()
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $logs,
        ]);
    }

    /**
     * تفعيل التكامل
     */
    public function activate(ECommerceIntegration $integration): JsonResponse
    {
        try {
            $integration->update(['status' => 'active']);

            return response()->json([
                'success' => true,
                'message' => 'تم تفعيل التكامل بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في تفعيل التكامل',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * إلغاء تفعيل التكامل
     */
    public function deactivate(ECommerceIntegration $integration): JsonResponse
    {
        try {
            $integration->update(['status' => 'inactive']);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء تفعيل التكامل بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إلغاء تفعيل التكامل',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * الحصول على إحصائيات التكامل
     */
    public function statistics(ECommerceIntegration $integration): JsonResponse
    {
        $stats = $this->integrationService->getIntegrationStatistics($integration);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * الحصول على المنصات المدعومة
     */
    public function supportedPlatforms(): JsonResponse
    {
        $platforms = ECommercePlatform::where('is_active', true)->get();
        $driversInfo = ECommercePlatformDriverFactory::getAllDriversInfo();

        return response()->json([
            'success' => true,
            'data' => [
                'platforms' => $platforms,
                'drivers_info' => $driversInfo,
                'stats' => ECommercePlatformDriverFactory::getDriverStats(),
            ],
        ]);
    }

    /**
     * التحقق من صحة إعدادات التكامل
     */
    public function validateConfiguration(Request $request): JsonResponse
    {
        $request->validate([
            'platform_slug' => 'required|string',
            'configuration' => 'required|array',
        ]);

        try {
            $validation = ECommercePlatformDriverFactory::validateConfiguration(
                $request->platform_slug,
                $request->configuration
            );

            return response()->json([
                'success' => $validation['valid'],
                'data' => $validation,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في التحقق من الإعدادات',
                'error' => $e->getMessage(),
            ], 422);
        }
    }
}
