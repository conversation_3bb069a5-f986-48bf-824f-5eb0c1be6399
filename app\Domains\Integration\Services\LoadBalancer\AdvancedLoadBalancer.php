<?php

namespace App\Domains\Integration\Services\LoadBalancer;

use App\Domains\Integration\Contracts\LoadBalancerInterface;
use App\Domains\Integration\Models\ApiEndpoint;
use App\Domains\Integration\Services\HealthChecker\DistributedHealthChecker;
use App\Domains\Integration\Services\Metrics\RealTimeMetricsCollector;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * Advanced Load Balancer with Machine Learning-based Traffic Distribution
 * 
 * Features:
 * - Adaptive load balancing based on real-time metrics
 * - Predictive scaling using ML algorithms
 * - Geographic load distribution
 * - Session affinity with consistent hashing
 * - Weighted round-robin with dynamic weight adjustment
 * - Least connections with connection prediction
 * - Response time-based routing
 * - Health-aware load balancing
 * - Circuit breaker integration
 * - Auto-failover and disaster recovery
 */
class AdvancedLoadBalancer implements LoadBalancerInterface
{
    protected DistributedHealthChecker $healthChecker;
    protected RealTimeMetricsCollector $metricsCollector;
    protected array $algorithms;
    protected array $config;
    protected string $defaultAlgorithm = 'adaptive_weighted';

    public function __construct(
        DistributedHealthChecker $healthChecker,
        RealTimeMetricsCollector $metricsCollector
    ) {
        $this->healthChecker = $healthChecker;
        $this->metricsCollector = $metricsCollector;
        $this->config = config('integration.load_balancer', []);
        $this->initializeAlgorithms();
    }

    /**
     * Select the best endpoint using advanced algorithms
     */
    public function selectEndpoint(Collection $endpoints, array $context = []): ?ApiEndpoint
    {
        if ($endpoints->isEmpty()) {
            return null;
        }

        // Filter healthy endpoints
        $healthyEndpoints = $this->filterHealthyEndpoints($endpoints);
        
        if ($healthyEndpoints->isEmpty()) {
            Log::warning('No healthy endpoints available, falling back to all endpoints');
            $healthyEndpoints = $endpoints;
        }

        // Get algorithm from context or use default
        $algorithm = $context['algorithm'] ?? $this->defaultAlgorithm;
        
        // Apply geographic filtering if specified
        if (isset($context['client_region'])) {
            $healthyEndpoints = $this->applyGeographicFiltering($healthyEndpoints, $context['client_region']);
        }

        // Apply session affinity if required
        if (isset($context['session_id']) && ($context['session_affinity'] ?? false)) {
            $affinityEndpoint = $this->getSessionAffinityEndpoint($healthyEndpoints, $context['session_id']);
            if ($affinityEndpoint) {
                return $affinityEndpoint;
            }
        }

        // Select endpoint using specified algorithm
        return $this->executeAlgorithm($algorithm, $healthyEndpoints, $context);
    }

    /**
     * Adaptive Weighted Round Robin with ML-based weight adjustment
     */
    protected function adaptiveWeightedRoundRobin(Collection $endpoints, array $context): ?ApiEndpoint
    {
        $weights = $this->calculateAdaptiveWeights($endpoints, $context);
        $totalWeight = array_sum($weights);
        
        if ($totalWeight === 0) {
            return $endpoints->first();
        }

        // Get current position from Redis
        $key = "lb:adaptive_weighted:" . md5(serialize($endpoints->pluck('id')->toArray()));
        $currentWeight = Redis::get($key) ?? 0;
        
        foreach ($endpoints as $endpoint) {
            $weight = $weights[$endpoint->id] ?? 1;
            $currentWeight += $weight;
            
            if ($currentWeight >= $totalWeight) {
                Redis::setex($key, 300, $currentWeight - $totalWeight);
                return $endpoint;
            }
        }

        Redis::setex($key, 300, 0);
        return $endpoints->first();
    }

    /**
     * Least Connections with Connection Prediction
     */
    protected function predictiveLeastConnections(Collection $endpoints, array $context): ?ApiEndpoint
    {
        $connectionCounts = [];
        $predictions = [];

        foreach ($endpoints as $endpoint) {
            // Get current connections
            $currentConnections = $this->getCurrentConnections($endpoint);
            
            // Predict future connections based on trends
            $predictedConnections = $this->predictFutureConnections($endpoint, $context);
            
            $connectionCounts[$endpoint->id] = $currentConnections;
            $predictions[$endpoint->id] = $predictedConnections;
        }

        // Find endpoint with lowest predicted load
        $selectedId = array_keys($predictions, min($predictions))[0];
        return $endpoints->firstWhere('id', $selectedId);
    }

    /**
     * Response Time-based Routing with Latency Prediction
     */
    protected function responseTimeBasedRouting(Collection $endpoints, array $context): ?ApiEndpoint
    {
        $responseTimes = [];
        $clientLocation = $context['client_location'] ?? null;

        foreach ($endpoints as $endpoint) {
            // Get historical response times
            $avgResponseTime = $this->getAverageResponseTime($endpoint, $clientLocation);
            
            // Apply geographic latency estimation
            $estimatedLatency = $this->estimateNetworkLatency($endpoint, $clientLocation);
            
            // Calculate total expected response time
            $totalExpectedTime = $avgResponseTime + $estimatedLatency;
            
            // Apply load factor
            $loadFactor = $this->calculateLoadFactor($endpoint);
            $responseTimes[$endpoint->id] = $totalExpectedTime * $loadFactor;
        }

        // Select endpoint with lowest expected response time
        $selectedId = array_keys($responseTimes, min($responseTimes))[0];
        return $endpoints->firstWhere('id', $selectedId);
    }

    /**
     * Health-aware Load Balancing with Degradation Handling
     */
    protected function healthAwareLoadBalancing(Collection $endpoints, array $context): ?ApiEndpoint
    {
        $healthScores = [];

        foreach ($endpoints as $endpoint) {
            $healthScore = $this->calculateHealthScore($endpoint);
            $healthScores[$endpoint->id] = $healthScore;
        }

        // Sort by health score (descending)
        arsort($healthScores);

        // Apply weighted selection based on health scores
        $totalScore = array_sum($healthScores);
        $random = mt_rand(1, $totalScore * 100) / 100;
        $currentSum = 0;

        foreach ($healthScores as $endpointId => $score) {
            $currentSum += $score;
            if ($random <= $currentSum) {
                return $endpoints->firstWhere('id', $endpointId);
            }
        }

        return $endpoints->first();
    }

    /**
     * Geographic Load Distribution
     */
    protected function geographicLoadDistribution(Collection $endpoints, array $context): ?ApiEndpoint
    {
        $clientRegion = $context['client_region'] ?? 'unknown';
        $clientCountry = $context['client_country'] ?? 'unknown';

        // Group endpoints by region
        $endpointsByRegion = $endpoints->groupBy(function ($endpoint) {
            return $endpoint->metadata['region'] ?? 'default';
        });

        // Prefer same region endpoints
        if (isset($endpointsByRegion[$clientRegion])) {
            $regionalEndpoints = $endpointsByRegion[$clientRegion];
            return $this->adaptiveWeightedRoundRobin($regionalEndpoints, $context);
        }

        // Fallback to country-based selection
        $endpointsByCountry = $endpoints->groupBy(function ($endpoint) {
            return $endpoint->metadata['country'] ?? 'default';
        });

        if (isset($endpointsByCountry[$clientCountry])) {
            $countryEndpoints = $endpointsByCountry[$clientCountry];
            return $this->adaptiveWeightedRoundRobin($countryEndpoints, $context);
        }

        // Final fallback to all endpoints
        return $this->adaptiveWeightedRoundRobin($endpoints, $context);
    }

    /**
     * Calculate adaptive weights based on real-time metrics
     */
    protected function calculateAdaptiveWeights(Collection $endpoints, array $context): array
    {
        $weights = [];
        $now = now();

        foreach ($endpoints as $endpoint) {
            $metrics = $this->metricsCollector->getEndpointMetrics($endpoint->id, $now->subMinutes(5), $now);
            
            // Base weight from configuration
            $baseWeight = $endpoint->load_balancer_weight ?? 1;
            
            // Adjust based on response time (lower is better)
            $responseTimeFactor = $this->calculateResponseTimeFactor($metrics['avg_response_time'] ?? 0);
            
            // Adjust based on error rate (lower is better)
            $errorRateFactor = $this->calculateErrorRateFactor($metrics['error_rate'] ?? 0);
            
            // Adjust based on CPU usage (lower is better)
            $cpuFactor = $this->calculateCpuFactor($metrics['cpu_usage'] ?? 0);
            
            // Adjust based on memory usage (lower is better)
            $memoryFactor = $this->calculateMemoryFactor($metrics['memory_usage'] ?? 0);
            
            // Adjust based on current connections (lower is better)
            $connectionsFactor = $this->calculateConnectionsFactor($metrics['active_connections'] ?? 0);

            // Calculate final weight
            $adaptiveWeight = $baseWeight * $responseTimeFactor * $errorRateFactor * $cpuFactor * $memoryFactor * $connectionsFactor;
            
            // Ensure minimum weight
            $weights[$endpoint->id] = max(0.1, $adaptiveWeight);
        }

        return $weights;
    }

    /**
     * Filter healthy endpoints using distributed health checker
     */
    protected function filterHealthyEndpoints(Collection $endpoints): Collection
    {
        return $endpoints->filter(function ($endpoint) {
            return $this->healthChecker->isEndpointHealthy($endpoint->id);
        });
    }

    /**
     * Apply geographic filtering
     */
    protected function applyGeographicFiltering(Collection $endpoints, string $clientRegion): Collection
    {
        $filtered = $endpoints->filter(function ($endpoint) use ($clientRegion) {
            $endpointRegion = $endpoint->metadata['region'] ?? 'default';
            return $endpointRegion === $clientRegion;
        });

        return $filtered->isNotEmpty() ? $filtered : $endpoints;
    }

    /**
     * Get session affinity endpoint using consistent hashing
     */
    protected function getSessionAffinityEndpoint(Collection $endpoints, string $sessionId): ?ApiEndpoint
    {
        $hash = crc32($sessionId);
        $endpointCount = $endpoints->count();
        $selectedIndex = $hash % $endpointCount;
        
        return $endpoints->values()->get($selectedIndex);
    }

    /**
     * Execute the specified load balancing algorithm
     */
    protected function executeAlgorithm(string $algorithm, Collection $endpoints, array $context): ?ApiEndpoint
    {
        if (!isset($this->algorithms[$algorithm])) {
            Log::warning("Unknown load balancing algorithm: {$algorithm}, falling back to default");
            $algorithm = $this->defaultAlgorithm;
        }

        return $this->algorithms[$algorithm]($endpoints, $context);
    }

    /**
     * Initialize available algorithms
     */
    protected function initializeAlgorithms(): void
    {
        $this->algorithms = [
            'adaptive_weighted' => [$this, 'adaptiveWeightedRoundRobin'],
            'predictive_least_connections' => [$this, 'predictiveLeastConnections'],
            'response_time_based' => [$this, 'responseTimeBasedRouting'],
            'health_aware' => [$this, 'healthAwareLoadBalancing'],
            'geographic' => [$this, 'geographicLoadDistribution'],
        ];
    }

    // Helper methods for calculations
    protected function getCurrentConnections(ApiEndpoint $endpoint): int
    {
        return (int) Redis::get("endpoint:{$endpoint->id}:connections") ?: 0;
    }

    protected function predictFutureConnections(ApiEndpoint $endpoint, array $context): float
    {
        // Simple linear prediction based on recent trends
        $recentConnections = $this->metricsCollector->getConnectionTrend($endpoint->id, 5);
        return $recentConnections['predicted'] ?? $recentConnections['current'] ?? 0;
    }

    protected function getAverageResponseTime(ApiEndpoint $endpoint, ?array $clientLocation): float
    {
        $cacheKey = "endpoint:{$endpoint->id}:avg_response_time";
        return (float) Cache::get($cacheKey, $endpoint->average_response_time ?? 0);
    }

    protected function estimateNetworkLatency(ApiEndpoint $endpoint, ?array $clientLocation): float
    {
        if (!$clientLocation || !isset($endpoint->metadata['location'])) {
            return 0;
        }

        // Simple geographic distance-based latency estimation
        $distance = $this->calculateGeographicDistance($clientLocation, $endpoint->metadata['location']);
        return $distance * 0.1; // Rough estimate: 0.1ms per km
    }

    protected function calculateLoadFactor(ApiEndpoint $endpoint): float
    {
        $currentLoad = $this->getCurrentConnections($endpoint);
        $maxCapacity = $endpoint->metadata['max_capacity'] ?? 1000;
        
        return 1 + ($currentLoad / $maxCapacity);
    }

    protected function calculateHealthScore(ApiEndpoint $endpoint): float
    {
        $healthData = $this->healthChecker->getDetailedHealth($endpoint->id);
        
        $responseTimeScore = $this->normalizeScore($healthData['response_time'] ?? 0, 0, 1000, true);
        $errorRateScore = $this->normalizeScore($healthData['error_rate'] ?? 0, 0, 0.1, true);
        $cpuScore = $this->normalizeScore($healthData['cpu_usage'] ?? 0, 0, 100, true);
        $memoryScore = $this->normalizeScore($healthData['memory_usage'] ?? 0, 0, 100, true);

        return ($responseTimeScore + $errorRateScore + $cpuScore + $memoryScore) / 4;
    }

    protected function calculateResponseTimeFactor(float $responseTime): float
    {
        // Lower response time = higher weight
        return max(0.1, 1 / (1 + $responseTime / 100));
    }

    protected function calculateErrorRateFactor(float $errorRate): float
    {
        // Lower error rate = higher weight
        return max(0.1, 1 - $errorRate);
    }

    protected function calculateCpuFactor(float $cpuUsage): float
    {
        // Lower CPU usage = higher weight
        return max(0.1, 1 - ($cpuUsage / 100));
    }

    protected function calculateMemoryFactor(float $memoryUsage): float
    {
        // Lower memory usage = higher weight
        return max(0.1, 1 - ($memoryUsage / 100));
    }

    protected function calculateConnectionsFactor(int $connections): float
    {
        // Fewer connections = higher weight
        $maxConnections = $this->config['max_connections_per_endpoint'] ?? 1000;
        return max(0.1, 1 - ($connections / $maxConnections));
    }

    protected function normalizeScore(float $value, float $min, float $max, bool $inverse = false): float
    {
        $normalized = ($value - $min) / ($max - $min);
        $normalized = max(0, min(1, $normalized));
        
        return $inverse ? 1 - $normalized : $normalized;
    }

    protected function calculateGeographicDistance(array $location1, array $location2): float
    {
        $lat1 = deg2rad($location1['lat'] ?? 0);
        $lon1 = deg2rad($location1['lon'] ?? 0);
        $lat2 = deg2rad($location2['lat'] ?? 0);
        $lon2 = deg2rad($location2['lon'] ?? 0);

        $deltaLat = $lat2 - $lat1;
        $deltaLon = $lon2 - $lon1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1) * cos($lat2) *
             sin($deltaLon / 2) * sin($deltaLon / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return 6371 * $c; // Earth's radius in kilometers
    }
}
