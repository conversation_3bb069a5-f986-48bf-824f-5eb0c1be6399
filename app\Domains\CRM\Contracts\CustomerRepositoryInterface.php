<?php

namespace App\Domains\CRM\Contracts;

use App\Domains\CRM\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * واجهة مستودع العملاء
 * Customer Repository Interface
 */
interface CustomerRepositoryInterface
{
    /**
     * الحصول على جميع العملاء
     */
    public function all(): Collection;

    /**
     * البحث عن عميل بالمعرف
     */
    public function find(int $id): ?Customer;

    /**
     * إنشاء عميل جديد
     */
    public function create(array $data): Customer;

    /**
     * تحديث عميل
     */
    public function update(Customer $customer, array $data): bool;

    /**
     * حذف عميل
     */
    public function delete(Customer $customer): bool;

    /**
     * البحث في العملاء
     */
    public function search(string $query): Collection;

    /**
     * الحصول على العملاء مع الترقيم
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator;

    /**
     * الحصول على العملاء النشطين
     */
    public function getActive(): Collection;

    /**
     * الحصول على العملاء حسب المصدر
     */
    public function getBySource(int $sourceId): Collection;

    /**
     * الحصول على العملاء حسب الشريحة
     */
    public function getBySegment(int $segmentId): Collection;

    /**
     * الحصول على العملاء عالي القيمة
     */
    public function getHighValue(float $threshold = 50000): Collection;

    /**
     * الحصول على العملاء المعرضين للخطر
     */
    public function getAtRisk(): Collection;
}
