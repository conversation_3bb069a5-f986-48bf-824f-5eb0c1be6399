<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * نموذج التحويل البنكي
 * يمثل عمليات التحويل البنكي المحلية والدولية
 */
class BankTransfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transfer_id',
        'reference_number',
        'user_id',
        'transferable_type',
        'transferable_id',
        'type',
        'status',
        'amount',
        'currency',
        'exchange_rate',
        'amount_in_destination_currency',
        'fees',
        'total_amount',
        'sender_bank_details',
        'recipient_bank_details',
        'swift_code',
        'iban',
        'routing_number',
        'account_number',
        'recipient_name',
        'recipient_address',
        'purpose_code',
        'description',
        'country_from',
        'country_to',
        'processing_time_estimate',
        'scheduled_at',
        'processed_at',
        'completed_at',
        'failed_at',
        'failure_reason',
        'bank_reference',
        'correspondent_bank_details',
        'compliance_check_result',
        'aml_check_result',
        'sanctions_check_result',
        'metadata',
        'tracking_number',
        'intermediary_banks',
        'regulatory_reporting',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'amount_in_destination_currency' => 'decimal:2',
        'fees' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'sender_bank_details' => 'array',
        'recipient_bank_details' => 'array',
        'recipient_address' => 'array',
        'scheduled_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
        'correspondent_bank_details' => 'array',
        'compliance_check_result' => 'array',
        'aml_check_result' => 'array',
        'sanctions_check_result' => 'array',
        'metadata' => 'array',
        'intermediary_banks' => 'array',
        'regulatory_reporting' => 'array',
        'processing_time_estimate' => 'integer',
    ];

    /**
     * أنواع التحويل
     */
    const TYPES = [
        'domestic' => 'تحويل محلي',
        'international' => 'تحويل دولي',
        'swift' => 'تحويل SWIFT',
        'sepa' => 'تحويل SEPA',
        'ach' => 'تحويل ACH',
        'wire' => 'تحويل سلكي',
        'instant' => 'تحويل فوري',
        'same_day' => 'تحويل نفس اليوم',
        'next_day' => 'تحويل اليوم التالي',
        'correspondent' => 'تحويل عبر بنك مراسل',
    ];

    /**
     * حالات التحويل
     */
    const STATUSES = [
        'pending' => 'في الانتظار',
        'processing' => 'قيد المعالجة',
        'compliance_check' => 'فحص الامتثال',
        'aml_check' => 'فحص مكافحة غسل الأموال',
        'sanctions_check' => 'فحص العقوبات',
        'approved' => 'موافق عليه',
        'rejected' => 'مرفوض',
        'sent_to_bank' => 'مرسل للبنك',
        'in_transit' => 'في الطريق',
        'received_by_correspondent' => 'استلمه البنك المراسل',
        'credited_to_beneficiary' => 'تم إيداعه للمستفيد',
        'completed' => 'مكتمل',
        'failed' => 'فشل',
        'returned' => 'مرتد',
        'cancelled' => 'ملغي',
        'on_hold' => 'معلق',
        'requires_documents' => 'يتطلب مستندات',
    ];

    /**
     * رموز الغرض (Purpose Codes)
     */
    const PURPOSE_CODES = [
        'P0101' => 'تحويل شخصي',
        'P0102' => 'دعم عائلي',
        'P0201' => 'دفع فواتير',
        'P0301' => 'استثمار',
        'P0401' => 'تجارة',
        'P0501' => 'خدمات',
        'P0601' => 'تعليم',
        'P0701' => 'علاج طبي',
        'P0801' => 'سياحة وسفر',
        'P0901' => 'عقارات',
        'P1001' => 'أخرى',
    ];

    /**
     * الشبكات المصرفية المدعومة
     */
    const BANKING_NETWORKS = [
        // المغرب
        'MICE' => 'الشبكة المغربية للتحويلات',
        'SIMT' => 'نظام التحويلات الفورية المغربي',
        
        // السعودية
        'SARIE' => 'نظام التحويلات السريعة السعودي',
        'SADAD' => 'نظام سداد',
        
        // الإمارات
        'UAEFTS' => 'نظام الإمارات للتحويلات المالية',
        'IPP' => 'منصة الدفع الفوري',
        
        // مصر
        'MEEM' => 'نظام الدفع الفوري المصري',
        'InstaPay' => 'إنستاباي',
        
        // دولية
        'SWIFT' => 'شبكة SWIFT العالمية',
        'SEPA' => 'منطقة الدفع الأوروبية الموحدة',
        'ACH' => 'غرفة المقاصة الآلية',
        'Fedwire' => 'نظام الاحتياطي الفيدرالي',
        'CHAPS' => 'نظام المدفوعات البريطاني',
        'TARGET2' => 'نظام الدفع الأوروبي',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transfer) {
            if (empty($transfer->transfer_id)) {
                $transfer->transfer_id = static::generateTransferId();
            }
            
            if (empty($transfer->reference_number)) {
                $transfer->reference_number = static::generateReferenceNumber();
            }
            
            if (empty($transfer->tracking_number)) {
                $transfer->tracking_number = static::generateTrackingNumber();
            }
        });
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة المتعددة الأشكال مع الكيان القابل للتحويل
     */
    public function transferable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * توليد معرف التحويل
     */
    public static function generateTransferId(): string
    {
        return 'BT_' . strtoupper(Str::random(12)) . '_' . time();
    }

    /**
     * توليد رقم المرجع
     */
    public static function generateReferenceNumber(): string
    {
        return 'BTR_' . date('Ymd') . '_' . strtoupper(Str::random(8));
    }

    /**
     * توليد رقم التتبع
     */
    public static function generateTrackingNumber(): string
    {
        return 'TRK_' . strtoupper(Str::random(16));
    }

    /**
     * التحقق من إمكانية الإلغاء
     */
    public function isCancellable(): bool
    {
        return in_array($this->status, ['pending', 'processing', 'compliance_check', 'on_hold']);
    }

    /**
     * التحقق من اكتمال التحويل
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من فشل التحويل
     */
    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'rejected', 'returned', 'cancelled']);
    }

    /**
     * التحقق من كون التحويل دولي
     */
    public function isInternational(): bool
    {
        return $this->country_from !== $this->country_to || 
               in_array($this->type, ['international', 'swift', 'correspondent']);
    }

    /**
     * الحصول على الشبكة المصرفية المناسبة
     */
    public function getRecommendedNetwork(): string
    {
        // تحويل محلي
        if (!$this->isInternational()) {
            return match ($this->country_from) {
                'MA' => 'MICE',
                'SA' => 'SARIE',
                'AE' => 'UAEFTS',
                'EG' => 'MEEM',
                default => 'ACH',
            };
        }

        // تحويل دولي
        if ($this->isEuropeanTransfer()) {
            return 'SEPA';
        }

        return 'SWIFT';
    }

    /**
     * التحقق من كون التحويل أوروبي
     */
    protected function isEuropeanTransfer(): bool
    {
        $europeanCountries = ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'PT', 'IE', 'FI', 'GR'];
        
        return in_array($this->country_from, $europeanCountries) && 
               in_array($this->country_to, $europeanCountries);
    }

    /**
     * حساب الرسوم
     */
    public function calculateFees(): array
    {
        $baseFee = $this->getBaseFee();
        $exchangeFee = $this->getExchangeFee();
        $correspondentFee = $this->getCorrespondentFee();
        $regulatoryFee = $this->getRegulatoryFee();

        $totalFees = $baseFee + $exchangeFee + $correspondentFee + $regulatoryFee;

        return [
            'base_fee' => $baseFee,
            'exchange_fee' => $exchangeFee,
            'correspondent_fee' => $correspondentFee,
            'regulatory_fee' => $regulatoryFee,
            'total_fees' => $totalFees,
            'breakdown' => [
                'processing' => $baseFee,
                'currency_conversion' => $exchangeFee,
                'correspondent_bank' => $correspondentFee,
                'compliance' => $regulatoryFee,
            ],
        ];
    }

    /**
     * الحصول على الرسوم الأساسية
     */
    protected function getBaseFee(): float
    {
        if ($this->isInternational()) {
            return match ($this->type) {
                'swift' => 25.00,
                'correspondent' => 35.00,
                'sepa' => 5.00,
                default => 15.00,
            };
        }

        return match ($this->country_from) {
            'SA' => 5.00,
            'AE' => 10.00,
            'EG' => 2.00,
            'MA' => 3.00,
            default => 5.00,
        };
    }

    /**
     * الحصول على رسوم الصرف
     */
    protected function getExchangeFee(): float
    {
        if ($this->currency === $this->getDestinationCurrency()) {
            return 0;
        }

        return $this->amount * 0.005; // 0.5%
    }

    /**
     * الحصول على رسوم البنك المراسل
     */
    protected function getCorrespondentFee(): float
    {
        if (!$this->isInternational() || $this->type === 'sepa') {
            return 0;
        }

        return 15.00;
    }

    /**
     * الحصول على الرسوم التنظيمية
     */
    protected function getRegulatoryFee(): float
    {
        if ($this->amount > 10000) {
            return 10.00; // رسوم إضافية للمبالغ الكبيرة
        }

        return 0;
    }

    /**
     * الحصول على عملة الوجهة
     */
    protected function getDestinationCurrency(): string
    {
        return match ($this->country_to) {
            'SA' => 'SAR',
            'AE' => 'AED',
            'EG' => 'EGP',
            'MA' => 'MAD',
            'US' => 'USD',
            'GB' => 'GBP',
            default => 'USD',
        };
    }

    /**
     * تقدير وقت المعالجة
     */
    public function estimateProcessingTime(): array
    {
        $hours = match (true) {
            $this->type === 'instant' => 0.1, // 6 دقائق
            $this->type === 'same_day' => 4,
            $this->type === 'next_day' => 24,
            !$this->isInternational() => 2,
            $this->type === 'sepa' => 24,
            $this->type === 'swift' => 72,
            default => 48,
        };

        // إضافة وقت إضافي للفحوصات
        if ($this->amount > 10000) {
            $hours += 24; // فحص إضافي للمبالغ الكبيرة
        }

        if ($this->isHighRiskCountry()) {
            $hours += 48; // فحص إضافي للدول عالية المخاطر
        }

        return [
            'estimated_hours' => $hours,
            'estimated_completion' => now()->addHours($hours),
            'business_days' => ceil($hours / 8),
        ];
    }

    /**
     * التحقق من الدول عالية المخاطر
     */
    protected function isHighRiskCountry(): bool
    {
        $highRiskCountries = ['AF', 'IR', 'KP', 'SY']; // قائمة مبسطة
        
        return in_array($this->country_to, $highRiskCountries) || 
               in_array($this->country_from, $highRiskCountries);
    }

    /**
     * تحديث حالة التحويل
     */
    public function updateStatus(string $status, array $data = []): void
    {
        $oldStatus = $this->status;
        
        $updateData = array_merge(['status' => $status], $data);
        
        // تحديث التواريخ حسب الحالة
        switch ($status) {
            case 'processing':
                $updateData['processed_at'] = now();
                break;
            case 'completed':
                $updateData['completed_at'] = now();
                break;
            case 'failed':
            case 'rejected':
                $updateData['failed_at'] = now();
                break;
        }
        
        $this->update($updateData);
        
        // إطلاق الأحداث
        event(new \App\Domains\Payments\Events\BankTransferStatusChanged($this, $oldStatus, $status));
    }

    /**
     * إجراء فحوصات الامتثال
     */
    public function performComplianceChecks(): array
    {
        $results = [
            'aml_check' => $this->performAMLCheck(),
            'sanctions_check' => $this->performSanctionsCheck(),
            'regulatory_check' => $this->performRegulatoryCheck(),
        ];

        $this->update([
            'compliance_check_result' => $results,
            'aml_check_result' => $results['aml_check'],
            'sanctions_check_result' => $results['sanctions_check'],
        ]);

        return $results;
    }

    /**
     * فحص مكافحة غسل الأموال
     */
    protected function performAMLCheck(): array
    {
        // فحص مبسط - في الواقع سيتصل بخدمات خارجية
        $riskScore = 0;
        $flags = [];

        if ($this->amount > 10000) {
            $riskScore += 30;
            $flags[] = 'large_amount';
        }

        if ($this->isHighRiskCountry()) {
            $riskScore += 40;
            $flags[] = 'high_risk_country';
        }

        return [
            'status' => $riskScore > 70 ? 'high_risk' : ($riskScore > 30 ? 'medium_risk' : 'low_risk'),
            'score' => $riskScore,
            'flags' => $flags,
            'checked_at' => now(),
        ];
    }

    /**
     * فحص العقوبات
     */
    protected function performSanctionsCheck(): array
    {
        // فحص مبسط - في الواقع سيتصل بقوائم العقوبات الدولية
        $sanctionedCountries = ['IR', 'KP', 'SY'];
        
        $isSanctioned = in_array($this->country_to, $sanctionedCountries) || 
                       in_array($this->country_from, $sanctionedCountries);

        return [
            'status' => $isSanctioned ? 'blocked' : 'clear',
            'sanctioned_countries' => $sanctionedCountries,
            'checked_at' => now(),
        ];
    }

    /**
     * الفحص التنظيمي
     */
    protected function performRegulatoryCheck(): array
    {
        $requirements = [];

        if ($this->amount > 3000) {
            $requirements[] = 'enhanced_due_diligence';
        }

        if ($this->amount > 10000) {
            $requirements[] = 'regulatory_reporting';
        }

        return [
            'status' => empty($requirements) ? 'compliant' : 'requires_action',
            'requirements' => $requirements,
            'checked_at' => now(),
        ];
    }

    /**
     * Scope للتحويلات المعلقة
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['pending', 'processing', 'compliance_check']);
    }

    /**
     * Scope للتحويلات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope للتحويلات الدولية
     */
    public function scopeInternational($query)
    {
        return $query->whereColumn('country_from', '!=', 'country_to');
    }

    /**
     * Scope للتحويلات في فترة زمنية
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope للتحويلات حسب العملة
     */
    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope للتحويلات عالية المخاطر
     */
    public function scopeHighRisk($query)
    {
        return $query->where('amount', '>', 10000)
            ->orWhereIn('country_to', ['AF', 'IR', 'KP', 'SY'])
            ->orWhereIn('country_from', ['AF', 'IR', 'KP', 'SY']);
    }
}
