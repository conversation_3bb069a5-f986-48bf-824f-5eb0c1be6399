<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج البرنامج - Program Management
 * يمثل مجموعة من المشاريع المترابطة
 */
class Program extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'code',
        'program_manager_id',
        'status',
        'priority',
        'start_date',
        'end_date',
        'budget',
        'actual_cost',
        'currency',
        'objectives',
        'success_criteria',
        'stakeholders',
        'risks',
        'benefits',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'budget' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'objectives' => 'array',
        'success_criteria' => 'array',
        'stakeholders' => 'array',
        'risks' => 'array',
        'benefits' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع مدير البرنامج
     */
    public function programManager(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'program_manager_id');
    }

    /**
     * العلاقة مع المشاريع
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * الحصول على نسبة الإنجاز الإجمالية
     */
    public function getOverallProgressAttribute(): float
    {
        $projects = $this->projects;
        
        if ($projects->isEmpty()) {
            return 0;
        }

        return $projects->avg('progress_percentage');
    }

    /**
     * الحصول على إجمالي التكلفة
     */
    public function getTotalCostAttribute(): float
    {
        return $this->projects()->sum('actual_cost');
    }

    /**
     * الحصول على إجمالي الميزانية
     */
    public function getTotalBudgetAttribute(): float
    {
        return $this->projects()->sum('budget');
    }
}
