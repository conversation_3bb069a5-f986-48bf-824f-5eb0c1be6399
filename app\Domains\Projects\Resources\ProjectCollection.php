<?php

namespace App\Domains\Projects\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * مجموعة موارد المشاريع
 * تنسيق مجموعة المشاريع مع إحصائيات إضافية
 */
class ProjectCollection extends ResourceCollection
{
    /**
     * تحويل مجموعة الموارد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($project) use ($request) {
                return [
                    'id' => $project->id,
                    'uuid' => $project->uuid,
                    'name' => $project->name,
                    'description' => $project->description,
                    'code' => $project->code,
                    
                    // Status and Priority
                    'status' => $project->status,
                    'status_label' => $project->status ? \App\Domains\Projects\Models\Project::STATUSES[$project->status] ?? $project->status : null,
                    'status_color' => $this->getStatusColor($project->status),
                    'priority' => $project->priority,
                    'priority_label' => $project->priority ? \App\Domains\Projects\Models\Project::PRIORITIES[$project->priority] ?? $project->priority : null,
                    'priority_color' => $this->getPriorityColor($project->priority),
                    'category' => $project->category,
                    'category_label' => $project->category ? \App\Domains\Projects\Models\Project::CATEGORIES[$project->category] ?? $project->category : null,
                    'methodology' => $project->methodology,
                    'methodology_label' => $project->methodology ? \App\Domains\Projects\Models\Project::METHODOLOGIES[$project->methodology] ?? $project->methodology : null,
                    
                    // Timeline
                    'start_date' => $project->start_date?->format('Y-m-d'),
                    'end_date' => $project->end_date?->format('Y-m-d'),
                    'duration_days' => $project->start_date && $project->end_date ? 
                        $project->start_date->diffInDays($project->end_date) : null,
                    'remaining_days' => $project->end_date ? 
                        max(0, now()->diffInDays($project->end_date, false)) : null,
                    'is_overdue' => $project->end_date && now() > $project->end_date && $project->status !== 'COMPLETED',
                    'days_overdue' => $project->end_date && now() > $project->end_date ? 
                        now()->diffInDays($project->end_date) : 0,
                    'progress_percentage' => $project->progress_percentage,
                    
                    // Financial
                    'budget' => $project->budget,
                    'currency' => $project->currency,
                    'formatted_budget' => $project->currency . ' ' . number_format($project->budget, 2),
                    'is_billable' => $project->is_billable,
                    'billing_type' => $project->billing_type,
                    
                    // Team
                    'client' => $project->client ? [
                        'id' => $project->client->id,
                        'name' => $project->client->name,
                        'company' => $project->client->company,
                        'avatar' => $project->client->avatar,
                    ] : null,
                    'project_manager' => $project->projectManager ? [
                        'id' => $project->projectManager->id,
                        'name' => $project->projectManager->name,
                        'avatar' => $project->projectManager->avatar,
                    ] : null,
                    'team_size' => $project->team ? $project->team->count() : 0,
                    
                    // Work Breakdown
                    'tasks_count' => $project->tasks ? $project->tasks->count() : 0,
                    'completed_tasks' => $project->tasks ? 
                        $project->tasks->where('status', 'DONE')->count() : 0,
                    'in_progress_tasks' => $project->tasks ? 
                        $project->tasks->where('status', 'IN_PROGRESS')->count() : 0,
                    'overdue_tasks' => $project->tasks ? 
                        $project->tasks->filter(function ($task) {
                            return $task->due_date && $task->due_date < now() && $task->status !== 'DONE';
                        })->count() : 0,
                    'milestones_count' => $project->milestones ? $project->milestones->count() : 0,
                    'completed_milestones' => $project->milestones ? 
                        $project->milestones->where('is_completed', true)->count() : 0,
                    
                    // Time Tracking
                    'total_logged_hours' => $project->timeEntries ? 
                        $project->timeEntries->sum('hours') : 0,
                    'estimated_hours' => $project->tasks ? 
                        $project->tasks->sum('estimated_hours') : 0,
                    
                    // Health Indicators
                    'overall_health' => $this->calculateOverallHealth($project),
                    'schedule_health' => $this->calculateScheduleHealth($project),
                    'budget_health' => $this->calculateBudgetHealth($project),
                    
                    // Risk and Quality
                    'risk_level' => $project->risk_level,
                    'high_risks_count' => $project->risks ? 
                        $project->risks->where('severity', 'HIGH')->count() : 0,
                    
                    // Activity
                    'last_activity' => $project->activities ? 
                        $project->activities->sortByDesc('created_at')->first()?->created_at?->diffForHumans() : null,
                    
                    // System Info
                    'created_at' => $project->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $project->updated_at?->format('Y-m-d H:i:s'),
                    
                    // Permissions
                    'permissions' => [
                        'can_view' => $request->user()?->can('view', $project),
                        'can_update' => $request->user()?->can('update', $project),
                        'can_delete' => $request->user()?->can('delete', $project),
                    ],
                ];
            }),
            
            // Collection Statistics
            'statistics' => $this->getCollectionStatistics(),
            
            // Aggregated Data
            'aggregations' => $this->getAggregatedData(),
        ];
    }

    /**
     * الحصول على إحصائيات المجموعة
     */
    protected function getCollectionStatistics(): array
    {
        $collection = $this->collection;
        
        return [
            'total_projects' => $collection->count(),
            'active_projects' => $collection->whereIn('status', ['PLANNING', 'IN_PROGRESS'])->count(),
            'completed_projects' => $collection->where('status', 'COMPLETED')->count(),
            'overdue_projects' => $collection->filter(function ($project) {
                return $project->end_date && now() > $project->end_date && $project->status !== 'COMPLETED';
            })->count(),
            'on_hold_projects' => $collection->where('status', 'ON_HOLD')->count(),
            'cancelled_projects' => $collection->where('status', 'CANCELLED')->count(),
            'average_progress' => round($collection->avg('progress_percentage'), 1),
            'total_budget' => $collection->sum('budget'),
            'average_budget' => round($collection->avg('budget'), 2),
            'total_tasks' => $collection->sum(function ($project) {
                return $project->tasks ? $project->tasks->count() : 0;
            }),
            'completed_tasks' => $collection->sum(function ($project) {
                return $project->tasks ? $project->tasks->where('status', 'DONE')->count() : 0;
            }),
            'total_team_members' => $collection->sum(function ($project) {
                return $project->team ? $project->team->count() : 0;
            }),
            'projects_with_high_risk' => $collection->where('risk_level', 'HIGH')->count(),
        ];
    }

    /**
     * الحصول على البيانات المجمعة
     */
    protected function getAggregatedData(): array
    {
        $collection = $this->collection;
        
        return [
            'by_status' => $collection->groupBy('status')->map(function ($group, $status) {
                return [
                    'status' => $status,
                    'status_label' => \App\Domains\Projects\Models\Project::STATUSES[$status] ?? $status,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'color' => $this->getStatusColor($status),
                ];
            })->values(),
            
            'by_priority' => $collection->groupBy('priority')->map(function ($group, $priority) {
                return [
                    'priority' => $priority,
                    'priority_label' => \App\Domains\Projects\Models\Project::PRIORITIES[$priority] ?? $priority,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'color' => $this->getPriorityColor($priority),
                ];
            })->values(),
            
            'by_category' => $collection->groupBy('category')->map(function ($group, $category) {
                return [
                    'category' => $category,
                    'category_label' => \App\Domains\Projects\Models\Project::CATEGORIES[$category] ?? $category,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->values(),
            
            'by_methodology' => $collection->groupBy('methodology')->map(function ($group, $methodology) {
                return [
                    'methodology' => $methodology,
                    'methodology_label' => \App\Domains\Projects\Models\Project::METHODOLOGIES[$methodology] ?? $methodology,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->values(),
            
            'by_client' => $collection->groupBy('client.name')->map(function ($group, $clientName) {
                return [
                    'client_name' => $clientName ?: 'بدون عميل',
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'total_budget' => $group->sum('budget'),
                ];
            })->sortByDesc('count')->values()->take(10),
            
            'by_project_manager' => $collection->groupBy('projectManager.name')->map(function ($group, $managerName) {
                return [
                    'manager_name' => $managerName ?: 'غير محدد',
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'average_progress' => round($group->avg('progress_percentage'), 1),
                ];
            })->sortByDesc('count')->values()->take(10),
            
            'by_health' => $this->getHealthDistribution($collection),
            
            'by_progress_range' => $this->getProgressDistribution($collection),
            
            'by_budget_range' => $this->getBudgetDistribution($collection),
            
            'timeline_analysis' => $this->getTimelineAnalysis($collection),
        ];
    }

    /**
     * الحصول على توزيع الصحة
     */
    protected function getHealthDistribution($collection): array
    {
        $healthGroups = [
            'EXCELLENT' => 0,
            'GOOD' => 0,
            'FAIR' => 0,
            'POOR' => 0,
        ];

        foreach ($collection as $project) {
            $health = $this->calculateOverallHealth($project);
            $healthGroups[$health]++;
        }

        return collect($healthGroups)->map(function ($count, $health) {
            return [
                'health' => $health,
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على توزيع التقدم
     */
    protected function getProgressDistribution($collection): array
    {
        $progressGroups = [
            '0-25' => $collection->whereBetween('progress_percentage', [0, 25])->count(),
            '26-50' => $collection->whereBetween('progress_percentage', [26, 50])->count(),
            '51-75' => $collection->whereBetween('progress_percentage', [51, 75])->count(),
            '76-100' => $collection->whereBetween('progress_percentage', [76, 100])->count(),
        ];

        return collect($progressGroups)->map(function ($count, $range) {
            return [
                'progress_range' => $range . '%',
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على توزيع الميزانية
     */
    protected function getBudgetDistribution($collection): array
    {
        $budgetGroups = [
            '0-50K' => $collection->whereBetween('budget', [0, 50000])->count(),
            '50K-100K' => $collection->whereBetween('budget', [50001, 100000])->count(),
            '100K-500K' => $collection->whereBetween('budget', [100001, 500000])->count(),
            '500K+' => $collection->where('budget', '>', 500000)->count(),
        ];

        return collect($budgetGroups)->map(function ($count, $range) {
            return [
                'budget_range' => $range,
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على تحليل الجدولة الزمنية
     */
    protected function getTimelineAnalysis($collection): array
    {
        $now = now();
        
        return [
            'starting_this_month' => $collection->filter(function ($project) use ($now) {
                return $project->start_date && $project->start_date->isSameMonth($now);
            })->count(),
            'ending_this_month' => $collection->filter(function ($project) use ($now) {
                return $project->end_date && $project->end_date->isSameMonth($now);
            })->count(),
            'overdue' => $collection->filter(function ($project) use ($now) {
                return $project->end_date && $now > $project->end_date && $project->status !== 'COMPLETED';
            })->count(),
            'due_next_week' => $collection->filter(function ($project) use ($now) {
                return $project->end_date && 
                       $project->end_date->between($now, $now->copy()->addWeek()) &&
                       $project->status !== 'COMPLETED';
            })->count(),
        ];
    }

    /**
     * حساب الصحة العامة للمشروع
     */
    protected function calculateOverallHealth($project): string
    {
        $scheduleScore = $this->calculateScheduleHealthScore($project);
        $budgetScore = $this->calculateBudgetHealthScore($project);
        $qualityScore = 80; // قيمة افتراضية

        $averageScore = ($scheduleScore + $budgetScore + $qualityScore) / 3;

        if ($averageScore >= 80) return 'EXCELLENT';
        if ($averageScore >= 60) return 'GOOD';
        if ($averageScore >= 40) return 'FAIR';
        return 'POOR';
    }

    /**
     * حساب صحة الجدولة
     */
    protected function calculateScheduleHealth($project): string
    {
        $score = $this->calculateScheduleHealthScore($project);
        
        if ($score >= 80) return 'ON_TRACK';
        if ($score >= 60) return 'MINOR_DELAYS';
        if ($score >= 40) return 'MODERATE_DELAYS';
        return 'MAJOR_DELAYS';
    }

    /**
     * حساب درجة صحة الجدولة
     */
    protected function calculateScheduleHealthScore($project): int
    {
        if ($project->end_date && now() > $project->end_date && $project->status !== 'COMPLETED') {
            return 20; // متأخر
        }

        if (!$project->start_date || !$project->end_date) {
            return 50; // غير محدد
        }

        $totalDays = $project->start_date->diffInDays($project->end_date);
        $elapsedDays = $project->start_date->diffInDays(now());
        
        if ($totalDays == 0) return 50;

        $timeProgress = min(100, ($elapsedDays / $totalDays) * 100);
        $workProgress = $project->progress_percentage;

        if ($workProgress >= $timeProgress) {
            return 90; // متقدم أو في الموعد
        }

        $variance = $timeProgress - $workProgress;
        return max(20, 90 - ($variance * 2));
    }

    /**
     * حساب صحة الميزانية
     */
    protected function calculateBudgetHealth($project): string
    {
        // يمكن تطوير هذا بناءً على التكاليف الفعلية
        return 'ON_BUDGET';
    }

    /**
     * حساب درجة صحة الميزانية
     */
    protected function calculateBudgetHealthScore($project): int
    {
        // يمكن تطوير هذا بناءً على التكاليف الفعلية
        return 75; // قيمة افتراضية
    }

    /**
     * الحصول على لون الحالة
     */
    protected function getStatusColor(?string $status): string
    {
        return match ($status) {
            'PLANNING' => '#3B82F6',
            'IN_PROGRESS' => '#10B981',
            'ON_HOLD' => '#F59E0B',
            'COMPLETED' => '#059669',
            'CANCELLED' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    protected function getPriorityColor(?string $priority): string
    {
        return match ($priority) {
            'LOW' => '#10B981',
            'MEDIUM' => '#F59E0B',
            'HIGH' => '#EF4444',
            'CRITICAL' => '#DC2626',
            default => '#6B7280',
        };
    }
}
