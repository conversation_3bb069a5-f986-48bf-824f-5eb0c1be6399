<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج مخاطر المشروع - Project Risk Management
 * يدير تحديد وتقييم ومراقبة المخاطر
 */
class ProjectRisk extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'category',
        'probability',
        'impact',
        'risk_score',
        'risk_level',
        'status',
        'identified_by',
        'identified_at',
        'assessed_by',
        'assessed_at',
        'owner_id',
        'due_date',
        'triggers',
        'early_warning_signs',
        'assessment_notes',
        'mitigation_strategy',
        'contingency_plan',
        'residual_risk',
        'metadata',
    ];

    protected $casts = [
        'probability' => 'integer',
        'impact' => 'integer',
        'risk_score' => 'integer',
        'identified_at' => 'datetime',
        'assessed_at' => 'datetime',
        'due_date' => 'date',
        'triggers' => 'array',
        'early_warning_signs' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع محدد المخاطر
     */
    public function identifier(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'identified_by');
    }

    /**
     * العلاقة مع مقيم المخاطر
     */
    public function assessor(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assessed_by');
    }

    /**
     * العلاقة مع مالك المخاطر
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'owner_id');
    }

    /**
     * العلاقة مع خطط التخفيف
     */
    public function mitigations(): HasMany
    {
        return $this->hasMany(RiskMitigation::class, 'risk_id');
    }

    /**
     * العلاقة مع تقييمات المخاطر
     */
    public function assessments(): HasMany
    {
        return $this->hasMany(RiskAssessment::class, 'risk_id');
    }

    /**
     * الحصول على لون المخاطر
     */
    public function getRiskColorAttribute(): string
    {
        return match ($this->risk_level) {
            'CRITICAL' => '#dc3545',
            'HIGH' => '#fd7e14',
            'MEDIUM' => '#ffc107',
            'LOW' => '#28a745',
            default => '#6c757d',
        };
    }

    /**
     * التحقق من تجاوز الموعد المحدد
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['CLOSED', 'RESOLVED']);
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        return $this->due_date ? now()->diffInDays($this->due_date, false) : 0;
    }

    /**
     * حساب نقاط المخاطر
     */
    public function calculateRiskScore(): int
    {
        return $this->probability * $this->impact;
    }

    /**
     * تحديد مستوى المخاطر
     */
    public function determineRiskLevel(): string
    {
        $score = $this->calculateRiskScore();

        return match (true) {
            $score >= 20 => 'CRITICAL',
            $score >= 12 => 'HIGH',
            $score >= 6 => 'MEDIUM',
            default => 'LOW',
        };
    }

    /**
     * تحديث تقييم المخاطر
     */
    public function updateAssessment(int $probability, int $impact, int $assessedBy, string $notes = null): void
    {
        $this->update([
            'probability' => $probability,
            'impact' => $impact,
            'risk_score' => $probability * $impact,
            'risk_level' => $this->determineRiskLevel(),
            'assessed_by' => $assessedBy,
            'assessed_at' => now(),
            'assessment_notes' => $notes,
            'status' => 'ASSESSED',
        ]);

        // تسجيل التقييم
        $this->assessments()->create([
            'probability' => $probability,
            'impact' => $impact,
            'risk_score' => $this->risk_score,
            'notes' => $notes,
            'assessed_by' => $assessedBy,
            'assessed_at' => now(),
        ]);
    }

    /**
     * إغلاق المخاطر
     */
    public function close(int $closedBy, string $reason = null): bool
    {
        $this->update([
            'status' => 'CLOSED',
            'metadata' => array_merge($this->metadata ?? [], [
                'closed_by' => $closedBy,
                'closed_at' => now(),
                'closure_reason' => $reason,
            ]),
        ]);

        return true;
    }

    /**
     * تفعيل المخاطر
     */
    public function activate(int $activatedBy): bool
    {
        $this->update([
            'status' => 'ACTIVE',
            'metadata' => array_merge($this->metadata ?? [], [
                'activated_by' => $activatedBy,
                'activated_at' => now(),
            ]),
        ]);

        return true;
    }

    /**
     * إضافة خطة تخفيف
     */
    public function addMitigation(array $mitigationData, int $createdBy): RiskMitigation
    {
        return $this->mitigations()->create(array_merge($mitigationData, [
            'created_by' => $createdBy,
        ]));
    }

    /**
     * الحصول على خطة التخفيف النشطة
     */
    public function getActiveMitigationAttribute(): ?RiskMitigation
    {
        return $this->mitigations()->where('status', 'ACTIVE')->first();
    }

    /**
     * فحص المؤشرات المبكرة
     */
    public function checkEarlyWarningSignals(): array
    {
        $triggeredSignals = [];
        
        foreach ($this->early_warning_signs as $signal) {
            // منطق فحص المؤشرات المبكرة
            if ($this->isSignalTriggered($signal)) {
                $triggeredSignals[] = $signal;
            }
        }

        return $triggeredSignals;
    }

    /**
     * فحص إشارة محددة
     */
    protected function isSignalTriggered(array $signal): bool
    {
        // منطق فحص الإشارة (يمكن تخصيصه حسب نوع الإشارة)
        return false;
    }

    /**
     * البحث في المخاطر
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('category', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب المستوى
     */
    public function scopeOfLevel($query, string $level)
    {
        return $query->where('risk_level', $level);
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * فلترة المخاطر النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['IDENTIFIED', 'ASSESSED', 'ACTIVE']);
    }

    /**
     * فلترة المخاطر عالية المستوى
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('risk_level', ['HIGH', 'CRITICAL']);
    }

    /**
     * فلترة المخاطر المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['CLOSED', 'RESOLVED']);
    }

    /**
     * ترتيب حسب نقاط المخاطر
     */
    public function scopeOrderByRiskScore($query, string $direction = 'desc')
    {
        return $query->orderBy('risk_score', $direction);
    }

    /**
     * ترتيب حسب تاريخ التحديد
     */
    public function scopeOrderByIdentified($query, string $direction = 'desc')
    {
        return $query->orderBy('identified_at', $direction);
    }
}
