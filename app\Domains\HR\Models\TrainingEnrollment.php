<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج تسجيل التدريب
 */
class TrainingEnrollment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'training_program_id',
        'employee_id',
        'enrollment_date',
        'status',
        'completion_date',
        'completion_percentage',
        'final_score',
        'certificate_issued',
        'certificate_number',
        'notes',
        'enrolled_by',
    ];

    protected $casts = [
        'enrollment_date' => 'date',
        'completion_date' => 'date',
        'completion_percentage' => 'decimal:2',
        'final_score' => 'decimal:2',
        'certificate_issued' => 'boolean',
    ];

    /**
     * حالات التسجيل
     */
    public const STATUSES = [
        'ENROLLED' => 'مسجل',
        'IN_PROGRESS' => 'قيد التنفيذ',
        'COMPLETED' => 'مكتمل',
        'DROPPED' => 'منسحب',
        'FAILED' => 'راسب',
        'CANCELLED' => 'ملغي',
    ];

    /**
     * البرنامج التدريبي
     */
    public function trainingProgram(): BelongsTo
    {
        return $this->belongsTo(TrainingProgram::class);
    }

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * من قام بالتسجيل
     */
    public function enrolledBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'enrolled_by');
    }

    /**
     * حضور الجلسات
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(TrainingAttendance::class);
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * فحص إذا كان التسجيل مكتمل
     */
    public function isCompleted(): bool
    {
        return $this->status === 'COMPLETED';
    }

    /**
     * فحص إذا كان التسجيل نشط
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['ENROLLED', 'IN_PROGRESS']);
    }

    /**
     * الحصول على معدل الحضور
     */
    public function getAttendanceRateAttribute(): float
    {
        $totalSessions = $this->trainingProgram->sessions()->count();
        $attendedSessions = $this->attendances()->where('attended', true)->count();
        
        return $totalSessions > 0 ? ($attendedSessions / $totalSessions) * 100 : 0;
    }

    /**
     * تحديث نسبة الإنجاز
     */
    public function updateCompletionPercentage(): void
    {
        $attendanceRate = $this->getAttendanceRateAttribute();
        $this->update(['completion_percentage' => $attendanceRate]);
        
        // تحديث الحالة تلقائياً
        if ($attendanceRate >= 80 && $this->status === 'IN_PROGRESS') {
            $this->update(['status' => 'COMPLETED', 'completion_date' => now()]);
        }
    }

    /**
     * إصدار شهادة
     */
    public function issueCertificate(): string
    {
        if (!$this->isCompleted()) {
            throw new \Exception('لا يمكن إصدار شهادة للتسجيل غير المكتمل');
        }
        
        $certificateNumber = 'CERT-' . $this->training_program_id . '-' . $this->employee_id . '-' . now()->format('Ymd');
        
        $this->update([
            'certificate_issued' => true,
            'certificate_number' => $certificateNumber,
        ]);
        
        return $certificateNumber;
    }

    /**
     * Scope للتسجيلات النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['ENROLLED', 'IN_PROGRESS']);
    }

    /**
     * Scope للتسجيلات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'COMPLETED');
    }

    /**
     * Scope للتسجيلات في فترة معينة
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('enrollment_date', [$startDate, $endDate]);
    }
}
