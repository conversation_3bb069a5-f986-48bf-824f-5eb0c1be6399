<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج مشاهدة مقال قاعدة المعرفة - Knowledge Base View
 */
class KnowledgeBaseView extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'article_id',
        'user_id',
        'ip_address',
        'user_agent',
        'viewed_at',
        'session_id',
        'referrer',
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
    ];

    public function article(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBaseArticle::class, 'article_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    public function getIsUniqueViewAttribute(): bool
    {
        // تحديد ما إذا كانت هذه مشاهدة فريدة (أول مشاهدة لهذا المستخدم/IP)
        return !self::where('article_id', $this->article_id)
                   ->where(function ($query) {
                       if ($this->user_id) {
                           $query->where('user_id', $this->user_id);
                       } else {
                           $query->where('ip_address', $this->ip_address);
                       }
                   })
                   ->where('id', '!=', $this->id)
                   ->exists();
    }
}
