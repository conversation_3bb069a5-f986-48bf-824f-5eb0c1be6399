<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\Customer;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * خدمة توقعات المبيعات - Sales Forecasting Service
 * توقعات ذكية ومتقدمة للمبيعات والإيرادات
 */
class SalesForecastingService
{
    /**
     * إنشاء توقعات المبيعات الشاملة
     */
    public function generateComprehensiveForecast(Carbon $forecastDate = null): array
    {
        $forecastDate = $forecastDate ?? now()->addMonths(3);
        
        return [
            'forecast_date' => $forecastDate,
            'generated_at' => now(),
            'pipeline_forecast' => $this->generatePipelineForecast($forecastDate),
            'historical_forecast' => $this->generateHistoricalForecast($forecastDate),
            'seasonal_forecast' => $this->generateSeasonalForecast($forecastDate),
            'rep_based_forecast' => $this->generateRepBasedForecast($forecastDate),
            'product_forecast' => $this->generateProductForecast($forecastDate),
            'confidence_intervals' => $this->calculateConfidenceIntervals($forecastDate),
            'risk_analysis' => $this->analyzeRisks($forecastDate),
            'recommendations' => $this->generateRecommendations($forecastDate),
        ];
    }

    /**
     * توقعات بناءً على الأنبوب الحالي
     */
    public function generatePipelineForecast(Carbon $forecastDate): array
    {
        $opportunities = Opportunity::whereNotIn('stage', ['won', 'lost'])
                                  ->where('expected_close_date', '<=', $forecastDate)
                                  ->get();

        $forecast = [
            'total_opportunities' => $opportunities->count(),
            'total_pipeline_value' => $opportunities->sum('value'),
            'weighted_pipeline_value' => $opportunities->sum(function ($opp) {
                return $opp->value * ($opp->probability / 100);
            }),
            'stage_breakdown' => [],
            'probability_scenarios' => $this->calculateProbabilityScenarios($opportunities),
        ];

        // تحليل حسب المرحلة
        foreach (Opportunity::STAGES as $stage => $label) {
            $stageOpportunities = $opportunities->where('stage', $stage);
            
            $forecast['stage_breakdown'][$stage] = [
                'label' => $label,
                'count' => $stageOpportunities->count(),
                'total_value' => $stageOpportunities->sum('value'),
                'weighted_value' => $stageOpportunities->sum(function ($opp) {
                    return $opp->value * ($opp->probability / 100);
                }),
                'average_probability' => $stageOpportunities->avg('probability'),
                'expected_conversions' => $this->calculateExpectedConversions($stageOpportunities, $stage),
            ];
        }

        return $forecast;
    }

    /**
     * توقعات بناءً على البيانات التاريخية
     */
    public function generateHistoricalForecast(Carbon $forecastDate): array
    {
        $historicalData = $this->getHistoricalSalesData();
        
        return [
            'trend_analysis' => $this->analyzeTrends($historicalData),
            'growth_rate' => $this->calculateGrowthRate($historicalData),
            'seasonal_patterns' => $this->identifySeasonalPatterns($historicalData),
            'moving_averages' => $this->calculateMovingAverages($historicalData),
            'linear_regression' => $this->performLinearRegression($historicalData, $forecastDate),
            'exponential_smoothing' => $this->performExponentialSmoothing($historicalData, $forecastDate),
        ];
    }

    /**
     * توقعات موسمية
     */
    public function generateSeasonalForecast(Carbon $forecastDate): array
    {
        $seasonalData = $this->getSeasonalData();
        
        return [
            'seasonal_index' => $this->calculateSeasonalIndex($forecastDate),
            'seasonal_adjustment' => $this->calculateSeasonalAdjustment($forecastDate),
            'holiday_impact' => $this->analyzeHolidayImpact($forecastDate),
            'quarterly_patterns' => $this->analyzeQuarterlyPatterns(),
            'monthly_patterns' => $this->analyzeMonthlyPatterns(),
            'weekly_patterns' => $this->analyzeWeeklyPatterns(),
        ];
    }

    /**
     * توقعات حسب مندوب المبيعات
     */
    public function generateRepBasedForecast(Carbon $forecastDate): array
    {
        $salesReps = \App\Domains\HR\Models\Employee::where('department', 'sales')
                                                   ->where('is_active', true)
                                                   ->get();

        $forecast = [];
        
        foreach ($salesReps as $rep) {
            $repOpportunities = Opportunity::where('assigned_to', $rep->id)
                                          ->whereNotIn('stage', ['won', 'lost'])
                                          ->where('expected_close_date', '<=', $forecastDate)
                                          ->get();

            $historicalPerformance = $this->getRepHistoricalPerformance($rep->id);
            
            $forecast[$rep->id] = [
                'rep_name' => $rep->full_name,
                'current_pipeline' => [
                    'count' => $repOpportunities->count(),
                    'value' => $repOpportunities->sum('value'),
                    'weighted_value' => $repOpportunities->sum(function ($opp) {
                        return $opp->value * ($opp->probability / 100);
                    }),
                ],
                'historical_performance' => $historicalPerformance,
                'predicted_performance' => $this->predictRepPerformance($rep->id, $forecastDate),
                'quota_attainment' => $this->calculateQuotaAttainment($rep->id, $forecastDate),
                'risk_factors' => $this->identifyRepRiskFactors($rep->id),
            ];
        }

        return $forecast;
    }

    /**
     * توقعات حسب المنتج/الخدمة
     */
    public function generateProductForecast(Carbon $forecastDate): array
    {
        // تحليل الفرص حسب نوع المنتج/الخدمة
        $productData = Opportunity::whereNotIn('stage', ['won', 'lost'])
                                 ->where('expected_close_date', '<=', $forecastDate)
                                 ->select('product_category', DB::raw('COUNT(*) as count'), DB::raw('SUM(value) as total_value'))
                                 ->groupBy('product_category')
                                 ->get();

        $forecast = [];
        
        foreach ($productData as $product) {
            $forecast[$product->product_category] = [
                'category' => $product->product_category,
                'opportunity_count' => $product->count,
                'total_value' => $product->total_value,
                'historical_performance' => $this->getProductHistoricalPerformance($product->product_category),
                'market_trends' => $this->analyzeProductMarketTrends($product->product_category),
                'competitive_analysis' => $this->analyzeProductCompetition($product->product_category),
                'demand_forecast' => $this->forecastProductDemand($product->product_category, $forecastDate),
            ];
        }

        return $forecast;
    }

    /**
     * حساب فترات الثقة
     */
    public function calculateConfidenceIntervals(Carbon $forecastDate): array
    {
        $baselineForecast = $this->getBaselineForecast($forecastDate);
        
        return [
            'conservative' => [
                'probability' => 90,
                'value' => $baselineForecast * 0.7,
                'description' => 'سيناريو محافظ - احتمالية تحقيق 90%',
            ],
            'realistic' => [
                'probability' => 70,
                'value' => $baselineForecast * 0.85,
                'description' => 'سيناريو واقعي - احتمالية تحقيق 70%',
            ],
            'optimistic' => [
                'probability' => 50,
                'value' => $baselineForecast,
                'description' => 'سيناريو متفائل - احتمالية تحقيق 50%',
            ],
            'stretch' => [
                'probability' => 25,
                'value' => $baselineForecast * 1.2,
                'description' => 'سيناريو طموح - احتمالية تحقيق 25%',
            ],
        ];
    }

    /**
     * تحليل المخاطر
     */
    public function analyzeRisks(Carbon $forecastDate): array
    {
        return [
            'pipeline_risks' => $this->analyzePipelineRisks(),
            'market_risks' => $this->analyzeMarketRisks(),
            'competitive_risks' => $this->analyzeCompetitiveRisks(),
            'economic_risks' => $this->analyzeEconomicRisks(),
            'operational_risks' => $this->analyzeOperationalRisks(),
            'mitigation_strategies' => $this->generateMitigationStrategies(),
        ];
    }

    /**
     * توليد التوصيات
     */
    public function generateRecommendations(Carbon $forecastDate): array
    {
        $forecast = $this->generatePipelineForecast($forecastDate);
        $risks = $this->analyzeRisks($forecastDate);
        
        $recommendations = [];
        
        // توصيات بناءً على الأنبوب
        if ($forecast['weighted_pipeline_value'] < $this->getTargetRevenue($forecastDate)) {
            $recommendations[] = [
                'type' => 'pipeline_gap',
                'priority' => 'high',
                'title' => 'فجوة في الأنبوب',
                'description' => 'القيمة المرجحة للأنبوب أقل من الهدف المطلوب',
                'actions' => [
                    'زيادة جهود توليد العملاء المحتملين',
                    'تسريع عملية المبيعات',
                    'تحسين معدلات التحويل',
                ],
            ];
        }
        
        // توصيات بناءً على المخاطر
        foreach ($risks as $riskType => $riskData) {
            if (isset($riskData['level']) && $riskData['level'] === 'high') {
                $recommendations[] = [
                    'type' => 'risk_mitigation',
                    'priority' => 'high',
                    'title' => "تخفيف مخاطر {$riskType}",
                    'description' => $riskData['description'] ?? '',
                    'actions' => $riskData['mitigation_actions'] ?? [],
                ];
            }
        }
        
        return $recommendations;
    }

    /**
     * توقعات الإيرادات الشهرية
     */
    public function generateMonthlyRevenueForecast(int $months = 12): array
    {
        $forecast = [];
        
        for ($i = 1; $i <= $months; $i++) {
            $targetDate = now()->addMonths($i);
            
            $forecast[$targetDate->format('Y-m')] = [
                'month' => $targetDate->format('Y-m'),
                'pipeline_forecast' => $this->generatePipelineForecast($targetDate),
                'historical_forecast' => $this->generateHistoricalForecast($targetDate),
                'confidence_level' => $this->calculateForecastConfidence($targetDate),
                'key_assumptions' => $this->getKeyAssumptions($targetDate),
            ];
        }
        
        return $forecast;
    }

    /**
     * تحديث التوقعات بناءً على البيانات الجديدة
     */
    public function updateForecast(array $newData): array
    {
        // تحديث النماذج التنبؤية بناءً على البيانات الجديدة
        $this->updatePredictiveModels($newData);
        
        // إعادة حساب التوقعات
        return $this->generateComprehensiveForecast();
    }

    // دوال مساعدة للحسابات المعقدة

    protected function calculateProbabilityScenarios($opportunities): array
    {
        return [
            'best_case' => $opportunities->sum('value'), // 100% احتمالية
            'worst_case' => $opportunities->where('probability', '>=', 90)->sum('value'), // فقط الفرص عالية الاحتمالية
            'most_likely' => $opportunities->sum(function ($opp) {
                return $opp->value * ($opp->probability / 100);
            }),
        ];
    }

    protected function calculateExpectedConversions($opportunities, string $stage): int
    {
        $conversionRates = [
            'lead' => 0.15,
            'qualified' => 0.25,
            'proposal' => 0.40,
            'negotiation' => 0.65,
            'contract' => 0.85,
        ];

        $rate = $conversionRates[$stage] ?? 0;
        return round($opportunities->count() * $rate);
    }

    protected function getHistoricalSalesData(): array
    {
        return Opportunity::where('stage', 'won')
                         ->where('updated_at', '>=', now()->subYears(2))
                         ->selectRaw('DATE_FORMAT(updated_at, "%Y-%m") as month, SUM(value) as revenue, COUNT(*) as deals')
                         ->groupBy('month')
                         ->orderBy('month')
                         ->get()
                         ->toArray();
    }

    protected function analyzeTrends(array $historicalData): array
    {
        // تحليل الاتجاهات في البيانات التاريخية
        $revenues = array_column($historicalData, 'revenue');
        $months = count($revenues);
        
        if ($months < 2) {
            return ['trend' => 'insufficient_data'];
        }
        
        $slope = $this->calculateSlope($revenues);
        
        return [
            'trend' => $slope > 0 ? 'increasing' : ($slope < 0 ? 'decreasing' : 'stable'),
            'slope' => $slope,
            'growth_rate' => $this->calculateGrowthRate($historicalData),
            'volatility' => $this->calculateVolatility($revenues),
        ];
    }

    protected function calculateGrowthRate(array $historicalData): float
    {
        if (count($historicalData) < 2) {
            return 0;
        }
        
        $revenues = array_column($historicalData, 'revenue');
        $firstValue = reset($revenues);
        $lastValue = end($revenues);
        $periods = count($revenues) - 1;
        
        if ($firstValue <= 0 || $periods <= 0) {
            return 0;
        }
        
        return round((pow($lastValue / $firstValue, 1 / $periods) - 1) * 100, 2);
    }

    protected function calculateSlope(array $values): float
    {
        $n = count($values);
        $x = range(1, $n);
        
        $sumX = array_sum($x);
        $sumY = array_sum($values);
        $sumXY = 0;
        $sumXX = 0;
        
        for ($i = 0; $i < $n; $i++) {
            $sumXY += $x[$i] * $values[$i];
            $sumXX += $x[$i] * $x[$i];
        }
        
        return ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);
    }

    protected function calculateVolatility(array $values): float
    {
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function ($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);
        
        return sqrt($variance);
    }

    protected function getBaselineForecast(Carbon $forecastDate): float
    {
        return Opportunity::whereNotIn('stage', ['won', 'lost'])
                         ->where('expected_close_date', '<=', $forecastDate)
                         ->sum(DB::raw('value * (probability / 100)'));
    }

    protected function getTargetRevenue(Carbon $forecastDate): float
    {
        // الحصول على الهدف المطلوب للفترة
        return 1000000; // مثال: مليون كهدف
    }

    protected function calculateForecastConfidence(Carbon $targetDate): float
    {
        // حساب مستوى الثقة في التوقع بناءً على المسافة الزمنية والبيانات المتاحة
        $daysAhead = now()->diffInDays($targetDate);
        
        if ($daysAhead <= 30) return 85;
        if ($daysAhead <= 90) return 70;
        if ($daysAhead <= 180) return 55;
        return 40;
    }

    // دوال مساعدة إضافية (ستكون فارغة للآن)
    protected function identifySeasonalPatterns(array $data): array { return []; }
    protected function calculateMovingAverages(array $data): array { return []; }
    protected function performLinearRegression(array $data, Carbon $date): array { return []; }
    protected function performExponentialSmoothing(array $data, Carbon $date): array { return []; }
    protected function getSeasonalData(): array { return []; }
    protected function calculateSeasonalIndex(Carbon $date): float { return 1.0; }
    protected function calculateSeasonalAdjustment(Carbon $date): float { return 1.0; }
    protected function analyzeHolidayImpact(Carbon $date): array { return []; }
    protected function analyzeQuarterlyPatterns(): array { return []; }
    protected function analyzeMonthlyPatterns(): array { return []; }
    protected function analyzeWeeklyPatterns(): array { return []; }
    protected function getRepHistoricalPerformance(int $repId): array { return []; }
    protected function predictRepPerformance(int $repId, Carbon $date): array { return []; }
    protected function calculateQuotaAttainment(int $repId, Carbon $date): array { return []; }
    protected function identifyRepRiskFactors(int $repId): array { return []; }
    protected function getProductHistoricalPerformance(string $category): array { return []; }
    protected function analyzeProductMarketTrends(string $category): array { return []; }
    protected function analyzeProductCompetition(string $category): array { return []; }
    protected function forecastProductDemand(string $category, Carbon $date): array { return []; }
    protected function analyzePipelineRisks(): array { return []; }
    protected function analyzeMarketRisks(): array { return []; }
    protected function analyzeCompetitiveRisks(): array { return []; }
    protected function analyzeEconomicRisks(): array { return []; }
    protected function analyzeOperationalRisks(): array { return []; }
    protected function generateMitigationStrategies(): array { return []; }
    protected function getKeyAssumptions(Carbon $date): array { return []; }
    protected function updatePredictiveModels(array $data): void { }
}
