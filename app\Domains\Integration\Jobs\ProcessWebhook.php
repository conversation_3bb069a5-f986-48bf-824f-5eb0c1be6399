<?php

namespace App\Domains\Integration\Jobs;

use App\Domains\Integration\Models\ExternalIntegration;
use App\Domains\Integration\Services\WebhookProcessor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Process Webhook Job
 * 
 * Handles processing of incoming webhooks from external systems
 * with signature verification, retry logic, and error handling
 */
class ProcessWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $webhookId;
    public ExternalIntegration $integration;
    public string $provider;
    public string $event;
    public array $payload;
    public array $headers;
    public string $signature;
    public array $metadata;
    public int $tries = 3;
    public int $maxExceptions = 3;
    public int $timeout = 300; // 5 minutes
    public int $retryAfter = 60; // 1 minute

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $webhookId,
        ExternalIntegration $integration,
        string $provider,
        string $event,
        array $payload,
        array $headers = [],
        string $signature = '',
        array $metadata = []
    ) {
        $this->webhookId = $webhookId;
        $this->integration = $integration;
        $this->provider = $provider;
        $this->event = $event;
        $this->payload = $payload;
        $this->headers = $headers;
        $this->signature = $signature;
        $this->metadata = $metadata;

        // Set queue based on event priority
        $this->onQueue($this->determineQueue());
        
        // Set delay if specified
        if (isset($metadata['delay_seconds'])) {
            $this->delay(now()->addSeconds($metadata['delay_seconds']));
        }
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping($this->webhookId),
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(WebhookProcessor $webhookProcessor): void
    {
        $startTime = microtime(true);
        
        try {
            Log::info('Processing webhook', [
                'webhook_id' => $this->webhookId,
                'integration_id' => $this->integration->id,
                'provider' => $this->provider,
                'event' => $this->event,
                'attempt' => $this->attempts(),
            ]);

            // Update webhook status
            $this->updateWebhookStatus('processing');

            // Validate webhook
            $this->validateWebhook();

            // Verify signature
            $this->verifySignature($webhookProcessor);

            // Check for duplicate
            $this->checkForDuplicate();

            // Process the webhook
            $result = $webhookProcessor->processWebhook(
                $this->integration,
                $this->provider,
                $this->event,
                $this->payload,
                $this->headers,
                $this->metadata
            );

            $processingTime = microtime(true) - $startTime;

            // Update webhook status
            $this->updateWebhookStatus('completed', $result);

            // Store webhook log
            $this->storeWebhookLog('success', $result, $processingTime);

            Log::info('Webhook processed successfully', [
                'webhook_id' => $this->webhookId,
                'processing_time' => $processingTime,
                'result' => $result,
            ]);

        } catch (\Exception $e) {
            $this->handleWebhookFailure($e, microtime(true) - $startTime);
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook processing failed permanently', [
            'webhook_id' => $this->webhookId,
            'integration_id' => $this->integration->id,
            'provider' => $this->provider,
            'event' => $this->event,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update webhook status
        $this->updateWebhookStatus('failed', null, $exception->getMessage());

        // Store webhook log
        $this->storeWebhookLog('failed', null, 0, $exception->getMessage());

        // Clean up any resources
        $this->cleanup();
    }

    /**
     * Validate webhook
     */
    protected function validateWebhook(): void
    {
        // Check if integration is active
        if (!$this->integration->is_active) {
            throw new \Exception("Integration {$this->integration->name} is not active");
        }

        // Check if webhook processing is enabled
        $webhookConfig = $this->integration->webhook_config ?? [];
        if (!($webhookConfig['enabled'] ?? true)) {
            throw new \Exception("Webhook processing is disabled for integration {$this->integration->name}");
        }

        // Validate event type
        $allowedEvents = $webhookConfig['allowed_events'] ?? [];
        if (!empty($allowedEvents) && !in_array($this->event, $allowedEvents)) {
            throw new \Exception("Event type {$this->event} is not allowed for integration {$this->integration->name}");
        }

        // Validate payload structure
        if (empty($this->payload)) {
            throw new \Exception("Webhook payload is empty");
        }
    }

    /**
     * Verify webhook signature
     */
    protected function verifySignature(WebhookProcessor $webhookProcessor): void
    {
        $webhookConfig = $this->integration->webhook_config ?? [];
        
        if ($webhookConfig['verify_signature'] ?? true) {
            $isValid = $webhookProcessor->verifySignature(
                $this->integration,
                $this->provider,
                $this->payload,
                $this->signature,
                $this->headers
            );

            if (!$isValid) {
                throw new \Exception("Webhook signature verification failed");
            }
        }
    }

    /**
     * Check for duplicate webhook
     */
    protected function checkForDuplicate(): void
    {
        $webhookConfig = $this->integration->webhook_config ?? [];
        
        if ($webhookConfig['check_duplicates'] ?? true) {
            $duplicateKey = $this->generateDuplicateKey();
            
            if (Cache::has($duplicateKey)) {
                throw new \Exception("Duplicate webhook detected: {$this->webhookId}");
            }

            // Store for duplicate detection (24 hours)
            Cache::put($duplicateKey, true, 86400);
        }
    }

    /**
     * Generate duplicate detection key
     */
    protected function generateDuplicateKey(): string
    {
        $keyData = [
            'integration_id' => $this->integration->id,
            'provider' => $this->provider,
            'event' => $this->event,
            'payload_hash' => md5(json_encode($this->payload)),
        ];

        return 'webhook_duplicate:' . md5(json_encode($keyData));
    }

    /**
     * Determine queue based on event priority
     */
    protected function determineQueue(): string
    {
        $priority = $this->metadata['priority'] ?? $this->getEventPriority();
        
        return match ($priority) {
            'critical' => 'webhook-critical',
            'high' => 'webhook-high',
            'low' => 'webhook-low',
            default => 'webhook-normal',
        };
    }

    /**
     * Get event priority based on type
     */
    protected function getEventPriority(): string
    {
        $highPriorityEvents = [
            'order.created',
            'payment.completed',
            'payment.failed',
            'subscription.cancelled',
            'account.suspended',
        ];

        $criticalEvents = [
            'security.breach',
            'system.down',
            'data.corruption',
        ];

        if (in_array($this->event, $criticalEvents)) {
            return 'critical';
        }

        if (in_array($this->event, $highPriorityEvents)) {
            return 'high';
        }

        return 'normal';
    }

    /**
     * Update webhook status in cache
     */
    protected function updateWebhookStatus(string $status, ?array $result = null, ?string $error = null): void
    {
        $statusData = [
            'webhook_id' => $this->webhookId,
            'integration_id' => $this->integration->id,
            'status' => $status,
            'provider' => $this->provider,
            'event' => $this->event,
            'updated_at' => now()->toISOString(),
            'attempt' => $this->attempts(),
        ];

        if ($result) {
            $statusData['result'] = $result;
        }

        if ($error) {
            $statusData['error'] = $error;
        }

        Cache::put("webhook_status:{$this->webhookId}", $statusData, 3600);
    }

    /**
     * Store webhook processing log
     */
    protected function storeWebhookLog(string $status, ?array $result = null, float $processingTime = 0, ?string $error = null): void
    {
        $logData = [
            'webhook_id' => $this->webhookId,
            'integration_id' => $this->integration->id,
            'provider' => $this->provider,
            'event' => $this->event,
            'status' => $status,
            'processing_time' => $processingTime,
            'payload_size' => strlen(json_encode($this->payload)),
            'attempt' => $this->attempts(),
            'processed_at' => now(),
        ];

        if ($result) {
            $logData['result'] = $result;
        }

        if ($error) {
            $logData['error'] = $error;
        }

        // Store in database or cache depending on configuration
        Cache::put("webhook_log:{$this->webhookId}", $logData, 86400);
    }

    /**
     * Handle webhook processing failure
     */
    protected function handleWebhookFailure(\Exception $e, float $processingTime): void
    {
        Log::warning('Webhook processing failed', [
            'webhook_id' => $this->webhookId,
            'integration_id' => $this->integration->id,
            'error' => $e->getMessage(),
            'attempt' => $this->attempts(),
            'processing_time' => $processingTime,
        ]);

        // Update webhook status
        $this->updateWebhookStatus('failed', null, $e->getMessage());

        // Store webhook log
        $this->storeWebhookLog('failed', null, $processingTime, $e->getMessage());

        // Re-throw to trigger retry mechanism
        throw $e;
    }

    /**
     * Cleanup resources
     */
    protected function cleanup(): void
    {
        // Remove temporary cache entries
        Cache::forget("webhook_lock:{$this->webhookId}");
        
        // Clean up any temporary files or resources
        // Implementation depends on specific requirements
    }
}
