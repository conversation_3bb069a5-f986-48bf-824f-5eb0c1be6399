<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Carbon\Carbon;

/**
 * نموذج رصيد الإجازات
 * تتبع دقيق لأرصدة الإجازات مع دعم الترحيل والانتهاء
 */
class LeaveBalance extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'year',
        'period_start',
        'period_end',
        
        // Balance Details
        'entitled_days',
        'used_days',
        'remaining_days',
        'pending_days',
        'carried_forward_days',
        'carried_forward_from_year',
        'adjustment_days',
        'adjustment_reason',
        
        // Expiry and Carry Forward
        'expires_at',
        'carry_forward_expires_at',
        'auto_carry_forward',
        'max_carry_forward_days',
        
        // Accrual System
        'accrual_method',
        'accrual_rate_per_month',
        'accrued_to_date',
        'next_accrual_date',
        'prorated_entitlement',
        
        // Status and Flags
        'is_active',
        'is_frozen',
        'freeze_reason',
        'frozen_at',
        'frozen_by',
        
        // Approval and Audit
        'approved_by',
        'approved_at',
        'last_calculated_at',
        'calculation_method',
        
        // System Fields
        'notes',
        'metadata',
    ];

    protected $casts = [
        'year' => 'integer',
        'period_start' => 'date',
        'period_end' => 'date',
        'expires_at' => 'date',
        'carry_forward_expires_at' => 'date',
        'next_accrual_date' => 'date',
        'frozen_at' => 'datetime',
        'approved_at' => 'datetime',
        'last_calculated_at' => 'datetime',
        'entitled_days' => 'decimal:2',
        'used_days' => 'decimal:2',
        'remaining_days' => 'decimal:2',
        'pending_days' => 'decimal:2',
        'carried_forward_days' => 'decimal:2',
        'adjustment_days' => 'decimal:2',
        'max_carry_forward_days' => 'decimal:2',
        'accrual_rate_per_month' => 'decimal:2',
        'accrued_to_date' => 'decimal:2',
        'prorated_entitlement' => 'decimal:2',
        'carried_forward_from_year' => 'integer',
        'auto_carry_forward' => 'boolean',
        'is_active' => 'boolean',
        'is_frozen' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * طرق الاستحقاق
     */
    const ACCRUAL_METHODS = [
        'ANNUAL' => 'سنوي',
        'MONTHLY' => 'شهري',
        'WEEKLY' => 'أسبوعي',
        'DAILY' => 'يومي',
        'PRORATED' => 'متناسب',
        'IMMEDIATE' => 'فوري',
    ];

    /**
     * طرق الحساب
     */
    const CALCULATION_METHODS = [
        'MANUAL' => 'يدوي',
        'AUTOMATIC' => 'تلقائي',
        'SYSTEM_GENERATED' => 'مولد من النظام',
        'IMPORTED' => 'مستورد',
        'ADJUSTED' => 'معدل',
    ];

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * نوع الإجازة
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * المعتمد من قبل
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * من قام بالتجميد
     */
    public function freezer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'frozen_by');
    }

    /**
     * حساب الرصيد المتاح (الباقي - المعلق)
     */
    public function getAvailableDaysAttribute(): float
    {
        return max(0, $this->remaining_days - $this->pending_days);
    }

    /**
     * حساب نسبة الاستخدام
     */
    public function getUsagePercentageAttribute(): float
    {
        if ($this->entitled_days == 0) {
            return 0;
        }

        return round(($this->used_days / $this->entitled_days) * 100, 2);
    }

    /**
     * التحقق من انتهاء صلاحية الرصيد
     */
    public function isExpired(): bool
    {
        return $this->expires_at && now() > $this->expires_at;
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expires_at && 
               now()->diffInDays($this->expires_at, false) <= $days && 
               !$this->isExpired();
    }

    /**
     * حساب الاستحقاق الشهري
     */
    public function calculateMonthlyAccrual(): float
    {
        if ($this->accrual_method !== 'MONTHLY') {
            return 0;
        }

        return $this->accrual_rate_per_month ?? 0;
    }

    /**
     * حساب الاستحقاق المتناسب
     */
    public function calculateProratedEntitlement(Carbon $startDate = null): float
    {
        $startDate = $startDate ?? $this->employee->hire_date;
        $yearStart = Carbon::create($this->year, 1, 1);
        $yearEnd = Carbon::create($this->year, 12, 31);

        // إذا بدأ الموظف في نفس السنة
        if ($startDate->year == $this->year) {
            $workingDays = $startDate->diffInDays($yearEnd) + 1;
            $totalDays = $yearStart->diffInDays($yearEnd) + 1;
            $ratio = $workingDays / $totalDays;
            
            return round($this->leaveType->default_days_per_year * $ratio, 2);
        }

        return $this->leaveType->default_days_per_year;
    }

    /**
     * تحديث الاستحقاق
     */
    public function updateAccrual(): bool
    {
        if ($this->is_frozen || !$this->is_active) {
            return false;
        }

        $newAccrual = 0;

        switch ($this->accrual_method) {
            case 'MONTHLY':
                $newAccrual = $this->calculateMonthlyAccrual();
                break;
            case 'PRORATED':
                $newAccrual = $this->calculateProratedEntitlement();
                break;
            case 'ANNUAL':
                $newAccrual = $this->leaveType->default_days_per_year;
                break;
        }

        if ($newAccrual > 0) {
            $this->update([
                'accrued_to_date' => $this->accrued_to_date + $newAccrual,
                'remaining_days' => $this->remaining_days + $newAccrual,
                'last_calculated_at' => now(),
                'next_accrual_date' => $this->calculateNextAccrualDate(),
            ]);
        }

        return true;
    }

    /**
     * حساب تاريخ الاستحقاق التالي
     */
    protected function calculateNextAccrualDate(): ?Carbon
    {
        switch ($this->accrual_method) {
            case 'MONTHLY':
                return now()->addMonth();
            case 'WEEKLY':
                return now()->addWeek();
            case 'DAILY':
                return now()->addDay();
            default:
                return null;
        }
    }

    /**
     * خصم أيام من الرصيد
     */
    public function deduct(float $days, string $reason = null): bool
    {
        if ($this->available_days < $days) {
            return false;
        }

        $this->update([
            'used_days' => $this->used_days + $days,
            'remaining_days' => $this->remaining_days - $days,
            'last_calculated_at' => now(),
        ]);

        // تسجيل السبب إذا تم تقديمه
        if ($reason) {
            $this->update(['notes' => $this->notes . "\n" . now()->format('Y-m-d H:i') . ": خصم {$days} أيام - {$reason}"]);
        }

        return true;
    }

    /**
     * إضافة أيام للرصيد
     */
    public function credit(float $days, string $reason = null): bool
    {
        $this->update([
            'used_days' => max(0, $this->used_days - $days),
            'remaining_days' => $this->remaining_days + $days,
            'last_calculated_at' => now(),
        ]);

        // تسجيل السبب إذا تم تقديمه
        if ($reason) {
            $this->update(['notes' => $this->notes . "\n" . now()->format('Y-m-d H:i') . ": إضافة {$days} أيام - {$reason}"]);
        }

        return true;
    }

    /**
     * تعديل الرصيد
     */
    public function adjust(float $days, string $reason): bool
    {
        $this->update([
            'adjustment_days' => $this->adjustment_days + $days,
            'remaining_days' => $this->remaining_days + $days,
            'adjustment_reason' => $reason,
            'last_calculated_at' => now(),
        ]);

        return true;
    }

    /**
     * ترحيل الرصيد للسنة التالية
     */
    public function carryForward(int $nextYear): ?self
    {
        if (!$this->auto_carry_forward || $this->remaining_days <= 0) {
            return null;
        }

        $carryForwardDays = min(
            $this->remaining_days,
            $this->max_carry_forward_days ?? $this->leaveType->carry_forward_days ?? $this->remaining_days
        );

        // إنشاء رصيد جديد للسنة التالية
        $nextYearBalance = self::create([
            'employee_id' => $this->employee_id,
            'leave_type_id' => $this->leave_type_id,
            'year' => $nextYear,
            'entitled_days' => $this->leaveType->default_days_per_year,
            'carried_forward_days' => $carryForwardDays,
            'carried_forward_from_year' => $this->year,
            'remaining_days' => $this->leaveType->default_days_per_year + $carryForwardDays,
            'used_days' => 0,
            'pending_days' => 0,
            'accrual_method' => $this->accrual_method,
            'accrual_rate_per_month' => $this->accrual_rate_per_month,
            'auto_carry_forward' => $this->auto_carry_forward,
            'max_carry_forward_days' => $this->max_carry_forward_days,
            'expires_at' => Carbon::create($nextYear, 12, 31),
            'carry_forward_expires_at' => $this->leaveType->carry_forward_expiry_months ? 
                Carbon::create($nextYear, 1, 1)->addMonths($this->leaveType->carry_forward_expiry_months) : null,
            'is_active' => true,
            'calculation_method' => 'SYSTEM_GENERATED',
        ]);

        // تحديث الرصيد الحالي
        $this->update([
            'is_active' => false,
            'remaining_days' => $this->remaining_days - $carryForwardDays,
        ]);

        return $nextYearBalance;
    }

    /**
     * تجميد الرصيد
     */
    public function freeze(string $reason, int $frozenBy = null): bool
    {
        return $this->update([
            'is_frozen' => true,
            'freeze_reason' => $reason,
            'frozen_at' => now(),
            'frozen_by' => $frozenBy ?? auth()->id(),
        ]);
    }

    /**
     * إلغاء تجميد الرصيد
     */
    public function unfreeze(): bool
    {
        return $this->update([
            'is_frozen' => false,
            'freeze_reason' => null,
            'frozen_at' => null,
            'frozen_by' => null,
        ]);
    }

    /**
     * إعادة حساب الرصيد
     */
    public function recalculate(): bool
    {
        // حساب الأيام المستخدمة من طلبات الإجازة المعتمدة
        $usedDays = $this->employee->leaveRequests()
                                  ->where('leave_type_id', $this->leave_type_id)
                                  ->where('status', 'APPROVED')
                                  ->whereYear('start_date', $this->year)
                                  ->sum('working_days');

        // حساب الأيام المعلقة من الطلبات قيد المراجعة
        $pendingDays = $this->employee->leaveRequests()
                                     ->where('leave_type_id', $this->leave_type_id)
                                     ->whereIn('status', ['SUBMITTED', 'PENDING_MANAGER', 'PENDING_HR'])
                                     ->whereYear('start_date', $this->year)
                                     ->sum('working_days');

        $this->update([
            'used_days' => $usedDays,
            'pending_days' => $pendingDays,
            'remaining_days' => $this->entitled_days + $this->carried_forward_days + $this->adjustment_days - $usedDays,
            'last_calculated_at' => now(),
            'calculation_method' => 'AUTOMATIC',
        ]);

        return true;
    }

    /**
     * نطاق للسنة
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }

    /**
     * نطاق للموظف
     */
    public function scopeForEmployee($query, int $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * نطاق لنوع الإجازة
     */
    public function scopeForLeaveType($query, int $leaveTypeId)
    {
        return $query->where('leave_type_id', $leaveTypeId);
    }

    /**
     * نطاق للأرصدة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق للأرصدة المجمدة
     */
    public function scopeFrozen($query)
    {
        return $query->where('is_frozen', true);
    }

    /**
     * نطاق للأرصدة المنتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * نطاق للأرصدة قريبة الانتهاء
     */
    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->whereBetween('expires_at', [now(), now()->addDays($days)]);
    }

    /**
     * نطاق للأرصدة التي تحتاج ترحيل
     */
    public function scopeNeedingCarryForward($query)
    {
        return $query->where('auto_carry_forward', true)
                    ->where('remaining_days', '>', 0)
                    ->where('expires_at', '<=', now());
    }
}
