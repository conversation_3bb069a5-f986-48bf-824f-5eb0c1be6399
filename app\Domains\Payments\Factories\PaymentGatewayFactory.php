<?php

namespace App\Domains\Payments\Factories;

use App\Domains\Payments\Models\PaymentGateway;
use App\Domains\Payments\Contracts\PaymentGatewayInterface;
use App\Domains\Payments\Drivers\Stripe\StripeDriver;
use App\Domains\Payments\Drivers\PayPal\PayPalDriver;
use App\Domains\Payments\Drivers\Mada\MadaDriver;
use App\Domains\Payments\Drivers\Fawry\FawryDriver;
use App\Domains\Payments\Drivers\CMI\CMIDriver;
use App\Domains\Payments\Drivers\HyperPay\HyperPayDriver;
use App\Domains\Payments\Drivers\TapPayments\TapPaymentsDriver;
use App\Domains\Payments\Drivers\PayTabs\PayTabsDriver;
use App\Domains\Payments\Drivers\Telr\TelrDriver;
use App\Domains\Payments\Drivers\PayBy\PayByDriver;
use App\Domains\Payments\Drivers\PayMob\PayMobDriver;
use App\Domains\Payments\Drivers\InstaPay\InstaPayDriver;
use App\Domains\Payments\Drivers\STCPay\STCPayDriver;
use App\Domains\Payments\Drivers\OrangeMoney\OrangeMoneyDriver;
use App\Domains\Payments\Drivers\VodafoneCash\VodafoneCashDriver;
use App\Domains\Payments\Drivers\Wafacash\WafacashDriver;
use App\Domains\Payments\Drivers\BaridCash\BaridCashDriver;
use App\Domains\Payments\Drivers\EmiratesWallet\EmiratesWalletDriver;
use App\Domains\Payments\Drivers\CareemPay\CareemPayDriver;
use App\Domains\Payments\Drivers\SWIFT\SWIFTDriver;
use App\Domains\Payments\Drivers\SARIE\SARIEDriver;
use App\Domains\Payments\Drivers\SADAD\SADADDriver;
use App\Domains\Payments\Drivers\UAEFTS\UAEFTSDriver;
use App\Domains\Payments\Drivers\MEEM\MEEMDriver;
use App\Domains\Payments\Drivers\MICE\MICEDriver;

/**
 * مصنع بوابات الدفع
 * ينشئ معالجات بوابات الدفع المختلفة
 */
class PaymentGatewayFactory
{
    /**
     * خريطة المعالجات
     */
    const DRIVER_MAP = [
        // بوابات عالمية
        'stripe' => StripeDriver::class,
        'paypal' => PayPalDriver::class,
        
        // السعودية
        'mada' => MadaDriver::class,
        'hyperpay' => HyperPayDriver::class,
        'tap_payments' => TapPaymentsDriver::class,
        'paytabs' => PayTabsDriver::class,
        'stc_pay' => STCPayDriver::class,
        'sarie' => SARIEDriver::class,
        'sadad' => SADADDriver::class,
        
        // الإمارات
        'telr' => TelrDriver::class,
        'payby' => PayByDriver::class,
        'emirates_wallet' => EmiratesWalletDriver::class,
        'careem_pay' => CareemPayDriver::class,
        'uaefts' => UAEFTSDriver::class,
        
        // مصر
        'fawry' => FawryDriver::class,
        'paymob' => PayMobDriver::class,
        'instapay' => InstaPayDriver::class,
        'vodafone_cash' => VodafoneCashDriver::class,
        'orange_money_eg' => OrangeMoneyDriver::class,
        'meem' => MEEMDriver::class,
        
        // المغرب
        'cmi_maroc' => CMIDriver::class,
        'wafacash' => WafacashDriver::class,
        'barid_cash' => BaridCashDriver::class,
        'orange_money_ma' => OrangeMoneyDriver::class,
        'mice' => MICEDriver::class,
        
        // تحويلات دولية
        'swift' => SWIFTDriver::class,
    ];

    /**
     * إنشاء معالج بوابة الدفع
     */
    public static function create(PaymentGateway $gateway): PaymentGatewayInterface
    {
        $driverClass = self::DRIVER_MAP[$gateway->provider] ?? null;
        
        if (!$driverClass) {
            throw new \InvalidArgumentException("معالج غير مدعوم: {$gateway->provider}");
        }
        
        if (!class_exists($driverClass)) {
            throw new \InvalidArgumentException("فئة المعالج غير موجودة: {$driverClass}");
        }
        
        return new $driverClass($gateway);
    }

    /**
     * إنشاء معالج بوابة الدفع من المزود
     */
    public static function createFromProvider(string $provider, array $config = []): PaymentGatewayInterface
    {
        $driverClass = self::DRIVER_MAP[$provider] ?? null;
        
        if (!$driverClass) {
            throw new \InvalidArgumentException("معالج غير مدعوم: {$provider}");
        }
        
        if (!class_exists($driverClass)) {
            throw new \InvalidArgumentException("فئة المعالج غير موجودة: {$driverClass}");
        }
        
        // إنشاء بوابة مؤقتة للاختبار
        $gateway = new PaymentGateway([
            'provider' => $provider,
            'configuration' => $config,
            'is_sandbox' => true,
        ]);
        
        return new $driverClass($gateway);
    }

    /**
     * الحصول على جميع المعالجات المتاحة
     */
    public static function getAvailableDrivers(): array
    {
        return array_keys(self::DRIVER_MAP);
    }

    /**
     * الحصول على المعالجات حسب الدولة
     */
    public static function getDriversByCountry(string $countryCode): array
    {
        $countryDrivers = [
            'SA' => [
                'mada', 'hyperpay', 'tap_payments', 'paytabs', 
                'stc_pay', 'sarie', 'sadad', 'stripe', 'paypal'
            ],
            'AE' => [
                'telr', 'payby', 'emirates_wallet', 'careem_pay', 
                'uaefts', 'stripe', 'paypal'
            ],
            'EG' => [
                'fawry', 'paymob', 'instapay', 'vodafone_cash', 
                'orange_money_eg', 'meem', 'stripe', 'paypal'
            ],
            'MA' => [
                'cmi_maroc', 'wafacash', 'barid_cash', 
                'orange_money_ma', 'mice', 'stripe', 'paypal'
            ],
            'KW' => ['stripe', 'paypal', 'hyperpay'],
            'QA' => ['stripe', 'paypal', 'hyperpay'],
            'BH' => ['stripe', 'paypal', 'hyperpay'],
            'OM' => ['stripe', 'paypal', 'hyperpay'],
            'JO' => ['stripe', 'paypal', 'hyperpay'],
            'TN' => ['stripe', 'paypal'],
            'DZ' => ['stripe', 'paypal'],
        ];
        
        return $countryDrivers[$countryCode] ?? ['stripe', 'paypal'];
    }

    /**
     * الحصول على المعالجات حسب نوع الدفع
     */
    public static function getDriversByPaymentMethod(string $paymentMethod): array
    {
        $methodDrivers = [
            'card' => [
                'stripe', 'paypal', 'mada', 'hyperpay', 'tap_payments', 
                'paytabs', 'telr', 'payby', 'cmi_maroc', 'paymob'
            ],
            'digital_wallet' => [
                'stc_pay', 'emirates_wallet', 'careem_pay', 'vodafone_cash', 
                'orange_money_eg', 'orange_money_ma', 'wafacash', 'barid_cash'
            ],
            'bank_transfer' => [
                'sarie', 'sadad', 'uaefts', 'meem', 'mice', 'swift', 
                'instapay', 'fawry'
            ],
            'mobile_payment' => [
                'stc_pay', 'vodafone_cash', 'orange_money_eg', 'orange_money_ma'
            ],
            'cash_on_delivery' => ['fawry', 'paymob'],
        ];
        
        return $methodDrivers[$paymentMethod] ?? [];
    }

    /**
     * الحصول على المعالجات حسب العملة
     */
    public static function getDriversByCurrency(string $currency): array
    {
        $currencyDrivers = [
            'SAR' => [
                'mada', 'hyperpay', 'tap_payments', 'paytabs', 
                'stc_pay', 'sarie', 'sadad'
            ],
            'AED' => [
                'telr', 'payby', 'emirates_wallet', 'careem_pay', 'uaefts'
            ],
            'EGP' => [
                'fawry', 'paymob', 'instapay', 'vodafone_cash', 
                'orange_money_eg', 'meem'
            ],
            'MAD' => [
                'cmi_maroc', 'wafacash', 'barid_cash', 
                'orange_money_ma', 'mice'
            ],
            'USD' => ['stripe', 'paypal', 'swift'],
            'EUR' => ['stripe', 'paypal', 'swift'],
            'GBP' => ['stripe', 'paypal', 'swift'],
        ];
        
        // إضافة المعالجات العالمية لجميع العملات
        $globalDrivers = ['stripe', 'paypal'];
        $drivers = $currencyDrivers[$currency] ?? [];
        
        return array_unique(array_merge($drivers, $globalDrivers));
    }

    /**
     * التحقق من دعم المعالج للميزة
     */
    public static function supportsFeature(string $provider, string $feature): bool
    {
        $featureSupport = [
            'stripe' => [
                'refunds', 'recurring', 'webhooks', '3d_secure', 
                'tokenization', 'multi_currency', 'subscriptions'
            ],
            'paypal' => [
                'refunds', 'recurring', 'webhooks', 'tokenization', 
                'multi_currency', 'subscriptions'
            ],
            'mada' => [
                'refunds', '3d_secure', 'tokenization'
            ],
            'hyperpay' => [
                'refunds', 'recurring', 'webhooks', '3d_secure', 
                'tokenization', 'multi_currency'
            ],
            'tap_payments' => [
                'refunds', 'recurring', 'webhooks', '3d_secure', 
                'tokenization', 'multi_currency'
            ],
            'paytabs' => [
                'refunds', 'recurring', 'webhooks', '3d_secure', 
                'tokenization', 'multi_currency'
            ],
            'fawry' => [
                'refunds', 'cash_collection', 'bill_payments'
            ],
            'stc_pay' => [
                'refunds', 'webhooks', 'qr_payments'
            ],
            'sarie' => [
                'instant_transfers', 'bulk_transfers'
            ],
            'swift' => [
                'international_transfers', 'correspondent_banking'
            ],
        ];
        
        $supportedFeatures = $featureSupport[$provider] ?? [];
        return in_array($feature, $supportedFeatures);
    }

    /**
     * الحصول على معلومات المعالج
     */
    public static function getDriverInfo(string $provider): array
    {
        $driverInfo = [
            'stripe' => [
                'name' => 'Stripe',
                'type' => 'global',
                'countries' => ['US', 'EU', 'SA', 'AE', 'EG'],
                'currencies' => ['USD', 'EUR', 'GBP', 'SAR', 'AED', 'EGP'],
                'payment_methods' => ['card', 'digital_wallet'],
                'features' => ['refunds', 'recurring', 'webhooks', '3d_secure'],
            ],
            'mada' => [
                'name' => 'Mada',
                'type' => 'local',
                'countries' => ['SA'],
                'currencies' => ['SAR'],
                'payment_methods' => ['card'],
                'features' => ['refunds', '3d_secure'],
            ],
            'fawry' => [
                'name' => 'Fawry',
                'type' => 'local',
                'countries' => ['EG'],
                'currencies' => ['EGP'],
                'payment_methods' => ['cash_on_delivery', 'digital_wallet'],
                'features' => ['refunds', 'cash_collection'],
            ],
            // يمكن إضافة المزيد...
        ];
        
        return $driverInfo[$provider] ?? [
            'name' => ucfirst($provider),
            'type' => 'unknown',
            'countries' => [],
            'currencies' => [],
            'payment_methods' => [],
            'features' => [],
        ];
    }

    /**
     * اختبار اتصال المعالج
     */
    public static function testConnection(string $provider, array $config): array
    {
        try {
            $driver = self::createFromProvider($provider, $config);
            
            // محاولة اختبار الاتصال
            $testResult = $driver->testConnection();
            
            return [
                'success' => true,
                'provider' => $provider,
                'test_result' => $testResult,
                'message' => 'تم الاتصال بنجاح',
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'provider' => $provider,
                'error' => $e->getMessage(),
                'message' => 'فشل في الاتصال',
            ];
        }
    }

    /**
     * الحصول على أفضل معالج للمعاملة
     */
    public static function getBestDriverForTransaction(array $criteria): ?string
    {
        $country = $criteria['country'] ?? 'SA';
        $currency = $criteria['currency'] ?? 'SAR';
        $paymentMethod = $criteria['payment_method'] ?? 'card';
        $amount = $criteria['amount'] ?? 0;
        
        // الحصول على المعالجات المناسبة
        $countryDrivers = self::getDriversByCountry($country);
        $currencyDrivers = self::getDriversByCurrency($currency);
        $methodDrivers = self::getDriversByPaymentMethod($paymentMethod);
        
        // العثور على التقاطع
        $availableDrivers = array_intersect($countryDrivers, $currencyDrivers, $methodDrivers);
        
        if (empty($availableDrivers)) {
            return null;
        }
        
        // ترتيب حسب الأولوية
        $priorityOrder = [
            // محلية أولاً
            'mada', 'fawry', 'stc_pay', 'sarie', 'telr', 'cmi_maroc',
            // إقليمية
            'hyperpay', 'tap_payments', 'paytabs', 'payby',
            // عالمية
            'stripe', 'paypal'
        ];
        
        foreach ($priorityOrder as $driver) {
            if (in_array($driver, $availableDrivers)) {
                return $driver;
            }
        }
        
        // إرجاع الأول المتاح
        return reset($availableDrivers);
    }

    /**
     * التحقق من صحة إعدادات المعالج
     */
    public static function validateDriverConfig(string $provider, array $config): array
    {
        $requiredFields = [
            'stripe' => ['secret_key', 'publishable_key'],
            'paypal' => ['client_id', 'client_secret'],
            'mada' => ['merchant_id', 'terminal_id', 'secret_key'],
            'fawry' => ['merchant_code', 'security_key'],
            'hyperpay' => ['entity_id', 'access_token'],
            'tap_payments' => ['secret_key', 'public_key'],
            'paytabs' => ['profile_id', 'server_key'],
            'telr' => ['merchant_id', 'authentication_key'],
            'payby' => ['app_id', 'private_key'],
        ];
        
        $required = $requiredFields[$provider] ?? ['api_key'];
        $missing = [];
        
        foreach ($required as $field) {
            if (empty($config[$field])) {
                $missing[] = $field;
            }
        }
        
        return [
            'valid' => empty($missing),
            'missing_fields' => $missing,
            'required_fields' => $required,
        ];
    }
}
