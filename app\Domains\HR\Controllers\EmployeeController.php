<?php

namespace App\Domains\HR\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\Department;
use App\Domains\HR\Models\Position;
use App\Domains\HR\Requests\StoreEmployeeRequest;
use App\Domains\HR\Requests\UpdateEmployeeRequest;
use App\Domains\HR\Resources\EmployeeResource;
use App\Domains\HR\Resources\EmployeeCollection;
use App\Domains\HR\Services\LeaveManagementService;
use App\Domains\HR\Services\PayrollCalculationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

/**
 * متحكم الموظفين
 * إدارة شاملة لبيانات الموظفين ودورة حياتهم
 */
class EmployeeController extends Controller
{
    protected LeaveManagementService $leaveService;
    protected PayrollCalculationService $payrollService;

    public function __construct(
        LeaveManagementService $leaveService,
        PayrollCalculationService $payrollService
    ) {
        $this->leaveService = $leaveService;
        $this->payrollService = $payrollService;
    }

    /**
     * عرض قائمة الموظفين
     */
    public function index(Request $request): JsonResponse
    {
        $query = Employee::with(['department', 'position', 'manager', 'user']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('employee_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // التصفية حسب القسم
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        // التصفية حسب المنصب
        if ($request->filled('position_id')) {
            $query->where('position_id', $request->position_id);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب نوع العقد
        if ($request->filled('contract_type')) {
            $query->where('contract_type', $request->contract_type);
        }

        // التصفية حسب الجنسية
        if ($request->filled('nationality')) {
            $query->where('nationality', $request->nationality);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $employees = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new EmployeeCollection($employees),
            'meta' => [
                'total' => $employees->total(),
                'per_page' => $employees->perPage(),
                'current_page' => $employees->currentPage(),
                'last_page' => $employees->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء موظف جديد
     */
    public function store(StoreEmployeeRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            // إنشاء رقم الموظف إذا لم يتم تقديمه
            if (!isset($data['employee_number'])) {
                $data['employee_number'] = $this->generateEmployeeNumber();
            }

            // رفع صورة الملف الشخصي
            if ($request->hasFile('profile_picture')) {
                $data['profile_picture'] = $request->file('profile_picture')
                    ->store('employees/profiles', 'public');
            }

            $employee = Employee::create($data);

            // إنشاء أرصدة الإجازات
            $this->leaveService->createLeaveBalancesForEmployee($employee);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الموظف بنجاح',
                'data' => new EmployeeResource($employee->load(['department', 'position', 'manager'])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الموظف',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل موظف محدد
     */
    public function show(int $id): JsonResponse
    {
        $employee = Employee::with([
            'department',
            'position',
            'manager',
            'subordinates',
            'user',
            'currentContract',
            'contracts',
            'leaveBalances.leaveType',
            'performanceReviews' => function ($query) {
                $query->latest()->limit(5);
            },
            'trainings' => function ($query) {
                $query->latest()->limit(5);
            },
        ])->findOrFail($id);

        // إضافة معلومات إضافية
        $additionalData = [
            'leave_balances' => $this->leaveService->getEmployeeLeaveBalance($employee),
            'expiring_documents' => $employee->getExpiringDocuments(),
            'years_of_service' => $employee->years_of_service,
            'age' => $employee->age,
            'gross_salary' => $employee->calculateGrossSalary(),
            'net_salary' => $employee->calculateNetSalary(),
        ];

        return response()->json([
            'success' => true,
            'data' => new EmployeeResource($employee),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث بيانات الموظف
     */
    public function update(UpdateEmployeeRequest $request, int $id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $employee = Employee::findOrFail($id);
            $data = $request->validated();

            // رفع صورة الملف الشخصي الجديدة
            if ($request->hasFile('profile_picture')) {
                // حذف الصورة القديمة
                if ($employee->profile_picture) {
                    Storage::disk('public')->delete($employee->profile_picture);
                }

                $data['profile_picture'] = $request->file('profile_picture')
                    ->store('employees/profiles', 'public');
            }

            $employee->update($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث بيانات الموظف بنجاح',
                'data' => new EmployeeResource($employee->load(['department', 'position', 'manager'])),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث بيانات الموظف',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف الموظف (حذف ناعم)
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $employee = Employee::findOrFail($id);
            
            // التحقق من إمكانية الحذف
            if ($employee->subordinates()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الموظف لأن لديه مرؤوسين',
                ], 422);
            }

            $employee->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الموظف بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الموظف',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إنهاء خدمة الموظف
     */
    public function terminate(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'termination_reason' => 'required|string|max:500',
            'termination_date' => 'nullable|date',
            'rehire_eligible' => 'boolean',
        ]);

        DB::beginTransaction();

        try {
            $employee = Employee::findOrFail($id);
            
            $terminationDate = $request->termination_date ? 
                \Carbon\Carbon::parse($request->termination_date) : now();

            $employee->terminate(
                $request->termination_reason,
                $terminationDate
            );

            if ($request->has('rehire_eligible')) {
                $employee->update(['rehire_eligible' => $request->rehire_eligible]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنهاء خدمة الموظف بنجاح',
                'data' => new EmployeeResource($employee),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنهاء خدمة الموظف',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إعادة تفعيل الموظف
     */
    public function reactivate(int $id): JsonResponse
    {
        try {
            $employee = Employee::findOrFail($id);
            
            if (!$employee->reactivate()) {
                return response()->json([
                    'success' => false,
                    'message' => 'الموظف غير مؤهل لإعادة التفعيل',
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إعادة تفعيل الموظف بنجاح',
                'data' => new EmployeeResource($employee),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة تفعيل الموظف',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على أرصدة إجازات الموظف
     */
    public function getLeaveBalances(int $id): JsonResponse
    {
        $employee = Employee::findOrFail($id);
        $balances = $this->leaveService->getEmployeeLeaveBalance($employee);

        return response()->json([
            'success' => true,
            'data' => $balances,
        ]);
    }

    /**
     * الحصول على كشف راتب الموظف
     */
    public function getPayslip(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'month' => 'required|integer|between:1,12',
            'year' => 'required|integer|min:2020',
        ]);

        $employee = Employee::findOrFail($id);
        
        $payslip = $this->payrollService->generatePayslip(
            $employee,
            $request->month,
            $request->year
        );

        return response()->json([
            'success' => true,
            'data' => $payslip,
        ]);
    }

    /**
     * الحصول على الهيكل التنظيمي للموظف
     */
    public function getOrganizationChart(int $id): JsonResponse
    {
        $employee = Employee::with(['manager', 'subordinates.subordinates'])
                           ->findOrFail($id);

        $chart = $this->buildOrganizationChart($employee);

        return response()->json([
            'success' => true,
            'data' => $chart,
        ]);
    }

    /**
     * الحصول على إحصائيات الموظفين
     */
    public function getStatistics(): JsonResponse
    {
        $statistics = [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::active()->count(),
            'on_probation' => Employee::onProbation()->count(),
            'probation_expired' => Employee::probationExpired()->count(),
            'expiring_documents' => Employee::withExpiringDocuments()->count(),
            'by_department' => Employee::select('department_id')
                ->with('department:id,name')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->department->name ?? 'غير محدد' => $item->count];
                }),
            'by_contract_type' => Employee::select('contract_type')
                ->groupBy('contract_type')
                ->selectRaw('contract_type, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [Employee::CONTRACT_TYPES[$item->contract_type] ?? $item->contract_type => $item->count];
                }),
            'by_nationality' => Employee::select('nationality')
                ->groupBy('nationality')
                ->selectRaw('nationality, count(*) as count')
                ->orderByDesc('count')
                ->limit(10)
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->nationality => $item->count];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * إنشاء رقم الموظف
     */
    protected function generateEmployeeNumber(): string
    {
        $year = now()->year;
        $lastEmployee = Employee::whereYear('created_at', $year)
                              ->orderBy('id', 'desc')
                              ->first();

        $sequence = $lastEmployee ? (int) substr($lastEmployee->employee_number, -4) + 1 : 1;

        return "EMP-{$year}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * بناء الهيكل التنظيمي
     */
    protected function buildOrganizationChart(Employee $employee): array
    {
        return [
            'id' => $employee->id,
            'name' => $employee->full_name,
            'position' => $employee->position->name ?? null,
            'department' => $employee->department->name ?? null,
            'manager' => $employee->manager ? [
                'id' => $employee->manager->id,
                'name' => $employee->manager->full_name,
                'position' => $employee->manager->position->name ?? null,
            ] : null,
            'subordinates' => $employee->subordinates->map(function ($subordinate) {
                return [
                    'id' => $subordinate->id,
                    'name' => $subordinate->full_name,
                    'position' => $subordinate->position->name ?? null,
                    'subordinates_count' => $subordinate->subordinates->count(),
                ];
            }),
        ];
    }
}
