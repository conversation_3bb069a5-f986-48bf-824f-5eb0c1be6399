<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج توقع التدفق النقدي
 * يحفظ توقعات التدفق النقدي المستقبلي
 */
class CashFlowForecast extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'forecast_name',
        'forecast_date',
        'period_start',
        'period_end',
        'forecast_type',
        'opening_balance',
        'closing_balance',
        'net_cash_flow',
        'operating_cash_flow',
        'investing_cash_flow',
        'financing_cash_flow',
        'confidence_level',
        'scenario_type',
        'model_used',
        'parameters',
        'actual_vs_forecast',
        'accuracy_score',
        'notes',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'forecast_date' => 'date',
        'period_start' => 'date',
        'period_end' => 'date',
        'opening_balance' => 'decimal:2',
        'closing_balance' => 'decimal:2',
        'net_cash_flow' => 'decimal:2',
        'operating_cash_flow' => 'decimal:2',
        'investing_cash_flow' => 'decimal:2',
        'financing_cash_flow' => 'decimal:2',
        'confidence_level' => 'decimal:2',
        'accuracy_score' => 'decimal:2',
        'parameters' => 'array',
        'actual_vs_forecast' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * حساب دقة التوقع
     */
    public function calculateAccuracy(): float
    {
        if (!$this->actual_vs_forecast) {
            return 0;
        }

        $totalError = 0;
        $count = 0;

        foreach ($this->actual_vs_forecast as $period => $data) {
            if (isset($data['actual']) && isset($data['forecast'])) {
                $error = abs($data['actual'] - $data['forecast']);
                $percentage = $data['forecast'] > 0 ? ($error / $data['forecast']) * 100 : 0;
                $totalError += $percentage;
                $count++;
            }
        }

        $averageError = $count > 0 ? $totalError / $count : 100;
        return max(0, 100 - $averageError);
    }

    /**
     * تحديث النتائج الفعلية
     */
    public function updateActualResults(array $actualData): void
    {
        $comparison = $this->actual_vs_forecast ?? [];
        
        foreach ($actualData as $period => $actual) {
            $comparison[$period] = array_merge(
                $comparison[$period] ?? [],
                ['actual' => $actual]
            );
        }

        $this->update([
            'actual_vs_forecast' => $comparison,
            'accuracy_score' => $this->calculateAccuracy(),
        ]);
    }

    /**
     * الحصول على الانحراف
     */
    public function getVarianceAttribute(): array
    {
        $variance = [];
        
        if ($this->actual_vs_forecast) {
            foreach ($this->actual_vs_forecast as $period => $data) {
                if (isset($data['actual']) && isset($data['forecast'])) {
                    $variance[$period] = [
                        'absolute' => $data['actual'] - $data['forecast'],
                        'percentage' => $data['forecast'] > 0 ? 
                            (($data['actual'] - $data['forecast']) / $data['forecast']) * 100 : 0,
                    ];
                }
            }
        }

        return $variance;
    }

    /**
     * التحقق من دقة التوقع
     */
    public function isAccurate(float $threshold = 85): bool
    {
        return $this->accuracy_score >= $threshold;
    }

    /**
     * الحصول على مستوى الثقة النصي
     */
    public function getConfidenceLevelTextAttribute(): string
    {
        if ($this->confidence_level >= 90) {
            return 'عالي جداً';
        } elseif ($this->confidence_level >= 80) {
            return 'عالي';
        } elseif ($this->confidence_level >= 70) {
            return 'متوسط';
        } elseif ($this->confidence_level >= 60) {
            return 'منخفض';
        } else {
            return 'منخفض جداً';
        }
    }

    /**
     * إنشاء توقع جديد
     */
    public static function createForecast(array $forecastData): self
    {
        return self::create([
            'forecast_name' => $forecastData['name'],
            'forecast_date' => now(),
            'period_start' => $forecastData['period_start'],
            'period_end' => $forecastData['period_end'],
            'forecast_type' => $forecastData['type'] ?? 'MONTHLY',
            'opening_balance' => $forecastData['opening_balance'],
            'closing_balance' => $forecastData['closing_balance'],
            'net_cash_flow' => $forecastData['net_cash_flow'],
            'operating_cash_flow' => $forecastData['operating_cash_flow'] ?? 0,
            'investing_cash_flow' => $forecastData['investing_cash_flow'] ?? 0,
            'financing_cash_flow' => $forecastData['financing_cash_flow'] ?? 0,
            'confidence_level' => $forecastData['confidence_level'] ?? 75,
            'scenario_type' => $forecastData['scenario_type'] ?? 'MOST_LIKELY',
            'model_used' => $forecastData['model_used'] ?? 'ARIMA',
            'parameters' => $forecastData['parameters'] ?? [],
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * البحث في التوقعات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('forecast_name', 'LIKE', "%{$search}%")
              ->orWhere('forecast_type', 'LIKE', "%{$search}%")
              ->orWhere('scenario_type', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('forecast_type', $type);
    }

    /**
     * فلترة حسب السيناريو
     */
    public function scopeOfScenario($query, string $scenario)
    {
        return $query->where('scenario_type', $scenario);
    }

    /**
     * فلترة حسب الفترة
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('period_start', [$startDate, $endDate])
              ->orWhereBetween('period_end', [$startDate, $endDate]);
        });
    }

    /**
     * فلترة التوقعات الدقيقة
     */
    public function scopeAccurate($query, float $threshold = 85)
    {
        return $query->where('accuracy_score', '>=', $threshold);
    }

    /**
     * ترتيب حسب الدقة
     */
    public function scopeOrderByAccuracy($query, string $direction = 'desc')
    {
        return $query->orderBy('accuracy_score', $direction);
    }

    /**
     * ترتيب حسب الثقة
     */
    public function scopeOrderByConfidence($query, string $direction = 'desc')
    {
        return $query->orderBy('confidence_level', $direction);
    }
}
