<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث استقبال Webhook من منصة التجارة الإلكترونية
 */
class ECommerceWebhookReceived
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public string $eventType;
    public array $payload;
    public array $headers;
    public string $signature;
    public \DateTime $receivedAt;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceIntegration $integration,
        string $eventType,
        array $payload,
        array $headers = [],
        string $signature = ''
    ) {
        $this->integration = $integration;
        $this->eventType = $eventType;
        $this->payload = $payload;
        $this->headers = $headers;
        $this->signature = $signature;
        $this->receivedAt = new \DateTime();
    }

    /**
     * الحصول على معرف فريد للحدث
     */
    public function getEventId(): string
    {
        return md5(
            $this->integration->id . 
            $this->eventType . 
            json_encode($this->payload) . 
            $this->receivedAt->getTimestamp()
        );
    }

    /**
     * الحصول على اسم المنصة
     */
    public function getPlatformName(): string
    {
        return $this->integration->platform->name;
    }

    /**
     * الحصول على slug المنصة
     */
    public function getPlatformSlug(): string
    {
        return $this->integration->platform->slug;
    }

    /**
     * التحقق من صحة التوقيع
     */
    public function isSignatureValid(): bool
    {
        if (empty($this->signature)) {
            return true; // بعض المنصات لا تستخدم توقيع
        }

        $webhookSecret = $this->integration->webhook_config['secret'] ?? '';
        if (empty($webhookSecret)) {
            return false;
        }

        // التحقق حسب نوع المنصة
        return match ($this->getPlatformSlug()) {
            'shopify' => $this->verifyShopifySignature($webhookSecret),
            'woocommerce' => $this->verifyWooCommerceSignature($webhookSecret),
            'salla' => $this->verifySallaSignature($webhookSecret),
            'zid' => $this->verifyZidSignature($webhookSecret),
            default => $this->verifyGenericSignature($webhookSecret),
        };
    }

    /**
     * التحقق من توقيع Shopify
     */
    protected function verifyShopifySignature(string $secret): bool
    {
        $calculatedSignature = base64_encode(hash_hmac('sha256', json_encode($this->payload), $secret, true));
        return hash_equals($calculatedSignature, $this->signature);
    }

    /**
     * التحقق من توقيع WooCommerce
     */
    protected function verifyWooCommerceSignature(string $secret): bool
    {
        $calculatedSignature = hash_hmac('sha256', json_encode($this->payload), $secret);
        return hash_equals($calculatedSignature, $this->signature);
    }

    /**
     * التحقق من توقيع Salla
     */
    protected function verifySallaSignature(string $secret): bool
    {
        $calculatedSignature = hash_hmac('sha256', json_encode($this->payload), $secret);
        return hash_equals($calculatedSignature, $this->signature);
    }

    /**
     * التحقق من توقيع Zid
     */
    protected function verifyZidSignature(string $secret): bool
    {
        $calculatedSignature = hash_hmac('sha256', json_encode($this->payload), $secret);
        return hash_equals($calculatedSignature, $this->signature);
    }

    /**
     * التحقق من التوقيع العام
     */
    protected function verifyGenericSignature(string $secret): bool
    {
        $calculatedSignature = hash_hmac('sha256', json_encode($this->payload), $secret);
        return hash_equals($calculatedSignature, $this->signature);
    }

    /**
     * الحصول على نوع الكيان من الحدث
     */
    public function getEntityType(): ?string
    {
        // استخراج نوع الكيان من نوع الحدث
        if (str_contains($this->eventType, 'product')) {
            return 'product';
        }
        
        if (str_contains($this->eventType, 'order')) {
            return 'order';
        }
        
        if (str_contains($this->eventType, 'customer')) {
            return 'customer';
        }
        
        if (str_contains($this->eventType, 'category')) {
            return 'category';
        }
        
        if (str_contains($this->eventType, 'inventory')) {
            return 'inventory';
        }

        return null;
    }

    /**
     * الحصول على نوع العملية من الحدث
     */
    public function getOperationType(): ?string
    {
        if (str_contains($this->eventType, 'create')) {
            return 'create';
        }
        
        if (str_contains($this->eventType, 'update')) {
            return 'update';
        }
        
        if (str_contains($this->eventType, 'delete')) {
            return 'delete';
        }

        return 'unknown';
    }

    /**
     * الحصول على معرف الكيان من البيانات
     */
    public function getEntityId(): ?string
    {
        // محاولة استخراج المعرف من البيانات
        $possibleIdFields = ['id', 'entity_id', 'product_id', 'order_id', 'customer_id'];
        
        foreach ($possibleIdFields as $field) {
            if (isset($this->payload[$field])) {
                return (string) $this->payload[$field];
            }
        }

        // البحث في البيانات المتداخلة
        if (isset($this->payload['data'])) {
            foreach ($possibleIdFields as $field) {
                if (isset($this->payload['data'][$field])) {
                    return (string) $this->payload['data'][$field];
                }
            }
        }

        return null;
    }

    /**
     * التحقق من أن الحدث يجب معالجته
     */
    public function shouldProcess(): bool
    {
        // التحقق من أن التكامل نشط
        if ($this->integration->status !== 'active') {
            return false;
        }

        // التحقق من أن Webhooks مفعلة
        if (!($this->integration->webhook_config['enabled'] ?? false)) {
            return false;
        }

        // التحقق من أن نوع الحدث مدعوم
        $supportedEvents = $this->integration->webhook_config['events'] ?? [];
        if (!empty($supportedEvents) && !in_array($this->eventType, $supportedEvents)) {
            return false;
        }

        return true;
    }

    /**
     * تحويل الحدث إلى مصفوفة
     */
    public function toArray(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'platform' => $this->getPlatformSlug(),
            'event_type' => $this->eventType,
            'entity_type' => $this->getEntityType(),
            'operation_type' => $this->getOperationType(),
            'entity_id' => $this->getEntityId(),
            'payload' => $this->payload,
            'headers' => $this->headers,
            'signature' => $this->signature,
            'received_at' => $this->receivedAt->format('Y-m-d H:i:s'),
            'event_id' => $this->getEventId(),
        ];
    }
}
