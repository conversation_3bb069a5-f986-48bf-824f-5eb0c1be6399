<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج قالب المشروع - Project Template
 * يدير القوالب المعدة مسبقاً لإنشاء المشاريع
 */
class ProjectTemplate extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'type',
        'category',
        'is_public',
        'is_active',
        'default_duration_days',
        'default_methodology',
        'template_structure',
        'default_settings',
        'required_roles',
        'estimated_budget_range',
        'complexity_level',
        'industry',
        'tags',
        'usage_count',
        'rating',
        'last_used_at',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'default_duration_days' => 'integer',
        'template_structure' => 'array',
        'default_settings' => 'array',
        'required_roles' => 'array',
        'estimated_budget_range' => 'array',
        'tags' => 'array',
        'usage_count' => 'integer',
        'rating' => 'decimal:2',
        'last_used_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع قوالب المهام
     */
    public function taskTemplates(): HasMany
    {
        return $this->hasMany(TaskTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع قوالب المعالم
     */
    public function milestoneTemplates(): HasMany
    {
        return $this->hasMany(MilestoneTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع قوالب سير العمل
     */
    public function workflowTemplates(): HasMany
    {
        return $this->hasMany(WorkflowTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع قواعد الأتمتة
     */
    public function automationRules(): HasMany
    {
        return $this->hasMany(AutomationRule::class, 'template_id');
    }

    /**
     * العلاقة مع المشاريع المنشأة من هذا القالب
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'template_id');
    }

    /**
     * الحصول على مستوى الشعبية
     */
    public function getPopularityLevelAttribute(): string
    {
        return match (true) {
            $this->usage_count >= 100 => 'VERY_HIGH',
            $this->usage_count >= 50 => 'HIGH',
            $this->usage_count >= 20 => 'MEDIUM',
            $this->usage_count >= 5 => 'LOW',
            default => 'VERY_LOW',
        };
    }

    /**
     * الحصول على تقييم النجوم
     */
    public function getStarRatingAttribute(): int
    {
        return (int) round($this->rating);
    }

    /**
     * تحديث التقييم
     */
    public function updateRating(float $newRating): void
    {
        $currentRating = $this->rating ?? 0;
        $ratingCount = $this->metadata['rating_count'] ?? 0;
        
        $totalRating = ($currentRating * $ratingCount) + $newRating;
        $newRatingCount = $ratingCount + 1;
        $averageRating = $totalRating / $newRatingCount;

        $this->update([
            'rating' => $averageRating,
            'metadata' => array_merge($this->metadata ?? [], [
                'rating_count' => $newRatingCount,
                'last_rated_at' => now(),
            ]),
        ]);
    }

    /**
     * تسجيل استخدام القالب
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * نسخ القالب
     */
    public function duplicate(string $newName, int $createdBy): self
    {
        $newTemplate = self::create([
            'name' => $newName,
            'description' => $this->description . ' (نسخة)',
            'type' => $this->type,
            'category' => $this->category,
            'is_public' => false,
            'is_active' => true,
            'default_duration_days' => $this->default_duration_days,
            'default_methodology' => $this->default_methodology,
            'template_structure' => $this->template_structure,
            'default_settings' => $this->default_settings,
            'required_roles' => $this->required_roles,
            'estimated_budget_range' => $this->estimated_budget_range,
            'complexity_level' => $this->complexity_level,
            'industry' => $this->industry,
            'tags' => $this->tags,
            'created_by' => $createdBy,
        ]);

        // نسخ قوالب المهام
        foreach ($this->taskTemplates as $taskTemplate) {
            $taskTemplate->duplicate($newTemplate->id);
        }

        // نسخ قوالب المعالم
        foreach ($this->milestoneTemplates as $milestoneTemplate) {
            $milestoneTemplate->duplicate($newTemplate->id);
        }

        return $newTemplate;
    }

    /**
     * تصدير القالب
     */
    public function export(): array
    {
        return [
            'template' => $this->toArray(),
            'task_templates' => $this->taskTemplates->toArray(),
            'milestone_templates' => $this->milestoneTemplates->toArray(),
            'workflow_templates' => $this->workflowTemplates->toArray(),
            'automation_rules' => $this->automationRules->toArray(),
            'exported_at' => now(),
            'version' => '1.0',
        ];
    }

    /**
     * البحث في القوالب
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhereJsonContains('tags', $search);
        });
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * فلترة القوالب العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * فلترة القوالب النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب الصناعة
     */
    public function scopeForIndustry($query, string $industry)
    {
        return $query->where('industry', $industry);
    }

    /**
     * فلترة حسب مستوى التعقيد
     */
    public function scopeWithComplexity($query, string $complexity)
    {
        return $query->where('complexity_level', $complexity);
    }

    /**
     * ترتيب حسب الشعبية
     */
    public function scopeOrderByPopularity($query, string $direction = 'desc')
    {
        return $query->orderBy('usage_count', $direction);
    }

    /**
     * ترتيب حسب التقييم
     */
    public function scopeOrderByRating($query, string $direction = 'desc')
    {
        return $query->orderBy('rating', $direction);
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * فلترة الأكثر استخداماً
     */
    public function scopeMostUsed($query, int $limit = 10)
    {
        return $query->orderBy('usage_count', 'desc')->limit($limit);
    }

    /**
     * فلترة الأعلى تقييماً
     */
    public function scopeTopRated($query, int $limit = 10)
    {
        return $query->orderBy('rating', 'desc')->limit($limit);
    }
}
