<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\Department;
use App\Domains\HR\Models\LeaveRequest;
use App\Domains\HR\Models\AttendanceRecord;
use App\Domains\HR\Models\PerformanceReview;
use App\Domains\HR\Models\JobApplication;
use App\Domains\HR\Models\Payslip;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * خدمة التحليلات والذكاء الاصطناعي للموارد البشرية
 * مؤشرات الأداء، التحليلات التنبؤية، مساعد HR ذكي
 */
class HRAnalyticsService
{
    /**
     * الحصول على مؤشرات الأداء الرئيسية
     */
    public function getHRKPIs(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfYear();
        $endDate = $filters['end_date'] ?? now();
        $departmentId = $filters['department_id'] ?? null;

        return [
            'workforce_metrics' => $this->getWorkforceMetrics($departmentId),
            'turnover_metrics' => $this->getTurnoverMetrics($startDate, $endDate, $departmentId),
            'recruitment_metrics' => $this->getRecruitmentMetrics($startDate, $endDate),
            'attendance_metrics' => $this->getAttendanceMetrics($startDate, $endDate, $departmentId),
            'performance_metrics' => $this->getPerformanceMetrics($startDate, $endDate, $departmentId),
            'cost_metrics' => $this->getCostMetrics($startDate, $endDate, $departmentId),
            'satisfaction_metrics' => $this->getSatisfactionMetrics($startDate, $endDate, $departmentId),
        ];
    }

    /**
     * مؤشرات القوى العاملة
     */
    protected function getWorkforceMetrics(?int $departmentId = null): array
    {
        $query = Employee::query();
        
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $totalEmployees = $query->count();
        $activeEmployees = $query->active()->count();
        $maleEmployees = $query->active()->where('gender', 'MALE')->count();
        $femaleEmployees = $query->active()->where('gender', 'FEMALE')->count();

        // التوزيع حسب العمر
        $ageDistribution = $query->active()
            ->selectRaw('
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 25 THEN "تحت 25"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 25 AND 34 THEN "25-34"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 35 AND 44 THEN "35-44"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 45 AND 54 THEN "45-54"
                    ELSE "55+"
                END as age_group,
                COUNT(*) as count
            ')
            ->groupBy('age_group')
            ->pluck('count', 'age_group')
            ->toArray();

        // التوزيع حسب سنوات الخدمة
        $serviceDistribution = $query->active()
            ->selectRaw('
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, hire_date, CURDATE()) < 1 THEN "أقل من سنة"
                    WHEN TIMESTAMPDIFF(YEAR, hire_date, CURDATE()) BETWEEN 1 AND 2 THEN "1-2 سنة"
                    WHEN TIMESTAMPDIFF(YEAR, hire_date, CURDATE()) BETWEEN 3 AND 5 THEN "3-5 سنوات"
                    WHEN TIMESTAMPDIFF(YEAR, hire_date, CURDATE()) BETWEEN 6 AND 10 THEN "6-10 سنوات"
                    ELSE "أكثر من 10 سنوات"
                END as service_group,
                COUNT(*) as count
            ')
            ->groupBy('service_group')
            ->pluck('count', 'service_group')
            ->toArray();

        return [
            'total_employees' => $totalEmployees,
            'active_employees' => $activeEmployees,
            'gender_distribution' => [
                'male' => $maleEmployees,
                'female' => $femaleEmployees,
                'male_percentage' => $activeEmployees > 0 ? ($maleEmployees / $activeEmployees) * 100 : 0,
                'female_percentage' => $activeEmployees > 0 ? ($femaleEmployees / $activeEmployees) * 100 : 0,
            ],
            'age_distribution' => $ageDistribution,
            'service_distribution' => $serviceDistribution,
            'headcount_by_department' => $this->getHeadcountByDepartment(),
        ];
    }

    /**
     * مؤشرات معدل الدوران الوظيفي
     */
    protected function getTurnoverMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId = null): array
    {
        $query = Employee::query();
        
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        // الموظفين المنتهية خدمتهم في الفترة
        $terminatedEmployees = $query->clone()
            ->whereIn('status', ['TERMINATED', 'RESIGNED'])
            ->whereBetween('termination_date', [$startDate, $endDate])
            ->count();

        // متوسط عدد الموظفين في الفترة
        $averageEmployees = $query->clone()->active()->count();

        // معدل الدوران السنوي
        $annualTurnoverRate = $averageEmployees > 0 ? 
            ($terminatedEmployees / $averageEmployees) * 100 : 0;

        // تحليل أسباب المغادرة
        $terminationReasons = $query->clone()
            ->whereIn('status', ['TERMINATED', 'RESIGNED'])
            ->whereBetween('termination_date', [$startDate, $endDate])
            ->groupBy('termination_reason')
            ->selectRaw('termination_reason, COUNT(*) as count')
            ->pluck('count', 'termination_reason')
            ->toArray();

        // الموظفين الجدد
        $newHires = $query->clone()
            ->whereBetween('hire_date', [$startDate, $endDate])
            ->count();

        return [
            'terminated_employees' => $terminatedEmployees,
            'new_hires' => $newHires,
            'net_change' => $newHires - $terminatedEmployees,
            'turnover_rate' => round($annualTurnoverRate, 2),
            'termination_reasons' => $terminationReasons,
            'voluntary_vs_involuntary' => $this->getVoluntaryVsInvoluntaryTurnover($startDate, $endDate, $departmentId),
        ];
    }

    /**
     * مؤشرات التوظيف
     */
    protected function getRecruitmentMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        $totalApplications = JobApplication::whereBetween('applied_at', [$startDate, $endDate])->count();
        $hiredCandidates = JobApplication::whereBetween('applied_at', [$startDate, $endDate])
            ->where('status', 'HIRED')->count();

        $averageTimeToHire = JobApplication::whereBetween('applied_at', [$startDate, $endDate])
            ->where('status', 'HIRED')
            ->whereNotNull('start_date')
            ->selectRaw('AVG(DATEDIFF(start_date, applied_at)) as avg_days')
            ->value('avg_days') ?? 0;

        $costPerHire = $this->calculateCostPerHire($startDate, $endDate);

        // مصادر التوظيف
        $sourcesEffectiveness = JobApplication::whereBetween('applied_at', [$startDate, $endDate])
            ->groupBy('application_source')
            ->selectRaw('
                application_source,
                COUNT(*) as total_applications,
                SUM(CASE WHEN status = "HIRED" THEN 1 ELSE 0 END) as hired_count,
                (SUM(CASE WHEN status = "HIRED" THEN 1 ELSE 0 END) / COUNT(*)) * 100 as conversion_rate
            ')
            ->get()
            ->keyBy('application_source')
            ->toArray();

        return [
            'total_applications' => $totalApplications,
            'hired_candidates' => $hiredCandidates,
            'conversion_rate' => $totalApplications > 0 ? ($hiredCandidates / $totalApplications) * 100 : 0,
            'average_time_to_hire' => round($averageTimeToHire, 1),
            'cost_per_hire' => $costPerHire,
            'sources_effectiveness' => $sourcesEffectiveness,
            'quality_of_hire' => $this->calculateQualityOfHire($startDate, $endDate),
        ];
    }

    /**
     * مؤشرات الحضور
     */
    protected function getAttendanceMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId = null): array
    {
        $query = AttendanceRecord::whereBetween('date', [$startDate, $endDate]);
        
        if ($departmentId) {
            $query->whereHas('employee', function ($q) use ($departmentId) {
                $q->where('department_id', $departmentId);
            });
        }

        $totalRecords = $query->count();
        $presentDays = $query->clone()->where('status', 'PRESENT')->count();
        $lateDays = $query->clone()->where('is_late', true)->count();
        $absentDays = $query->clone()->where('status', 'ABSENT')->count();
        $overtimeHours = $query->clone()->sum('overtime_hours');

        $attendanceRate = $totalRecords > 0 ? ($presentDays / $totalRecords) * 100 : 0;
        $punctualityRate = $presentDays > 0 ? (($presentDays - $lateDays) / $presentDays) * 100 : 0;

        return [
            'attendance_rate' => round($attendanceRate, 2),
            'punctuality_rate' => round($punctualityRate, 2),
            'absenteeism_rate' => $totalRecords > 0 ? ($absentDays / $totalRecords) * 100 : 0,
            'total_overtime_hours' => $overtimeHours,
            'average_overtime_per_employee' => $this->getAverageOvertimePerEmployee($startDate, $endDate, $departmentId),
            'attendance_trends' => $this->getAttendanceTrends($startDate, $endDate, $departmentId),
        ];
    }

    /**
     * مؤشرات الأداء
     */
    protected function getPerformanceMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId = null): array
    {
        $query = PerformanceReview::whereBetween('completed_date', [$startDate, $endDate])
            ->where('status', 'COMPLETED');
        
        if ($departmentId) {
            $query->whereHas('employee', function ($q) use ($departmentId) {
                $q->where('department_id', $departmentId);
            });
        }

        $averageScore = $query->avg('overall_score') ?? 0;
        $totalReviews = $query->count();

        // توزيع النقاط
        $scoreDistribution = $query->selectRaw('
            CASE 
                WHEN overall_score >= 4.5 THEN "ممتاز (4.5-5.0)"
                WHEN overall_score >= 3.5 THEN "جيد جداً (3.5-4.4)"
                WHEN overall_score >= 2.5 THEN "جيد (2.5-3.4)"
                WHEN overall_score >= 1.5 THEN "مقبول (1.5-2.4)"
                ELSE "ضعيف (أقل من 1.5)"
            END as score_range,
            COUNT(*) as count
        ')
        ->groupBy('score_range')
        ->pluck('count', 'score_range')
        ->toArray();

        return [
            'average_performance_score' => round($averageScore, 2),
            'total_reviews_completed' => $totalReviews,
            'score_distribution' => $scoreDistribution,
            'high_performers_percentage' => $this->getHighPerformersPercentage($startDate, $endDate, $departmentId),
            'improvement_needed_percentage' => $this->getImprovementNeededPercentage($startDate, $endDate, $departmentId),
        ];
    }

    /**
     * مؤشرات التكلفة
     */
    protected function getCostMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId = null): array
    {
        $query = Payslip::whereBetween('pay_period_start', [$startDate, $endDate])
            ->where('status', 'FINALIZED');
        
        if ($departmentId) {
            $query->whereHas('employee', function ($q) use ($departmentId) {
                $q->where('department_id', $departmentId);
            });
        }

        $totalPayrollCost = $query->sum('gross_salary');
        $totalEmployees = $query->distinct('employee_id')->count();
        $averageSalary = $totalEmployees > 0 ? $totalPayrollCost / $totalEmployees : 0;

        // تكلفة الفوائد والمزايا
        $benefitsCost = $query->sum('total_benefits');
        
        // تكلفة التدريب
        $trainingCost = $this->calculateTrainingCost($startDate, $endDate, $departmentId);
        
        // تكلفة التوظيف
        $recruitmentCost = $this->calculateRecruitmentCost($startDate, $endDate);

        return [
            'total_payroll_cost' => $totalPayrollCost,
            'average_salary' => round($averageSalary, 2),
            'benefits_cost' => $benefitsCost,
            'training_cost' => $trainingCost,
            'recruitment_cost' => $recruitmentCost,
            'total_hr_cost' => $totalPayrollCost + $benefitsCost + $trainingCost + $recruitmentCost,
            'cost_per_employee' => $totalEmployees > 0 ? 
                ($totalPayrollCost + $benefitsCost + $trainingCost) / $totalEmployees : 0,
        ];
    }

    /**
     * مؤشرات الرضا الوظيفي
     */
    protected function getSatisfactionMetrics(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId = null): array
    {
        // يمكن تطوير هذا عند إضافة نظام استطلاعات الرضا
        return [
            'employee_satisfaction_score' => 0, // eNPS
            'engagement_score' => 0,
            'retention_rate' => $this->calculateRetentionRate($startDate, $endDate, $departmentId),
            'internal_mobility_rate' => $this->calculateInternalMobilityRate($startDate, $endDate, $departmentId),
        ];
    }

    /**
     * التحليلات التنبؤية
     */
    public function getPredictiveAnalytics(): array
    {
        return [
            'attrition_risk' => $this->calculateAttritionRisk(),
            'performance_predictions' => $this->predictPerformance(),
            'hiring_forecast' => $this->forecastHiringNeeds(),
            'salary_trends' => $this->analyzeSalaryTrends(),
            'skill_gaps' => $this->identifySkillGaps(),
        ];
    }

    /**
     * حساب مخاطر المغادرة
     */
    protected function calculateAttritionRisk(): array
    {
        $employees = Employee::active()
            ->with(['performanceReviews', 'attendanceRecords', 'leaveRequests'])
            ->get();

        $riskScores = [];

        foreach ($employees as $employee) {
            $riskFactors = [];
            $totalScore = 0;

            // عامل الأداء
            $latestReview = $employee->performanceReviews()
                ->where('status', 'COMPLETED')
                ->orderBy('completed_date', 'desc')
                ->first();

            if ($latestReview && $latestReview->overall_score < 3.0) {
                $riskFactors[] = 'أداء منخفض';
                $totalScore += 30;
            }

            // عامل الحضور
            $attendanceRate = $this->getEmployeeAttendanceRate($employee);
            if ($attendanceRate < 85) {
                $riskFactors[] = 'معدل حضور منخفض';
                $totalScore += 25;
            }

            // عامل سنوات الخدمة
            if ($employee->years_of_service < 2) {
                $riskFactors[] = 'موظف جديد';
                $totalScore += 20;
            } elseif ($employee->years_of_service > 10) {
                $riskFactors[] = 'خدمة طويلة';
                $totalScore += 15;
            }

            // عامل الإجازات المتكررة
            $recentLeaves = $employee->leaveRequests()
                ->where('start_date', '>=', now()->subMonths(6))
                ->count();

            if ($recentLeaves > 3) {
                $riskFactors[] = 'إجازات متكررة';
                $totalScore += 20;
            }

            $riskLevel = match (true) {
                $totalScore >= 70 => 'HIGH',
                $totalScore >= 40 => 'MEDIUM',
                default => 'LOW',
            };

            if ($totalScore > 0) {
                $riskScores[] = [
                    'employee_id' => $employee->id,
                    'employee_name' => $employee->full_name,
                    'department' => $employee->department?->name,
                    'risk_score' => $totalScore,
                    'risk_level' => $riskLevel,
                    'risk_factors' => $riskFactors,
                ];
            }
        }

        // ترتيب حسب النقاط
        usort($riskScores, function ($a, $b) {
            return $b['risk_score'] <=> $a['risk_score'];
        });

        return array_slice($riskScores, 0, 20); // أعلى 20 موظف في المخاطر
    }

    /**
     * مساعد HR ذكي
     */
    public function getAIInsights(string $question): array
    {
        // تحليل السؤال وتقديم إجابة ذكية
        $insights = [];

        if (str_contains(strtolower($question), 'ترقية') || str_contains($question, 'promotion')) {
            $insights = $this->getPromotionRecommendations();
        } elseif (str_contains(strtolower($question), 'راتب') || str_contains($question, 'salary')) {
            $insights = $this->getSalaryInsights($question);
        } elseif (str_contains(strtolower($question), 'أداء') || str_contains($question, 'performance')) {
            $insights = $this->getPerformanceInsights();
        } elseif (str_contains(strtolower($question), 'توظيف') || str_contains($question, 'hiring')) {
            $insights = $this->getHiringInsights();
        } else {
            $insights = $this->getGeneralHRInsights();
        }

        return [
            'question' => $question,
            'insights' => $insights,
            'recommendations' => $this->generateRecommendations($insights),
            'generated_at' => now(),
        ];
    }

    /**
     * توصيات الترقية
     */
    protected function getPromotionRecommendations(): array
    {
        return Employee::active()
            ->whereHas('performanceReviews', function ($query) {
                $query->where('overall_score', '>=', 4.0)
                      ->where('promotion_readiness', 'READY');
            })
            ->with(['position', 'department', 'performanceReviews'])
            ->limit(10)
            ->get()
            ->map(function ($employee) {
                return [
                    'employee_name' => $employee->full_name,
                    'current_position' => $employee->position?->title,
                    'department' => $employee->department?->name,
                    'latest_score' => $employee->performanceReviews->first()?->overall_score,
                    'years_in_position' => $employee->years_of_service,
                    'recommendation' => 'مرشح قوي للترقية',
                ];
            })
            ->toArray();
    }

    /**
     * رؤى الرواتب
     */
    protected function getSalaryInsights(string $question): array
    {
        // استخراج المنصب من السؤال إن وجد
        $position = $this->extractPositionFromQuestion($question);
        $location = $this->extractLocationFromQuestion($question);

        $query = Payslip::where('status', 'FINALIZED')
            ->whereBetween('pay_period_start', [now()->subYear(), now()]);

        if ($position) {
            $query->whereHas('employee.position', function ($q) use ($position) {
                $q->where('title', 'LIKE', "%{$position}%");
            });
        }

        $salaryStats = $query->selectRaw('
            AVG(gross_salary) as average_salary,
            MIN(gross_salary) as min_salary,
            MAX(gross_salary) as max_salary,
            STDDEV(gross_salary) as salary_deviation
        ')->first();

        return [
            'position' => $position ?? 'جميع المناصب',
            'location' => $location ?? 'جميع المواقع',
            'average_salary' => round($salaryStats->average_salary ?? 0, 2),
            'min_salary' => round($salaryStats->min_salary ?? 0, 2),
            'max_salary' => round($salaryStats->max_salary ?? 0, 2),
            'market_analysis' => 'تحليل السوق غير متوفر حالياً',
        ];
    }

    /**
     * استخراج المنصب من السؤال
     */
    protected function extractPositionFromQuestion(string $question): ?string
    {
        $positions = ['مهندس برمجيات', 'محاسب', 'مدير', 'مطور', 'محلل', 'مصمم'];
        
        foreach ($positions as $position) {
            if (str_contains($question, $position)) {
                return $position;
            }
        }

        return null;
    }

    /**
     * استخراج الموقع من السؤال
     */
    protected function extractLocationFromQuestion(string $question): ?string
    {
        $locations = ['دبي', 'الرياض', 'الدار البيضاء', 'القاهرة', 'عمان'];
        
        foreach ($locations as $location) {
            if (str_contains($question, $location)) {
                return $location;
            }
        }

        return null;
    }

    // المزيد من الدوال المساعدة...
    
    protected function getHeadcountByDepartment(): array
    {
        return Department::withCount(['activeEmployees'])
            ->get()
            ->pluck('active_employees_count', 'name')
            ->toArray();
    }

    protected function getVoluntaryVsInvoluntaryTurnover(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate, ?int $departmentId): array
    {
        $query = Employee::whereIn('status', ['TERMINATED', 'RESIGNED'])
            ->whereBetween('termination_date', [$startDate, $endDate]);
            
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $voluntary = $query->clone()->where('status', 'RESIGNED')->count();
        $involuntary = $query->clone()->where('status', 'TERMINATED')->count();

        return [
            'voluntary' => $voluntary,
            'involuntary' => $involuntary,
            'voluntary_percentage' => ($voluntary + $involuntary) > 0 ? 
                ($voluntary / ($voluntary + $involuntary)) * 100 : 0,
        ];
    }

    protected function calculateCostPerHire(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        // تكلفة افتراضية - يمكن تطويرها لتشمل التكاليف الفعلية
        $hiredCount = JobApplication::whereBetween('applied_at', [$startDate, $endDate])
            ->where('status', 'HIRED')->count();

        $estimatedCostPerHire = 5000; // تكلفة افتراضية
        
        return $hiredCount > 0 ? $estimatedCostPerHire : 0;
    }

    protected function calculateQualityOfHire(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        // مؤشر جودة التوظيف - يمكن تطويره
        return 75.0; // نسبة افتراضية
    }

    protected function getEmployeeAttendanceRate(Employee $employee): float
    {
        $totalDays = AttendanceRecord::where('employee_id', $employee->id)
            ->where('date', '>=', now()->subMonths(3))
            ->count();

        $presentDays = AttendanceRecord::where('employee_id', $employee->id)
            ->where('date', '>=', now()->subMonths(3))
            ->where('status', 'PRESENT')
            ->count();

        return $totalDays > 0 ? ($presentDays / $totalDays) * 100 : 100;
    }

    protected function getPerformanceInsights(): array
    {
        return [
            'top_performers' => $this->getTopPerformers(),
            'improvement_needed' => $this->getEmployeesNeedingImprovement(),
            'performance_trends' => $this->getPerformanceTrends(),
        ];
    }

    protected function getTopPerformers(): array
    {
        return PerformanceReview::where('status', 'COMPLETED')
            ->where('overall_score', '>=', 4.5)
            ->with('employee')
            ->orderBy('overall_score', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($review) {
                return [
                    'name' => $review->employee->full_name,
                    'score' => $review->overall_score,
                    'department' => $review->employee->department?->name,
                ];
            })
            ->toArray();
    }

    protected function getEmployeesNeedingImprovement(): array
    {
        return PerformanceReview::where('status', 'COMPLETED')
            ->where('overall_score', '<', 2.5)
            ->with('employee')
            ->orderBy('overall_score', 'asc')
            ->limit(10)
            ->get()
            ->map(function ($review) {
                return [
                    'name' => $review->employee->full_name,
                    'score' => $review->overall_score,
                    'department' => $review->employee->department?->name,
                    'areas_for_improvement' => $review->areas_for_improvement,
                ];
            })
            ->toArray();
    }

    protected function generateRecommendations(array $insights): array
    {
        // توليد توصيات ذكية بناءً على البيانات
        return [
            'immediate_actions' => [
                'مراجعة الموظفين عالي المخاطر',
                'تحديث سياسات الحضور',
                'تطوير برامج التدريب',
            ],
            'long_term_strategies' => [
                'تحسين عملية التوظيف',
                'تطوير برامج الاحتفاظ بالمواهب',
                'تعزيز ثقافة الأداء',
            ],
        ];
    }

    // دوال إضافية يمكن تطويرها...
    protected function getAttendanceTrends($startDate, $endDate, $departmentId) { return []; }
    protected function getAverageOvertimePerEmployee($startDate, $endDate, $departmentId) { return 0; }
    protected function getHighPerformersPercentage($startDate, $endDate, $departmentId) { return 0; }
    protected function getImprovementNeededPercentage($startDate, $endDate, $departmentId) { return 0; }
    protected function calculateTrainingCost($startDate, $endDate, $departmentId) { return 0; }
    protected function calculateRecruitmentCost($startDate, $endDate) { return 0; }
    protected function calculateRetentionRate($startDate, $endDate, $departmentId) { return 0; }
    protected function calculateInternalMobilityRate($startDate, $endDate, $departmentId) { return 0; }
    protected function predictPerformance() { return []; }
    protected function forecastHiringNeeds() { return []; }
    protected function analyzeSalaryTrends() { return []; }
    protected function identifySkillGaps() { return []; }
    protected function getHiringInsights() { return []; }
    protected function getGeneralHRInsights() { return []; }
    protected function getPerformanceTrends() { return []; }
}
