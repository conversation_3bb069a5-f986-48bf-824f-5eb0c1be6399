<?php

namespace App\Domains\Projects\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Projects\Models\TimeEntry;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Services\TimeTrackingService;
use App\Domains\Projects\Requests\StoreTimeEntryRequest;
use App\Domains\Projects\Requests\UpdateTimeEntryRequest;
use App\Domains\Projects\Resources\TimeEntryResource;
use App\Domains\Projects\Resources\TimeEntryCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * متحكم تتبع الوقت
 * إدارة شاملة لتتبع الوقت والإنتاجية
 */
class TimeTrackingController extends Controller
{
    protected TimeTrackingService $timeService;

    public function __construct(TimeTrackingService $timeService)
    {
        $this->timeService = $timeService;
    }

    /**
     * عرض قائمة إدخالات الوقت
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', TimeEntry::class);

        $query = TimeEntry::with(['user', 'task', 'project']);

        // التصفية حسب المستخدم
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // التصفية حسب المشروع
        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        // التصفية حسب المهمة
        if ($request->filled('task_id')) {
            $query->where('task_id', $request->task_id);
        }

        // التصفية حسب التاريخ
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('start_time', [$request->start_date, $request->end_date]);
        }

        // التصفية للإدخالات النشطة
        if ($request->boolean('active_only')) {
            $query->whereNull('end_time');
        }

        // التصفية للإدخالات المكتملة
        if ($request->boolean('completed_only')) {
            $query->whereNotNull('end_time');
        }

        // التصفية حسب القابلية للفوترة
        if ($request->has('is_billable')) {
            $query->where('is_billable', $request->boolean('is_billable'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'start_time');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $timeEntries = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new TimeEntryCollection($timeEntries),
            'meta' => [
                'total' => $timeEntries->total(),
                'per_page' => $timeEntries->perPage(),
                'current_page' => $timeEntries->currentPage(),
                'last_page' => $timeEntries->lastPage(),
            ],
        ]);
    }

    /**
     * بدء تتبع الوقت
     */
    public function startTimer(Request $request): JsonResponse
    {
        $request->validate([
            'task_id' => 'nullable|exists:tasks,id',
            'project_id' => 'required|exists:projects,id',
            'description' => 'nullable|string|max:500',
            'is_billable' => 'boolean',
        ]);

        $this->authorize('create', TimeEntry::class);

        try {
            // إيقاف أي مؤقت نشط للمستخدم
            $this->timeService->stopActiveTimer(auth()->id());

            $timeEntry = $this->timeService->startTimer([
                'user_id' => auth()->id(),
                'task_id' => $request->task_id,
                'project_id' => $request->project_id,
                'description' => $request->description,
                'is_billable' => $request->boolean('is_billable', true),
                'start_time' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم بدء تتبع الوقت بنجاح',
                'data' => new TimeEntryResource($timeEntry),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء بدء تتبع الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إيقاف تتبع الوقت
     */
    public function stopTimer(Request $request, int $id): JsonResponse
    {
        $timeEntry = TimeEntry::findOrFail($id);
        $this->authorize('update', $timeEntry);

        $request->validate([
            'description' => 'nullable|string|max:500',
        ]);

        try {
            $timeEntry = $this->timeService->stopTimer($timeEntry, $request->description);

            return response()->json([
                'success' => true,
                'message' => 'تم إيقاف تتبع الوقت بنجاح',
                'data' => new TimeEntryResource($timeEntry),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إيقاف تتبع الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إنشاء إدخال وقت يدوي
     */
    public function store(StoreTimeEntryRequest $request): JsonResponse
    {
        $this->authorize('create', TimeEntry::class);

        DB::beginTransaction();

        try {
            $timeEntry = $this->timeService->createManualEntry($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء إدخال الوقت بنجاح',
                'data' => new TimeEntryResource($timeEntry->load(['user', 'task', 'project'])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء إدخال الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل إدخال وقت محدد
     */
    public function show(int $id): JsonResponse
    {
        $timeEntry = TimeEntry::with(['user', 'task', 'project'])->findOrFail($id);
        $this->authorize('view', $timeEntry);

        return response()->json([
            'success' => true,
            'data' => new TimeEntryResource($timeEntry),
        ]);
    }

    /**
     * تحديث إدخال الوقت
     */
    public function update(UpdateTimeEntryRequest $request, int $id): JsonResponse
    {
        $timeEntry = TimeEntry::findOrFail($id);
        $this->authorize('update', $timeEntry);

        DB::beginTransaction();

        try {
            $timeEntry = $this->timeService->updateTimeEntry($timeEntry, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إدخال الوقت بنجاح',
                'data' => new TimeEntryResource($timeEntry),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث إدخال الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف إدخال الوقت
     */
    public function destroy(int $id): JsonResponse
    {
        $timeEntry = TimeEntry::findOrFail($id);
        $this->authorize('delete', $timeEntry);

        try {
            $timeEntry->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف إدخال الوقت بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف إدخال الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على المؤقت النشط للمستخدم
     */
    public function getActiveTimer(): JsonResponse
    {
        $activeTimer = $this->timeService->getActiveTimer(auth()->id());

        return response()->json([
            'success' => true,
            'data' => $activeTimer ? new TimeEntryResource($activeTimer) : null,
        ]);
    }

    /**
     * الحصول على تقرير الوقت
     */
    public function getTimeReport(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'project_id' => 'nullable|exists:projects,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'group_by' => 'nullable|string|in:day,week,month,project,task,user',
        ]);

        $this->authorize('viewAny', TimeEntry::class);

        $report = $this->timeService->generateTimeReport([
            'user_id' => $request->user_id,
            'project_id' => $request->project_id,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'group_by' => $request->group_by ?? 'day',
        ]);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * الحصول على إحصائيات الإنتاجية
     */
    public function getProductivityStats(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'project_id' => 'nullable|exists:projects,id',
            'period' => 'nullable|string|in:today,week,month,quarter,year',
        ]);

        $this->authorize('viewAny', TimeEntry::class);

        $stats = $this->timeService->getProductivityStatistics([
            'user_id' => $request->user_id ?? auth()->id(),
            'project_id' => $request->project_id,
            'period' => $request->period ?? 'week',
        ]);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * تصدير تقرير الوقت
     */
    public function exportTimeReport(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:excel,pdf,csv',
            'user_id' => 'nullable|exists:users,id',
            'project_id' => 'nullable|exists:projects,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $this->authorize('viewAny', TimeEntry::class);

        try {
            $filePath = $this->timeService->exportTimeReport([
                'format' => $request->format,
                'user_id' => $request->user_id,
                'project_id' => $request->project_id,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تصدير تقرير الوقت بنجاح',
                'download_url' => asset('storage/' . $filePath),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير تقرير الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الموافقة على إدخالات الوقت
     */
    public function approveTimeEntries(Request $request): JsonResponse
    {
        $request->validate([
            'time_entry_ids' => 'required|array',
            'time_entry_ids.*' => 'exists:time_entries,id',
            'comment' => 'nullable|string|max:500',
        ]);

        try {
            $approvedCount = $this->timeService->approveTimeEntries(
                $request->time_entry_ids,
                auth()->id(),
                $request->comment
            );

            return response()->json([
                'success' => true,
                'message' => "تم اعتماد {$approvedCount} إدخال وقت بنجاح",
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء اعتماد إدخالات الوقت',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
