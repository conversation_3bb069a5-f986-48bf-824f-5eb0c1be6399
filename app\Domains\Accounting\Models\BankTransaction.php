<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج المعاملة البنكية
 * يدعم الربط التلقائي مع البنوك والمطابقة الذكية
 */
class BankTransaction extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'bank_account_id',
        'transaction_date',
        'value_date',
        'description',
        'reference',
        'amount',
        'type',
        'balance',
        'status',
        'matched_type',
        'matched_id',
        'suggested_account_id',
        'confidence_score',
        'category',
        'tags',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'value_date' => 'date',
        'amount' => 'decimal:2',
        'balance' => 'decimal:2',
        'confidence_score' => 'decimal:2',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع المعاملات
     */
    public const TYPES = [
        'DEBIT' => 'مدين',
        'CREDIT' => 'دائن',
    ];

    /**
     * حالات المعاملة
     */
    public const STATUSES = [
        'PENDING' => 'في الانتظار',
        'MATCHED' => 'مطابقة',
        'CLASSIFIED' => 'مصنفة',
        'MANUAL' => 'يدوية',
        'IGNORED' => 'متجاهلة',
        'DISPUTED' => 'متنازع عليها',
    ];

    /**
     * أنواع المطابقة
     */
    public const MATCHED_TYPES = [
        'invoice' => 'فاتورة',
        'payment' => 'دفعة',
        'expense' => 'مصروف',
        'transfer' => 'تحويل',
        'fee' => 'رسوم',
        'interest' => 'فوائد',
    ];

    /**
     * فئات المعاملات
     */
    public const CATEGORIES = [
        'SALES' => 'مبيعات',
        'PURCHASES' => 'مشتريات',
        'SALARIES' => 'رواتب',
        'UTILITIES' => 'مرافق',
        'RENT' => 'إيجار',
        'MARKETING' => 'تسويق',
        'TRAVEL' => 'سفر',
        'OFFICE' => 'مكتب',
        'FEES' => 'رسوم',
        'TAXES' => 'ضرائب',
        'LOAN' => 'قرض',
        'INVESTMENT' => 'استثمار',
        'OTHER' => 'أخرى',
    ];

    /**
     * الحساب البنكي
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * الحساب المقترح
     */
    public function suggestedAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'suggested_account_id');
    }

    /**
     * العنصر المطابق (polymorphic)
     */
    public function matched(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * القيود المحاسبية المرتبطة
     */
    public function journalEntries()
    {
        return $this->morphMany(JournalEntry::class, 'source');
    }

    /**
     * التحقق من كون المعاملة مطابقة
     */
    public function isMatched(): bool
    {
        return $this->status === 'MATCHED';
    }

    /**
     * التحقق من كون المعاملة مصنفة
     */
    public function isClassified(): bool
    {
        return $this->status === 'CLASSIFIED';
    }

    /**
     * التحقق من كون المعاملة دائنة
     */
    public function isCredit(): bool
    {
        return $this->type === 'CREDIT' || $this->amount > 0;
    }

    /**
     * التحقق من كون المعاملة مدينة
     */
    public function isDebit(): bool
    {
        return $this->type === 'DEBIT' || $this->amount < 0;
    }

    /**
     * الحصول على المبلغ المطلق
     */
    public function getAbsoluteAmount(): float
    {
        return abs($this->amount);
    }

    /**
     * مطابقة مع فاتورة
     */
    public function matchWithInvoice(Invoice $invoice): bool
    {
        if (abs($this->getAbsoluteAmount() - $invoice->balance_amount) < 0.01) {
            $this->update([
                'status' => 'MATCHED',
                'matched_type' => 'invoice',
                'matched_id' => $invoice->id,
                'confidence_score' => 1.0,
            ]);

            return true;
        }

        return false;
    }

    /**
     * تصنيف المعاملة
     */
    public function classify(string $category, Account $account, float $confidence = 0.8): bool
    {
        return $this->update([
            'status' => 'CLASSIFIED',
            'category' => $category,
            'suggested_account_id' => $account->id,
            'confidence_score' => $confidence,
        ]);
    }

    /**
     * إضافة علامة
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    /**
     * إزالة علامة
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        $this->update(['tags' => array_values($tags)]);
    }

    /**
     * البحث في الوصف
     */
    public function searchInDescription(string $term): bool
    {
        return stripos($this->description, $term) !== false;
    }

    /**
     * الحصول على معاملات مشابهة
     */
    public function getSimilarTransactions(int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('id', '!=', $this->id)
            ->where('bank_account_id', $this->bank_account_id)
            ->where(function ($query) {
                // البحث بالوصف المشابه
                $words = explode(' ', $this->description);
                foreach ($words as $word) {
                    if (strlen($word) > 3) {
                        $query->orWhere('description', 'LIKE', "%{$word}%");
                    }
                }
            })
            ->orWhereBetween('amount', [
                $this->amount * 0.9,
                $this->amount * 1.1
            ])
            ->orderByRaw('ABS(amount - ?) ASC', [$this->amount])
            ->limit($limit)
            ->get();
    }

    /**
     * تطبيق قاعدة تصنيف
     */
    public function applyClassificationRule(array $rule): bool
    {
        $matches = false;

        // التحقق من النمط
        if (isset($rule['pattern']) && preg_match($rule['pattern'], $this->description)) {
            $matches = true;
        }

        // التحقق من المبلغ
        if (isset($rule['amount_range'])) {
            $min = $rule['amount_range']['min'] ?? null;
            $max = $rule['amount_range']['max'] ?? null;

            if ($min && $this->getAbsoluteAmount() < $min) {
                $matches = false;
            }

            if ($max && $this->getAbsoluteAmount() > $max) {
                $matches = false;
            }
        }

        // التحقق من النوع
        if (isset($rule['type']) && $this->type !== $rule['type']) {
            $matches = false;
        }

        if ($matches && isset($rule['account_id'], $rule['category'])) {
            return $this->classify(
                $rule['category'],
                Account::find($rule['account_id']),
                $rule['confidence'] ?? 0.8
            );
        }

        return false;
    }

    /**
     * نطاق للمعاملات غير المطابقة
     */
    public function scopeUnmatched($query)
    {
        return $query->where('status', 'PENDING');
    }

    /**
     * نطاق للمعاملات المطابقة
     */
    public function scopeMatched($query)
    {
        return $query->where('status', 'MATCHED');
    }

    /**
     * نطاق حسب النوع
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * نطاق حسب الفئة
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * نطاق حسب التاريخ
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    /**
     * نطاق حسب المبلغ
     */
    public function scopeByAmountRange($query, float $min, float $max)
    {
        return $query->whereBetween('amount', [$min, $max]);
    }
}
