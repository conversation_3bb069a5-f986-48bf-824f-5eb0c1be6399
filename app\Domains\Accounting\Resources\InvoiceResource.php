<?php

namespace App\Domains\Accounting\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Invoice Resource
 * مورد الفاتورة
 */
class InvoiceResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'invoice_number' => $this->invoice_number,
            'reference_number' => $this->reference_number,
            'po_number' => $this->po_number,
            'invoice_date' => $this->invoice_date,
            'due_date' => $this->due_date,
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            
            // المبالغ
            'subtotal' => $this->subtotal,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'total_amount' => $this->total_amount,
            'paid_amount' => $this->paid_amount,
            'remaining_amount' => $this->total_amount - $this->paid_amount,
            
            // العملة
            'currency' => $this->currency,
            'currency_symbol' => $this->getCurrencySymbol(),
            'exchange_rate' => $this->exchange_rate,
            
            // الخصم
            'discount_type' => $this->discount_type,
            'discount_value' => $this->discount_value,
            'tax_inclusive' => $this->tax_inclusive,
            
            // الشروط والملاحظات
            'payment_terms' => $this->payment_terms,
            'notes' => $this->notes,
            'terms_conditions' => $this->terms_conditions,
            
            // الفواتير المتكررة
            'is_recurring' => $this->is_recurring,
            'recurring_frequency' => $this->recurring_frequency,
            'recurring_end_date' => $this->recurring_end_date,
            'next_recurring_date' => $this->next_recurring_date,
            'parent_invoice_id' => $this->parent_invoice_id,
            
            // العميل
            'customer' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->id,
                    'name' => $this->customer->name,
                    'email' => $this->customer->email,
                    'phone' => $this->customer->phone,
                    'address' => $this->customer->address,
                    'tax_number' => $this->customer->tax_number,
                ];
            }),
            
            // بنود الفاتورة
            'items' => $this->whenLoaded('items', function () {
                return $this->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'description' => $item->description,
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'discount_percentage' => $item->discount_percentage,
                        'discount_amount' => $item->discount_amount,
                        'tax_rate' => $item->tax_rate,
                        'tax_amount' => $item->tax_amount,
                        'line_total' => $item->line_total,
                        'account_id' => $item->account_id,
                        'product' => $item->product ? [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'sku' => $item->product->sku,
                        ] : null,
                    ];
                });
            }),
            
            // المدفوعات
            'payments' => $this->whenLoaded('payments', function () {
                return $this->payments->map(function ($payment) {
                    return [
                        'id' => $payment->id,
                        'payment_number' => $payment->payment_number,
                        'amount' => $payment->amount,
                        'payment_date' => $payment->payment_date,
                        'payment_method' => $payment->payment_method,
                        'reference' => $payment->reference,
                        'status' => $payment->status,
                    ];
                });
            }),
            
            // القيود المحاسبية
            'journal_entries' => $this->whenLoaded('journalEntries', function () {
                return $this->journalEntries->map(function ($entry) {
                    return [
                        'id' => $entry->id,
                        'entry_number' => $entry->entry_number,
                        'entry_date' => $entry->entry_date,
                        'description' => $entry->description,
                        'total_debit' => $entry->total_debit,
                        'total_credit' => $entry->total_credit,
                        'status' => $entry->status,
                    ];
                });
            }),
            
            // المشروع ومركز التكلفة
            'project' => $this->whenLoaded('project', function () {
                return [
                    'id' => $this->project->id,
                    'name' => $this->project->name,
                    'code' => $this->project->code,
                ];
            }),
            
            'cost_center' => $this->whenLoaded('costCenter', function () {
                return [
                    'id' => $this->costCenter->id,
                    'name' => $this->costCenter->name,
                    'code' => $this->costCenter->code,
                ];
            }),
            
            // الحالة والتواريخ المهمة
            'is_overdue' => $this->isOverdue(),
            'days_overdue' => $this->getDaysOverdue(),
            'days_until_due' => $this->getDaysUntilDue(),
            'payment_status' => $this->getPaymentStatus(),
            
            // بيانات التدقيق
            'audit_info' => [
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
                'sent_at' => $this->sent_at,
                'viewed_at' => $this->viewed_at,
                'created_by' => $this->whenLoaded('creator', function () {
                    return [
                        'id' => $this->creator->id,
                        'name' => $this->creator->name,
                    ];
                }),
                'updated_by' => $this->whenLoaded('updater', function () {
                    return [
                        'id' => $this->updater->id,
                        'name' => $this->updater->name,
                    ];
                }),
            ],
            
            // الروابط
            'links' => [
                'self' => route('api.accounting.invoices.show', $this->id),
                'edit' => route('api.accounting.invoices.edit', $this->id),
                'pdf' => route('api.accounting.invoices.pdf', $this->id),
                'send' => route('api.accounting.invoices.send', $this->id),
                'payments' => route('api.accounting.invoices.payments', $this->id),
            ],
            
            // الأذونات
            'permissions' => $this->when(auth()->check(), function () {
                return [
                    'can_view' => auth()->user()->can('view', $this->resource),
                    'can_edit' => auth()->user()->can('update', $this->resource),
                    'can_delete' => auth()->user()->can('delete', $this->resource),
                    'can_send' => auth()->user()->can('send', $this->resource),
                    'can_approve' => auth()->user()->can('approve', $this->resource),
                    'can_cancel' => auth()->user()->can('cancel', $this->resource),
                    'can_duplicate' => auth()->user()->can('duplicate', $this->resource),
                    'can_record_payment' => auth()->user()->can('recordPayment', $this->resource),
                ];
            }),
        ];
    }

    /**
     * الحصول على تسمية الحالة
     */
    protected function getStatusLabel(): string
    {
        return match ($this->status) {
            'draft' => 'مسودة',
            'pending' => 'في انتظار الاعتماد',
            'approved' => 'معتمدة',
            'sent' => 'مرسلة',
            'viewed' => 'تم عرضها',
            'paid' => 'مدفوعة',
            'partially_paid' => 'مدفوعة جزئياً',
            'overdue' => 'متأخرة',
            'cancelled' => 'ملغية',
            'refunded' => 'مستردة',
            default => $this->status,
        };
    }

    /**
     * الحصول على رمز العملة
     */
    protected function getCurrencySymbol(): string
    {
        return match ($this->currency) {
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'AED' => 'د.إ',
            'EGP' => 'ج.م',
            default => $this->currency,
        };
    }

    /**
     * التحقق من كون الفاتورة متأخرة
     */
    protected function isOverdue(): bool
    {
        return $this->due_date < now() && 
               in_array($this->status, ['sent', 'viewed']) &&
               $this->remaining_amount > 0;
    }

    /**
     * الحصول على عدد الأيام المتأخرة
     */
    protected function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return now()->diffInDays($this->due_date);
    }

    /**
     * الحصول على عدد الأيام حتى الاستحقاق
     */
    protected function getDaysUntilDue(): int
    {
        if ($this->due_date < now()) {
            return 0;
        }

        return now()->diffInDays($this->due_date);
    }

    /**
     * الحصول على حالة الدفع
     */
    protected function getPaymentStatus(): string
    {
        if ($this->paid_amount == 0) {
            return 'unpaid';
        } elseif ($this->paid_amount >= $this->total_amount) {
            return 'paid';
        } else {
            return 'partially_paid';
        }
    }

    /**
     * البيانات الإضافية مع الاستجابة
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'invoice_statuses' => [
                    'draft' => 'مسودة',
                    'pending' => 'في انتظار الاعتماد',
                    'approved' => 'معتمدة',
                    'sent' => 'مرسلة',
                    'viewed' => 'تم عرضها',
                    'paid' => 'مدفوعة',
                    'partially_paid' => 'مدفوعة جزئياً',
                    'overdue' => 'متأخرة',
                    'cancelled' => 'ملغية',
                    'refunded' => 'مستردة',
                ],
                'payment_statuses' => [
                    'unpaid' => 'غير مدفوعة',
                    'partially_paid' => 'مدفوعة جزئياً',
                    'paid' => 'مدفوعة',
                ],
                'discount_types' => [
                    'percentage' => 'نسبة مئوية',
                    'fixed' => 'مبلغ ثابت',
                ],
                'recurring_frequencies' => [
                    'weekly' => 'أسبوعي',
                    'monthly' => 'شهري',
                    'quarterly' => 'ربع سنوي',
                    'yearly' => 'سنوي',
                ],
                'supported_currencies' => [
                    'SAR' => 'ريال سعودي',
                    'USD' => 'دولار أمريكي',
                    'EUR' => 'يورو',
                    'GBP' => 'جنيه إسترليني',
                    'AED' => 'درهم إماراتي',
                    'EGP' => 'جنيه مصري',
                ],
            ],
        ];
    }
}
