<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج عميل التجارة الإلكترونية
 * يمثل عميل مزامن من منصة التجارة الإلكترونية
 */
class ECommerceCustomer extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'external_id',
        'username',
        'email',
        'phone',
        'first_name',
        'last_name',
        'display_name',
        'avatar_url',
        'date_of_birth',
        'gender',
        'language',
        'locale',
        'timezone',
        'currency',
        'country',
        'state',
        'city',
        'postal_code',
        'address_line_1',
        'address_line_2',
        'billing_address',
        'shipping_address',
        'addresses',
        'role',
        'customer_group',
        'customer_type',
        'customer_segment',
        'customer_tier',
        'customer_status',
        'account_status',
        'email_verified',
        'phone_verified',
        'identity_verified',
        'kyc_status',
        'kyc_level',
        'risk_level',
        'trust_score',
        'credit_score',
        'loyalty_points',
        'reward_points',
        'store_credit',
        'wallet_balance',
        'total_spent',
        'total_orders',
        'total_items',
        'average_order_value',
        'lifetime_value',
        'first_order_date',
        'last_order_date',
        'last_login_date',
        'registration_date',
        'preferences',
        'interests',
        'tags',
        'notes',
        'custom_fields',
        'marketing_consent',
        'email_marketing',
        'sms_marketing',
        'push_notifications',
        'newsletter_subscription',
        'promotional_emails',
        'order_updates',
        'shipping_updates',
        'product_recommendations',
        'price_alerts',
        'stock_alerts',
        'wishlist_updates',
        'review_reminders',
        'birthday_offers',
        'anniversary_offers',
        'seasonal_offers',
        'personalized_offers',
        'exclusive_offers',
        'vip_offers',
        'early_access',
        'beta_access',
        'preview_access',
        'member_benefits',
        'loyalty_benefits',
        'tier_benefits',
        'group_benefits',
        'referral_code',
        'referred_by',
        'referral_count',
        'referral_earnings',
        'affiliate_id',
        'affiliate_code',
        'affiliate_earnings',
        'social_profiles',
        'social_connections',
        'social_sharing',
        'social_login',
        'oauth_providers',
        'login_methods',
        'security_settings',
        'privacy_settings',
        'communication_settings',
        'notification_settings',
        'display_settings',
        'accessibility_settings',
        'device_info',
        'browser_info',
        'session_info',
        'location_info',
        'ip_address',
        'user_agent',
        'last_seen_ip',
        'last_seen_location',
        'last_seen_device',
        'last_seen_browser',
        'login_count',
        'session_count',
        'page_views',
        'time_spent',
        'bounce_rate',
        'conversion_rate',
        'engagement_score',
        'activity_score',
        'interaction_score',
        'participation_score',
        'contribution_score',
        'influence_score',
        'advocacy_score',
        'satisfaction_score',
        'nps_score',
        'csat_score',
        'ces_score',
        'quality_score',
        'service_score',
        'support_score',
        'experience_score',
        'journey_score',
        'touchpoint_score',
        'channel_score',
        'platform_score',
        'device_score',
        'browser_score',
        'mobile_score',
        'desktop_score',
        'tablet_score',
        'app_score',
        'web_score',
        'email_score',
        'sms_score',
        'push_score',
        'social_score',
        'search_score',
        'organic_score',
        'paid_score',
        'direct_score',
        'referral_score',
        'affiliate_score',
        'partner_score',
        'influencer_score',
        'brand_score',
        'product_score',
        'category_score',
        'price_score',
        'value_score',
        'quality_product_score',
        'service_product_score',
        'delivery_score',
        'shipping_score',
        'packaging_score',
        'return_score',
        'refund_score',
        'exchange_score',
        'warranty_score',
        'support_product_score',
        'documentation_score',
        'tutorial_score',
        'training_score',
        'onboarding_score',
        'adoption_score',
        'usage_score',
        'feature_score',
        'functionality_score',
        'usability_score',
        'accessibility_score',
        'performance_score',
        'reliability_score',
        'availability_score',
        'scalability_score',
        'security_score',
        'privacy_score',
        'compliance_score',
        'legal_score',
        'ethical_score',
        'environmental_score',
        'social_responsibility_score',
        'sustainability_score',
        'innovation_score',
        'technology_score',
        'digital_score',
        'mobile_first_score',
        'cloud_score',
        'ai_score',
        'ml_score',
        'automation_score',
        'personalization_score',
        'customization_score',
        'localization_score',
        'globalization_score',
        'internationalization_score',
        'regionalization_score',
        'culturalization_score',
        'demographic_score',
        'psychographic_score',
        'behavioral_score',
        'transactional_score',
        'operational_score',
        'strategic_score',
        'tactical_score',
        'competitive_score',
        'market_score',
        'industry_score',
        'segment_score',
        'niche_score',
        'vertical_score',
        'horizontal_score',
        'b2b_score',
        'b2c_score',
        'b2b2c_score',
        'c2c_score',
        'c2b_score',
        'g2b_score',
        'g2c_score',
        'p2p_score',
        'marketplace_score',
        'platform_marketplace_score',
        'ecosystem_score',
        'network_score',
        'community_score',
        'social_network_score',
        'professional_score',
        'business_score',
        'enterprise_score',
        'corporate_score',
        'institutional_score',
        'government_score',
        'nonprofit_score',
        'education_score',
        'healthcare_score',
        'finance_score',
        'retail_score',
        'ecommerce_score',
        'manufacturing_score',
        'logistics_score',
        'transportation_score',
        'hospitality_score',
        'travel_score',
        'entertainment_score',
        'media_score',
        'gaming_score',
        'sports_score',
        'fitness_score',
        'wellness_score',
        'beauty_score',
        'fashion_score',
        'lifestyle_score',
        'luxury_score',
        'premium_score',
        'mass_market_score',
        'budget_score',
        'economy_score',
        'value_segment_score',
        'mid_market_score',
        'high_end_score',
        'ultra_high_end_score',
        'exclusive_score',
        'limited_edition_score',
        'collector_score',
        'enthusiast_score',
        'professional_user_score',
        'expert_score',
        'novice_score',
        'beginner_score',
        'intermediate_score',
        'advanced_score',
        'power_user_score',
        'super_user_score',
        'admin_score',
        'moderator_score',
        'contributor_score',
        'member_score',
        'guest_score',
        'visitor_score',
        'prospect_score',
        'lead_score',
        'opportunity_score',
        'customer_lead_score',
        'hot_lead_score',
        'warm_lead_score',
        'cold_lead_score',
        'qualified_lead_score',
        'unqualified_lead_score',
        'marketing_qualified_score',
        'sales_qualified_score',
        'product_qualified_score',
        'service_qualified_score',
        'support_qualified_score',
        'technical_qualified_score',
        'business_qualified_score',
        'decision_maker_score',
        'influencer_customer_score',
        'user_score',
        'buyer_score',
        'purchaser_score',
        'evaluator_score',
        'recommender_score',
        'approver_score',
        'gatekeeper_score',
        'champion_score',
        'sponsor_score',
        'stakeholder_score',
        'end_user_score',
        'administrator_score',
        'manager_score',
        'director_score',
        'executive_score',
        'c_level_score',
        'board_score',
        'investor_score',
        'partner_customer_score',
        'vendor_score',
        'supplier_score',
        'distributor_score',
        'reseller_score',
        'retailer_customer_score',
        'wholesaler_score',
        'manufacturer_customer_score',
        'producer_score',
        'creator_score',
        'developer_score',
        'designer_score',
        'architect_score',
        'engineer_score',
        'consultant_score',
        'advisor_score',
        'analyst_score',
        'researcher_score',
        'scientist_score',
        'academic_score',
        'student_score',
        'teacher_score',
        'trainer_score',
        'coach_score',
        'mentor_score',
        'guide_score',
        'facilitator_score',
        'coordinator_score',
        'organizer_score',
        'planner_score',
        'strategist_score',
        'tactician_score',
        'operator_score',
        'implementer_score',
        'executor_score',
        'performer_score',
        'achiever_score',
        'winner_score',
        'champion_customer_score',
        'leader_customer_score',
        'pioneer_customer_score',
        'innovator_customer_score',
        'early_adopter_score',
        'early_majority_score',
        'late_majority_score',
        'laggard_score',
        'mainstream_score',
        'niche_customer_score',
        'mass_market_customer_score',
        'target_market_score',
        'addressable_market_score',
        'serviceable_market_score',
        'obtainable_market_score',
        'total_addressable_market_score',
        'serviceable_addressable_market_score',
        'serviceable_obtainable_market_score',
        'sync_status',
        'last_synced_at',
        'sync_errors',
        'sync_warnings',
        'sync_notes',
        'is_active',
        'is_synced',
        'is_mapped',
        'is_transformed',
        'is_validated',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'addresses' => 'array',
        'preferences' => 'array',
        'interests' => 'array',
        'tags' => 'array',
        'notes' => 'array',
        'custom_fields' => 'array',
        'social_profiles' => 'array',
        'social_connections' => 'array',
        'oauth_providers' => 'array',
        'login_methods' => 'array',
        'security_settings' => 'array',
        'privacy_settings' => 'array',
        'communication_settings' => 'array',
        'notification_settings' => 'array',
        'display_settings' => 'array',
        'accessibility_settings' => 'array',
        'device_info' => 'array',
        'browser_info' => 'array',
        'session_info' => 'array',
        'location_info' => 'array',
        'sync_errors' => 'array',
        'sync_warnings' => 'array',
        'metadata' => 'array',
        'total_spent' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'lifetime_value' => 'decimal:2',
        'loyalty_points' => 'integer',
        'reward_points' => 'integer',
        'store_credit' => 'decimal:2',
        'wallet_balance' => 'decimal:2',
        'total_orders' => 'integer',
        'total_items' => 'integer',
        'referral_count' => 'integer',
        'referral_earnings' => 'decimal:2',
        'affiliate_earnings' => 'decimal:2',
        'login_count' => 'integer',
        'session_count' => 'integer',
        'page_views' => 'integer',
        'time_spent' => 'integer',
        'trust_score' => 'decimal:2',
        'credit_score' => 'decimal:2',
        'bounce_rate' => 'decimal:4',
        'conversion_rate' => 'decimal:4',
        'engagement_score' => 'decimal:2',
        'activity_score' => 'decimal:2',
        'satisfaction_score' => 'decimal:2',
        'nps_score' => 'decimal:2',
        'csat_score' => 'decimal:2',
        'ces_score' => 'decimal:2',
        'email_verified' => 'boolean',
        'phone_verified' => 'boolean',
        'identity_verified' => 'boolean',
        'marketing_consent' => 'boolean',
        'email_marketing' => 'boolean',
        'sms_marketing' => 'boolean',
        'push_notifications' => 'boolean',
        'newsletter_subscription' => 'boolean',
        'promotional_emails' => 'boolean',
        'order_updates' => 'boolean',
        'shipping_updates' => 'boolean',
        'product_recommendations' => 'boolean',
        'price_alerts' => 'boolean',
        'stock_alerts' => 'boolean',
        'wishlist_updates' => 'boolean',
        'review_reminders' => 'boolean',
        'birthday_offers' => 'boolean',
        'anniversary_offers' => 'boolean',
        'seasonal_offers' => 'boolean',
        'personalized_offers' => 'boolean',
        'exclusive_offers' => 'boolean',
        'vip_offers' => 'boolean',
        'early_access' => 'boolean',
        'beta_access' => 'boolean',
        'preview_access' => 'boolean',
        'social_sharing' => 'boolean',
        'social_login' => 'boolean',
        'is_active' => 'boolean',
        'is_synced' => 'boolean',
        'is_mapped' => 'boolean',
        'is_transformed' => 'boolean',
        'is_validated' => 'boolean',
        'date_of_birth' => 'date',
        'first_order_date' => 'datetime',
        'last_order_date' => 'datetime',
        'last_login_date' => 'datetime',
        'registration_date' => 'datetime',
        'last_synced_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'first_name', 'last_name', 'email', 'phone', 'customer_status',
                'total_spent', 'total_orders', 'is_active', 'sync_status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(ECommerceOrder::class, 'customer_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSynced($query)
    {
        return $query->where('is_synced', true);
    }

    public function scopeVerified($query)
    {
        return $query->where('email_verified', true);
    }

    public function scopeByCustomerType($query, $type)
    {
        return $query->where('customer_type', $type);
    }

    public function scopeByCustomerGroup($query, $group)
    {
        return $query->where('customer_group', $group);
    }

    public function scopeByCustomerTier($query, $tier)
    {
        return $query->where('customer_tier', $tier);
    }

    public function scopeHighValue($query, $threshold = 1000)
    {
        return $query->where('lifetime_value', '>=', $threshold);
    }

    public function scopeFrequentBuyers($query, $threshold = 5)
    {
        return $query->where('total_orders', '>=', $threshold);
    }

    public function scopeRecentCustomers($query, $days = 30)
    {
        return $query->where('registration_date', '>=', now()->subDays($days));
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isSynced(): bool
    {
        return $this->is_synced;
    }

    public function isVerified(): bool
    {
        return $this->email_verified && $this->phone_verified;
    }

    public function isEmailVerified(): bool
    {
        return $this->email_verified;
    }

    public function isPhoneVerified(): bool
    {
        return $this->phone_verified;
    }

    public function isIdentityVerified(): bool
    {
        return $this->identity_verified;
    }

    public function hasMarketingConsent(): bool
    {
        return $this->marketing_consent;
    }

    public function allowsEmailMarketing(): bool
    {
        return $this->email_marketing;
    }

    public function allowsSMSMarketing(): bool
    {
        return $this->sms_marketing;
    }

    public function allowsPushNotifications(): bool
    {
        return $this->push_notifications;
    }

    public function getFullName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getDisplayName(): string
    {
        return $this->display_name ?: $this->getFullName() ?: $this->username ?: $this->email;
    }

    public function getTotalSpent(): float
    {
        return $this->total_spent ?? 0;
    }

    public function getTotalOrders(): int
    {
        return $this->total_orders ?? 0;
    }

    public function getTotalItems(): int
    {
        return $this->total_items ?? 0;
    }

    public function getAverageOrderValue(): float
    {
        return $this->average_order_value ?? 0;
    }

    public function getLifetimeValue(): float
    {
        return $this->lifetime_value ?? 0;
    }

    public function getLoyaltyPoints(): int
    {
        return $this->loyalty_points ?? 0;
    }

    public function getRewardPoints(): int
    {
        return $this->reward_points ?? 0;
    }

    public function getStoreCredit(): float
    {
        return $this->store_credit ?? 0;
    }

    public function getWalletBalance(): float
    {
        return $this->wallet_balance ?? 0;
    }

    public function getTrustScore(): float
    {
        return $this->trust_score ?? 0;
    }

    public function getCreditScore(): float
    {
        return $this->credit_score ?? 0;
    }

    public function getEngagementScore(): float
    {
        return $this->engagement_score ?? 0;
    }

    public function getActivityScore(): float
    {
        return $this->activity_score ?? 0;
    }

    public function getSatisfactionScore(): float
    {
        return $this->satisfaction_score ?? 0;
    }

    public function getNPSScore(): float
    {
        return $this->nps_score ?? 0;
    }

    public function getCSATScore(): float
    {
        return $this->csat_score ?? 0;
    }

    public function getCESScore(): float
    {
        return $this->ces_score ?? 0;
    }

    public function getRiskLevel(): string
    {
        return $this->risk_level ?? 'medium';
    }

    public function getCustomerType(): string
    {
        return $this->customer_type ?? 'regular';
    }

    public function getCustomerGroup(): ?string
    {
        return $this->customer_group;
    }

    public function getCustomerTier(): ?string
    {
        return $this->customer_tier;
    }

    public function getCustomerSegment(): ?string
    {
        return $this->customer_segment;
    }

    public function getKYCStatus(): string
    {
        return $this->kyc_status ?? 'pending';
    }

    public function getKYCLevel(): int
    {
        return $this->kyc_level ?? 0;
    }

    public function isHighValue(): bool
    {
        return $this->getLifetimeValue() > 1000; // Configurable threshold
    }

    public function isFrequentBuyer(): bool
    {
        return $this->getTotalOrders() >= 5; // Configurable threshold
    }

    public function isNewCustomer(): bool
    {
        return $this->registration_date && $this->registration_date > now()->subDays(30);
    }

    public function isReturningCustomer(): bool
    {
        return $this->getTotalOrders() > 1;
    }

    public function isVIPCustomer(): bool
    {
        return $this->customer_tier === 'vip' || $this->isHighValue();
    }

    public function isPremiumCustomer(): bool
    {
        return $this->customer_tier === 'premium' || $this->customer_type === 'premium';
    }

    public function isLoyalCustomer(): bool
    {
        return $this->getLoyaltyPoints() > 1000 || $this->isFrequentBuyer();
    }

    public function hasRecentActivity(): bool
    {
        return $this->last_login_date && $this->last_login_date > now()->subDays(30);
    }

    public function hasRecentPurchase(): bool
    {
        return $this->last_order_date && $this->last_order_date > now()->subDays(90);
    }

    public function isAtRisk(): bool
    {
        return !$this->hasRecentActivity() || !$this->hasRecentPurchase();
    }

    public function getBillingAddress(): array
    {
        return $this->billing_address ?? [];
    }

    public function getShippingAddress(): array
    {
        return $this->shipping_address ?? [];
    }

    public function getAllAddresses(): array
    {
        return $this->addresses ?? [];
    }

    public function getPreferences(): array
    {
        return $this->preferences ?? [];
    }

    public function getInterests(): array
    {
        return $this->interests ?? [];
    }

    public function getTags(): array
    {
        return $this->tags ?? [];
    }

    public function getNotes(): array
    {
        return $this->notes ?? [];
    }

    public function getCustomFields(): array
    {
        return $this->custom_fields ?? [];
    }

    public function getSocialProfiles(): array
    {
        return $this->social_profiles ?? [];
    }

    public function getDeviceInfo(): array
    {
        return $this->device_info ?? [];
    }

    public function getBrowserInfo(): array
    {
        return $this->browser_info ?? [];
    }

    public function getLocationInfo(): array
    {
        return $this->location_info ?? [];
    }

    public function getAge(): ?int
    {
        if (!$this->date_of_birth) {
            return null;
        }

        return now()->diffInYears($this->date_of_birth);
    }

    public function getCustomerSince(): ?int
    {
        if (!$this->registration_date) {
            return null;
        }

        return now()->diffInDays($this->registration_date);
    }

    public function getDaysSinceLastOrder(): ?int
    {
        if (!$this->last_order_date) {
            return null;
        }

        return now()->diffInDays($this->last_order_date);
    }

    public function getDaysSinceLastLogin(): ?int
    {
        if (!$this->last_login_date) {
            return null;
        }

        return now()->diffInDays($this->last_login_date);
    }

    public function getOrderFrequency(): float
    {
        $customerSince = $this->getCustomerSince();
        
        if (!$customerSince || $customerSince <= 0) {
            return 0;
        }

        return $this->getTotalOrders() / ($customerSince / 30); // Orders per month
    }

    public function getRepurchaseRate(): float
    {
        if ($this->getTotalOrders() <= 1) {
            return 0;
        }

        return (($this->getTotalOrders() - 1) / $this->getTotalOrders()) * 100;
    }

    public function getChurnRisk(): string
    {
        $daysSinceLastOrder = $this->getDaysSinceLastOrder();
        $daysSinceLastLogin = $this->getDaysSinceLastLogin();

        if ($daysSinceLastOrder > 180 || $daysSinceLastLogin > 90) {
            return 'high';
        }

        if ($daysSinceLastOrder > 90 || $daysSinceLastLogin > 60) {
            return 'medium';
        }

        return 'low';
    }

    public function getSegmentScore(): float
    {
        $scores = [
            'recency' => $this->getRecencyScore(),
            'frequency' => $this->getFrequencyScore(),
            'monetary' => $this->getMonetaryScore(),
        ];

        return array_sum($scores) / count($scores);
    }

    public function getRecencyScore(): float
    {
        $daysSinceLastOrder = $this->getDaysSinceLastOrder();

        if ($daysSinceLastOrder === null) {
            return 0;
        }

        if ($daysSinceLastOrder <= 30) return 5;
        if ($daysSinceLastOrder <= 60) return 4;
        if ($daysSinceLastOrder <= 90) return 3;
        if ($daysSinceLastOrder <= 180) return 2;
        
        return 1;
    }

    public function getFrequencyScore(): float
    {
        $totalOrders = $this->getTotalOrders();

        if ($totalOrders >= 20) return 5;
        if ($totalOrders >= 10) return 4;
        if ($totalOrders >= 5) return 3;
        if ($totalOrders >= 2) return 2;
        if ($totalOrders >= 1) return 1;
        
        return 0;
    }

    public function getMonetaryScore(): float
    {
        $lifetimeValue = $this->getLifetimeValue();

        if ($lifetimeValue >= 5000) return 5;
        if ($lifetimeValue >= 2000) return 4;
        if ($lifetimeValue >= 1000) return 3;
        if ($lifetimeValue >= 500) return 2;
        if ($lifetimeValue >= 100) return 1;
        
        return 0;
    }

    public function getRFMSegment(): string
    {
        $recency = $this->getRecencyScore();
        $frequency = $this->getFrequencyScore();
        $monetary = $this->getMonetaryScore();

        if ($recency >= 4 && $frequency >= 4 && $monetary >= 4) {
            return 'champions';
        }

        if ($recency >= 3 && $frequency >= 3 && $monetary >= 3) {
            return 'loyal_customers';
        }

        if ($recency >= 4 && $frequency <= 2) {
            return 'new_customers';
        }

        if ($recency >= 3 && $frequency >= 3 && $monetary <= 2) {
            return 'potential_loyalists';
        }

        if ($recency <= 2 && $frequency >= 3 && $monetary >= 3) {
            return 'at_risk';
        }

        if ($recency <= 2 && $frequency <= 2 && $monetary >= 3) {
            return 'cannot_lose_them';
        }

        if ($recency <= 2 && $frequency <= 2 && $monetary <= 2) {
            return 'hibernating';
        }

        return 'others';
    }

    public function getSyncErrors(): array
    {
        return $this->sync_errors ?? [];
    }

    public function getSyncWarnings(): array
    {
        return $this->sync_warnings ?? [];
    }

    public function hasSyncErrors(): bool
    {
        return !empty($this->getSyncErrors());
    }

    public function hasSyncWarnings(): bool
    {
        return !empty($this->getSyncWarnings());
    }

    public function getLastSyncStatus(): string
    {
        if ($this->hasSyncErrors()) {
            return 'error';
        }

        if ($this->hasSyncWarnings()) {
            return 'warning';
        }

        if ($this->isSynced()) {
            return 'success';
        }

        return 'pending';
    }

    public function needsSync(): bool
    {
        return !$this->isSynced() || $this->hasSyncErrors();
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->integration && $this->integration->canSync();
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getTrustScore(),
            $this->getEngagementScore(),
            $this->getActivityScore(),
            $this->getSatisfactionScore(),
            $this->getSegmentScore() * 20, // Convert 5-point scale to 100-point scale
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'external_id' => $this->external_id,
            'display_name' => $this->getDisplayName(),
            'email' => $this->email,
            'phone' => $this->phone,
            'customer_type' => $this->getCustomerType(),
            'customer_group' => $this->getCustomerGroup(),
            'customer_tier' => $this->getCustomerTier(),
            'customer_segment' => $this->getRFMSegment(),
            'total_spent' => $this->getTotalSpent(),
            'total_orders' => $this->getTotalOrders(),
            'average_order_value' => $this->getAverageOrderValue(),
            'lifetime_value' => $this->getLifetimeValue(),
            'loyalty_points' => $this->getLoyaltyPoints(),
            'registration_date' => $this->registration_date,
            'last_order_date' => $this->last_order_date,
            'last_login_date' => $this->last_login_date,
            'is_verified' => $this->isVerified(),
            'is_high_value' => $this->isHighValue(),
            'is_frequent_buyer' => $this->isFrequentBuyer(),
            'is_vip' => $this->isVIPCustomer(),
            'is_at_risk' => $this->isAtRisk(),
            'churn_risk' => $this->getChurnRisk(),
            'trust_score' => $this->getTrustScore(),
            'engagement_score' => $this->getEngagementScore(),
            'satisfaction_score' => $this->getSatisfactionScore(),
            'sync_status' => $this->getLastSyncStatus(),
            'last_synced' => $this->last_synced_at,
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
