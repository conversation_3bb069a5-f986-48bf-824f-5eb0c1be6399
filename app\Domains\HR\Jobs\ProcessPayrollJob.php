<?php

namespace App\Domains\HR\Jobs;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Services\PayrollCalculationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * مهمة معالجة كشوف الرواتب
 * معالجة شاملة لكشوف الرواتب الشهرية
 */
class ProcessPayrollJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $month;
    public int $year;
    public ?array $employeeIds;
    public array $options;

    /**
     * عدد المحاولات
     */
    public int $tries = 3;

    /**
     * مهلة انتهاء المهمة (بالثواني)
     */
    public int $timeout = 1800; // 30 دقيقة

    /**
     * إنشاء مثيل جديد من المهمة
     */
    public function __construct(
        int $month,
        int $year,
        ?array $employeeIds = null,
        array $options = []
    ) {
        $this->month = $month;
        $this->year = $year;
        $this->employeeIds = $employeeIds;
        $this->options = $options;
    }

    /**
     * تنفيذ المهمة
     */
    public function handle(PayrollCalculationService $payrollService): void
    {
        Log::info('بدء معالجة كشوف الرواتب', [
            'month' => $this->month,
            'year' => $this->year,
            'employee_count' => $this->employeeIds ? count($this->employeeIds) : 'all',
        ]);

        try {
            $query = Employee::active();

            // تصفية الموظفين إذا تم تحديدهم
            if ($this->employeeIds) {
                $query->whereIn('id', $this->employeeIds);
            }

            $employees = $query->with(['department', 'position', 'currentContract'])->get();
            
            $processedCount = 0;
            $failedCount = 0;
            $totalAmount = 0;

            foreach ($employees as $employee) {
                try {
                    $payslip = $payrollService->generatePayslip(
                        $employee,
                        $this->month,
                        $this->year,
                        $this->options
                    );

                    if ($payslip) {
                        $processedCount++;
                        $totalAmount += $payslip['net_salary'] ?? 0;
                        
                        Log::debug('تم معالجة كشف راتب الموظف', [
                            'employee_id' => $employee->id,
                            'employee_name' => $employee->full_name,
                            'net_salary' => $payslip['net_salary'] ?? 0,
                        ]);
                    }

                } catch (\Exception $e) {
                    $failedCount++;
                    
                    Log::error('فشل في معالجة كشف راتب الموظف', [
                        'employee_id' => $employee->id,
                        'employee_name' => $employee->full_name,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('انتهاء معالجة كشوف الرواتب', [
                'month' => $this->month,
                'year' => $this->year,
                'total_employees' => $employees->count(),
                'processed_count' => $processedCount,
                'failed_count' => $failedCount,
                'total_amount' => $totalAmount,
            ]);

            // إرسال إشعار بانتهاء المعالجة
            $this->sendCompletionNotification($processedCount, $failedCount, $totalAmount);

        } catch (\Exception $e) {
            Log::error('فشل في معالجة كشوف الرواتب', [
                'month' => $this->month,
                'year' => $this->year,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('فشل نهائي في معالجة كشوف الرواتب', [
            'month' => $this->month,
            'year' => $this->year,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // إرسال إشعار بالفشل
        $this->sendFailureNotification($exception);
    }

    /**
     * إرسال إشعار بانتهاء المعالجة
     */
    protected function sendCompletionNotification(int $processed, int $failed, float $totalAmount): void
    {
        // يمكن إضافة إرسال إشعارات عبر البريد الإلكتروني أو الرسائل
        Log::info('إشعار انتهاء معالجة كشوف الرواتب', [
            'processed' => $processed,
            'failed' => $failed,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * إرسال إشعار بالفشل
     */
    protected function sendFailureNotification(\Throwable $exception): void
    {
        // يمكن إضافة إرسال إشعارات عبر البريد الإلكتروني أو الرسائل
        Log::error('إشعار فشل معالجة كشوف الرواتب', [
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * الحصول على معرف فريد للمهمة
     */
    public function uniqueId(): string
    {
        return "payroll-{$this->year}-{$this->month}";
    }

    /**
     * تحديد العلامات للمهمة
     */
    public function tags(): array
    {
        return [
            'payroll',
            'hr',
            "month:{$this->month}",
            "year:{$this->year}",
        ];
    }
}
