<?php

namespace App\Domains\Payments\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Payments\Services\PaymentService;
use App\Domains\Payments\Services\PaymentAnalyticsService;
use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\PaymentGateway;
use App\Domains\Payments\Models\DigitalWallet;
use App\Domains\Payments\Models\RecurringPayment;
use App\Domains\Payments\Models\CashOnDelivery;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * تحكم لوحة المدفوعات
 * يدير لوحة التحكم والتقارير والإحصائيات
 */
class PaymentDashboardController extends Controller
{
    protected PaymentService $paymentService;
    protected PaymentAnalyticsService $analyticsService;

    public function __construct(
        PaymentService $paymentService,
        PaymentAnalyticsService $analyticsService
    ) {
        $this->paymentService = $paymentService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * لوحة التحكم الرئيسية
     */
    public function dashboard(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $data = [
            'overview' => $this->getOverviewStats($dateRange),
            'transactions' => $this->getTransactionStats($dateRange),
            'gateways' => $this->getGatewayStats($dateRange),
            'currencies' => $this->getCurrencyStats($dateRange),
            'countries' => $this->getCountryStats($dateRange),
            'recent_transactions' => $this->getRecentTransactions(),
            'alerts' => $this->getSystemAlerts(),
            'performance' => $this->getPerformanceMetrics($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * إحصائيات المعاملات
     */
    public function transactionStats(Request $request): JsonResponse
    {
        $filters = $request->only([
            'date_from', 'date_to', 'currency', 'gateway_id', 
            'country', 'status', 'payment_method'
        ]);

        $stats = $this->paymentService->getPaymentStatistics($filters);
        $trends = $this->analyticsService->getTransactionTrends($filters);
        $breakdown = $this->analyticsService->getTransactionBreakdown($filters);

        return response()->json([
            'success' => true,
            'data' => [
                'statistics' => $stats,
                'trends' => $trends,
                'breakdown' => $breakdown,
            ],
        ]);
    }

    /**
     * تقرير الإيرادات
     */
    public function revenueReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day'); // day, week, month, year
        
        $revenue = $this->analyticsService->getRevenueReport($dateRange, $groupBy);
        $comparison = $this->analyticsService->getRevenueComparison($dateRange);
        $forecast = $this->analyticsService->getRevenueForecast($dateRange);

        return response()->json([
            'success' => true,
            'data' => [
                'revenue' => $revenue,
                'comparison' => $comparison,
                'forecast' => $forecast,
            ],
        ]);
    }

    /**
     * تقرير البوابات
     */
    public function gatewayReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $gatewayStats = PaymentGateway::with(['transactions' => function ($query) use ($dateRange) {
            $query->whereBetween('created_at', $dateRange);
        }])->get()->map(function ($gateway) {
            $transactions = $gateway->transactions;
            $successful = $transactions->where('status', 'succeeded');
            $total = $transactions->count();
            
            return [
                'id' => $gateway->id,
                'name' => $gateway->name,
                'provider' => $gateway->provider,
                'total_transactions' => $total,
                'successful_transactions' => $successful->count(),
                'success_rate' => $total > 0 ? round(($successful->count() / $total) * 100, 2) : 0,
                'total_amount' => $successful->sum('amount'),
                'total_fees' => $successful->sum('fees'),
                'average_amount' => $successful->count() > 0 ? round($successful->avg('amount'), 2) : 0,
                'countries' => $transactions->pluck('country_code')->unique()->values(),
                'currencies' => $transactions->pluck('currency')->unique()->values(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $gatewayStats,
        ]);
    }

    /**
     * تقرير المحافظ الرقمية
     */
    public function walletReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $walletStats = [
            'total_wallets' => DigitalWallet::count(),
            'active_wallets' => DigitalWallet::active()->count(),
            'total_balance' => DigitalWallet::sum('balance'),
            'total_available_balance' => DigitalWallet::sum('available_balance'),
            'total_reserved_balance' => DigitalWallet::sum('reserved_balance'),
            'currency_breakdown' => DigitalWallet::select('currency', DB::raw('COUNT(*) as count'), DB::raw('SUM(balance) as total_balance'))
                ->groupBy('currency')
                ->get(),
            'verification_levels' => DigitalWallet::select('verification_level', DB::raw('COUNT(*) as count'))
                ->groupBy('verification_level')
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $walletStats,
        ]);
    }

    /**
     * تقرير الاشتراكات
     */
    public function subscriptionReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $subscriptionStats = [
            'total_subscriptions' => RecurringPayment::count(),
            'active_subscriptions' => RecurringPayment::active()->count(),
            'trial_subscriptions' => RecurringPayment::inTrial()->count(),
            'past_due_subscriptions' => RecurringPayment::pastDue()->count(),
            'cancelled_subscriptions' => RecurringPayment::where('status', 'cancelled')->count(),
            'monthly_recurring_revenue' => RecurringPayment::active()
                ->where('interval', 'month')
                ->sum('amount'),
            'annual_recurring_revenue' => RecurringPayment::active()
                ->where('interval', 'year')
                ->sum('amount'),
            'churn_rate' => $this->calculateChurnRate($dateRange),
            'interval_breakdown' => RecurringPayment::select('interval', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total_amount'))
                ->groupBy('interval')
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $subscriptionStats,
        ]);
    }

    /**
     * تقرير الدفع عند الاستلام
     */
    public function codReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $codStats = [
            'total_orders' => CashOnDelivery::whereBetween('created_at', $dateRange)->count(),
            'delivered_orders' => CashOnDelivery::whereBetween('created_at', $dateRange)->delivered()->count(),
            'pending_orders' => CashOnDelivery::whereBetween('created_at', $dateRange)->where('status', 'pending')->count(),
            'failed_orders' => CashOnDelivery::whereBetween('created_at', $dateRange)->where('status', 'failed')->count(),
            'total_amount' => CashOnDelivery::whereBetween('created_at', $dateRange)->delivered()->sum('amount'),
            'total_delivery_fees' => CashOnDelivery::whereBetween('created_at', $dateRange)->delivered()->sum('delivery_fee'),
            'average_delivery_time' => $this->calculateAverageDeliveryTime($dateRange),
            'delivery_success_rate' => $this->calculateDeliverySuccessRate($dateRange),
            'status_breakdown' => CashOnDelivery::whereBetween('created_at', $dateRange)
                ->select('status', DB::raw('COUNT(*) as count'))
                ->groupBy('status')
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $codStats,
        ]);
    }

    /**
     * تقرير الاحتيال
     */
    public function fraudReport(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $fraudStats = [
            'total_transactions' => PaymentTransaction::whereBetween('created_at', $dateRange)->count(),
            'high_risk_transactions' => PaymentTransaction::whereBetween('created_at', $dateRange)->highRisk()->count(),
            'blocked_transactions' => PaymentTransaction::whereBetween('created_at', $dateRange)->where('status', 'blocked')->count(),
            'fraud_rate' => $this->calculateFraudRate($dateRange),
            'risk_distribution' => $this->getRiskDistribution($dateRange),
            'top_risk_factors' => $this->getTopRiskFactors($dateRange),
            'country_risk_analysis' => $this->getCountryRiskAnalysis($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $fraudStats,
        ]);
    }

    /**
     * تصدير التقارير
     */
    public function exportReport(Request $request): JsonResponse
    {
        $request->validate([
            'report_type' => 'required|in:transactions,revenue,gateways,wallets,subscriptions,cod,fraud',
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $exportData = $this->analyticsService->exportReport(
                $request->report_type,
                $request->format,
                $request->only(['date_from', 'date_to'])
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء التقرير بنجاح',
                'data' => $exportData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء التقرير: ' . $e->getMessage(),
            ], 400);
        }
    }

    /**
     * الحصول على نطاق التاريخ
     */
    protected function getDateRange(Request $request): array
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->startOfDay());
        $dateTo = $request->get('date_to', now()->endOfDay());
        
        return [Carbon::parse($dateFrom), Carbon::parse($dateTo)];
    }

    /**
     * إحصائيات عامة
     */
    protected function getOverviewStats(array $dateRange): array
    {
        $transactions = PaymentTransaction::whereBetween('created_at', $dateRange);
        
        return [
            'total_transactions' => $transactions->count(),
            'successful_transactions' => $transactions->clone()->successful()->count(),
            'total_amount' => $transactions->clone()->successful()->sum('amount'),
            'total_fees' => $transactions->clone()->successful()->sum('fees'),
            'unique_customers' => $transactions->clone()->distinct('user_id')->count('user_id'),
            'active_gateways' => PaymentGateway::active()->count(),
            'total_wallets' => DigitalWallet::count(),
            'active_subscriptions' => RecurringPayment::active()->count(),
        ];
    }

    /**
     * إحصائيات المعاملات
     */
    protected function getTransactionStats(array $dateRange): array
    {
        return [
            'hourly_volume' => $this->getHourlyTransactionVolume($dateRange),
            'payment_method_breakdown' => $this->getPaymentMethodBreakdown($dateRange),
            'status_breakdown' => $this->getTransactionStatusBreakdown($dateRange),
            'average_transaction_amount' => $this->getAverageTransactionAmount($dateRange),
        ];
    }

    /**
     * إحصائيات البوابات
     */
    protected function getGatewayStats(array $dateRange): array
    {
        return PaymentGateway::withCount(['transactions' => function ($query) use ($dateRange) {
            $query->whereBetween('created_at', $dateRange);
        }])->get()->map(function ($gateway) {
            return [
                'name' => $gateway->name,
                'transaction_count' => $gateway->transactions_count,
                'success_rate' => $this->calculateGatewaySuccessRate($gateway, $dateRange),
            ];
        })->toArray();
    }

    /**
     * إحصائيات العملات
     */
    protected function getCurrencyStats(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->successful()
            ->select('currency', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('currency')
            ->orderBy('total', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * إحصائيات الدول
     */
    protected function getCountryStats(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->successful()
            ->select('country_code', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('country_code')
            ->orderBy('total', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * المعاملات الحديثة
     */
    protected function getRecentTransactions(): array
    {
        return PaymentTransaction::with(['user', 'gateway'])
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'gateway' => $transaction->gateway->name ?? 'غير محدد',
                    'customer' => $transaction->customer_email,
                    'created_at' => $transaction->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * تنبيهات النظام
     */
    protected function getSystemAlerts(): array
    {
        $alerts = [];
        
        // تحقق من انخفاض معدل النجاح
        $successRate = $this->calculateOverallSuccessRate();
        if ($successRate < 85) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "معدل نجاح المعاملات منخفض: {$successRate}%",
                'action' => 'review_gateways',
            ];
        }
        
        // تحقق من المعاملات عالية المخاطر
        $highRiskCount = PaymentTransaction::where('created_at', '>=', now()->subHours(24))
            ->highRisk()
            ->count();
        
        if ($highRiskCount > 10) {
            $alerts[] = [
                'type' => 'danger',
                'message' => "عدد كبير من المعاملات عالية المخاطر: {$highRiskCount}",
                'action' => 'review_fraud_settings',
            ];
        }
        
        return $alerts;
    }

    /**
     * مقاييس الأداء
     */
    protected function getPerformanceMetrics(array $dateRange): array
    {
        return [
            'average_processing_time' => $this->calculateAverageProcessingTime($dateRange),
            'gateway_uptime' => $this->calculateGatewayUptime($dateRange),
            'error_rate' => $this->calculateErrorRate($dateRange),
            'peak_hours' => $this->getPeakHours($dateRange),
        ];
    }

    // Helper methods للحسابات المختلفة
    protected function calculateChurnRate(array $dateRange): float
    {
        // حساب معدل الإلغاء للاشتراكات
        $totalSubscriptions = RecurringPayment::whereBetween('created_at', $dateRange)->count();
        $cancelledSubscriptions = RecurringPayment::whereBetween('cancelled_at', $dateRange)->count();
        
        return $totalSubscriptions > 0 ? round(($cancelledSubscriptions / $totalSubscriptions) * 100, 2) : 0;
    }

    protected function calculateAverageDeliveryTime(array $dateRange): float
    {
        return CashOnDelivery::whereBetween('created_at', $dateRange)
            ->whereNotNull('delivered_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, delivered_at)) as avg_hours')
            ->value('avg_hours') ?? 0;
    }

    protected function calculateDeliverySuccessRate(array $dateRange): float
    {
        $total = CashOnDelivery::whereBetween('created_at', $dateRange)->count();
        $delivered = CashOnDelivery::whereBetween('created_at', $dateRange)->delivered()->count();
        
        return $total > 0 ? round(($delivered / $total) * 100, 2) : 0;
    }

    protected function calculateFraudRate(array $dateRange): float
    {
        $total = PaymentTransaction::whereBetween('created_at', $dateRange)->count();
        $highRisk = PaymentTransaction::whereBetween('created_at', $dateRange)->highRisk()->count();
        
        return $total > 0 ? round(($highRisk / $total) * 100, 2) : 0;
    }

    protected function getRiskDistribution(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->selectRaw('
                CASE 
                    WHEN risk_score >= 80 THEN "very_high"
                    WHEN risk_score >= 60 THEN "high" 
                    WHEN risk_score >= 40 THEN "medium"
                    WHEN risk_score >= 20 THEN "low"
                    ELSE "very_low"
                END as risk_level,
                COUNT(*) as count
            ')
            ->groupBy('risk_level')
            ->get()
            ->toArray();
    }

    protected function getTopRiskFactors(array $dateRange): array
    {
        // تحليل عوامل المخاطر الأكثر شيوعاً
        return [
            'high_risk_countries' => ['AF', 'IR', 'KP'],
            'suspicious_patterns' => ['rapid_transactions', 'unusual_amounts'],
            'device_risks' => ['vpn_usage', 'tor_usage'],
        ];
    }

    protected function getCountryRiskAnalysis(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->select('country_code', DB::raw('AVG(risk_score) as avg_risk_score'), DB::raw('COUNT(*) as count'))
            ->groupBy('country_code')
            ->orderBy('avg_risk_score', 'desc')
            ->get()
            ->toArray();
    }

    protected function calculateOverallSuccessRate(): float
    {
        $total = PaymentTransaction::where('created_at', '>=', now()->subDays(7))->count();
        $successful = PaymentTransaction::where('created_at', '>=', now()->subDays(7))->successful()->count();
        
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    protected function calculateAverageProcessingTime(array $dateRange): float
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->whereNotNull('processed_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, created_at, processed_at)) as avg_seconds')
            ->value('avg_seconds') ?? 0;
    }

    protected function calculateGatewayUptime(array $dateRange): float
    {
        // حساب وقت تشغيل البوابات (محاكاة)
        return 99.5;
    }

    protected function calculateErrorRate(array $dateRange): float
    {
        $total = PaymentTransaction::whereBetween('created_at', $dateRange)->count();
        $failed = PaymentTransaction::whereBetween('created_at', $dateRange)->failed()->count();
        
        return $total > 0 ? round(($failed / $total) * 100, 2) : 0;
    }

    protected function getPeakHours(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get()
            ->toArray();
    }

    protected function getHourlyTransactionVolume(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->toArray();
    }

    protected function getPaymentMethodBreakdown(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->orderBy('total', 'desc')
            ->get()
            ->toArray();
    }

    protected function getTransactionStatusBreakdown(array $dateRange): array
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->toArray();
    }

    protected function getAverageTransactionAmount(array $dateRange): float
    {
        return PaymentTransaction::whereBetween('created_at', $dateRange)
            ->successful()
            ->avg('amount') ?? 0;
    }

    protected function calculateGatewaySuccessRate(PaymentGateway $gateway, array $dateRange): float
    {
        $total = $gateway->transactions()->whereBetween('created_at', $dateRange)->count();
        $successful = $gateway->transactions()->whereBetween('created_at', $dateRange)->successful()->count();
        
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }
}
