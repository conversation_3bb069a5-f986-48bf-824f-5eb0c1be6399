<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - حسابي AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <!-- Location Confirmation Modal -->
    <div id="location-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <i class="fas fa-map-marker-alt text-4xl text-purple-600 mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-800 mb-2">تأكيد الموقع</h3>
                <p class="text-gray-600">يرجى تأكيد موقعك الحالي أو اختيار دولة أخرى</p>
            </div>
            
            <div class="space-y-4">
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('SA')">
                    <div class="flex items-center">
                        <img src="/images/flags/sa.png" alt="السعودية" class="w-8 h-6 mr-3">
                        <span class="font-semibold">المملكة العربية السعودية</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('AE')">
                    <div class="flex items-center">
                        <img src="/images/flags/ae.png" alt="الإمارات" class="w-8 h-6 mr-3">
                        <span class="font-semibold">دولة الإمارات العربية المتحدة</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('MA')">
                    <div class="flex items-center">
                        <img src="/images/flags/ma.png" alt="المغرب" class="w-8 h-6 mr-3">
                        <span class="font-semibold">المملكة المغربية</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('EG')">
                    <div class="flex items-center">
                        <img src="/images/flags/eg.png" alt="مصر" class="w-8 h-6 mr-3">
                        <span class="font-semibold">جمهورية مصر العربية</span>
                    </div>
                </div>
                
                <select class="w-full p-4 border border-gray-200 rounded-lg" onchange="selectCountry(this.value)">
                    <option value="">اختر دولة أخرى...</option>
                    <option value="JO">الأردن</option>
                    <option value="KW">الكويت</option>
                    <option value="QA">قطر</option>
                    <option value="BH">البحرين</option>
                    <option value="OM">عمان</option>
                    <option value="LB">لبنان</option>
                    <option value="SY">سوريا</option>
                    <option value="IQ">العراق</option>
                    <option value="YE">اليمن</option>
                    <option value="LY">ليبيا</option>
                    <option value="TN">تونس</option>
                    <option value="DZ">الجزائر</option>
                    <option value="SD">السودان</option>
                </select>
            </div>
        </div>
    </div>

    <div class="w-full max-w-md">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center space-x-4 space-x-reverse mb-4">
                <img src="/images/logo.png" alt="حسابي AI" class="h-12 w-12">
                <span class="text-3xl font-bold text-white">حسابي AI</span>
            </div>
            <p class="text-white opacity-90">مرحباً بك مرة أخرى</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-xl shadow-2xl p-8">
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-8">تسجيل الدخول</h2>
            
            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}" class="space-y-6">
                @csrf
                
                <div>
                    <label for="email" class="block text-gray-700 font-semibold mb-2">البريد الإلكتروني</label>
                    <div class="relative">
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="{{ old('email') }}"
                               class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                               placeholder="أدخل بريدك الإلكتروني"
                               required>
                        <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-gray-700 font-semibold mb-2">كلمة المرور</label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                               placeholder="أدخل كلمة المرور"
                               required>
                        <i class="fas fa-lock absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <button type="button" onclick="togglePassword()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="password-toggle" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember" class="rounded border-gray-300 text-purple-600 shadow-sm focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50">
                        <span class="mr-2 text-gray-700">تذكرني</span>
                    </label>
                    
                    @if (Route::has('password.request'))
                        <a href="{{ route('password.request') }}" class="text-purple-600 hover:text-purple-800 text-sm">
                            نسيت كلمة المرور؟
                        </a>
                    @endif
                </div>

                <button type="submit" class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition duration-300">
                    تسجيل الدخول
                </button>
            </form>

            <!-- Demo Admin Credentials -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">بيانات المشرف التجريبية:</h4>
                <div class="text-sm text-blue-700 space-y-1">
                    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>كلمة المرور:</strong> Colorado2020@</p>
                </div>
                <button type="button" onclick="fillAdminCredentials()" class="mt-2 text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition duration-300">
                    استخدام بيانات المشرف
                </button>
            </div>

            <!-- Social Login -->
            <div class="mt-8">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">أو سجل الدخول باستخدام</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fab fa-google text-red-500 ml-2"></i>
                        Google
                    </button>
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fab fa-microsoft text-blue-500 ml-2"></i>
                        Microsoft
                    </button>
                </div>
            </div>

            <!-- Register Link -->
            <div class="mt-8 text-center">
                <p class="text-gray-600">
                    ليس لديك حساب؟ 
                    <a href="{{ route('register') }}" class="text-purple-600 hover:text-purple-800 font-semibold">
                        سجل الآن
                    </a>
                </p>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-6">
            <a href="{{ route('welcome') }}" class="text-white hover:text-gray-200 transition duration-300">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <script>
        // Show location modal on page load
        window.addEventListener('load', function() {
            document.getElementById('location-modal').style.display = 'flex';
        });

        // Select country function
        function selectCountry(countryCode) {
            // Store selected country
            localStorage.setItem('selectedCountry', countryCode);
            
            // Hide modal
            document.getElementById('location-modal').style.display = 'none';
            
            // You can add logic here to update currency, language, etc.
            console.log('Selected country:', countryCode);
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Fill admin credentials
        function fillAdminCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'Colorado2020@';
        }

        // Check if country was already selected
        if (localStorage.getItem('selectedCountry')) {
            document.getElementById('location-modal').style.display = 'none';
        }
    </script>
</body>
</html>
