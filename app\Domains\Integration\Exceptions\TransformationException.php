<?php

namespace App\Domains\Integration\Exceptions;

use Exception;

/**
 * Transformation Exception
 * Thrown when data transformation fails
 */
class TransformationException extends Exception
{
    protected array $context;
    protected string $transformationType;

    public function __construct(
        string $message = 'Data transformation failed',
        string $transformationType = '',
        array $context = [],
        int $code = 0,
        Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->transformationType = $transformationType;
        $this->context = $context;
    }

    /**
     * Get transformation type that failed
     */
    public function getTransformationType(): string
    {
        return $this->transformationType;
    }

    /**
     * Get transformation context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'transformation_type' => $this->transformationType,
            'context' => $this->context,
            'code' => $this->getCode(),
        ];
    }
}
