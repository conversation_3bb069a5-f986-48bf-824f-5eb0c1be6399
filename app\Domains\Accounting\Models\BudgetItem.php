<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج بند الميزانية
 */
class BudgetItem extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'budget_id',
        'account_id',
        'item_code',
        'description',
        'category',
        'budgeted_amount',
        'allocated_amount',
        'actual_amount',
        'committed_amount',
        'available_amount',
        'variance_amount',
        'variance_percentage',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'budgeted_amount' => 'decimal:2',
        'allocated_amount' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'committed_amount' => 'decimal:2',
        'available_amount' => 'decimal:2',
        'variance_amount' => 'decimal:2',
        'variance_percentage' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * فئات البنود
     */
    public const CATEGORIES = [
        'REVENUE' => 'إيرادات',
        'SALARIES' => 'رواتب',
        'BENEFITS' => 'مزايا',
        'OFFICE_EXPENSES' => 'مصاريف مكتبية',
        'TRAVEL' => 'سفر',
        'MARKETING' => 'تسويق',
        'UTILITIES' => 'مرافق',
        'RENT' => 'إيجار',
        'EQUIPMENT' => 'معدات',
        'SOFTWARE' => 'برمجيات',
        'TRAINING' => 'تدريب',
        'CONSULTING' => 'استشارات',
        'OTHER' => 'أخرى',
    ];

    /**
     * الميزانية
     */
    public function budget(): BelongsTo
    {
        return $this->belongsTo(Budget::class);
    }

    /**
     * الحساب المحاسبي
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * حساب المجاميع عند الحفظ
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->calculateVariances();
        });

        static::saved(function ($item) {
            $item->budget->calculateTotals();
            $item->budget->save();
        });
    }

    /**
     * حساب الانحرافات
     */
    public function calculateVariances(): void
    {
        $this->variance_amount = $this->actual_amount - $this->budgeted_amount;
        $this->variance_percentage = $this->budgeted_amount > 0
            ? ($this->variance_amount / $this->budgeted_amount) * 100
            : 0;

        $this->available_amount = $this->budgeted_amount - $this->actual_amount - $this->committed_amount;
    }

    /**
     * التحقق من توفر المبلغ
     */
    public function hasAvailableAmount(float $amount): bool
    {
        return $this->available_amount >= $amount;
    }

    /**
     * حجز مبلغ
     */
    public function commitAmount(float $amount): bool
    {
        if ($this->hasAvailableAmount($amount)) {
            $this->committed_amount += $amount;
            $this->calculateVariances();
            return $this->save();
        }

        return false;
    }

    /**
     * إلغاء حجز مبلغ
     */
    public function uncommitAmount(float $amount): bool
    {
        if ($this->committed_amount >= $amount) {
            $this->committed_amount -= $amount;
            $this->calculateVariances();
            return $this->save();
        }

        return false;
    }

    /**
     * إنفاق مبلغ
     */
    public function spendAmount(float $amount): bool
    {
        $this->actual_amount += $amount;
        $this->calculateVariances();
        return $this->save();
    }

    /**
     * الحصول على حالة البند
     */
    public function getStatus(): string
    {
        $utilizationPercentage = $this->budgeted_amount > 0
            ? ($this->actual_amount / $this->budgeted_amount) * 100
            : 0;

        if ($utilizationPercentage > 100) {
            return 'متجاوز';
        } elseif ($utilizationPercentage > 90) {
            return 'قريب من النفاد';
        } elseif ($utilizationPercentage > 50) {
            return 'طبيعي';
        } else {
            return 'منخفض الاستخدام';
        }
    }

    /**
     * نطاق حسب الفئة
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * نطاق للبنود المتجاوزة
     */
    public function scopeOverBudget($query)
    {
        return $query->whereRaw('actual_amount > budgeted_amount');
    }

    /**
     * نطاق للبنود تحت الميزانية
     */
    public function scopeUnderBudget($query)
    {
        return $query->whereRaw('actual_amount < budgeted_amount');
    }
}
