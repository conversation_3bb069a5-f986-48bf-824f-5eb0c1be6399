<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج استبيان الرضا - Satisfaction Survey
 */
class SatisfactionSurvey extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'type',
        'ticket_id',
        'chat_id',
        'customer_id',
        'survey_token',
        'questions',
        'responses',
        'status',
        'sent_at',
        'completed_at',
        'expires_at',
        'reminder_sent_at',
    ];

    protected $casts = [
        'questions' => 'array',
        'responses' => 'array',
        'sent_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
    ];

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    public function chat(): BelongsTo
    {
        return $this->belongsTo(LiveChat::class, 'chat_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\CRM\Models\Customer::class);
    }

    public function getPublicUrlAttribute(): string
    {
        return route('satisfaction.survey', ['token' => $this->survey_token]);
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at);
    }

    public function getOverallRatingAttribute(): ?int
    {
        $responses = $this->responses ?? [];
        return $responses['overall_satisfaction'] ?? $responses['chat_experience'] ?? $responses['nps_score'] ?? null;
    }
}
