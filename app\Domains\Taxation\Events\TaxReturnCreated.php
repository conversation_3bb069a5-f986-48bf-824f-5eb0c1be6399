<?php

namespace App\Domains\Taxation\Events;

use App\Domains\Taxation\Models\TaxReturn;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنشاء إقرار ضريبي جديد
 * يتم إطلاقه عند إنشاء إقرار ضريبي جديد في النظام
 */
class TaxReturnCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public TaxReturn $taxReturn;
    public array $metadata;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(TaxReturn $taxReturn, array $metadata = [])
    {
        $this->taxReturn = $taxReturn;
        $this->metadata = array_merge([
            'created_at' => now(),
            'created_by' => auth()->user()?->name ?? 'System',
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ], $metadata);
    }

    /**
     * الحصول على القنوات التي يجب بث الحدث عليها
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('tax-returns'),
            new PrivateChannel('tax-returns.' . $this->taxReturn->company_id),
            new PrivateChannel('user.' . $this->taxReturn->created_by),
            new Channel('tax-system.' . $this->taxReturn->tax_system_id),
        ];
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'tax-return.created';
    }

    /**
     * البيانات التي يتم بثها مع الحدث
     */
    public function broadcastWith(): array
    {
        return [
            'tax_return' => [
                'id' => $this->taxReturn->id,
                'uuid' => $this->taxReturn->uuid,
                'return_number' => $this->taxReturn->return_number,
                'type' => $this->taxReturn->type,
                'status' => $this->taxReturn->status,
                'company_id' => $this->taxReturn->company_id,
                'company_name' => $this->taxReturn->company?->name,
                'tax_system_id' => $this->taxReturn->tax_system_id,
                'tax_system_name' => $this->taxReturn->taxSystem?->name,
                'tax_period_from' => $this->taxReturn->tax_period_from?->format('Y-m-d'),
                'tax_period_to' => $this->taxReturn->tax_period_to?->format('Y-m-d'),
                'due_date' => $this->taxReturn->due_date?->format('Y-m-d'),
                'total_tax_amount' => $this->taxReturn->total_tax_amount,
                'currency' => $this->taxReturn->taxSystem?->currency,
                'created_at' => $this->taxReturn->created_at?->format('Y-m-d H:i:s'),
            ],
            'metadata' => $this->metadata,
            'message' => "تم إنشاء إقرار ضريبي جديد رقم {$this->taxReturn->return_number}",
            'notification_type' => 'success',
            'action_required' => $this->getActionRequired(),
            'next_steps' => $this->getNextSteps(),
        ];
    }

    /**
     * تحديد ما إذا كان يجب بث الحدث
     */
    public function shouldBroadcast(): bool
    {
        // بث الحدث فقط للإقرارات المهمة أو للمستخدمين المخولين
        return $this->taxReturn->total_tax_amount > 0 || 
               auth()->user()?->can('receive-tax-notifications');
    }

    /**
     * الحصول على الإجراءات المطلوبة
     */
    protected function getActionRequired(): array
    {
        $actions = [];

        // إذا لم يتم حساب الضرائب بعد
        if (!$this->taxReturn->calculated_at) {
            $actions[] = [
                'type' => 'calculate_taxes',
                'title' => 'حساب الضرائب',
                'description' => 'يجب حساب الضرائب لهذا الإقرار',
                'priority' => 'high',
                'url' => route('tax-returns.calculate', $this->taxReturn->id),
            ];
        }

        // إذا كان تاريخ الاستحقاق قريب
        if ($this->taxReturn->due_date && $this->taxReturn->due_date->diffInDays(now()) <= 7) {
            $actions[] = [
                'type' => 'urgent_submission',
                'title' => 'تقديم عاجل',
                'description' => 'تاريخ الاستحقاق قريب - يجب التقديم قريباً',
                'priority' => 'urgent',
                'days_remaining' => $this->taxReturn->due_date->diffInDays(now()),
            ];
        }

        // إذا كانت هناك مستندات مطلوبة
        $requiredDocuments = $this->getRequiredDocuments();
        if (!empty($requiredDocuments)) {
            $actions[] = [
                'type' => 'upload_documents',
                'title' => 'رفع المستندات',
                'description' => 'يجب رفع المستندات المطلوبة',
                'priority' => 'medium',
                'required_documents' => $requiredDocuments,
            ];
        }

        return $actions;
    }

    /**
     * الحصول على الخطوات التالية
     */
    protected function getNextSteps(): array
    {
        return [
            [
                'step' => 1,
                'title' => 'مراجعة البيانات',
                'description' => 'تأكد من صحة جميع البيانات المدخلة',
                'estimated_time' => '10 دقائق',
            ],
            [
                'step' => 2,
                'title' => 'حساب الضرائب',
                'description' => 'احسب الضرائب المستحقة',
                'estimated_time' => '5 دقائق',
            ],
            [
                'step' => 3,
                'title' => 'رفع المستندات',
                'description' => 'ارفع جميع المستندات المطلوبة',
                'estimated_time' => '15 دقيقة',
            ],
            [
                'step' => 4,
                'title' => 'التحقق والتقديم',
                'description' => 'تحقق من الإقرار وقدمه للهيئة الضريبية',
                'estimated_time' => '10 دقائق',
            ],
        ];
    }

    /**
     * الحصول على المستندات المطلوبة
     */
    protected function getRequiredDocuments(): array
    {
        $documents = [];

        if ($this->taxReturn->vat_applicable) {
            $documents[] = 'سجلات ضريبة القيمة المضافة';
        }

        if ($this->taxReturn->corporate_tax_applicable) {
            $documents[] = 'القوائم المالية';
            $documents[] = 'قائمة الدخل';
        }

        if ($this->taxReturn->withholding_tax_applicable) {
            $documents[] = 'شهادات الاستقطاع';
        }

        return $documents;
    }

    /**
     * الحصول على معلومات إضافية للحدث
     */
    public function getEventInfo(): array
    {
        return [
            'event_type' => 'tax_return_created',
            'event_category' => 'taxation',
            'severity' => $this->getEventSeverity(),
            'affects_compliance' => $this->affectsCompliance(),
            'requires_approval' => $this->requiresApproval(),
            'estimated_completion_time' => $this->getEstimatedCompletionTime(),
            'related_deadlines' => $this->getRelatedDeadlines(),
        ];
    }

    /**
     * تحديد شدة الحدث
     */
    protected function getEventSeverity(): string
    {
        if ($this->taxReturn->due_date && $this->taxReturn->due_date->diffInDays(now()) <= 3) {
            return 'critical';
        }

        if ($this->taxReturn->total_tax_amount > 100000) {
            return 'high';
        }

        if ($this->taxReturn->due_date && $this->taxReturn->due_date->diffInDays(now()) <= 7) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * تحديد ما إذا كان الحدث يؤثر على الامتثال
     */
    protected function affectsCompliance(): bool
    {
        return $this->taxReturn->due_date && $this->taxReturn->due_date <= now()->addDays(30);
    }

    /**
     * تحديد ما إذا كان الحدث يتطلب موافقة
     */
    protected function requiresApproval(): bool
    {
        return $this->taxReturn->total_tax_amount > 50000 || 
               $this->taxReturn->type === 'ANNUAL_CORPORATE_TAX';
    }

    /**
     * تقدير وقت الإنجاز
     */
    protected function getEstimatedCompletionTime(): string
    {
        $baseTime = 30; // دقائق

        if ($this->taxReturn->vat_applicable) {
            $baseTime += 15;
        }

        if ($this->taxReturn->corporate_tax_applicable) {
            $baseTime += 30;
        }

        if ($this->taxReturn->withholding_tax_applicable) {
            $baseTime += 20;
        }

        return $baseTime . ' دقيقة';
    }

    /**
     * الحصول على المواعيد النهائية ذات الصلة
     */
    protected function getRelatedDeadlines(): array
    {
        $deadlines = [];

        if ($this->taxReturn->due_date) {
            $deadlines[] = [
                'type' => 'submission_deadline',
                'date' => $this->taxReturn->due_date->format('Y-m-d'),
                'description' => 'الموعد النهائي لتقديم الإقرار',
                'days_remaining' => $this->taxReturn->due_date->diffInDays(now()),
            ];
        }

        if ($this->taxReturn->payment_due_date) {
            $deadlines[] = [
                'type' => 'payment_deadline',
                'date' => $this->taxReturn->payment_due_date->format('Y-m-d'),
                'description' => 'الموعد النهائي لدفع الضرائب',
                'days_remaining' => $this->taxReturn->payment_due_date->diffInDays(now()),
            ];
        }

        return $deadlines;
    }

    /**
     * تحويل الحدث إلى مصفوفة للتسجيل
     */
    public function toArray(): array
    {
        return [
            'event' => 'TaxReturnCreated',
            'tax_return_id' => $this->taxReturn->id,
            'return_number' => $this->taxReturn->return_number,
            'type' => $this->taxReturn->type,
            'company_id' => $this->taxReturn->company_id,
            'tax_system_id' => $this->taxReturn->tax_system_id,
            'total_amount' => $this->taxReturn->total_tax_amount,
            'due_date' => $this->taxReturn->due_date?->format('Y-m-d'),
            'metadata' => $this->metadata,
            'event_info' => $this->getEventInfo(),
            'timestamp' => now()->toISOString(),
        ];
    }
}
