<?php

namespace App\Domains\Taxation\Services;

use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Models\TaxReturn;
use App\Domains\Accounting\Models\Invoice;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * خدمة التكامل مع الهيئات الضريبية
 * تدعم التكامل مع الإدارة العامة للضرائب المغربية وهيئات أخرى
 */
class TaxAuthorityIntegrationService
{
    protected TaxSystem $taxSystem;
    protected array $config;

    public function __construct(?TaxSystem $taxSystem = null)
    {
        $this->taxSystem = $taxSystem ?? TaxSystem::getDefault();
        $this->config = $this->loadConfiguration();
    }

    /**
     * تحميل إعدادات التكامل
     */
    protected function loadConfiguration(): array
    {
        $endpoints = $this->taxSystem->getAPIEndpoints();
        
        return [
            'base_url' => $endpoints['base_url'] ?? '',
            'auth_endpoint' => $endpoints['auth'] ?? '',
            'invoice_submission' => $endpoints['invoice_submission'] ?? '',
            'tax_return_submission' => $endpoints['tax_return_submission'] ?? '',
            'status_check' => $endpoints['status_check'] ?? '',
            'certificate_path' => $endpoints['certificate_path'] ?? '',
            'private_key_path' => $endpoints['private_key_path'] ?? '',
            'timeout' => $endpoints['timeout'] ?? 30,
            'retry_attempts' => $endpoints['retry_attempts'] ?? 3,
        ];
    }

    /**
     * إرسال فاتورة إلكترونية
     */
    public function submitElectronicInvoice(Invoice $invoice): array
    {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateInvoiceForSubmission($invoice);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                ];
            }

            // تحضير البيانات
            $invoiceData = $this->prepareInvoiceData($invoice);

            // إضافة التوقيع الرقمي
            $invoiceData['digital_signature'] = $this->generateDigitalSignature($invoiceData);

            // إرسال للهيئة الضريبية
            $response = $this->sendToTaxAuthority('invoice_submission', $invoiceData);

            if ($response['success']) {
                // تحديث الفاتورة
                $invoice->update([
                    'e_invoice_uuid' => $response['data']['uuid'] ?? null,
                    'tax_authority_status' => 'SUBMITTED',
                    'submitted_at' => now(),
                    'qr_code' => $this->generateQRCode($invoice, $response['data']),
                ]);

                return [
                    'success' => true,
                    'uuid' => $response['data']['uuid'] ?? null,
                    'qr_code' => $invoice->qr_code,
                    'submission_date' => now(),
                ];
            }

            return [
                'success' => false,
                'errors' => $response['errors'] ?? ['فشل في الإرسال'],
            ];

        } catch (\Exception $e) {
            Log::error('خطأ في إرسال الفاتورة الإلكترونية', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * إرسال إقرار ضريبي
     */
    public function submitTaxReturn(TaxReturn $taxReturn): array
    {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateTaxReturnForSubmission($taxReturn);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                ];
            }

            // تحضير البيانات
            $returnData = $this->prepareTaxReturnData($taxReturn);

            // إضافة التوقيع الرقمي
            $returnData['digital_signature'] = $this->generateDigitalSignature($returnData);

            // إرسال للهيئة الضريبية
            $response = $this->sendToTaxAuthority('tax_return_submission', $returnData);

            if ($response['success']) {
                // تحديث الإقرار
                $taxReturn->update([
                    'status' => 'SUBMITTED',
                    'filing_date' => now(),
                    'reference_number' => $response['data']['reference_number'] ?? null,
                    'acknowledgment_number' => $response['data']['acknowledgment_number'] ?? null,
                ]);

                return [
                    'success' => true,
                    'reference_number' => $taxReturn->reference_number,
                    'acknowledgment_number' => $taxReturn->acknowledgment_number,
                    'filing_date' => $taxReturn->filing_date,
                ];
            }

            return [
                'success' => false,
                'errors' => $response['errors'] ?? ['فشل في الإرسال'],
            ];

        } catch (\Exception $e) {
            Log::error('خطأ في إرسال الإقرار الضريبي', [
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * التحقق من حالة الإرسال
     */
    public function checkSubmissionStatus(string $referenceNumber, string $type = 'invoice'): array
    {
        try {
            $response = $this->sendToTaxAuthority('status_check', [
                'reference_number' => $referenceNumber,
                'type' => $type,
            ]);

            if ($response['success']) {
                return [
                    'success' => true,
                    'status' => $response['data']['status'] ?? 'UNKNOWN',
                    'message' => $response['data']['message'] ?? '',
                    'last_updated' => $response['data']['last_updated'] ?? null,
                ];
            }

            return [
                'success' => false,
                'errors' => $response['errors'] ?? ['فشل في التحقق من الحالة'],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * تحضير بيانات الفاتورة
     */
    protected function prepareInvoiceData(Invoice $invoice): array
    {
        return [
            'invoice_info' => [
                'number' => $invoice->invoice_number,
                'date' => $invoice->invoice_date->format('Y-m-d'),
                'due_date' => $invoice->due_date->format('Y-m-d'),
                'currency' => $invoice->currency,
                'type' => $invoice->invoice_type,
            ],
            'seller_info' => [
                'tax_id' => $invoice->company->tax_id ?? '',
                'name' => $invoice->company->name ?? '',
                'address' => $invoice->company->address ?? '',
                'city' => $invoice->company->city ?? '',
                'country' => $invoice->company->country ?? 'MA',
            ],
            'buyer_info' => [
                'tax_id' => $invoice->customer->tax_id ?? '',
                'name' => $invoice->customer->name ?? '',
                'address' => $invoice->customer->address ?? '',
                'city' => $invoice->customer->city ?? '',
                'country' => $invoice->customer->country ?? 'MA',
            ],
            'line_items' => $invoice->items->map(function ($item) {
                return [
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'discount_amount' => $item->discount_amount,
                    'tax_rate' => $item->tax_rate,
                    'tax_amount' => $item->tax_amount,
                    'line_total' => $item->line_total,
                ];
            })->toArray(),
            'totals' => [
                'subtotal' => $invoice->subtotal,
                'tax_amount' => $invoice->tax_amount,
                'discount_amount' => $invoice->discount_amount,
                'total_amount' => $invoice->total_amount,
            ],
            'metadata' => [
                'submission_timestamp' => now()->toISOString(),
                'system_version' => config('app.version', '1.0'),
            ],
        ];
    }

    /**
     * تحضير بيانات الإقرار الضريبي
     */
    protected function prepareTaxReturnData(TaxReturn $taxReturn): array
    {
        return [
            'return_info' => [
                'type' => $taxReturn->return_type,
                'tax_year' => $taxReturn->tax_year,
                'period_start' => $taxReturn->period_start_date->format('Y-m-d'),
                'period_end' => $taxReturn->period_end_date->format('Y-m-d'),
                'currency' => 'MAD',
            ],
            'taxpayer_info' => [
                'tax_id' => $taxReturn->company->tax_id ?? '',
                'name' => $taxReturn->company->name ?? '',
                'address' => $taxReturn->company->address ?? '',
                'business_type' => $taxReturn->company->business_type ?? '',
            ],
            'financial_data' => [
                'total_revenue' => $taxReturn->total_revenue,
                'total_expenses' => $taxReturn->total_expenses,
                'taxable_income' => $taxReturn->taxable_income,
                'tax_calculated' => $taxReturn->tax_calculated,
                'tax_paid' => $taxReturn->tax_paid,
                'tax_due' => $taxReturn->tax_due,
            ],
            'line_items' => $taxReturn->items->map(function ($item) {
                return $item->toSubmissionArray();
            })->toArray(),
            'metadata' => [
                'submission_timestamp' => now()->toISOString(),
                'system_version' => config('app.version', '1.0'),
                'preparation_method' => 'AUTOMATED',
            ],
        ];
    }

    /**
     * إرسال البيانات للهيئة الضريبية
     */
    protected function sendToTaxAuthority(string $endpoint, array $data): array
    {
        $url = $this->config['base_url'] . '/' . $this->config[$endpoint];
        
        if (!$url || $url === '/') {
            return [
                'success' => false,
                'errors' => ['نقطة النهاية غير محددة'],
            ];
        }

        try {
            // الحصول على رمز المصادقة
            $authToken = $this->getAuthToken();
            if (!$authToken) {
                return [
                    'success' => false,
                    'errors' => ['فشل في المصادقة'],
                ];
            }

            // إرسال الطلب
            $response = Http::timeout($this->config['timeout'])
                ->withHeaders([
                    'Authorization' => "Bearer {$authToken}",
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->withOptions([
                    'verify' => $this->config['certificate_path'] ?? false,
                ])
                ->retry($this->config['retry_attempts'], 1000)
                ->post($url, $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'errors' => [
                    'HTTP ' . $response->status() . ': ' . $response->body()
                ],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * الحصول على رمز المصادقة
     */
    protected function getAuthToken(): ?string
    {
        $cacheKey = "tax_authority_token_{$this->taxSystem->id}";
        
        return Cache::remember($cacheKey, 3600, function () {
            try {
                $response = Http::timeout(30)
                    ->post($this->config['auth_endpoint'], [
                        'client_id' => config('taxation.client_id'),
                        'client_secret' => config('taxation.client_secret'),
                        'grant_type' => 'client_credentials',
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['access_token'] ?? null;
                }

                return null;
            } catch (\Exception $e) {
                Log::error('فشل في الحصول على رمز المصادقة', [
                    'error' => $e->getMessage(),
                ]);
                return null;
            }
        });
    }

    /**
     * توليد التوقيع الرقمي
     */
    protected function generateDigitalSignature(array $data): string
    {
        $dataString = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        
        // إذا كان هناك مفتاح خاص، استخدمه للتوقيع
        if ($this->config['private_key_path'] && file_exists($this->config['private_key_path'])) {
            $privateKey = openssl_pkey_get_private(file_get_contents($this->config['private_key_path']));
            openssl_sign($dataString, $signature, $privateKey, OPENSSL_ALGO_SHA256);
            return base64_encode($signature);
        }

        // توقيع بسيط باستخدام HMAC
        return hash_hmac('sha256', $dataString, config('app.key'));
    }

    /**
     * توليد QR Code للفاتورة
     */
    protected function generateQRCode(Invoice $invoice, array $responseData): string
    {
        $qrData = [
            'seller_name' => $invoice->company->name ?? '',
            'tax_id' => $invoice->company->tax_id ?? '',
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            'total_amount' => $invoice->total_amount,
            'tax_amount' => $invoice->tax_amount,
            'uuid' => $responseData['uuid'] ?? '',
        ];

        return base64_encode(json_encode($qrData));
    }

    /**
     * التحقق من صحة الفاتورة للإرسال
     */
    protected function validateInvoiceForSubmission(Invoice $invoice): array
    {
        $errors = [];

        if (!$invoice->customer_id) {
            $errors[] = 'معرف العميل مطلوب';
        }

        if (!$invoice->invoice_number) {
            $errors[] = 'رقم الفاتورة مطلوب';
        }

        if ($invoice->items()->count() === 0) {
            $errors[] = 'يجب إضافة بنود للفاتورة';
        }

        if (!$invoice->company->tax_id) {
            $errors[] = 'الرقم الضريبي للشركة مطلوب';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * التحقق من صحة الإقرار للإرسال
     */
    protected function validateTaxReturnForSubmission(TaxReturn $taxReturn): array
    {
        $errors = [];

        if (!$taxReturn->company_id) {
            $errors[] = 'معرف الشركة مطلوب';
        }

        if (!$taxReturn->tax_calculated && $taxReturn->tax_calculated !== 0) {
            $errors[] = 'يجب حساب الضريبة أولاً';
        }

        if (!$taxReturn->company->tax_id) {
            $errors[] = 'الرقم الضريبي للشركة مطلوب';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * مزامنة حالة الإرسالات
     */
    public function syncSubmissionStatuses(): array
    {
        $results = [
            'invoices_updated' => 0,
            'tax_returns_updated' => 0,
            'errors' => [],
        ];

        // مزامنة الفواتير
        $pendingInvoices = Invoice::where('tax_authority_status', 'SUBMITTED')
            ->whereNotNull('e_invoice_uuid')
            ->get();

        foreach ($pendingInvoices as $invoice) {
            try {
                $status = $this->checkSubmissionStatus($invoice->e_invoice_uuid, 'invoice');
                if ($status['success']) {
                    $invoice->update(['tax_authority_status' => $status['status']]);
                    $results['invoices_updated']++;
                }
            } catch (\Exception $e) {
                $results['errors'][] = "فاتورة {$invoice->invoice_number}: {$e->getMessage()}";
            }
        }

        // مزامنة الإقرارات
        $pendingReturns = TaxReturn::where('status', 'SUBMITTED')
            ->whereNotNull('reference_number')
            ->get();

        foreach ($pendingReturns as $taxReturn) {
            try {
                $status = $this->checkSubmissionStatus($taxReturn->reference_number, 'tax_return');
                if ($status['success']) {
                    $taxReturn->update(['status' => $status['status']]);
                    $results['tax_returns_updated']++;
                }
            } catch (\Exception $e) {
                $results['errors'][] = "إقرار {$taxReturn->reference_number}: {$e->getMessage()}";
            }
        }

        return $results;
    }
}
