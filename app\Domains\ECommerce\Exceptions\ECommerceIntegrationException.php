<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء التكامل مع منصات التجارة الإلكترونية
 */
class ECommerceIntegrationException extends ECommerceException
{
    protected string $integrationId = '';
    protected string $platformName = '';
    protected string $operation = '';

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $integrationId = '',
        string $platformName = '',
        string $operation = '',
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->integrationId = $integrationId;
        $this->platformName = $platformName;
        $this->operation = $operation;
        $this->errorCode = 'INTEGRATION_ERROR';
    }

    /**
     * الحصول على معرف التكامل
     */
    public function getIntegrationId(): string
    {
        return $this->integrationId;
    }

    /**
     * الحصول على اسم المنصة
     */
    public function getPlatformName(): string
    {
        return $this->platformName;
    }

    /**
     * الحصول على العملية
     */
    public function getOperation(): string
    {
        return $this->operation;
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'integration_id' => $this->getIntegrationId(),
            'platform_name' => $this->getPlatformName(),
            'operation' => $this->getOperation(),
        ]);
    }
}
