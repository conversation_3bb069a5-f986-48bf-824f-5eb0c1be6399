<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الملحمة - Epic Management for Large Features
 * يدير المهام الكبيرة والمعقدة التي تتطلب عدة سبرنتات
 */
class Epic extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'epic_key',
        'status',
        'priority',
        'start_date',
        'target_date',
        'completed_date',
        'owner_id',
        'business_value',
        'acceptance_criteria',
        'success_metrics',
        'estimated_story_points',
        'actual_story_points',
        'progress_percentage',
        'color',
        'labels',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'target_date' => 'date',
        'completed_date' => 'date',
        'business_value' => 'integer',
        'estimated_story_points' => 'integer',
        'actual_story_points' => 'integer',
        'progress_percentage' => 'decimal:2',
        'acceptance_criteria' => 'array',
        'success_metrics' => 'array',
        'labels' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المالك
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'owner_id');
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * العلاقة مع القصص (User Stories)
     */
    public function userStories(): HasMany
    {
        return $this->hasMany(Task::class)->where('type', 'USER_STORY');
    }

    /**
     * توليد مفتاح الملحمة
     */
    public static function generateEpicKey(int $projectId): string
    {
        $project = Project::findOrFail($projectId);
        $lastEpic = self::where('project_id', $projectId)
                        ->orderBy('epic_key', 'desc')
                        ->first();

        if (!$lastEpic) {
            return $project->code . '-EPIC-001';
        }

        $lastNumber = (int) substr($lastEpic->epic_key, -3);
        $newNumber = $lastNumber + 1;

        return $project->code . '-EPIC-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على نسبة الإنجاز المحسوبة
     */
    public function getCalculatedProgressAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->tasks()->where('status', 'COMPLETED')->count();
        return ($completedTasks / $totalTasks) * 100;
    }

    /**
     * الحصول على إجمالي النقاط المكتملة
     */
    public function getCompletedStoryPointsAttribute(): int
    {
        return $this->tasks()
                   ->where('status', 'COMPLETED')
                   ->sum('story_points');
    }

    /**
     * الحصول على إجمالي النقاط المقدرة
     */
    public function getTotalEstimatedPointsAttribute(): int
    {
        return $this->tasks()->sum('story_points');
    }

    /**
     * التحقق من التأخير
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->target_date && 
               $this->target_date->isPast() && 
               $this->status !== 'COMPLETED';
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        return $this->target_date ? now()->diffInDays($this->target_date, false) : 0;
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'TODO' => '#6c757d',
            'IN_PROGRESS' => '#fd7e14',
            'COMPLETED' => '#28a745',
            'ON_HOLD' => '#ffc107',
            'CANCELLED' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * بدء العمل على الملحمة
     */
    public function start(int $startedBy): bool
    {
        if ($this->status !== 'TODO') {
            return false;
        }

        $this->update([
            'status' => 'IN_PROGRESS',
            'start_date' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'started_by' => $startedBy,
                'started_at' => now(),
            ]),
        ]);

        return true;
    }

    /**
     * إكمال الملحمة
     */
    public function complete(int $completedBy): bool
    {
        if ($this->status !== 'IN_PROGRESS') {
            return false;
        }

        // التحقق من اكتمال جميع المهام
        $incompleteTasks = $this->tasks()
                               ->whereNotIn('status', ['COMPLETED', 'CANCELLED'])
                               ->count();

        if ($incompleteTasks > 0) {
            return false;
        }

        $this->update([
            'status' => 'COMPLETED',
            'completed_date' => now(),
            'progress_percentage' => 100,
            'actual_story_points' => $this->completed_story_points,
            'metadata' => array_merge($this->metadata ?? [], [
                'completed_by' => $completedBy,
                'completed_at' => now(),
            ]),
        ]);

        // إشعار الفريق
        $this->notifyCompletion();

        return true;
    }

    /**
     * وضع الملحمة في الانتظار
     */
    public function putOnHold(int $userId, string $reason = null): void
    {
        $this->update([
            'status' => 'ON_HOLD',
            'metadata' => array_merge($this->metadata ?? [], [
                'put_on_hold_by' => $userId,
                'put_on_hold_at' => now(),
                'hold_reason' => $reason,
            ]),
        ]);
    }

    /**
     * استئناف الملحمة
     */
    public function resume(int $userId): void
    {
        $this->update([
            'status' => 'IN_PROGRESS',
            'metadata' => array_merge($this->metadata ?? [], [
                'resumed_by' => $userId,
                'resumed_at' => now(),
            ]),
        ]);
    }

    /**
     * إلغاء الملحمة
     */
    public function cancel(int $userId, string $reason = null): void
    {
        $this->update([
            'status' => 'CANCELLED',
            'metadata' => array_merge($this->metadata ?? [], [
                'cancelled_by' => $userId,
                'cancelled_at' => now(),
                'cancellation_reason' => $reason,
            ]),
        ]);

        // إلغاء المهام المرتبطة
        $this->tasks()->whereNotIn('status', ['COMPLETED'])->update(['status' => 'CANCELLED']);
    }

    /**
     * إضافة مهمة للملحمة
     */
    public function addTask(int $taskId): bool
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->project_id !== $this->project_id) {
            return false;
        }

        $task->update(['epic_id' => $this->id]);

        // تحديث النقاط المقدرة
        $this->updateEstimatedPoints();

        return true;
    }

    /**
     * إزالة مهمة من الملحمة
     */
    public function removeTask(int $taskId): bool
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->epic_id !== $this->id) {
            return false;
        }

        $task->update(['epic_id' => null]);

        // تحديث النقاط المقدرة
        $this->updateEstimatedPoints();

        return true;
    }

    /**
     * تحديث النقاط المقدرة
     */
    public function updateEstimatedPoints(): void
    {
        $totalPoints = $this->tasks()->sum('story_points');
        $this->update(['estimated_story_points' => $totalPoints]);
    }

    /**
     * تحديث التقدم
     */
    public function updateProgress(): void
    {
        $calculatedProgress = $this->calculated_progress;
        
        $this->update(['progress_percentage' => $calculatedProgress]);

        // تحديث الحالة تلقائياً
        if ($calculatedProgress === 100 && $this->status === 'IN_PROGRESS') {
            $this->complete(auth()->id());
        } elseif ($calculatedProgress > 0 && $this->status === 'TODO') {
            $this->start(auth()->id());
        }
    }

    /**
     * إنشاء قصة مستخدم
     */
    public function createUserStory(array $storyData, int $createdBy): Task
    {
        return $this->tasks()->create(array_merge($storyData, [
            'project_id' => $this->project_id,
            'epic_id' => $this->id,
            'type' => 'USER_STORY',
            'created_by' => $createdBy,
        ]));
    }

    /**
     * تقسيم الملحمة إلى قصص
     */
    public function breakdownIntoStories(array $storiesData, int $createdBy): array
    {
        $stories = [];
        
        foreach ($storiesData as $storyData) {
            $stories[] = $this->createUserStory($storyData, $createdBy);
        }

        $this->updateEstimatedPoints();

        return $stories;
    }

    /**
     * الحصول على إحصائيات الملحمة
     */
    public function getStatistics(): array
    {
        return [
            'total_tasks' => $this->tasks()->count(),
            'completed_tasks' => $this->tasks()->where('status', 'COMPLETED')->count(),
            'in_progress_tasks' => $this->tasks()->where('status', 'IN_PROGRESS')->count(),
            'todo_tasks' => $this->tasks()->where('status', 'TODO')->count(),
            'total_story_points' => $this->total_estimated_points,
            'completed_story_points' => $this->completed_story_points,
            'progress_percentage' => $this->calculated_progress,
            'days_remaining' => $this->days_remaining,
            'is_overdue' => $this->is_overdue,
            'velocity' => $this->calculateVelocity(),
        ];
    }

    /**
     * حساب السرعة
     */
    protected function calculateVelocity(): float
    {
        if (!$this->start_date || $this->status === 'TODO') {
            return 0;
        }

        $daysWorked = $this->start_date->diffInDays(now()) + 1;
        return $daysWorked > 0 ? $this->completed_story_points / $daysWorked : 0;
    }

    /**
     * إشعار الإكمال
     */
    protected function notifyCompletion(): void
    {
        // إشعار المالك
        if ($this->owner) {
            $this->owner->notify(new \App\Notifications\EpicCompletedNotification($this));
        }

        // إشعار مدير المشروع
        if ($this->project->projectManager) {
            $this->project->projectManager->notify(new \App\Notifications\EpicCompletedNotification($this));
        }
    }

    /**
     * البحث في الملاحم
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('epic_key', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة حسب المالك
     */
    public function scopeOwnedBy($query, int $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * فلترة الملاحم النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['TODO', 'IN_PROGRESS']);
    }

    /**
     * فلترة الملاحم المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('target_date', '<', now())
                    ->where('status', '!=', 'COMPLETED');
    }

    /**
     * ترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW')");
    }

    /**
     * ترتيب حسب التقدم
     */
    public function scopeOrderByProgress($query, string $direction = 'desc')
    {
        return $query->orderBy('progress_percentage', $direction);
    }
}
