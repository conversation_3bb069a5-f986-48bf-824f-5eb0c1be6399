<?php

namespace App\Domains\HR\Events;

use App\Domains\HR\Models\Employee;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنهاء خدمة الموظف
 */
class EmployeeTerminated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Employee $employee;
    public string $reason;
    public \Carbon\Carbon $terminationDate;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Employee $employee, string $reason, \Carbon\Carbon $terminationDate)
    {
        $this->employee = $employee;
        $this->reason = $reason;
        $this->terminationDate = $terminationDate;
    }
}
