<?php

namespace App\Domains\Accounting\Jobs;

use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Services\SmartInvoicingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Generate Recurring Invoices Job
 * مهمة إنشاء الفواتير المتكررة
 */
class GenerateRecurringInvoicesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 دقائق
    public int $tries = 3;

    protected SmartInvoicingService $invoicingService;

    /**
     * إنشاء مثيل جديد من المهمة
     */
    public function __construct()
    {
        $this->onQueue('accounting');
    }

    /**
     * تنفيذ المهمة
     */
    public function handle(SmartInvoicingService $invoicingService): void
    {
        $this->invoicingService = $invoicingService;

        Log::info('بدء مهمة إنشاء الفواتير المتكررة');

        try {
            $recurringInvoices = $this->getRecurringInvoicesDue();
            $generatedCount = 0;
            $failedCount = 0;

            foreach ($recurringInvoices as $invoice) {
                try {
                    $this->generateNextInvoice($invoice);
                    $generatedCount++;
                    
                    Log::info('تم إنشاء فاتورة متكررة', [
                        'original_invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                    ]);

                } catch (\Exception $e) {
                    $failedCount++;
                    
                    Log::error('فشل في إنشاء فاتورة متكررة', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('انتهاء مهمة إنشاء الفواتير المتكررة', [
                'total_processed' => $recurringInvoices->count(),
                'generated' => $generatedCount,
                'failed' => $failedCount,
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في مهمة إنشاء الفواتير المتكررة', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * الحصول على الفواتير المتكررة المستحقة
     */
    protected function getRecurringInvoicesDue()
    {
        return Invoice::where('is_recurring', true)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('recurring_end_date')
                      ->orWhere('recurring_end_date', '>=', now());
            })
            ->whereDate('next_recurring_date', '<=', now())
            ->with(['customer', 'items'])
            ->get();
    }

    /**
     * إنشاء الفاتورة التالية
     */
    protected function generateNextInvoice(Invoice $originalInvoice): Invoice
    {
        // إنشاء بيانات الفاتورة الجديدة
        $newInvoiceData = [
            'customer_id' => $originalInvoice->customer_id,
            'invoice_date' => now()->format('Y-m-d'),
            'due_date' => $this->calculateDueDate($originalInvoice),
            'currency' => $originalInvoice->currency,
            'exchange_rate' => $originalInvoice->exchange_rate,
            'payment_terms' => $originalInvoice->payment_terms,
            'notes' => $originalInvoice->notes,
            'terms_conditions' => $originalInvoice->terms_conditions,
            'discount_type' => $originalInvoice->discount_type,
            'discount_value' => $originalInvoice->discount_value,
            'tax_inclusive' => $originalInvoice->tax_inclusive,
            'project_id' => $originalInvoice->project_id,
            'cost_center_id' => $originalInvoice->cost_center_id,
            'is_recurring' => false, // الفاتورة المنشأة ليست متكررة
            'parent_invoice_id' => $originalInvoice->id,
            'items' => [],
        ];

        // نسخ بنود الفاتورة
        foreach ($originalInvoice->items as $item) {
            $newInvoiceData['items'][] = [
                'product_id' => $item->product_id,
                'description' => $item->description,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'discount_percentage' => $item->discount_percentage,
                'tax_rate' => $item->tax_rate,
                'account_id' => $item->account_id,
            ];
        }

        // إنشاء الفاتورة الجديدة
        $newInvoice = $this->invoicingService->createInvoice($newInvoiceData);

        // تحديث تاريخ التكرار التالي للفاتورة الأصلية
        $this->updateNextRecurringDate($originalInvoice);

        return $newInvoice;
    }

    /**
     * حساب تاريخ الاستحقاق
     */
    protected function calculateDueDate(Invoice $originalInvoice): string
    {
        $invoiceDate = Carbon::now();
        $originalInvoiceDate = Carbon::parse($originalInvoice->invoice_date);
        $originalDueDate = Carbon::parse($originalInvoice->due_date);
        
        $daysDifference = $originalDueDate->diffInDays($originalInvoiceDate);
        
        return $invoiceDate->addDays($daysDifference)->format('Y-m-d');
    }

    /**
     * تحديث تاريخ التكرار التالي
     */
    protected function updateNextRecurringDate(Invoice $invoice): void
    {
        $nextDate = Carbon::parse($invoice->next_recurring_date ?? now());

        switch ($invoice->recurring_frequency) {
            case 'weekly':
                $nextDate->addWeek();
                break;
            case 'monthly':
                $nextDate->addMonth();
                break;
            case 'quarterly':
                $nextDate->addQuarter();
                break;
            case 'yearly':
                $nextDate->addYear();
                break;
        }

        // التحقق من عدم تجاوز تاريخ الانتهاء
        if ($invoice->recurring_end_date && $nextDate->gt(Carbon::parse($invoice->recurring_end_date))) {
            // إيقاف التكرار
            $invoice->update([
                'is_recurring' => false,
                'status' => 'completed',
                'next_recurring_date' => null,
            ]);
        } else {
            $invoice->update([
                'next_recurring_date' => $nextDate->format('Y-m-d'),
            ]);
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('فشل في مهمة إنشاء الفواتير المتكررة', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // إرسال تنبيه للمسؤولين
        // يمكن إضافة إشعار هنا
    }

    /**
     * تحديد عدد الثواني قبل انتهاء مهلة المهمة
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(30);
    }
}
