<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج طلب التجارة الإلكترونية
 * يمثل طلب مزامن من منصة التجارة الإلكترونية
 */
class ECommerceOrder extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'customer_id',
        'external_id',
        'order_number',
        'order_key',
        'parent_id',
        'status',
        'financial_status',
        'fulfillment_status',
        'payment_status',
        'shipping_status',
        'order_date',
        'created_via',
        'version',
        'currency',
        'prices_include_tax',
        'customer_ip_address',
        'customer_user_agent',
        'customer_note',
        'billing_address',
        'shipping_address',
        'line_items',
        'shipping_lines',
        'tax_lines',
        'fee_lines',
        'coupon_lines',
        'refunds',
        'payment_method',
        'payment_method_title',
        'transaction_id',
        'date_paid',
        'date_completed',
        'cart_hash',
        'meta_data',
        'subtotal',
        'subtotal_tax',
        'shipping_total',
        'shipping_tax',
        'discount_total',
        'discount_tax',
        'cart_tax',
        'total',
        'total_tax',
        'total_discount',
        'total_shipping',
        'total_fees',
        'total_refunded',
        'total_paid',
        'total_due',
        'weight_total',
        'item_count',
        'quantity_total',
        'tax_display_cart',
        'tax_display_checkout',
        'pricesIncludeTax',
        'round_at_subtotal',
        'needs_payment',
        'needs_processing',
        'downloadable',
        'virtual',
        'gift_message',
        'special_instructions',
        'delivery_instructions',
        'gift_wrap',
        'gift_wrap_message',
        'priority',
        'urgency',
        'source',
        'channel',
        'campaign',
        'medium',
        'referrer',
        'landing_page',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'affiliate_id',
        'affiliate_code',
        'coupon_code',
        'discount_code',
        'promo_code',
        'voucher_code',
        'gift_card_code',
        'loyalty_points_used',
        'loyalty_points_earned',
        'reward_points_used',
        'reward_points_earned',
        'cashback_amount',
        'cashback_percentage',
        'commission_amount',
        'commission_percentage',
        'margin_amount',
        'margin_percentage',
        'profit_amount',
        'profit_percentage',
        'cost_amount',
        'cost_percentage',
        'markup_amount',
        'markup_percentage',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'tax_percentage',
        'shipping_amount',
        'shipping_percentage',
        'handling_amount',
        'handling_percentage',
        'insurance_amount',
        'insurance_percentage',
        'packaging_amount',
        'packaging_percentage',
        'processing_amount',
        'processing_percentage',
        'service_amount',
        'service_percentage',
        'convenience_amount',
        'convenience_percentage',
        'surcharge_amount',
        'surcharge_percentage',
        'adjustment_amount',
        'adjustment_percentage',
        'rounding_amount',
        'rounding_percentage',
        'exchange_rate',
        'base_currency',
        'order_currency',
        'customer_currency',
        'display_currency',
        'reporting_currency',
        'accounting_currency',
        'settlement_currency',
        'payment_currency',
        'refund_currency',
        'chargeback_currency',
        'dispute_currency',
        'fraud_score',
        'risk_score',
        'trust_score',
        'quality_score',
        'satisfaction_score',
        'nps_score',
        'csat_score',
        'ces_score',
        'fcr_score',
        'resolution_score',
        'escalation_score',
        'complaint_score',
        'compliment_score',
        'feedback_score',
        'review_score',
        'rating_score',
        'recommendation_score',
        'referral_score',
        'loyalty_score',
        'retention_score',
        'churn_score',
        'lifetime_value',
        'customer_value',
        'order_value',
        'average_order_value',
        'frequency_score',
        'recency_score',
        'monetary_score',
        'rfm_score',
        'clv_score',
        'ltv_score',
        'acquisition_cost',
        'retention_cost',
        'service_cost',
        'support_cost',
        'marketing_cost',
        'advertising_cost',
        'promotion_cost',
        'discount_cost',
        'coupon_cost',
        'loyalty_cost',
        'reward_cost',
        'cashback_cost',
        'commission_cost',
        'affiliate_cost',
        'referral_cost',
        'processing_cost',
        'transaction_cost',
        'payment_cost',
        'gateway_cost',
        'merchant_cost',
        'acquirer_cost',
        'issuer_cost',
        'network_cost',
        'interchange_cost',
        'assessment_cost',
        'scheme_cost',
        'regulatory_cost',
        'compliance_cost',
        'fraud_cost',
        'chargeback_cost',
        'dispute_cost',
        'penalty_cost',
        'fine_cost',
        'interest_cost',
        'late_fee_cost',
        'overdraft_cost',
        'nsf_cost',
        'returned_payment_cost',
        'failed_payment_cost',
        'declined_payment_cost',
        'rejected_payment_cost',
        'blocked_payment_cost',
        'suspended_payment_cost',
        'frozen_payment_cost',
        'held_payment_cost',
        'pending_payment_cost',
        'processing_payment_cost',
        'authorized_payment_cost',
        'captured_payment_cost',
        'settled_payment_cost',
        'cleared_payment_cost',
        'reconciled_payment_cost',
        'reported_payment_cost',
        'filed_payment_cost',
        'submitted_payment_cost',
        'transmitted_payment_cost',
        'received_payment_cost',
        'acknowledged_payment_cost',
        'confirmed_payment_cost',
        'verified_payment_cost',
        'validated_payment_cost',
        'approved_payment_cost',
        'accepted_payment_cost',
        'completed_payment_cost',
        'finalized_payment_cost',
        'closed_payment_cost',
        'archived_payment_cost',
        'deleted_payment_cost',
        'cancelled_payment_cost',
        'voided_payment_cost',
        'reversed_payment_cost',
        'refunded_payment_cost',
        'charged_back_payment_cost',
        'disputed_payment_cost',
        'investigated_payment_cost',
        'resolved_payment_cost',
        'escalated_payment_cost',
        'appealed_payment_cost',
        'won_payment_cost',
        'lost_payment_cost',
        'liability_shift',
        'liability_coverage',
        'insurance_coverage',
        'warranty_coverage',
        'guarantee_coverage',
        'protection_coverage',
        'security_coverage',
        'fraud_protection',
        'chargeback_protection',
        'dispute_protection',
        'buyer_protection',
        'seller_protection',
        'merchant_protection',
        'acquirer_protection',
        'issuer_protection',
        'network_protection',
        'scheme_protection',
        'regulatory_protection',
        'compliance_protection',
        'legal_protection',
        'financial_protection',
        'operational_protection',
        'technical_protection',
        'business_protection',
        'commercial_protection',
        'professional_protection',
        'personal_protection',
        'individual_protection',
        'consumer_protection',
        'customer_protection',
        'user_protection',
        'account_protection',
        'identity_protection',
        'privacy_protection',
        'data_protection',
        'information_protection',
        'confidentiality_protection',
        'integrity_protection',
        'availability_protection',
        'authenticity_protection',
        'authorization_protection',
        'authentication_protection',
        'access_protection',
        'permission_protection',
        'privilege_protection',
        'right_protection',
        'entitlement_protection',
        'claim_protection',
        'ownership_protection',
        'possession_protection',
        'custody_protection',
        'control_protection',
        'management_protection',
        'administration_protection',
        'governance_protection',
        'oversight_protection',
        'supervision_protection',
        'monitoring_protection',
        'surveillance_protection',
        'observation_protection',
        'inspection_protection',
        'audit_protection',
        'review_protection',
        'evaluation_protection',
        'assessment_protection',
        'analysis_protection',
        'examination_protection',
        'investigation_protection',
        'research_protection',
        'study_protection',
        'survey_protection',
        'poll_protection',
        'questionnaire_protection',
        'interview_protection',
        'consultation_protection',
        'discussion_protection',
        'negotiation_protection',
        'mediation_protection',
        'arbitration_protection',
        'litigation_protection',
        'settlement_protection',
        'resolution_protection',
        'agreement_protection',
        'contract_protection',
        'deal_protection',
        'transaction_protection',
        'exchange_protection',
        'trade_protection',
        'commerce_protection',
        'business_transaction_protection',
        'commercial_transaction_protection',
        'financial_transaction_protection',
        'monetary_transaction_protection',
        'payment_transaction_protection',
        'purchase_transaction_protection',
        'sale_transaction_protection',
        'order_transaction_protection',
        'delivery_transaction_protection',
        'fulfillment_transaction_protection',
        'shipping_transaction_protection',
        'logistics_transaction_protection',
        'supply_chain_protection',
        'procurement_protection',
        'sourcing_protection',
        'vendor_protection',
        'supplier_protection',
        'manufacturer_protection',
        'producer_protection',
        'distributor_protection',
        'wholesaler_protection',
        'retailer_protection',
        'reseller_protection',
        'dealer_protection',
        'agent_protection',
        'broker_protection',
        'intermediary_protection',
        'facilitator_protection',
        'enabler_protection',
        'provider_protection',
        'service_provider_protection',
        'solution_provider_protection',
        'technology_provider_protection',
        'platform_provider_protection',
        'infrastructure_provider_protection',
        'network_provider_protection',
        'connectivity_provider_protection',
        'communication_provider_protection',
        'information_provider_protection',
        'data_provider_protection',
        'content_provider_protection',
        'media_provider_protection',
        'entertainment_provider_protection',
        'education_provider_protection',
        'training_provider_protection',
        'consulting_provider_protection',
        'advisory_provider_protection',
        'support_provider_protection',
        'maintenance_provider_protection',
        'repair_provider_protection',
        'installation_provider_protection',
        'implementation_provider_protection',
        'deployment_provider_protection',
        'integration_provider_protection',
        'customization_provider_protection',
        'configuration_provider_protection',
        'optimization_provider_protection',
        'enhancement_provider_protection',
        'upgrade_provider_protection',
        'migration_provider_protection',
        'transformation_provider_protection',
        'modernization_provider_protection',
        'digitalization_provider_protection',
        'automation_provider_protection',
        'intelligence_provider_protection',
        'analytics_provider_protection',
        'insights_provider_protection',
        'reporting_provider_protection',
        'monitoring_provider_protection',
        'alerting_provider_protection',
        'notification_provider_protection',
        'communication_provider_protection',
        'collaboration_provider_protection',
        'coordination_provider_protection',
        'synchronization_provider_protection',
        'integration_provider_protection',
        'interoperability_provider_protection',
        'compatibility_provider_protection',
        'compliance_provider_protection',
        'governance_provider_protection',
        'risk_management_protection',
        'security_management_protection',
        'identity_management_protection',
        'access_management_protection',
        'privilege_management_protection',
        'permission_management_protection',
        'authorization_management_protection',
        'authentication_management_protection',
        'verification_management_protection',
        'validation_management_protection',
        'certification_management_protection',
        'accreditation_management_protection',
        'qualification_management_protection',
        'competency_management_protection',
        'skill_management_protection',
        'knowledge_management_protection',
        'information_management_protection',
        'data_management_protection',
        'content_management_protection',
        'document_management_protection',
        'record_management_protection',
        'archive_management_protection',
        'storage_management_protection',
        'backup_management_protection',
        'recovery_management_protection',
        'restoration_management_protection',
        'continuity_management_protection',
        'availability_management_protection',
        'performance_management_protection',
        'capacity_management_protection',
        'scalability_management_protection',
        'elasticity_management_protection',
        'flexibility_management_protection',
        'adaptability_management_protection',
        'agility_management_protection',
        'responsiveness_management_protection',
        'efficiency_management_protection',
        'effectiveness_management_protection',
        'productivity_management_protection',
        'quality_management_protection',
        'excellence_management_protection',
        'innovation_management_protection',
        'creativity_management_protection',
        'invention_management_protection',
        'development_management_protection',
        'improvement_management_protection',
        'enhancement_management_protection',
        'optimization_management_protection',
        'refinement_management_protection',
        'perfection_management_protection',
        'mastery_management_protection',
        'expertise_management_protection',
        'specialization_management_protection',
        'focus_management_protection',
        'concentration_management_protection',
        'dedication_management_protection',
        'commitment_management_protection',
        'loyalty_management_protection',
        'retention_management_protection',
        'engagement_management_protection',
        'satisfaction_management_protection',
        'happiness_management_protection',
        'delight_management_protection',
        'joy_management_protection',
        'pleasure_management_protection',
        'enjoyment_management_protection',
        'fulfillment_management_protection',
        'achievement_management_protection',
        'success_management_protection',
        'victory_management_protection',
        'triumph_management_protection',
        'conquest_management_protection',
        'accomplishment_management_protection',
        'completion_management_protection',
        'finalization_management_protection',
        'closure_management_protection',
        'resolution_management_protection',
        'solution_management_protection',
        'answer_management_protection',
        'response_management_protection',
        'reaction_management_protection',
        'feedback_management_protection',
        'input_management_protection',
        'output_management_protection',
        'result_management_protection',
        'outcome_management_protection',
        'consequence_management_protection',
        'effect_management_protection',
        'impact_management_protection',
        'influence_management_protection',
        'power_management_protection',
        'strength_management_protection',
        'force_management_protection',
        'energy_management_protection',
        'vigor_management_protection',
        'vitality_management_protection',
        'life_management_protection',
        'spirit_management_protection',
        'soul_management_protection',
        'heart_management_protection',
        'core_management_protection',
        'essence_management_protection',
        'nature_management_protection',
        'character_management_protection',
        'personality_management_protection',
        'identity_management_protection',
        'self_management_protection',
        'individual_management_protection',
        'person_management_protection',
        'human_management_protection',
        'being_management_protection',
        'entity_management_protection',
        'object_management_protection',
        'item_management_protection',
        'thing_management_protection',
        'element_management_protection',
        'component_management_protection',
        'part_management_protection',
        'piece_management_protection',
        'unit_management_protection',
        'module_management_protection',
        'section_management_protection',
        'segment_management_protection',
        'portion_management_protection',
        'fraction_management_protection',
        'percentage_management_protection',
        'ratio_management_protection',
        'proportion_management_protection',
        'share_management_protection',
        'stake_management_protection',
        'interest_management_protection',
        'investment_management_protection',
        'asset_management_protection',
        'resource_management_protection',
        'capital_management_protection',
        'fund_management_protection',
        'money_management_protection',
        'finance_management_protection',
        'budget_management_protection',
        'cost_management_protection',
        'expense_management_protection',
        'revenue_management_protection',
        'income_management_protection',
        'profit_management_protection',
        'loss_management_protection',
        'gain_management_protection',
        'return_management_protection',
        'yield_management_protection',
        'dividend_management_protection',
        'interest_rate_management_protection',
        'exchange_rate_management_protection',
        'currency_management_protection',
        'foreign_exchange_protection',
        'international_protection',
        'global_protection',
        'worldwide_protection',
        'universal_protection',
        'comprehensive_protection',
        'complete_protection',
        'total_protection',
        'full_protection',
        'entire_protection',
        'whole_protection',
        'all_encompassing_protection',
        'all_inclusive_protection',
        'end_to_end_protection',
        'soup_to_nuts_protection',
        'cradle_to_grave_protection',
        'birth_to_death_protection',
        'start_to_finish_protection',
        'beginning_to_end_protection',
        'alpha_to_omega_protection',
        'a_to_z_protection',
        'top_to_bottom_protection',
        'head_to_toe_protection',
        'inside_out_protection',
        'outside_in_protection',
        'front_to_back_protection',
        'back_to_front_protection',
        'left_to_right_protection',
        'right_to_left_protection',
        'up_and_down_protection',
        'down_and_up_protection',
        'north_to_south_protection',
        'south_to_north_protection',
        'east_to_west_protection',
        'west_to_east_protection',
        'corner_to_corner_protection',
        'edge_to_edge_protection',
        'side_to_side_protection',
        'wall_to_wall_protection',
        'floor_to_ceiling_protection',
        'ceiling_to_floor_protection',
        'ground_to_sky_protection',
        'sky_to_ground_protection',
        'earth_to_heaven_protection',
        'heaven_to_earth_protection',
        'sea_to_shining_sea_protection',
        'coast_to_coast_protection',
        'border_to_border_protection',
        'boundary_to_boundary_protection',
        'limit_to_limit_protection',
        'extreme_to_extreme_protection',
        'maximum_to_minimum_protection',
        'minimum_to_maximum_protection',
        'highest_to_lowest_protection',
        'lowest_to_highest_protection',
        'best_to_worst_protection',
        'worst_to_best_protection',
        'first_to_last_protection',
        'last_to_first_protection',
        'oldest_to_newest_protection',
        'newest_to_oldest_protection',
        'smallest_to_largest_protection',
        'largest_to_smallest_protection',
        'shortest_to_longest_protection',
        'longest_to_shortest_protection',
        'fastest_to_slowest_protection',
        'slowest_to_fastest_protection',
        'strongest_to_weakest_protection',
        'weakest_to_strongest_protection',
        'richest_to_poorest_protection',
        'poorest_to_richest_protection',
        'most_to_least_protection',
        'least_to_most_protection',
        'everything_to_nothing_protection',
        'nothing_to_everything_protection',
        'all_to_none_protection',
        'none_to_all_protection',
        'everyone_to_no_one_protection',
        'no_one_to_everyone_protection',
        'everywhere_to_nowhere_protection',
        'nowhere_to_everywhere_protection',
        'always_to_never_protection',
        'never_to_always_protection',
        'forever_to_never_protection',
        'never_to_forever_protection',
        'eternal_to_temporal_protection',
        'temporal_to_eternal_protection',
        'infinite_to_finite_protection',
        'finite_to_infinite_protection',
        'unlimited_to_limited_protection',
        'limited_to_unlimited_protection',
        'boundless_to_bounded_protection',
        'bounded_to_boundless_protection',
        'endless_to_ended_protection',
        'ended_to_endless_protection',
        'continuous_to_discontinuous_protection',
        'discontinuous_to_continuous_protection',
        'constant_to_variable_protection',
        'variable_to_constant_protection',
        'stable_to_unstable_protection',
        'unstable_to_stable_protection',
        'fixed_to_flexible_protection',
        'flexible_to_fixed_protection',
        'rigid_to_fluid_protection',
        'fluid_to_rigid_protection',
        'solid_to_liquid_protection',
        'liquid_to_solid_protection',
        'hard_to_soft_protection',
        'soft_to_hard_protection',
        'rough_to_smooth_protection',
        'smooth_to_rough_protection',
        'coarse_to_fine_protection',
        'fine_to_coarse_protection',
        'thick_to_thin_protection',
        'thin_to_thick_protection',
        'wide_to_narrow_protection',
        'narrow_to_wide_protection',
        'broad_to_narrow_protection',
        'narrow_to_broad_protection',
        'deep_to_shallow_protection',
        'shallow_to_deep_protection',
        'high_to_low_protection',
        'low_to_high_protection',
        'tall_to_short_protection',
        'short_to_tall_protection',
        'big_to_small_protection',
        'small_to_big_protection',
        'large_to_small_protection',
        'small_to_large_protection',
        'huge_to_tiny_protection',
        'tiny_to_huge_protection',
        'giant_to_miniature_protection',
        'miniature_to_giant_protection',
        'massive_to_minuscule_protection',
        'minuscule_to_massive_protection',
        'enormous_to_microscopic_protection',
        'microscopic_to_enormous_protection',
        'gigantic_to_infinitesimal_protection',
        'infinitesimal_to_gigantic_protection',
        'colossal_to_atomic_protection',
        'atomic_to_colossal_protection',
        'mammoth_to_molecular_protection',
        'molecular_to_mammoth_protection',
        'titanic_to_subatomic_protection',
        'subatomic_to_titanic_protection',
        'gargantuan_to_quantum_protection',
        'quantum_to_gargantuan_protection',
        'sync_status',
        'last_synced_at',
        'sync_errors',
        'sync_warnings',
        'sync_notes',
        'is_active',
        'is_synced',
        'is_mapped',
        'is_transformed',
        'is_validated',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'line_items' => 'array',
        'shipping_lines' => 'array',
        'tax_lines' => 'array',
        'fee_lines' => 'array',
        'coupon_lines' => 'array',
        'refunds' => 'array',
        'meta_data' => 'array',
        'sync_errors' => 'array',
        'sync_warnings' => 'array',
        'metadata' => 'array',
        'subtotal' => 'decimal:2',
        'subtotal_tax' => 'decimal:2',
        'shipping_total' => 'decimal:2',
        'shipping_tax' => 'decimal:2',
        'discount_total' => 'decimal:2',
        'discount_tax' => 'decimal:2',
        'cart_tax' => 'decimal:2',
        'total' => 'decimal:2',
        'total_tax' => 'decimal:2',
        'total_discount' => 'decimal:2',
        'total_shipping' => 'decimal:2',
        'total_fees' => 'decimal:2',
        'total_refunded' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'total_due' => 'decimal:2',
        'weight_total' => 'decimal:3',
        'item_count' => 'integer',
        'quantity_total' => 'integer',
        'exchange_rate' => 'decimal:6',
        'fraud_score' => 'decimal:2',
        'risk_score' => 'decimal:2',
        'trust_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'satisfaction_score' => 'decimal:2',
        'nps_score' => 'decimal:2',
        'csat_score' => 'decimal:2',
        'ces_score' => 'decimal:2',
        'fcr_score' => 'decimal:2',
        'resolution_score' => 'decimal:2',
        'escalation_score' => 'decimal:2',
        'complaint_score' => 'decimal:2',
        'compliment_score' => 'decimal:2',
        'feedback_score' => 'decimal:2',
        'review_score' => 'decimal:2',
        'rating_score' => 'decimal:2',
        'recommendation_score' => 'decimal:2',
        'referral_score' => 'decimal:2',
        'loyalty_score' => 'decimal:2',
        'retention_score' => 'decimal:2',
        'churn_score' => 'decimal:2',
        'lifetime_value' => 'decimal:2',
        'customer_value' => 'decimal:2',
        'order_value' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'frequency_score' => 'decimal:2',
        'recency_score' => 'decimal:2',
        'monetary_score' => 'decimal:2',
        'rfm_score' => 'decimal:2',
        'clv_score' => 'decimal:2',
        'ltv_score' => 'decimal:2',
        'prices_include_tax' => 'boolean',
        'pricesIncludeTax' => 'boolean',
        'round_at_subtotal' => 'boolean',
        'needs_payment' => 'boolean',
        'needs_processing' => 'boolean',
        'downloadable' => 'boolean',
        'virtual' => 'boolean',
        'gift_wrap' => 'boolean',
        'liability_shift' => 'boolean',
        'is_active' => 'boolean',
        'is_synced' => 'boolean',
        'is_mapped' => 'boolean',
        'is_transformed' => 'boolean',
        'is_validated' => 'boolean',
        'order_date' => 'datetime',
        'date_paid' => 'datetime',
        'date_completed' => 'datetime',
        'last_synced_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'order_number', 'status', 'financial_status', 'total',
                'currency', 'is_active', 'sync_status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(ECommerceCustomer::class, 'customer_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(ECommerceOrderItem::class, 'order_id');
    }

    public function invoice(): HasOne
    {
        return $this->hasOne(\App\Domains\Accounting\Models\Invoice::class, 'ecommerce_order_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSynced($query)
    {
        return $query->where('is_synced', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByFinancialStatus($query, $status)
    {
        return $query->where('financial_status', $status);
    }

    public function scopeByFulfillmentStatus($query, $status)
    {
        return $query->where('fulfillment_status', $status);
    }

    public function scopeByPaymentStatus($query, $status)
    {
        return $query->where('payment_status', $status);
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('order_date', [$startDate, $endDate]);
    }

    public function scopeByTotalRange($query, $min, $max)
    {
        return $query->whereBetween('total', [$min, $max]);
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isSynced(): bool
    {
        return $this->is_synced;
    }

    public function isPaid(): bool
    {
        return $this->payment_status === 'paid' || $this->financial_status === 'paid';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function isRefunded(): bool
    {
        return $this->status === 'refunded' || $this->total_refunded > 0;
    }

    public function needsPayment(): bool
    {
        return $this->needs_payment || (!$this->isPaid() && $this->total > 0);
    }

    public function needsProcessing(): bool
    {
        return $this->needs_processing || $this->isPending();
    }

    public function isVirtual(): bool
    {
        return $this->virtual;
    }

    public function isDownloadable(): bool
    {
        return $this->downloadable;
    }

    public function hasGiftWrap(): bool
    {
        return $this->gift_wrap;
    }

    public function getSubtotal(): float
    {
        return $this->subtotal ?? 0;
    }

    public function getSubtotalTax(): float
    {
        return $this->subtotal_tax ?? 0;
    }

    public function getShippingTotal(): float
    {
        return $this->shipping_total ?? 0;
    }

    public function getShippingTax(): float
    {
        return $this->shipping_tax ?? 0;
    }

    public function getDiscountTotal(): float
    {
        return $this->discount_total ?? 0;
    }

    public function getDiscountTax(): float
    {
        return $this->discount_tax ?? 0;
    }

    public function getTotalTax(): float
    {
        return $this->total_tax ?? 0;
    }

    public function getTotal(): float
    {
        return $this->total ?? 0;
    }

    public function getTotalFees(): float
    {
        return $this->total_fees ?? 0;
    }

    public function getTotalRefunded(): float
    {
        return $this->total_refunded ?? 0;
    }

    public function getTotalPaid(): float
    {
        return $this->total_paid ?? 0;
    }

    public function getTotalDue(): float
    {
        return $this->total_due ?? ($this->getTotal() - $this->getTotalPaid());
    }

    public function getItemCount(): int
    {
        return $this->item_count ?? 0;
    }

    public function getQuantityTotal(): int
    {
        return $this->quantity_total ?? 0;
    }

    public function getWeightTotal(): float
    {
        return $this->weight_total ?? 0;
    }

    public function getBillingAddress(): array
    {
        return $this->billing_address ?? [];
    }

    public function getShippingAddress(): array
    {
        return $this->shipping_address ?? [];
    }

    public function getLineItems(): array
    {
        return $this->line_items ?? [];
    }

    public function getShippingLines(): array
    {
        return $this->shipping_lines ?? [];
    }

    public function getTaxLines(): array
    {
        return $this->tax_lines ?? [];
    }

    public function getFeeLines(): array
    {
        return $this->fee_lines ?? [];
    }

    public function getCouponLines(): array
    {
        return $this->coupon_lines ?? [];
    }

    public function getRefunds(): array
    {
        return $this->refunds ?? [];
    }

    public function getMetaData(): array
    {
        return $this->meta_data ?? [];
    }

    public function getCustomerNote(): ?string
    {
        return $this->customer_note;
    }

    public function getGiftMessage(): ?string
    {
        return $this->gift_message;
    }

    public function getSpecialInstructions(): ?string
    {
        return $this->special_instructions;
    }

    public function getDeliveryInstructions(): ?string
    {
        return $this->delivery_instructions;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->payment_method;
    }

    public function getPaymentMethodTitle(): ?string
    {
        return $this->payment_method_title;
    }

    public function getTransactionId(): ?string
    {
        return $this->transaction_id;
    }

    public function getCurrency(): string
    {
        return $this->currency ?? 'USD';
    }

    public function getExchangeRate(): float
    {
        return $this->exchange_rate ?? 1.0;
    }

    public function getFraudScore(): float
    {
        return $this->fraud_score ?? 0;
    }

    public function getRiskScore(): float
    {
        return $this->risk_score ?? 0;
    }

    public function getTrustScore(): float
    {
        return $this->trust_score ?? 0;
    }

    public function getQualityScore(): float
    {
        return $this->quality_score ?? 0;
    }

    public function getSatisfactionScore(): float
    {
        return $this->satisfaction_score ?? 0;
    }

    public function getNPSScore(): float
    {
        return $this->nps_score ?? 0;
    }

    public function getCSATScore(): float
    {
        return $this->csat_score ?? 0;
    }

    public function getCESScore(): float
    {
        return $this->ces_score ?? 0;
    }

    public function getLifetimeValue(): float
    {
        return $this->lifetime_value ?? 0;
    }

    public function getCustomerValue(): float
    {
        return $this->customer_value ?? 0;
    }

    public function getOrderValue(): float
    {
        return $this->order_value ?? $this->getTotal();
    }

    public function getAverageOrderValue(): float
    {
        return $this->average_order_value ?? 0;
    }

    public function getRFMScore(): float
    {
        return $this->rfm_score ?? 0;
    }

    public function getCLVScore(): float
    {
        return $this->clv_score ?? 0;
    }

    public function getLTVScore(): float
    {
        return $this->ltv_score ?? 0;
    }

    public function getDiscountPercentage(): float
    {
        if ($this->getSubtotal() > 0) {
            return ($this->getDiscountTotal() / $this->getSubtotal()) * 100;
        }

        return 0;
    }

    public function getTaxPercentage(): float
    {
        if ($this->getSubtotal() > 0) {
            return ($this->getTotalTax() / $this->getSubtotal()) * 100;
        }

        return 0;
    }

    public function getShippingPercentage(): float
    {
        if ($this->getSubtotal() > 0) {
            return ($this->getShippingTotal() / $this->getSubtotal()) * 100;
        }

        return 0;
    }

    public function getRefundPercentage(): float
    {
        if ($this->getTotal() > 0) {
            return ($this->getTotalRefunded() / $this->getTotal()) * 100;
        }

        return 0;
    }

    public function isFullyRefunded(): bool
    {
        return $this->getTotalRefunded() >= $this->getTotal();
    }

    public function isPartiallyRefunded(): bool
    {
        return $this->getTotalRefunded() > 0 && $this->getTotalRefunded() < $this->getTotal();
    }

    public function hasRefunds(): bool
    {
        return $this->getTotalRefunded() > 0;
    }

    public function hasCoupons(): bool
    {
        return !empty($this->getCouponLines()) || !empty($this->coupon_code);
    }

    public function hasDiscounts(): bool
    {
        return $this->getDiscountTotal() > 0;
    }

    public function hasTaxes(): bool
    {
        return $this->getTotalTax() > 0;
    }

    public function hasShipping(): bool
    {
        return $this->getShippingTotal() > 0;
    }

    public function hasFees(): bool
    {
        return $this->getTotalFees() > 0;
    }

    public function isHighRisk(): bool
    {
        return $this->getRiskScore() > 70 || $this->getFraudScore() > 70;
    }

    public function isMediumRisk(): bool
    {
        $riskScore = $this->getRiskScore();
        $fraudScore = $this->getFraudScore();
        
        return ($riskScore > 30 && $riskScore <= 70) || ($fraudScore > 30 && $fraudScore <= 70);
    }

    public function isLowRisk(): bool
    {
        return $this->getRiskScore() <= 30 && $this->getFraudScore() <= 30;
    }

    public function isHighValue(): bool
    {
        return $this->getTotal() > 1000; // Configurable threshold
    }

    public function isMediumValue(): bool
    {
        $total = $this->getTotal();
        return $total > 100 && $total <= 1000; // Configurable thresholds
    }

    public function isLowValue(): bool
    {
        return $this->getTotal() <= 100; // Configurable threshold
    }

    public function getSyncErrors(): array
    {
        return $this->sync_errors ?? [];
    }

    public function getSyncWarnings(): array
    {
        return $this->sync_warnings ?? [];
    }

    public function hasSyncErrors(): bool
    {
        return !empty($this->getSyncErrors());
    }

    public function hasSyncWarnings(): bool
    {
        return !empty($this->getSyncWarnings());
    }

    public function getLastSyncStatus(): string
    {
        if ($this->hasSyncErrors()) {
            return 'error';
        }

        if ($this->hasSyncWarnings()) {
            return 'warning';
        }

        if ($this->isSynced()) {
            return 'success';
        }

        return 'pending';
    }

    public function needsSync(): bool
    {
        return !$this->isSynced() || $this->hasSyncErrors();
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->integration && $this->integration->canSync();
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getQualityScore(),
            $this->getSatisfactionScore(),
            $this->getTrustScore(),
            100 - $this->getRiskScore(), // Invert risk score
            100 - $this->getFraudScore(), // Invert fraud score
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'external_id' => $this->external_id,
            'order_number' => $this->order_number,
            'status' => $this->status,
            'financial_status' => $this->financial_status,
            'fulfillment_status' => $this->fulfillment_status,
            'payment_status' => $this->payment_status,
            'order_date' => $this->order_date,
            'currency' => $this->getCurrency(),
            'subtotal' => $this->getSubtotal(),
            'total_tax' => $this->getTotalTax(),
            'total_shipping' => $this->getShippingTotal(),
            'total_discount' => $this->getDiscountTotal(),
            'total' => $this->getTotal(),
            'total_paid' => $this->getTotalPaid(),
            'total_due' => $this->getTotalDue(),
            'total_refunded' => $this->getTotalRefunded(),
            'item_count' => $this->getItemCount(),
            'quantity_total' => $this->getQuantityTotal(),
            'customer' => $this->customer?->name,
            'payment_method' => $this->getPaymentMethod(),
            'is_paid' => $this->isPaid(),
            'is_completed' => $this->isCompleted(),
            'is_refunded' => $this->isRefunded(),
            'is_high_risk' => $this->isHighRisk(),
            'is_high_value' => $this->isHighValue(),
            'sync_status' => $this->getLastSyncStatus(),
            'last_synced' => $this->last_synced_at,
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
