<!-- <PERSON><PERSON> Overview Widget -->
<div class="widget bg-white rounded-xl shadow-lg p-6" 
     style="grid-column: span {{ $widget['size']['w'] }}; grid-row: span {{ $widget['size']['h'] }};"
     data-widget-type="kpi_overview">
     
    <!-- Widget Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <div class="bg-purple-100 p-3 rounded-lg mr-3">
                <i class="fas fa-chart-line text-purple-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-bold text-gray-800">مؤشرات الأداء الرئيسية</h3>
                <p class="text-sm text-gray-500">نظرة شاملة على أداء الشركة</p>
            </div>
        </div>
        
        <div class="flex items-center space-x-2 space-x-reverse">
            <button class="text-gray-400 hover:text-gray-600" onclick="refreshWidget('kpi_overview')">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="text-gray-400 hover:text-gray-600" onclick="configureWidget('kpi_overview')">
                <i class="fas fa-cog"></i>
            </button>
            <button class="text-gray-400 hover:text-gray-600" onclick="removeWidget('kpi_overview')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- KPI Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Total Revenue -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm">إجمالي الإيرادات</p>
                    <p class="text-2xl font-bold">{{ number_format($data['kpis']['total_revenue'] ?? 0) }} ر.س</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-arrow-up text-green-200 text-xs mr-1"></i>
                        <span class="text-green-200 text-xs">+12.5% من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-dollar-sign text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Net Profit -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">صافي الربح</p>
                    <p class="text-2xl font-bold">{{ number_format($data['kpis']['net_profit'] ?? 0) }} ر.س</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-arrow-up text-blue-200 text-xs mr-1"></i>
                        <span class="text-blue-200 text-xs">+8.3% من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-chart-pie text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Profit Margin -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm">هامش الربح</p>
                    <p class="text-2xl font-bold">{{ $data['kpis']['profit_margin'] ?? 0 }}%</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-arrow-up text-purple-200 text-xs mr-1"></i>
                        <span class="text-purple-200 text-xs">+2.1% من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-percentage text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Projects -->
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm">المشاريع النشطة</p>
                    <p class="text-2xl font-bold">{{ $data['kpis']['active_projects'] ?? 0 }}</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-arrow-up text-orange-200 text-xs mr-1"></i>
                        <span class="text-orange-200 text-xs">+3 مشاريع جديدة</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-project-diagram text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Customer Satisfaction -->
        <div class="bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-pink-100 text-sm">رضا العملاء</p>
                    <p class="text-2xl font-bold">{{ $data['kpis']['customer_satisfaction'] ?? 0 }}/5</p>
                    <div class="flex items-center mt-2">
                        <div class="flex text-yellow-300">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= floor($data['kpis']['customer_satisfaction'] ?? 0))
                                    <i class="fas fa-star text-xs"></i>
                                @else
                                    <i class="far fa-star text-xs"></i>
                                @endif
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-smile text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Employee Count -->
        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-indigo-100 text-sm">عدد الموظفين</p>
                    <p class="text-2xl font-bold">{{ $data['kpis']['employee_count'] ?? 0 }}</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-arrow-up text-indigo-200 text-xs mr-1"></i>
                        <span class="text-indigo-200 text-xs">+5 موظفين جدد</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                    <i class="fas fa-users text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Insights -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="font-semibold text-gray-800 mb-3">رؤى سريعة</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm text-gray-600">الإيرادات تتجه نحو الأعلى بمعدل ثابت</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                <span class="text-sm text-gray-600">هامش الربح يتحسن تدريجياً</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                <span class="text-sm text-gray-600">يُنصح بمراجعة تكاليف المشروع الجديد</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                <span class="text-sm text-gray-600">رضا العملاء في أعلى مستوياته</span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-3 space-x-reverse">
        <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition duration-300 text-sm">
            <i class="fas fa-chart-bar mr-2"></i>
            تقرير مفصل
        </button>
        <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-300 text-sm">
            <i class="fas fa-download mr-2"></i>
            تصدير البيانات
        </button>
        <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-300 text-sm">
            <i class="fas fa-cog mr-2"></i>
            إعدادات المؤشرات
        </button>
    </div>
</div>

<script>
// KPI Widget JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // تحديث البيانات كل 5 دقائق
    setInterval(function() {
        refreshWidget('kpi_overview');
    }, 300000);
    
    // إضافة تأثيرات تفاعلية
    const kpiCards = document.querySelectorAll('.bg-gradient-to-r');
    kpiCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});

function refreshWidget(widgetType) {
    // إظهار مؤشر التحميل
    const widget = document.querySelector(`[data-widget-type="${widgetType}"]`);
    const refreshBtn = widget.querySelector('.fa-sync-alt');
    refreshBtn.classList.add('fa-spin');
    
    // محاكاة تحديث البيانات
    setTimeout(() => {
        refreshBtn.classList.remove('fa-spin');
        showNotification('تم تحديث البيانات بنجاح', 'success');
    }, 2000);
}

function configureWidget(widgetType) {
    // فتح نافذة إعدادات الودجت
    showModal('widget-config-modal');
}

function removeWidget(widgetType) {
    if (confirm('هل أنت متأكد من حذف هذا الودجت؟')) {
        const widget = document.querySelector(`[data-widget-type="${widgetType}"]`);
        widget.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            widget.remove();
            showNotification('تم حذف الودجت', 'info');
        }, 300);
    }
}
</script>
