<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تصعيد التذكرة - Ticket Escalation
 */
class TicketEscalation extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'ticket_id',
        'escalated_by',
        'escalated_to',
        'level',
        'reason',
        'escalated_at',
        'resolved_at',
        'resolution_notes',
    ];

    protected $casts = [
        'level' => 'integer',
        'escalated_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    public function escalatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'escalated_by');
    }

    public function escalatedTo(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'escalated_to');
    }

    public function getIsResolvedAttribute(): bool
    {
        return !is_null($this->resolved_at);
    }

    public function getDurationAttribute(): ?int
    {
        if (!$this->resolved_at) {
            return null;
        }

        return $this->escalated_at->diffInMinutes($this->resolved_at);
    }
}
