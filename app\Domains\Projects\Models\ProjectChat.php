<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج محادثة المشروع - Real-Time Communication
 * يدعم المراسلة الفورية والتعاون داخل المشروع
 */
class ProjectChat extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'type',
        'is_private',
        'is_archived',
        'created_by',
        'settings',
        'metadata',
    ];

    protected $casts = [
        'is_private' => 'boolean',
        'is_archived' => 'boolean',
        'settings' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع منشئ المحادثة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع الرسائل
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'chat_id');
    }

    /**
     * العلاقة مع المشاركين
     */
    public function participants(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Domains\HR\Models\Employee::class,
            'chat_participants',
            'chat_id',
            'employee_id'
        )->withPivot([
            'role',
            'joined_at',
            'last_read_at',
            'is_muted',
            'notification_settings'
        ])->withTimestamps();
    }

    /**
     * الحصول على آخر رسالة
     */
    public function getLastMessageAttribute(): ?ChatMessage
    {
        return $this->messages()->latest()->first();
    }

    /**
     * الحصول على عدد الرسائل غير المقروءة لمستخدم معين
     */
    public function getUnreadCountForUser(int $userId): int
    {
        $participant = $this->participants()->where('employee_id', $userId)->first();
        
        if (!$participant) {
            return 0;
        }

        $lastReadAt = $participant->pivot->last_read_at;
        
        return $this->messages()
                    ->where('sender_id', '!=', $userId)
                    ->when($lastReadAt, function ($query) use ($lastReadAt) {
                        return $query->where('created_at', '>', $lastReadAt);
                    })
                    ->count();
    }

    /**
     * إضافة مشارك للمحادثة
     */
    public function addParticipant(int $employeeId, string $role = 'MEMBER'): void
    {
        $this->participants()->syncWithoutDetaching([
            $employeeId => [
                'role' => $role,
                'joined_at' => now(),
                'notification_settings' => json_encode([
                    'mentions' => true,
                    'all_messages' => true,
                    'files' => true,
                ]),
            ]
        ]);

        // إرسال رسالة نظام
        $this->addSystemMessage("انضم {$this->getEmployeeName($employeeId)} للمحادثة");
    }

    /**
     * إزالة مشارك من المحادثة
     */
    public function removeParticipant(int $employeeId): void
    {
        $employeeName = $this->getEmployeeName($employeeId);
        $this->participants()->detach($employeeId);
        
        // إرسال رسالة نظام
        $this->addSystemMessage("غادر {$employeeName} المحادثة");
    }

    /**
     * إرسال رسالة
     */
    public function sendMessage(int $senderId, string $content, string $type = 'TEXT', array $attachments = []): ChatMessage
    {
        $message = $this->messages()->create([
            'sender_id' => $senderId,
            'content' => $content,
            'type' => $type,
            'attachments' => $attachments,
            'metadata' => [
                'mentions' => $this->extractMentions($content),
                'links' => $this->extractLinks($content),
            ],
        ]);

        // تحديث آخر نشاط
        $this->touch();

        // إرسال إشعارات فورية
        $this->broadcastMessage($message);

        // إشعار المذكورين
        $this->notifyMentionedUsers($message);

        return $message;
    }

    /**
     * إرسال رسالة نظام
     */
    public function addSystemMessage(string $content): ChatMessage
    {
        return $this->messages()->create([
            'sender_id' => null,
            'content' => $content,
            'type' => 'SYSTEM',
            'is_system' => true,
        ]);
    }

    /**
     * تثبيت رسالة
     */
    public function pinMessage(int $messageId, int $pinnedBy): void
    {
        $message = $this->messages()->findOrFail($messageId);
        
        $message->update([
            'is_pinned' => true,
            'pinned_by' => $pinnedBy,
            'pinned_at' => now(),
        ]);

        $this->addSystemMessage("تم تثبيت رسالة بواسطة {$this->getEmployeeName($pinnedBy)}");
    }

    /**
     * إلغاء تثبيت رسالة
     */
    public function unpinMessage(int $messageId, int $unpinnedBy): void
    {
        $message = $this->messages()->findOrFail($messageId);
        
        $message->update([
            'is_pinned' => false,
            'pinned_by' => null,
            'pinned_at' => null,
        ]);

        $this->addSystemMessage("تم إلغاء تثبيت رسالة بواسطة {$this->getEmployeeName($unpinnedBy)}");
    }

    /**
     * تحديد آخر قراءة للمستخدم
     */
    public function markAsReadForUser(int $userId): void
    {
        $this->participants()->updateExistingPivot($userId, [
            'last_read_at' => now(),
        ]);
    }

    /**
     * كتم الإشعارات للمستخدم
     */
    public function muteForUser(int $userId, bool $mute = true): void
    {
        $this->participants()->updateExistingPivot($userId, [
            'is_muted' => $mute,
        ]);
    }

    /**
     * أرشفة المحادثة
     */
    public function archive(): void
    {
        $this->update(['is_archived' => true]);
        $this->addSystemMessage("تم أرشفة المحادثة");
    }

    /**
     * إلغاء أرشفة المحادثة
     */
    public function unarchive(): void
    {
        $this->update(['is_archived' => false]);
        $this->addSystemMessage("تم إلغاء أرشفة المحادثة");
    }

    /**
     * البحث في الرسائل
     */
    public function searchMessages(string $query): \Illuminate\Database\Eloquent\Collection
    {
        return $this->messages()
                    ->where('content', 'LIKE', "%{$query}%")
                    ->where('type', '!=', 'SYSTEM')
                    ->with(['sender', 'reactions'])
                    ->orderBy('created_at', 'desc')
                    ->get();
    }

    /**
     * الحصول على الرسائل المثبتة
     */
    public function getPinnedMessages(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->messages()
                    ->where('is_pinned', true)
                    ->with(['sender'])
                    ->orderBy('pinned_at', 'desc')
                    ->get();
    }

    /**
     * الحصول على إحصائيات المحادثة
     */
    public function getStatistics(): array
    {
        return [
            'total_messages' => $this->messages()->count(),
            'total_participants' => $this->participants()->count(),
            'messages_today' => $this->messages()->whereDate('created_at', today())->count(),
            'most_active_user' => $this->getMostActiveUser(),
            'files_shared' => $this->messages()->where('type', 'FILE')->count(),
            'links_shared' => $this->messages()->whereJsonLength('metadata->links', '>', 0)->count(),
        ];
    }

    /**
     * الحصول على أكثر المستخدمين نشاطاً
     */
    protected function getMostActiveUser(): ?array
    {
        $mostActive = $this->messages()
                          ->selectRaw('sender_id, COUNT(*) as message_count')
                          ->whereNotNull('sender_id')
                          ->groupBy('sender_id')
                          ->orderBy('message_count', 'desc')
                          ->first();

        if (!$mostActive) {
            return null;
        }

        $employee = \App\Domains\HR\Models\Employee::find($mostActive->sender_id);
        
        return [
            'employee' => $employee,
            'message_count' => $mostActive->message_count,
        ];
    }

    /**
     * بث الرسالة للمشاركين
     */
    protected function broadcastMessage(ChatMessage $message): void
    {
        // استخدام Laravel Broadcasting
        broadcast(new \App\Events\MessageSent($message, $this));
    }

    /**
     * إشعار المستخدمين المذكورين
     */
    protected function notifyMentionedUsers(ChatMessage $message): void
    {
        $mentions = $message->metadata['mentions'] ?? [];
        
        foreach ($mentions as $mention) {
            $employee = \App\Domains\HR\Models\Employee::where('username', $mention)
                                                      ->orWhere('email', $mention)
                                                      ->first();
            
            if ($employee && $employee->id !== $message->sender_id) {
                $employee->notify(new \App\Notifications\ChatMentionNotification($message, $this));
            }
        }
    }

    /**
     * استخراج الإشارات من النص
     */
    protected function extractMentions(string $content): array
    {
        preg_match_all('/@(\w+)/', $content, $matches);
        return $matches[1] ?? [];
    }

    /**
     * استخراج الروابط من النص
     */
    protected function extractLinks(string $content): array
    {
        preg_match_all('/https?:\/\/[^\s]+/', $content, $matches);
        return $matches[0] ?? [];
    }

    /**
     * الحصول على اسم الموظف
     */
    protected function getEmployeeName(int $employeeId): string
    {
        $employee = \App\Domains\HR\Models\Employee::find($employeeId);
        return $employee ? $employee->name : 'مستخدم غير معروف';
    }

    /**
     * إنشاء محادثة عامة للمشروع
     */
    public static function createGeneralChat(int $projectId, int $createdBy): self
    {
        $project = Project::findOrFail($projectId);
        
        $chat = self::create([
            'project_id' => $projectId,
            'name' => 'المحادثة العامة',
            'description' => "المحادثة العامة لمشروع {$project->name}",
            'type' => 'GENERAL',
            'is_private' => false,
            'created_by' => $createdBy,
            'settings' => [
                'allow_file_sharing' => true,
                'allow_mentions' => true,
                'auto_archive_after_days' => 30,
            ],
        ]);

        // إضافة جميع أعضاء الفريق
        foreach ($project->teamMembers as $member) {
            $chat->addParticipant($member->id);
        }

        return $chat;
    }

    /**
     * البحث في المحادثات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة المحادثات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_archived', false);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة المحادثات الخاصة
     */
    public function scopePrivate($query)
    {
        return $query->where('is_private', true);
    }

    /**
     * فلترة المحادثات العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    /**
     * فلترة حسب المشارك
     */
    public function scopeForParticipant($query, int $employeeId)
    {
        return $query->whereHas('participants', function ($q) use ($employeeId) {
            $q->where('employee_id', $employeeId);
        });
    }
}
