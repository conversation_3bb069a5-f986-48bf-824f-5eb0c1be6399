<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تخفيف المخاطر - Risk Mitigation
 */
class RiskMitigation extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'risk_id',
        'strategy',
        'description',
        'action_items',
        'responsible_id',
        'target_date',
        'budget_required',
        'success_criteria',
        'status',
        'created_by',
    ];

    protected $casts = [
        'action_items' => 'array',
        'target_date' => 'date',
        'budget_required' => 'decimal:2',
        'success_criteria' => 'array',
    ];

    public function risk(): BelongsTo
    {
        return $this->belongsTo(ProjectRisk::class, 'risk_id');
    }

    public function responsible(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'responsible_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class, 'project_id');
    }
}
