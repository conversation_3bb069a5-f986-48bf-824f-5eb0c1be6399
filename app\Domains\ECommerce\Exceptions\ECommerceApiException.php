<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء API للتجارة الإلكترونية
 */
class ECommerceApiException extends ECommerceException
{
    protected string $endpoint = '';
    protected string $method = '';
    protected int $httpStatusCode = 0;
    protected array $requestData = [];
    protected array $responseData = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $endpoint = '',
        string $method = '',
        int $httpStatusCode = 0,
        array $requestData = [],
        array $responseData = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->endpoint = $endpoint;
        $this->method = $method;
        $this->httpStatusCode = $httpStatusCode;
        $this->requestData = $requestData;
        $this->responseData = $responseData;
        $this->errorCode = 'API_ERROR';
    }

    /**
     * الحصول على نقطة النهاية
     */
    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    /**
     * الحصول على طريقة HTTP
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * الحصول على رمز حالة HTTP
     */
    public function getHttpStatusCode(): int
    {
        return $this->httpStatusCode;
    }

    /**
     * الحصول على بيانات الطلب
     */
    public function getRequestData(): array
    {
        return $this->requestData;
    }

    /**
     * الحصول على بيانات الاستجابة
     */
    public function getResponseData(): array
    {
        return $this->responseData;
    }

    /**
     * تحديد ما إذا كان الخطأ قابل للإعادة
     */
    public function isRetryable(): bool
    {
        // الأخطاء القابلة للإعادة (5xx, 429, timeout)
        return in_array($this->httpStatusCode, [500, 502, 503, 504, 429]) ||
               str_contains(strtolower($this->getMessage()), 'timeout');
    }

    /**
     * تحديد ما إذا كان الخطأ مؤقت
     */
    public function isTemporary(): bool
    {
        return $this->isRetryable();
    }

    /**
     * تحديد ما إذا كان الخطأ دائم
     */
    public function isPermanent(): bool
    {
        return !$this->isTemporary();
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'endpoint' => $this->getEndpoint(),
            'method' => $this->getMethod(),
            'http_status_code' => $this->getHttpStatusCode(),
            'request_data' => $this->getRequestData(),
            'response_data' => $this->getResponseData(),
            'is_retryable' => $this->isRetryable(),
            'is_temporary' => $this->isTemporary(),
            'is_permanent' => $this->isPermanent(),
        ]);
    }
}
