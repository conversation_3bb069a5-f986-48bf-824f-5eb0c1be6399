<?php

namespace App\Domains\HR\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\HR\Models\Employee;

/**
 * طلب تحديث بيانات الموظف
 * تحقق شامل مع مراعاة البيانات الموجودة
 */
class UpdateEmployeeRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('update-employees');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        $employeeId = $this->route('employee') ?? $this->route('id');

        return [
            // Basic Information
            'employee_number' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('employees', 'employee_number')->ignore($employeeId)
            ],
            'user_id' => [
                'sometimes',
                'nullable',
                'exists:users,id',
                Rule::unique('employees', 'user_id')->ignore($employeeId)
            ],
            'department_id' => 'sometimes|exists:departments,id',
            'position_id' => 'sometimes|exists:positions,id',
            'manager_id' => 'sometimes|nullable|exists:employees,id',
            'company_id' => 'sometimes|nullable|exists:companies,id',
            'branch_id' => 'sometimes|nullable|exists:branches,id',

            // Personal Information
            'first_name' => 'sometimes|string|max:100',
            'middle_name' => 'sometimes|nullable|string|max:100',
            'last_name' => 'sometimes|string|max:100',
            'first_name_ar' => 'sometimes|nullable|string|max:100',
            'middle_name_ar' => 'sometimes|nullable|string|max:100',
            'last_name_ar' => 'sometimes|nullable|string|max:100',
            'email' => [
                'sometimes',
                'email',
                'max:255',
                Rule::unique('employees', 'email')->ignore($employeeId)
            ],
            'phone' => 'sometimes|nullable|string|max:20',
            'mobile' => 'sometimes|string|max:20',
            'date_of_birth' => 'sometimes|date|before:today',
            'gender' => ['sometimes', Rule::in(array_keys(Employee::GENDERS))],
            'marital_status' => ['sometimes', Rule::in(array_keys(Employee::MARITAL_STATUSES))],
            'nationality' => 'sometimes|string|max:100',
            'national_id' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('employees', 'national_id')->ignore($employeeId)
            ],
            'passport_number' => 'sometimes|nullable|string|max:50',
            'passport_expiry' => 'sometimes|nullable|date|after:today',
            'visa_number' => 'sometimes|nullable|string|max:50',
            'visa_expiry' => 'sometimes|nullable|date|after:today',
            'iqama_number' => 'sometimes|nullable|string|max:50',
            'iqama_expiry' => 'sometimes|nullable|date|after:today',

            // Address Information
            'address_line_1' => 'sometimes|string|max:255',
            'address_line_2' => 'sometimes|nullable|string|max:255',
            'city' => 'sometimes|string|max:100',
            'state' => 'sometimes|nullable|string|max:100',
            'postal_code' => 'sometimes|nullable|string|max:20',
            'country' => 'sometimes|string|max:100',

            // Employment Information
            'hire_date' => 'sometimes|date|before_or_equal:today',
            'probation_end_date' => 'sometimes|nullable|date',
            'contract_type' => ['sometimes', Rule::in(array_keys(Employee::CONTRACT_TYPES))],
            'employment_type' => ['sometimes', Rule::in(array_keys(Employee::EMPLOYMENT_TYPES))],
            'work_location' => 'sometimes|nullable|string|max:255',
            'status' => ['sometimes', Rule::in(array_keys(Employee::STATUSES))],
            'termination_date' => 'sometimes|nullable|date',
            'termination_reason' => 'sometimes|nullable|string|max:500',
            'rehire_eligible' => 'sometimes|boolean',

            // Salary Information
            'basic_salary' => 'sometimes|numeric|min:0',
            'currency' => 'sometimes|string|size:3',
            'salary_frequency' => 'sometimes|string|in:MONTHLY,WEEKLY,BIWEEKLY,QUARTERLY,ANNUALLY',
            'bank_name' => 'sometimes|nullable|string|max:100',
            'bank_account_number' => 'sometimes|nullable|string|max:50',
            'iban' => 'sometimes|nullable|string|max:50',

            // Emergency Contact
            'emergency_contact_name' => 'sometimes|string|max:255',
            'emergency_contact_relationship' => 'sometimes|string|max:100',
            'emergency_contact_phone' => 'sometimes|string|max:20',

            // System Fields
            'profile_picture' => 'sometimes|nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'sometimes|boolean',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'employee_number.unique' => 'رقم الموظف مستخدم مسبقاً',
            'user_id.exists' => 'المستخدم المحدد غير موجود',
            'user_id.unique' => 'المستخدم مرتبط بموظف آخر',
            'department_id.exists' => 'القسم المحدد غير موجود',
            'position_id.exists' => 'المنصب المحدد غير موجود',
            'manager_id.exists' => 'المدير المحدد غير موجود',

            // Personal Information
            'first_name.max' => 'الاسم الأول لا يجب أن يتجاوز 100 حرف',
            'last_name.max' => 'اسم العائلة لا يجب أن يتجاوز 100 حرف',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            'date_of_birth.before' => 'تاريخ الميلاد يجب أن يكون قبل اليوم',
            'gender.in' => 'الجنس المحدد غير صحيح',
            'marital_status.in' => 'الحالة الاجتماعية المحددة غير صحيحة',
            'national_id.unique' => 'رقم الهوية مستخدم مسبقاً',
            'passport_expiry.after' => 'تاريخ انتهاء جواز السفر يجب أن يكون في المستقبل',
            'visa_expiry.after' => 'تاريخ انتهاء الفيزا يجب أن يكون في المستقبل',
            'iqama_expiry.after' => 'تاريخ انتهاء الإقامة يجب أن يكون في المستقبل',

            // Employment Information
            'hire_date.before_or_equal' => 'تاريخ التوظيف لا يمكن أن يكون في المستقبل',
            'contract_type.in' => 'نوع العقد المحدد غير صحيح',
            'employment_type.in' => 'نوع التوظيف المحدد غير صحيح',
            'status.in' => 'حالة الموظف المحددة غير صحيحة',

            // Salary Information
            'basic_salary.numeric' => 'الراتب الأساسي يجب أن يكون رقماً',
            'basic_salary.min' => 'الراتب الأساسي لا يمكن أن يكون سالباً',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',

            // System Fields
            'profile_picture.image' => 'الملف يجب أن يكون صورة',
            'profile_picture.mimes' => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg',
            'profile_picture.max' => 'حجم الصورة لا يجب أن يتجاوز 2 ميجابايت',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower(trim($this->email))
            ]);
        }

        if ($this->has('national_id')) {
            $this->merge([
                'national_id' => preg_replace('/[^0-9]/', '', $this->national_id)
            ]);
        }

        if ($this->has('mobile')) {
            $this->merge([
                'mobile' => preg_replace('/[^0-9+]/', '', $this->mobile)
            ]);
        }

        if ($this->has('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+]/', '', $this->phone)
            ]);
        }
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $employeeId = $this->route('employee') ?? $this->route('id');

            // التحقق من عدم تعيين الموظف كمدير لنفسه
            if ($this->manager_id && $this->manager_id == $employeeId) {
                $validator->errors()->add('manager_id', 'لا يمكن للموظف أن يكون مديراً لنفسه');
            }

            // التحقق من صحة تاريخ انتهاء فترة التجربة
            if ($this->probation_end_date && $this->hire_date) {
                $hireDate = \Carbon\Carbon::parse($this->hire_date);
                $probationEndDate = \Carbon\Carbon::parse($this->probation_end_date);
                
                if ($probationEndDate->diffInMonths($hireDate) > 6) {
                    $validator->errors()->add('probation_end_date', 'فترة التجربة لا يمكن أن تتجاوز 6 أشهر');
                }
            }

            // التحقق من صحة العمر
            if ($this->date_of_birth) {
                $age = \Carbon\Carbon::parse($this->date_of_birth)->age;
                if ($age < 18) {
                    $validator->errors()->add('date_of_birth', 'عمر الموظف يجب أن يكون 18 سنة على الأقل');
                }
                if ($age > 65) {
                    $validator->errors()->add('date_of_birth', 'عمر الموظف لا يمكن أن يتجاوز 65 سنة');
                }
            }

            // التحقق من تاريخ الإنهاء
            if ($this->termination_date) {
                if ($this->hire_date && \Carbon\Carbon::parse($this->termination_date) < \Carbon\Carbon::parse($this->hire_date)) {
                    $validator->errors()->add('termination_date', 'تاريخ إنهاء الخدمة لا يمكن أن يكون قبل تاريخ التوظيف');
                }

                if (\Carbon\Carbon::parse($this->termination_date) > now()) {
                    $validator->errors()->add('termination_date', 'تاريخ إنهاء الخدمة لا يمكن أن يكون في المستقبل');
                }
            }

            // التحقق من متطلبات الوثائق للأجانب
            if ($this->nationality && $this->nationality !== 'Saudi') {
                if ($this->has('passport_number') && !$this->passport_number) {
                    $validator->errors()->add('passport_number', 'رقم جواز السفر مطلوب للموظفين الأجانب');
                }
            }

            // التحقق من الراتب الأدنى
            if ($this->basic_salary && $this->basic_salary < 1000) {
                $validator->errors()->add('basic_salary', 'الراتب الأساسي لا يمكن أن يكون أقل من 1000');
            }

            // التحقق من حالة الموظف
            if ($this->status === 'TERMINATED' && !$this->termination_date) {
                $validator->errors()->add('termination_date', 'تاريخ إنهاء الخدمة مطلوب عند تغيير الحالة إلى منتهي الخدمة');
            }

            if ($this->status === 'TERMINATED' && !$this->termination_reason) {
                $validator->errors()->add('termination_reason', 'سبب إنهاء الخدمة مطلوب عند تغيير الحالة إلى منتهي الخدمة');
            }
        });
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value, $key) {
            // الاحتفاظ بالحقول المنطقية حتى لو كانت false
            if (in_array($key, ['is_active', 'rehire_eligible'])) {
                return true;
            }
            return $value !== null && $value !== '';
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * التحقق من وجود تغييرات حساسة
     */
    public function hasSensitiveChanges(): bool
    {
        $sensitiveFields = [
            'basic_salary',
            'department_id',
            'position_id',
            'manager_id',
            'status',
            'contract_type',
            'employment_type',
        ];

        foreach ($sensitiveFields as $field) {
            if ($this->has($field)) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على التغييرات الحساسة
     */
    public function getSensitiveChanges(): array
    {
        $sensitiveFields = [
            'basic_salary' => 'الراتب الأساسي',
            'department_id' => 'القسم',
            'position_id' => 'المنصب',
            'manager_id' => 'المدير المباشر',
            'status' => 'حالة الموظف',
            'contract_type' => 'نوع العقد',
            'employment_type' => 'نوع التوظيف',
        ];

        $changes = [];
        foreach ($sensitiveFields as $field => $label) {
            if ($this->has($field)) {
                $changes[$field] = $label;
            }
        }

        return $changes;
    }
}
