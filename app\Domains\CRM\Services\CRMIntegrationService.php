<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\CustomerInteraction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * خدمة التكامل الشاملة - CRM Integration Service
 * تكامل CRM مع جميع الأنظمة الداخلية والخارجية
 */
class CRMIntegrationService
{
    /**
     * مزامنة شاملة مع جميع الأنظمة
     */
    public function syncAllSystems(): array
    {
        $results = [];

        try {
            // التكامل مع الأنظمة الداخلية
            $results['accounting'] = $this->syncWithAccounting();
            $results['projects'] = $this->syncWithProjects();
            $results['support'] = $this->syncWithSupport();
            $results['hr'] = $this->syncWithHR();
            $results['ecommerce'] = $this->syncWithEcommerce();
            $results['email'] = $this->syncWithEmailSystem();

            // التكامل مع الأنظمة الخارجية
            $results['external_ecommerce'] = $this->syncWithExternalEcommerce();
            $results['social_media'] = $this->syncWithSocialMedia();
            $results['telephony'] = $this->syncWithTelephony();
            $results['calendar'] = $this->syncWithCalendar();
            $results['marketing_tools'] = $this->syncWithMarketingTools();

            $results['status'] = 'success';
            $results['synced_at'] = now();

        } catch (\Exception $e) {
            Log::error('CRM Integration Error: ' . $e->getMessage());
            $results['status'] = 'error';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع نظام المحاسبة
     */
    public function syncWithAccounting(): array
    {
        $results = [
            'customers_synced' => 0,
            'invoices_synced' => 0,
            'payments_synced' => 0,
            'errors' => [],
        ];

        try {
            // مزامنة العملاء مع نظام المحاسبة
            $customers = Customer::where('updated_at', '>=', now()->subHours(24))->get();
            
            foreach ($customers as $customer) {
                $this->syncCustomerToAccounting($customer);
                $results['customers_synced']++;
            }

            // مزامنة الفواتير
            $invoices = \App\Domains\Accounting\Models\Invoice::where('updated_at', '>=', now()->subHours(24))->get();
            
            foreach ($invoices as $invoice) {
                $this->updateCustomerFromInvoice($invoice);
                $results['invoices_synced']++;
            }

            // مزامنة المدفوعات
            $payments = \App\Domains\Accounting\Models\Payment::where('created_at', '>=', now()->subHours(24))->get();
            
            foreach ($payments as $payment) {
                $this->updateCustomerFromPayment($payment);
                $results['payments_synced']++;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع نظام المشاريع
     */
    public function syncWithProjects(): array
    {
        $results = [
            'projects_created' => 0,
            'opportunities_updated' => 0,
            'customers_updated' => 0,
            'errors' => [],
        ];

        try {
            // إنشاء مشاريع من الفرص المكسوبة
            $wonOpportunities = Opportunity::where('stage', 'won')
                                         ->whereDoesntHave('projects')
                                         ->where('value', '>=', 10000)
                                         ->get();

            foreach ($wonOpportunities as $opportunity) {
                $project = $this->createProjectFromOpportunity($opportunity);
                if ($project) {
                    $results['projects_created']++;
                }
            }

            // تحديث الفرص من حالة المشاريع
            $projects = \App\Domains\Projects\Models\Project::where('updated_at', '>=', now()->subHours(24))->get();
            
            foreach ($projects as $project) {
                if ($project->opportunity_id) {
                    $this->updateOpportunityFromProject($project);
                    $results['opportunities_updated']++;
                }
                
                // تحديث بيانات العميل
                $this->updateCustomerFromProject($project);
                $results['customers_updated']++;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع نظام الدعم الفني
     */
    public function syncWithSupport(): array
    {
        $results = [
            'tickets_synced' => 0,
            'customers_updated' => 0,
            'interactions_created' => 0,
            'errors' => [],
        ];

        try {
            // مزامنة التذاكر الجديدة
            $tickets = \App\Domains\Support\Models\Ticket::where('created_at', '>=', now()->subHours(24))->get();
            
            foreach ($tickets as $ticket) {
                // إنشاء تفاعل من التذكرة
                $interaction = $this->createInteractionFromTicket($ticket);
                if ($interaction) {
                    $results['interactions_created']++;
                }
                
                // تحديث بيانات العميل
                $this->updateCustomerFromTicket($ticket);
                $results['customers_updated']++;
                $results['tickets_synced']++;
            }

            // تحديث نقاط رضا العملاء من التذاكر المحلولة
            $resolvedTickets = \App\Domains\Support\Models\Ticket::where('status', 'resolved')
                                                                ->where('updated_at', '>=', now()->subHours(24))
                                                                ->whereNotNull('satisfaction_rating')
                                                                ->get();

            foreach ($resolvedTickets as $ticket) {
                $this->updateCustomerSatisfactionFromTicket($ticket);
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع نظام الموارد البشرية
     */
    public function syncWithHR(): array
    {
        $results = [
            'sales_reps_synced' => 0,
            'assignments_updated' => 0,
            'errors' => [],
        ];

        try {
            // مزامنة مندوبي المبيعات
            $salesReps = \App\Domains\HR\Models\Employee::where('department', 'sales')
                                                       ->where('is_active', true)
                                                       ->get();

            foreach ($salesReps as $rep) {
                $this->updateSalesRepCapacity($rep);
                $results['sales_reps_synced']++;
            }

            // إعادة توزيع العملاء للمندوبين الجدد
            $newReps = \App\Domains\HR\Models\Employee::where('department', 'sales')
                                                     ->where('created_at', '>=', now()->subDays(7))
                                                     ->get();

            foreach ($newReps as $rep) {
                $assigned = $this->redistributeCustomersToNewRep($rep);
                $results['assignments_updated'] += $assigned;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع التجارة الإلكترونية الداخلية
     */
    public function syncWithEcommerce(): array
    {
        $results = [
            'orders_synced' => 0,
            'customers_updated' => 0,
            'opportunities_created' => 0,
            'errors' => [],
        ];

        try {
            // مزامنة الطلبات الجديدة
            $orders = \App\Domains\Ecommerce\Models\Order::where('created_at', '>=', now()->subHours(24))->get();
            
            foreach ($orders as $order) {
                // تحديث بيانات العميل
                $this->updateCustomerFromOrder($order);
                $results['customers_updated']++;
                
                // إنشاء فرصة للطلبات الكبيرة
                if ($order->total_amount >= 5000) {
                    $opportunity = $this->createOpportunityFromOrder($order);
                    if ($opportunity) {
                        $results['opportunities_created']++;
                    }
                }
                
                $results['orders_synced']++;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع نظام البريد الداخلي
     */
    public function syncWithEmailSystem(): array
    {
        $results = [
            'emails_processed' => 0,
            'interactions_created' => 0,
            'opportunities_created' => 0,
            'errors' => [],
        ];

        try {
            // معالجة الرسائل الجديدة
            $emails = \App\Domains\Email\Models\Email::where('created_at', '>=', now()->subHours(24))
                                                    ->where('direction', 'inbound')
                                                    ->get();

            foreach ($emails as $email) {
                // البحث عن العميل
                $customer = $this->findCustomerByEmail($email->from_email);
                
                if ($customer) {
                    // إنشاء تفاعل
                    $interaction = $this->createInteractionFromEmail($email, $customer);
                    if ($interaction) {
                        $results['interactions_created']++;
                    }
                    
                    // تحليل البريد لإنشاء فرصة محتملة
                    if ($this->isBusinessInquiry($email)) {
                        $opportunity = $this->createOpportunityFromEmail($email, $customer);
                        if ($opportunity) {
                            $results['opportunities_created']++;
                        }
                    }
                }
                
                $results['emails_processed']++;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع منصات التجارة الإلكترونية الخارجية
     */
    public function syncWithExternalEcommerce(): array
    {
        $results = [
            'shopify' => $this->syncWithShopify(),
            'woocommerce' => $this->syncWithWooCommerce(),
            'local_platforms' => $this->syncWithLocalPlatforms(),
        ];

        return $results;
    }

    /**
     * التكامل مع Shopify
     */
    protected function syncWithShopify(): array
    {
        if (!config('crm.integrations.ecommerce.shopify.enabled', false)) {
            return ['status' => 'disabled'];
        }

        $results = [
            'customers_synced' => 0,
            'orders_synced' => 0,
            'errors' => [],
        ];

        try {
            $shopifyApi = config('crm.integrations.ecommerce.shopify.api_url');
            $accessToken = config('crm.integrations.ecommerce.shopify.access_token');

            // جلب العملاء الجدد
            $response = Http::withHeaders([
                'X-Shopify-Access-Token' => $accessToken,
            ])->get($shopifyApi . '/customers.json', [
                'created_at_min' => now()->subDays(1)->toISOString(),
            ]);

            if ($response->successful()) {
                $customers = $response->json()['customers'] ?? [];
                
                foreach ($customers as $shopifyCustomer) {
                    $customer = $this->createOrUpdateCustomerFromShopify($shopifyCustomer);
                    if ($customer) {
                        $results['customers_synced']++;
                    }
                }
            }

            // جلب الطلبات الجديدة
            $ordersResponse = Http::withHeaders([
                'X-Shopify-Access-Token' => $accessToken,
            ])->get($shopifyApi . '/orders.json', [
                'created_at_min' => now()->subDays(1)->toISOString(),
                'status' => 'any',
            ]);

            if ($ordersResponse->successful()) {
                $orders = $ordersResponse->json()['orders'] ?? [];
                
                foreach ($orders as $shopifyOrder) {
                    $this->processShopifyOrder($shopifyOrder);
                    $results['orders_synced']++;
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع وسائل التواصل الاجتماعي
     */
    public function syncWithSocialMedia(): array
    {
        $results = [
            'facebook' => $this->syncWithFacebook(),
            'linkedin' => $this->syncWithLinkedIn(),
            'twitter' => $this->syncWithTwitter(),
            'instagram' => $this->syncWithInstagram(),
        ];

        return $results;
    }

    /**
     * التكامل مع أنظمة الهاتف
     */
    public function syncWithTelephony(): array
    {
        if (!config('crm.integrations.telephony.enabled', false)) {
            return ['status' => 'disabled'];
        }

        $results = [
            'calls_logged' => 0,
            'recordings_processed' => 0,
            'errors' => [],
        ];

        try {
            $provider = config('crm.integrations.telephony.provider');
            
            switch ($provider) {
                case 'twilio':
                    $results = $this->syncWithTwilio();
                    break;
                case '3cx':
                    $results = $this->syncWith3CX();
                    break;
                case 'asterisk':
                    $results = $this->syncWithAsterisk();
                    break;
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * التكامل مع التقويم
     */
    public function syncWithCalendar(): array
    {
        $results = [
            'google_calendar' => $this->syncWithGoogleCalendar(),
            'outlook' => $this->syncWithOutlook(),
            'exchange' => $this->syncWithExchange(),
        ];

        return $results;
    }

    // دوال مساعدة للتكامل

    protected function syncCustomerToAccounting(Customer $customer): void
    {
        // منطق مزامنة العميل مع نظام المحاسبة
        $accountingCustomer = \App\Domains\Accounting\Models\Customer::updateOrCreate(
            ['crm_customer_id' => $customer->id],
            [
                'name' => $customer->full_name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'address' => $customer->full_address,
                'tax_number' => $customer->tax_number,
                'credit_limit' => $customer->credit_limit,
                'payment_terms' => $customer->payment_terms,
            ]
        );
    }

    protected function updateCustomerFromInvoice($invoice): void
    {
        if ($invoice->customer_id) {
            $customer = Customer::find($invoice->customer_id);
            if ($customer) {
                // تحديث الرصيد المستحق
                $outstandingBalance = $customer->invoices()
                                              ->where('status', 'pending')
                                              ->sum('total_amount');
                
                $customer->update(['outstanding_balance' => $outstandingBalance]);
            }
        }
    }

    protected function createProjectFromOpportunity(Opportunity $opportunity): ?\App\Domains\Projects\Models\Project
    {
        return \App\Domains\Projects\Models\Project::create([
            'name' => "مشروع: {$opportunity->title}",
            'description' => $opportunity->description,
            'customer_id' => $opportunity->customer_id,
            'opportunity_id' => $opportunity->id,
            'budget' => $opportunity->value,
            'status' => 'planning',
            'start_date' => now(),
            'manager_id' => $opportunity->assigned_to,
            'created_by' => 1, // System user
        ]);
    }

    protected function createInteractionFromTicket($ticket): ?CustomerInteraction
    {
        if (!$ticket->customer_id) {
            return null;
        }

        return CustomerInteraction::create([
            'customer_id' => $ticket->customer_id,
            'user_id' => $ticket->assigned_to,
            'type' => 'support',
            'direction' => 'inbound',
            'subject' => $ticket->subject,
            'description' => $ticket->description,
            'status' => 'completed',
            'occurred_at' => $ticket->created_at,
            'outcome' => $ticket->status === 'resolved' ? 'successful' : 'in_progress',
            'sentiment' => $this->analyzeSentimentFromTicket($ticket),
            'related_id' => $ticket->id,
            'related_type' => get_class($ticket),
        ]);
    }

    protected function findCustomerByEmail(string $email): ?Customer
    {
        return Customer::where('email', $email)
                      ->orWhereHas('contacts', function ($query) use ($email) {
                          $query->where('email', $email);
                      })
                      ->first();
    }

    protected function isBusinessInquiry($email): bool
    {
        $businessKeywords = [
            'quote', 'price', 'cost', 'service', 'product', 'business',
            'عرض', 'سعر', 'تكلفة', 'خدمة', 'منتج', 'عمل',
            'devis', 'prix', 'coût', 'service', 'produit', 'affaire'
        ];

        $content = strtolower($email->subject . ' ' . $email->body);
        
        foreach ($businessKeywords as $keyword) {
            if (str_contains($content, strtolower($keyword))) {
                return true;
            }
        }

        return false;
    }

    protected function createOrUpdateCustomerFromShopify(array $shopifyCustomer): ?Customer
    {
        return Customer::updateOrCreate(
            ['email' => $shopifyCustomer['email']],
            [
                'customer_number' => 'SH-' . $shopifyCustomer['id'],
                'first_name' => $shopifyCustomer['first_name'],
                'last_name' => $shopifyCustomer['last_name'],
                'phone' => $shopifyCustomer['phone'],
                'source' => 'shopify',
                'status' => 'active',
                'metadata' => [
                    'shopify_id' => $shopifyCustomer['id'],
                    'shopify_data' => $shopifyCustomer,
                ],
            ]
        );
    }

    // دوال مساعدة إضافية (ستكون فارغة للآن)
    protected function updateCustomerFromPayment($payment): void { }
    protected function updateOpportunityFromProject($project): void { }
    protected function updateCustomerFromProject($project): void { }
    protected function updateCustomerFromTicket($ticket): void { }
    protected function updateCustomerSatisfactionFromTicket($ticket): void { }
    protected function updateSalesRepCapacity($rep): void { }
    protected function redistributeCustomersToNewRep($rep): int { return 0; }
    protected function updateCustomerFromOrder($order): void { }
    protected function createOpportunityFromOrder($order): ?Opportunity { return null; }
    protected function createInteractionFromEmail($email, $customer): ?CustomerInteraction { return null; }
    protected function createOpportunityFromEmail($email, $customer): ?Opportunity { return null; }
    protected function syncWithWooCommerce(): array { return []; }
    protected function syncWithLocalPlatforms(): array { return []; }
    protected function processShopifyOrder($order): void { }
    protected function syncWithFacebook(): array { return []; }
    protected function syncWithLinkedIn(): array { return []; }
    protected function syncWithTwitter(): array { return []; }
    protected function syncWithInstagram(): array { return []; }
    protected function syncWithTwilio(): array { return []; }
    protected function syncWith3CX(): array { return []; }
    protected function syncWithAsterisk(): array { return []; }
    protected function syncWithGoogleCalendar(): array { return []; }
    protected function syncWithOutlook(): array { return []; }
    protected function syncWithExchange(): array { return []; }
    protected function analyzeSentimentFromTicket($ticket): string { return 'neutral'; }
}
