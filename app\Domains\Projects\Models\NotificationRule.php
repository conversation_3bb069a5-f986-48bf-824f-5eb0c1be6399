<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قاعدة الإشعار - Notification Rule
 */
class NotificationRule extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'description',
        'trigger_event',
        'conditions',
        'recipients',
        'channels',
        'template_id',
        'is_active',
        'priority',
        'delay_minutes',
        'frequency_limit',
        'scope',
        'target_id',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'conditions' => 'array',
        'recipients' => 'array',
        'channels' => 'array',
        'is_active' => 'boolean',
        'delay_minutes' => 'integer',
        'metadata' => 'array',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(NotificationTemplate::class, 'template_id');
    }
}
