<?php

namespace App\Domains\Projects\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Domains\Projects\Services\AdvancedProjectManagementService;
use App\Domains\Projects\Services\SmartTaskManagementService;
use App\Domains\Projects\Services\ResourceAllocationService;
use App\Domains\Projects\Services\TimeTrackingService;
use App\Domains\Projects\Services\ProjectAccountingService;
use App\Domains\Projects\Services\CollaborationService;
use App\Domains\Projects\Services\ProjectAnalyticsService;
use App\Domains\Projects\Services\AgileProjectService;

/**
 * Projects Domain Service Provider
 * مزود خدمات مجال إدارة المشاريع
 */
class ProjectsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل الخدمات الأساسية
        $this->app->singleton(AdvancedProjectManagementService::class);
        $this->app->singleton(SmartTaskManagementService::class);
        $this->app->singleton(ResourceAllocationService::class);
        $this->app->singleton(TimeTrackingService::class);
        $this->app->singleton(ProjectAccountingService::class);
        $this->app->singleton(CollaborationService::class);
        $this->app->singleton(ProjectAnalyticsService::class);
        $this->app->singleton(AgileProjectService::class);

        // تسجيل الخدمات الجديدة المطورة
        $this->app->singleton(TaskManagementService::class);
        $this->app->singleton(\App\Domains\Projects\Services\TimeTrackingService::class);

        // تسجيل التكوينات
        $this->mergeConfigFrom(
            config_path('projects.php'),
            'projects'
        );

        // تسجيل الواجهات والتنفيذات
        $this->registerContracts();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل المسارات
        $this->loadRoutes();

        // تحميل الـ Views
        $this->loadViews();

        // تحميل الـ Migrations
        $this->loadMigrations();

        // تحميل الترجمات
        $this->loadTranslations();

        // تسجيل السياسات
        $this->registerPolicies();

        // تسجيل نظام إدارة المشاريع الذكي
        $this->registerIntelligentProjectManagement();

        // تسجيل نظام الذكاء الاصطناعي لإدارة المشاريع
        $this->registerProjectAIServices();

        // تسجيل نظام إدارة الموارد المتقدم
        $this->registerAdvancedResourceManagement();

        // تسجيل خدمات التحليل التنبؤي للمشاريع
        $this->registerProjectPredictiveAnalytics();

        // نشر الموارد المتقدمة لإدارة المشاريع
        $this->publishFiles();
    }

    /**
     * تسجيل الواجهات والتنفيذات
     */
    protected function registerContracts(): void
    {
        $this->app->bind(
            \App\Domains\Projects\Contracts\ProjectRepositoryInterface::class,
            \App\Domains\Projects\Repositories\ProjectRepository::class
        );

        $this->app->bind(
            \App\Domains\Projects\Contracts\TaskRepositoryInterface::class,
            \App\Domains\Projects\Repositories\TaskRepository::class
        );

        $this->app->bind(
            \App\Domains\Projects\Contracts\TimeEntryRepositoryInterface::class,
            \App\Domains\Projects\Repositories\TimeEntryRepository::class
        );

        $this->app->bind(
            \App\Domains\Projects\Contracts\ProjectServiceInterface::class,
            AdvancedProjectManagementService::class
        );
    }

    /**
     * تحميل المسارات
     */
    protected function loadRoutes(): void
    {
        if ($this->app->routesAreCached()) {
            return;
        }

        // API Routes
        Route::prefix('api/projects')
            ->middleware(['api', 'auth:sanctum'])
            ->namespace('App\Domains\Projects\Controllers')
            ->group(__DIR__ . '/../Routes/api.php');

        // Web Routes
        Route::prefix('projects')
            ->middleware(['web', 'auth'])
            ->namespace('App\Domains\Projects\Controllers')
            ->group(__DIR__ . '/../Routes/web.php');
    }

    /**
     * تحميل الـ Views
     */
    protected function loadViews(): void
    {
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'projects');
    }

    /**
     * تحميل الـ Migrations
     */
    protected function loadMigrations(): void
    {
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations/projects');
    }

    /**
     * تحميل الترجمات
     */
    protected function loadTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'projects');
    }

    /**
     * تسجيل السياسات
     */
    protected function registerPolicies(): void
    {
        $policies = [
            \App\Domains\Projects\Models\Project::class => \App\Domains\Projects\Policies\ProjectPolicy::class,
            \App\Domains\Projects\Models\Task::class => \App\Domains\Projects\Policies\TaskPolicy::class,
            \App\Domains\Projects\Models\TimeEntry::class => \App\Domains\Projects\Policies\TimeEntryPolicy::class,
            \App\Domains\Projects\Models\ProjectMilestone::class => \App\Domains\Projects\Policies\ProjectMilestonePolicy::class,
        ];

        foreach ($policies as $model => $policy) {
            \Illuminate\Support\Facades\Gate::policy($model, $policy);
        }
    }

    /**
     * تسجيل الأحداث والمستمعين
     */
    protected function registerEvents(): void
    {
        $events = [
            \App\Domains\Projects\Events\ProjectCreated::class => [
                \App\Domains\Projects\Listeners\SetupProjectStructure::class,
                \App\Domains\Projects\Listeners\NotifyTeamMembers::class,
                \App\Domains\Projects\Listeners\CreateProjectAccounting::class,
                \App\Domains\Projects\Listeners\SendProjectCreatedNotification::class,
            ],
            \App\Domains\Projects\Events\TaskCreated::class => [
                \App\Domains\Projects\Listeners\SendTaskCreatedNotification::class,
                \App\Domains\Projects\Listeners\UpdateProjectProgress::class,
            ],
            \App\Domains\Projects\Events\TaskAssigned::class => [
                \App\Domains\Projects\Listeners\SendTaskAssignedNotification::class,
            ],
            \App\Domains\Projects\Events\TaskStatusChanged::class => [
                \App\Domains\Projects\Listeners\SendTaskStatusChangedNotification::class,
                \App\Domains\Projects\Listeners\UpdateProjectProgress::class,
            ],
            \App\Domains\Projects\Events\TaskCompleted::class => [
                \App\Domains\Projects\Listeners\UpdateProjectProgress::class,
                \App\Domains\Projects\Listeners\CheckMilestones::class,
                \App\Domains\Projects\Listeners\NotifyStakeholders::class,
            ],
            \App\Domains\Projects\Events\ProjectCompleted::class => [
                \App\Domains\Projects\Listeners\GenerateProjectReport::class,
                \App\Domains\Projects\Listeners\ArchiveProjectData::class,
                \App\Domains\Projects\Listeners\UpdateCRMOpportunity::class,
            ],
        ];

        foreach ($events as $event => $listeners) {
            foreach ($listeners as $listener) {
                \Illuminate\Support\Facades\Event::listen($event, $listener);
            }
        }
    }



    /**
     * تسجيل نظام إدارة المشاريع الذكي
     */
    protected function registerIntelligentProjectManagement(): void
    {
        // تسجيل محرك إدارة المشاريع الذكي
        $this->app->singleton('projects.intelligent_engine', function ($app) {
            return new \App\Domains\Projects\Services\Intelligence\IntelligentProjectEngine(
                $app['db'],
                $app['cache.store'],
                $app['projects.ai_processor']
            );
        });

        // تسجيل خدمة إدارة دورة حياة المشروع
        $this->app->singleton('projects.lifecycle_manager', function ($app) {
            return new \App\Domains\Projects\Services\Lifecycle\ProjectLifecycleManager(
                $app['projects.intelligent_engine'],
                $app['projects.risk_analyzer'],
                $app['events']
            );
        });

        // تسجيل خدمة التخطيط الذكي
        $this->app->singleton('projects.smart_planner', function ($app) {
            return new \App\Domains\Projects\Services\Planning\IntelligentProjectPlanner(
                $app['projects.ml_model'],
                $app['projects.resource_optimizer'],
                $app['cache.store']
            );
        });
    }

    /**
     * تسجيل نظام الذكاء الاصطناعي لإدارة المشاريع
     */
    protected function registerProjectAIServices(): void
    {
        // تسجيل معالج الذكاء الاصطناعي للمشاريع
        $this->app->singleton('projects.ai_processor', function ($app) {
            return new \App\Domains\Projects\Services\AI\ProjectAIProcessor(
                config('projects.ai_models'),
                $app['log']
            );
        });

        // تسجيل نموذج التعلم الآلي للمشاريع
        $this->app->singleton('projects.ml_model', function ($app) {
            return new \App\Domains\Projects\Services\MachineLearning\ProjectPredictionModel(
                config('projects.ml_model_path'),
                $app['projects.ai_processor']
            );
        });

        // تسجيل محلل المخاطر الذكي
        $this->app->singleton('projects.risk_analyzer', function ($app) {
            return new \App\Domains\Projects\Services\RiskManagement\IntelligentRiskAnalyzer(
                $app['db'],
                $app['projects.ai_processor'],
                $app['projects.pattern_detector']
            );
        });

        // تسجيل كاشف الأنماط
        $this->app->singleton('projects.pattern_detector', function ($app) {
            return new \App\Domains\Projects\Services\Analytics\ProjectPatternDetector(
                $app['db'],
                $app['projects.ml_model']
            );
        });
    }

    /**
     * تسجيل نظام إدارة الموارد المتقدم
     */
    protected function registerAdvancedResourceManagement(): void
    {
        // تسجيل محسن الموارد
        $this->app->singleton('projects.resource_optimizer', function ($app) {
            return new \App\Domains\Projects\Services\Resources\IntelligentResourceOptimizer(
                $app['db'],
                $app['projects.ml_model'],
                $app['cache.store']
            );
        });
    }

    /**
     * تسجيل خدمات التحليل التنبؤي للمشاريع
     */
    protected function registerProjectPredictiveAnalytics(): void
    {
        // تسجيل محرك التحليل التنبؤي
        $this->app->singleton('projects.predictive_engine', function ($app) {
            return new \App\Domains\Projects\Services\Predictive\ProjectPredictiveEngine(
                $app['projects.ml_model'],
                $app['projects.data_processor']
            );
        });
    }

    /**
     * نشر الملفات
     */
    protected function publishFiles(): void
    {
        if ($this->app->runningInConsole()) {
            // نشر ملف التكوين
            $this->publishes([
                __DIR__ . '/../../../config/projects.php' => config_path('projects.php'),
            ], 'projects-config');

            // نشر الـ Views
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/projects'),
            ], 'projects-views');

            // نشر الترجمات
            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/projects'),
            ], 'projects-lang');

            // نشر الأصول
            $this->publishes([
                __DIR__ . '/../Resources/assets' => public_path('vendor/projects'),
            ], 'projects-assets');
        }
    }

    /**
     * الحصول على الخدمات المتقدمة لإدارة المشاريع
     */
    public function provides(): array
    {
        return [
            // Intelligent Project Management Core
            'projects.intelligent_engine',
            'projects.lifecycle_manager',
            'projects.smart_planner',

            // AI & Machine Learning Services
            'projects.ai_processor',
            'projects.ml_model',
            'projects.risk_analyzer',
            'projects.pattern_detector',

            // Resource Management
            'projects.resource_optimizer',
            'projects.capacity_planner',
            'projects.allocation_engine',

            // Predictive Analytics
            'projects.predictive_engine',
            'projects.timeline_predictor',
            'projects.cost_predictor',
        ];
    }
}
