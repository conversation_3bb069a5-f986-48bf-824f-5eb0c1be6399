<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج إعلان التوظيف - نظام التوظيف الذكي
 */
class JobPosting extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'title',
        'title_ar',
        'title_en',
        'title_fr',
        'description',
        'description_ar',
        'description_en',
        'description_fr',
        'position_id',
        'department_id',
        'company_id',
        'branch_id',
        'employment_type',
        'contract_type',
        'work_location',
        'remote_work_allowed',
        'requirements',
        'qualifications',
        'skills_required',
        'experience_required',
        'min_salary',
        'max_salary',
        'currency',
        'benefits',
        'application_deadline',
        'start_date',
        'status',
        'priority',
        'posting_channels',
        'external_job_id',
        'application_instructions',
        'contact_email',
        'contact_phone',
        'published_at',
        'closed_at',
        'metadata',
    ];

    protected $casts = [
        'requirements' => 'array',
        'qualifications' => 'array',
        'skills_required' => 'array',
        'benefits' => 'array',
        'posting_channels' => 'array',
        'min_salary' => 'decimal:2',
        'max_salary' => 'decimal:2',
        'remote_work_allowed' => 'boolean',
        'application_deadline' => 'date',
        'start_date' => 'date',
        'published_at' => 'datetime',
        'closed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * حالات الإعلان
     */
    const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING_APPROVAL' => 'في انتظار الموافقة',
        'PUBLISHED' => 'منشور',
        'PAUSED' => 'متوقف مؤقتاً',
        'CLOSED' => 'مغلق',
        'FILLED' => 'تم الشغل',
        'CANCELLED' => 'ملغي',
    ];

    /**
     * أولويات الإعلان
     */
    const PRIORITIES = [
        'LOW' => 'منخفضة',
        'NORMAL' => 'عادية',
        'HIGH' => 'عالية',
        'URGENT' => 'عاجلة',
    ];

    /**
     * قنوات النشر
     */
    const POSTING_CHANNELS = [
        'COMPANY_WEBSITE' => 'موقع الشركة',
        'LINKEDIN' => 'لينكدإن',
        'BAYT' => 'بيت.كوم',
        'AKHTABOOT' => 'اختبوط',
        'EMPLOI_MA' => 'Emploi.ma',
        'APEC' => 'APEC',
        'INDEED' => 'Indeed',
        'GLASSDOOR' => 'Glassdoor',
        'FACEBOOK' => 'فيسبوك',
        'TWITTER' => 'تويتر',
        'INSTAGRAM' => 'إنستغرام',
        'UNIVERSITY_PARTNERSHIPS' => 'شراكات جامعية',
        'RECRUITMENT_AGENCIES' => 'وكالات التوظيف',
    ];

    /**
     * المنصب
     */
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    /**
     * القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * الفرع
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * طلبات التقديم
     */
    public function applications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * الطلبات النشطة
     */
    public function activeApplications(): HasMany
    {
        return $this->applications()->where('status', '!=', 'WITHDRAWN');
    }

    /**
     * الحصول على العنوان المترجم
     */
    public function getLocalizedTitle(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->title_ar ?? $this->title,
            'en' => $this->title_en ?? $this->title,
            'fr' => $this->title_fr ?? $this->title,
            default => $this->title,
        };
    }

    /**
     * الحصول على الوصف المترجم
     */
    public function getLocalizedDescription(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->description_ar ?? $this->description,
            'en' => $this->description_en ?? $this->description,
            'fr' => $this->description_fr ?? $this->description,
            default => $this->description,
        };
    }

    /**
     * الحصول على عدد الطلبات
     */
    public function getApplicationsCountAttribute(): int
    {
        return $this->applications()->count();
    }

    /**
     * الحصول على عدد الطلبات الجديدة
     */
    public function getNewApplicationsCountAttribute(): int
    {
        return $this->applications()->where('status', 'SUBMITTED')->count();
    }

    /**
     * التحقق من انتهاء موعد التقديم
     */
    public function isExpired(): bool
    {
        return $this->application_deadline && now() > $this->application_deadline;
    }

    /**
     * التحقق من كون الإعلان نشط
     */
    public function isActive(): bool
    {
        return $this->status === 'PUBLISHED' && !$this->isExpired();
    }

    /**
     * نشر الإعلان
     */
    public function publish(): bool
    {
        if ($this->status !== 'DRAFT' && $this->status !== 'PENDING_APPROVAL') {
            return false;
        }

        $this->update([
            'status' => 'PUBLISHED',
            'published_at' => now(),
        ]);

        // نشر على القنوات المحددة
        $this->publishToChannels();

        return true;
    }

    /**
     * إغلاق الإعلان
     */
    public function close(string $reason = null): bool
    {
        $this->update([
            'status' => 'CLOSED',
            'closed_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'close_reason' => $reason,
            ]),
        ]);

        return true;
    }

    /**
     * وضع علامة "تم الشغل"
     */
    public function markAsFilled(JobApplication $selectedApplication = null): bool
    {
        $this->update([
            'status' => 'FILLED',
            'closed_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'selected_application_id' => $selectedApplication?->id,
                'filled_at' => now(),
            ]),
        ]);

        // رفض باقي الطلبات
        $this->applications()
            ->where('status', '!=', 'REJECTED')
            ->where('id', '!=', $selectedApplication?->id)
            ->update(['status' => 'REJECTED']);

        return true;
    }

    /**
     * نشر على القنوات الخارجية
     */
    protected function publishToChannels(): void
    {
        $recruitmentService = app(\App\Domains\HR\Services\RecruitmentService::class);
        
        foreach ($this->posting_channels ?? [] as $channel) {
            try {
                $recruitmentService->publishToChannel($this, $channel);
            } catch (\Exception $e) {
                \Log::error("فشل في نشر الإعلان على {$channel}", [
                    'job_posting_id' => $this->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * الحصول على إحصائيات الطلبات
     */
    public function getApplicationStatistics(): array
    {
        $applications = $this->applications;

        return [
            'total' => $applications->count(),
            'new' => $applications->where('status', 'SUBMITTED')->count(),
            'under_review' => $applications->where('status', 'UNDER_REVIEW')->count(),
            'shortlisted' => $applications->where('status', 'SHORTLISTED')->count(),
            'interviewed' => $applications->where('status', 'INTERVIEWED')->count(),
            'offered' => $applications->where('status', 'OFFERED')->count(),
            'hired' => $applications->where('status', 'HIRED')->count(),
            'rejected' => $applications->where('status', 'REJECTED')->count(),
            'withdrawn' => $applications->where('status', 'WITHDRAWN')->count(),
        ];
    }

    /**
     * الحصول على أفضل المرشحين
     */
    public function getTopCandidates(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->applications()
            ->with('candidate')
            ->where('status', '!=', 'REJECTED')
            ->where('status', '!=', 'WITHDRAWN')
            ->orderByDesc('ai_score')
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get();
    }

    /**
     * إنشاء رابط مشاركة
     */
    public function generateShareableLink(): string
    {
        return route('jobs.show', [
            'job' => $this->id,
            'slug' => \Str::slug($this->title),
        ]);
    }

    /**
     * نطاق للإعلانات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'PUBLISHED')
            ->where(function ($q) {
                $q->whereNull('application_deadline')
                  ->orWhere('application_deadline', '>', now());
            });
    }

    /**
     * نطاق للإعلانات المنتهية
     */
    public function scopeExpired($query)
    {
        return $query->where('application_deadline', '<', now())
            ->where('status', 'PUBLISHED');
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق حسب القسم
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * نطاق حسب الأولوية
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * نطاق للعمل عن بُعد
     */
    public function scopeRemoteWork($query)
    {
        return $query->where('remote_work_allowed', true);
    }

    /**
     * نطاق حسب نطاق الراتب
     */
    public function scopeBySalaryRange($query, float $minSalary = null, float $maxSalary = null)
    {
        if ($minSalary) {
            $query->where('max_salary', '>=', $minSalary);
        }

        if ($maxSalary) {
            $query->where('min_salary', '<=', $maxSalary);
        }

        return $query;
    }

    /**
     * نطاق للبحث النصي
     */
    public function scopeSearch($query, string $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('title', 'LIKE', "%{$term}%")
              ->orWhere('title_ar', 'LIKE', "%{$term}%")
              ->orWhere('title_en', 'LIKE', "%{$term}%")
              ->orWhere('description', 'LIKE', "%{$term}%")
              ->orWhere('description_ar', 'LIKE', "%{$term}%");
        });
    }
}
