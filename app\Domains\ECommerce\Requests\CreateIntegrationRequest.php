<?php

namespace App\Domains\ECommerce\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب إنشاء تكامل جديد
 */
class CreateIntegrationRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return true; // يمكن تخصيص هذا حسب نظام الصلاحيات
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'platform_id' => 'required|exists:e_commerce_platforms,id',
            'store_id' => 'required|exists:stores,id',
            'authentication_config' => 'required|array',
            'sync_config' => 'nullable|array',
            'webhook_config' => 'nullable|array',
            'status' => 'nullable|in:active,inactive,pending',
            'description' => 'nullable|string|max:1000',
            
            // التحقق من إعدادات المصادقة حسب المنصة
            'authentication_config.access_token' => 'required_if:platform_slug,shopify,salla,zid,youcan,noon,jumia',
            'authentication_config.api_key' => 'required_if:platform_slug,woocommerce,magento,bigcommerce',
            'authentication_config.consumer_key' => 'required_if:platform_slug,woocommerce',
            'authentication_config.consumer_secret' => 'required_if:platform_slug,woocommerce',
            'authentication_config.base_url' => 'required_if:platform_slug,woocommerce,magento',
            'authentication_config.store_hash' => 'required_if:platform_slug,bigcommerce',
            'authentication_config.marketplace_id' => 'required_if:platform_slug,amazon,ebay',
            'authentication_config.seller_id' => 'required_if:platform_slug,amazon,noon,jumia',
            'authentication_config.user_id' => 'required_if:platform_slug,jumia',
            'authentication_config.store_id' => 'required_if:platform_slug,youcan',
            'authentication_config.country' => 'required_if:platform_slug,jumia',
            'authentication_config.environment' => 'nullable|in:production,sandbox',
            
            // إعدادات المزامنة
            'sync_config.auto_sync' => 'nullable|boolean',
            'sync_config.sync_interval' => 'nullable|integer|min:5|max:1440', // بالدقائق
            'sync_config.sync_products' => 'nullable|boolean',
            'sync_config.sync_orders' => 'nullable|boolean',
            'sync_config.sync_customers' => 'nullable|boolean',
            'sync_config.sync_categories' => 'nullable|boolean',
            'sync_config.sync_inventory' => 'nullable|boolean',
            'sync_config.batch_size' => 'nullable|integer|min:1|max:1000',
            
            // إعدادات Webhooks
            'webhook_config.enabled' => 'nullable|boolean',
            'webhook_config.events' => 'nullable|array',
            'webhook_config.events.*' => 'string',
            'webhook_config.secret' => 'nullable|string',
            'webhook_config.url' => 'nullable|url',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'name.required' => 'اسم التكامل مطلوب',
            'name.max' => 'اسم التكامل يجب ألا يتجاوز 255 حرف',
            'platform_id.required' => 'منصة التجارة الإلكترونية مطلوبة',
            'platform_id.exists' => 'منصة التجارة الإلكترونية المحددة غير موجودة',
            'store_id.required' => 'المتجر مطلوب',
            'store_id.exists' => 'المتجر المحدد غير موجود',
            'authentication_config.required' => 'إعدادات المصادقة مطلوبة',
            'authentication_config.access_token.required_if' => 'رمز الوصول مطلوب لهذه المنصة',
            'authentication_config.api_key.required_if' => 'مفتاح API مطلوب لهذه المنصة',
            'authentication_config.consumer_key.required_if' => 'مفتاح المستهلك مطلوب لهذه المنصة',
            'authentication_config.consumer_secret.required_if' => 'سر المستهلك مطلوب لهذه المنصة',
            'authentication_config.base_url.required_if' => 'رابط الموقع مطلوب لهذه المنصة',
            'authentication_config.store_hash.required_if' => 'معرف المتجر مطلوب لهذه المنصة',
            'authentication_config.marketplace_id.required_if' => 'معرف السوق مطلوب لهذه المنصة',
            'authentication_config.seller_id.required_if' => 'معرف البائع مطلوب لهذه المنصة',
            'authentication_config.user_id.required_if' => 'معرف المستخدم مطلوب لهذه المنصة',
            'authentication_config.store_id.required_if' => 'معرف المتجر مطلوب لهذه المنصة',
            'authentication_config.country.required_if' => 'الدولة مطلوبة لهذه المنصة',
            'status.in' => 'حالة التكامل يجب أن تكون: نشط، غير نشط، أو في الانتظار',
            'description.max' => 'الوصف يجب ألا يتجاوز 1000 حرف',
            'sync_config.sync_interval.min' => 'فترة المزامنة يجب أن تكون على الأقل 5 دقائق',
            'sync_config.sync_interval.max' => 'فترة المزامنة يجب ألا تتجاوز 1440 دقيقة (24 ساعة)',
            'sync_config.batch_size.min' => 'حجم الدفعة يجب أن يكون على الأقل 1',
            'sync_config.batch_size.max' => 'حجم الدفعة يجب ألا يتجاوز 1000',
            'webhook_config.url.url' => 'رابط Webhook يجب أن يكون صحيحاً',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'name' => 'اسم التكامل',
            'platform_id' => 'منصة التجارة الإلكترونية',
            'store_id' => 'المتجر',
            'authentication_config' => 'إعدادات المصادقة',
            'sync_config' => 'إعدادات المزامنة',
            'webhook_config' => 'إعدادات Webhooks',
            'status' => 'الحالة',
            'description' => 'الوصف',
        ];
    }

    /**
     * إعداد البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // الحصول على معلومات المنصة لتحديد نوع المصادقة المطلوب
        if ($this->has('platform_id')) {
            $platform = \App\Domains\ECommerce\Models\ECommercePlatform::find($this->platform_id);
            if ($platform) {
                $this->merge(['platform_slug' => $platform->slug]);
            }
        }

        // تعيين القيم الافتراضية
        if (!$this->has('status')) {
            $this->merge(['status' => 'pending']);
        }

        if (!$this->has('sync_config')) {
            $this->merge(['sync_config' => [
                'auto_sync' => true,
                'sync_interval' => 60, // كل ساعة
                'sync_products' => true,
                'sync_orders' => true,
                'sync_customers' => true,
                'sync_categories' => true,
                'sync_inventory' => true,
                'batch_size' => 50,
            ]]);
        }

        if (!$this->has('webhook_config')) {
            $this->merge(['webhook_config' => [
                'enabled' => false,
                'events' => [],
            ]]);
        }
    }

    /**
     * التحقق من صحة البيانات بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من عدم وجود تكامل مكرر لنفس المنصة والمتجر
            if ($this->platform_id && $this->store_id) {
                $exists = \App\Domains\ECommerce\Models\ECommerceIntegration::where('platform_id', $this->platform_id)
                    ->where('store_id', $this->store_id)
                    ->exists();

                if ($exists) {
                    $validator->errors()->add('platform_id', 'يوجد تكامل مسبق لهذه المنصة مع هذا المتجر');
                }
            }

            // التحقق من صحة إعدادات المصادقة حسب المنصة
            if ($this->platform_slug && $this->authentication_config) {
                $this->validatePlatformSpecificConfig($validator);
            }
        });
    }

    /**
     * التحقق من إعدادات المنصة المحددة
     */
    protected function validatePlatformSpecificConfig($validator): void
    {
        $config = $this->authentication_config;
        $platform = $this->platform_slug;

        switch ($platform) {
            case 'shopify':
                if (empty($config['shop_domain'])) {
                    $validator->errors()->add('authentication_config.shop_domain', 'نطاق المتجر مطلوب لـ Shopify');
                }
                break;

            case 'woocommerce':
                if (empty($config['base_url'])) {
                    $validator->errors()->add('authentication_config.base_url', 'رابط الموقع مطلوب لـ WooCommerce');
                }
                if (!filter_var($config['base_url'] ?? '', FILTER_VALIDATE_URL)) {
                    $validator->errors()->add('authentication_config.base_url', 'رابط الموقع غير صحيح');
                }
                break;

            case 'amazon':
                if (empty($config['region'])) {
                    $validator->errors()->add('authentication_config.region', 'المنطقة مطلوبة لـ Amazon');
                }
                if (!in_array($config['region'] ?? '', ['us-east-1', 'eu-west-1', 'us-west-2'])) {
                    $validator->errors()->add('authentication_config.region', 'المنطقة غير مدعومة');
                }
                break;

            case 'jumia':
                if (empty($config['country'])) {
                    $validator->errors()->add('authentication_config.country', 'الدولة مطلوبة لـ Jumia');
                }
                if (!in_array($config['country'] ?? '', ['eg', 'ma', 'ng', 'ke', 'ug'])) {
                    $validator->errors()->add('authentication_config.country', 'الدولة غير مدعومة في Jumia');
                }
                break;
        }
    }
}
