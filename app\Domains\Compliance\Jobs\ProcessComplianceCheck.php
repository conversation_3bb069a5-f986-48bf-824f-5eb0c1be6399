<?php

namespace App\Domains\Compliance\Jobs;

use App\Domains\Compliance\Models\Company;
use App\Domains\Compliance\Services\ComplianceManagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * مهمة معالجة فحص الامتثال
 */
class ProcessComplianceCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    public function __construct(
        public Company $company,
        public array $options = []
    ) {}

    /**
     * تنفيذ المهمة
     */
    public function handle(ComplianceManagementService $complianceService): void
    {
        try {
            Log::info('Starting compliance check', [
                'company_id' => $this->company->id,
                'company_name' => $this->company->name,
            ]);

            // إجراء فحص الامتثال الشامل
            $complianceResult = $complianceService->performComprehensiveComplianceCheck($this->company);

            // تحديث حالة الامتثال للشركة
            $this->company->updateComplianceStatus();

            // إنشاء تنبيهات إذا لزم الأمر
            if ($complianceResult['violations_count'] > 0) {
                $this->createComplianceAlerts($complianceResult);
            }

            // إنشاء أنشطة امتثال تلقائية
            if ($this->options['create_activities'] ?? true) {
                $complianceService->createAutomaticComplianceActivities($this->company);
            }

            Log::info('Compliance check completed successfully', [
                'company_id' => $this->company->id,
                'overall_score' => $complianceResult['overall_score'],
                'violations_count' => $complianceResult['violations_count'],
            ]);

        } catch (\Exception $e) {
            Log::error('Compliance check failed', [
                'company_id' => $this->company->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * إنشاء تنبيهات الامتثال
     */
    protected function createComplianceAlerts(array $complianceResult): void
    {
        foreach ($complianceResult['violations'] as $violation) {
            // منطق إنشاء التنبيهات
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Compliance check job failed permanently', [
            'company_id' => $this->company->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
