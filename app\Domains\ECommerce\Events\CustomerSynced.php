<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceCustomer;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث مزامنة العميل
 * يتم إطلاقه عند مزامنة عميل مع المنصة
 */
class CustomerSynced
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceCustomer $customer;
    public string $action;
    public ECommerceIntegration $integration;
    public array $syncData;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceCustomer $customer,
        string $action,
        ECommerceIntegration $integration,
        array $syncData = []
    ) {
        $this->customer = $customer;
        $this->action = $action;
        $this->integration = $integration;
        $this->syncData = $syncData;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'customer_id' => $this->customer->id,
            'external_id' => $this->customer->external_id,
            'customer_email' => $this->customer->email,
            'customer_name' => $this->customer->display_name,
            'customer_phone' => $this->customer->phone,
            'total_spent' => $this->customer->total_spent,
            'orders_count' => $this->customer->total_orders,
            'action' => $this->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'synced_at' => $this->customer->last_synced_at,
            'sync_data' => $this->syncData,
        ];
    }

    /**
     * تحديد ما إذا كان العميل جديد
     */
    public function isNewCustomer(): bool
    {
        return $this->action === 'created';
    }

    /**
     * تحديد ما إذا كان العميل محدث
     */
    public function isUpdatedCustomer(): bool
    {
        return $this->action === 'updated';
    }

    /**
     * تحديد ما إذا كان العميل محذوف
     */
    public function isDeletedCustomer(): bool
    {
        return $this->action === 'deleted';
    }

    /**
     * تحديد ما إذا كان العميل VIP
     */
    public function isVipCustomer(): bool
    {
        return $this->customer->customer_tier === 'vip' ||
               $this->customer->total_spent > 10000; // مثال على معيار VIP
    }

    /**
     * تحديد ما إذا كان العميل نشط
     */
    public function isActiveCustomer(): bool
    {
        return $this->customer->account_status === 'active' &&
               $this->customer->last_order_date &&
               $this->customer->last_order_date->diffInDays(now()) <= 90;
    }
}
