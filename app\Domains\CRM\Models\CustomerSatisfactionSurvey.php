<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج استبيان رضا العملاء - Customer Satisfaction Survey
 * قياس رضا العملاء وجمع التعليقات
 */
class CustomerSatisfactionSurvey extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'customer_id',
        'opportunity_id',
        'interaction_id',
        'survey_type',
        'trigger_event',
        'status',
        'sent_at',
        'opened_at',
        'completed_at',
        'expires_at',
        'rating',
        'nps_score',
        'csat_score',
        'ces_score',
        'feedback',
        'questions',
        'responses',
        'follow_up_required',
        'follow_up_completed',
        'survey_token',
        'language',
        'channel',
        'metadata',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'opened_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'rating' => 'integer',
        'nps_score' => 'integer',
        'csat_score' => 'integer',
        'ces_score' => 'integer',
        'questions' => 'array',
        'responses' => 'array',
        'follow_up_required' => 'boolean',
        'follow_up_completed' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * أنواع الاستبيانات
     */
    const SURVEY_TYPES = [
        'nps' => 'Net Promoter Score',
        'csat' => 'Customer Satisfaction',
        'ces' => 'Customer Effort Score',
        'general' => 'استبيان عام',
        'post_purchase' => 'بعد الشراء',
        'post_support' => 'بعد الدعم',
        'post_interaction' => 'بعد التفاعل',
        'periodic' => 'دوري',
        'exit' => 'عند المغادرة',
    ];

    /**
     * أحداث الإطلاق
     */
    const TRIGGER_EVENTS = [
        'order_completed' => 'اكتمال الطلب',
        'ticket_resolved' => 'حل التذكرة',
        'interaction_completed' => 'اكتمال التفاعل',
        'project_completed' => 'اكتمال المشروع',
        'contract_signed' => 'توقيع العقد',
        'subscription_renewal' => 'تجديد الاشتراك',
        'manual_trigger' => 'إطلاق يدوي',
        'scheduled' => 'مجدول',
        'milestone_reached' => 'وصول لمعلم',
    ];

    /**
     * حالات الاستبيان
     */
    const STATUSES = [
        'draft' => 'مسودة',
        'sent' => 'مرسل',
        'opened' => 'مفتوح',
        'completed' => 'مكتمل',
        'expired' => 'منتهي الصلاحية',
        'cancelled' => 'ملغي',
    ];

    /**
     * قنوات الإرسال
     */
    const CHANNELS = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسالة نصية',
        'whatsapp' => 'واتساب',
        'in_app' => 'داخل التطبيق',
        'web' => 'موقع إلكتروني',
        'phone' => 'هاتف',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع الفرصة التجارية
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * العلاقة مع التفاعل
     */
    public function interaction(): BelongsTo
    {
        return $this->belongsTo(CustomerInteraction::class, 'interaction_id');
    }

    /**
     * الحصول على تسمية نوع الاستبيان
     */
    public function getSurveyTypeLabelAttribute(): string
    {
        return self::SURVEY_TYPES[$this->survey_type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية حدث الإطلاق
     */
    public function getTriggerEventLabelAttribute(): string
    {
        return self::TRIGGER_EVENTS[$this->trigger_event] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية القناة
     */
    public function getChannelLabelAttribute(): string
    {
        return self::CHANNELS[$this->channel] ?? 'غير محدد';
    }

    /**
     * التحقق من كون الاستبيان مكتمل
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من انتهاء صلاحية الاستبيان
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at);
    }

    /**
     * التحقق من فتح الاستبيان
     */
    public function getIsOpenedAttribute(): bool
    {
        return !is_null($this->opened_at);
    }

    /**
     * الحصول على معدل الاستجابة
     */
    public function getResponseRateAttribute(): float
    {
        if (!$this->is_opened) {
            return 0;
        }

        $totalQuestions = count($this->questions ?? []);
        $answeredQuestions = count(array_filter($this->responses ?? []));

        return $totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100, 1) : 0;
    }

    /**
     * الحصول على تصنيف NPS
     */
    public function getNpsSegmentAttribute(): ?string
    {
        if (is_null($this->nps_score)) {
            return null;
        }

        if ($this->nps_score >= 9) {
            return 'promoter';
        } elseif ($this->nps_score >= 7) {
            return 'passive';
        } else {
            return 'detractor';
        }
    }

    /**
     * الحصول على تسمية تصنيف NPS
     */
    public function getNpsSegmentLabelAttribute(): ?string
    {
        return match ($this->nps_segment) {
            'promoter' => 'مروج',
            'passive' => 'محايد',
            'detractor' => 'منتقد',
            default => null,
        };
    }

    /**
     * الحصول على لون تصنيف NPS
     */
    public function getNpsSegmentColorAttribute(): string
    {
        return match ($this->nps_segment) {
            'promoter' => '#28a745',
            'passive' => '#ffc107',
            'detractor' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على تصنيف CSAT
     */
    public function getCsatSegmentAttribute(): ?string
    {
        if (is_null($this->csat_score)) {
            return null;
        }

        if ($this->csat_score >= 4) {
            return 'satisfied';
        } elseif ($this->csat_score >= 3) {
            return 'neutral';
        } else {
            return 'dissatisfied';
        }
    }

    /**
     * الحصول على تسمية تصنيف CSAT
     */
    public function getCsatSegmentLabelAttribute(): ?string
    {
        return match ($this->csat_segment) {
            'satisfied' => 'راضي',
            'neutral' => 'محايد',
            'dissatisfied' => 'غير راضي',
            default => null,
        };
    }

    /**
     * الحصول على الرابط العام للاستبيان
     */
    public function getPublicUrlAttribute(): string
    {
        return route('surveys.show', ['token' => $this->survey_token]);
    }

    /**
     * الحصول على وقت انتهاء الصلاحية المتبقي
     */
    public function getTimeToExpiryAttribute(): ?string
    {
        if (!$this->expires_at || $this->is_expired) {
            return null;
        }

        $diff = now()->diffInHours($this->expires_at, false);

        if ($diff < 24) {
            return "{$diff} ساعة";
        } else {
            $days = intval($diff / 24);
            return "{$days} يوم";
        }
    }

    /**
     * تحديد حالة الاستبيان كمفتوح
     */
    public function markAsOpened(): bool
    {
        if ($this->status === 'sent') {
            return $this->update([
                'status' => 'opened',
                'opened_at' => now(),
            ]);
        }

        return false;
    }

    /**
     * إكمال الاستبيان
     */
    public function complete(array $responses): bool
    {
        $updated = $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'responses' => $responses,
            'rating' => $responses['rating'] ?? null,
            'nps_score' => $responses['nps_score'] ?? null,
            'csat_score' => $responses['csat_score'] ?? null,
            'ces_score' => $responses['ces_score'] ?? null,
            'feedback' => $responses['feedback'] ?? null,
        ]);

        if ($updated) {
            // تحديث نقاط رضا العميل
            $this->updateCustomerSatisfactionScore();
            
            // تحديد ما إذا كانت هناك حاجة لمتابعة
            $this->checkFollowUpRequired();
        }

        return $updated;
    }

    /**
     * تحديث نقاط رضا العميل
     */
    protected function updateCustomerSatisfactionScore(): void
    {
        $customer = $this->customer;
        
        // حساب متوسط الرضا من جميع الاستبيانات
        $avgSatisfaction = $customer->satisfactionSurveys()
                                  ->completed()
                                  ->whereNotNull('rating')
                                  ->avg('rating');

        // حساب متوسط NPS
        $avgNPS = $customer->satisfactionSurveys()
                          ->completed()
                          ->whereNotNull('nps_score')
                          ->avg('nps_score');

        $customer->update([
            'satisfaction_score' => $avgSatisfaction,
            'nps_score' => $avgNPS,
        ]);
    }

    /**
     * تحديد الحاجة للمتابعة
     */
    protected function checkFollowUpRequired(): void
    {
        $needsFollowUp = false;

        // متابعة للتقييمات المنخفضة
        if ($this->rating && $this->rating <= 2) {
            $needsFollowUp = true;
        }

        // متابعة لـ NPS المنخفض
        if ($this->nps_score && $this->nps_score <= 6) {
            $needsFollowUp = true;
        }

        // متابعة للتعليقات السلبية
        if ($this->feedback && $this->containsNegativeKeywords($this->feedback)) {
            $needsFollowUp = true;
        }

        if ($needsFollowUp) {
            $this->update(['follow_up_required' => true]);
            
            // إنشاء مهمة متابعة
            $this->createFollowUpTask();
        }
    }

    /**
     * التحقق من وجود كلمات سلبية
     */
    protected function containsNegativeKeywords(string $text): bool
    {
        $negativeKeywords = [
            'سيء', 'فظيع', 'مشكلة', 'خطأ', 'بطيء', 'صعب',
            'bad', 'terrible', 'problem', 'error', 'slow', 'difficult',
            'mauvais', 'terrible', 'problème', 'erreur', 'lent', 'difficile'
        ];

        $text = strtolower($text);
        
        foreach ($negativeKeywords as $keyword) {
            if (str_contains($text, strtolower($keyword))) {
                return true;
            }
        }

        return false;
    }

    /**
     * إنشاء مهمة متابعة
     */
    protected function createFollowUpTask(): void
    {
        CustomerTask::create([
            'customer_id' => $this->customer_id,
            'assigned_to' => $this->customer->assigned_to,
            'title' => 'متابعة استبيان رضا منخفض',
            'description' => "متابعة العميل {$this->customer->full_name} بخصوص تقييم منخفض في الاستبيان",
            'type' => 'follow_up',
            'priority' => 'high',
            'due_date' => now()->addDays(1),
            'status' => 'pending',
            'metadata' => [
                'survey_id' => $this->id,
                'rating' => $this->rating,
                'nps_score' => $this->nps_score,
                'feedback' => $this->feedback,
            ],
        ]);
    }

    /**
     * إرسال الاستبيان
     */
    public function send(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $sent = $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        if ($sent) {
            // إرسال الاستبيان عبر القناة المحددة
            $this->sendViaChannel();
        }

        return $sent;
    }

    /**
     * إرسال عبر القناة المحددة
     */
    protected function sendViaChannel(): void
    {
        switch ($this->channel) {
            case 'email':
                $this->sendViaEmail();
                break;
            case 'sms':
                $this->sendViaSMS();
                break;
            case 'whatsapp':
                $this->sendViaWhatsApp();
                break;
            // باقي القنوات...
        }
    }

    /**
     * إرسال عبر البريد الإلكتروني
     */
    protected function sendViaEmail(): void
    {
        // تنفيذ إرسال البريد الإلكتروني
        // سيتم تطوير هذا في خدمة منفصلة
    }

    /**
     * إرسال عبر SMS
     */
    protected function sendViaSMS(): void
    {
        // تنفيذ إرسال SMS
    }

    /**
     * إرسال عبر واتساب
     */
    protected function sendViaWhatsApp(): void
    {
        // تنفيذ إرسال واتساب
    }

    /**
     * فلترة الاستبيانات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * فلترة الاستبيانات المرسلة
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * فلترة الاستبيانات المنتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * فلترة حسب نوع الاستبيان
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('survey_type', $type);
    }

    /**
     * فلترة الاستبيانات التي تحتاج متابعة
     */
    public function scopeNeedsFollowUp($query)
    {
        return $query->where('follow_up_required', true)
                    ->where('follow_up_completed', false);
    }

    /**
     * فلترة التقييمات المنخفضة
     */
    public function scopeLowRating($query, int $threshold = 3)
    {
        return $query->where('rating', '<=', $threshold);
    }

    /**
     * فلترة المنتقدين في NPS
     */
    public function scopeDetractors($query)
    {
        return $query->where('nps_score', '<=', 6);
    }

    /**
     * فلترة المروجين في NPS
     */
    public function scopePromoters($query)
    {
        return $query->where('nps_score', '>=', 9);
    }

    /**
     * فلترة حسب فترة زمنية
     */
    public function scopeBetweenDates($query, \DateTime $from, \DateTime $to)
    {
        return $query->whereBetween('completed_at', [$from, $to]);
    }
}
