<template>
  <component
    :is="as"
    :href="href"
    :method="method"
    class="block w-full px-4 py-2 text-left text-sm leading-5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-800 transition duration-150 ease-in-out"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { Link } from '@inertiajs/vue3';

interface Props {
  href?: string;
  method?: string;
  as?: string;
}

withDefaults(defineProps<Props>(), {
  as: 'Link',
});
</script>
