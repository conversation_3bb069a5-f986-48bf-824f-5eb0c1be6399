<?php

namespace App\Domains\Compliance\Policies;

use App\Domains\Compliance\Models\GovernmentIntegration;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة التكاملات الحكومية
 */
class GovernmentIntegrationPolicy
{
    use HandlesAuthorization;

    /**
     * عرض جميع التكاملات
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('compliance.integrations.view');
    }

    /**
     * عرض تكامل محدد
     */
    public function view(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.view') &&
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * إنشاء تكامل جديد
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('compliance.integrations.create') &&
               $user->hasRole('super-admin'); // فقط المدير العام
    }

    /**
     * تحديث تكامل
     */
    public function update(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.update') &&
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * حذف تكامل
     */
    public function delete(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.delete') &&
               $user->hasRole('super-admin') && // فقط المدير العام
               !$integration->is_mandatory; // لا يمكن حذف التكاملات الإلزامية
    }

    /**
     * تفعيل/إلغاء تفعيل تكامل
     */
    public function toggleStatus(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.manage') &&
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * اختبار تكامل
     */
    public function test(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.test') &&
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * عرض سجلات API
     */
    public function viewApiLogs(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.logs') &&
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * تحديث إعدادات المصادقة
     */
    public function updateAuthentication(User $user, GovernmentIntegration $integration): bool
    {
        return $user->hasPermissionTo('compliance.integrations.auth') &&
               $user->hasRole('super-admin') && // فقط المدير العام
               $this->canAccessCountry($user, $integration->country->code);
    }

    /**
     * التحقق من إمكانية الوصول للدولة
     */
    protected function canAccessCountry(User $user, string $countryCode): bool
    {
        return $user->hasRole('super-admin') ||
               $user->hasPermissionTo("compliance.countries.{$countryCode}");
    }
}
