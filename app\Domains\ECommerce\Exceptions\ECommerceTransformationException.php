<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء تحويل البيانات للتجارة الإلكترونية
 */
class ECommerceTransformationException extends ECommerceException
{
    protected string $transformationType = '';
    protected string $sourceFormat = '';
    protected string $targetFormat = '';
    protected array $sourceData = [];
    protected array $transformationRules = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $transformationType = '',
        string $sourceFormat = '',
        string $targetFormat = '',
        array $sourceData = [],
        array $transformationRules = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->transformationType = $transformationType;
        $this->sourceFormat = $sourceFormat;
        $this->targetFormat = $targetFormat;
        $this->sourceData = $sourceData;
        $this->transformationRules = $transformationRules;
        $this->errorCode = 'TRANSFORMATION_ERROR';
    }

    /**
     * الحصول على نوع التحويل
     */
    public function getTransformationType(): string
    {
        return $this->transformationType;
    }

    /**
     * الحصول على تنسيق المصدر
     */
    public function getSourceFormat(): string
    {
        return $this->sourceFormat;
    }

    /**
     * الحصول على التنسيق المستهدف
     */
    public function getTargetFormat(): string
    {
        return $this->targetFormat;
    }

    /**
     * الحصول على بيانات المصدر
     */
    public function getSourceData(): array
    {
        return $this->sourceData;
    }

    /**
     * الحصول على قواعد التحويل
     */
    public function getTransformationRules(): array
    {
        return $this->transformationRules;
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتخطيط
     */
    public function isMappingError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'mapping') ||
               str_contains(strtolower($this->getMessage()), 'field');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتحقق
     */
    public function isValidationError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'validation') ||
               str_contains(strtolower($this->getMessage()), 'invalid');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتنسيق
     */
    public function isFormatError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'format') ||
               str_contains(strtolower($this->getMessage()), 'type');
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'transformation_type' => $this->getTransformationType(),
            'source_format' => $this->getSourceFormat(),
            'target_format' => $this->getTargetFormat(),
            'source_data_size' => strlen(json_encode($this->getSourceData())),
            'transformation_rules_count' => count($this->getTransformationRules()),
            'is_mapping_error' => $this->isMappingError(),
            'is_validation_error' => $this->isValidationError(),
            'is_format_error' => $this->isFormatError(),
        ]);
    }
}
