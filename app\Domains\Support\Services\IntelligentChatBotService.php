<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\KnowledgeBaseArticle;
use App\Domains\Support\Models\Ticket;
use App\Domains\Support\Models\TicketCategory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة البوت الذكي - Intelligent ChatBot Service
 * يدير البوت الذكي متعدد اللغات مع فهم اللغة الطبيعية
 */
class IntelligentChatBotService
{
    protected array $welcomeMessages = [
        'ar' => 'مرحباً! أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟',
        'en' => 'Hello! I\'m your intelligent assistant. How can I help you today?',
        'fr' => 'Bonjour! Je suis votre assistant intelligent. Comment puis-je vous aider aujourd\'hui?',
    ];

    protected array $commonQuestions = [
        'ar' => [
            'كيف أغير كلمة المرور' => 'password_reset',
            'أين فاتورتي' => 'find_invoice',
            'ما هي حالة طلبي' => 'order_status',
            'كيف أتواصل معكم' => 'contact_info',
            'ساعات العمل' => 'business_hours',
            'كيف أحذف حسابي' => 'delete_account',
            'مشكلة في الدفع' => 'payment_issue',
            'لا أستطيع تسجيل الدخول' => 'login_issue',
        ],
        'en' => [
            'how to change password' => 'password_reset',
            'where is my invoice' => 'find_invoice',
            'order status' => 'order_status',
            'contact information' => 'contact_info',
            'business hours' => 'business_hours',
            'delete account' => 'delete_account',
            'payment problem' => 'payment_issue',
            'cannot login' => 'login_issue',
        ],
        'fr' => [
            'changer mot de passe' => 'password_reset',
            'où est ma facture' => 'find_invoice',
            'statut commande' => 'order_status',
            'informations contact' => 'contact_info',
            'heures ouverture' => 'business_hours',
            'supprimer compte' => 'delete_account',
            'problème paiement' => 'payment_issue',
            'connexion impossible' => 'login_issue',
        ],
    ];

    protected array $responses = [
        'password_reset' => [
            'ar' => 'لتغيير كلمة المرور:\n1. اذهب إلى صفحة تسجيل الدخول\n2. اضغط على "نسيت كلمة المرور"\n3. أدخل بريدك الإلكتروني\n4. تحقق من بريدك للحصول على رابط إعادة التعيين',
            'en' => 'To change your password:\n1. Go to the login page\n2. Click "Forgot Password"\n3. Enter your email address\n4. Check your email for the reset link',
            'fr' => 'Pour changer votre mot de passe:\n1. Allez à la page de connexion\n2. Cliquez sur "Mot de passe oublié"\n3. Entrez votre adresse email\n4. Vérifiez votre email pour le lien de réinitialisation',
        ],
        'find_invoice' => [
            'ar' => 'يمكنك العثور على فاتورتك في:\n1. لوحة التحكم > الفواتير\n2. قسم "طلباتي"\n3. أو يمكنني البحث عنها لك إذا أعطيتني رقم الطلب',
            'en' => 'You can find your invoice in:\n1. Dashboard > Invoices\n2. "My Orders" section\n3. Or I can search for it if you give me the order number',
            'fr' => 'Vous pouvez trouver votre facture dans:\n1. Tableau de bord > Factures\n2. Section "Mes commandes"\n3. Ou je peux la chercher si vous me donnez le numéro de commande',
        ],
        'order_status' => [
            'ar' => 'لمعرفة حالة طلبك، أحتاج رقم الطلب. يمكنك العثور عليه في:\n1. بريد تأكيد الطلب\n2. لوحة التحكم > طلباتي\n3. أو أعطني رقم الطلب وسأتحقق لك',
            'en' => 'To check your order status, I need the order number. You can find it in:\n1. Order confirmation email\n2. Dashboard > My Orders\n3. Or give me the order number and I\'ll check for you',
            'fr' => 'Pour vérifier le statut de votre commande, j\'ai besoin du numéro de commande. Vous pouvez le trouver dans:\n1. Email de confirmation\n2. Tableau de bord > Mes commandes\n3. Ou donnez-moi le numéro et je vérifierai',
        ],
        'contact_info' => [
            'ar' => 'يمكنك التواصل معنا عبر:\n📧 البريد: <EMAIL>\n📞 الهاتف: +212-XXX-XXXX\n💬 الدردشة الحية (متاحة الآن)\n🕒 ساعات العمل: 9 صباحاً - 6 مساءً',
            'en' => 'You can contact us via:\n📧 Email: <EMAIL>\n📞 Phone: +212-XXX-XXXX\n💬 Live Chat (available now)\n🕒 Business hours: 9 AM - 6 PM',
            'fr' => 'Vous pouvez nous contacter via:\n📧 Email: <EMAIL>\n📞 Téléphone: +212-XXX-XXXX\n💬 Chat en direct (disponible maintenant)\n🕒 Heures d\'ouverture: 9h - 18h',
        ],
        'business_hours' => [
            'ar' => 'ساعات عملنا:\n🕘 الإثنين - الجمعة: 9:00 صباحاً - 6:00 مساءً\n🕘 السبت: 10:00 صباحاً - 4:00 مساءً\n🕘 الأحد: مغلق\n\nالدردشة الحية متاحة 24/7',
            'en' => 'Our business hours:\n🕘 Monday - Friday: 9:00 AM - 6:00 PM\n🕘 Saturday: 10:00 AM - 4:00 PM\n🕘 Sunday: Closed\n\nLive chat available 24/7',
            'fr' => 'Nos heures d\'ouverture:\n🕘 Lundi - Vendredi: 9h00 - 18h00\n🕘 Samedi: 10h00 - 16h00\n🕘 Dimanche: Fermé\n\nChat en direct disponible 24h/24',
        ],
    ];

    protected array $fallbackResponses = [
        'ar' => [
            'لم أفهم سؤالك تماماً. هل يمكنك إعادة صياغته؟',
            'أعتذر، لم أتمكن من فهم طلبك. هل تريد التحدث مع وكيل؟',
            'يبدو أن سؤالك معقد. دعني أبحث في قاعدة المعرفة...',
        ],
        'en' => [
            'I didn\'t quite understand your question. Could you rephrase it?',
            'Sorry, I couldn\'t understand your request. Would you like to speak with an agent?',
            'Your question seems complex. Let me search our knowledge base...',
        ],
        'fr' => [
            'Je n\'ai pas bien compris votre question. Pourriez-vous la reformuler?',
            'Désolé, je n\'ai pas pu comprendre votre demande. Voulez-vous parler à un agent?',
            'Votre question semble complexe. Laissez-moi chercher dans notre base de connaissances...',
        ],
    ];

    /**
     * توليد رد البوت الذكي
     */
    public function generateResponse(string $message, array $context = []): array
    {
        $language = $context['language'] ?? 'ar';
        $sentiment = $context['sentiment'] ?? 'neutral';
        
        // تنظيف وتحليل الرسالة
        $cleanMessage = $this->cleanMessage($message);
        $intent = $this->detectIntent($cleanMessage, $language);
        
        // البحث عن رد مباشر
        $directResponse = $this->findDirectResponse($intent, $language);
        if ($directResponse) {
            return [
                'message' => $directResponse,
                'confidence' => 0.9,
                'intent' => $intent,
                'suggested_actions' => $this->getSuggestedActions($intent),
                'related_articles' => $this->findRelatedArticles($intent),
            ];
        }

        // البحث في قاعدة المعرفة
        $knowledgeResponse = $this->searchKnowledgeBase($cleanMessage, $language);
        if ($knowledgeResponse['confidence'] > 0.7) {
            return [
                'message' => $knowledgeResponse['answer'],
                'confidence' => $knowledgeResponse['confidence'],
                'intent' => 'knowledge_base_search',
                'suggested_actions' => ['read_full_article'],
                'related_articles' => $knowledgeResponse['articles'],
            ];
        }

        // البحث في التذاكر المحلولة
        $ticketResponse = $this->searchResolvedTickets($cleanMessage, $language);
        if ($ticketResponse['confidence'] > 0.6) {
            return [
                'message' => $ticketResponse['solution'],
                'confidence' => $ticketResponse['confidence'],
                'intent' => 'similar_issue_found',
                'suggested_actions' => ['create_ticket_if_not_resolved'],
                'related_articles' => [],
            ];
        }

        // معالجة خاصة للمشاعر السلبية
        if ($sentiment === 'negative' || $sentiment === 'angry') {
            return $this->handleNegativeSentiment($cleanMessage, $language, $context);
        }

        // رد افتراضي مع اقتراحات
        return $this->generateFallbackResponse($cleanMessage, $language, $context);
    }

    /**
     * الحصول على رسالة الترحيب
     */
    public function getWelcomeMessage(string $language = 'ar'): string
    {
        return $this->welcomeMessages[$language] ?? $this->welcomeMessages['ar'];
    }

    /**
     * كشف النية من الرسالة
     */
    protected function detectIntent(string $message, string $language): ?string
    {
        $message = strtolower($message);
        $questions = $this->commonQuestions[$language] ?? [];

        foreach ($questions as $pattern => $intent) {
            if (str_contains($message, strtolower($pattern))) {
                return $intent;
            }
        }

        // كشف النوايا المعقدة
        return $this->detectComplexIntent($message, $language);
    }

    /**
     * البحث عن رد مباشر
     */
    protected function findDirectResponse(string $intent, string $language): ?string
    {
        if (!$intent || !isset($this->responses[$intent])) {
            return null;
        }

        return $this->responses[$intent][$language] ?? $this->responses[$intent]['ar'] ?? null;
    }

    /**
     * البحث في قاعدة المعرفة
     */
    protected function searchKnowledgeBase(string $query, string $language): array
    {
        $cacheKey = "kb_search_" . md5($query . $language);
        
        return Cache::remember($cacheKey, 1800, function () use ($query, $language) {
            $keywords = $this->extractKeywords($query);
            
            $articles = KnowledgeBaseArticle::published()
                ->where(function ($q) use ($keywords, $language) {
                    foreach ($keywords as $keyword) {
                        $q->orWhere("title_{$language}", 'LIKE', "%{$keyword}%")
                          ->orWhere("content_{$language}", 'LIKE', "%{$keyword}%")
                          ->orWhere('tags', 'LIKE', "%{$keyword}%");
                    }
                })
                ->orderBy('view_count', 'desc')
                ->limit(3)
                ->get();

            if ($articles->isEmpty()) {
                return ['confidence' => 0, 'answer' => '', 'articles' => []];
            }

            $bestArticle = $articles->first();
            $confidence = $this->calculateRelevanceScore($bestArticle, $keywords);

            return [
                'confidence' => $confidence,
                'answer' => $this->generateAnswerFromArticle($bestArticle, $language),
                'articles' => $articles->map(function ($article) {
                    return [
                        'id' => $article->id,
                        'title' => $article->localized_title,
                        'url' => $article->permalink,
                    ];
                })->toArray(),
            ];
        });
    }

    /**
     * البحث في التذاكر المحلولة
     */
    protected function searchResolvedTickets(string $query, string $language): array
    {
        $keywords = $this->extractKeywords($query);
        
        $resolvedTickets = Ticket::where('status', 'resolved')
            ->whereNotNull('resolution_summary')
            ->where(function ($q) use ($keywords) {
                foreach ($keywords as $keyword) {
                    $q->orWhere('subject', 'LIKE', "%{$keyword}%")
                      ->orWhere('description', 'LIKE', "%{$keyword}%")
                      ->orWhere('resolution_summary', 'LIKE', "%{$keyword}%");
                }
            })
            ->orderBy('resolved_at', 'desc')
            ->limit(5)
            ->get();

        if ($resolvedTickets->isEmpty()) {
            return ['confidence' => 0, 'solution' => ''];
        }

        $bestTicket = $resolvedTickets->first();
        $confidence = $this->calculateTicketRelevance($bestTicket, $keywords);

        return [
            'confidence' => $confidence,
            'solution' => $this->formatTicketSolution($bestTicket, $language),
        ];
    }

    /**
     * معالجة المشاعر السلبية
     */
    protected function handleNegativeSentiment(string $message, string $language, array $context): array
    {
        $empathyResponses = [
            'ar' => 'أفهم إحباطك وأعتذر عن أي إزعاج. دعني أساعدك في حل هذه المشكلة.',
            'en' => 'I understand your frustration and apologize for any inconvenience. Let me help you resolve this issue.',
            'fr' => 'Je comprends votre frustration et m\'excuse pour tout désagrément. Laissez-moi vous aider à résoudre ce problème.',
        ];

        $escalationSuggestions = [
            'ar' => 'هل تريد التحدث مع أحد وكلائنا المتخصصين؟',
            'en' => 'Would you like to speak with one of our specialized agents?',
            'fr' => 'Souhaitez-vous parler à l\'un de nos agents spécialisés?',
        ];

        return [
            'message' => $empathyResponses[$language] . "\n\n" . $escalationSuggestions[$language],
            'confidence' => 0.8,
            'intent' => 'negative_sentiment_detected',
            'suggested_actions' => ['transfer_to_agent', 'escalate_to_supervisor'],
            'related_articles' => [],
        ];
    }

    /**
     * توليد رد افتراضي
     */
    protected function generateFallbackResponse(string $message, string $language, array $context): array
    {
        $fallbacks = $this->fallbackResponses[$language] ?? $this->fallbackResponses['ar'];
        $randomFallback = $fallbacks[array_rand($fallbacks)];

        // إضافة اقتراحات مفيدة
        $suggestions = $this->generateHelpfulSuggestions($language);

        return [
            'message' => $randomFallback . "\n\n" . $suggestions,
            'confidence' => 0.3,
            'intent' => 'unknown',
            'suggested_actions' => ['transfer_to_agent', 'search_knowledge_base', 'browse_categories'],
            'related_articles' => $this->getPopularArticles($language),
        ];
    }

    /**
     * تنظيف الرسالة
     */
    protected function cleanMessage(string $message): string
    {
        // إزالة الرموز والأرقام غير المرغوبة
        $message = preg_replace('/[^\p{L}\p{N}\s\?!.]/u', ' ', $message);
        
        // إزالة المسافات الزائدة
        $message = preg_replace('/\s+/', ' ', $message);
        
        return trim($message);
    }

    /**
     * كشف النوايا المعقدة
     */
    protected function detectComplexIntent(string $message, string $language): ?string
    {
        // كشف نوايا أكثر تعقيداً باستخدام patterns
        $complexPatterns = [
            'ar' => [
                '/لا\s+(أستطيع|يمكنني|أقدر)/' => 'cannot_do_something',
                '/مشكلة\s+في/' => 'technical_issue',
                '/كيف\s+(أقوم|أعمل|أفعل)/' => 'how_to_question',
                '/متى\s+سيتم/' => 'timeline_question',
                '/لماذا\s+(لا|لم)/' => 'why_question',
            ],
            'en' => [
                '/cannot\s+/' => 'cannot_do_something',
                '/problem\s+with/' => 'technical_issue',
                '/how\s+to\s+/' => 'how_to_question',
                '/when\s+will/' => 'timeline_question',
                '/why\s+(not|don\'t)/' => 'why_question',
            ],
        ];

        $patterns = $complexPatterns[$language] ?? [];
        
        foreach ($patterns as $pattern => $intent) {
            if (preg_match($pattern, $message)) {
                return $intent;
            }
        }

        return null;
    }

    /**
     * استخراج الكلمات المفتاحية
     */
    protected function extractKeywords(string $text): array
    {
        // إزالة كلمات الوقف
        $stopWords = [
            'ar' => ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي'],
            'en' => ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'],
            'fr' => ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'dans', 'sur', 'avec'],
        ];

        $words = preg_split('/\s+/', strtolower($text));
        $language = $this->detectLanguage($text);
        $stopWordsForLang = $stopWords[$language] ?? [];

        return array_filter($words, function ($word) use ($stopWordsForLang) {
            return strlen($word) >= 3 && !in_array($word, $stopWordsForLang);
        });
    }

    /**
     * حساب نقاط الصلة
     */
    protected function calculateRelevanceScore($article, array $keywords): float
    {
        $score = 0;
        $content = strtolower($article->localized_title . ' ' . $article->localized_content);

        foreach ($keywords as $keyword) {
            if (str_contains($content, $keyword)) {
                $score += 0.2;
            }
        }

        // إضافة نقاط للشعبية
        $score += min($article->view_count / 1000, 0.3);
        $score += min($article->helpful_count / 100, 0.2);

        return min($score, 1.0);
    }

    /**
     * توليد إجابة من المقال
     */
    protected function generateAnswerFromArticle($article, string $language): string
    {
        $excerpt = $article->getLocalizedExcerptAttribute();
        
        $intro = [
            'ar' => 'وجدت هذه المعلومات التي قد تساعدك:',
            'en' => 'I found this information that might help you:',
            'fr' => 'J\'ai trouvé ces informations qui pourraient vous aider:',
        ];

        $readMore = [
            'ar' => 'اقرأ المقال كاملاً للمزيد من التفاصيل.',
            'en' => 'Read the full article for more details.',
            'fr' => 'Lisez l\'article complet pour plus de détails.',
        ];

        return $intro[$language] . "\n\n" . $excerpt . "\n\n" . $readMore[$language];
    }

    /**
     * حساب صلة التذكرة
     */
    protected function calculateTicketRelevance($ticket, array $keywords): float
    {
        $score = 0;
        $content = strtolower($ticket->subject . ' ' . $ticket->description . ' ' . $ticket->resolution_summary);

        foreach ($keywords as $keyword) {
            if (str_contains($content, $keyword)) {
                $score += 0.25;
            }
        }

        return min($score, 1.0);
    }

    /**
     * تنسيق حل التذكرة
     */
    protected function formatTicketSolution($ticket, string $language): string
    {
        $intro = [
            'ar' => 'وجدت حلاً لمشكلة مشابهة:',
            'en' => 'I found a solution for a similar issue:',
            'fr' => 'J\'ai trouvé une solution pour un problème similaire:',
        ];

        return $intro[$language] . "\n\n" . $ticket->resolution_summary;
    }

    /**
     * توليد اقتراحات مفيدة
     */
    protected function generateHelpfulSuggestions(string $language): string
    {
        $suggestions = [
            'ar' => "يمكنني مساعدتك في:\n• تغيير كلمة المرور\n• العثور على الفواتير\n• حالة الطلبات\n• معلومات الاتصال",
            'en' => "I can help you with:\n• Changing password\n• Finding invoices\n• Order status\n• Contact information",
            'fr' => "Je peux vous aider avec:\n• Changer le mot de passe\n• Trouver les factures\n• Statut des commandes\n• Informations de contact",
        ];

        return $suggestions[$language] ?? $suggestions['ar'];
    }

    /**
     * الحصول على المقالات الشائعة
     */
    protected function getPopularArticles(string $language): array
    {
        return KnowledgeBaseArticle::published()
            ->orderBy('view_count', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->localized_title,
                    'url' => $article->permalink,
                ];
            })
            ->toArray();
    }

    /**
     * الحصول على الإجراءات المقترحة
     */
    protected function getSuggestedActions(string $intent): array
    {
        $actions = [
            'password_reset' => ['reset_password', 'contact_support'],
            'find_invoice' => ['view_invoices', 'download_invoice'],
            'order_status' => ['track_order', 'contact_shipping'],
            'contact_info' => ['call_support', 'send_email'],
            'business_hours' => ['schedule_callback', 'send_message'],
        ];

        return $actions[$intent] ?? ['transfer_to_agent'];
    }

    /**
     * البحث عن مقالات ذات صلة
     */
    protected function findRelatedArticles(string $intent): array
    {
        $categoryMapping = [
            'password_reset' => 'account',
            'find_invoice' => 'billing',
            'order_status' => 'orders',
            'contact_info' => 'general',
        ];

        $categoryName = $categoryMapping[$intent] ?? null;
        if (!$categoryName) {
            return [];
        }

        return KnowledgeBaseArticle::published()
            ->whereHas('category', function ($q) use ($categoryName) {
                $q->where('name', 'LIKE', "%{$categoryName}%");
            })
            ->limit(3)
            ->get()
            ->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->localized_title,
                    'url' => $article->permalink,
                ];
            })
            ->toArray();
    }

    /**
     * كشف اللغة
     */
    protected function detectLanguage(string $text): string
    {
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
            return 'ar';
        } elseif (preg_match('/[àâäéèêëïîôöùûüÿç]/i', $text)) {
            return 'fr';
        } else {
            return 'en';
        }
    }
}
