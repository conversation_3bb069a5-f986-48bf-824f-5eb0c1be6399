<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج رد التذكرة - Ticket Reply
 * يدعم الردود متعددة الأنواع والذكاء الاصطناعي
 */
class TicketReply extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasFiles;

    protected $fillable = [
        'ticket_id',
        'parent_id',
        'user_id',
        'reply_type',
        'content',
        'content_html',
        'is_internal',
        'is_solution',
        'is_auto_generated',
        'channel',
        'source_reference',
        'language',
        'ai_generated',
        'ai_confidence',
        'ai_suggestions',
        'time_spent_minutes',
        'is_billable',
        'billable_rate',
        'status',
        'read_at',
        'delivered_at',
        'failed_at',
        'failure_reason',
        'metadata',
    ];

    protected $casts = [
        'is_internal' => 'boolean',
        'is_solution' => 'boolean',
        'is_auto_generated' => 'boolean',
        'ai_generated' => 'boolean',
        'ai_confidence' => 'decimal:2',
        'ai_suggestions' => 'array',
        'time_spent_minutes' => 'integer',
        'is_billable' => 'boolean',
        'billable_rate' => 'decimal:2',
        'read_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع التذكرة
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    /**
     * العلاقة مع الرد الأب (للردود المتداخلة)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * العلاقة مع الردود الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id')->orderBy('created_at');
    }

    /**
     * العلاقة مع تقييمات الرد
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(TicketReplyRating::class, 'reply_id');
    }

    /**
     * الحصول على نوع المرسل
     */
    public function getSenderTypeAttribute(): string
    {
        return match ($this->reply_type) {
            'agent' => 'وكيل',
            'customer' => 'عميل',
            'system' => 'النظام',
            'bot' => 'بوت',
            'admin' => 'مدير',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على اسم المرسل
     */
    public function getSenderNameAttribute(): string
    {
        if ($this->reply_type === 'system') {
            return 'النظام';
        }

        if ($this->reply_type === 'bot') {
            return 'مساعد ذكي';
        }

        return $this->user->name ?? 'غير محدد';
    }

    /**
     * الحصول على صورة المرسل
     */
    public function getSenderAvatarAttribute(): string
    {
        if (in_array($this->reply_type, ['system', 'bot'])) {
            return asset('images/system-avatar.png');
        }

        return $this->user->avatar_url ?? asset('images/default-avatar.png');
    }

    /**
     * التحقق من قراءة الرد
     */
    public function getIsReadAttribute(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * التحقق من تسليم الرد
     */
    public function getIsDeliveredAttribute(): bool
    {
        return !is_null($this->delivered_at);
    }

    /**
     * التحقق من فشل التسليم
     */
    public function getIsFailedAttribute(): bool
    {
        return !is_null($this->failed_at);
    }

    /**
     * الحصول على حالة التسليم
     */
    public function getDeliveryStatusAttribute(): string
    {
        if ($this->is_failed) {
            return 'فشل';
        }

        if ($this->is_delivered) {
            return 'تم التسليم';
        }

        if ($this->status === 'sending') {
            return 'جاري الإرسال';
        }

        return 'في الانتظار';
    }

    /**
     * الحصول على وقت القراءة المنسق
     */
    public function getFormattedReadTimeAttribute(): ?string
    {
        return $this->read_at?->diffForHumans();
    }

    /**
     * الحصول على المحتوى المنسق
     */
    public function getFormattedContentAttribute(): string
    {
        if ($this->content_html) {
            return $this->content_html;
        }

        // تحويل النص العادي إلى HTML
        return nl2br(e($this->content));
    }

    /**
     * الحصول على ملخص المحتوى
     */
    public function getContentSummaryAttribute(): string
    {
        $content = strip_tags($this->formatted_content);
        return \Str::limit($content, 100);
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRatingAttribute(): ?float
    {
        $ratings = $this->ratings()->avg('rating');
        return $ratings ? round($ratings, 1) : null;
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getRatingsCountAttribute(): int
    {
        return $this->ratings()->count();
    }

    /**
     * تحديد الرد كحل
     */
    public function markAsSolution(): bool
    {
        // إلغاء تحديد الحلول الأخرى في نفس التذكرة
        $this->ticket->replies()->where('id', '!=', $this->id)->update(['is_solution' => false]);

        // تحديد هذا الرد كحل
        $this->update(['is_solution' => true]);

        // تحديث حالة التذكرة
        $this->ticket->update(['status' => 'resolved', 'resolved_at' => now()]);

        return true;
    }

    /**
     * إلغاء تحديد الرد كحل
     */
    public function unmarkAsSolution(): bool
    {
        $this->update(['is_solution' => false]);

        // إعادة فتح التذكرة إذا لم يعد هناك حل
        if (!$this->ticket->replies()->where('is_solution', true)->exists()) {
            $this->ticket->update([
                'status' => 'open',
                'resolved_at' => null,
            ]);
        }

        return true;
    }

    /**
     * تقييم الرد
     */
    public function rate(int $userId, int $rating, string $feedback = null): TicketReplyRating
    {
        return $this->ratings()->updateOrCreate(
            ['user_id' => $userId],
            [
                'rating' => $rating,
                'feedback' => $feedback,
                'rated_at' => now(),
            ]
        );
    }

    /**
     * تحديد الرد كمقروء
     */
    public function markAsRead(int $userId = null): bool
    {
        if ($this->is_read) {
            return true;
        }

        return $this->update([
            'read_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'read_by' => $userId ?? auth()->id(),
            ]),
        ]);
    }

    /**
     * تحديد الرد كمُسلم
     */
    public function markAsDelivered(): bool
    {
        return $this->update([
            'delivered_at' => now(),
            'status' => 'delivered',
        ]);
    }

    /**
     * تحديد الرد كفاشل
     */
    public function markAsFailed(string $reason): bool
    {
        return $this->update([
            'failed_at' => now(),
            'failure_reason' => $reason,
            'status' => 'failed',
        ]);
    }

    /**
     * إنشاء رد فرعي
     */
    public function createChild(array $replyData): self
    {
        $replyData['parent_id'] = $this->id;
        $replyData['ticket_id'] = $this->ticket_id;

        return self::create($replyData);
    }

    /**
     * تحليل المحتوى بالذكاء الاصطناعي
     */
    public function analyzeWithAI(): array
    {
        // سيتم تنفيذها في الخدمة
        return [
            'sentiment' => 'neutral',
            'confidence' => 0.8,
            'suggestions' => [],
        ];
    }

    /**
     * فلترة الردود حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('reply_type', $type);
    }

    /**
     * فلترة الردود الداخلية
     */
    public function scopeInternal($query)
    {
        return $query->where('is_internal', true);
    }

    /**
     * فلترة الردود العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_internal', false);
    }

    /**
     * فلترة الحلول
     */
    public function scopeSolutions($query)
    {
        return $query->where('is_solution', true);
    }

    /**
     * فلترة الردود المولدة تلقائياً
     */
    public function scopeAutoGenerated($query)
    {
        return $query->where('is_auto_generated', true);
    }

    /**
     * فلترة الردود المولدة بالذكاء الاصطناعي
     */
    public function scopeAiGenerated($query)
    {
        return $query->where('ai_generated', true);
    }

    /**
     * فلترة الردود القابلة للفوترة
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * فلترة الردود غير المقروءة
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * فلترة الردود الفاشلة
     */
    public function scopeFailed($query)
    {
        return $query->whereNotNull('failed_at');
    }

    /**
     * ترتيب حسب التاريخ
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * ترتيب حسب التاريخ (الأقدم أولاً)
     */
    public function scopeOldest($query)
    {
        return $query->orderBy('created_at', 'asc');
    }
}
