<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Security Incident Model
 * 
 * Tracks security incidents and threats detected by the system
 */
class SecurityIncident extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_gateway_id',
        'api_endpoint_id',
        'api_key_id',
        'incident_type',
        'severity',
        'status',
        'title',
        'description',
        'source_ip',
        'user_agent',
        'request_data',
        'threat_indicators',
        'detection_method',
        'confidence_score',
        'false_positive',
        'mitigation_actions',
        'resolved_at',
        'resolved_by',
        'resolution_notes',
        'metadata',
    ];

    protected $casts = [
        'request_data' => 'array',
        'threat_indicators' => 'array',
        'mitigation_actions' => 'array',
        'metadata' => 'array',
        'resolved_at' => 'datetime',
        'confidence_score' => 'decimal:2',
        'false_positive' => 'boolean',
    ];

    /**
     * Relationship with API Gateway
     */
    public function apiGateway(): BelongsTo
    {
        return $this->belongsTo(ApiGateway::class);
    }

    /**
     * Relationship with API Endpoint
     */
    public function apiEndpoint(): BelongsTo
    {
        return $this->belongsTo(ApiEndpoint::class);
    }

    /**
     * Relationship with API Key
     */
    public function apiKey(): BelongsTo
    {
        return $this->belongsTo(ApiKey::class);
    }

    /**
     * Relationship with resolver
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'resolved_by');
    }

    /**
     * Check if incident is critical
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Check if incident is high severity
     */
    public function isHigh(): bool
    {
        return $this->severity === 'high';
    }

    /**
     * Check if incident is resolved
     */
    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    /**
     * Mark incident as resolved
     */
    public function markAsResolved(int $userId, string $notes = ''): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => $userId,
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Mark as false positive
     */
    public function markAsFalsePositive(int $userId, string $notes = ''): void
    {
        $this->update([
            'false_positive' => true,
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => $userId,
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Add mitigation action
     */
    public function addMitigationAction(string $action, array $details = []): void
    {
        $actions = $this->mitigation_actions ?? [];
        $actions[] = [
            'action' => $action,
            'details' => $details,
            'timestamp' => now()->toISOString(),
        ];
        
        $this->update(['mitigation_actions' => $actions]);
    }

    /**
     * Get risk score based on severity and confidence
     */
    public function getRiskScore(): float
    {
        $severityWeights = [
            'low' => 0.25,
            'medium' => 0.5,
            'high' => 0.75,
            'critical' => 1.0,
        ];
        
        $severityWeight = $severityWeights[$this->severity] ?? 0.5;
        $confidenceWeight = $this->confidence_score / 100;
        
        return $severityWeight * $confidenceWeight * 100;
    }
}
