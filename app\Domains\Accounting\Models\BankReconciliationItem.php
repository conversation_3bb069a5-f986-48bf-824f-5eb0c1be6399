<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج بند تسوية البنك
 */
class BankReconciliationItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'bank_reconciliation_id',
        'bank_transaction_id',
        'journal_entry_id',
        'type',
        'description',
        'amount',
        'status',
        'adjustment_type',
        'adjustment_reason',
        'notes',
        'created_by',
        'resolved_by',
        'resolved_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'resolved_at' => 'datetime',
    ];

    /**
     * أنواع البنود
     */
    public const TYPES = [
        'OUTSTANDING_DEPOSIT' => 'وديعة معلقة',
        'OUTSTANDING_CHECK' => 'شيك معلق',
        'BANK_CHARGE' => 'رسوم بنكية',
        'INTEREST_EARNED' => 'فوائد مكتسبة',
        'NSF_CHECK' => 'شيك مرتد',
        'BANK_ERROR' => 'خطأ بنكي',
        'BOOK_ERROR' => 'خطأ محاسبي',
        'ADJUSTMENT' => 'تعديل',
    ];

    /**
     * حالات البند
     */
    public const STATUSES = [
        'PENDING' => 'في الانتظار',
        'MATCHED' => 'مطابق',
        'ADJUSTED' => 'معدل',
        'RESOLVED' => 'محلول',
        'CANCELLED' => 'ملغي',
    ];

    /**
     * أنواع التعديل
     */
    public const ADJUSTMENT_TYPES = [
        'ADD_TO_BOOK' => 'إضافة للدفاتر',
        'SUBTRACT_FROM_BOOK' => 'طرح من الدفاتر',
        'ADD_TO_BANK' => 'إضافة للبنك',
        'SUBTRACT_FROM_BANK' => 'طرح من البنك',
    ];

    /**
     * التسوية
     */
    public function bankReconciliation(): BelongsTo
    {
        return $this->belongsTo(BankReconciliation::class);
    }

    /**
     * المعاملة البنكية
     */
    public function bankTransaction(): BelongsTo
    {
        return $this->belongsTo(BankTransaction::class);
    }

    /**
     * القيد المحاسبي
     */
    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    /**
     * من أنشأ البند
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * من حل البند
     */
    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'resolved_by');
    }

    /**
     * الحصول على اسم النوع
     */
    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على اسم نوع التعديل
     */
    public function getAdjustmentTypeNameAttribute(): string
    {
        return self::ADJUSTMENT_TYPES[$this->adjustment_type] ?? $this->adjustment_type;
    }

    /**
     * فحص إذا كان البند محلول
     */
    public function isResolved(): bool
    {
        return $this->status === 'RESOLVED';
    }

    /**
     * فحص إذا كان البند معلق
     */
    public function isPending(): bool
    {
        return $this->status === 'PENDING';
    }

    /**
     * فحص إذا كان البند يؤثر على الدفاتر
     */
    public function affectsBooks(): bool
    {
        return in_array($this->adjustment_type, ['ADD_TO_BOOK', 'SUBTRACT_FROM_BOOK']);
    }

    /**
     * فحص إذا كان البند يؤثر على البنك
     */
    public function affectsBank(): bool
    {
        return in_array($this->adjustment_type, ['ADD_TO_BANK', 'SUBTRACT_FROM_BANK']);
    }

    /**
     * حساب تأثير البند على الرصيد
     */
    public function getBalanceImpact(): float
    {
        switch ($this->adjustment_type) {
            case 'ADD_TO_BOOK':
            case 'ADD_TO_BANK':
                return $this->amount;
            case 'SUBTRACT_FROM_BOOK':
            case 'SUBTRACT_FROM_BANK':
                return -$this->amount;
            default:
                return 0;
        }
    }

    /**
     * مطابقة البند
     */
    public function match(): void
    {
        $this->update([
            'status' => 'MATCHED',
        ]);
    }

    /**
     * تعديل البند
     */
    public function adjust(string $adjustmentType, string $reason): void
    {
        $this->update([
            'status' => 'ADJUSTED',
            'adjustment_type' => $adjustmentType,
            'adjustment_reason' => $reason,
        ]);
    }

    /**
     * حل البند
     */
    public function resolve(int $resolvedBy, ?string $notes = null): void
    {
        $this->update([
            'status' => 'RESOLVED',
            'resolved_by' => $resolvedBy,
            'resolved_at' => now(),
            'notes' => $notes ? "{$this->notes}\n{$notes}" : $this->notes,
        ]);
    }

    /**
     * إلغاء البند
     */
    public function cancel(string $reason): void
    {
        $this->update([
            'status' => 'CANCELLED',
            'notes' => "{$this->notes}\nسبب الإلغاء: {$reason}",
        ]);
    }

    /**
     * Scope للبنود المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'PENDING');
    }

    /**
     * Scope للبنود المحلولة
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'RESOLVED');
    }

    /**
     * Scope للبنود حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope للبنود التي تؤثر على الدفاتر
     */
    public function scopeAffectingBooks($query)
    {
        return $query->whereIn('adjustment_type', ['ADD_TO_BOOK', 'SUBTRACT_FROM_BOOK']);
    }

    /**
     * Scope للبنود التي تؤثر على البنك
     */
    public function scopeAffectingBank($query)
    {
        return $query->whereIn('adjustment_type', ['ADD_TO_BANK', 'SUBTRACT_FROM_BANK']);
    }
}
