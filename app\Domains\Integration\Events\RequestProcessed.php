<?php

namespace App\Domains\Integration\Events;

use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\ApiEndpoint;
use App\Domains\Integration\Models\ApiKey;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

/**
 * Request Processed Event
 * 
 * Fired when an API request has been successfully processed
 * through the gateway system
 */
class RequestProcessed implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $requestId;
    public ApiGateway $gateway;
    public ?ApiEndpoint $endpoint;
    public ?ApiKey $apiKey;
    public array $requestData;
    public array $responseData;
    public float $processingTime;
    public string $status;
    public ?string $errorMessage;
    public array $metrics;
    public Carbon $processedAt;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $requestId,
        ApiGateway $gateway,
        ?ApiEndpoint $endpoint,
        ?ApiKey $apiKey,
        array $requestData,
        array $responseData,
        float $processingTime,
        string $status = 'success',
        ?string $errorMessage = null,
        array $metrics = []
    ) {
        $this->requestId = $requestId;
        $this->gateway = $gateway;
        $this->endpoint = $endpoint;
        $this->apiKey = $apiKey;
        $this->requestData = $requestData;
        $this->responseData = $responseData;
        $this->processingTime = $processingTime;
        $this->status = $status;
        $this->errorMessage = $errorMessage;
        $this->metrics = $metrics;
        $this->processedAt = now();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("gateway.{$this->gateway->gateway_id}"),
            new PrivateChannel("company.{$this->gateway->company_id}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'request_id' => $this->requestId,
            'gateway_id' => $this->gateway->gateway_id,
            'endpoint_id' => $this->endpoint?->endpoint_id,
            'api_key_id' => $this->apiKey?->id,
            'status' => $this->status,
            'processing_time' => $this->processingTime,
            'error_message' => $this->errorMessage,
            'metrics' => $this->metrics,
            'processed_at' => $this->processedAt->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'request.processed';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        // Only broadcast if real-time monitoring is enabled
        return $this->gateway->monitoring_enabled && 
               config('integration.monitoring.enable_real_time', true);
    }

    /**
     * Get event data for logging
     */
    public function getLogData(): array
    {
        return [
            'event' => 'request_processed',
            'request_id' => $this->requestId,
            'gateway_id' => $this->gateway->gateway_id,
            'endpoint_id' => $this->endpoint?->endpoint_id,
            'api_key_id' => $this->apiKey?->id,
            'status' => $this->status,
            'processing_time' => $this->processingTime,
            'request_size' => strlen(json_encode($this->requestData)),
            'response_size' => strlen(json_encode($this->responseData)),
            'error_message' => $this->errorMessage,
            'metrics' => $this->metrics,
            'processed_at' => $this->processedAt->toISOString(),
        ];
    }

    /**
     * Check if request was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'success';
    }

    /**
     * Check if request failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed' || $this->status === 'error';
    }

    /**
     * Get processing time in milliseconds
     */
    public function getProcessingTimeMs(): float
    {
        return $this->processingTime * 1000;
    }

    /**
     * Check if processing time exceeds threshold
     */
    public function isSlowRequest(float $thresholdMs = 1000): bool
    {
        return $this->getProcessingTimeMs() > $thresholdMs;
    }
}
