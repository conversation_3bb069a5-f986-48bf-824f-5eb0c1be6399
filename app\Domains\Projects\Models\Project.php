<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasComments;
use App\Domains\Shared\Traits\HasFiles;
use App\Domains\Shared\Traits\HasActivities;

/**
 * نموذج المشروع - Enterprise Grade
 * يدعم إدارة المشاريع المعقدة مع جميع الميزات المتقدمة
 */
class Project extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasComments, HasFiles, HasActivities;

    protected $fillable = [
        'name',
        'description',
        'code',
        'program_id',
        'parent_id',
        'client_id',
        'project_manager_id',
        'status',
        'priority',
        'start_date',
        'end_date',
        'planned_start_date',
        'planned_end_date',
        'budget',
        'actual_cost',
        'currency',
        'progress_percentage',
        'health_status',
        'risk_level',
        'methodology',
        'category',
        'tags',
        'is_billable',
        'hourly_rate',
        'fixed_price',
        'is_template',
        'template_id',
        'visibility',
        'settings',
        'custom_fields',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'planned_start_date' => 'date',
        'planned_end_date' => 'date',
        'budget' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'fixed_price' => 'decimal:2',
        'progress_percentage' => 'decimal:2',
        'is_billable' => 'boolean',
        'is_template' => 'boolean',
        'tags' => 'array',
        'settings' => 'array',
        'custom_fields' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع البرنامج
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * العلاقة مع المشروع الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * العلاقة مع المشاريع الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * العلاقة مع العميل
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\CRM\Models\Client::class);
    }

    /**
     * العلاقة مع مدير المشروع
     */
    public function projectManager(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'project_manager_id');
    }

    /**
     * العلاقة مع أعضاء الفريق
     */
    public function teamMembers(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Domains\HR\Models\Employee::class,
            'project_team_members',
            'project_id',
            'employee_id'
        )->withPivot([
            'role',
            'permissions',
            'hourly_rate',
            'allocation_percentage',
            'joined_at',
            'left_at',
            'is_active'
        ])->withTimestamps();
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * العلاقة مع المعالم
     */
    public function milestones(): HasMany
    {
        return $this->hasMany(Milestone::class);
    }

    /**
     * العلاقة مع تسجيلات الوقت
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * العلاقة مع المخاطر
     */
    public function risks(): HasMany
    {
        return $this->hasMany(ProjectRisk::class);
    }

    /**
     * العلاقة مع المشكلات
     */
    public function issues(): HasMany
    {
        return $this->hasMany(ProjectIssue::class);
    }

    /**
     * العلاقة مع المحادثات
     */
    public function chats(): HasMany
    {
        return $this->hasMany(ProjectChat::class);
    }

    /**
     * العلاقة مع الموافقات
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(ProjectApproval::class);
    }

    /**
     * العلاقة مع التقارير
     */
    public function reports(): HasMany
    {
        return $this->hasMany(ProjectReport::class);
    }

    /**
     * العلاقة مع مراكز التكلفة
     */
    public function costCenters(): HasMany
    {
        return $this->hasMany(CostCenter::class);
    }

    /**
     * الحصول على نسبة الإنجاز المحسوبة
     */
    public function getCalculatedProgressAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->tasks()->where('status', 'COMPLETED')->count();
        return ($completedTasks / $totalTasks) * 100;
    }

    /**
     * الحصول على الحالة الصحية للمشروع
     */
    public function getHealthStatusAttribute(): string
    {
        $progress = $this->calculated_progress;
        $daysRemaining = $this->end_date ? now()->diffInDays($this->end_date, false) : 0;
        $budgetVariance = $this->budget > 0 ? (($this->actual_cost - $this->budget) / $this->budget) * 100 : 0;

        // تحليل الحالة الصحية
        if ($budgetVariance > 20 || $daysRemaining < 0) {
            return 'CRITICAL';
        } elseif ($budgetVariance > 10 || ($daysRemaining < 7 && $progress < 80)) {
            return 'AT_RISK';
        } elseif ($progress >= 90 && $budgetVariance <= 5) {
            return 'EXCELLENT';
        } else {
            return 'ON_TRACK';
        }
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        return $this->end_date ? now()->diffInDays($this->end_date, false) : 0;
    }

    /**
     * الحصول على إجمالي الساعات المسجلة
     */
    public function getTotalHoursAttribute(): float
    {
        return $this->timeEntries()->sum('hours');
    }

    /**
     * الحصول على التكلفة المحسوبة من الساعات
     */
    public function getCalculatedCostAttribute(): float
    {
        return $this->timeEntries()
                    ->join('employees', 'time_entries.employee_id', '=', 'employees.id')
                    ->selectRaw('SUM(time_entries.hours * COALESCE(time_entries.hourly_rate, employees.hourly_rate, 0)) as total_cost')
                    ->value('total_cost') ?? 0;
    }

    /**
     * الحصول على انحراف الميزانية
     */
    public function getBudgetVarianceAttribute(): array
    {
        $variance = $this->actual_cost - $this->budget;
        $variancePercentage = $this->budget > 0 ? ($variance / $this->budget) * 100 : 0;

        return [
            'amount' => $variance,
            'percentage' => $variancePercentage,
            'status' => $variancePercentage > 10 ? 'OVER_BUDGET' : ($variancePercentage < -10 ? 'UNDER_BUDGET' : 'ON_BUDGET')
        ];
    }

    /**
     * الحصول على انحراف الجدولة
     */
    public function getScheduleVarianceAttribute(): array
    {
        if (!$this->planned_end_date || !$this->end_date) {
            return ['days' => 0, 'status' => 'ON_SCHEDULE'];
        }

        $varianceDays = $this->end_date->diffInDays($this->planned_end_date, false);
        
        return [
            'days' => $varianceDays,
            'status' => $varianceDays > 0 ? 'AHEAD_OF_SCHEDULE' : ($varianceDays < 0 ? 'BEHIND_SCHEDULE' : 'ON_SCHEDULE')
        ];
    }

    /**
     * إضافة عضو للفريق
     */
    public function addTeamMember(int $employeeId, array $memberData = []): void
    {
        $this->teamMembers()->attach($employeeId, array_merge([
            'role' => 'MEMBER',
            'permissions' => ['VIEW', 'COMMENT'],
            'allocation_percentage' => 100,
            'joined_at' => now(),
            'is_active' => true,
        ], $memberData));

        $this->logActivity('team_member_added', [
            'employee_id' => $employeeId,
            'role' => $memberData['role'] ?? 'MEMBER'
        ]);
    }

    /**
     * إزالة عضو من الفريق
     */
    public function removeTeamMember(int $employeeId): void
    {
        $this->teamMembers()->updateExistingPivot($employeeId, [
            'left_at' => now(),
            'is_active' => false,
        ]);

        $this->logActivity('team_member_removed', ['employee_id' => $employeeId]);
    }

    /**
     * تحديث نسبة الإنجاز
     */
    public function updateProgress(): void
    {
        $calculatedProgress = $this->calculated_progress;
        
        $this->update(['progress_percentage' => $calculatedProgress]);

        // إرسال إشعارات عند الوصول لمعالم مهمة
        if ($calculatedProgress >= 25 && $this->getOriginal('progress_percentage') < 25) {
            $this->sendProgressNotification('25% مكتمل');
        } elseif ($calculatedProgress >= 50 && $this->getOriginal('progress_percentage') < 50) {
            $this->sendProgressNotification('50% مكتمل');
        } elseif ($calculatedProgress >= 75 && $this->getOriginal('progress_percentage') < 75) {
            $this->sendProgressNotification('75% مكتمل');
        } elseif ($calculatedProgress >= 100 && $this->getOriginal('progress_percentage') < 100) {
            $this->sendProgressNotification('مكتمل 100%');
        }
    }

    /**
     * إرسال إشعار التقدم
     */
    protected function sendProgressNotification(string $milestone): void
    {
        // إرسال إشعارات لأعضاء الفريق
        foreach ($this->teamMembers as $member) {
            $member->notify(new \App\Notifications\ProjectProgressNotification($this, $milestone));
        }
    }

    /**
     * إنشاء مشروع من قالب
     */
    public static function createFromTemplate(int $templateId, array $projectData): self
    {
        $template = self::findOrFail($templateId);
        
        $project = self::create(array_merge($projectData, [
            'template_id' => $templateId,
            'settings' => $template->settings,
            'custom_fields' => $template->custom_fields,
        ]));

        // نسخ المهام من القالب
        foreach ($template->tasks as $templateTask) {
            $templateTask->duplicateToProject($project->id);
        }

        // نسخ المعالم
        foreach ($template->milestones as $templateMilestone) {
            $templateMilestone->duplicateToProject($project->id);
        }

        return $project;
    }

    /**
     * تحويل المشروع إلى قالب
     */
    public function convertToTemplate(string $templateName): self
    {
        return self::create([
            'name' => $templateName,
            'description' => $this->description,
            'is_template' => true,
            'settings' => $this->settings,
            'custom_fields' => $this->custom_fields,
            'methodology' => $this->methodology,
            'category' => $this->category,
        ]);
    }

    /**
     * حساب مؤشرات الأداء الرئيسية
     */
    public function getKPIs(): array
    {
        return [
            'schedule_performance_index' => $this->calculateSPI(),
            'cost_performance_index' => $this->calculateCPI(),
            'quality_index' => $this->calculateQualityIndex(),
            'team_productivity' => $this->calculateTeamProductivity(),
            'client_satisfaction' => $this->getClientSatisfactionScore(),
        ];
    }

    /**
     * حساب مؤشر أداء الجدولة (SPI)
     */
    protected function calculateSPI(): float
    {
        $plannedValue = $this->budget * ($this->progress_percentage / 100);
        $earnedValue = $this->budget * ($this->calculated_progress / 100);
        
        return $plannedValue > 0 ? $earnedValue / $plannedValue : 1;
    }

    /**
     * حساب مؤشر أداء التكلفة (CPI)
     */
    protected function calculateCPI(): float
    {
        $earnedValue = $this->budget * ($this->calculated_progress / 100);
        
        return $this->actual_cost > 0 ? $earnedValue / $this->actual_cost : 1;
    }

    /**
     * حساب مؤشر الجودة
     */
    protected function calculateQualityIndex(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) return 100;

        $qualityTasks = $this->tasks()
                            ->where('quality_score', '>=', 80)
                            ->count();

        return ($qualityTasks / $totalTasks) * 100;
    }

    /**
     * حساب إنتاجية الفريق
     */
    protected function calculateTeamProductivity(): float
    {
        $totalHours = $this->total_hours;
        $completedTasks = $this->tasks()->where('status', 'COMPLETED')->count();

        return $totalHours > 0 ? $completedTasks / $totalHours : 0;
    }

    /**
     * الحصول على نقاط رضا العميل
     */
    protected function getClientSatisfactionScore(): float
    {
        // يمكن ربطها بنظام التقييمات
        return $this->metadata['client_satisfaction_score'] ?? 0;
    }

    /**
     * تصدير المشروع
     */
    public function export(string $format = 'json'): array
    {
        $data = [
            'project' => $this->toArray(),
            'tasks' => $this->tasks()->with(['assignees', 'dependencies'])->get(),
            'milestones' => $this->milestones,
            'team_members' => $this->teamMembers,
            'time_entries' => $this->timeEntries,
            'files' => $this->files,
            'risks' => $this->risks,
            'issues' => $this->issues,
        ];

        return $data;
    }

    /**
     * البحث في المشاريع
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('code', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة المشاريع النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['PLANNING', 'IN_PROGRESS', 'ON_HOLD']);
    }

    /**
     * فلترة حسب مدير المشروع
     */
    public function scopeForManager($query, int $managerId)
    {
        return $query->where('project_manager_id', $managerId);
    }

    /**
     * فلترة حسب العضو
     */
    public function scopeForMember($query, int $employeeId)
    {
        return $query->whereHas('teamMembers', function ($q) use ($employeeId) {
            $q->where('employee_id', $employeeId)
              ->where('is_active', true);
        });
    }

    /**
     * فلترة حسب العميل
     */
    public function scopeForClient($query, int $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * فلترة المشاريع المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('end_date', '<', now())
                    ->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
    }

    /**
     * فلترة المشاريع القابلة للفوترة
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * ترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW')");
    }
}
