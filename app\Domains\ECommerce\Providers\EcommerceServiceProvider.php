<?php

namespace App\Domains\Ecommerce\Providers;

use Illuminate\Support\ServiceProvider;

class EcommerceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل التكوينات
        $this->mergeConfigFrom(
            config_path('ecommerce.php'),
            'ecommerce'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل الطرق
        $this->loadRoutesFrom(__DIR__ . '/../Routes/web.php');
        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');

        // تحميل المشاهدات
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'ecommerce');

        // تحميل الترجمات
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'ecommerce');

        // تحميل Migrations
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        // نشر الملفات
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/ecommerce'),
            ], 'ecommerce-views');

            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/ecommerce'),
            ], 'ecommerce-lang');
        }
    }
}
