<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasFiles;
use App\Domains\Shared\Traits\HasActivities;
use Laravel\Scout\Searchable;

/**
 * نموذج العميل - Customer 360° Profile
 * ملف عميل شامل مع جميع التفاعلات والبيانات
 */
class Customer extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasFiles, HasActivities, Searchable;

    protected $fillable = [
        'customer_number',
        'type',
        'company_name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'mobile',
        'whatsapp',
        'website',
        'industry_id',
        'company_size',
        'annual_revenue',
        'country_code',
        'state',
        'city',
        'address',
        'postal_code',
        'timezone',
        'language',
        'currency',
        'tax_number',
        'commercial_register',
        'status',
        'tier',
        'source',
        'assigned_to',
        'created_by',
        'tags',
        'custom_fields',
        'social_profiles',
        'communication_preferences',
        'marketing_consent',
        'gdpr_consent',
        'last_contact_at',
        'last_purchase_at',
        'total_spent',
        'total_orders',
        'average_order_value',
        'lifetime_value',
        'acquisition_cost',
        'satisfaction_score',
        'nps_score',
        'risk_score',
        'credit_limit',
        'payment_terms',
        'preferred_contact_method',
        'preferred_contact_time',
        'birthday',
        'anniversary',
        'notes',
        'ai_insights',
        'behavior_data',
        'segmentation_data',
        'metadata',
    ];

    protected $casts = [
        'tags' => 'array',
        'custom_fields' => 'array',
        'social_profiles' => 'array',
        'communication_preferences' => 'array',
        'marketing_consent' => 'boolean',
        'gdpr_consent' => 'boolean',
        'last_contact_at' => 'datetime',
        'last_purchase_at' => 'datetime',
        'total_spent' => 'decimal:2',
        'total_orders' => 'integer',
        'average_order_value' => 'decimal:2',
        'lifetime_value' => 'decimal:2',
        'acquisition_cost' => 'decimal:2',
        'satisfaction_score' => 'decimal:1',
        'nps_score' => 'integer',
        'risk_score' => 'integer',
        'credit_limit' => 'decimal:2',
        'birthday' => 'date',
        'anniversary' => 'date',
        'ai_insights' => 'array',
        'behavior_data' => 'array',
        'segmentation_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الصناعة/القطاع
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }

    /**
     * العلاقة مع مندوب المبيعات المسؤول
     */
    public function assignedSalesRep(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع منشئ العميل
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع جهات الاتصال
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * العلاقة مع الفرص التجارية
     */
    public function opportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class);
    }

    /**
     * العلاقة مع التفاعلات
     */
    public function interactions(): HasMany
    {
        return $this->hasMany(CustomerInteraction::class);
    }

    /**
     * العلاقة مع المواعيد
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(CustomerTask::class);
    }

    /**
     * العلاقة مع الحملات التسويقية
     */
    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(MarketingCampaign::class, 'campaign_recipients')
                    ->withPivot(['sent_at', 'opened_at', 'clicked_at', 'status'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Invoice::class);
    }

    /**
     * العلاقة مع المشاريع
     */
    public function projects(): HasMany
    {
        return $this->hasMany(\App\Domains\Projects\Models\Project::class);
    }

    /**
     * العلاقة مع تذاكر الدعم
     */
    public function supportTickets(): HasMany
    {
        return $this->hasMany(\App\Domains\Support\Models\Ticket::class);
    }

    /**
     * العلاقة مع طلبات التجارة الإلكترونية
     */
    public function ecommerceOrders(): HasMany
    {
        return $this->hasMany(\App\Domains\Ecommerce\Models\Order::class);
    }

    /**
     * العلاقة مع الشرائح
     */
    public function segments(): BelongsToMany
    {
        return $this->belongsToMany(CustomerSegment::class, 'customer_segment_members');
    }

    /**
     * العلاقة مع استبيانات الرضا
     */
    public function satisfactionSurveys(): HasMany
    {
        return $this->hasMany(CustomerSatisfactionSurvey::class);
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        if ($this->type === 'company') {
            return $this->company_name;
        }

        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * الحصول على العنوان الكامل
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
        ]);

        return implode(', ', $parts);
    }

    /**
     * الحصول على حالة العميل
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'prospect' => 'محتمل',
            'lead' => 'عميل محتمل',
            'customer' => 'عميل',
            'vip' => 'عميل مميز',
            'churned' => 'مفقود',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على مستوى العميل
     */
    public function getTierLabelAttribute(): string
    {
        return match ($this->tier) {
            'vip' => 'VIP',
            'premium' => 'مميز',
            'standard' => 'عادي',
            'basic' => 'أساسي',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون المستوى
     */
    public function getTierColorAttribute(): string
    {
        return match ($this->tier) {
            'vip' => '#ffd700',
            'premium' => '#c0392b',
            'standard' => '#3498db',
            'basic' => '#95a5a6',
            default => '#bdc3c7',
        };
    }

    /**
     * حساب العمر
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birthday ? $this->birthday->age : null;
    }

    /**
     * الحصول على آخر تفاعل
     */
    public function getLastInteractionAttribute(): ?CustomerInteraction
    {
        return $this->interactions()->latest()->first();
    }

    /**
     * الحصول على عدد الأيام منذ آخر تفاعل
     */
    public function getDaysSinceLastContactAttribute(): int
    {
        return $this->last_contact_at ? $this->last_contact_at->diffInDays(now()) : 0;
    }

    /**
     * التحقق من كون العميل نشط
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    /**
     * التحقق من كون العميل VIP
     */
    public function getIsVipAttribute(): bool
    {
        return $this->tier === 'vip';
    }

    /**
     * الحصول على متوسط الرضا
     */
    public function getAverageSatisfactionAttribute(): float
    {
        return $this->satisfactionSurveys()
                   ->whereNotNull('rating')
                   ->avg('rating') ?? 0;
    }

    /**
     * الحصول على عدد الفرص المفتوحة
     */
    public function getOpenOpportunitiesCountAttribute(): int
    {
        return $this->opportunities()
                   ->whereNotIn('stage', ['won', 'lost', 'closed'])
                   ->count();
    }

    /**
     * الحصول على قيمة الفرص المفتوحة
     */
    public function getOpenOpportunitiesValueAttribute(): float
    {
        return $this->opportunities()
                   ->whereNotIn('stage', ['won', 'lost', 'closed'])
                   ->sum('value');
    }

    /**
     * الحصول على عدد التذاكر المفتوحة
     */
    public function getOpenTicketsCountAttribute(): int
    {
        return $this->supportTickets()
                   ->whereNotIn('status', ['resolved', 'closed'])
                   ->count();
    }

    /**
     * الحصول على الرصيد المستحق
     */
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->invoices()
                   ->where('status', 'pending')
                   ->sum('total_amount');
    }

    /**
     * تحديث آخر تاريخ تواصل
     */
    public function updateLastContact(): void
    {
        $this->update(['last_contact_at' => now()]);
    }

    /**
     * تحديث إحصائيات الشراء
     */
    public function updatePurchaseStats(): void
    {
        $orders = $this->ecommerceOrders()->where('status', 'completed');
        
        $this->update([
            'total_orders' => $orders->count(),
            'total_spent' => $orders->sum('total_amount'),
            'average_order_value' => $orders->avg('total_amount') ?? 0,
            'last_purchase_at' => $orders->latest()->first()?->created_at,
        ]);

        // حساب القيمة الدائمة للعميل
        $this->calculateLifetimeValue();
    }

    /**
     * حساب القيمة الدائمة للعميل
     */
    public function calculateLifetimeValue(): void
    {
        // صيغة مبسطة: متوسط قيمة الطلب × عدد الطلبات × معدل الاحتفاظ المتوقع
        $avgOrderValue = $this->average_order_value;
        $orderFrequency = $this->calculateOrderFrequency();
        $retentionRate = $this->calculateRetentionRate();
        
        $clv = $avgOrderValue * $orderFrequency * $retentionRate * 12; // سنوي
        
        $this->update(['lifetime_value' => $clv]);
    }

    /**
     * حساب تكرار الطلبات
     */
    protected function calculateOrderFrequency(): float
    {
        if ($this->total_orders <= 1) {
            return 1;
        }

        $firstOrder = $this->ecommerceOrders()->oldest()->first();
        $lastOrder = $this->ecommerceOrders()->latest()->first();
        
        if (!$firstOrder || !$lastOrder) {
            return 1;
        }

        $monthsBetween = $firstOrder->created_at->diffInMonths($lastOrder->created_at);
        
        return $monthsBetween > 0 ? $this->total_orders / $monthsBetween : 1;
    }

    /**
     * حساب معدل الاحتفاظ
     */
    protected function calculateRetentionRate(): float
    {
        // منطق حساب معدل الاحتفاظ بناءً على سلوك العميل
        $daysSinceLastPurchase = $this->last_purchase_at ? 
            $this->last_purchase_at->diffInDays(now()) : 365;

        if ($daysSinceLastPurchase <= 30) return 0.9;
        if ($daysSinceLastPurchase <= 90) return 0.7;
        if ($daysSinceLastPurchase <= 180) return 0.5;
        if ($daysSinceLastPurchase <= 365) return 0.3;
        
        return 0.1;
    }

    /**
     * إضافة تفاعل جديد
     */
    public function addInteraction(array $interactionData): CustomerInteraction
    {
        $interactionData['customer_id'] = $this->id;
        $interaction = CustomerInteraction::create($interactionData);
        
        $this->updateLastContact();
        
        return $interaction;
    }

    /**
     * إضافة مهمة جديدة
     */
    public function addTask(array $taskData): CustomerTask
    {
        $taskData['customer_id'] = $this->id;
        return CustomerTask::create($taskData);
    }

    /**
     * إضافة موعد جديد
     */
    public function addAppointment(array $appointmentData): Appointment
    {
        $appointmentData['customer_id'] = $this->id;
        return Appointment::create($appointmentData);
    }

    /**
     * تحديث النقاط والمستوى
     */
    public function updateTier(): void
    {
        $tier = 'basic';
        
        if ($this->total_spent >= 100000) {
            $tier = 'vip';
        } elseif ($this->total_spent >= 50000) {
            $tier = 'premium';
        } elseif ($this->total_spent >= 10000) {
            $tier = 'standard';
        }

        $this->update(['tier' => $tier]);
    }

    /**
     * فلترة العملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * فلترة العملاء VIP
     */
    public function scopeVip($query)
    {
        return $query->where('tier', 'vip');
    }

    /**
     * فلترة حسب مندوب المبيعات
     */
    public function scopeAssignedTo($query, int $salesRepId)
    {
        return $query->where('assigned_to', $salesRepId);
    }

    /**
     * فلترة حسب الصناعة
     */
    public function scopeInIndustry($query, int $industryId)
    {
        return $query->where('industry_id', $industryId);
    }

    /**
     * فلترة حسب الدولة
     */
    public function scopeInCountry($query, string $countryCode)
    {
        return $query->where('country_code', $countryCode);
    }

    /**
     * فلترة العملاء الذين لم يتم التواصل معهم مؤخراً
     */
    public function scopeNotContactedSince($query, int $days)
    {
        return $query->where('last_contact_at', '<', now()->subDays($days))
                    ->orWhereNull('last_contact_at');
    }

    /**
     * البحث في العملاء
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'LIKE', "%{$search}%")
              ->orWhere('last_name', 'LIKE', "%{$search}%")
              ->orWhere('company_name', 'LIKE', "%{$search}%")
              ->orWhere('email', 'LIKE', "%{$search}%")
              ->orWhere('phone', 'LIKE', "%{$search}%")
              ->orWhere('customer_number', 'LIKE', "%{$search}%");
        });
    }

    /**
     * إعدادات البحث للـ Scout
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'customer_number' => $this->customer_number,
            'full_name' => $this->full_name,
            'company_name' => $this->company_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'industry' => $this->industry->name ?? '',
            'status' => $this->status,
            'tier' => $this->tier,
        ];
    }
}
