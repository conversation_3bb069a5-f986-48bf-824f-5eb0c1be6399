<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث اتصال التكامل
 * يتم إطلاقه عند نجاح اتصال التكامل مع المنصة
 */
class IntegrationConnected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public array $connectionData;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(ECommerceIntegration $integration, array $connectionData = [])
    {
        $this->integration = $integration;
        $this->connectionData = $connectionData;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'connected_at' => now(),
            'connection_data' => $this->connectionData,
        ];
    }
}
