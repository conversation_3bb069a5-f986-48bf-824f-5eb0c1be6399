<?php

namespace App\Domains\Projects\Policies;

use App\Models\User;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة المهام
 * تحديد الأذونات والصلاحيات للمهام
 */
class TaskPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي مهمة
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyPermission([
            'view-tasks',
            'manage-tasks',
            'admin-tasks'
        ]) || $user->hasRole(['admin', 'project-manager', 'team-lead']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المهمة
     */
    public function view(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم رؤية جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'manage-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // التحقق من إمكانية الوصول للمشروع أولاً
        if (!$user->can('view', $task->project)) {
            return false;
        }

        // المكلف بالمهمة يمكنه رؤيتها
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه رؤيتها
        if ($task->reporter_id === $user->id) {
            return true;
        }

        // المراجع للمهمة يمكنه رؤيتها
        if ($task->reviewer_id === $user->id) {
            return true;
        }

        // مدير المشروع يمكنه رؤية جميع مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // أعضاء فريق المشروع يمكنهم رؤية المهام
        if ($task->project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // المتابعون للمهمة يمكنهم رؤيتها
        if ($task->watchers()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // المهام العامة في المشاريع العامة
        if ($task->project->visibility === 'PUBLIC' && 
            $user->hasPermission('view-tasks')) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء مهمة
     */
    public function create(User $user): bool
    {
        return $user->hasAnyPermission([
            'create-tasks',
            'manage-tasks',
            'admin-tasks'
        ]) || $user->hasRole(['admin', 'project-manager', 'team-lead']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء مهمة في مشروع محدد
     */
    public function createInProject(User $user, Project $project): bool
    {
        // التحقق من الصلاحية العامة لإنشاء المهام
        if (!$this->create($user)) {
            return false;
        }

        // التحقق من إمكانية إدارة مهام المشروع
        if ($user->can('manageTasks', $project)) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث المهمة
     */
    public function update(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم تحديث جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'manage-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // التحقق من حالة المهمة
        if (!$this->canModifyBasedOnStatus($user, $task)) {
            return false;
        }

        // المكلف بالمهمة يمكنه تحديثها
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // مدير المشروع يمكنه تحديث جميع مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه تحديثها
        if ($task->reporter_id === $user->id) {
            return true;
        }

        // أعضاء فريق المشروع مع صلاحيات التحديث
        $teamMember = $task->project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('update_tasks', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف المهمة
     */
    public function delete(User $user, Task $task): bool
    {
        // فقط المدراء والمشرفون يمكنهم حذف المهام
        if ($user->hasAnyPermission(['admin-tasks', 'delete-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه حذف مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه حذفها إذا لم تبدأ بعد
        if ($task->reporter_id === $user->id && 
            $task->status === 'TODO' &&
            $task->timeEntries()->count() === 0) {
            return true;
        }

        // لا يمكن حذف المهام التي لها مهام فرعية
        if ($task->children()->count() > 0) {
            return false;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استعادة المهمة
     */
    public function restore(User $user, Task $task): bool
    {
        return $user->hasAnyPermission(['admin-tasks', 'restore-tasks']) || 
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف المهمة نهائياً
     */
    public function forceDelete(User $user, Task $task): bool
    {
        return $user->hasPermission('force-delete-tasks') || 
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعيين المهمة
     */
    public function assign(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم تعيين جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'assign-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه تعيين مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه تعيينها
        if ($task->reporter_id === $user->id) {
            return true;
        }

        // أعضاء فريق المشروع مع صلاحيات التعيين
        $teamMember = $task->project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array('assign_tasks', $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تغيير حالة المهمة
     */
    public function changeStatus(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم تغيير حالة جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'change-task-status']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // المكلف بالمهمة يمكنه تغيير حالتها
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // مدير المشروع يمكنه تغيير حالة مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المراجع يمكنه تغيير الحالة للمراجعة والاعتماد
        if ($task->reviewer_id === $user->id && 
            in_array($task->status, ['IN_REVIEW', 'TESTING'])) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تتبع الوقت للمهمة
     */
    public function trackTime(User $user, Task $task): bool
    {
        // التحقق من تفعيل تتبع الوقت في المشروع
        if (!$task->project->enable_time_tracking) {
            return false;
        }

        // المدراء والمشرفون يمكنهم تتبع الوقت لجميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'track-time']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // المكلف بالمهمة يمكنه تتبع الوقت
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // أعضاء فريق المشروع يمكنهم تتبع الوقت
        if ($task->project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إضافة تعليقات
     */
    public function comment(User $user, Task $task): bool
    {
        // التحقق من إمكانية عرض المهمة أولاً
        if (!$this->view($user, $task)) {
            return false;
        }

        // المدراء والمشرفون يمكنهم التعليق على جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'comment-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // المكلف والمبلغ والمراجع يمكنهم التعليق
        if (in_array($user->id, [$task->assignee_id, $task->reporter_id, $task->reviewer_id])) {
            return true;
        }

        // أعضاء فريق المشروع يمكنهم التعليق
        if ($task->project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // المتابعون يمكنهم التعليق
        if ($task->watchers()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إضافة مرفقات
     */
    public function attachFiles(User $user, Task $task): bool
    {
        // التحقق من تفعيل المرفقات في المشروع
        if (!$task->project->enable_documents) {
            return false;
        }

        // المدراء والمشرفون يمكنهم إضافة مرفقات لجميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'attach-files']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // المكلف والمبلغ يمكنهم إضافة مرفقات
        if (in_array($user->id, [$task->assignee_id, $task->reporter_id])) {
            return true;
        }

        // أعضاء فريق المشروع يمكنهم إضافة مرفقات
        if ($task->project->team()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة التبعيات
     */
    public function manageDependencies(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع التبعيات
        if ($user->hasAnyPermission(['admin-tasks', 'manage-dependencies']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة تبعيات مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه إدارة تبعياتها
        if ($task->reporter_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة المتابعين
     */
    public function manageWatchers(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم إدارة جميع المتابعين
        if ($user->hasAnyPermission(['admin-tasks', 'manage-watchers']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه إدارة متابعي مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المكلف والمبلغ يمكنهم إدارة المتابعين
        if (in_array($user->id, [$task->assignee_id, $task->reporter_id])) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه نسخ المهمة
     */
    public function duplicate(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم نسخ جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'duplicate-tasks']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه نسخ مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المبلغ عن المهمة يمكنه نسخها
        if ($task->reporter_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تفاصيل الوقت
     */
    public function viewTimeDetails(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم رؤية تفاصيل الوقت لجميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'view-time-details']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // مدير المشروع يمكنه رؤية تفاصيل الوقت لمهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        // المكلف يمكنه رؤية تفاصيل وقته
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // العميل يمكنه رؤية تفاصيل الوقت القابل للفوترة
        if ($task->project->client_id && 
            $user->client_id === $task->project->client_id) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من إمكانية التعديل بناءً على الحالة
     */
    protected function canModifyBasedOnStatus(User $user, Task $task): bool
    {
        // المهام المكتملة أو الملغاة لا يمكن تعديلها إلا للمدراء
        if (in_array($task->status, ['DONE', 'CANCELLED'])) {
            return $user->hasAnyPermission(['admin-tasks', 'modify-completed-tasks']) || 
                   $user->hasRole(['admin', 'super-admin']);
        }

        return true;
    }

    /**
     * التحقق من إمكانية الوصول للمهمة بناءً على الحالة
     */
    public function canAccessBasedOnStatus(User $user, Task $task): bool
    {
        // المهام الملغاة لا يمكن الوصول إليها إلا للمدراء
        if ($task->status === 'CANCELLED') {
            return $user->hasAnyPermission(['admin-tasks', 'view-cancelled-tasks']) || 
                   $user->hasRole(['admin', 'super-admin']);
        }

        return true;
    }

    /**
     * التحقق من صلاحيات خاصة للمهمة
     */
    public function hasSpecialPermission(User $user, Task $task, string $permission): bool
    {
        // التحقق من الصلاحيات العامة
        if ($user->hasPermission($permission)) {
            return true;
        }

        // التحقق من صلاحيات عضو فريق المشروع
        $teamMember = $task->project->team()->where('user_id', $user->id)->first();
        if ($teamMember && 
            (in_array($permission, $teamMember->permissions ?? []) || 
             in_array('all', $teamMember->permissions ?? []))) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من إمكانية تحديث حقل محدد
     */
    public function canUpdateField(User $user, Task $task, string $field): bool
    {
        // الحقول الحساسة التي تحتاج صلاحيات خاصة
        $sensitiveFields = [
            'assignee_id' => 'assign',
            'status' => 'changeStatus',
            'priority' => 'update',
            'due_date' => 'update',
            'estimated_hours' => 'update',
            'story_points' => 'update',
        ];

        if (isset($sensitiveFields[$field])) {
            $method = $sensitiveFields[$field];
            return $this->$method($user, $task);
        }

        // الحقول العادية تحتاج صلاحية التحديث العامة
        return $this->update($user, $task);
    }

    /**
     * التحقق من إمكانية تحديث التقدم
     */
    public function updateProgress(User $user, Task $task): bool
    {
        // المدراء والمشرفون يمكنهم تحديث تقدم جميع المهام
        if ($user->hasAnyPermission(['admin-tasks', 'update-progress']) || 
            $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // المكلف بالمهمة يمكنه تحديث تقدمها
        if ($task->assignee_id === $user->id) {
            return true;
        }

        // مدير المشروع يمكنه تحديث تقدم مهام مشروعه
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        return false;
    }
}
