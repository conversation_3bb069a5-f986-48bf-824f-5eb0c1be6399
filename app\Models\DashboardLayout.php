<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DashboardLayout extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'layout_data',
        'is_active',
        'is_default',
        'is_shared',
        'shared_with_roles',
        'created_by',
        'metadata'
    ];

    protected $casts = [
        'layout_data' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_shared' => 'boolean',
        'shared_with_roles' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع منشئ التخطيط
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * جلب التخطيط النشط للمستخدم
     */
    public static function getActiveLayoutForUser($userId)
    {
        return static::where('user_id', $userId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * جلب التخطيطات المشتركة للدور
     */
    public static function getSharedLayoutsForRole($role)
    {
        return static::where('is_shared', true)
            ->whereJsonContains('shared_with_roles', $role)
            ->get();
    }

    /**
     * تفعيل تخطيط معين
     */
    public function activate()
    {
        // إلغاء تفعيل جميع التخطيطات الأخرى للمستخدم
        static::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_active' => false]);

        // تفعيل هذا التخطيط
        $this->update(['is_active' => true]);
    }

    /**
     * نسخ التخطيط لمستخدم آخر
     */
    public function copyToUser($userId, $name = null)
    {
        return static::create([
            'user_id' => $userId,
            'name' => $name ?? $this->name . ' (نسخة)',
            'layout_data' => $this->layout_data,
            'is_active' => false,
            'is_default' => false,
            'is_shared' => false,
            'created_by' => $this->user_id,
            'metadata' => array_merge($this->metadata ?? [], [
                'copied_from' => $this->id,
                'copied_at' => now()
            ])
        ]);
    }
}
