<?php

namespace App\Domains\Integration\Events;

use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\ApiEndpoint;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

/**
 * Performance Threshold Exceeded Event
 * 
 * Fired when performance metrics exceed defined thresholds
 */
class PerformanceThresholdExceeded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $requestId;
    public string $metric;
    public float $value;
    public float $threshold;
    public string $severity;
    public ?ApiGateway $gateway;
    public ?ApiEndpoint $endpoint;
    public array $context;
    public Carbon $occurredAt;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $requestId,
        string $metric,
        float $value,
        float $threshold,
        string $severity = 'warning',
        ?ApiGateway $gateway = null,
        ?ApiEndpoint $endpoint = null,
        array $context = []
    ) {
        $this->requestId = $requestId;
        $this->metric = $metric;
        $this->value = $value;
        $this->threshold = $threshold;
        $this->severity = $severity;
        $this->gateway = $gateway;
        $this->endpoint = $endpoint;
        $this->context = $context;
        $this->occurredAt = now();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('performance.alerts'),
        ];

        if ($this->gateway) {
            $channels[] = new PrivateChannel("gateway.{$this->gateway->gateway_id}.alerts");
        }

        if ($this->endpoint) {
            $channels[] = new PrivateChannel("endpoint.{$this->endpoint->endpoint_id}.alerts");
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'request_id' => $this->requestId,
            'metric' => $this->metric,
            'value' => $this->value,
            'threshold' => $this->threshold,
            'severity' => $this->severity,
            'gateway_id' => $this->gateway?->gateway_id,
            'endpoint_id' => $this->endpoint?->endpoint_id,
            'context' => $this->context,
            'occurred_at' => $this->occurredAt->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'performance.threshold.exceeded';
    }

    /**
     * Get alert message
     */
    public function getAlertMessage(): string
    {
        $entityName = $this->gateway ? $this->gateway->name : ($this->endpoint ? $this->endpoint->name : 'System');
        $percentage = round((($this->value - $this->threshold) / $this->threshold) * 100, 1);
        
        return "Performance alert: {$this->metric} for {$entityName} exceeded threshold by {$percentage}% ({$this->value} > {$this->threshold})";
    }

    /**
     * Check if this is a critical alert
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Get alert priority
     */
    public function getPriority(): int
    {
        return match ($this->severity) {
            'critical' => 1,
            'high' => 2,
            'medium' => 3,
            'warning' => 4,
            'low' => 5,
            default => 3,
        };
    }
}
