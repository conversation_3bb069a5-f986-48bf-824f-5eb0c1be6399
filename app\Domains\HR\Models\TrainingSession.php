<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج جلسة التدريب
 */
class TrainingSession extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'training_program_id',
        'session_number',
        'title',
        'description',
        'session_date',
        'start_time',
        'end_time',
        'location',
        'instructor_id',
        'max_participants',
        'status',
        'notes',
        'materials',
        'recording_url',
    ];

    protected $casts = [
        'session_date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'materials' => 'array',
        'session_number' => 'integer',
        'max_participants' => 'integer',
    ];

    /**
     * حالات الجلسة
     */
    public const STATUSES = [
        'SCHEDULED' => 'مجدولة',
        'IN_PROGRESS' => 'جارية',
        'COMPLETED' => 'مكتملة',
        'CANCELLED' => 'ملغاة',
        'POSTPONED' => 'مؤجلة',
    ];

    /**
     * البرنامج التدريبي
     */
    public function trainingProgram(): BelongsTo
    {
        return $this->belongsTo(TrainingProgram::class);
    }

    /**
     * المدرب
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'instructor_id');
    }

    /**
     * حضور الجلسة
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(TrainingAttendance::class);
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على مدة الجلسة بالدقائق
     */
    public function getDurationInMinutesAttribute(): int
    {
        if (!$this->start_time || !$this->end_time) {
            return 0;
        }
        
        return $this->start_time->diffInMinutes($this->end_time);
    }

    /**
     * فحص إذا كانت الجلسة مكتملة
     */
    public function isCompleted(): bool
    {
        return $this->status === 'COMPLETED';
    }

    /**
     * فحص إذا كانت الجلسة ملغاة
     */
    public function isCancelled(): bool
    {
        return $this->status === 'CANCELLED';
    }

    /**
     * الحصول على معدل الحضور
     */
    public function getAttendanceRateAttribute(): float
    {
        $totalEnrolled = $this->trainingProgram->enrollments()->count();
        $attended = $this->attendances()->where('attended', true)->count();
        
        return $totalEnrolled > 0 ? ($attended / $totalEnrolled) * 100 : 0;
    }

    /**
     * Scope للجلسات المجدولة
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'SCHEDULED');
    }

    /**
     * Scope للجلسات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'COMPLETED');
    }

    /**
     * Scope للجلسات في فترة معينة
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('session_date', [$startDate, $endDate]);
    }
}
