<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\Ticket;
use App\Domains\Support\Models\Department;
use App\Domains\HR\Models\Employee;
use Illuminate\Support\Facades\Cache;

/**
 * خدمة التوجيه الذكي للتذاكر - Smart Ticket Routing Service
 */
class SmartTicketRoutingService
{
    /**
     * تعيين تذكرة لأفضل وكيل متاح
     */
    public function assignTicket(Ticket $ticket): ?Employee
    {
        // تحديد القسم المناسب
        $department = $this->determineDepartment($ticket);
        
        if (!$department) {
            return null;
        }

        // العثور على أفضل وكيل
        $agent = $this->findBestAgent($ticket, $department);
        
        return $agent;
    }

    /**
     * تحديد القسم المناسب للتذكرة
     */
    protected function determineDepartment(Ticket $ticket): ?Department
    {
        // بناءً على الفئة
        if ($ticket->category && $ticket->category->department) {
            return $ticket->category->department;
        }

        // بناءً على التصنيف الذكي
        if ($ticket->ai_classification) {
            $classification = $ticket->ai_classification;
            
            if (isset($classification['department_suggestion'])) {
                return Department::find($classification['department_suggestion']);
            }
        }

        // بناءً على الكلمات المفتاحية
        return $this->classifyByKeywords($ticket);
    }

    /**
     * العثور على أفضل وكيل للتذكرة
     */
    protected function findBestAgent(Ticket $ticket, Department $department): ?Employee
    {
        $agents = $department->agents()
            ->where('is_active', true)
            ->where('is_available_for_tickets', true)
            ->get();

        if ($agents->isEmpty()) {
            return null;
        }

        // تطبيق معايير التوجيه
        $scoredAgents = $agents->map(function ($agent) use ($ticket) {
            return [
                'agent' => $agent,
                'score' => $this->calculateAgentScore($agent, $ticket),
            ];
        })->sortByDesc('score');

        return $scoredAgents->first()['agent'] ?? null;
    }

    /**
     * حساب نقاط الوكيل للتذكرة
     */
    protected function calculateAgentScore(Employee $agent, Ticket $ticket): float
    {
        $score = 0;

        // عبء العمل الحالي (وزن 40%)
        $currentLoad = $this->getAgentCurrentLoad($agent);
        $maxLoad = $agent->max_concurrent_tickets ?? 10;
        $loadScore = max(0, (1 - ($currentLoad / $maxLoad)) * 40);
        $score += $loadScore;

        // المهارات واللغة (وزن 30%)
        $skillScore = $this->calculateSkillMatch($agent, $ticket) * 30;
        $score += $skillScore;

        // الأداء التاريخي (وزن 20%)
        $performanceScore = $this->getAgentPerformanceScore($agent) * 20;
        $score += $performanceScore;

        // التوفر والوقت (وزن 10%)
        $availabilityScore = $this->calculateAvailabilityScore($agent) * 10;
        $score += $availabilityScore;

        return $score;
    }

    /**
     * تصنيف بناءً على الكلمات المفتاحية
     */
    protected function classifyByKeywords(Ticket $ticket): ?Department
    {
        $content = strtolower($ticket->subject . ' ' . $ticket->description);
        
        $departmentKeywords = [
            'technical' => ['خطأ', 'مشكلة تقنية', 'لا يعمل', 'error', 'bug', 'technical'],
            'billing' => ['فاتورة', 'دفع', 'رسوم', 'billing', 'payment', 'invoice'],
            'sales' => ['شراء', 'منتج', 'سعر', 'buy', 'product', 'price'],
            'hr' => ['موظف', 'راتب', 'إجازة', 'employee', 'salary', 'leave'],
        ];

        foreach ($departmentKeywords as $type => $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains($content, $keyword)) {
                    return Department::where('specializations', 'LIKE', "%{$type}%")->first();
                }
            }
        }

        return Department::where('is_default', true)->first();
    }

    /**
     * حساب عبء العمل الحالي للوكيل
     */
    protected function getAgentCurrentLoad(Employee $agent): int
    {
        return Cache::remember("agent_load_{$agent->id}", 300, function () use ($agent) {
            return Ticket::where('assigned_to', $agent->id)
                         ->whereIn('status', ['open', 'assigned', 'in_progress'])
                         ->count();
        });
    }

    /**
     * حساب تطابق المهارات
     */
    protected function calculateSkillMatch(Employee $agent, Ticket $ticket): float
    {
        $score = 0;

        // تطابق اللغة
        if ($ticket->language && in_array($ticket->language, $agent->languages ?? [])) {
            $score += 0.5;
        }

        // تطابق التخصص
        if ($ticket->category) {
            $categoryType = $ticket->category->type ?? '';
            if (in_array($categoryType, $agent->specializations ?? [])) {
                $score += 0.3;
            }
        }

        // خبرة في نوع المشكلة
        $similarTicketsResolved = Ticket::where('assigned_to', $agent->id)
            ->where('category_id', $ticket->category_id)
            ->where('status', 'resolved')
            ->count();

        if ($similarTicketsResolved > 0) {
            $score += min(0.2, $similarTicketsResolved * 0.02);
        }

        return min(1.0, $score);
    }

    /**
     * حساب نقاط الأداء التاريخي
     */
    protected function getAgentPerformanceScore(Employee $agent): float
    {
        return Cache::remember("agent_performance_{$agent->id}", 3600, function () use ($agent) {
            $recentTickets = Ticket::where('assigned_to', $agent->id)
                                  ->where('resolved_at', '>=', now()->subDays(30))
                                  ->get();

            if ($recentTickets->isEmpty()) {
                return 0.5; // نقاط متوسطة للوكلاء الجدد
            }

            $metrics = [
                'avg_resolution_time' => $this->calculateAvgResolutionTime($recentTickets),
                'customer_satisfaction' => $this->calculateAvgSatisfaction($recentTickets),
                'first_response_time' => $this->calculateAvgFirstResponseTime($recentTickets),
                'sla_compliance' => $this->calculateSLACompliance($recentTickets),
            ];

            // تحويل المقاييس لنقاط (0-1)
            $score = 0;
            $score += $this->normalizeResolutionTime($metrics['avg_resolution_time']) * 0.3;
            $score += ($metrics['customer_satisfaction'] / 5) * 0.3;
            $score += $this->normalizeResponseTime($metrics['first_response_time']) * 0.2;
            $score += $metrics['sla_compliance'] * 0.2;

            return min(1.0, $score);
        });
    }

    /**
     * حساب نقاط التوفر
     */
    protected function calculateAvailabilityScore(Employee $agent): float
    {
        $now = now();
        $workingHours = $agent->working_hours ?? [];
        
        // التحقق من ساعات العمل
        $dayOfWeek = strtolower($now->format('l'));
        if (!isset($workingHours[$dayOfWeek])) {
            return 0; // خارج ساعات العمل
        }

        $todayHours = $workingHours[$dayOfWeek];
        if (!$todayHours) {
            return 0; // يوم عطلة
        }

        $currentTime = $now->format('H:i');
        [$startTime, $endTime] = $todayHours;

        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            return 1.0; // داخل ساعات العمل
        }

        return 0.2; // خارج ساعات العمل لكن متاح للطوارئ
    }

    // دوال مساعدة للحسابات
    protected function calculateAvgResolutionTime($tickets): float
    {
        $totalMinutes = $tickets->sum(function ($ticket) {
            return $ticket->created_at->diffInMinutes($ticket->resolved_at);
        });

        return $tickets->count() > 0 ? $totalMinutes / $tickets->count() : 0;
    }

    protected function calculateAvgSatisfaction($tickets): float
    {
        $ratings = $tickets->whereNotNull('satisfaction_rating');
        return $ratings->count() > 0 ? $ratings->avg('satisfaction_rating') : 3;
    }

    protected function calculateAvgFirstResponseTime($tickets): float
    {
        $responseTimes = $tickets->whereNotNull('first_response_at');
        
        $totalMinutes = $responseTimes->sum(function ($ticket) {
            return $ticket->created_at->diffInMinutes($ticket->first_response_at);
        });

        return $responseTimes->count() > 0 ? $totalMinutes / $responseTimes->count() : 0;
    }

    protected function calculateSLACompliance($tickets): float
    {
        $slaCompliant = $tickets->filter(function ($ticket) {
            return !$ticket->is_sla_breached;
        });

        return $tickets->count() > 0 ? $slaCompliant->count() / $tickets->count() : 1.0;
    }

    protected function normalizeResolutionTime(float $minutes): float
    {
        // تحويل وقت الحل لنقاط (كلما قل الوقت، زادت النقاط)
        $maxMinutes = 1440; // 24 ساعة
        return max(0, 1 - ($minutes / $maxMinutes));
    }

    protected function normalizeResponseTime(float $minutes): float
    {
        // تحويل وقت الاستجابة لنقاط
        $maxMinutes = 240; // 4 ساعات
        return max(0, 1 - ($minutes / $maxMinutes));
    }
}
