<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Health Check Model
 * 
 * Stores health check results for API gateways and endpoints
 */
class HealthCheck extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_gateway_id',
        'api_endpoint_id',
        'check_type',
        'status',
        'response_time_ms',
        'status_code',
        'response_body',
        'error_message',
        'check_config',
        'metrics',
        'alerts_triggered',
        'checked_at',
        'next_check_at',
    ];

    protected $casts = [
        'check_config' => 'array',
        'metrics' => 'array',
        'alerts_triggered' => 'array',
        'checked_at' => 'datetime',
        'next_check_at' => 'datetime',
        'response_time_ms' => 'integer',
        'status_code' => 'integer',
    ];

    /**
     * Relationship with API Gateway
     */
    public function apiGateway(): BelongsTo
    {
        return $this->belongsTo(ApiGateway::class);
    }

    /**
     * Relationship with API Endpoint
     */
    public function apiEndpoint(): BelongsTo
    {
        return $this->belongsTo(ApiEndpoint::class);
    }

    /**
     * Check if health check is healthy
     */
    public function isHealthy(): bool
    {
        return $this->status === 'healthy';
    }

    /**
     * Check if health check is degraded
     */
    public function isDegraded(): bool
    {
        return $this->status === 'degraded';
    }

    /**
     * Check if health check is unhealthy
     */
    public function isUnhealthy(): bool
    {
        return $this->status === 'unhealthy';
    }

    /**
     * Get health score (0-100)
     */
    public function getHealthScore(): int
    {
        return match ($this->status) {
            'healthy' => 100,
            'degraded' => 50,
            'unhealthy' => 0,
            default => 0,
        };
    }

    /**
     * Schedule next health check
     */
    public function scheduleNext(int $intervalSeconds = 300): void
    {
        $this->update([
            'next_check_at' => now()->addSeconds($intervalSeconds),
        ]);
    }
}
