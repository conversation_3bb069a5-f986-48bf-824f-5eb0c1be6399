<?php

namespace App\Domains\Projects\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\Projects\Models\Project;

/**
 * طلب تحديث المشروع
 * تحقق شامل مع مراعاة البيانات الموجودة
 */
class UpdateProjectRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('update-projects');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        $projectId = $this->route('project') ?? $this->route('id');

        return [
            // Basic Information
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|nullable|string|max:2000',
            'code' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('projects', 'code')->ignore($projectId),
                'regex:/^[A-Z0-9_-]+$/'
            ],
            'client_id' => 'sometimes|nullable|exists:clients,id',
            'project_manager_id' => 'sometimes|exists:users,id',
            'parent_id' => 'sometimes|nullable|exists:projects,id',

            // Status and Priority
            'status' => [
                'sometimes',
                Rule::in(array_keys(Project::STATUSES))
            ],
            'priority' => [
                'sometimes',
                Rule::in(array_keys(Project::PRIORITIES))
            ],
            'category' => [
                'sometimes',
                Rule::in(array_keys(Project::CATEGORIES))
            ],
            'methodology' => [
                'sometimes',
                Rule::in(array_keys(Project::METHODOLOGIES))
            ],

            // Dates
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after:start_date',
            'planned_start_date' => 'sometimes|nullable|date',
            'planned_end_date' => 'sometimes|nullable|date',
            'actual_start_date' => 'sometimes|nullable|date',
            'actual_end_date' => 'sometimes|nullable|date',

            // Budget and Financial
            'budget' => 'sometimes|nullable|numeric|min:0',
            'currency' => 'sometimes|nullable|string|size:3',
            'is_billable' => 'sometimes|boolean',
            'hourly_rate' => 'sometimes|nullable|numeric|min:0',
            'fixed_price' => 'sometimes|nullable|numeric|min:0',
            'billing_type' => 'sometimes|nullable|string|in:HOURLY,FIXED,MILESTONE',

            // Progress
            'progress_percentage' => 'sometimes|integer|between:0,100',
            'auto_calculate_progress' => 'sometimes|boolean',

            // Settings
            'visibility' => 'sometimes|string|in:PUBLIC,PRIVATE,TEAM,CLIENT',
            'enable_time_tracking' => 'sometimes|boolean',
            'enable_expenses' => 'sometimes|boolean',
            'enable_documents' => 'sometimes|boolean',
            'enable_chat' => 'sometimes|boolean',
            'enable_gantt' => 'sometimes|boolean',
            'enable_kanban' => 'sometimes|boolean',
            'enable_calendar' => 'sometimes|boolean',

            // Risk Management
            'risk_level' => 'sometimes|string|in:LOW,MEDIUM,HIGH,CRITICAL',
            'risk_factors' => 'sometimes|nullable|array',
            'risk_factors.*' => 'string|max:255',

            // Quality Assurance
            'quality_standards' => 'sometimes|nullable|array',
            'testing_requirements' => 'sometimes|nullable|string|max:2000',
            'acceptance_criteria' => 'sometimes|nullable|string|max:2000',

            // Integration Settings
            'repository_url' => 'sometimes|nullable|url',
            'deployment_url' => 'sometimes|nullable|url',
            'staging_url' => 'sometimes|nullable|url',
            'documentation_url' => 'sometimes|nullable|url',

            // Compliance and Standards
            'compliance_requirements' => 'sometimes|nullable|array',
            'industry_standards' => 'sometimes|nullable|array',
            'security_level' => 'sometimes|string|in:LOW,MEDIUM,HIGH,CRITICAL',

            // Custom Fields
            'custom_fields' => 'sometimes|nullable|array',
            'tags' => 'sometimes|nullable|array',
            'tags.*' => 'string|max:50',

            // Notifications
            'notification_settings' => 'sometimes|nullable|array',

            // Additional Settings
            'notes' => 'sometimes|nullable|string|max:2000',
            'metadata' => 'sometimes|nullable|array',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'name.max' => 'اسم المشروع لا يجب أن يتجاوز 255 حرف',
            'description.max' => 'وصف المشروع لا يجب أن يتجاوز 2000 حرف',
            'code.unique' => 'رمز المشروع مستخدم مسبقاً',
            'code.regex' => 'رمز المشروع يجب أن يحتوي على أحرف كبيرة وأرقام وشرطات فقط',
            'client_id.exists' => 'العميل المحدد غير موجود',
            'project_manager_id.exists' => 'مدير المشروع المحدد غير موجود',
            'parent_id.exists' => 'المشروع الأب المحدد غير موجود',

            // Status and Priority
            'status.in' => 'حالة المشروع المحددة غير صحيحة',
            'priority.in' => 'أولوية المشروع المحددة غير صحيحة',
            'category.in' => 'فئة المشروع المحددة غير صحيحة',
            'methodology.in' => 'منهجية المشروع المحددة غير صحيحة',

            // Dates
            'end_date.after' => 'تاريخ نهاية المشروع يجب أن يكون بعد تاريخ البداية',

            // Budget and Financial
            'budget.numeric' => 'الميزانية يجب أن تكون رقماً',
            'budget.min' => 'الميزانية لا يمكن أن تكون سالبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'hourly_rate.numeric' => 'السعر بالساعة يجب أن يكون رقماً',
            'hourly_rate.min' => 'السعر بالساعة لا يمكن أن يكون سالباً',
            'fixed_price.numeric' => 'السعر الثابت يجب أن يكون رقماً',
            'fixed_price.min' => 'السعر الثابت لا يمكن أن يكون سالباً',
            'billing_type.in' => 'نوع الفوترة المحدد غير صحيح',

            // Progress
            'progress_percentage.between' => 'نسبة التقدم يجب أن تكون بين 0 و 100',

            // Settings
            'visibility.in' => 'مستوى الرؤية المحدد غير صحيح',

            // Risk Management
            'risk_level.in' => 'مستوى المخاطر المحدد غير صحيح',

            // Security
            'security_level.in' => 'مستوى الأمان المحدد غير صحيح',

            // URLs
            'repository_url.url' => 'رابط المستودع غير صحيح',
            'deployment_url.url' => 'رابط النشر غير صحيح',
            'staging_url.url' => 'رابط الاختبار غير صحيح',
            'documentation_url.url' => 'رابط التوثيق غير صحيح',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper(trim($this->code))
            ]);
        }
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $projectId = $this->route('project') ?? $this->route('id');

            // التحقق من عدم تعيين المشروع كأب لنفسه
            if ($this->parent_id && $this->parent_id == $projectId) {
                $validator->errors()->add('parent_id', 'لا يمكن للمشروع أن يكون أباً لنفسه');
            }

            // التحقق من صحة التواريخ
            if ($this->start_date && $this->end_date) {
                if ($this->start_date >= $this->end_date) {
                    $validator->errors()->add('end_date', 'تاريخ نهاية المشروع يجب أن يكون بعد تاريخ البداية');
                }
            }

            if ($this->planned_start_date && $this->planned_end_date) {
                if ($this->planned_start_date >= $this->planned_end_date) {
                    $validator->errors()->add('planned_end_date', 'تاريخ النهاية المخطط يجب أن يكون بعد تاريخ البداية المخطط');
                }
            }

            if ($this->actual_start_date && $this->actual_end_date) {
                if ($this->actual_start_date >= $this->actual_end_date) {
                    $validator->errors()->add('actual_end_date', 'تاريخ النهاية الفعلي يجب أن يكون بعد تاريخ البداية الفعلي');
                }
            }

            // التحقق من صحة الميزانية والسعر
            if ($this->billing_type === 'FIXED' && !$this->fixed_price) {
                $validator->errors()->add('fixed_price', 'السعر الثابت مطلوب عند اختيار الفوترة الثابتة');
            }

            if ($this->billing_type === 'HOURLY' && !$this->hourly_rate) {
                $validator->errors()->add('hourly_rate', 'السعر بالساعة مطلوب عند اختيار الفوترة بالساعة');
            }

            // التحقق من حالة المشروع
            if ($this->status) {
                $project = Project::find($projectId);
                if ($project) {
                    // منع تغيير حالة المشروع المكتمل إلى حالة أخرى
                    if ($project->status === 'COMPLETED' && $this->status !== 'COMPLETED') {
                        $validator->errors()->add('status', 'لا يمكن تغيير حالة المشروع المكتمل');
                    }

                    // التحقق من وجود مهام غير مكتملة عند تغيير الحالة إلى مكتمل
                    if ($this->status === 'COMPLETED') {
                        $incompleteTasks = $project->tasks()
                            ->whereNotIn('status', ['DONE', 'CANCELLED'])
                            ->count();
                        
                        if ($incompleteTasks > 0) {
                            $validator->errors()->add('status', 'لا يمكن إكمال المشروع مع وجود مهام غير مكتملة');
                        }
                    }
                }
            }

            // التحقق من نسبة التقدم
            if ($this->has('progress_percentage') && $this->progress_percentage === 100) {
                if (!$this->has('status') || $this->status !== 'COMPLETED') {
                    $validator->errors()->add('progress_percentage', 'يجب تغيير حالة المشروع إلى مكتمل عند الوصول لـ 100%');
                }
            }

            // التحقق من التواريخ الفعلية
            if ($this->actual_end_date && !$this->actual_start_date) {
                $validator->errors()->add('actual_start_date', 'تاريخ البداية الفعلي مطلوب عند تحديد تاريخ النهاية الفعلي');
            }

            // التحقق من مدة المشروع عند التحديث
            if ($this->start_date && $this->end_date) {
                $startDate = \Carbon\Carbon::parse($this->start_date);
                $endDate = \Carbon\Carbon::parse($this->end_date);
                $duration = $startDate->diffInDays($endDate);

                if ($duration > 365 * 3) { // 3 سنوات
                    $validator->errors()->add('end_date', 'مدة المشروع لا يمكن أن تتجاوز 3 سنوات');
                }
            }
        });
    }

    /**
     * التحقق من وجود تغييرات حساسة
     */
    public function hasSensitiveChanges(): bool
    {
        $sensitiveFields = [
            'status',
            'project_manager_id',
            'budget',
            'end_date',
            'billing_type',
            'fixed_price',
            'hourly_rate',
        ];

        foreach ($sensitiveFields as $field) {
            if ($this->has($field)) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على التغييرات الحساسة
     */
    public function getSensitiveChanges(): array
    {
        $sensitiveFields = [
            'status' => 'حالة المشروع',
            'project_manager_id' => 'مدير المشروع',
            'budget' => 'الميزانية',
            'end_date' => 'تاريخ النهاية',
            'billing_type' => 'نوع الفوترة',
            'fixed_price' => 'السعر الثابت',
            'hourly_rate' => 'السعر بالساعة',
        ];

        $changes = [];
        foreach ($sensitiveFields as $field => $label) {
            if ($this->has($field)) {
                $changes[$field] = $label;
            }
        }

        return $changes;
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value, $key) {
            // الاحتفاظ بالحقول المنطقية حتى لو كانت false
            if (in_array($key, [
                'is_billable', 'auto_calculate_progress', 'enable_time_tracking',
                'enable_expenses', 'enable_documents', 'enable_chat',
                'enable_gantt', 'enable_kanban', 'enable_calendar'
            ])) {
                return true;
            }
            return $value !== null && $value !== '';
        }, ARRAY_FILTER_USE_BOTH);
    }
}
