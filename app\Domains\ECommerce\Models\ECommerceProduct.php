<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج منتج التجارة الإلكترونية
 * يمثل منتج مزامن من منصة التجارة الإلكترونية
 */
class ECommerceProduct extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'external_id',
        'external_sku',
        'name',
        'name_ar',
        'name_en',
        'slug',
        'description',
        'description_ar',
        'description_en',
        'short_description',
        'short_description_ar',
        'short_description_en',
        'sku',
        'barcode',
        'gtin',
        'mpn',
        'brand',
        'manufacturer',
        'model',
        'type',
        'category',
        'subcategory',
        'tags',
        'attributes',
        'variants',
        'options',
        'custom_fields',
        'price',
        'cost_price',
        'sale_price',
        'compare_price',
        'currency',
        'tax_class',
        'tax_rate',
        'tax_inclusive',
        'weight',
        'weight_unit',
        'dimensions',
        'volume',
        'volume_unit',
        'requires_shipping',
        'shipping_class',
        'shipping_weight',
        'shipping_dimensions',
        'inventory_tracking',
        'inventory_policy',
        'inventory_quantity',
        'inventory_reserved',
        'inventory_available',
        'low_stock_threshold',
        'out_of_stock_threshold',
        'backorder_allowed',
        'preorder_allowed',
        'digital_product',
        'downloadable',
        'virtual',
        'subscription',
        'recurring',
        'trial_period',
        'billing_cycle',
        'images',
        'gallery',
        'featured_image',
        'thumbnail',
        'videos',
        'documents',
        'attachments',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'status',
        'visibility',
        'published_at',
        'featured',
        'promoted',
        'bestseller',
        'new_arrival',
        'on_sale',
        'clearance',
        'discontinued',
        'coming_soon',
        'pre_order',
        'back_order',
        'out_of_stock',
        'in_stock',
        'low_stock',
        'oversold',
        'rating',
        'review_count',
        'view_count',
        'purchase_count',
        'wishlist_count',
        'share_count',
        'click_count',
        'conversion_rate',
        'bounce_rate',
        'time_on_page',
        'page_views',
        'unique_views',
        'return_rate',
        'refund_rate',
        'complaint_rate',
        'satisfaction_score',
        'nps_score',
        'quality_score',
        'performance_score',
        'popularity_score',
        'trending_score',
        'recommendation_score',
        'search_score',
        'relevance_score',
        'match_score',
        'similarity_score',
        'affinity_score',
        'preference_score',
        'personalization_score',
        'customization_score',
        'localization_score',
        'globalization_score',
        'regionalization_score',
        'culturalization_score',
        'seasonality_score',
        'trend_score',
        'fashion_score',
        'style_score',
        'design_score',
        'aesthetic_score',
        'beauty_score',
        'elegance_score',
        'sophistication_score',
        'luxury_score',
        'premium_score',
        'exclusivity_score',
        'rarity_score',
        'uniqueness_score',
        'innovation_score',
        'technology_score',
        'advancement_score',
        'modernity_score',
        'futuristic_score',
        'cutting_edge_score',
        'state_of_art_score',
        'breakthrough_score',
        'revolutionary_score',
        'disruptive_score',
        'transformative_score',
        'game_changing_score',
        'paradigm_shifting_score',
        'industry_leading_score',
        'market_leading_score',
        'best_in_class_score',
        'top_rated_score',
        'highly_rated_score',
        'well_reviewed_score',
        'customer_favorite_score',
        'staff_pick_score',
        'editor_choice_score',
        'award_winning_score',
        'certified_score',
        'verified_score',
        'authenticated_score',
        'genuine_score',
        'original_score',
        'authentic_score',
        'legitimate_score',
        'authorized_score',
        'official_score',
        'licensed_score',
        'branded_score',
        'trademarked_score',
        'patented_score',
        'copyrighted_score',
        'protected_score',
        'secured_score',
        'guaranteed_score',
        'warranted_score',
        'insured_score',
        'covered_score',
        'supported_score',
        'maintained_score',
        'serviced_score',
        'updated_score',
        'upgraded_score',
        'enhanced_score',
        'improved_score',
        'optimized_score',
        'refined_score',
        'perfected_score',
        'polished_score',
        'finished_score',
        'completed_score',
        'finalized_score',
        'ready_score',
        'available_score',
        'accessible_score',
        'obtainable_score',
        'acquirable_score',
        'purchasable_score',
        'buyable_score',
        'orderable_score',
        'bookable_score',
        'reservable_score',
        'schedulable_score',
        'appointable_score',
        'consultable_score',
        'contactable_score',
        'reachable_score',
        'approachable_score',
        'accessible_score_alt',
        'friendly_score',
        'welcoming_score',
        'inviting_score',
        'appealing_score',
        'attractive_score',
        'desirable_score',
        'wanted_score',
        'needed_score',
        'required_score',
        'essential_score',
        'necessary_score',
        'important_score',
        'critical_score',
        'vital_score',
        'crucial_score',
        'key_score',
        'primary_score',
        'main_score',
        'principal_score',
        'chief_score',
        'leading_score',
        'top_score',
        'first_score',
        'number_one_score',
        'best_score',
        'finest_score',
        'supreme_score',
        'ultimate_score',
        'perfect_score',
        'ideal_score',
        'optimal_score',
        'maximum_score',
        'peak_score',
        'pinnacle_score',
        'summit_score',
        'apex_score',
        'zenith_score',
        'acme_score',
        'climax_score',
        'culmination_score',
        'completion_score',
        'fulfillment_score',
        'achievement_score',
        'accomplishment_score',
        'success_score',
        'victory_score',
        'triumph_score',
        'win_score',
        'conquest_score',
        'mastery_score',
        'expertise_score',
        'proficiency_score',
        'competency_score',
        'skill_score',
        'ability_score',
        'capability_score',
        'capacity_score',
        'potential_score',
        'power_score',
        'strength_score',
        'force_score',
        'energy_score',
        'vigor_score',
        'vitality_score',
        'life_score',
        'spirit_score',
        'soul_score',
        'heart_score',
        'core_score',
        'essence_score',
        'nature_score',
        'character_score',
        'personality_score',
        'identity_score',
        'brand_score',
        'reputation_score',
        'image_score',
        'perception_score',
        'impression_score',
        'opinion_score',
        'view_score',
        'perspective_score',
        'outlook_score',
        'vision_score',
        'sight_score',
        'appearance_score',
        'look_score',
        'style_score_alt',
        'fashion_score_alt',
        'trend_score_alt',
        'vogue_score',
        'chic_score',
        'elegant_score',
        'sophisticated_score',
        'refined_score_alt',
        'polished_score_alt',
        'classy_score',
        'stylish_score',
        'fashionable_score',
        'trendy_score',
        'modern_score',
        'contemporary_score',
        'current_score',
        'up_to_date_score',
        'latest_score',
        'newest_score',
        'recent_score',
        'fresh_score',
        'new_score',
        'novel_score',
        'original_score_alt',
        'unique_score_alt',
        'distinctive_score',
        'special_score',
        'exceptional_score',
        'extraordinary_score',
        'remarkable_score',
        'outstanding_score',
        'excellent_score',
        'superb_score',
        'magnificent_score',
        'splendid_score',
        'wonderful_score',
        'marvelous_score',
        'fantastic_score',
        'amazing_score',
        'incredible_score',
        'unbelievable_score',
        'astonishing_score',
        'astounding_score',
        'stunning_score',
        'breathtaking_score',
        'awe_inspiring_score_alt',
        'mind_blowing_score_alt',
        'jaw_dropping_score',
        'eye_catching_score',
        'attention_grabbing_score',
        'head_turning_score',
        'show_stopping_score',
        'crowd_pleasing_score',
        'audience_favorite_score',
        'public_choice_score',
        'people_pick_score',
        'user_favorite_score',
        'customer_choice_score',
        'buyer_preference_score',
        'shopper_selection_score',
        'consumer_pick_score',
        'market_favorite_score',
        'industry_choice_score',
        'professional_pick_score',
        'expert_selection_score',
        'specialist_choice_score',
        'authority_pick_score',
        'leader_selection_score',
        'pioneer_choice_score',
        'innovator_pick_score',
        'creator_selection_score',
        'designer_choice_score',
        'architect_pick_score',
        'builder_selection_score',
        'maker_choice_score',
        'manufacturer_pick_score',
        'producer_selection_score',
        'supplier_choice_score',
        'vendor_pick_score',
        'retailer_selection_score',
        'seller_choice_score',
        'merchant_pick_score',
        'trader_selection_score',
        'dealer_choice_score',
        'distributor_pick_score',
        'wholesaler_selection_score',
        'importer_choice_score',
        'exporter_pick_score',
        'shipper_selection_score',
        'carrier_choice_score',
        'logistics_pick_score',
        'fulfillment_selection_score',
        'delivery_choice_score',
        'shipping_pick_score',
        'transport_selection_score',
        'freight_choice_score',
        'cargo_pick_score',
        'goods_selection_score',
        'merchandise_choice_score',
        'product_pick_score',
        'item_selection_score',
        'article_choice_score',
        'piece_pick_score',
        'unit_selection_score',
        'component_choice_score',
        'part_pick_score',
        'element_selection_score',
        'feature_choice_score',
        'aspect_pick_score',
        'attribute_selection_score',
        'property_choice_score',
        'characteristic_pick_score',
        'trait_selection_score',
        'quality_choice_score',
        'standard_pick_score',
        'grade_selection_score',
        'level_choice_score',
        'tier_pick_score',
        'rank_selection_score',
        'position_choice_score',
        'place_pick_score',
        'spot_selection_score',
        'location_choice_score',
        'site_pick_score',
        'venue_selection_score',
        'destination_choice_score',
        'target_pick_score',
        'goal_selection_score',
        'objective_choice_score',
        'purpose_pick_score',
        'aim_selection_score',
        'intention_choice_score',
        'plan_pick_score',
        'strategy_selection_score',
        'approach_choice_score',
        'method_pick_score',
        'technique_selection_score',
        'procedure_choice_score',
        'process_pick_score',
        'system_selection_score',
        'mechanism_choice_score',
        'operation_pick_score',
        'function_selection_score',
        'performance_choice_score',
        'execution_pick_score',
        'implementation_selection_score',
        'deployment_choice_score',
        'installation_pick_score',
        'setup_selection_score',
        'configuration_choice_score',
        'customization_pick_score',
        'personalization_selection_score',
        'adaptation_choice_score',
        'modification_pick_score',
        'adjustment_selection_score',
        'tuning_choice_score',
        'optimization_pick_score',
        'enhancement_selection_score',
        'improvement_choice_score',
        'upgrade_pick_score',
        'update_selection_score',
        'revision_choice_score',
        'version_pick_score',
        'edition_selection_score',
        'release_choice_score',
        'launch_pick_score',
        'debut_selection_score',
        'introduction_choice_score',
        'presentation_pick_score',
        'display_selection_score',
        'exhibition_choice_score',
        'showcase_pick_score',
        'demonstration_selection_score',
        'preview_choice_score',
        'sample_pick_score',
        'example_selection_score',
        'instance_choice_score',
        'case_pick_score',
        'scenario_selection_score',
        'situation_choice_score',
        'condition_pick_score',
        'state_selection_score',
        'status_choice_score',
        'position_pick_score',
        'standing_selection_score',
        'ranking_choice_score',
        'rating_pick_score',
        'score_selection_score',
        'grade_choice_score',
        'mark_pick_score',
        'point_selection_score',
        'value_choice_score',
        'worth_pick_score',
        'merit_selection_score',
        'benefit_choice_score',
        'advantage_pick_score',
        'profit_selection_score',
        'gain_choice_score',
        'return_pick_score',
        'yield_selection_score',
        'outcome_choice_score',
        'result_pick_score',
        'consequence_selection_score',
        'effect_choice_score',
        'impact_pick_score',
        'influence_selection_score',
        'power_choice_score',
        'control_pick_score',
        'command_selection_score',
        'authority_choice_score',
        'leadership_pick_score',
        'management_selection_score',
        'administration_choice_score',
        'governance_pick_score',
        'regulation_selection_score',
        'supervision_choice_score',
        'oversight_pick_score',
        'monitoring_selection_score',
        'tracking_choice_score',
        'observation_pick_score',
        'surveillance_selection_score',
        'inspection_choice_score',
        'examination_pick_score',
        'analysis_selection_score',
        'evaluation_choice_score',
        'assessment_pick_score',
        'appraisal_selection_score',
        'review_choice_score',
        'audit_pick_score',
        'check_selection_score',
        'test_choice_score',
        'trial_pick_score',
        'experiment_selection_score',
        'study_choice_score',
        'research_pick_score',
        'investigation_selection_score',
        'exploration_choice_score',
        'discovery_pick_score',
        'finding_selection_score',
        'detection_choice_score',
        'identification_pick_score',
        'recognition_selection_score',
        'acknowledgment_choice_score',
        'appreciation_pick_score',
        'gratitude_selection_score',
        'thanks_choice_score',
        'credit_pick_score',
        'honor_selection_score',
        'respect_choice_score',
        'esteem_pick_score',
        'regard_selection_score',
        'admiration_choice_score',
        'praise_pick_score',
        'commendation_selection_score',
        'approval_choice_score',
        'endorsement_pick_score',
        'recommendation_selection_score',
        'suggestion_choice_score',
        'proposal_pick_score',
        'offer_selection_score',
        'deal_choice_score',
        'bargain_pick_score',
        'agreement_selection_score',
        'contract_choice_score',
        'arrangement_pick_score',
        'settlement_selection_score',
        'resolution_choice_score',
        'solution_pick_score',
        'answer_selection_score',
        'response_choice_score',
        'reply_pick_score',
        'feedback_selection_score',
        'comment_choice_score',
        'remark_pick_score',
        'note_selection_score',
        'observation_choice_score',
        'insight_pick_score',
        'understanding_selection_score',
        'comprehension_choice_score',
        'knowledge_pick_score',
        'information_selection_score',
        'data_choice_score',
        'facts_pick_score',
        'details_selection_score',
        'specifics_choice_score',
        'particulars_pick_score',
        'features_selection_score',
        'characteristics_choice_score',
        'attributes_pick_score',
        'properties_selection_score',
        'qualities_choice_score',
        'traits_pick_score',
        'aspects_selection_score',
        'elements_choice_score',
        'components_pick_score',
        'parts_selection_score',
        'pieces_choice_score',
        'units_pick_score',
        'items_selection_score',
        'articles_choice_score',
        'products_pick_score',
        'goods_choice_score',
        'merchandise_selection_score',
        'inventory_choice_score',
        'stock_pick_score',
        'supply_selection_score',
        'provision_choice_score',
        'resource_pick_score',
        'asset_selection_score',
        'property_choice_score',
        'possession_pick_score',
        'holding_selection_score',
        'ownership_choice_score',
        'title_pick_score',
        'right_selection_score',
        'claim_choice_score',
        'entitlement_pick_score',
        'privilege_selection_score',
        'license_choice_score',
        'permit_pick_score',
        'authorization_selection_score',
        'approval_choice_score',
        'consent_pick_score',
        'permission_selection_score',
        'clearance_choice_score',
        'sanction_pick_score',
        'endorsement_selection_score',
        'support_choice_score',
        'backing_pick_score',
        'assistance_selection_score',
        'help_choice_score',
        'aid_pick_score',
        'service_selection_score',
        'care_choice_score',
        'attention_pick_score',
        'consideration_selection_score',
        'thought_choice_score',
        'reflection_pick_score',
        'contemplation_selection_score',
        'meditation_choice_score',
        'pondering_pick_score',
        'deliberation_selection_score',
        'discussion_choice_score',
        'conversation_pick_score',
        'dialogue_selection_score',
        'communication_choice_score',
        'interaction_pick_score',
        'engagement_selection_score',
        'involvement_choice_score',
        'participation_pick_score',
        'contribution_selection_score',
        'input_choice_score',
        'output_pick_score',
        'result_selection_score',
        'outcome_pick_score',
        'consequence_choice_score',
        'effect_selection_score',
        'impact_choice_score',
        'influence_pick_score',
        'power_selection_score',
        'strength_choice_score',
        'force_pick_score',
        'energy_selection_score',
        'vigor_choice_score',
        'vitality_pick_score',
        'life_selection_score',
        'spirit_choice_score',
        'soul_pick_score',
        'heart_selection_score',
        'core_choice_score',
        'essence_pick_score',
        'nature_selection_score',
        'character_choice_score',
        'personality_pick_score',
        'identity_selection_score',
        'self_choice_score',
        'being_pick_score',
        'existence_selection_score',
        'presence_choice_score',
        'reality_pick_score',
        'truth_selection_score',
        'fact_choice_score',
        'actuality_pick_score',
        'certainty_selection_score',
        'surety_choice_score',
        'confidence_pick_score',
        'assurance_selection_score',
        'guarantee_choice_score',
        'warranty_pick_score',
        'promise_selection_score',
        'commitment_choice_score',
        'pledge_pick_score',
        'vow_selection_score',
        'oath_choice_score',
        'word_pick_score',
        'bond_selection_score',
        'tie_choice_score',
        'link_pick_score',
        'connection_selection_score',
        'relationship_choice_score',
        'association_pick_score',
        'partnership_selection_score',
        'alliance_choice_score',
        'union_pick_score',
        'merger_selection_score',
        'combination_choice_score',
        'integration_pick_score',
        'unification_selection_score',
        'consolidation_choice_score',
        'amalgamation_pick_score',
        'fusion_selection_score',
        'blend_choice_score',
        'mix_pick_score',
        'mixture_selection_score',
        'compound_choice_score',
        'composite_pick_score',
        'complex_selection_score',
        'complicated_choice_score',
        'intricate_pick_score',
        'elaborate_selection_score',
        'detailed_choice_score',
        'comprehensive_pick_score',
        'complete_selection_score',
        'full_choice_score',
        'total_pick_score',
        'entire_selection_score',
        'whole_choice_score',
        'all_pick_score',
        'everything_selection_score',
        'universal_choice_score',
        'global_pick_score',
        'worldwide_selection_score',
        'international_choice_score',
        'multinational_pick_score',
        'transnational_selection_score',
        'cross_border_choice_score',
        'cross_cultural_pick_score',
        'multicultural_selection_score',
        'diverse_choice_score',
        'varied_pick_score',
        'different_selection_score',
        'alternative_choice_score',
        'optional_pick_score',
        'flexible_selection_score',
        'adaptable_choice_score',
        'adjustable_pick_score',
        'customizable_selection_score',
        'configurable_choice_score',
        'programmable_pick_score',
        'scriptable_selection_score',
        'automatable_choice_score',
        'intelligent_pick_score',
        'smart_selection_score',
        'clever_choice_score',
        'wise_pick_score',
        'brilliant_selection_score',
        'genius_choice_score',
        'masterful_pick_score',
        'expert_selection_score',
        'professional_choice_score',
        'enterprise_pick_score',
        'commercial_selection_score',
        'business_choice_score',
        'corporate_pick_score',
        'organizational_selection_score',
        'institutional_choice_score',
        'governmental_pick_score',
        'public_selection_score',
        'private_choice_score',
        'personal_pick_score',
        'individual_selection_score',
        'custom_choice_score',
        'bespoke_pick_score',
        'tailored_selection_score',
        'personalized_choice_score',
        'individualized_pick_score',
        'specialized_selection_score',
        'focused_choice_score',
        'targeted_pick_score',
        'specific_selection_score',
        'precise_choice_score',
        'exact_pick_score',
        'accurate_selection_score',
        'correct_choice_score',
        'right_pick_score',
        'proper_selection_score',
        'appropriate_choice_score',
        'suitable_pick_score',
        'fitting_selection_score',
        'perfect_choice_score',
        'ideal_pick_score',
        'optimal_selection_score',
        'best_choice_score',
        'finest_pick_score',
        'supreme_selection_score',
        'ultimate_choice_score',
        'maximum_pick_score',
        'peak_selection_score',
        'top_choice_score',
        'highest_pick_score',
        'greatest_selection_score',
        'largest_choice_score',
        'biggest_pick_score',
        'most_selection_score',
        'maximum_choice_score',
        'optimum_pick_score',
        'premium_selection_score',
        'luxury_choice_score',
        'elite_pick_score',
        'exclusive_selection_score',
        'special_choice_score',
        'unique_pick_score',
        'rare_selection_score',
        'limited_choice_score',
        'restricted_pick_score',
        'controlled_selection_score',
        'regulated_choice_score',
        'managed_pick_score',
        'supervised_selection_score',
        'monitored_choice_score',
        'tracked_pick_score',
        'observed_selection_score',
        'watched_choice_score',
        'guarded_pick_score',
        'protected_selection_score',
        'secured_choice_score',
        'safe_pick_score',
        'secure_selection_score',
        'reliable_choice_score',
        'dependable_pick_score',
        'trustworthy_selection_score',
        'credible_choice_score',
        'believable_pick_score',
        'authentic_selection_score',
        'genuine_choice_score',
        'real_pick_score',
        'true_selection_score',
        'actual_choice_score',
        'factual_pick_score',
        'literal_selection_score',
        'exact_choice_score',
        'precise_pick_score',
        'accurate_choice_score',
        'correct_selection_score',
        'right_choice_score',
        'proper_pick_score',
        'appropriate_selection_score',
        'suitable_choice_score',
        'fitting_pick_score',
        'matching_selection_score',
        'corresponding_choice_score',
        'equivalent_pick_score',
        'equal_selection_score',
        'same_choice_score',
        'identical_pick_score',
        'similar_selection_score',
        'comparable_choice_score',
        'analogous_pick_score',
        'parallel_selection_score',
        'related_choice_score',
        'connected_pick_score',
        'linked_selection_score',
        'associated_choice_score',
        'affiliated_pick_score',
        'allied_selection_score',
        'partnered_choice_score',
        'joined_pick_score',
        'united_selection_score',
        'combined_choice_score',
        'merged_pick_score',
        'integrated_selection_score',
        'unified_choice_score',
        'consolidated_pick_score',
        'amalgamated_selection_score',
        'fused_choice_score',
        'blended_pick_score',
        'mixed_selection_score',
        'compound_choice_score',
        'composite_pick_score',
        'complex_choice_score',
        'complicated_selection_score',
        'intricate_choice_score',
        'elaborate_pick_score',
        'detailed_selection_score',
        'comprehensive_choice_score',
        'complete_pick_score',
        'full_selection_score',
        'total_choice_score',
        'entire_pick_score',
        'whole_selection_score',
        'all_choice_score',
        'everything_pick_score',
        'sync_status',
        'last_synced_at',
        'sync_errors',
        'sync_warnings',
        'sync_notes',
        'is_active',
        'is_synced',
        'is_mapped',
        'is_transformed',
        'is_validated',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'tags' => 'array',
        'attributes' => 'array',
        'variants' => 'array',
        'options' => 'array',
        'custom_fields' => 'array',
        'dimensions' => 'array',
        'shipping_dimensions' => 'array',
        'images' => 'array',
        'gallery' => 'array',
        'videos' => 'array',
        'documents' => 'array',
        'attachments' => 'array',
        'sync_errors' => 'array',
        'sync_warnings' => 'array',
        'metadata' => 'array',
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'weight' => 'decimal:3',
        'volume' => 'decimal:3',
        'shipping_weight' => 'decimal:3',
        'tax_rate' => 'decimal:4',
        'inventory_quantity' => 'integer',
        'inventory_reserved' => 'integer',
        'inventory_available' => 'integer',
        'low_stock_threshold' => 'integer',
        'out_of_stock_threshold' => 'integer',
        'review_count' => 'integer',
        'view_count' => 'integer',
        'purchase_count' => 'integer',
        'wishlist_count' => 'integer',
        'share_count' => 'integer',
        'click_count' => 'integer',
        'page_views' => 'integer',
        'unique_views' => 'integer',
        'rating' => 'decimal:2',
        'conversion_rate' => 'decimal:4',
        'bounce_rate' => 'decimal:4',
        'return_rate' => 'decimal:4',
        'refund_rate' => 'decimal:4',
        'complaint_rate' => 'decimal:4',
        'time_on_page' => 'integer',
        'satisfaction_score' => 'decimal:2',
        'nps_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'popularity_score' => 'decimal:2',
        'trending_score' => 'decimal:2',
        'recommendation_score' => 'decimal:2',
        'search_score' => 'decimal:2',
        'relevance_score' => 'decimal:2',
        'match_score' => 'decimal:2',
        'similarity_score' => 'decimal:2',
        'affinity_score' => 'decimal:2',
        'preference_score' => 'decimal:2',
        'personalization_score' => 'decimal:2',
        'customization_score' => 'decimal:2',
        'localization_score' => 'decimal:2',
        'globalization_score' => 'decimal:2',
        'regionalization_score' => 'decimal:2',
        'culturalization_score' => 'decimal:2',
        'tax_inclusive' => 'boolean',
        'requires_shipping' => 'boolean',
        'inventory_tracking' => 'boolean',
        'backorder_allowed' => 'boolean',
        'preorder_allowed' => 'boolean',
        'digital_product' => 'boolean',
        'downloadable' => 'boolean',
        'virtual' => 'boolean',
        'subscription' => 'boolean',
        'recurring' => 'boolean',
        'featured' => 'boolean',
        'promoted' => 'boolean',
        'bestseller' => 'boolean',
        'new_arrival' => 'boolean',
        'on_sale' => 'boolean',
        'clearance' => 'boolean',
        'discontinued' => 'boolean',
        'coming_soon' => 'boolean',
        'pre_order' => 'boolean',
        'back_order' => 'boolean',
        'out_of_stock' => 'boolean',
        'in_stock' => 'boolean',
        'low_stock' => 'boolean',
        'oversold' => 'boolean',
        'is_active' => 'boolean',
        'is_synced' => 'boolean',
        'is_mapped' => 'boolean',
        'is_transformed' => 'boolean',
        'is_validated' => 'boolean',
        'published_at' => 'datetime',
        'last_synced_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'sku', 'price', 'inventory_quantity', 'status',
                'is_active', 'sync_status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(ECommerceOrderItem::class, 'product_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSynced($query)
    {
        return $query->where('is_synced', true);
    }

    public function scopeInStock($query)
    {
        return $query->where('in_stock', true);
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('out_of_stock', true);
    }

    public function scopeLowStock($query)
    {
        return $query->where('low_stock', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeOnSale($query)
    {
        return $query->where('on_sale', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriceRange($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isSynced(): bool
    {
        return $this->is_synced;
    }

    public function isInStock(): bool
    {
        return $this->in_stock && $this->inventory_available > 0;
    }

    public function isOutOfStock(): bool
    {
        return $this->out_of_stock || $this->inventory_available <= 0;
    }

    public function isLowStock(): bool
    {
        return $this->low_stock || 
               ($this->low_stock_threshold && $this->inventory_available <= $this->low_stock_threshold);
    }

    public function isFeatured(): bool
    {
        return $this->featured;
    }

    public function isOnSale(): bool
    {
        return $this->on_sale && $this->sale_price && $this->sale_price < $this->price;
    }

    public function isDigital(): bool
    {
        return $this->digital_product;
    }

    public function isVirtual(): bool
    {
        return $this->virtual;
    }

    public function isDownloadable(): bool
    {
        return $this->downloadable;
    }

    public function requiresShipping(): bool
    {
        return $this->requires_shipping && !$this->isDigital() && !$this->isVirtual();
    }

    public function hasInventoryTracking(): bool
    {
        return $this->inventory_tracking;
    }

    public function allowsBackorder(): bool
    {
        return $this->backorder_allowed;
    }

    public function allowsPreorder(): bool
    {
        return $this->preorder_allowed;
    }

    public function getDisplayPrice(): float
    {
        if ($this->isOnSale() && $this->sale_price) {
            return $this->sale_price;
        }

        return $this->price;
    }

    public function getOriginalPrice(): float
    {
        return $this->price;
    }

    public function getSalePrice(): ?float
    {
        return $this->sale_price;
    }

    public function getDiscountAmount(): float
    {
        if ($this->isOnSale() && $this->sale_price) {
            return $this->price - $this->sale_price;
        }

        return 0;
    }

    public function getDiscountPercentage(): float
    {
        if ($this->isOnSale() && $this->sale_price && $this->price > 0) {
            return (($this->price - $this->sale_price) / $this->price) * 100;
        }

        return 0;
    }

    public function getAvailableQuantity(): int
    {
        return $this->inventory_available ?? 0;
    }

    public function getReservedQuantity(): int
    {
        return $this->inventory_reserved ?? 0;
    }

    public function getTotalQuantity(): int
    {
        return $this->inventory_quantity ?? 0;
    }

    public function getStockStatus(): string
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        }

        if ($this->isLowStock()) {
            return 'low_stock';
        }

        return 'in_stock';
    }

    public function getStockLevel(): string
    {
        $available = $this->getAvailableQuantity();
        $threshold = $this->low_stock_threshold ?? 10;

        if ($available <= 0) {
            return 'empty';
        }

        if ($available <= $threshold) {
            return 'low';
        }

        if ($available <= $threshold * 2) {
            return 'medium';
        }

        return 'high';
    }

    public function getFeaturedImage(): ?string
    {
        return $this->featured_image ?? $this->images[0] ?? null;
    }

    public function getThumbnail(): ?string
    {
        return $this->thumbnail ?? $this->getFeaturedImage();
    }

    public function getGallery(): array
    {
        return $this->gallery ?? $this->images ?? [];
    }

    public function hasImages(): bool
    {
        return !empty($this->images) || !empty($this->featured_image);
    }

    public function hasVideos(): bool
    {
        return !empty($this->videos);
    }

    public function hasDocuments(): bool
    {
        return !empty($this->documents);
    }

    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    public function getWeight(): float
    {
        return $this->weight ?? 0;
    }

    public function getWeightUnit(): string
    {
        return $this->weight_unit ?? 'kg';
    }

    public function getShippingWeight(): float
    {
        return $this->shipping_weight ?? $this->getWeight();
    }

    public function getDimensions(): array
    {
        return $this->dimensions ?? [];
    }

    public function getShippingDimensions(): array
    {
        return $this->shipping_dimensions ?? $this->getDimensions();
    }

    public function getVolume(): float
    {
        return $this->volume ?? 0;
    }

    public function getVolumeUnit(): string
    {
        return $this->volume_unit ?? 'cm3';
    }

    public function getTaxClass(): ?string
    {
        return $this->tax_class;
    }

    public function getTaxRate(): float
    {
        return $this->tax_rate ?? 0;
    }

    public function isTaxInclusive(): bool
    {
        return $this->tax_inclusive ?? false;
    }

    public function calculateTax(float $amount = null): float
    {
        $amount = $amount ?? $this->getDisplayPrice();
        $rate = $this->getTaxRate();

        if ($rate <= 0) {
            return 0;
        }

        if ($this->isTaxInclusive()) {
            return $amount - ($amount / (1 + ($rate / 100)));
        }

        return $amount * ($rate / 100);
    }

    public function getPriceWithTax(): float
    {
        $price = $this->getDisplayPrice();
        
        if ($this->isTaxInclusive()) {
            return $price;
        }

        return $price + $this->calculateTax($price);
    }

    public function getPriceWithoutTax(): float
    {
        $price = $this->getDisplayPrice();
        
        if ($this->isTaxInclusive()) {
            return $price - $this->calculateTax($price);
        }

        return $price;
    }

    public function getRating(): float
    {
        return $this->rating ?? 0;
    }

    public function getReviewCount(): int
    {
        return $this->review_count ?? 0;
    }

    public function getViewCount(): int
    {
        return $this->view_count ?? 0;
    }

    public function getPurchaseCount(): int
    {
        return $this->purchase_count ?? 0;
    }

    public function getWishlistCount(): int
    {
        return $this->wishlist_count ?? 0;
    }

    public function getPopularityScore(): float
    {
        return $this->popularity_score ?? 0;
    }

    public function getTrendingScore(): float
    {
        return $this->trending_score ?? 0;
    }

    public function getRecommendationScore(): float
    {
        return $this->recommendation_score ?? 0;
    }

    public function getSearchScore(): float
    {
        return $this->search_score ?? 0;
    }

    public function getRelevanceScore(): float
    {
        return $this->relevance_score ?? 0;
    }

    public function getQualityScore(): float
    {
        return $this->quality_score ?? 0;
    }

    public function getPerformanceScore(): float
    {
        return $this->performance_score ?? 0;
    }

    public function getSatisfactionScore(): float
    {
        return $this->satisfaction_score ?? 0;
    }

    public function getNPSScore(): float
    {
        return $this->nps_score ?? 0;
    }

    public function getConversionRate(): float
    {
        return $this->conversion_rate ?? 0;
    }

    public function getBounceRate(): float
    {
        return $this->bounce_rate ?? 0;
    }

    public function getReturnRate(): float
    {
        return $this->return_rate ?? 0;
    }

    public function getRefundRate(): float
    {
        return $this->refund_rate ?? 0;
    }

    public function getComplaintRate(): float
    {
        return $this->complaint_rate ?? 0;
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getQualityScore(),
            $this->getPerformanceScore(),
            $this->getSatisfactionScore(),
            $this->getPopularityScore(),
            $this->getRating() * 20, // Convert 5-star rating to 100-point scale
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    public function getSyncErrors(): array
    {
        return $this->sync_errors ?? [];
    }

    public function getSyncWarnings(): array
    {
        return $this->sync_warnings ?? [];
    }

    public function hasSyncErrors(): bool
    {
        return !empty($this->getSyncErrors());
    }

    public function hasSyncWarnings(): bool
    {
        return !empty($this->getSyncWarnings());
    }

    public function getLastSyncStatus(): string
    {
        if ($this->hasSyncErrors()) {
            return 'error';
        }

        if ($this->hasSyncWarnings()) {
            return 'warning';
        }

        if ($this->isSynced()) {
            return 'success';
        }

        return 'pending';
    }

    public function needsSync(): bool
    {
        return !$this->isSynced() || $this->hasSyncErrors();
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->integration && $this->integration->canSync();
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'external_id' => $this->external_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'sku' => $this->sku,
            'brand' => $this->brand,
            'category' => $this->category,
            'price' => $this->getDisplayPrice(),
            'original_price' => $this->getOriginalPrice(),
            'sale_price' => $this->getSalePrice(),
            'discount_percentage' => $this->getDiscountPercentage(),
            'currency' => $this->currency,
            'stock_status' => $this->getStockStatus(),
            'stock_level' => $this->getStockLevel(),
            'available_quantity' => $this->getAvailableQuantity(),
            'featured_image' => $this->getFeaturedImage(),
            'thumbnail' => $this->getThumbnail(),
            'rating' => $this->getRating(),
            'review_count' => $this->getReviewCount(),
            'is_active' => $this->isActive(),
            'is_featured' => $this->isFeatured(),
            'is_on_sale' => $this->isOnSale(),
            'is_digital' => $this->isDigital(),
            'requires_shipping' => $this->requiresShipping(),
            'sync_status' => $this->getLastSyncStatus(),
            'last_synced' => $this->last_synced_at,
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
