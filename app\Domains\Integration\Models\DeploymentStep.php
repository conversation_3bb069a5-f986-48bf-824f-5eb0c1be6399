<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Deployment Step Model
 * 
 * Tracks individual steps in a deployment process
 */
class DeploymentStep extends Model
{
    use HasFactory;

    protected $fillable = [
        'deployment_id',
        'name',
        'description',
        'status',
        'order',
        'config',
        'output',
        'error_message',
        'started_at',
        'completed_at',
        'duration_seconds',
        'retry_count',
        'max_retries',
    ];

    protected $casts = [
        'config' => 'array',
        'output' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'duration_seconds' => 'integer',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
    ];

    /**
     * Relationship with deployment
     */
    public function deployment(): BelongsTo
    {
        return $this->belongsTo(Deployment::class);
    }

    /**
     * Mark step as completed
     */
    public function markAsCompleted(array $output = []): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'duration_seconds' => $this->started_at ? now()->diffInSeconds($this->started_at) : 0,
            'output' => $output,
        ]);
    }

    /**
     * Mark step as failed
     */
    public function markAsFailed(string $errorMessage, array $output = []): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'duration_seconds' => $this->started_at ? now()->diffInSeconds($this->started_at) : 0,
            'error_message' => $errorMessage,
            'output' => $output,
        ]);
    }

    /**
     * Retry the step
     */
    public function retry(): bool
    {
        if ($this->retry_count >= $this->max_retries) {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'retry_count' => $this->retry_count + 1,
            'started_at' => now(),
            'completed_at' => null,
            'error_message' => null,
        ]);

        return true;
    }
}
