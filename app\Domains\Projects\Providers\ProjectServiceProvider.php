<?php

namespace App\Domains\Projects\Providers;

use Illuminate\Support\ServiceProvider;
use App\Domains\Projects\Services\AdvancedProjectManagementService;
use App\Domains\Projects\Services\AdvancedTimeTrackingService;
use App\Domains\Projects\Services\ProjectAnalyticsService;
use App\Domains\Projects\Services\WorkflowApprovalService;
use App\Domains\Projects\Services\ExternalIntegrationService;
use App\Domains\Projects\Services\RiskManagementService;
use App\Domains\Projects\Services\TeamEngagementService;
use App\Domains\Projects\Services\RemoteWorkService;
use App\Domains\Projects\Services\ModuleIntegrationService;
use App\Domains\Projects\Services\ProjectTemplateService;
use App\Domains\Projects\Services\AdvancedNotificationService;
use App\Domains\Projects\Services\FileCollaborationService;

/**
 * مزود خدمات المشاريع - Project Service Provider
 */
class ProjectServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل الخدمات الأساسية
        $this->app->singleton(AdvancedProjectManagementService::class);
        $this->app->singleton(AdvancedTimeTrackingService::class);
        $this->app->singleton(ProjectAnalyticsService::class);
        $this->app->singleton(WorkflowApprovalService::class);
        $this->app->singleton(ExternalIntegrationService::class);
        $this->app->singleton(RiskManagementService::class);
        $this->app->singleton(TeamEngagementService::class);
        $this->app->singleton(RemoteWorkService::class);
        $this->app->singleton(ModuleIntegrationService::class);
        $this->app->singleton(ProjectTemplateService::class);
        $this->app->singleton(AdvancedNotificationService::class);
        $this->app->singleton(FileCollaborationService::class);

        // تسجيل الخدمات مع الواجهات
        $this->app->bind(
            'App\Domains\Projects\Contracts\ProjectManagementInterface',
            AdvancedProjectManagementService::class
        );

        $this->app->bind(
            'App\Domains\Projects\Contracts\TimeTrackingInterface',
            AdvancedTimeTrackingService::class
        );

        $this->app->bind(
            'App\Domains\Projects\Contracts\AnalyticsInterface',
            ProjectAnalyticsService::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل التكوينات
        $this->mergeConfigFrom(
            __DIR__ . '/../../../../config/projects.php',
            'projects'
        );

        // تحميل الطرق
        $this->loadRoutesFrom(__DIR__ . '/../../../../routes/projects.php');

        // تحميل الترجمات
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'projects');

        // تحميل العروض
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'projects');

        // تحميل الهجرات
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        // نشر الملفات
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../../../config/projects.php' => config_path('projects.php'),
            ], 'projects-config');

            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/projects'),
            ], 'projects-views');

            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/projects'),
            ], 'projects-lang');

            $this->publishes([
                __DIR__ . '/../Resources/assets' => public_path('vendor/projects'),
            ], 'projects-assets');
        }

        // تسجيل الأوامر
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Domains\Projects\Console\Commands\GenerateProjectReports::class,
                \App\Domains\Projects\Console\Commands\SyncProjectIntegrations::class,
                \App\Domains\Projects\Console\Commands\CleanupOldProjects::class,
                \App\Domains\Projects\Console\Commands\SendProjectReminders::class,
            ]);
        }

        // تسجيل المراقبين (Observers)
        $this->registerObservers();

        // تسجيل السياسات (Policies)
        $this->registerPolicies();

        // تسجيل المستمعين (Event Listeners)
        $this->registerEventListeners();
    }

    /**
     * تسجيل المراقبين
     */
    protected function registerObservers(): void
    {
        \App\Domains\Projects\Models\Project::observe(\App\Domains\Projects\Observers\ProjectObserver::class);
        \App\Domains\Projects\Models\Task::observe(\App\Domains\Projects\Observers\TaskObserver::class);
        \App\Domains\Projects\Models\TimeEntry::observe(\App\Domains\Projects\Observers\TimeEntryObserver::class);
    }

    /**
     * تسجيل السياسات
     */
    protected function registerPolicies(): void
    {
        $gate = $this->app->make(\Illuminate\Contracts\Auth\Access\Gate::class);

        $gate->policy(\App\Domains\Projects\Models\Project::class, \App\Domains\Projects\Policies\ProjectPolicy::class);
        $gate->policy(\App\Domains\Projects\Models\Task::class, \App\Domains\Projects\Policies\TaskPolicy::class);
        $gate->policy(\App\Domains\Projects\Models\TimeEntry::class, \App\Domains\Projects\Policies\TimeEntryPolicy::class);
    }

    /**
     * تسجيل مستمعي الأحداث
     */
    protected function registerEventListeners(): void
    {
        $events = $this->app->make('events');

        // أحداث المشروع
        $events->listen(
            \App\Events\ProjectAccountingIntegrationCompleted::class,
            \App\Domains\Projects\Listeners\HandleAccountingIntegration::class
        );

        $events->listen(
            \App\Events\ProjectHRIntegrationCompleted::class,
            \App\Domains\Projects\Listeners\HandleHRIntegration::class
        );

        // أحداث المهام
        $events->listen(
            'App\Domains\Projects\Events\TaskCreated',
            'App\Domains\Projects\Listeners\SendTaskNotification'
        );

        $events->listen(
            'App\Domains\Projects\Events\TaskCompleted',
            'App\Domains\Projects\Listeners\UpdateProjectProgress'
        );

        // أحداث تتبع الوقت
        $events->listen(
            'App\Domains\Projects\Events\TimeEntryCreated',
            'App\Domains\Projects\Listeners\UpdateProjectCosts'
        );
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            AdvancedProjectManagementService::class,
            AdvancedTimeTrackingService::class,
            ProjectAnalyticsService::class,
            WorkflowApprovalService::class,
            ExternalIntegrationService::class,
            RiskManagementService::class,
            TeamEngagementService::class,
            RemoteWorkService::class,
            ModuleIntegrationService::class,
            ProjectTemplateService::class,
            AdvancedNotificationService::class,
            FileCollaborationService::class,
        ];
    }
}
