<?php

namespace App\Domains\Integration\Services\Transformation;

use App\Domains\Integration\Models\ApiEndpoint;
use App\Domains\Integration\Contracts\TransformationInterface;
use App\Domains\Integration\Services\Transformation\Processors\JsonProcessor;
use App\Domains\Integration\Services\Transformation\Processors\XmlProcessor;
use App\Domains\Integration\Services\Transformation\Processors\CsvProcessor;
use App\Domains\Integration\Services\Transformation\Processors\ProtobufProcessor;
use App\Domains\Integration\Services\Transformation\Validators\JsonSchemaValidator;
use App\Domains\Integration\Services\Transformation\Validators\XmlSchemaValidator;
use App\Domains\Integration\Services\Transformation\Enrichers\DataEnricher;
use App\Domains\Integration\Services\Transformation\Sanitizers\DataSanitizer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Advanced Data Transformation Pipeline
 *
 * Features:
 * - Multi-format data processing (JSON, XML, CSV, Protobuf, Avro)
 * - Schema validation and enforcement
 * - Data enrichment and augmentation
 * - Field mapping and transformation
 * - Data sanitization and normalization
 * - Conditional transformations
 * - Batch processing capabilities
 * - Real-time streaming transformations
 * - Error handling and recovery
 * - Performance optimization with caching
 * - Custom transformation plugins
 * - Data lineage tracking
 */
class DataTransformationPipeline implements TransformationInterface
{
    protected array $processors;
    protected array $validators;
    protected DataEnricher $enricher;
    protected DataSanitizer $sanitizer;
    protected array $config;
    protected array $transformationCache;

    public function __construct(
        JsonProcessor $jsonProcessor,
        XmlProcessor $xmlProcessor,
        CsvProcessor $csvProcessor,
        ProtobufProcessor $protobufProcessor,
        JsonSchemaValidator $jsonValidator,
        XmlSchemaValidator $xmlValidator,
        DataEnricher $enricher,
        DataSanitizer $sanitizer
    ) {
        $this->processors = [
            'json' => $jsonProcessor,
            'xml' => $xmlProcessor,
            'csv' => $csvProcessor,
            'protobuf' => $protobufProcessor,
        ];

        $this->validators = [
            'json' => $jsonValidator,
            'xml' => $xmlValidator,
        ];

        $this->enricher = $enricher;
        $this->sanitizer = $sanitizer;
        $this->config = config('integration.transformation', []);
        $this->transformationCache = [];
    }

    /**
     * Transform request data through the pipeline
     */
    public function transformRequest(array $data, ApiEndpoint $endpoint, array $context = []): array
    {
        $transformationConfig = $endpoint->transformation_rules['request'] ?? [];

        if (empty($transformationConfig)) {
            return $data;
        }

        $pipeline = $this->buildPipeline($transformationConfig, 'request');

        return $this->executePipeline($data, $pipeline, $endpoint, $context);
    }

    /**
     * Transform response data through the pipeline
     */
    public function transformResponse(array $data, ApiEndpoint $endpoint, array $context = []): array
    {
        $transformationConfig = $endpoint->transformation_rules['response'] ?? [];

        if (empty($transformationConfig)) {
            return $data;
        }

        $pipeline = $this->buildPipeline($transformationConfig, 'response');

        return $this->executePipeline($data, $pipeline, $endpoint, $context);
    }

    /**
     * Build transformation pipeline from configuration
     */
    protected function buildPipeline(array $config, string $direction): array
    {
        $pipeline = [];

        foreach ($config['steps'] ?? [] as $step) {
            $processor = $this->createProcessor($step);
            if ($processor) {
                $pipeline[] = $processor;
            }
        }

        return $pipeline;
    }

    /**
     * Execute the transformation pipeline
     */
    protected function executePipeline(array $data, array $pipeline, ApiEndpoint $endpoint, array $context): array
    {
        $transformedData = $data;
        $lineage = [];

        foreach ($pipeline as $step) {
            try {
                $stepStartTime = microtime(true);

                // Check cache for this transformation step
                $cacheKey = $this->generateCacheKey($transformedData, $step, $endpoint);

                if ($this->config['enable_caching'] ?? true) {
                    $cachedResult = Cache::get($cacheKey);
                    if ($cachedResult !== null) {
                        $transformedData = $cachedResult;
                        $lineage[] = [
                            'step' => $step['type'],
                            'cached' => true,
                            'execution_time' => 0,
                        ];
                        continue;
                    }
                }

                // Execute transformation step
                $transformedData = $this->executeStep($transformedData, $step, $endpoint, $context);

                $executionTime = microtime(true) - $stepStartTime;

                // Cache the result
                if ($this->config['enable_caching'] ?? true) {
                    $ttl = $step['cache_ttl'] ?? $this->config['default_cache_ttl'] ?? 300;
                    Cache::put($cacheKey, $transformedData, $ttl);
                }

                // Track lineage
                $lineage[] = [
                    'step' => $step['type'],
                    'cached' => false,
                    'execution_time' => $executionTime,
                    'input_size' => $this->calculateDataSize($data),
                    'output_size' => $this->calculateDataSize($transformedData),
                ];

            } catch (\Exception $e) {
                Log::error('Transformation step failed', [
                    'step' => $step,
                    'endpoint_id' => $endpoint->id,
                    'error' => $e->getMessage(),
                ]);

                // Handle error based on configuration
                if ($step['on_error'] ?? 'fail' === 'continue') {
                    continue;
                } else {
                    throw new \Exception("Transformation failed at step {$step['type']}: {$e->getMessage()}");
                }
            }
        }

        // Store transformation lineage
        $this->storeTransformationLineage($endpoint, $lineage, $context);

        return $transformedData;
    }

    /**
     * Execute a single transformation step
     */
    protected function executeStep(array $data, array $step, ApiEndpoint $endpoint, array $context): array
    {
        switch ($step['type']) {
            case 'validate':
                return $this->validateData($data, $step, $endpoint);

            case 'sanitize':
                return $this->sanitizer->sanitize($data, $step['config'] ?? []);

            case 'enrich':
                return $this->enricher->enrich($data, $step['config'] ?? [], $context);

            case 'map_fields':
                return $this->mapFields($data, $step['config'] ?? []);

            case 'filter':
                return $this->filterData($data, $step['config'] ?? []);

            case 'aggregate':
                return $this->aggregateData($data, $step['config'] ?? []);

            case 'format':
                return $this->formatData($data, $step['config'] ?? []);

            case 'conditional':
                return $this->conditionalTransform($data, $step['config'] ?? [], $endpoint, $context);

            case 'custom':
                return $this->executeCustomTransformation($data, $step['config'] ?? [], $endpoint, $context);

            default:
                throw new \Exception("Unknown transformation step type: {$step['type']}");
        }
    }

    /**
     * Validate data against schema
     */
    protected function validateData(array $data, array $step, ApiEndpoint $endpoint): array
    {
        $format = $step['config']['format'] ?? 'json';
        $schema = $step['config']['schema'] ?? null;

        if (!$schema) {
            return $data;
        }

        if (!isset($this->validators[$format])) {
            throw new \Exception("No validator available for format: {$format}");
        }

        $validator = $this->validators[$format];
        $isValid = $validator->validate($data, $schema);

        if (!$isValid) {
            $errors = $validator->getErrors();
            throw new \Exception("Data validation failed: " . implode(', ', $errors));
        }

        return $data;
    }

    /**
     * Map fields according to configuration
     */
    protected function mapFields(array $data, array $config): array
    {
        $mappings = $config['mappings'] ?? [];
        $mapped = [];

        foreach ($mappings as $sourceField => $targetField) {
            $value = $this->getNestedValue($data, $sourceField);
            if ($value !== null) {
                $this->setNestedValue($mapped, $targetField, $value);
            }
        }

        // Include unmapped fields if configured
        if ($config['include_unmapped'] ?? false) {
            $mappedSources = array_keys($mappings);
            foreach ($data as $key => $value) {
                if (!in_array($key, $mappedSources)) {
                    $mapped[$key] = $value;
                }
            }
        }

        return $mapped;
    }

    /**
     * Filter data based on conditions
     */
    protected function filterData(array $data, array $config): array
    {
        $conditions = $config['conditions'] ?? [];
        $mode = $config['mode'] ?? 'include'; // include or exclude

        if (empty($conditions)) {
            return $data;
        }

        $filtered = [];

        foreach ($data as $key => $value) {
            $shouldInclude = $this->evaluateConditions($key, $value, $conditions);

            if (($mode === 'include' && $shouldInclude) || ($mode === 'exclude' && !$shouldInclude)) {
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }

    /**
     * Aggregate data
     */
    protected function aggregateData(array $data, array $config): array
    {
        $operations = $config['operations'] ?? [];
        $groupBy = $config['group_by'] ?? null;

        if ($groupBy) {
            return $this->groupAndAggregate($data, $groupBy, $operations);
        }

        return $this->simpleAggregate($data, $operations);
    }

    /**
     * Format data according to specifications
     */
    protected function formatData(array $data, array $config): array
    {
        $formatters = $config['formatters'] ?? [];
        $formatted = $data;

        foreach ($formatters as $field => $formatter) {
            $value = $this->getNestedValue($formatted, $field);
            if ($value !== null) {
                $formattedValue = $this->applyFormatter($value, $formatter);
                $this->setNestedValue($formatted, $field, $formattedValue);
            }
        }

        return $formatted;
    }

    /**
     * Conditional transformation
     */
    protected function conditionalTransform(array $data, array $config, ApiEndpoint $endpoint, array $context): array
    {
        $conditions = $config['conditions'] ?? [];
        $transformations = $config['transformations'] ?? [];

        foreach ($conditions as $condition) {
            if ($this->evaluateCondition($data, $condition, $context)) {
                $transformationKey = $condition['then'] ?? 'default';
                if (isset($transformations[$transformationKey])) {
                    return $this->executeStep($data, $transformations[$transformationKey], $endpoint, $context);
                }
            }
        }

        // Default transformation
        if (isset($transformations['default'])) {
            return $this->executeStep($data, $transformations['default'], $endpoint, $context);
        }

        return $data;
    }

    /**
     * Execute custom transformation
     */
    protected function executeCustomTransformation(array $data, array $config, ApiEndpoint $endpoint, array $context): array
    {
        $className = $config['class'] ?? null;
        $method = $config['method'] ?? 'transform';

        if (!$className) {
            throw new \Exception('Custom transformation class not specified');
        }

        if (!class_exists($className)) {
            throw new \Exception("Custom transformation class not found: {$className}");
        }

        $transformer = new $className();

        if (!method_exists($transformer, $method)) {
            throw new \Exception("Method {$method} not found in {$className}");
        }

        return $transformer->$method($data, $config, $endpoint, $context);
    }

    /**
     * Create processor for transformation step
     */
    protected function createProcessor(array $step): ?array
    {
        // Validate step configuration
        if (!isset($step['type'])) {
            Log::warning('Transformation step missing type', $step);
            return null;
        }

        return $step;
    }

    /**
     * Generate cache key for transformation step
     */
    protected function generateCacheKey(array $data, array $step, ApiEndpoint $endpoint): string
    {
        $keyData = [
            'endpoint_id' => $endpoint->id,
            'step' => $step,
            'data_hash' => md5(serialize($data)),
        ];

        return 'transformation:' . md5(serialize($keyData));
    }

    /**
     * Calculate data size for metrics
     */
    protected function calculateDataSize(array $data): int
    {
        return strlen(serialize($data));
    }

    /**
     * Store transformation lineage for auditing
     */
    protected function storeTransformationLineage(ApiEndpoint $endpoint, array $lineage, array $context): void
    {
        if (!($this->config['track_lineage'] ?? false)) {
            return;
        }

        $lineageData = [
            'endpoint_id' => $endpoint->id,
            'request_id' => $context['request_id'] ?? uniqid(),
            'lineage' => $lineage,
            'timestamp' => now(),
            'total_execution_time' => array_sum(array_column($lineage, 'execution_time')),
        ];

        // Store in cache or database
        $requestId = $context['request_id'] ?? uniqid();
        Cache::put(
            "lineage:{$requestId}",
            $lineageData,
            $this->config['lineage_retention_hours'] ?? 24
        );
    }

    // Helper methods
    protected function getNestedValue(array $data, string $path)
    {
        $keys = explode('.', $path);
        $value = $data;

        foreach ($keys as $key) {
            if (!is_array($value) || !isset($value[$key])) {
                return null;
            }
            $value = $value[$key];
        }

        return $value;
    }

    protected function setNestedValue(array &$data, string $path, $value): void
    {
        $keys = explode('.', $path);
        $current = &$data;

        foreach ($keys as $i => $key) {
            if ($i === count($keys) - 1) {
                $current[$key] = $value;
            } else {
                if (!isset($current[$key]) || !is_array($current[$key])) {
                    $current[$key] = [];
                }
                $current = &$current[$key];
            }
        }
    }

    protected function evaluateConditions(string $key, $value, array $conditions): bool
    {
        foreach ($conditions as $condition) {
            if (!$this->evaluateSingleCondition($key, $value, $condition)) {
                return false;
            }
        }
        return true;
    }

    protected function evaluateSingleCondition(string $key, $value, array $condition): bool
    {
        $operator = $condition['operator'] ?? '=';
        $expected = $condition['value'] ?? null;

        switch ($operator) {
            case '=':
            case '==':
                return $value == $expected;
            case '!=':
                return $value != $expected;
            case '>':
                return $value > $expected;
            case '<':
                return $value < $expected;
            case '>=':
                return $value >= $expected;
            case '<=':
                return $value <= $expected;
            case 'in':
                return in_array($value, (array) $expected);
            case 'not_in':
                return !in_array($value, (array) $expected);
            case 'regex':
                return preg_match($expected, (string) $value);
            default:
                return false;
        }
    }

    protected function evaluateCondition(array $data, array $condition, array $context): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field) {
            return false;
        }

        $fieldValue = $this->getNestedValue($data, $field);

        return $this->evaluateSingleCondition($field, $fieldValue, [
            'operator' => $operator,
            'value' => $value,
        ]);
    }

    protected function groupAndAggregate(array $data, string $groupBy, array $operations): array
    {
        $grouped = [];

        foreach ($data as $item) {
            $groupValue = $this->getNestedValue($item, $groupBy);
            $groupKey = is_scalar($groupValue) ? (string) $groupValue : 'null';

            if (!isset($grouped[$groupKey])) {
                $grouped[$groupKey] = [];
            }

            $grouped[$groupKey][] = $item;
        }

        $result = [];
        foreach ($grouped as $groupKey => $items) {
            $result[$groupKey] = $this->simpleAggregate($items, $operations);
        }

        return $result;
    }

    protected function simpleAggregate(array $data, array $operations): array
    {
        $result = [];

        foreach ($operations as $field => $operation) {
            $values = array_map(fn($item) => $this->getNestedValue($item, $field), $data);
            $values = array_filter($values, fn($v) => $v !== null);

            switch ($operation) {
                case 'sum':
                    $result[$field] = array_sum($values);
                    break;
                case 'avg':
                    $result[$field] = count($values) > 0 ? array_sum($values) / count($values) : 0;
                    break;
                case 'min':
                    $result[$field] = count($values) > 0 ? min($values) : null;
                    break;
                case 'max':
                    $result[$field] = count($values) > 0 ? max($values) : null;
                    break;
                case 'count':
                    $result[$field] = count($values);
                    break;
                default:
                    $result[$field] = $values;
            }
        }

        return $result;
    }

    protected function applyFormatter($value, array $formatter)
    {
        $type = $formatter['type'] ?? 'string';
        $format = $formatter['format'] ?? null;

        switch ($type) {
            case 'date':
                if ($format) {
                    return Carbon::parse($value)->format($format);
                }
                return Carbon::parse($value)->toISOString();

            case 'number':
                $decimals = $formatter['decimals'] ?? 2;
                return number_format((float) $value, $decimals);

            case 'currency':
                $currency = $formatter['currency'] ?? 'USD';
                return $currency . ' ' . number_format((float) $value, 2);

            case 'string':
                if ($format === 'upper') {
                    return strtoupper((string) $value);
                } elseif ($format === 'lower') {
                    return strtolower((string) $value);
                } elseif ($format === 'title') {
                    return ucwords((string) $value);
                }
                return (string) $value;

            default:
                return $value;
        }
    }
}
