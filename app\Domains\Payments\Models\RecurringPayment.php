<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * نموذج الدفعات المتكررة
 * يدير الاشتراكات والدفعات الدورية
 */
class RecurringPayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'subscription_id',
        'user_id',
        'payment_method_id',
        'subscribable_type',
        'subscribable_id',
        'name',
        'description',
        'amount',
        'currency',
        'interval',
        'interval_count',
        'trial_days',
        'trial_end_date',
        'status',
        'started_at',
        'next_payment_date',
        'last_payment_date',
        'ended_at',
        'failure_count',
        'max_failures',
        'dunning_config',
        'metadata',
        'total_cycles',
        'completed_cycles',
        'remaining_cycles',
        'setup_fee',
        'cancellation_reason',
        'cancelled_at',
        'cancelled_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'setup_fee' => 'decimal:2',
        'trial_end_date' => 'datetime',
        'started_at' => 'datetime',
        'next_payment_date' => 'datetime',
        'last_payment_date' => 'datetime',
        'ended_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'dunning_config' => 'array',
        'metadata' => 'array',
        'trial_days' => 'integer',
        'failure_count' => 'integer',
        'max_failures' => 'integer',
        'total_cycles' => 'integer',
        'completed_cycles' => 'integer',
        'remaining_cycles' => 'integer',
        'cancelled_by' => 'integer',
    ];

    /**
     * فترات التكرار
     */
    const INTERVALS = [
        'day' => 'يومي',
        'week' => 'أسبوعي',
        'month' => 'شهري',
        'quarter' => 'ربع سنوي',
        'year' => 'سنوي',
    ];

    /**
     * حالات الاشتراك
     */
    const STATUSES = [
        'pending' => 'في الانتظار',
        'trial' => 'فترة تجريبية',
        'active' => 'نشط',
        'past_due' => 'متأخر الدفع',
        'cancelled' => 'ملغي',
        'expired' => 'منتهي',
        'paused' => 'متوقف مؤقتاً',
        'failed' => 'فشل',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            if (empty($subscription->subscription_id)) {
                $subscription->subscription_id = static::generateSubscriptionId();
            }
        });
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع طريقة الدفع
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    /**
     * العلاقة المتعددة الأشكال مع الكيان القابل للاشتراك
     */
    public function subscribable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع معاملات الاشتراك
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'recurring_payment_id');
    }

    /**
     * العلاقة مع محاولات الدفع
     */
    public function paymentAttempts(): HasMany
    {
        return $this->hasMany(RecurringPaymentAttempt::class, 'recurring_payment_id');
    }

    /**
     * توليد معرف الاشتراك
     */
    public static function generateSubscriptionId(): string
    {
        return 'SUB_' . strtoupper(Str::random(12)) . '_' . time();
    }

    /**
     * التحقق من كون الاشتراك في فترة تجريبية
     */
    public function isInTrial(): bool
    {
        return $this->status === 'trial' && 
               $this->trial_end_date && 
               $this->trial_end_date->isFuture();
    }

    /**
     * التحقق من انتهاء الفترة التجريبية
     */
    public function isTrialExpired(): bool
    {
        return $this->trial_end_date && 
               $this->trial_end_date->isPast() && 
               $this->status === 'trial';
    }

    /**
     * التحقق من نشاط الاشتراك
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['active', 'trial']);
    }

    /**
     * التحقق من إلغاء الاشتراك
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * التحقق من تأخر الدفع
     */
    public function isPastDue(): bool
    {
        return $this->status === 'past_due';
    }

    /**
     * التحقق من استحقاق الدفع
     */
    public function isDue(): bool
    {
        return $this->next_payment_date && 
               $this->next_payment_date->isPast() && 
               $this->isActive();
    }

    /**
     * حساب تاريخ الدفع التالي
     */
    public function calculateNextPaymentDate(?Carbon $fromDate = null): Carbon
    {
        $fromDate = $fromDate ?? $this->last_payment_date ?? $this->started_at ?? now();

        return match ($this->interval) {
            'day' => $fromDate->copy()->addDays($this->interval_count),
            'week' => $fromDate->copy()->addWeeks($this->interval_count),
            'month' => $fromDate->copy()->addMonths($this->interval_count),
            'quarter' => $fromDate->copy()->addMonths($this->interval_count * 3),
            'year' => $fromDate->copy()->addYears($this->interval_count),
            default => $fromDate->copy()->addMonth(),
        };
    }

    /**
     * بدء الاشتراك
     */
    public function start(): void
    {
        $this->update([
            'status' => $this->trial_days > 0 ? 'trial' : 'active',
            'started_at' => now(),
            'next_payment_date' => $this->trial_days > 0 
                ? now()->addDays($this->trial_days)
                : $this->calculateNextPaymentDate(now()),
            'trial_end_date' => $this->trial_days > 0 
                ? now()->addDays($this->trial_days)
                : null,
        ]);
    }

    /**
     * إلغاء الاشتراك
     */
    public function cancel(string $reason = null, ?int $cancelledBy = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => $cancelledBy,
            'cancellation_reason' => $reason,
            'ended_at' => now(),
        ]);
    }

    /**
     * إيقاف الاشتراك مؤقتاً
     */
    public function pause(): void
    {
        $this->update([
            'status' => 'paused',
            'metadata' => array_merge($this->metadata ?? [], [
                'paused_at' => now(),
                'previous_status' => $this->status,
            ]),
        ]);
    }

    /**
     * استئناف الاشتراك
     */
    public function resume(): void
    {
        $previousStatus = $this->metadata['previous_status'] ?? 'active';
        
        $this->update([
            'status' => $previousStatus,
            'next_payment_date' => $this->calculateNextPaymentDate(),
            'metadata' => array_merge($this->metadata ?? [], [
                'resumed_at' => now(),
            ]),
        ]);
    }

    /**
     * معالجة دفعة الاشتراك
     */
    public function processPayment(): array
    {
        try {
            // إنشاء محاولة دفع
            $attempt = $this->paymentAttempts()->create([
                'attempt_id' => 'ATT_' . strtoupper(Str::random(10)),
                'amount' => $this->amount,
                'currency' => $this->currency,
                'status' => 'processing',
                'attempted_at' => now(),
            ]);

            // معالجة الدفع عبر طريقة الدفع المحفوظة
            $paymentService = app(\App\Domains\Payments\Services\PaymentService::class);
            
            $paymentData = [
                'amount' => $this->amount,
                'currency' => $this->currency,
                'payment_method_id' => $this->payment_method_id,
                'customer_email' => $this->user->email,
                'description' => "دفعة اشتراك - {$this->name}",
                'metadata' => [
                    'subscription_id' => $this->subscription_id,
                    'cycle_number' => $this->completed_cycles + 1,
                    'attempt_id' => $attempt->attempt_id,
                ],
            ];

            $transaction = $paymentService->processRecurringPayment($this->paymentMethod, $paymentData);

            if ($transaction->isSuccessful()) {
                // نجح الدفع
                $this->handleSuccessfulPayment($transaction, $attempt);
                
                return [
                    'success' => true,
                    'transaction' => $transaction,
                    'attempt' => $attempt,
                ];
            } else {
                // فشل الدفع
                $this->handleFailedPayment($transaction, $attempt);
                
                return [
                    'success' => false,
                    'transaction' => $transaction,
                    'attempt' => $attempt,
                    'error' => $transaction->failure_reason,
                ];
            }

        } catch (\Exception $e) {
            // خطأ في المعالجة
            if (isset($attempt)) {
                $attempt->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                ]);
            }

            $this->increment('failure_count');
            $this->checkMaxFailures();

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'attempt' => $attempt ?? null,
            ];
        }
    }

    /**
     * معالجة الدفع الناجح
     */
    protected function handleSuccessfulPayment(PaymentTransaction $transaction, RecurringPaymentAttempt $attempt): void
    {
        // تحديث الاشتراك
        $this->update([
            'status' => 'active',
            'last_payment_date' => now(),
            'next_payment_date' => $this->calculateNextPaymentDate(),
            'completed_cycles' => $this->completed_cycles + 1,
            'remaining_cycles' => $this->total_cycles ? $this->total_cycles - ($this->completed_cycles + 1) : null,
            'failure_count' => 0, // إعادة تعيين عداد الفشل
        ]);

        // تحديث محاولة الدفع
        $attempt->update([
            'status' => 'successful',
            'transaction_id' => $transaction->id,
            'processed_at' => now(),
        ]);

        // التحقق من انتهاء الاشتراك
        if ($this->total_cycles && $this->completed_cycles >= $this->total_cycles) {
            $this->update([
                'status' => 'expired',
                'ended_at' => now(),
            ]);
        }

        // إطلاق الأحداث
        event(new \App\Domains\Payments\Events\RecurringPaymentSuccessful($this, $transaction));
    }

    /**
     * معالجة الدفع الفاشل
     */
    protected function handleFailedPayment(PaymentTransaction $transaction, RecurringPaymentAttempt $attempt): void
    {
        // تحديث محاولة الدفع
        $attempt->update([
            'status' => 'failed',
            'transaction_id' => $transaction->id,
            'error_message' => $transaction->failure_reason,
            'processed_at' => now(),
        ]);

        // زيادة عداد الفشل
        $this->increment('failure_count');

        // التحقق من الحد الأقصى للفشل
        $this->checkMaxFailures();

        // تحديث حالة الاشتراك
        if ($this->failure_count < $this->max_failures) {
            $this->update(['status' => 'past_due']);
            
            // جدولة إعادة المحاولة
            $this->scheduleRetry();
        }

        // إطلاق الأحداث
        event(new \App\Domains\Payments\Events\RecurringPaymentFailed($this, $transaction));
    }

    /**
     * التحقق من الحد الأقصى للفشل
     */
    protected function checkMaxFailures(): void
    {
        if ($this->failure_count >= $this->max_failures) {
            $this->update([
                'status' => 'failed',
                'ended_at' => now(),
            ]);

            event(new \App\Domains\Payments\Events\RecurringPaymentMaxFailuresReached($this));
        }
    }

    /**
     * جدولة إعادة المحاولة
     */
    protected function scheduleRetry(): void
    {
        $dunningConfig = $this->dunning_config ?? [];
        $retryDays = $dunningConfig['retry_days'] ?? [1, 3, 7];
        
        if (isset($retryDays[$this->failure_count - 1])) {
            $retryDate = now()->addDays($retryDays[$this->failure_count - 1]);
            
            $this->update(['next_payment_date' => $retryDate]);
            
            // جدولة مهمة إعادة المحاولة
            \App\Domains\Payments\Jobs\ProcessRecurringPaymentJob::dispatch($this)
                ->delay($retryDate);
        }
    }

    /**
     * تحديث طريقة الدفع
     */
    public function updatePaymentMethod(PaymentMethod $paymentMethod): void
    {
        $this->update(['payment_method_id' => $paymentMethod->id]);
    }

    /**
     * تحديث المبلغ
     */
    public function updateAmount(float $newAmount): void
    {
        $this->update(['amount' => $newAmount]);
    }

    /**
     * الحصول على إجمالي المبلغ المدفوع
     */
    public function getTotalPaidAmount(): float
    {
        return $this->transactions()
            ->where('status', 'succeeded')
            ->sum('amount');
    }

    /**
     * الحصول على عدد الدفعات الناجحة
     */
    public function getSuccessfulPaymentsCount(): int
    {
        return $this->transactions()
            ->where('status', 'succeeded')
            ->count();
    }

    /**
     * الحصول على آخر دفعة ناجحة
     */
    public function getLastSuccessfulPayment(): ?PaymentTransaction
    {
        return $this->transactions()
            ->where('status', 'succeeded')
            ->latest()
            ->first();
    }

    /**
     * Scope للاشتراكات النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['active', 'trial']);
    }

    /**
     * Scope للاشتراكات المستحقة
     */
    public function scopeDue($query)
    {
        return $query->active()
            ->where('next_payment_date', '<=', now());
    }

    /**
     * Scope للاشتراكات المتأخرة
     */
    public function scopePastDue($query)
    {
        return $query->where('status', 'past_due');
    }

    /**
     * Scope للاشتراكات في الفترة التجريبية
     */
    public function scopeInTrial($query)
    {
        return $query->where('status', 'trial')
            ->where('trial_end_date', '>', now());
    }

    /**
     * Scope للاشتراكات منتهية الفترة التجريبية
     */
    public function scopeTrialExpired($query)
    {
        return $query->where('status', 'trial')
            ->where('trial_end_date', '<=', now());
    }
}
