<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء Webhook للتجارة الإلكترونية
 */
class ECommerceWebhookException extends ECommerceException
{
    protected string $webhookId = '';
    protected string $eventType = '';
    protected array $payload = [];
    protected array $headers = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $webhookId = '',
        string $eventType = '',
        array $payload = [],
        array $headers = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->webhookId = $webhookId;
        $this->eventType = $eventType;
        $this->payload = $payload;
        $this->headers = $headers;
        $this->errorCode = 'WEBHOOK_ERROR';
    }

    /**
     * الحصول على معرف Webhook
     */
    public function getWebhookId(): string
    {
        return $this->webhookId;
    }

    /**
     * الحصول على نوع الحدث
     */
    public function getEventType(): string
    {
        return $this->eventType;
    }

    /**
     * الحصول على البيانات المرسلة
     */
    public function getPayload(): array
    {
        return $this->payload;
    }

    /**
     * الحصول على Headers
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتوقيع
     */
    public function isSignatureError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'signature') ||
               str_contains(strtolower($this->getMessage()), 'verification');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتحليل
     */
    public function isParsingError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'parsing') ||
               str_contains(strtolower($this->getMessage()), 'json') ||
               str_contains(strtolower($this->getMessage()), 'format');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالمعالجة
     */
    public function isProcessingError(): bool
    {
        return !$this->isSignatureError() && !$this->isParsingError();
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'webhook_id' => $this->getWebhookId(),
            'event_type' => $this->getEventType(),
            'payload_size' => strlen(json_encode($this->getPayload())),
            'headers_count' => count($this->getHeaders()),
            'is_signature_error' => $this->isSignatureError(),
            'is_parsing_error' => $this->isParsingError(),
            'is_processing_error' => $this->isProcessingError(),
        ]);
    }
}
