<!-- Revenue Chart Widget -->
<div class="widget bg-white rounded-xl shadow-lg p-6" 
     style="grid-column: span {{ $widget['size']['w'] }}; grid-row: span {{ $widget['size']['h'] }};"
     data-widget-type="revenue_chart">
     
    <!-- Widget Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <div class="bg-green-100 p-3 rounded-lg mr-3">
                <i class="fas fa-chart-area text-green-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-bold text-gray-800">تحليل الإيرادات</h3>
                <p class="text-sm text-gray-500">اتجاهات الإيرادات والتنبؤات</p>
            </div>
        </div>
        
        <div class="flex items-center space-x-2 space-x-reverse">
            <!-- Time Period Selector -->
            <select id="revenue-period" class="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-green-500">
                <option value="7d">آخر 7 أيام</option>
                <option value="30d" selected>آخر 30 يوم</option>
                <option value="90d">آخر 3 أشهر</option>
                <option value="1y">آخر سنة</option>
            </select>
            
            <button class="text-gray-400 hover:text-gray-600" onclick="refreshWidget('revenue_chart')">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="text-gray-400 hover:text-gray-600" onclick="toggleFullscreen('revenue_chart')">
                <i class="fas fa-expand"></i>
            </button>
            <button class="text-gray-400 hover:text-gray-600" onclick="removeWidget('revenue_chart')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Chart Container -->
    <div class="relative">
        <canvas id="revenueChart" class="w-full" style="height: 300px;"></canvas>
        
        <!-- Chart Loading Overlay -->
        <div id="chart-loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center hidden">
            <div class="text-center">
                <div class="spinner mx-auto mb-2"></div>
                <p class="text-sm text-gray-600">جاري تحميل البيانات...</p>
            </div>
        </div>
    </div>

    <!-- Chart Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        <div class="text-center p-3 bg-green-50 rounded-lg">
            <p class="text-2xl font-bold text-green-600">2.5M</p>
            <p class="text-sm text-gray-600">إجمالي الإيرادات</p>
            <div class="flex items-center justify-center mt-1">
                <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                <span class="text-green-500 text-xs">+12.5%</span>
            </div>
        </div>
        
        <div class="text-center p-3 bg-blue-50 rounded-lg">
            <p class="text-2xl font-bold text-blue-600">83K</p>
            <p class="text-sm text-gray-600">متوسط شهري</p>
            <div class="flex items-center justify-center mt-1">
                <i class="fas fa-arrow-up text-blue-500 text-xs mr-1"></i>
                <span class="text-blue-500 text-xs">+8.3%</span>
            </div>
        </div>
        
        <div class="text-center p-3 bg-purple-50 rounded-lg">
            <p class="text-2xl font-bold text-purple-600">125K</p>
            <p class="text-sm text-gray-600">أعلى شهر</p>
            <div class="flex items-center justify-center mt-1">
                <span class="text-purple-500 text-xs">أكتوبر 2024</span>
            </div>
        </div>
        
        <div class="text-center p-3 bg-orange-50 rounded-lg">
            <p class="text-2xl font-bold text-orange-600">95K</p>
            <p class="text-sm text-gray-600">التنبؤ القادم</p>
            <div class="flex items-center justify-center mt-1">
                <i class="fas fa-arrow-up text-orange-500 text-xs mr-1"></i>
                <span class="text-orange-500 text-xs">+14.5%</span>
            </div>
        </div>
    </div>

    <!-- Revenue Breakdown -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="font-semibold text-gray-800 mb-3">تفصيل الإيرادات حسب المصدر</h4>
        <div class="space-y-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                    <span class="text-sm text-gray-600">المشاريع الجديدة</span>
                </div>
                <div class="flex items-center">
                    <span class="text-sm font-semibold text-gray-800">1.2M ر.س</span>
                    <span class="text-xs text-gray-500 mr-2">(48%)</span>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span class="text-sm text-gray-600">العملاء المتكررون</span>
                </div>
                <div class="flex items-center">
                    <span class="text-sm font-semibold text-gray-800">850K ر.س</span>
                    <span class="text-xs text-gray-500 mr-2">(34%)</span>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                    <span class="text-sm text-gray-600">الخدمات الإضافية</span>
                </div>
                <div class="flex items-center">
                    <span class="text-sm font-semibold text-gray-800">450K ر.س</span>
                    <span class="text-xs text-gray-500 mr-2">(18%)</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-3 space-x-reverse">
        <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-300 text-sm">
            <i class="fas fa-download mr-2"></i>
            تصدير التقرير
        </button>
        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-sm">
            <i class="fas fa-chart-line mr-2"></i>
            تحليل متقدم
        </button>
        <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-300 text-sm">
            <i class="fas fa-cog mr-2"></i>
            إعدادات المخطط
        </button>
    </div>
</div>

<script>
let revenueChart;

document.addEventListener('DOMContentLoaded', function() {
    initRevenueChart();
    
    // مراقبة تغيير الفترة الزمنية
    document.getElementById('revenue-period').addEventListener('change', function() {
        updateRevenueChart(this.value);
    });
});

function initRevenueChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    // بيانات وهمية للمخطط
    const data = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [
            {
                label: 'الإيرادات الفعلية',
                data: [65000, 72000, 68000, 85000, 92000, 88000, 95000, 102000, 98000, 125000, 118000, 135000],
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'التنبؤات',
                data: [null, null, null, null, null, null, null, null, null, null, 120000, 140000],
                borderColor: 'rgb(168, 85, 247)',
                backgroundColor: 'rgba(168, 85, 247, 0.1)',
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            },
            {
                label: 'الهدف',
                data: [80000, 80000, 80000, 80000, 80000, 80000, 80000, 80000, 80000, 80000, 80000, 80000],
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'transparent',
                borderDash: [10, 5],
                fill: false,
                pointRadius: 0
            }
        ]
    };

    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + 
                                   new Intl.NumberFormat('ar-SA', {
                                       style: 'currency',
                                       currency: 'SAR'
                                   }).format(context.parsed.y);
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'الشهر',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'الإيرادات (ر.س)',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('ar-SA', {
                                style: 'currency',
                                currency: 'SAR',
                                minimumFractionDigits: 0
                            }).format(value);
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 8
                }
            }
        }
    };

    revenueChart = new Chart(ctx, config);
}

function updateRevenueChart(period) {
    // إظهار مؤشر التحميل
    document.getElementById('chart-loading').classList.remove('hidden');
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        // هنا يمكن تحديث البيانات حسب الفترة المختارة
        let newData;
        
        switch(period) {
            case '7d':
                newData = {
                    labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                    datasets: [{
                        label: 'الإيرادات اليومية',
                        data: [12000, 15000, 18000, 14000, 22000, 8000, 16000],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                };
                break;
            case '30d':
                // البيانات الافتراضية
                break;
            case '90d':
                newData = {
                    labels: ['الربع الأول', 'الربع الثاني', 'الربع الثالث'],
                    datasets: [{
                        label: 'الإيرادات الربعية',
                        data: [205000, 275000, 325000],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                };
                break;
            case '1y':
                // البيانات السنوية
                break;
        }
        
        if (newData) {
            revenueChart.data = newData;
            revenueChart.update();
        }
        
        document.getElementById('chart-loading').classList.add('hidden');
        showNotification('تم تحديث المخطط بنجاح', 'success');
    }, 1500);
}

function toggleFullscreen(widgetType) {
    const widget = document.querySelector(`[data-widget-type="${widgetType}"]`);
    
    if (widget.classList.contains('fullscreen')) {
        // الخروج من وضع الشاشة الكاملة
        widget.classList.remove('fullscreen');
        widget.style.position = '';
        widget.style.top = '';
        widget.style.left = '';
        widget.style.width = '';
        widget.style.height = '';
        widget.style.zIndex = '';
        widget.style.backgroundColor = '';
    } else {
        // الدخول في وضع الشاشة الكاملة
        widget.classList.add('fullscreen');
        widget.style.position = 'fixed';
        widget.style.top = '0';
        widget.style.left = '0';
        widget.style.width = '100vw';
        widget.style.height = '100vh';
        widget.style.zIndex = '9999';
        widget.style.backgroundColor = 'white';
    }
    
    // إعادة تحجيم المخطط
    setTimeout(() => {
        revenueChart.resize();
    }, 300);
}
</script>
