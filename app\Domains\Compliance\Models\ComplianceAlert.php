<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use App\Models\Company;

/**
 * نموذج تنبيهات الامتثال
 * يدير جميع التنبيهات والإشعارات المتعلقة بالامتثال
 */
class ComplianceAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'alert_id',
        'company_id',
        'user_id',
        'country_id',
        'compliance_rule_id',
        'compliance_activity_id',
        'related_model_type',
        'related_model_id',
        'alert_type',
        'severity',
        'title',
        'message',
        'description',
        'action_required',
        'due_date',
        'escalation_date',
        'status',
        'priority',
        'category',
        'source',
        'trigger_conditions',
        'alert_data',
        'notification_channels',
        'recipients',
        'sent_at',
        'acknowledged_at',
        'resolved_at',
        'dismissed_at',
        'escalated_at',
        'acknowledged_by',
        'resolved_by',
        'dismissed_by',
        'escalated_by',
        'resolution_notes',
        'auto_resolve',
        'repeat_interval',
        'last_repeated_at',
        'repeat_count',
        'metadata',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'escalation_date' => 'datetime',
        'trigger_conditions' => 'array',
        'alert_data' => 'array',
        'notification_channels' => 'array',
        'recipients' => 'array',
        'sent_at' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'dismissed_at' => 'datetime',
        'escalated_at' => 'datetime',
        'auto_resolve' => 'boolean',
        'last_repeated_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * أنواع التنبيهات
     */
    const ALERT_TYPES = [
        'deadline_approaching' => 'اقتراب موعد نهائي',
        'deadline_missed' => 'تجاوز موعد نهائي',
        'compliance_violation' => 'مخالفة امتثال',
        'regulatory_change' => 'تغيير تنظيمي',
        'system_error' => 'خطأ في النظام',
        'data_inconsistency' => 'عدم تطابق البيانات',
        'audit_required' => 'مراجعة مطلوبة',
        'license_expiry' => 'انتهاء ترخيص',
        'certificate_expiry' => 'انتهاء شهادة',
        'payment_due' => 'دفعة مستحقة',
        'submission_failed' => 'فشل في التقديم',
        'approval_pending' => 'موافقة معلقة',
        'threshold_exceeded' => 'تجاوز حد أقصى',
        'unusual_activity' => 'نشاط غير عادي',
        'integration_failure' => 'فشل في التكامل',
    ];

    /**
     * مستويات الخطورة
     */
    const SEVERITIES = [
        'info' => 'معلوماتي',
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
        'emergency' => 'طوارئ',
    ];

    /**
     * حالات التنبيه
     */
    const STATUSES = [
        'active' => 'نشط',
        'acknowledged' => 'مؤكد',
        'resolved' => 'محلول',
        'dismissed' => 'مرفوض',
        'escalated' => 'مصعد',
        'expired' => 'منتهي الصلاحية',
        'auto_resolved' => 'محلول تلقائياً',
    ];

    /**
     * مستويات الأولوية
     */
    const PRIORITIES = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'urgent' => 'عاجل',
        'critical' => 'حرج',
    ];

    /**
     * فئات التنبيهات
     */
    const CATEGORIES = [
        'tax' => 'ضرائب',
        'social_security' => 'ضمان اجتماعي',
        'e_invoicing' => 'فوترة إلكترونية',
        'accounting' => 'محاسبة',
        'regulatory' => 'تنظيمي',
        'system' => 'نظام',
        'security' => 'أمان',
        'performance' => 'أداء',
        'integration' => 'تكامل',
        'audit' => 'مراجعة',
    ];

    /**
     * مصادر التنبيه
     */
    const SOURCES = [
        'system' => 'النظام',
        'user' => 'المستخدم',
        'api' => 'واجهة برمجية',
        'scheduler' => 'مجدول',
        'webhook' => 'ويب هوك',
        'external' => 'خارجي',
        'ai' => 'ذكاء اصطناعي',
    ];

    /**
     * قنوات الإشعار
     */
    const NOTIFICATION_CHANNELS = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسالة نصية',
        'push' => 'إشعار فوري',
        'slack' => 'سلاك',
        'teams' => 'تيمز',
        'webhook' => 'ويب هوك',
        'dashboard' => 'لوحة التحكم',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع قاعدة الامتثال
     */
    public function complianceRule(): BelongsTo
    {
        return $this->belongsTo(ComplianceRule::class);
    }

    /**
     * العلاقة مع نشاط الامتثال
     */
    public function complianceActivity(): BelongsTo
    {
        return $this->belongsTo(ComplianceActivity::class);
    }

    /**
     * العلاقة مع النموذج المرتبط (polymorphic)
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * إنشاء تنبيه جديد
     */
    public static function createAlert(array $alertData): self
    {
        $alert = self::create([
            'alert_id' => self::generateAlertId(),
            'company_id' => $alertData['company_id'],
            'user_id' => $alertData['user_id'] ?? auth()->id(),
            'country_id' => $alertData['country_id'] ?? null,
            'compliance_rule_id' => $alertData['compliance_rule_id'] ?? null,
            'compliance_activity_id' => $alertData['compliance_activity_id'] ?? null,
            'related_model_type' => $alertData['related_model_type'] ?? null,
            'related_model_id' => $alertData['related_model_id'] ?? null,
            'alert_type' => $alertData['alert_type'],
            'severity' => $alertData['severity'] ?? 'medium',
            'title' => $alertData['title'],
            'message' => $alertData['message'],
            'description' => $alertData['description'] ?? null,
            'action_required' => $alertData['action_required'] ?? null,
            'due_date' => $alertData['due_date'] ?? null,
            'escalation_date' => $alertData['escalation_date'] ?? null,
            'status' => 'active',
            'priority' => $alertData['priority'] ?? self::calculatePriority($alertData['severity'] ?? 'medium'),
            'category' => $alertData['category'] ?? 'system',
            'source' => $alertData['source'] ?? 'system',
            'trigger_conditions' => $alertData['trigger_conditions'] ?? [],
            'alert_data' => $alertData['alert_data'] ?? [],
            'notification_channels' => $alertData['notification_channels'] ?? ['dashboard'],
            'recipients' => $alertData['recipients'] ?? [],
            'auto_resolve' => $alertData['auto_resolve'] ?? false,
            'repeat_interval' => $alertData['repeat_interval'] ?? null,
            'metadata' => $alertData['metadata'] ?? [],
        ]);

        // إرسال الإشعارات
        if ($alertData['send_immediately'] ?? true) {
            $alert->sendNotifications();
        }

        return $alert;
    }

    /**
     * توليد معرف التنبيه
     */
    protected static function generateAlertId(): string
    {
        return 'ALT_' . strtoupper(uniqid());
    }

    /**
     * حساب الأولوية بناءً على الخطورة
     */
    protected static function calculatePriority(string $severity): string
    {
        return match ($severity) {
            'emergency' => 'critical',
            'critical' => 'critical',
            'high' => 'urgent',
            'medium' => 'high',
            'low' => 'medium',
            'info' => 'low',
            default => 'medium',
        };
    }

    /**
     * إرسال الإشعارات
     */
    public function sendNotifications(): void
    {
        $channels = $this->notification_channels ?? ['dashboard'];
        
        foreach ($channels as $channel) {
            $this->sendNotificationToChannel($channel);
        }

        $this->update(['sent_at' => now()]);
    }

    /**
     * إرسال إشعار لقناة معينة
     */
    protected function sendNotificationToChannel(string $channel): void
    {
        switch ($channel) {
            case 'email':
                $this->sendEmailNotification();
                break;
            case 'sms':
                $this->sendSMSNotification();
                break;
            case 'push':
                $this->sendPushNotification();
                break;
            case 'slack':
                $this->sendSlackNotification();
                break;
            case 'teams':
                $this->sendTeamsNotification();
                break;
            case 'webhook':
                $this->sendWebhookNotification();
                break;
            case 'dashboard':
                // التنبيه موجود بالفعل في لوحة التحكم
                break;
        }
    }

    /**
     * تأكيد التنبيه
     */
    public function acknowledge(int $userId = null, string $notes = null): void
    {
        $this->update([
            'status' => 'acknowledged',
            'acknowledged_at' => now(),
            'acknowledged_by' => $userId ?? auth()->id(),
        ]);

        if ($notes) {
            $this->addNote('acknowledgment', $notes);
        }

        $this->logStatusChange('acknowledged');
    }

    /**
     * حل التنبيه
     */
    public function resolve(int $userId = null, string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => $userId ?? auth()->id(),
            'resolution_notes' => $notes,
        ]);

        $this->logStatusChange('resolved');
    }

    /**
     * رفض التنبيه
     */
    public function dismiss(int $userId = null, string $reason = null): void
    {
        $this->update([
            'status' => 'dismissed',
            'dismissed_at' => now(),
            'dismissed_by' => $userId ?? auth()->id(),
        ]);

        if ($reason) {
            $this->addNote('dismissal', $reason);
        }

        $this->logStatusChange('dismissed');
    }

    /**
     * تصعيد التنبيه
     */
    public function escalate(int $userId = null, string $reason = null): void
    {
        $this->update([
            'status' => 'escalated',
            'escalated_at' => now(),
            'escalated_by' => $userId ?? auth()->id(),
            'priority' => $this->getEscalatedPriority(),
        ]);

        if ($reason) {
            $this->addNote('escalation', $reason);
        }

        $this->logStatusChange('escalated');
        $this->sendEscalationNotifications();
    }

    /**
     * الحصول على أولوية مصعدة
     */
    protected function getEscalatedPriority(): string
    {
        return match ($this->priority) {
            'low' => 'medium',
            'medium' => 'high',
            'high' => 'urgent',
            'urgent' => 'critical',
            default => 'critical',
        };
    }

    /**
     * إضافة ملاحظة
     */
    public function addNote(string $type, string $note): void
    {
        $metadata = $this->metadata ?? [];
        $metadata['notes'][] = [
            'type' => $type,
            'note' => $note,
            'added_by' => auth()->id(),
            'added_at' => now()->toISOString(),
        ];

        $this->update(['metadata' => $metadata]);
    }

    /**
     * تسجيل تغيير الحالة
     */
    protected function logStatusChange(string $newStatus): void
    {
        $metadata = $this->metadata ?? [];
        $metadata['status_history'][] = [
            'from_status' => $this->getOriginal('status'),
            'to_status' => $newStatus,
            'changed_by' => auth()->id(),
            'changed_at' => now()->toISOString(),
        ];

        $this->update(['metadata' => $metadata]);
    }

    /**
     * إرسال إشعارات التصعيد
     */
    protected function sendEscalationNotifications(): void
    {
        // منطق إرسال إشعارات التصعيد للمدراء
    }

    /**
     * التحقق من انتهاء صلاحية التنبيه
     */
    public function isExpired(): bool
    {
        return $this->due_date && $this->due_date->isPast() && $this->status === 'active';
    }

    /**
     * التحقق من الحاجة للتصعيد
     */
    public function needsEscalation(): bool
    {
        return $this->escalation_date && 
               $this->escalation_date->isPast() && 
               $this->status === 'active';
    }

    /**
     * التحقق من الحاجة للتكرار
     */
    public function needsRepeat(): bool
    {
        if (!$this->repeat_interval || $this->status !== 'active') {
            return false;
        }

        $lastRepeated = $this->last_repeated_at ?? $this->created_at;
        $nextRepeat = $lastRepeated->addMinutes($this->repeat_interval);

        return now()->isAfter($nextRepeat);
    }

    /**
     * تكرار التنبيه
     */
    public function repeat(): void
    {
        if (!$this->needsRepeat()) {
            return;
        }

        $this->sendNotifications();
        
        $this->update([
            'last_repeated_at' => now(),
            'repeat_count' => ($this->repeat_count ?? 0) + 1,
        ]);
    }

    /**
     * الحل التلقائي
     */
    public function autoResolve(): void
    {
        if (!$this->auto_resolve) {
            return;
        }

        $this->update([
            'status' => 'auto_resolved',
            'resolved_at' => now(),
        ]);

        $this->logStatusChange('auto_resolved');
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('alert_type', $type);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent', 'critical']);
    }

    public function scopeCritical($query)
    {
        return $query->whereIn('severity', ['critical', 'emergency']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('status', 'active');
    }

    public function scopeNeedsEscalation($query)
    {
        return $query->where('escalation_date', '<', now())
                    ->where('status', 'active');
    }

    public function scopeUnacknowledged($query)
    {
        return $query->where('status', 'active')
                    ->whereNull('acknowledged_at');
    }

    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    /**
     * الحصول على ملخص التنبيه
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->alert_id,
            'title' => $this->title,
            'type' => $this->alert_type,
            'severity' => $this->severity,
            'status' => $this->status,
            'priority' => $this->priority,
            'category' => $this->category,
            'due_date' => $this->due_date?->format('Y-m-d H:i'),
            'is_expired' => $this->isExpired(),
            'needs_escalation' => $this->needsEscalation(),
            'created_at' => $this->created_at->format('Y-m-d H:i'),
            'country' => $this->country?->name_ar,
        ];
    }

    // طرق إرسال الإشعارات (يمكن تطويرها لاحقاً)
    protected function sendEmailNotification(): void { /* منطق البريد الإلكتروني */ }
    protected function sendSMSNotification(): void { /* منطق الرسائل النصية */ }
    protected function sendPushNotification(): void { /* منطق الإشعارات الفورية */ }
    protected function sendSlackNotification(): void { /* منطق سلاك */ }
    protected function sendTeamsNotification(): void { /* منطق تيمز */ }
    protected function sendWebhookNotification(): void { /* منطق ويب هوك */ }
}
