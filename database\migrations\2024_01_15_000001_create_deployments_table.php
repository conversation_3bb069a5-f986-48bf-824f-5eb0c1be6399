<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('deployments', function (Blueprint $table) {
            $table->id();
            $table->string('deployment_id')->unique();
            $table->string('deployable_type');
            $table->unsignedBigInteger('deployable_id');
            $table->string('strategy'); // blue_green, canary, rolling, ab_testing
            $table->string('version');
            $table->string('environment');
            $table->string('status'); // pending, in_progress, completed, failed, rolled_back
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->json('config')->nullable();
            $table->json('metadata')->nullable();
            $table->json('health_checks')->nullable();
            $table->json('rollback_config')->nullable();
            $table->json('traffic_config')->nullable();
            $table->json('feature_flags')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('rolled_back_at')->nullable();
            $table->unsignedBigInteger('deployed_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->text('notes')->nullable();
            $table->json('artifacts')->nullable();
            $table->json('metrics')->nullable();
            $table->json('logs')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['deployable_type', 'deployable_id']);
            $table->index(['status', 'created_at']);
            $table->index(['environment', 'strategy']);
            $table->foreign('deployed_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deployments');
    }
};
