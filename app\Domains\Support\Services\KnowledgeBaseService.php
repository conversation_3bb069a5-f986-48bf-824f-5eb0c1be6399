<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\KnowledgeBaseArticle;
use App\Domains\Support\Models\KnowledgeBaseCategory;
use App\Domains\Support\Models\Ticket;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * خدمة إدارة قاعدة المعرفة - Knowledge Base Service
 */
class KnowledgeBaseService
{
    protected AITicketClassificationService $aiService;

    public function __construct(AITicketClassificationService $aiService)
    {
        $this->aiService = $aiService;
    }

    /**
     * البحث الذكي في قاعدة المعرفة
     */
    public function smartSearch(string $query, array $filters = []): array
    {
        $cacheKey = 'kb_search_' . md5($query . serialize($filters));
        
        return Cache::remember($cacheKey, 1800, function () use ($query, $filters) {
            // تحليل الاستعلام
            $analysis = $this->analyzeSearchQuery($query);
            
            // البحث الأساسي
            $articles = $this->performSearch($query, $filters, $analysis);
            
            // ترتيب النتائج بالذكاء الاصطناعي
            $rankedResults = $this->rankSearchResults($articles, $analysis);
            
            // اقتراحات ذات صلة
            $suggestions = $this->generateSearchSuggestions($query, $analysis);
            
            return [
                'results' => $rankedResults,
                'total' => $articles->count(),
                'suggestions' => $suggestions,
                'query_analysis' => $analysis,
                'search_time' => microtime(true) - LARAVEL_START,
            ];
        });
    }

    /**
     * توليد مقالات تلقائية من التذاكر الشائعة
     */
    public function generateArticlesFromTickets(): array
    {
        $commonIssues = $this->identifyCommonIssues();
        $generatedArticles = [];

        foreach ($commonIssues as $issue) {
            if ($issue['frequency'] >= 5 && !$this->articleExistsForIssue($issue)) {
                $article = $this->createArticleFromIssue($issue);
                $generatedArticles[] = $article;
            }
        }

        return $generatedArticles;
    }

    /**
     * تحسين محتوى قاعدة المعرفة
     */
    public function optimizeContent(): array
    {
        $optimizations = [];
        
        // تحديد المقالات التي تحتاج تحسين
        $articles = KnowledgeBaseArticle::published()
            ->where('last_reviewed_at', '<', now()->subMonths(6))
            ->orWhere('helpful_count', '<', 5)
            ->orWhere('view_count', '<', 10)
            ->get();

        foreach ($articles as $article) {
            $analysis = $this->analyzeArticlePerformance($article);
            
            if ($analysis['needs_optimization']) {
                $optimizations[] = [
                    'article' => $article,
                    'issues' => $analysis['issues'],
                    'suggestions' => $this->generateOptimizationSuggestions($article, $analysis),
                ];
            }
        }

        return $optimizations;
    }

    /**
     * تحديد الفجوات في المعرفة
     */
    public function identifyKnowledgeGaps(): array
    {
        return [
            'unanswered_queries' => $this->getUnansweredSearchQueries(),
            'frequent_ticket_topics' => $this->getFrequentTicketTopicsWithoutArticles(),
            'low_success_searches' => $this->getLowSuccessSearches(),
            'missing_language_content' => $this->getMissingLanguageContent(),
            'outdated_articles' => $this->getOutdatedArticles(),
        ];
    }

    /**
     * إنشاء مقال جديد مع التحسين التلقائي
     */
    public function createOptimizedArticle(array $data): KnowledgeBaseArticle
    {
        // توليد slug تلقائي
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title']);
        }

        // تحسين SEO
        $data = $this->optimizeForSEO($data);

        // تحليل المحتوى وإضافة الوسوم
        $data['tags'] = $this->extractTagsFromContent($data['content']);

        // تقدير وقت القراءة
        $data['estimated_read_time'] = $this->calculateReadingTime($data['content']);

        // إنشاء المقال
        $article = KnowledgeBaseArticle::create($data);

        // ربط الوسوم
        $this->attachTags($article, $data['tags']);

        // فهرسة للبحث
        $this->indexForSearch($article);

        return $article;
    }

    /**
     * تحديث مقال مع التحسين
     */
    public function updateArticleWithOptimization(KnowledgeBaseArticle $article, array $data): KnowledgeBaseArticle
    {
        // تسجيل التغييرات
        $changes = $this->trackChanges($article, $data);

        // تحسين المحتوى
        $data = $this->optimizeForSEO($data);

        // تحديث المقال
        $article->update($data);

        // تحديث الإصدار
        if (!empty($changes)) {
            $article->createNewVersion($changes);
        }

        // إعادة فهرسة
        $this->reindexForSearch($article);

        return $article->fresh();
    }

    /**
     * تحليل أداء المقال
     */
    public function analyzeArticlePerformance(KnowledgeBaseArticle $article): array
    {
        $metrics = [
            'view_count' => $article->view_count,
            'helpful_percentage' => $article->helpful_percentage,
            'average_rating' => $article->average_rating,
            'comments_count' => $article->comments()->approved()->count(),
            'last_updated' => $article->updated_at,
            'search_appearances' => $this->getSearchAppearances($article),
            'bounce_rate' => $this->calculateBounceRate($article),
        ];

        $issues = [];
        $needs_optimization = false;

        // تحليل المقاييس
        if ($metrics['view_count'] < 10) {
            $issues[] = 'عدد مشاهدات منخفض';
            $needs_optimization = true;
        }

        if ($metrics['helpful_percentage'] < 60) {
            $issues[] = 'نسبة مفيد منخفضة';
            $needs_optimization = true;
        }

        if ($metrics['last_updated']->diffInMonths(now()) > 6) {
            $issues[] = 'محتوى قديم';
            $needs_optimization = true;
        }

        if ($metrics['bounce_rate'] > 70) {
            $issues[] = 'معدل ارتداد عالي';
            $needs_optimization = true;
        }

        return [
            'metrics' => $metrics,
            'issues' => $issues,
            'needs_optimization' => $needs_optimization,
            'performance_score' => $this->calculatePerformanceScore($metrics),
        ];
    }

    /**
     * توليد اقتراحات التحسين
     */
    protected function generateOptimizationSuggestions(KnowledgeBaseArticle $article, array $analysis): array
    {
        $suggestions = [];

        foreach ($analysis['issues'] as $issue) {
            switch ($issue) {
                case 'عدد مشاهدات منخفض':
                    $suggestions[] = [
                        'type' => 'seo',
                        'title' => 'تحسين SEO',
                        'description' => 'إضافة كلمات مفتاحية أكثر وتحسين العنوان',
                        'priority' => 'high',
                    ];
                    break;

                case 'نسبة مفيد منخفضة':
                    $suggestions[] = [
                        'type' => 'content',
                        'title' => 'تحسين المحتوى',
                        'description' => 'إضافة أمثلة عملية وخطوات أوضح',
                        'priority' => 'high',
                    ];
                    break;

                case 'محتوى قديم':
                    $suggestions[] = [
                        'type' => 'update',
                        'title' => 'تحديث المحتوى',
                        'description' => 'مراجعة وتحديث المعلومات',
                        'priority' => 'medium',
                    ];
                    break;

                case 'معدل ارتداد عالي':
                    $suggestions[] = [
                        'type' => 'structure',
                        'title' => 'تحسين البنية',
                        'description' => 'إعادة تنظيم المحتوى وإضافة فهرس',
                        'priority' => 'medium',
                    ];
                    break;
            }
        }

        return $suggestions;
    }

    /**
     * تحليل استعلام البحث
     */
    protected function analyzeSearchQuery(string $query): array
    {
        return [
            'language' => $this->detectLanguage($query),
            'intent' => $this->detectSearchIntent($query),
            'keywords' => $this->extractKeywords($query),
            'complexity' => $this->assessQueryComplexity($query),
            'category_hints' => $this->detectCategoryHints($query),
        ];
    }

    /**
     * تنفيذ البحث
     */
    protected function performSearch(string $query, array $filters, array $analysis): \Illuminate\Database\Eloquent\Collection
    {
        $searchQuery = KnowledgeBaseArticle::published();

        // البحث النصي
        $searchQuery->where(function ($q) use ($query, $analysis) {
            $language = $analysis['language'];
            $titleField = "title_{$language}";
            $contentField = "content_{$language}";

            $q->where($titleField, 'LIKE', "%{$query}%")
              ->orWhere($contentField, 'LIKE', "%{$query}%")
              ->orWhere('tags', 'LIKE', "%{$query}%");

            // البحث في الكلمات المفتاحية
            foreach ($analysis['keywords'] as $keyword) {
                $q->orWhere($titleField, 'LIKE', "%{$keyword}%")
                  ->orWhere($contentField, 'LIKE', "%{$keyword}%");
            }
        });

        // تطبيق الفلاتر
        if (!empty($filters['category_id'])) {
            $searchQuery->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['difficulty_level'])) {
            $searchQuery->where('difficulty_level', $filters['difficulty_level']);
        }

        if (!empty($filters['target_audience'])) {
            $searchQuery->where('target_audience', $filters['target_audience']);
        }

        return $searchQuery->with(['category', 'author'])->get();
    }

    /**
     * ترتيب نتائج البحث
     */
    protected function rankSearchResults($articles, array $analysis): array
    {
        return $articles->map(function ($article) use ($analysis) {
            $score = $this->calculateRelevanceScore($article, $analysis);
            
            return [
                'article' => $article,
                'relevance_score' => $score,
                'match_reasons' => $this->getMatchReasons($article, $analysis),
            ];
        })->sortByDesc('relevance_score')->values()->toArray();
    }

    /**
     * حساب نقاط الصلة
     */
    protected function calculateRelevanceScore(KnowledgeBaseArticle $article, array $analysis): float
    {
        $score = 0;

        // تطابق العنوان (وزن 40%)
        $titleMatch = $this->calculateTextMatch($article->localized_title, $analysis['keywords']);
        $score += $titleMatch * 0.4;

        // تطابق المحتوى (وزن 30%)
        $contentMatch = $this->calculateTextMatch($article->localized_content, $analysis['keywords']);
        $score += $contentMatch * 0.3;

        // شعبية المقال (وزن 20%)
        $popularityScore = min($article->view_count / 1000, 1.0);
        $score += $popularityScore * 0.2;

        // جودة المقال (وزن 10%)
        $qualityScore = $article->helpful_percentage / 100;
        $score += $qualityScore * 0.1;

        return round($score, 3);
    }

    /**
     * حساب تطابق النص
     */
    protected function calculateTextMatch(string $text, array $keywords): float
    {
        $text = strtolower($text);
        $matches = 0;

        foreach ($keywords as $keyword) {
            if (str_contains($text, strtolower($keyword))) {
                $matches++;
            }
        }

        return count($keywords) > 0 ? $matches / count($keywords) : 0;
    }

    // دوال مساعدة أخرى
    protected function detectLanguage(string $text): string
    {
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
            return 'ar';
        } elseif (preg_match('/[àâäéèêëïîôöùûüÿç]/i', $text)) {
            return 'fr';
        } else {
            return 'en';
        }
    }

    protected function detectSearchIntent(string $query): string
    {
        $query = strtolower($query);
        
        if (str_contains($query, 'كيف') || str_contains($query, 'how')) {
            return 'how_to';
        } elseif (str_contains($query, 'ما هو') || str_contains($query, 'what is')) {
            return 'definition';
        } elseif (str_contains($query, 'مشكلة') || str_contains($query, 'problem')) {
            return 'troubleshooting';
        } else {
            return 'general';
        }
    }

    protected function extractKeywords(string $text): array
    {
        $words = preg_split('/\s+/', strtolower($text));
        return array_filter($words, function ($word) {
            return strlen($word) >= 3;
        });
    }

    protected function assessQueryComplexity(string $query): string
    {
        $wordCount = str_word_count($query);
        
        if ($wordCount <= 2) return 'simple';
        if ($wordCount <= 5) return 'medium';
        return 'complex';
    }

    protected function detectCategoryHints(string $query): array
    {
        // منطق كشف الفئات من الاستعلام
        return [];
    }

    protected function generateSearchSuggestions(string $query, array $analysis): array
    {
        // توليد اقتراحات البحث
        return [];
    }

    protected function identifyCommonIssues(): array
    {
        // تحديد المشاكل الشائعة من التذاكر
        return [];
    }

    protected function articleExistsForIssue(array $issue): bool
    {
        // التحقق من وجود مقال للمشكلة
        return false;
    }

    protected function createArticleFromIssue(array $issue): KnowledgeBaseArticle
    {
        // إنشاء مقال من المشكلة
        return new KnowledgeBaseArticle();
    }

    protected function generateUniqueSlug(string $title): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (KnowledgeBaseArticle::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    protected function optimizeForSEO(array $data): array
    {
        // تحسين SEO
        if (empty($data['meta_title'])) {
            $data['meta_title'] = Str::limit($data['title'], 60);
        }

        if (empty($data['meta_description'])) {
            $data['meta_description'] = Str::limit(strip_tags($data['content']), 160);
        }

        return $data;
    }

    protected function extractTagsFromContent(string $content): array
    {
        // استخراج الوسوم من المحتوى
        return [];
    }

    protected function calculateReadingTime(string $content): int
    {
        $wordCount = str_word_count(strip_tags($content));
        return ceil($wordCount / 200); // 200 كلمة في الدقيقة
    }

    protected function attachTags(KnowledgeBaseArticle $article, array $tags): void
    {
        // ربط الوسوم
    }

    protected function indexForSearch(KnowledgeBaseArticle $article): void
    {
        // فهرسة للبحث
        $article->searchable();
    }

    protected function reindexForSearch(KnowledgeBaseArticle $article): void
    {
        // إعادة فهرسة
        $article->searchable();
    }

    protected function trackChanges(KnowledgeBaseArticle $article, array $data): array
    {
        // تتبع التغييرات
        return [];
    }

    protected function getSearchAppearances(KnowledgeBaseArticle $article): int
    {
        // عدد ظهور المقال في البحث
        return 0;
    }

    protected function calculateBounceRate(KnowledgeBaseArticle $article): float
    {
        // حساب معدل الارتداد
        return 0;
    }

    protected function calculatePerformanceScore(array $metrics): int
    {
        // حساب نقاط الأداء
        return 75;
    }

    protected function getMatchReasons(KnowledgeBaseArticle $article, array $analysis): array
    {
        // أسباب التطابق
        return [];
    }

    protected function getUnansweredSearchQueries(): array { return []; }
    protected function getFrequentTicketTopicsWithoutArticles(): array { return []; }
    protected function getLowSuccessSearches(): array { return []; }
    protected function getMissingLanguageContent(): array { return []; }
    protected function getOutdatedArticles(): array { return []; }
}
