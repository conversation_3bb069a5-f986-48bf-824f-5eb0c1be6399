<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\BankTransaction;
use App\Domains\Accounting\Models\Account;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة الكشف عن الشذوذ في المعاملات المحاسبية
 * تستخدم خوارزميات الذكاء الاصطناعي لاكتشاف المعاملات المشبوهة
 */
class AnomalyDetectionService
{
    protected array $anomalyRules = [];
    protected array $statisticalThresholds = [];
    protected array $behavioralPatterns = [];

    public function __construct()
    {
        $this->loadAnomalyRules();
        $this->loadStatisticalThresholds();
        $this->loadBehavioralPatterns();
    }

    /**
     * كشف الشذوذ في قيد محاسبي
     */
    public function detectAnomalies(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص القواعد الأساسية
        $basicAnomalies = $this->checkBasicRules($entry);
        $anomalies = array_merge($anomalies, $basicAnomalies);

        // فحص الشذوذ الإحصائي
        $statisticalAnomalies = $this->checkStatisticalAnomalies($entry);
        $anomalies = array_merge($anomalies, $statisticalAnomalies);

        // فحص الأنماط السلوكية
        $behavioralAnomalies = $this->checkBehavioralAnomalies($entry);
        $anomalies = array_merge($anomalies, $behavioralAnomalies);

        // فحص التوقيت
        $timingAnomalies = $this->checkTimingAnomalies($entry);
        $anomalies = array_merge($anomalies, $timingAnomalies);

        // فحص المبالغ
        $amountAnomalies = $this->checkAmountAnomalies($entry);
        $anomalies = array_merge($anomalies, $amountAnomalies);

        // تقييم مستوى الخطر الإجمالي
        $riskLevel = $this->calculateRiskLevel($anomalies);

        return [
            'anomalies' => $anomalies,
            'risk_level' => $riskLevel,
            'requires_review' => $riskLevel >= 70,
            'requires_approval' => $riskLevel >= 85,
            'total_anomalies' => count($anomalies),
            'detection_timestamp' => now(),
        ];
    }

    /**
     * فحص القواعد الأساسية
     */
    protected function checkBasicRules(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص التوازن
        if (abs($entry->total_debit - $entry->total_credit) > 0.01) {
            $anomalies[] = [
                'type' => 'UNBALANCED_ENTRY',
                'severity' => 'HIGH',
                'message' => 'القيد غير متوازن',
                'details' => [
                    'debit_total' => $entry->total_debit,
                    'credit_total' => $entry->total_credit,
                    'difference' => abs($entry->total_debit - $entry->total_credit),
                ],
                'score' => 50,
            ];
        }

        // فحص الحسابات المغلقة
        foreach ($entry->lines as $line) {
            if (!$line->account->is_active) {
                $anomalies[] = [
                    'type' => 'INACTIVE_ACCOUNT',
                    'severity' => 'MEDIUM',
                    'message' => 'استخدام حساب غير نشط',
                    'details' => [
                        'account_code' => $line->account->code,
                        'account_name' => $line->account->name,
                        'line_amount' => $line->debit_amount ?: $line->credit_amount,
                    ],
                    'score' => 25,
                ];
            }
        }

        // فحص التواريخ المستقبلية
        if ($entry->entry_date > now()->addDays(1)) {
            $anomalies[] = [
                'type' => 'FUTURE_DATE',
                'severity' => 'MEDIUM',
                'message' => 'تاريخ القيد في المستقبل',
                'details' => [
                    'entry_date' => $entry->entry_date,
                    'current_date' => now(),
                ],
                'score' => 20,
            ];
        }

        // فحص التواريخ القديمة جداً
        if ($entry->entry_date < now()->subMonths(6)) {
            $anomalies[] = [
                'type' => 'OLD_DATE',
                'severity' => 'LOW',
                'message' => 'تاريخ القيد قديم جداً',
                'details' => [
                    'entry_date' => $entry->entry_date,
                    'months_old' => now()->diffInMonths($entry->entry_date),
                ],
                'score' => 10,
            ];
        }

        return $anomalies;
    }

    /**
     * فحص الشذوذ الإحصائي
     */
    protected function checkStatisticalAnomalies(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص المبالغ الاستثنائية
        $totalAmount = $entry->total_debit;
        $accountStats = $this->getAccountStatistics($entry);

        foreach ($entry->lines as $line) {
            $amount = $line->debit_amount ?: $line->credit_amount;
            $accountId = $line->account_id;

            // فحص المبلغ مقارنة بالمتوسط التاريخي
            if (isset($accountStats[$accountId])) {
                $stats = $accountStats[$accountId];
                $zScore = $this->calculateZScore($amount, $stats['mean'], $stats['std_dev']);

                if (abs($zScore) > 3) { // أكثر من 3 انحرافات معيارية
                    $anomalies[] = [
                        'type' => 'STATISTICAL_OUTLIER',
                        'severity' => abs($zScore) > 4 ? 'HIGH' : 'MEDIUM',
                        'message' => 'مبلغ استثنائي مقارنة بالتاريخ',
                        'details' => [
                            'account_code' => $line->account->code,
                            'amount' => $amount,
                            'historical_mean' => $stats['mean'],
                            'z_score' => $zScore,
                        ],
                        'score' => min(abs($zScore) * 10, 40),
                    ];
                }
            }
        }

        // فحص تكرار المعاملات المتشابهة
        $similarEntries = $this->findSimilarEntries($entry);
        if ($similarEntries > 5) {
            $anomalies[] = [
                'type' => 'FREQUENT_SIMILAR_ENTRIES',
                'severity' => 'MEDIUM',
                'message' => 'تكرار عالي لمعاملات مشابهة',
                'details' => [
                    'similar_entries_count' => $similarEntries,
                    'time_period' => '30 يوم',
                ],
                'score' => 20,
            ];
        }

        return $anomalies;
    }

    /**
     * فحص الأنماط السلوكية
     */
    protected function checkBehavioralAnomalies(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص النشاط خارج ساعات العمل
        $hour = $entry->created_at->hour;
        if ($hour < 8 || $hour > 18) {
            $anomalies[] = [
                'type' => 'OFF_HOURS_ACTIVITY',
                'severity' => 'LOW',
                'message' => 'نشاط خارج ساعات العمل',
                'details' => [
                    'entry_hour' => $hour,
                    'created_at' => $entry->created_at,
                ],
                'score' => 15,
            ];
        }

        // فحص النشاط في عطلة نهاية الأسبوع
        $dayOfWeek = $entry->created_at->dayOfWeek;
        if ($dayOfWeek == 0 || $dayOfWeek == 6) { // الأحد أو السبت
            $anomalies[] = [
                'type' => 'WEEKEND_ACTIVITY',
                'severity' => 'LOW',
                'message' => 'نشاط في عطلة نهاية الأسبوع',
                'details' => [
                    'day_of_week' => $dayOfWeek,
                    'created_at' => $entry->created_at,
                ],
                'score' => 10,
            ];
        }

        // فحص المستخدم
        if ($entry->created_by) {
            $userBehavior = $this->analyzeUserBehavior($entry->created_by, $entry);
            if (!empty($userBehavior['anomalies'])) {
                $anomalies = array_merge($anomalies, $userBehavior['anomalies']);
            }
        }

        return $anomalies;
    }

    /**
     * فحص شذوذ التوقيت
     */
    protected function checkTimingAnomalies(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص السرعة في الإدخال
        $entrySpeed = $this->calculateEntrySpeed($entry);
        if ($entrySpeed < 30) { // أقل من 30 ثانية لقيد معقد
            $anomalies[] = [
                'type' => 'RAPID_ENTRY',
                'severity' => 'MEDIUM',
                'message' => 'إدخال سريع جداً للقيد',
                'details' => [
                    'entry_speed_seconds' => $entrySpeed,
                    'lines_count' => $entry->lines->count(),
                ],
                'score' => 25,
            ];
        }

        // فحص التعديلات المتكررة
        $modifications = $this->getEntryModifications($entry);
        if ($modifications > 3) {
            $anomalies[] = [
                'type' => 'FREQUENT_MODIFICATIONS',
                'severity' => 'MEDIUM',
                'message' => 'تعديلات متكررة على القيد',
                'details' => [
                    'modifications_count' => $modifications,
                ],
                'score' => 20,
            ];
        }

        return $anomalies;
    }

    /**
     * فحص شذوذ المبالغ
     */
    protected function checkAmountAnomalies(JournalEntry $entry): array
    {
        $anomalies = [];

        // فحص الأرقام المستديرة المشبوهة
        foreach ($entry->lines as $line) {
            $amount = $line->debit_amount ?: $line->credit_amount;
            
            if ($amount > 1000 && $amount % 1000 == 0) {
                $anomalies[] = [
                    'type' => 'ROUND_NUMBER_SUSPICIOUS',
                    'severity' => 'LOW',
                    'message' => 'مبلغ مستدير قد يكون مشبوه',
                    'details' => [
                        'amount' => $amount,
                        'account_code' => $line->account->code,
                    ],
                    'score' => 5,
                ];
            }
        }

        // فحص المبالغ الكبيرة جداً
        $totalAmount = $entry->total_debit;
        $companyThreshold = $this->getCompanyLargeAmountThreshold();
        
        if ($totalAmount > $companyThreshold) {
            $anomalies[] = [
                'type' => 'LARGE_AMOUNT',
                'severity' => 'HIGH',
                'message' => 'مبلغ كبير يتطلب مراجعة',
                'details' => [
                    'amount' => $totalAmount,
                    'threshold' => $companyThreshold,
                ],
                'score' => 35,
            ];
        }

        // فحص المبالغ الصغيرة المشبوهة
        if ($totalAmount < 1 && $totalAmount > 0) {
            $anomalies[] = [
                'type' => 'VERY_SMALL_AMOUNT',
                'severity' => 'LOW',
                'message' => 'مبلغ صغير جداً قد يكون خطأ',
                'details' => [
                    'amount' => $totalAmount,
                ],
                'score' => 10,
            ];
        }

        return $anomalies;
    }

    /**
     * حساب مستوى الخطر الإجمالي
     */
    protected function calculateRiskLevel(array $anomalies): int
    {
        $totalScore = 0;
        $highSeverityCount = 0;
        $mediumSeverityCount = 0;

        foreach ($anomalies as $anomaly) {
            $totalScore += $anomaly['score'];
            
            switch ($anomaly['severity']) {
                case 'HIGH':
                    $highSeverityCount++;
                    break;
                case 'MEDIUM':
                    $mediumSeverityCount++;
                    break;
            }
        }

        // تطبيق مضاعفات للشذوذ عالي الخطورة
        $riskLevel = $totalScore + ($highSeverityCount * 20) + ($mediumSeverityCount * 10);

        return min($riskLevel, 100); // حد أقصى 100
    }

    /**
     * الحصول على إحصائيات الحسابات
     */
    protected function getAccountStatistics(JournalEntry $entry): array
    {
        $cacheKey = 'account_statistics_' . md5(serialize($entry->lines->pluck('account_id')->toArray()));
        
        return Cache::remember($cacheKey, now()->addHours(6), function () use ($entry) {
            $stats = [];
            
            foreach ($entry->lines as $line) {
                $accountId = $line->account_id;
                
                $amounts = JournalEntryLine::where('account_id', $accountId)
                    ->where('created_at', '>=', now()->subMonths(6))
                    ->get()
                    ->map(function ($line) {
                        return $line->debit_amount ?: $line->credit_amount;
                    })
                    ->filter(function ($amount) {
                        return $amount > 0;
                    });

                if ($amounts->count() > 5) {
                    $stats[$accountId] = [
                        'mean' => $amounts->avg(),
                        'std_dev' => $this->calculateStandardDeviation($amounts->toArray()),
                        'count' => $amounts->count(),
                    ];
                }
            }
            
            return $stats;
        });
    }

    /**
     * حساب Z-Score
     */
    protected function calculateZScore(float $value, float $mean, float $stdDev): float
    {
        if ($stdDev == 0) {
            return 0;
        }
        
        return ($value - $mean) / $stdDev;
    }

    /**
     * حساب الانحراف المعياري
     */
    protected function calculateStandardDeviation(array $values): float
    {
        $count = count($values);
        if ($count < 2) {
            return 0;
        }

        $mean = array_sum($values) / $count;
        $variance = array_sum(array_map(function ($value) use ($mean) {
            return pow($value - $mean, 2);
        }, $values)) / ($count - 1);

        return sqrt($variance);
    }

    /**
     * البحث عن قيود مشابهة
     */
    protected function findSimilarEntries(JournalEntry $entry): int
    {
        return JournalEntry::where('id', '!=', $entry->id)
            ->where('total_debit', $entry->total_debit)
            ->where('description', 'LIKE', '%' . substr($entry->description, 0, 20) . '%')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();
    }

    /**
     * تحليل سلوك المستخدم
     */
    protected function analyzeUserBehavior(int $userId, JournalEntry $entry): array
    {
        $anomalies = [];
        
        // فحص نشاط المستخدم غير المعتاد
        $userEntries = JournalEntry::where('created_by', $userId)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        $dailyAverage = $userEntries / 30;
        $todayEntries = JournalEntry::where('created_by', $userId)
            ->whereDate('created_at', today())
            ->count();

        if ($todayEntries > $dailyAverage * 3) {
            $anomalies[] = [
                'type' => 'UNUSUAL_USER_ACTIVITY',
                'severity' => 'MEDIUM',
                'message' => 'نشاط غير معتاد للمستخدم',
                'details' => [
                    'user_id' => $userId,
                    'today_entries' => $todayEntries,
                    'daily_average' => $dailyAverage,
                ],
                'score' => 20,
            ];
        }

        return ['anomalies' => $anomalies];
    }

    /**
     * حساب سرعة الإدخال
     */
    protected function calculateEntrySpeed(JournalEntry $entry): int
    {
        // تقدير بسيط - في التطبيق الحقيقي يمكن تتبع الوقت الفعلي
        $complexity = $entry->lines->count() * 10; // 10 ثواني لكل خط
        return max($complexity, 30); // حد أدنى 30 ثانية
    }

    /**
     * الحصول على عدد التعديلات
     */
    protected function getEntryModifications(JournalEntry $entry): int
    {
        // في التطبيق الحقيقي، يمكن تتبع التعديلات في جدول منفصل
        return 0;
    }

    /**
     * الحصول على حد المبلغ الكبير للشركة
     */
    protected function getCompanyLargeAmountThreshold(): float
    {
        // يمكن تخصيص هذا حسب حجم الشركة
        return 100000; // 100,000 درهم افتراضياً
    }

    /**
     * تحميل قواعد الشذوذ
     */
    protected function loadAnomalyRules(): void
    {
        $this->anomalyRules = [
            'max_daily_entries_per_user' => 50,
            'max_entry_amount' => 1000000,
            'min_entry_speed_seconds' => 30,
            'max_modifications_allowed' => 3,
        ];
    }

    /**
     * تحميل العتبات الإحصائية
     */
    protected function loadStatisticalThresholds(): void
    {
        $this->statisticalThresholds = [
            'z_score_threshold' => 3.0,
            'similar_entries_threshold' => 5,
            'large_amount_multiplier' => 10,
        ];
    }

    /**
     * تحميل الأنماط السلوكية
     */
    protected function loadBehavioralPatterns(): void
    {
        $this->behavioralPatterns = [
            'normal_work_hours' => [8, 18],
            'normal_work_days' => [1, 2, 3, 4, 5], // الاثنين إلى الجمعة
            'suspicious_keywords' => ['test', 'تجربة', 'خطأ'],
        ];
    }

    /**
     * كشف الشذوذ في معاملة بنكية
     */
    public function detectBankTransactionAnomalies(BankTransaction $transaction): array
    {
        $anomalies = [];

        // فحص المبالغ الاستثنائية
        if (abs($transaction->amount) > 500000) {
            $anomalies[] = [
                'type' => 'LARGE_BANK_TRANSACTION',
                'severity' => 'HIGH',
                'message' => 'معاملة بنكية كبيرة',
                'score' => 40,
            ];
        }

        // فحص التوقيت المشبوه
        $hour = $transaction->transaction_date->hour;
        if ($hour < 6 || $hour > 22) {
            $anomalies[] = [
                'type' => 'SUSPICIOUS_TIMING',
                'severity' => 'MEDIUM',
                'message' => 'معاملة في توقيت مشبوه',
                'score' => 25,
            ];
        }

        return $anomalies;
    }
}
