<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج الاعتراف - Recognition
 */
class Recognition extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'project_id',
        'giver_id',
        'recipient_id',
        'type',
        'message',
        'points',
        'is_public',
        'metadata',
    ];

    protected $casts = [
        'points' => 'integer',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function giver(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'giver_id');
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'recipient_id');
    }
}
