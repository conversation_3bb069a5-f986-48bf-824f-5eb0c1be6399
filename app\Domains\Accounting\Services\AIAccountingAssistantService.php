<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * مساعد المحاسبة الذكي
 * يجيب على الأسئلة المحاسبية باللغة الطبيعية ويقدم توصيات ذكية
 */
class AIAccountingAssistantService
{
    protected array $knowledgeBase = [];
    protected array $conversationHistory = [];
    protected array $contextualData = [];

    public function __construct()
    {
        $this->loadKnowledgeBase();
        $this->loadContextualData();
    }

    /**
     * الإجابة على سؤال محاسبي
     */
    public function askQuestion(string $question, array $context = []): array
    {
        try {
            // تحليل السؤال
            $questionAnalysis = $this->analyzeQuestion($question);
            
            // استخراج المعلومات المطلوبة
            $requiredData = $this->extractRequiredData($questionAnalysis, $context);
            
            // توليد الإجابة
            $answer = $this->generateAnswer($questionAnalysis, $requiredData);
            
            // إضافة توصيات
            $recommendations = $this->generateRecommendations($questionAnalysis, $requiredData);
            
            // حفظ في تاريخ المحادثة
            $this->saveToConversationHistory($question, $answer);

            return [
                'question' => $question,
                'answer' => $answer,
                'data_sources' => $requiredData['sources'] ?? [],
                'recommendations' => $recommendations,
                'confidence_score' => $this->calculateConfidenceScore($questionAnalysis, $requiredData),
                'follow_up_questions' => $this->suggestFollowUpQuestions($questionAnalysis),
                'visualizations' => $this->suggestVisualizations($questionAnalysis, $requiredData),
                'timestamp' => now(),
            ];

        } catch (\Exception $e) {
            Log::error('خطأ في مساعد المحاسبة الذكي', [
                'question' => $question,
                'error' => $e->getMessage(),
            ]);

            return [
                'question' => $question,
                'answer' => 'عذراً، لم أتمكن من فهم السؤال أو الحصول على البيانات المطلوبة.',
                'error' => $e->getMessage(),
                'suggestions' => $this->getSuggestionForError($question),
            ];
        }
    }

    /**
     * تحليل السؤال
     */
    protected function analyzeQuestion(string $question): array
    {
        $question = strtolower(trim($question));
        
        $analysis = [
            'intent' => null,
            'entities' => [],
            'time_period' => null,
            'metrics' => [],
            'filters' => [],
            'question_type' => null,
        ];

        // تحديد نوع السؤال
        $analysis['question_type'] = $this->identifyQuestionType($question);
        
        // تحديد القصد
        $analysis['intent'] = $this->identifyIntent($question);
        
        // استخراج الكيانات
        $analysis['entities'] = $this->extractEntities($question);
        
        // استخراج الفترة الزمنية
        $analysis['time_period'] = $this->extractTimePeriod($question);
        
        // استخراج المقاييس المطلوبة
        $analysis['metrics'] = $this->extractMetrics($question);
        
        // استخراج المرشحات
        $analysis['filters'] = $this->extractFilters($question);

        return $analysis;
    }

    /**
     * تحديد نوع السؤال
     */
    protected function identifyQuestionType(string $question): string
    {
        $patterns = [
            'FINANCIAL_REPORT' => [
                'ما هو', 'كم', 'أظهر', 'اعرض', 'قائمة', 'تقرير', 'ميزانية', 'أرباح', 'خسائر'
            ],
            'COMPARISON' => [
                'مقارنة', 'الفرق', 'أفضل', 'أسوأ', 'مقابل', 'بالمقارنة مع'
            ],
            'TREND_ANALYSIS' => [
                'اتجاه', 'تطور', 'نمو', 'انخفاض', 'زيادة', 'تغير', 'خلال'
            ],
            'PREDICTION' => [
                'توقع', 'تنبؤ', 'المستقبل', 'القادم', 'متوقع', 'سيكون'
            ],
            'EXPLANATION' => [
                'لماذا', 'كيف', 'السبب', 'التفسير', 'ما سبب'
            ],
            'RECOMMENDATION' => [
                'نصيحة', 'توصية', 'اقتراح', 'ما رأيك', 'ماذا تنصح'
            ],
        ];

        foreach ($patterns as $type => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($question, $keyword) !== false) {
                    return $type;
                }
            }
        }

        return 'GENERAL';
    }

    /**
     * تحديد القصد
     */
    protected function identifyIntent(string $question): string
    {
        $intents = [
            'GET_REVENUE' => ['إيرادات', 'مبيعات', 'دخل', 'عائدات'],
            'GET_EXPENSES' => ['مصروفات', 'تكاليف', 'نفقات', 'مصاريف'],
            'GET_PROFIT' => ['ربح', 'أرباح', 'صافي الربح', 'هامش الربح'],
            'GET_CASH_FLOW' => ['تدفق نقدي', 'سيولة', 'نقدية', 'خزينة'],
            'GET_BALANCE_SHEET' => ['ميزانية', 'أصول', 'خصوم', 'حقوق ملكية'],
            'GET_CUSTOMER_ANALYSIS' => ['عميل', 'عملاء', 'زبون', 'زبائن'],
            'GET_VENDOR_ANALYSIS' => ['مورد', 'موردين', 'مقاول', 'مقاولين'],
            'GET_TAX_INFO' => ['ضريبة', 'ضرائب', 'TVA', 'IR'],
            'GET_BUDGET_VARIANCE' => ['ميزانية', 'انحراف', 'مقارنة الميزانية'],
        ];

        foreach ($intents as $intent => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($question, $keyword) !== false) {
                    return $intent;
                }
            }
        }

        return 'UNKNOWN';
    }

    /**
     * استخراج الكيانات
     */
    protected function extractEntities(string $question): array
    {
        $entities = [];

        // استخراج أسماء العملاء
        $customers = Customer::pluck('name')->toArray();
        foreach ($customers as $customer) {
            if (strpos($question, strtolower($customer)) !== false) {
                $entities['customers'][] = $customer;
            }
        }

        // استخراج أسماء الحسابات
        $accounts = Account::pluck('name')->toArray();
        foreach ($accounts as $account) {
            if (strpos($question, strtolower($account)) !== false) {
                $entities['accounts'][] = $account;
            }
        }

        // استخراج العملات
        $currencies = ['درهم', 'دولار', 'يورو', 'MAD', 'USD', 'EUR'];
        foreach ($currencies as $currency) {
            if (strpos($question, strtolower($currency)) !== false) {
                $entities['currency'] = $currency;
                break;
            }
        }

        return $entities;
    }

    /**
     * استخراج الفترة الزمنية
     */
    protected function extractTimePeriod(string $question): ?array
    {
        $patterns = [
            'THIS_MONTH' => ['هذا الشهر', 'الشهر الحالي', 'شهر حالي'],
            'LAST_MONTH' => ['الشهر الماضي', 'الشهر السابق'],
            'THIS_YEAR' => ['هذه السنة', 'السنة الحالية', 'هذا العام'],
            'LAST_YEAR' => ['السنة الماضية', 'العام الماضي', 'السنة السابقة'],
            'THIS_QUARTER' => ['هذا الربع', 'الربع الحالي'],
            'LAST_QUARTER' => ['الربع الماضي', 'الربع السابق'],
            'YTD' => ['منذ بداية السنة', 'من أول السنة'],
        ];

        foreach ($patterns as $period => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($question, $keyword) !== false) {
                    return $this->convertPeriodToDateRange($period);
                }
            }
        }

        // البحث عن تواريخ محددة
        if (preg_match('/(\d{4})/', $question, $matches)) {
            $year = $matches[1];
            return [
                'start_date' => "{$year}-01-01",
                'end_date' => "{$year}-12-31",
                'period_type' => 'YEAR',
            ];
        }

        return null;
    }

    /**
     * استخراج المقاييس المطلوبة
     */
    protected function extractMetrics(string $question): array
    {
        $metrics = [];
        
        $metricPatterns = [
            'TOTAL_AMOUNT' => ['إجمالي', 'مجموع', 'كامل'],
            'AVERAGE' => ['متوسط', 'معدل'],
            'PERCENTAGE' => ['نسبة', 'بالمئة', '%'],
            'GROWTH_RATE' => ['نمو', 'زيادة', 'نسبة النمو'],
            'MARGIN' => ['هامش', 'ربحية'],
        ];

        foreach ($metricPatterns as $metric => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($question, $keyword) !== false) {
                    $metrics[] = $metric;
                }
            }
        }

        return array_unique($metrics);
    }

    /**
     * استخراج المرشحات
     */
    protected function extractFilters(string $question): array
    {
        $filters = [];

        // مرشح العميل
        if (preg_match('/عميل\s+([^\s]+)/', $question, $matches)) {
            $filters['customer'] = $matches[1];
        }

        // مرشح القسم
        if (preg_match('/قسم\s+([^\s]+)/', $question, $matches)) {
            $filters['department'] = $matches[1];
        }

        return $filters;
    }

    /**
     * استخراج البيانات المطلوبة
     */
    protected function extractRequiredData(array $analysis, array $context): array
    {
        $data = ['sources' => []];

        switch ($analysis['intent']) {
            case 'GET_REVENUE':
                $data = array_merge($data, $this->getRevenueData($analysis, $context));
                break;
            case 'GET_EXPENSES':
                $data = array_merge($data, $this->getExpenseData($analysis, $context));
                break;
            case 'GET_PROFIT':
                $data = array_merge($data, $this->getProfitData($analysis, $context));
                break;
            case 'GET_CASH_FLOW':
                $data = array_merge($data, $this->getCashFlowData($analysis, $context));
                break;
            case 'GET_CUSTOMER_ANALYSIS':
                $data = array_merge($data, $this->getCustomerData($analysis, $context));
                break;
        }

        return $data;
    }

    /**
     * الحصول على بيانات الإيرادات
     */
    protected function getRevenueData(array $analysis, array $context): array
    {
        $dateRange = $analysis['time_period'] ?? $this->getDefaultDateRange();
        
        $query = DB::table('journal_entries as je')
            ->join('journal_entry_lines as jel', 'je.id', '=', 'jel.journal_entry_id')
            ->join('accounts as a', 'jel.account_id', '=', 'a.id')
            ->where('je.status', 'POSTED')
            ->where('a.account_type', 'REVENUE')
            ->whereBetween('je.entry_date', [$dateRange['start_date'], $dateRange['end_date']]);

        $totalRevenue = $query->sum('jel.credit_amount');
        
        $revenueByMonth = $query->selectRaw('MONTH(je.entry_date) as month, SUM(jel.credit_amount) as revenue')
            ->groupBy('month')
            ->pluck('revenue', 'month')
            ->toArray();

        return [
            'total_revenue' => $totalRevenue,
            'revenue_by_month' => $revenueByMonth,
            'period' => $dateRange,
            'sources' => ['journal_entries', 'accounts'],
        ];
    }

    /**
     * الحصول على بيانات المصروفات
     */
    protected function getExpenseData(array $analysis, array $context): array
    {
        $dateRange = $analysis['time_period'] ?? $this->getDefaultDateRange();
        
        $query = DB::table('journal_entries as je')
            ->join('journal_entry_lines as jel', 'je.id', '=', 'jel.journal_entry_id')
            ->join('accounts as a', 'jel.account_id', '=', 'a.id')
            ->where('je.status', 'POSTED')
            ->where('a.account_type', 'EXPENSE')
            ->whereBetween('je.entry_date', [$dateRange['start_date'], $dateRange['end_date']]);

        $totalExpenses = $query->sum('jel.debit_amount');
        
        $expensesByCategory = $query->selectRaw('a.name as category, SUM(jel.debit_amount) as expenses')
            ->groupBy('a.name')
            ->pluck('expenses', 'category')
            ->toArray();

        return [
            'total_expenses' => $totalExpenses,
            'expenses_by_category' => $expensesByCategory,
            'period' => $dateRange,
            'sources' => ['journal_entries', 'accounts'],
        ];
    }

    /**
     * توليد الإجابة
     */
    protected function generateAnswer(array $analysis, array $data): string
    {
        switch ($analysis['intent']) {
            case 'GET_REVENUE':
                return $this->generateRevenueAnswer($analysis, $data);
            case 'GET_EXPENSES':
                return $this->generateExpenseAnswer($analysis, $data);
            case 'GET_PROFIT':
                return $this->generateProfitAnswer($analysis, $data);
            default:
                return $this->generateGenericAnswer($analysis, $data);
        }
    }

    /**
     * توليد إجابة الإيرادات
     */
    protected function generateRevenueAnswer(array $analysis, array $data): string
    {
        $totalRevenue = number_format($data['total_revenue'] ?? 0, 2);
        $period = $this->formatPeriod($data['period'] ?? []);
        
        $answer = "إجمالي الإيرادات {$period} هو {$totalRevenue} درهم.";
        
        if (!empty($data['revenue_by_month'])) {
            $bestMonth = array_keys($data['revenue_by_month'], max($data['revenue_by_month']))[0];
            $bestMonthRevenue = number_format(max($data['revenue_by_month']), 2);
            $answer .= " أفضل شهر كان الشهر رقم {$bestMonth} بإيرادات {$bestMonthRevenue} درهم.";
        }

        return $answer;
    }

    /**
     * توليد إجابة المصروفات
     */
    protected function generateExpenseAnswer(array $analysis, array $data): string
    {
        $totalExpenses = number_format($data['total_expenses'] ?? 0, 2);
        $period = $this->formatPeriod($data['period'] ?? []);
        
        $answer = "إجمالي المصروفات {$period} هو {$totalExpenses} درهم.";
        
        if (!empty($data['expenses_by_category'])) {
            $topCategory = array_keys($data['expenses_by_category'], max($data['expenses_by_category']))[0];
            $topCategoryExpense = number_format(max($data['expenses_by_category']), 2);
            $answer .= " أعلى فئة مصروفات هي {$topCategory} بمبلغ {$topCategoryExpense} درهم.";
        }

        return $answer;
    }

    /**
     * توليد التوصيات
     */
    protected function generateRecommendations(array $analysis, array $data): array
    {
        $recommendations = [];

        switch ($analysis['intent']) {
            case 'GET_REVENUE':
                if (isset($data['total_revenue']) && $data['total_revenue'] > 0) {
                    $recommendations[] = "يمكنك تحليل الإيرادات حسب العملاء لتحديد أهم مصادر الدخل";
                    $recommendations[] = "راجع اتجاهات الإيرادات الشهرية لتحديد الأنماط الموسمية";
                }
                break;
            case 'GET_EXPENSES':
                if (isset($data['total_expenses']) && $data['total_expenses'] > 0) {
                    $recommendations[] = "راجع أكبر فئات المصروفات لتحديد فرص التوفير";
                    $recommendations[] = "قارن المصروفات مع الميزانية المخططة";
                }
                break;
        }

        return $recommendations;
    }

    /**
     * حساب نقاط الثقة
     */
    protected function calculateConfidenceScore(array $analysis, array $data): float
    {
        $score = 50; // نقطة البداية

        // زيادة الثقة إذا تم تحديد القصد بوضوح
        if ($analysis['intent'] !== 'UNKNOWN') {
            $score += 20;
        }

        // زيادة الثقة إذا تم العثور على بيانات
        if (!empty($data) && count($data) > 1) {
            $score += 20;
        }

        // زيادة الثقة إذا تم تحديد الفترة الزمنية
        if ($analysis['time_period']) {
            $score += 10;
        }

        return min($score, 100);
    }

    /**
     * اقتراح أسئلة متابعة
     */
    protected function suggestFollowUpQuestions(array $analysis): array
    {
        $suggestions = [];

        switch ($analysis['intent']) {
            case 'GET_REVENUE':
                $suggestions = [
                    "ما هي الإيرادات حسب العملاء؟",
                    "كيف تطورت الإيرادات مقارنة بالسنة الماضية؟",
                    "ما هو متوسط الإيرادات الشهرية؟",
                ];
                break;
            case 'GET_EXPENSES':
                $suggestions = [
                    "ما هي أكبر فئات المصروفات؟",
                    "كيف تقارن المصروفات مع الميزانية؟",
                    "ما هي المصروفات المتغيرة والثابتة؟",
                ];
                break;
        }

        return $suggestions;
    }

    // دوال مساعدة
    protected function loadKnowledgeBase(): void
    {
        $this->knowledgeBase = [
            'accounting_terms' => [
                'إيرادات' => 'المبالغ المحصلة من بيع السلع أو الخدمات',
                'مصروفات' => 'التكاليف المتكبدة في سير العمل',
                'ربح' => 'الفرق بين الإيرادات والمصروفات',
            ],
        ];
    }

    protected function loadContextualData(): void
    {
        $this->contextualData = [
            'company_info' => [
                'fiscal_year_start' => '01-01',
                'currency' => 'MAD',
                'business_type' => 'SERVICE',
            ],
        ];
    }

    protected function convertPeriodToDateRange(string $period): array
    {
        switch ($period) {
            case 'THIS_MONTH':
                return [
                    'start_date' => now()->startOfMonth()->format('Y-m-d'),
                    'end_date' => now()->endOfMonth()->format('Y-m-d'),
                ];
            case 'LAST_MONTH':
                return [
                    'start_date' => now()->subMonth()->startOfMonth()->format('Y-m-d'),
                    'end_date' => now()->subMonth()->endOfMonth()->format('Y-m-d'),
                ];
            case 'THIS_YEAR':
                return [
                    'start_date' => now()->startOfYear()->format('Y-m-d'),
                    'end_date' => now()->endOfYear()->format('Y-m-d'),
                ];
            default:
                return $this->getDefaultDateRange();
        }
    }

    protected function getDefaultDateRange(): array
    {
        return [
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->endOfMonth()->format('Y-m-d'),
        ];
    }

    protected function formatPeriod(array $period): string
    {
        if (empty($period)) return '';
        
        $start = \Carbon\Carbon::parse($period['start_date'])->format('d/m/Y');
        $end = \Carbon\Carbon::parse($period['end_date'])->format('d/m/Y');
        
        return "من {$start} إلى {$end}";
    }

    protected function saveToConversationHistory(string $question, string $answer): void
    {
        $this->conversationHistory[] = [
            'question' => $question,
            'answer' => $answer,
            'timestamp' => now(),
        ];
    }

    protected function getSuggestionForError(string $question): array
    {
        return [
            "تأكد من صياغة السؤال بوضوح",
            "حدد الفترة الزمنية المطلوبة",
            "استخدم مصطلحات محاسبية واضحة",
        ];
    }

    protected function suggestVisualizations(array $analysis, array $data): array
    {
        $visualizations = [];

        if ($analysis['intent'] === 'GET_REVENUE' && isset($data['revenue_by_month'])) {
            $visualizations[] = [
                'type' => 'line_chart',
                'title' => 'تطور الإيرادات الشهرية',
                'data' => $data['revenue_by_month'],
            ];
        }

        return $visualizations;
    }

    // دوال إضافية للبيانات
    protected function getProfitData(array $analysis, array $context): array { return []; }
    protected function getCashFlowData(array $analysis, array $context): array { return []; }
    protected function getCustomerData(array $analysis, array $context): array { return []; }
    protected function generateProfitAnswer(array $analysis, array $data): string { return ''; }
    protected function generateGenericAnswer(array $analysis, array $data): string { return 'عذراً، لم أتمكن من فهم السؤال.'; }
}
