<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تقييم الفريق - Team Feedback
 */
class TeamFeedback extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'project_id',
        'feedback_type',
        'rating',
        'comments',
        'feedback_data',
        'submitted_by',
        'is_anonymous',
    ];

    protected $casts = [
        'rating' => 'integer',
        'feedback_data' => 'array',
        'is_anonymous' => 'boolean',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function submitter(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'submitted_by');
    }
}
