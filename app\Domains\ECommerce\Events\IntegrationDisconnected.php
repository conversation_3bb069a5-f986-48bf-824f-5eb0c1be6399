<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث قطع اتصال التكامل
 * يتم إطلاقه عند قطع اتصال التكامل مع المنصة
 */
class IntegrationDisconnected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public string $reason;
    public array $disconnectionData;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(ECommerceIntegration $integration, string $reason = '', array $disconnectionData = [])
    {
        $this->integration = $integration;
        $this->reason = $reason;
        $this->disconnectionData = $disconnectionData;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'disconnected_at' => now(),
            'reason' => $this->reason,
            'disconnection_data' => $this->disconnectionData,
        ];
    }
}
