<?php

namespace App\Domains\Compliance\Jobs;

use App\Domains\Compliance\Models\Company;
use App\Domains\Compliance\Services\GovernmentIntegrationService;
use App\Domains\Compliance\Events\GovernmentSubmissionCompleted;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * مهمة التقديم للحكومة
 */
class SubmitToGovernment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120; // 2 minutes
    public $tries = 3;

    public function __construct(
        public Company $company,
        public string $countryCode,
        public string $systemCode,
        public string $operation,
        public array $data
    ) {}

    /**
     * تنفيذ المهمة
     */
    public function handle(GovernmentIntegrationService $integrationService): void
    {
        try {
            Log::info('Starting government submission', [
                'company_id' => $this->company->id,
                'country' => $this->countryCode,
                'system' => $this->systemCode,
                'operation' => $this->operation,
            ]);

            // إجراء التقديم
            $response = $integrationService->submitToGovernment(
                $this->countryCode,
                $this->systemCode,
                $this->operation,
                $this->data,
                $this->company
            );

            // إطلاق حدث إكمال التقديم
            $integration = $integrationService->getIntegration($this->countryCode, $this->systemCode);
            
            GovernmentSubmissionCompleted::dispatch(
                $integration,
                $this->company,
                $this->operation,
                $this->data,
                $response,
                true
            );

            Log::info('Government submission completed successfully', [
                'company_id' => $this->company->id,
                'country' => $this->countryCode,
                'system' => $this->systemCode,
                'operation' => $this->operation,
                'reference' => $response['reference_number'] ?? null,
            ]);

        } catch (\Exception $e) {
            Log::error('Government submission failed', [
                'company_id' => $this->company->id,
                'country' => $this->countryCode,
                'system' => $this->systemCode,
                'operation' => $this->operation,
                'error' => $e->getMessage(),
            ]);

            // إطلاق حدث فشل التقديم
            $integration = $integrationService->getIntegration($this->countryCode, $this->systemCode);
            
            GovernmentSubmissionCompleted::dispatch(
                $integration,
                $this->company,
                $this->operation,
                $this->data,
                ['error' => $e->getMessage()],
                false
            );

            throw $e;
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Government submission job failed permanently', [
            'company_id' => $this->company->id,
            'country' => $this->countryCode,
            'system' => $this->systemCode,
            'operation' => $this->operation,
            'error' => $exception->getMessage(),
        ]);
    }
}
