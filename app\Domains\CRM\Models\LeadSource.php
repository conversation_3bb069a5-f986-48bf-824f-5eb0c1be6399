<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج مصدر العميل المحتمل - Lead Source
 * تتبع مصادر العملاء المحتملين لتحليل فعالية القنوات
 */
class LeadSource extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'description',
        'type',
        'category',
        'channel',
        'cost_per_lead',
        'conversion_rate',
        'is_active',
        'tracking_code',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'attribution_model',
        'quality_score',
        'priority',
        'color',
        'icon',
        'settings',
        'metadata',
    ];

    protected $casts = [
        'cost_per_lead' => 'decimal:2',
        'conversion_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'quality_score' => 'integer',
        'priority' => 'integer',
        'settings' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع المصادر
     */
    const TYPES = [
        'organic' => 'عضوي',
        'paid' => 'مدفوع',
        'referral' => 'إحالة',
        'direct' => 'مباشر',
        'social' => 'وسائل التواصل',
        'email' => 'بريد إلكتروني',
        'content' => 'محتوى',
        'event' => 'فعالية',
        'partnership' => 'شراكة',
        'offline' => 'غير متصل',
    ];

    /**
     * فئات المصادر
     */
    const CATEGORIES = [
        'digital_marketing' => 'تسويق رقمي',
        'traditional_marketing' => 'تسويق تقليدي',
        'sales_outreach' => 'مبيعات مباشرة',
        'customer_referral' => 'إحالة عملاء',
        'partner_referral' => 'إحالة شركاء',
        'content_marketing' => 'تسويق المحتوى',
        'event_marketing' => 'تسويق الفعاليات',
        'pr_media' => 'علاقات عامة وإعلام',
        'word_of_mouth' => 'كلام منقول',
        'other' => 'أخرى',
    ];

    /**
     * قنوات المصادر
     */
    const CHANNELS = [
        'website' => 'موقع إلكتروني',
        'google_ads' => 'إعلانات جوجل',
        'facebook_ads' => 'إعلانات فيسبوك',
        'linkedin_ads' => 'إعلانات لينكد إن',
        'instagram_ads' => 'إعلانات إنستجرام',
        'twitter_ads' => 'إعلانات تويتر',
        'youtube_ads' => 'إعلانات يوتيوب',
        'seo' => 'تحسين محركات البحث',
        'email_marketing' => 'تسويق بالبريد الإلكتروني',
        'content_marketing' => 'تسويق المحتوى',
        'webinar' => 'ندوة إلكترونية',
        'trade_show' => 'معرض تجاري',
        'conference' => 'مؤتمر',
        'cold_calling' => 'مكالمات باردة',
        'cold_email' => 'بريد بارد',
        'referral_program' => 'برنامج إحالة',
        'affiliate' => 'تسويق بالعمولة',
        'print_media' => 'إعلام مطبوع',
        'radio' => 'راديو',
        'tv' => 'تلفزيون',
        'outdoor' => 'إعلانات خارجية',
        'direct_mail' => 'بريد مباشر',
        'telemarketing' => 'تسويق هاتفي',
        'word_of_mouth' => 'كلام منقول',
        'other' => 'أخرى',
    ];

    /**
     * نماذج الإسناد
     */
    const ATTRIBUTION_MODELS = [
        'first_touch' => 'اللمسة الأولى',
        'last_touch' => 'اللمسة الأخيرة',
        'linear' => 'خطي',
        'time_decay' => 'تدهور زمني',
        'position_based' => 'قائم على الموقع',
        'data_driven' => 'مدفوع بالبيانات',
    ];

    /**
     * العلاقة مع العملاء
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'source_id');
    }

    /**
     * العلاقة مع الفرص التجارية
     */
    public function opportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class, 'source_id');
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الفئة
     */
    public function getCategoryLabelAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية القناة
     */
    public function getChannelLabelAttribute(): string
    {
        return self::CHANNELS[$this->channel] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية نموذج الإسناد
     */
    public function getAttributionModelLabelAttribute(): string
    {
        return self::ATTRIBUTION_MODELS[$this->attribution_model] ?? 'غير محدد';
    }

    /**
     * التحقق من كون المصدر نشط
     */
    public function getIsActiveSourceAttribute(): bool
    {
        return $this->is_active;
    }

    /**
     * الحصول على عدد العملاء المحتملين
     */
    public function getLeadsCountAttribute(): int
    {
        return $this->customers()->count();
    }

    /**
     * الحصول على عدد العملاء المحولين
     */
    public function getConvertedLeadsCountAttribute(): int
    {
        return $this->customers()->where('status', '!=', 'lead')->count();
    }

    /**
     * الحصول على معدل التحويل المحسوب
     */
    public function getCalculatedConversionRateAttribute(): float
    {
        $totalLeads = $this->leads_count;
        $convertedLeads = $this->converted_leads_count;

        return $totalLeads > 0 ? round(($convertedLeads / $totalLeads) * 100, 2) : 0;
    }

    /**
     * الحصول على إجمالي الإيرادات
     */
    public function getTotalRevenueAttribute(): float
    {
        return $this->customers()->sum('total_spent');
    }

    /**
     * الحصول على متوسط قيمة العميل
     */
    public function getAverageCustomerValueAttribute(): float
    {
        $convertedLeads = $this->converted_leads_count;
        $totalRevenue = $this->total_revenue;

        return $convertedLeads > 0 ? round($totalRevenue / $convertedLeads, 2) : 0;
    }

    /**
     * الحصول على تكلفة اكتساب العميل
     */
    public function getCustomerAcquisitionCostAttribute(): float
    {
        $totalCost = $this->cost_per_lead * $this->leads_count;
        $convertedLeads = $this->converted_leads_count;

        return $convertedLeads > 0 ? round($totalCost / $convertedLeads, 2) : 0;
    }

    /**
     * الحصول على عائد الاستثمار
     */
    public function getReturnOnInvestmentAttribute(): float
    {
        $totalCost = $this->cost_per_lead * $this->leads_count;
        $totalRevenue = $this->total_revenue;

        return $totalCost > 0 ? round((($totalRevenue - $totalCost) / $totalCost) * 100, 2) : 0;
    }

    /**
     * الحصول على نقاط الجودة المحسوبة
     */
    public function getCalculatedQualityScoreAttribute(): int
    {
        $score = 0;

        // نقاط بناءً على معدل التحويل
        $conversionRate = $this->calculated_conversion_rate;
        if ($conversionRate >= 20) $score += 30;
        elseif ($conversionRate >= 15) $score += 25;
        elseif ($conversionRate >= 10) $score += 20;
        elseif ($conversionRate >= 5) $score += 15;
        elseif ($conversionRate > 0) $score += 10;

        // نقاط بناءً على متوسط قيمة العميل
        $avgValue = $this->average_customer_value;
        if ($avgValue >= 50000) $score += 25;
        elseif ($avgValue >= 20000) $score += 20;
        elseif ($avgValue >= 10000) $score += 15;
        elseif ($avgValue >= 5000) $score += 10;
        elseif ($avgValue > 0) $score += 5;

        // نقاط بناءً على عائد الاستثمار
        $roi = $this->return_on_investment;
        if ($roi >= 300) $score += 25;
        elseif ($roi >= 200) $score += 20;
        elseif ($roi >= 100) $score += 15;
        elseif ($roi >= 50) $score += 10;
        elseif ($roi > 0) $score += 5;

        // نقاط بناءً على حجم العملاء المحتملين
        $leadsCount = $this->leads_count;
        if ($leadsCount >= 100) $score += 20;
        elseif ($leadsCount >= 50) $score += 15;
        elseif ($leadsCount >= 20) $score += 10;
        elseif ($leadsCount >= 10) $score += 5;

        return min(100, $score);
    }

    /**
     * الحصول على لون نقاط الجودة
     */
    public function getQualityScoreColorAttribute(): string
    {
        $score = $this->quality_score ?: $this->calculated_quality_score;

        if ($score >= 80) return '#28a745';
        if ($score >= 60) return '#ffc107';
        if ($score >= 40) return '#fd7e14';
        return '#dc3545';
    }

    /**
     * الحصول على تصنيف الجودة
     */
    public function getQualityRatingAttribute(): string
    {
        $score = $this->quality_score ?: $this->calculated_quality_score;

        if ($score >= 80) return 'ممتاز';
        if ($score >= 60) return 'جيد';
        if ($score >= 40) return 'متوسط';
        return 'ضعيف';
    }

    /**
     * الحصول على إحصائيات المصدر
     */
    public function getStatsAttribute(): array
    {
        return [
            'leads_count' => $this->leads_count,
            'converted_leads_count' => $this->converted_leads_count,
            'conversion_rate' => $this->calculated_conversion_rate,
            'total_revenue' => $this->total_revenue,
            'average_customer_value' => $this->average_customer_value,
            'customer_acquisition_cost' => $this->customer_acquisition_cost,
            'return_on_investment' => $this->return_on_investment,
            'quality_score' => $this->calculated_quality_score,
            'quality_rating' => $this->quality_rating,
        ];
    }

    /**
     * الحصول على رابط التتبع
     */
    public function getTrackingUrlAttribute(): string
    {
        $baseUrl = config('app.url');
        $params = [];

        if ($this->utm_source) $params['utm_source'] = $this->utm_source;
        if ($this->utm_medium) $params['utm_medium'] = $this->utm_medium;
        if ($this->utm_campaign) $params['utm_campaign'] = $this->utm_campaign;
        if ($this->utm_term) $params['utm_term'] = $this->utm_term;
        if ($this->utm_content) $params['utm_content'] = $this->utm_content;
        if ($this->tracking_code) $params['ref'] = $this->tracking_code;

        return $baseUrl . (empty($params) ? '' : '?' . http_build_query($params));
    }

    /**
     * تحديث إحصائيات المصدر
     */
    public function updateStats(): void
    {
        $this->update([
            'conversion_rate' => $this->calculated_conversion_rate,
            'quality_score' => $this->calculated_quality_score,
        ]);
    }

    /**
     * تحليل الأداء مقارنة بالمصادر الأخرى
     */
    public function getPerformanceComparison(): array
    {
        $allSources = self::active()->get();
        
        $metrics = [
            'conversion_rate' => $this->calculated_conversion_rate,
            'average_customer_value' => $this->average_customer_value,
            'customer_acquisition_cost' => $this->customer_acquisition_cost,
            'return_on_investment' => $this->return_on_investment,
        ];

        $comparison = [];
        
        foreach ($metrics as $metric => $value) {
            $allValues = $allSources->pluck($metric)->filter()->values();
            $average = $allValues->avg();
            $percentile = $allValues->filter(fn($v) => $v <= $value)->count() / $allValues->count() * 100;
            
            $comparison[$metric] = [
                'value' => $value,
                'average' => round($average, 2),
                'percentile' => round($percentile),
                'performance' => $value > $average ? 'above_average' : 'below_average',
            ];
        }

        return $comparison;
    }

    /**
     * إنشاء تقرير أداء المصدر
     */
    public function generatePerformanceReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        $customers = $this->customers()
                         ->whereBetween('created_at', [$dateFrom, $dateTo])
                         ->get();

        return [
            'period' => [
                'from' => $dateFrom->format('Y-m-d'),
                'to' => $dateTo->format('Y-m-d'),
            ],
            'leads' => [
                'total' => $customers->count(),
                'converted' => $customers->where('status', '!=', 'lead')->count(),
                'conversion_rate' => $customers->count() > 0 ? 
                    round(($customers->where('status', '!=', 'lead')->count() / $customers->count()) * 100, 2) : 0,
            ],
            'revenue' => [
                'total' => $customers->sum('total_spent'),
                'average_per_customer' => $customers->where('total_spent', '>', 0)->avg('total_spent'),
            ],
            'costs' => [
                'total_cost' => $this->cost_per_lead * $customers->count(),
                'cost_per_acquisition' => $customers->where('status', '!=', 'lead')->count() > 0 ?
                    ($this->cost_per_lead * $customers->count()) / $customers->where('status', '!=', 'lead')->count() : 0,
            ],
            'trends' => $this->analyzeTrends($dateFrom, $dateTo),
        ];
    }

    /**
     * تحليل الاتجاهات
     */
    protected function analyzeTrends(Carbon $dateFrom, Carbon $dateTo): array
    {
        // تحليل الاتجاهات الأسبوعية
        $weeks = [];
        $currentDate = $dateFrom->copy()->startOfWeek();
        
        while ($currentDate->lte($dateTo)) {
            $weekEnd = $currentDate->copy()->endOfWeek();
            
            $weekCustomers = $this->customers()
                                 ->whereBetween('created_at', [$currentDate, $weekEnd])
                                 ->get();
            
            $weeks[] = [
                'week' => $currentDate->format('Y-m-d'),
                'leads' => $weekCustomers->count(),
                'converted' => $weekCustomers->where('status', '!=', 'lead')->count(),
                'revenue' => $weekCustomers->sum('total_spent'),
            ];
            
            $currentDate->addWeek();
        }

        return $weeks;
    }

    /**
     * فلترة المصادر النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * فلترة حسب القناة
     */
    public function scopeOfChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * ترتيب حسب الجودة
     */
    public function scopeOrderByQuality($query)
    {
        return $query->orderBy('quality_score', 'desc');
    }

    /**
     * ترتيب حسب الأداء
     */
    public function scopeOrderByPerformance($query)
    {
        return $query->orderByRaw('(conversion_rate * quality_score) DESC');
    }

    /**
     * البحث في المصادر
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('utm_source', 'LIKE', "%{$search}%")
              ->orWhere('utm_campaign', 'LIKE', "%{$search}%");
        });
    }
}
