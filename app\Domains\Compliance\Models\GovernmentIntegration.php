<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * نموذج التكاملات الحكومية
 * يدير جميع التكاملات مع الأنظمة الحكومية لكل دولة
 */
class GovernmentIntegration extends Model
{
    use HasFactory;

    protected $fillable = [
        'integration_id',
        'country_id',
        'authority_name',
        'authority_name_ar',
        'authority_name_en',
        'system_name',
        'system_code',
        'integration_type',
        'service_category',
        'api_version',
        'base_url',
        'sandbox_url',
        'production_url',
        'authentication_method',
        'authentication_config',
        'api_endpoints',
        'supported_operations',
        'data_formats',
        'rate_limits',
        'timeout_settings',
        'retry_config',
        'webhook_config',
        'certificate_config',
        'encryption_config',
        'validation_rules',
        'error_handling',
        'monitoring_config',
        'health_check_endpoint',
        'status_check_interval',
        'last_health_check',
        'connection_status',
        'api_status',
        'last_successful_call',
        'last_failed_call',
        'total_calls',
        'successful_calls',
        'failed_calls',
        'average_response_time',
        'uptime_percentage',
        'maintenance_windows',
        'documentation_url',
        'support_contact',
        'compliance_requirements',
        'data_retention_policy',
        'audit_requirements',
        'is_active',
        'is_mandatory',
        'environment',
        'go_live_date',
        'last_updated_by_authority',
        'metadata',
    ];

    protected $casts = [
        'authentication_config' => 'array',
        'api_endpoints' => 'array',
        'supported_operations' => 'array',
        'data_formats' => 'array',
        'rate_limits' => 'array',
        'timeout_settings' => 'array',
        'retry_config' => 'array',
        'webhook_config' => 'array',
        'certificate_config' => 'array',
        'encryption_config' => 'array',
        'validation_rules' => 'array',
        'error_handling' => 'array',
        'monitoring_config' => 'array',
        'last_health_check' => 'datetime',
        'last_successful_call' => 'datetime',
        'last_failed_call' => 'datetime',
        'maintenance_windows' => 'array',
        'compliance_requirements' => 'array',
        'data_retention_policy' => 'array',
        'audit_requirements' => 'array',
        'is_active' => 'boolean',
        'is_mandatory' => 'boolean',
        'go_live_date' => 'date',
        'last_updated_by_authority' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * أنواع التكامل
     */
    const INTEGRATION_TYPES = [
        'api_rest' => 'REST API',
        'api_soap' => 'SOAP API',
        'api_graphql' => 'GraphQL API',
        'file_upload' => 'رفع ملفات',
        'file_download' => 'تحميل ملفات',
        'webhook' => 'ويب هوك',
        'sftp' => 'SFTP',
        'email' => 'بريد إلكتروني',
        'portal' => 'بوابة إلكترونية',
        'batch_processing' => 'معالجة دفعية',
    ];

    /**
     * فئات الخدمة
     */
    const SERVICE_CATEGORIES = [
        'tax_filing' => 'التقديم الضريبي',
        'tax_payment' => 'الدفع الضريبي',
        'e_invoicing' => 'الفوترة الإلكترونية',
        'social_security' => 'الضمان الاجتماعي',
        'customs' => 'الجمارك',
        'licensing' => 'التراخيص',
        'permits' => 'التصاريح',
        'registrations' => 'التسجيلات',
        'notifications' => 'الإشعارات',
        'status_inquiry' => 'استعلام الحالة',
        'document_verification' => 'التحقق من الوثائق',
        'data_exchange' => 'تبادل البيانات',
    ];

    /**
     * طرق المصادقة
     */
    const AUTHENTICATION_METHODS = [
        'api_key' => 'مفتاح API',
        'oauth2' => 'OAuth 2.0',
        'jwt' => 'JWT Token',
        'basic_auth' => 'مصادقة أساسية',
        'certificate' => 'شهادة رقمية',
        'signature' => 'توقيع رقمي',
        'mutual_tls' => 'TLS متبادل',
        'saml' => 'SAML',
        'custom' => 'مخصص',
    ];

    /**
     * حالات الاتصال
     */
    const CONNECTION_STATUSES = [
        'connected' => 'متصل',
        'disconnected' => 'منقطع',
        'error' => 'خطأ',
        'maintenance' => 'صيانة',
        'testing' => 'اختبار',
        'unknown' => 'غير معروف',
    ];

    /**
     * حالات API
     */
    const API_STATUSES = [
        'operational' => 'تشغيلي',
        'degraded' => 'متدهور',
        'partial_outage' => 'انقطاع جزئي',
        'major_outage' => 'انقطاع كبير',
        'maintenance' => 'صيانة',
        'unknown' => 'غير معروف',
    ];

    /**
     * البيئات
     */
    const ENVIRONMENTS = [
        'sandbox' => 'بيئة اختبار',
        'staging' => 'بيئة تجريبية',
        'production' => 'بيئة إنتاج',
    ];

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع سجلات الاستدعاء
     */
    public function apiCalls(): HasMany
    {
        return $this->hasMany(GovernmentApiCall::class);
    }

    /**
     * إجراء استدعاء API
     */
    public function makeApiCall(string $operation, array $data = [], array $options = []): array
    {
        if (!$this->is_active) {
            throw new \Exception('التكامل غير نشط');
        }

        $endpoint = $this->getEndpoint($operation);
        if (!$endpoint) {
            throw new \Exception("العملية {$operation} غير مدعومة");
        }

        // تحضير الطلب
        $request = $this->prepareRequest($endpoint, $data, $options);
        
        // تسجيل بداية الاستدعاء
        $callLog = $this->logApiCall($operation, $request);

        try {
            // إجراء الاستدعاء
            $response = $this->executeApiCall($request);
            
            // معالجة الاستجابة
            $processedResponse = $this->processResponse($response, $endpoint);
            
            // تحديث سجل الاستدعاء
            $callLog->update([
                'status' => 'success',
                'response_data' => $processedResponse,
                'response_time' => $response['response_time'] ?? 0,
                'completed_at' => now(),
            ]);

            // تحديث إحصائيات التكامل
            $this->updateSuccessStats($response['response_time'] ?? 0);

            return $processedResponse;

        } catch (\Exception $e) {
            // تحديث سجل الاستدعاء
            $callLog->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            // تحديث إحصائيات التكامل
            $this->updateFailureStats();

            throw $e;
        }
    }

    /**
     * الحصول على نقطة النهاية للعملية
     */
    protected function getEndpoint(string $operation): ?array
    {
        $endpoints = $this->api_endpoints ?? [];
        return $endpoints[$operation] ?? null;
    }

    /**
     * تحضير الطلب
     */
    protected function prepareRequest(array $endpoint, array $data, array $options): array
    {
        $baseUrl = $this->environment === 'production' ? $this->production_url : $this->sandbox_url;
        $url = $baseUrl . $endpoint['path'];

        $request = [
            'method' => $endpoint['method'] ?? 'POST',
            'url' => $url,
            'headers' => $this->prepareHeaders($endpoint, $options),
            'data' => $this->prepareData($data, $endpoint),
            'timeout' => $this->timeout_settings['request_timeout'] ?? 30,
        ];

        // إضافة المصادقة
        $request = $this->addAuthentication($request, $options);

        return $request;
    }

    /**
     * تحضير الرؤوس
     */
    protected function prepareHeaders(array $endpoint, array $options): array
    {
        $headers = [
            'Content-Type' => $endpoint['content_type'] ?? 'application/json',
            'Accept' => $endpoint['accept'] ?? 'application/json',
            'User-Agent' => 'Hesabiai-Compliance-System/1.0',
        ];

        // إضافة رؤوس مخصصة
        if (isset($endpoint['headers'])) {
            $headers = array_merge($headers, $endpoint['headers']);
        }

        if (isset($options['headers'])) {
            $headers = array_merge($headers, $options['headers']);
        }

        return $headers;
    }

    /**
     * تحضير البيانات
     */
    protected function prepareData(array $data, array $endpoint): array
    {
        $format = $endpoint['data_format'] ?? 'json';

        switch ($format) {
            case 'json':
                return $data;
            case 'xml':
                return $this->convertToXml($data);
            case 'form':
                return $this->convertToFormData($data);
            default:
                return $data;
        }
    }

    /**
     * إضافة المصادقة
     */
    protected function addAuthentication(array $request, array $options): array
    {
        $authConfig = $this->authentication_config ?? [];
        $method = $this->authentication_method;

        switch ($method) {
            case 'api_key':
                $request['headers'][$authConfig['header_name'] ?? 'X-API-Key'] = $authConfig['api_key'];
                break;

            case 'oauth2':
                $token = $this->getOAuth2Token();
                $request['headers']['Authorization'] = "Bearer {$token}";
                break;

            case 'jwt':
                $token = $this->generateJWT();
                $request['headers']['Authorization'] = "Bearer {$token}";
                break;

            case 'basic_auth':
                $credentials = base64_encode($authConfig['username'] . ':' . $authConfig['password']);
                $request['headers']['Authorization'] = "Basic {$credentials}";
                break;

            case 'certificate':
                $request['certificate'] = $authConfig['certificate_path'];
                $request['private_key'] = $authConfig['private_key_path'];
                break;

            case 'signature':
                $signature = $this->generateSignature($request['data']);
                $request['headers']['X-Signature'] = $signature;
                break;
        }

        return $request;
    }

    /**
     * تنفيذ استدعاء API
     */
    protected function executeApiCall(array $request): array
    {
        $startTime = microtime(true);

        $httpClient = Http::timeout($request['timeout']);

        // إضافة الشهادات إذا كانت مطلوبة
        if (isset($request['certificate'])) {
            $httpClient = $httpClient->withOptions([
                'cert' => $request['certificate'],
                'ssl_key' => $request['private_key'],
            ]);
        }

        // إضافة الرؤوس
        $httpClient = $httpClient->withHeaders($request['headers']);

        // إجراء الطلب
        $response = match ($request['method']) {
            'GET' => $httpClient->get($request['url'], $request['data']),
            'POST' => $httpClient->post($request['url'], $request['data']),
            'PUT' => $httpClient->put($request['url'], $request['data']),
            'DELETE' => $httpClient->delete($request['url'], $request['data']),
            'PATCH' => $httpClient->patch($request['url'], $request['data']),
            default => throw new \Exception("طريقة HTTP غير مدعومة: {$request['method']}"),
        };

        $endTime = microtime(true);
        $responseTime = ($endTime - $startTime) * 1000; // بالميلي ثانية

        if (!$response->successful()) {
            throw new \Exception("فشل استدعاء API: {$response->status()} - {$response->body()}");
        }

        return [
            'status_code' => $response->status(),
            'headers' => $response->headers(),
            'body' => $response->body(),
            'json' => $response->json(),
            'response_time' => $responseTime,
        ];
    }

    /**
     * معالجة الاستجابة
     */
    protected function processResponse(array $response, array $endpoint): array
    {
        $data = $response['json'] ?? [];

        // التحقق من صحة الاستجابة
        if (isset($endpoint['validation_rules'])) {
            $this->validateResponse($data, $endpoint['validation_rules']);
        }

        // معالجة الأخطاء
        if (isset($data['error']) || isset($data['errors'])) {
            $errorMessage = $data['error']['message'] ?? $data['message'] ?? 'خطأ غير محدد';
            throw new \Exception("خطأ من API: {$errorMessage}");
        }

        return [
            'success' => true,
            'data' => $data,
            'status_code' => $response['status_code'],
            'response_time' => $response['response_time'],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * التحقق من صحة الاستجابة
     */
    protected function validateResponse(array $data, array $rules): void
    {
        foreach ($rules as $field => $rule) {
            if ($rule['required'] && !isset($data[$field])) {
                throw new \Exception("الحقل المطلوب {$field} مفقود في الاستجابة");
            }

            if (isset($data[$field]) && isset($rule['type'])) {
                $this->validateFieldType($data[$field], $rule['type'], $field);
            }
        }
    }

    /**
     * التحقق من نوع الحقل
     */
    protected function validateFieldType($value, string $expectedType, string $field): void
    {
        $actualType = gettype($value);
        
        if ($expectedType !== $actualType) {
            throw new \Exception("نوع الحقل {$field} غير صحيح. متوقع: {$expectedType}, فعلي: {$actualType}");
        }
    }

    /**
     * تسجيل استدعاء API
     */
    protected function logApiCall(string $operation, array $request): GovernmentApiCall
    {
        return GovernmentApiCall::create([
            'government_integration_id' => $this->id,
            'operation' => $operation,
            'request_data' => $request,
            'status' => 'pending',
            'started_at' => now(),
        ]);
    }

    /**
     * تحديث إحصائيات النجاح
     */
    protected function updateSuccessStats(float $responseTime): void
    {
        $this->increment('total_calls');
        $this->increment('successful_calls');
        
        // حساب متوسط وقت الاستجابة
        $newAverage = (($this->average_response_time * ($this->total_calls - 1)) + $responseTime) / $this->total_calls;
        
        $this->update([
            'last_successful_call' => now(),
            'average_response_time' => round($newAverage, 2),
            'connection_status' => 'connected',
        ]);
    }

    /**
     * تحديث إحصائيات الفشل
     */
    protected function updateFailureStats(): void
    {
        $this->increment('total_calls');
        $this->increment('failed_calls');
        
        $this->update([
            'last_failed_call' => now(),
            'connection_status' => 'error',
        ]);
    }

    /**
     * فحص صحة التكامل
     */
    public function checkHealth(): array
    {
        $healthEndpoint = $this->health_check_endpoint;
        
        if (!$healthEndpoint) {
            return [
                'status' => 'unknown',
                'message' => 'لا توجد نقطة فحص صحة محددة',
                'checked_at' => now(),
            ];
        }

        try {
            $response = Http::timeout(10)->get($healthEndpoint);
            
            $status = $response->successful() ? 'operational' : 'degraded';
            $message = $response->successful() ? 'النظام يعمل بشكل طبيعي' : 'النظام يواجه مشاكل';
            
            $this->update([
                'last_health_check' => now(),
                'api_status' => $status,
            ]);

            return [
                'status' => $status,
                'message' => $message,
                'response_time' => $response->transferStats?->getTransferTime() ?? 0,
                'checked_at' => now(),
            ];

        } catch (\Exception $e) {
            $this->update([
                'last_health_check' => now(),
                'api_status' => 'major_outage',
            ]);

            return [
                'status' => 'major_outage',
                'message' => 'فشل في الاتصال: ' . $e->getMessage(),
                'checked_at' => now(),
            ];
        }
    }

    /**
     * حساب نسبة التوفر
     */
    public function calculateUptime(int $days = 30): float
    {
        $totalCalls = $this->apiCalls()
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        if ($totalCalls === 0) {
            return 100.0;
        }

        $successfulCalls = $this->apiCalls()
            ->where('created_at', '>=', now()->subDays($days))
            ->where('status', 'success')
            ->count();

        return round(($successfulCalls / $totalCalls) * 100, 2);
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('service_category', $category);
    }

    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    public function scopeOperational($query)
    {
        return $query->where('api_status', 'operational');
    }

    public function scopeMandatory($query)
    {
        return $query->where('is_mandatory', true);
    }

    // طرق مساعدة إضافية
    protected function getOAuth2Token(): string
    {
        // منطق الحصول على OAuth2 token
        return Cache::remember("oauth2_token_{$this->id}", 3600, function () {
            // استدعاء endpoint للحصول على token
            return 'sample_token';
        });
    }

    protected function generateJWT(): string
    {
        // منطق توليد JWT token
        return 'sample_jwt_token';
    }

    protected function generateSignature(array $data): string
    {
        // منطق توليد التوقيع الرقمي
        return hash_hmac('sha256', json_encode($data), $this->authentication_config['secret_key'] ?? '');
    }

    protected function convertToXml(array $data): string
    {
        // منطق تحويل البيانات إلى XML
        return '<xml></xml>';
    }

    protected function convertToFormData(array $data): array
    {
        // منطق تحويل البيانات إلى form data
        return $data;
    }
}
