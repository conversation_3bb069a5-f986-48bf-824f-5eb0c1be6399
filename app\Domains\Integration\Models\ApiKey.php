<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * نموذج مفاتيح API المتقدمة
 * يدير جميع مفاتيح API مع أمان متقدم وإدارة صلاحيات
 */
class ApiKey extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'api_gateway_id',
        'key_id',
        'name',
        'description',
        'key_hash',
        'key_prefix',
        'key_type',
        'status',
        'owner_type',
        'owner_id',
        'permissions',
        'scopes',
        'rate_limit_config',
        'ip_whitelist',
        'allowed_endpoints',
        'blocked_endpoints',
        'allowed_methods',
        'allowed_origins',
        'webhook_urls',
        'expires_at',
        'last_used_at',
        'usage_count',
        'rate_limit_remaining',
        'rate_limit_reset_at',
        'security_config',
        'monitoring_config',
        'analytics_enabled',
        'logging_level',
        'environment',
        'tags',
        'metadata',
        'created_by',
        'revoked_at',
        'revoked_by',
        'revocation_reason',
    ];

    protected $casts = [
        'permissions' => 'array',
        'scopes' => 'array',
        'rate_limit_config' => 'array',
        'ip_whitelist' => 'array',
        'allowed_endpoints' => 'array',
        'blocked_endpoints' => 'array',
        'allowed_methods' => 'array',
        'allowed_origins' => 'array',
        'webhook_urls' => 'array',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'rate_limit_reset_at' => 'datetime',
        'security_config' => 'array',
        'monitoring_config' => 'array',
        'analytics_enabled' => 'boolean',
        'tags' => 'array',
        'metadata' => 'array',
        'revoked_at' => 'datetime',
    ];

    protected $hidden = [
        'key_hash',
    ];

    /**
     * أنواع المفاتيح
     */
    const KEY_TYPES = [
        'public' => 'Public API Key',
        'private' => 'Private API Key',
        'partner' => 'Partner API Key',
        'internal' => 'Internal API Key',
        'webhook' => 'Webhook API Key',
        'mcp' => 'MCP API Key',
        'temporary' => 'Temporary API Key',
        'service' => 'Service API Key',
    ];

    /**
     * حالات المفتاح
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'suspended' => 'معلق',
        'revoked' => 'ملغي',
        'expired' => 'منتهي الصلاحية',
        'pending' => 'قيد الانتظار',
    ];

    /**
     * مستويات التسجيل
     */
    const LOGGING_LEVELS = [
        'none' => 'بدون تسجيل',
        'basic' => 'أساسي',
        'detailed' => 'مفصل',
        'full' => 'كامل',
        'debug' => 'تصحيح',
    ];

    /**
     * البيئات
     */
    const ENVIRONMENTS = [
        'development' => 'تطوير',
        'staging' => 'تجريبي',
        'production' => 'إنتاج',
        'sandbox' => 'صندوق رمل',
    ];

    /**
     * الصلاحيات الافتراضية
     */
    const DEFAULT_PERMISSIONS = [
        'read' => 'قراءة',
        'write' => 'كتابة',
        'delete' => 'حذف',
        'admin' => 'إدارة',
    ];

    /**
     * النطاقات المتاحة
     */
    const AVAILABLE_SCOPES = [
        'accounting:read' => 'قراءة المحاسبة',
        'accounting:write' => 'كتابة المحاسبة',
        'taxation:read' => 'قراءة الضرائب',
        'taxation:write' => 'كتابة الضرائب',
        'hr:read' => 'قراءة الموارد البشرية',
        'hr:write' => 'كتابة الموارد البشرية',
        'projects:read' => 'قراءة المشاريع',
        'projects:write' => 'كتابة المشاريع',
        'support:read' => 'قراءة الدعم',
        'support:write' => 'كتابة الدعم',
        'ecommerce:read' => 'قراءة التجارة الإلكترونية',
        'ecommerce:write' => 'كتابة التجارة الإلكترونية',
        'payments:read' => 'قراءة المدفوعات',
        'payments:write' => 'كتابة المدفوعات',
        'webhooks:manage' => 'إدارة الويب هوك',
        'analytics:read' => 'قراءة التحليلات',
        'admin:full' => 'إدارة كاملة',
    ];

    /**
     * العلاقة مع بوابة API
     */
    public function apiGateway(): BelongsTo
    {
        return $this->belongsTo(ApiGateway::class);
    }

    /**
     * العلاقة مع المالك (polymorphic)
     */
    public function owner()
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع سجلات الاستخدام
     */
    public function usageLogs(): HasMany
    {
        return $this->hasMany(ApiRequestLog::class);
    }

    /**
     * العلاقة مع منشئ المفتاح
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * العلاقة مع ملغي المفتاح
     */
    public function revoker(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'revoked_by');
    }

    /**
     * إنشاء مفتاح API جديد
     */
    public static function generateApiKey(array $data): self
    {
        // توليد مفتاح فريد
        $keyValue = self::generateUniqueKey();
        $keyPrefix = $data['key_prefix'] ?? self::generateKeyPrefix($data['key_type'] ?? 'public');
        $fullKey = $keyPrefix . '_' . $keyValue;

        // إنشاء المفتاح
        $apiKey = self::create([
            'api_gateway_id' => $data['api_gateway_id'],
            'key_id' => 'KEY_' . strtoupper(uniqid()),
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'key_hash' => Hash::make($fullKey),
            'key_prefix' => $keyPrefix,
            'key_type' => $data['key_type'] ?? 'public',
            'status' => 'active',
            'owner_type' => $data['owner_type'] ?? null,
            'owner_id' => $data['owner_id'] ?? null,
            'permissions' => $data['permissions'] ?? ['read'],
            'scopes' => $data['scopes'] ?? [],
            'rate_limit_config' => $data['rate_limit_config'] ?? self::getDefaultRateLimit($data['key_type'] ?? 'public'),
            'ip_whitelist' => $data['ip_whitelist'] ?? [],
            'allowed_endpoints' => $data['allowed_endpoints'] ?? [],
            'blocked_endpoints' => $data['blocked_endpoints'] ?? [],
            'allowed_methods' => $data['allowed_methods'] ?? ['GET', 'POST'],
            'allowed_origins' => $data['allowed_origins'] ?? [],
            'webhook_urls' => $data['webhook_urls'] ?? [],
            'expires_at' => $data['expires_at'] ?? null,
            'security_config' => $data['security_config'] ?? self::getDefaultSecurityConfig(),
            'monitoring_config' => $data['monitoring_config'] ?? self::getDefaultMonitoringConfig(),
            'analytics_enabled' => $data['analytics_enabled'] ?? true,
            'logging_level' => $data['logging_level'] ?? 'basic',
            'environment' => $data['environment'] ?? 'production',
            'tags' => $data['tags'] ?? [],
            'metadata' => $data['metadata'] ?? [],
            'created_by' => auth()->id(),
        ]);

        // إرجاع المفتاح مع القيمة الكاملة (مرة واحدة فقط)
        $apiKey->setAttribute('full_key', $fullKey);

        return $apiKey;
    }

    /**
     * التحقق من صحة مفتاح API
     */
    public static function validateApiKey(string $key): ?self
    {
        // استخراج البادئة
        $parts = explode('_', $key, 2);
        if (count($parts) !== 2) {
            return null;
        }

        $prefix = $parts[0];

        // البحث عن المفتاح بالبادئة
        $apiKeys = self::where('key_prefix', $prefix)
                      ->where('status', 'active')
                      ->get();

        foreach ($apiKeys as $apiKey) {
            if (Hash::check($key, $apiKey->key_hash)) {
                // التحقق من انتهاء الصلاحية
                if ($apiKey->isExpired()) {
                    $apiKey->markAsExpired();
                    return null;
                }

                // التحقق من Rate Limiting
                if (!$apiKey->checkRateLimit()) {
                    return null;
                }

                // تحديث آخر استخدام
                $apiKey->updateLastUsed();

                return $apiKey;
            }
        }

        return null;
    }

    /**
     * التحقق من الصلاحية
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];

        // التحقق من الصلاحية المباشرة
        if (in_array($permission, $permissions)) {
            return true;
        }

        // التحقق من صلاحية الإدارة الكاملة
        if (in_array('admin', $permissions)) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من النطاق
     */
    public function hasScope(string $scope): bool
    {
        $scopes = $this->scopes ?? [];

        // التحقق من النطاق المباشر
        if (in_array($scope, $scopes)) {
            return true;
        }

        // التحقق من النطاق الكامل
        if (in_array('admin:full', $scopes)) {
            return true;
        }

        // التحقق من النطاق الجزئي (مثل accounting:* يشمل accounting:read)
        foreach ($scopes as $allowedScope) {
            if (str_ends_with($allowedScope, ':*')) {
                $scopePrefix = str_replace(':*', ':', $allowedScope);
                if (str_starts_with($scope, $scopePrefix)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * التحقق من نقطة النهاية المسموحة
     */
    public function canAccessEndpoint(string $endpoint): bool
    {
        // التحقق من القائمة المحظورة أولاً
        if (!empty($this->blocked_endpoints) && in_array($endpoint, $this->blocked_endpoints)) {
            return false;
        }

        // إذا لم تكن هناك قائمة مسموحة، فالوصول مسموح
        if (empty($this->allowed_endpoints)) {
            return true;
        }

        // التحقق من القائمة المسموحة
        return in_array($endpoint, $this->allowed_endpoints);
    }

    /**
     * التحقق من الطريقة المسموحة
     */
    public function canUseMethod(string $method): bool
    {
        $allowedMethods = $this->allowed_methods ?? ['GET', 'POST'];
        return in_array(strtoupper($method), $allowedMethods);
    }

    /**
     * التحقق من IP المسموح
     */
    public function canAccessFromIp(string $ip): bool
    {
        $whitelist = $this->ip_whitelist ?? [];

        // إذا لم تكن هناك قائمة بيضاء، فالوصول مسموح
        if (empty($whitelist)) {
            return true;
        }

        // التحقق من IP المباشر
        if (in_array($ip, $whitelist)) {
            return true;
        }

        // التحقق من نطاقات IP (CIDR)
        foreach ($whitelist as $allowedIp) {
            if (str_contains($allowedIp, '/')) {
                if ($this->ipInRange($ip, $allowedIp)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * التحقق من Rate Limiting
     */
    public function checkRateLimit(): bool
    {
        $rateLimitConfig = $this->rate_limit_config ?? [];

        if (!($rateLimitConfig['enabled'] ?? true)) {
            return true;
        }

        $limit = $rateLimitConfig['requests_per_minute'] ?? 60;
        $window = $rateLimitConfig['window_minutes'] ?? 1;

        $cacheKey = "api_rate_limit:{$this->id}";
        $current = Cache::get($cacheKey, 0);

        if ($current >= $limit) {
            return false;
        }

        // تحديث العداد
        Cache::put($cacheKey, $current + 1, now()->addMinutes($window));

        // تحديث المتبقي
        $this->update([
            'rate_limit_remaining' => $limit - ($current + 1),
            'rate_limit_reset_at' => now()->addMinutes($window),
        ]);

        return true;
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * التحقق من الإلغاء
     */
    public function isRevoked(): bool
    {
        return $this->status === 'revoked' || $this->revoked_at;
    }

    /**
     * التحقق من النشاط
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->isExpired() && !$this->isRevoked();
    }

    /**
     * تحديث آخر استخدام
     */
    public function updateLastUsed(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * تمييز كمنتهي الصلاحية
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * إلغاء المفتاح
     */
    public function revoke(string $reason = null, int $revokedBy = null): void
    {
        $this->update([
            'status' => 'revoked',
            'revoked_at' => now(),
            'revoked_by' => $revokedBy ?? auth()->id(),
            'revocation_reason' => $reason,
        ]);

        // مسح التخزين المؤقت
        $this->clearCache();
    }

    /**
     * تجديد المفتاح
     */
    public function regenerate(): string
    {
        $newKeyValue = self::generateUniqueKey();
        $fullKey = $this->key_prefix . '_' . $newKeyValue;

        $this->update([
            'key_hash' => Hash::make($fullKey),
            'usage_count' => 0,
            'last_used_at' => null,
            'rate_limit_remaining' => null,
            'rate_limit_reset_at' => null,
        ]);

        $this->clearCache();

        return $fullKey;
    }

    /**
     * الحصول على إحصائيات الاستخدام
     */
    public function getUsageStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $logs = $this->usageLogs()
                    ->where('created_at', '>=', $startDate)
                    ->get();

        return [
            'total_requests' => $logs->count(),
            'successful_requests' => $logs->where('status', 'success')->count(),
            'failed_requests' => $logs->where('status', 'failed')->count(),
            'average_response_time' => $logs->avg('response_time'),
            'most_used_endpoints' => $logs->groupBy('endpoint')
                                         ->map->count()
                                         ->sortDesc()
                                         ->take(10)
                                         ->toArray(),
            'daily_usage' => $logs->groupBy(fn($log) => $log->created_at->format('Y-m-d'))
                                  ->map->count()
                                  ->toArray(),
        ];
    }

    /**
     * مسح التخزين المؤقت
     */
    protected function clearCache(): void
    {
        Cache::forget("api_rate_limit:{$this->id}");
        Cache::forget("api_key_validation:{$this->key_prefix}");
    }

    // طرق مساعدة ثابتة
    protected static function generateUniqueKey(): string
    {
        return Str::random(32);
    }

    protected static function generateKeyPrefix(string $keyType): string
    {
        return match ($keyType) {
            'public' => 'pk',
            'private' => 'sk',
            'partner' => 'pt',
            'internal' => 'in',
            'webhook' => 'wh',
            'mcp' => 'mcp',
            'temporary' => 'tmp',
            'service' => 'svc',
            default => 'api',
        };
    }

    protected static function getDefaultRateLimit(string $keyType): array
    {
        return match ($keyType) {
            'public' => ['enabled' => true, 'requests_per_minute' => 60, 'window_minutes' => 1],
            'private' => ['enabled' => true, 'requests_per_minute' => 300, 'window_minutes' => 1],
            'partner' => ['enabled' => true, 'requests_per_minute' => 1000, 'window_minutes' => 1],
            'internal' => ['enabled' => false],
            'webhook' => ['enabled' => true, 'requests_per_minute' => 100, 'window_minutes' => 1],
            'mcp' => ['enabled' => true, 'requests_per_minute' => 200, 'window_minutes' => 1],
            'temporary' => ['enabled' => true, 'requests_per_minute' => 30, 'window_minutes' => 1],
            'service' => ['enabled' => true, 'requests_per_minute' => 500, 'window_minutes' => 1],
            default => ['enabled' => true, 'requests_per_minute' => 60, 'window_minutes' => 1],
        };
    }

    protected static function getDefaultSecurityConfig(): array
    {
        return [
            'require_https' => true,
            'validate_origin' => false,
            'log_all_requests' => true,
            'block_suspicious_activity' => true,
        ];
    }

    protected static function getDefaultMonitoringConfig(): array
    {
        return [
            'track_usage' => true,
            'alert_on_high_usage' => true,
            'alert_threshold' => 80,
            'generate_reports' => true,
        ];
    }

    protected function ipInRange(string $ip, string $range): bool
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    /**
     * Perform comprehensive security validations
     */
    public function performAdvancedSecurityValidations(): void
    {
        // Check if key is suspended
        if ($this->status === 'suspended') {
            throw new \Exception('API key is suspended', 403);
        }

        // Check rate limits
        $this->checkAdvancedRateLimits();

        // Check IP restrictions
        $this->checkAdvancedIpRestrictions();

        // Check time-based restrictions
        $this->checkAdvancedTimeRestrictions();

        // Check quota limits
        $this->checkAdvancedQuotaLimits();

        // Check security policies
        $this->checkAdvancedSecurityPolicies();
    }

    /**
     * Check advanced rate limits for the API key
     */
    protected function checkAdvancedRateLimits(): void
    {
        $rateLimits = $this->rate_limits ?? [];

        foreach ($rateLimits as $limitType => $config) {
            $this->checkSpecificRateLimit($limitType, $config);
        }
    }

    /**
     * Check specific rate limit
     */
    protected function checkSpecificRateLimit(string $limitType, array $config): void
    {
        $limit = $config['limit'] ?? 100;
        $window = $config['window'] ?? 3600; // 1 hour default

        $key = "rate_limit:api_key:{$this->id}:{$limitType}";
        $current = Redis::incr($key);

        if ($current === 1) {
            Redis::expire($key, $window);
        }

        if ($current > $limit) {
            throw new \Exception("Rate limit exceeded for {$limitType}: {$current}/{$limit}", 429);
        }
    }

    /**
     * Check advanced IP restrictions
     */
    protected function checkAdvancedIpRestrictions(): void
    {
        $ipRestrictions = $this->ip_restrictions ?? [];

        if (empty($ipRestrictions)) {
            return;
        }

        $clientIp = request()->ip();
        $allowedIps = $ipRestrictions['allowed_ips'] ?? [];
        $blockedIps = $ipRestrictions['blocked_ips'] ?? [];

        // Check blocked IPs first
        if (in_array($clientIp, $blockedIps)) {
            throw new \Exception('IP address is blocked', 403);
        }

        // Check allowed IPs if whitelist is configured
        if (!empty($allowedIps) && !in_array($clientIp, $allowedIps)) {
            throw new \Exception('IP address not in whitelist', 403);
        }
    }

    /**
     * Check advanced time-based restrictions
     */
    protected function checkAdvancedTimeRestrictions(): void
    {
        $timeRestrictions = $this->time_restrictions ?? [];

        if (empty($timeRestrictions)) {
            return;
        }

        $now = now();
        $currentHour = $now->hour;
        $currentDay = $now->dayOfWeek;

        // Check allowed hours
        $allowedHours = $timeRestrictions['allowed_hours'] ?? [];
        if (!empty($allowedHours) && !in_array($currentHour, $allowedHours)) {
            throw new \Exception('API access not allowed at this time', 403);
        }

        // Check allowed days
        $allowedDays = $timeRestrictions['allowed_days'] ?? [];
        if (!empty($allowedDays) && !in_array($currentDay, $allowedDays)) {
            throw new \Exception('API access not allowed on this day', 403);
        }
    }

    /**
     * Check advanced quota limits
     */
    protected function checkAdvancedQuotaLimits(): void
    {
        $quotaLimits = $this->quota_limits ?? [];

        foreach ($quotaLimits as $quotaType => $config) {
            $this->checkSpecificQuota($quotaType, $config);
        }
    }

    /**
     * Check specific quota limit
     */
    protected function checkSpecificQuota(string $quotaType, array $config): void
    {
        $limit = $config['limit'] ?? 1000;
        $period = $config['period'] ?? 'monthly';

        $usage = $this->getQuotaUsage($quotaType, $period);

        if ($usage >= $limit) {
            throw new \Exception("Quota exceeded for {$quotaType}: {$usage}/{$limit}", 429);
        }
    }

    /**
     * Get quota usage for a specific type and period
     */
    protected function getQuotaUsage(string $quotaType, string $period): int
    {
        $startDate = $this->getQuotaPeriodStart($period);

        return $this->requestLogs()
            ->where('created_at', '>=', $startDate)
            ->where('quota_type', $quotaType)
            ->count();
    }

    /**
     * Get quota period start date
     */
    protected function getQuotaPeriodStart(string $period): Carbon
    {
        return match ($period) {
            'daily' => now()->startOfDay(),
            'weekly' => now()->startOfWeek(),
            'monthly' => now()->startOfMonth(),
            'yearly' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * Check advanced security policies
     */
    protected function checkAdvancedSecurityPolicies(): void
    {
        $securityPolicies = $this->security_policies ?? [];

        // Check for suspicious activity
        if ($securityPolicies['detect_anomalies'] ?? false) {
            $this->detectAnomalousActivity();
        }

        // Check for concurrent usage
        if ($securityPolicies['max_concurrent_requests'] ?? 0 > 0) {
            $this->checkConcurrentRequests($securityPolicies['max_concurrent_requests']);
        }

        // Check for geographic restrictions
        if (!empty($securityPolicies['allowed_countries'] ?? [])) {
            $this->checkGeographicRestrictions($securityPolicies['allowed_countries']);
        }
    }

    /**
     * Detect anomalous activity
     */
    protected function detectAnomalousActivity(): void
    {
        $recentRequests = $this->requestLogs()
            ->where('created_at', '>=', now()->subHour())
            ->count();

        $averageRequests = $this->requestLogs()
            ->where('created_at', '>=', now()->subDays(7))
            ->count() / 7 / 24; // Average per hour over last week

        // If current hour requests are 10x the average, flag as anomalous
        if ($recentRequests > ($averageRequests * 10)) {
            Log::warning('Anomalous API key activity detected', [
                'api_key_id' => $this->id,
                'recent_requests' => $recentRequests,
                'average_requests' => $averageRequests,
            ]);

            throw new \Exception('Anomalous activity detected', 429);
        }
    }

    /**
     * Check concurrent requests
     */
    protected function checkConcurrentRequests(int $maxConcurrent): void
    {
        $key = "concurrent:api_key:{$this->id}";
        $current = Redis::incr($key);
        Redis::expire($key, 60); // 1 minute expiry

        if ($current > $maxConcurrent) {
            Redis::decr($key);
            throw new \Exception('Too many concurrent requests', 429);
        }

        // Decrement after request (this would be called in a finally block)
        register_shutdown_function(function () use ($key) {
            Redis::decr($key);
        });
    }

    /**
     * Check geographic restrictions
     */
    protected function checkGeographicRestrictions(array $allowedCountries): void
    {
        $clientIp = request()->ip();
        $country = $this->getCountryFromIp($clientIp);

        if (!in_array($country, $allowedCountries)) {
            throw new \Exception('Geographic access restriction', 403);
        }
    }

    /**
     * Get country from IP address
     */
    protected function getCountryFromIp(string $ip): string
    {
        // This would integrate with a GeoIP service
        // For now, return a default value
        return 'US';
    }
}
