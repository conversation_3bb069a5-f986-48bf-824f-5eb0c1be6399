<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج طلب التوظيف - مع الفلترة الذكية والتقييم بالذكاء الاصطناعي
 */
class JobApplication extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'job_posting_id',
        'candidate_id',
        'application_source',
        'status',
        'stage',
        'priority',
        'applied_at',
        'reviewed_at',
        'interviewed_at',
        'decision_date',
        'start_date',
        
        // AI Scoring
        'ai_score',
        'skills_match_score',
        'experience_match_score',
        'education_match_score',
        'overall_fit_score',
        'ai_analysis',
        
        // Application Data
        'cover_letter',
        'expected_salary',
        'available_start_date',
        'notice_period',
        'willing_to_relocate',
        'visa_sponsorship_required',
        
        // Interview Data
        'interview_feedback',
        'interview_score',
        'interviewer_notes',
        
        // Decision Data
        'rejection_reason',
        'offer_details',
        'salary_offered',
        'benefits_offered',
        
        'notes',
        'metadata',
    ];

    protected $casts = [
        'applied_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'interviewed_at' => 'datetime',
        'decision_date' => 'datetime',
        'start_date' => 'date',
        'available_start_date' => 'date',
        'ai_score' => 'decimal:2',
        'skills_match_score' => 'decimal:2',
        'experience_match_score' => 'decimal:2',
        'education_match_score' => 'decimal:2',
        'overall_fit_score' => 'decimal:2',
        'interview_score' => 'decimal:2',
        'expected_salary' => 'decimal:2',
        'salary_offered' => 'decimal:2',
        'willing_to_relocate' => 'boolean',
        'visa_sponsorship_required' => 'boolean',
        'ai_analysis' => 'array',
        'offer_details' => 'array',
        'benefits_offered' => 'array',
        'metadata' => 'array',
    ];

    /**
     * حالات الطلب
     */
    const STATUSES = [
        'SUBMITTED' => 'مقدم',
        'UNDER_REVIEW' => 'قيد المراجعة',
        'SHORTLISTED' => 'مرشح',
        'INTERVIEWED' => 'تمت المقابلة',
        'SECOND_INTERVIEW' => 'مقابلة ثانية',
        'REFERENCE_CHECK' => 'فحص المراجع',
        'OFFERED' => 'تم العرض',
        'HIRED' => 'تم التوظيف',
        'REJECTED' => 'مرفوض',
        'WITHDRAWN' => 'منسحب',
        'ON_HOLD' => 'معلق',
    ];

    /**
     * مراحل التوظيف
     */
    const STAGES = [
        'APPLICATION' => 'التقديم',
        'SCREENING' => 'الفرز الأولي',
        'PHONE_INTERVIEW' => 'مقابلة هاتفية',
        'TECHNICAL_ASSESSMENT' => 'تقييم تقني',
        'FACE_TO_FACE_INTERVIEW' => 'مقابلة شخصية',
        'PANEL_INTERVIEW' => 'مقابلة لجنة',
        'FINAL_INTERVIEW' => 'مقابلة نهائية',
        'BACKGROUND_CHECK' => 'فحص الخلفية',
        'OFFER_NEGOTIATION' => 'التفاوض على العرض',
        'ONBOARDING' => 'التأهيل',
    ];

    /**
     * مصادر التقديم
     */
    const APPLICATION_SOURCES = [
        'COMPANY_WEBSITE' => 'موقع الشركة',
        'LINKEDIN' => 'لينكدإن',
        'BAYT' => 'بيت.كوم',
        'AKHTABOOT' => 'اختبوط',
        'EMPLOI_MA' => 'Emploi.ma',
        'REFERRAL' => 'إحالة',
        'RECRUITMENT_AGENCY' => 'وكالة توظيف',
        'UNIVERSITY' => 'جامعة',
        'WALK_IN' => 'زيارة مباشرة',
        'SOCIAL_MEDIA' => 'وسائل التواصل',
        'OTHER' => 'أخرى',
    ];

    /**
     * الأولويات
     */
    const PRIORITIES = [
        'LOW' => 'منخفضة',
        'NORMAL' => 'عادية',
        'HIGH' => 'عالية',
        'URGENT' => 'عاجلة',
    ];

    /**
     * إعلان التوظيف
     */
    public function jobPosting(): BelongsTo
    {
        return $this->belongsTo(JobPosting::class);
    }

    /**
     * المرشح
     */
    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    /**
     * المقابلات
     */
    public function interviews(): HasMany
    {
        return $this->hasMany(Interview::class);
    }

    /**
     * التقييمات
     */
    public function assessments(): HasMany
    {
        return $this->hasMany(CandidateAssessment::class);
    }

    /**
     * المستندات
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(\App\Domains\Shared\Models\Document::class, 'documentable');
    }

    /**
     * تحديث النقاط بالذكاء الاصطناعي
     */
    public function updateAIScoring(): void
    {
        $aiService = app(\App\Domains\HR\Services\AIRecruitmentService::class);
        $scoring = $aiService->scoreCandidate($this);

        $this->update([
            'ai_score' => $scoring['overall_score'],
            'skills_match_score' => $scoring['skills_match'],
            'experience_match_score' => $scoring['experience_match'],
            'education_match_score' => $scoring['education_match'],
            'overall_fit_score' => $scoring['cultural_fit'],
            'ai_analysis' => $scoring['analysis'],
        ]);
    }

    /**
     * الانتقال للمرحلة التالية
     */
    public function moveToNextStage(): bool
    {
        $stages = array_keys(self::STAGES);
        $currentIndex = array_search($this->stage, $stages);
        
        if ($currentIndex !== false && $currentIndex < count($stages) - 1) {
            $nextStage = $stages[$currentIndex + 1];
            
            $this->update([
                'stage' => $nextStage,
                'status' => $this->getStatusForStage($nextStage),
            ]);

            return true;
        }

        return false;
    }

    /**
     * الحصول على الحالة المناسبة للمرحلة
     */
    protected function getStatusForStage(string $stage): string
    {
        return match ($stage) {
            'APPLICATION' => 'SUBMITTED',
            'SCREENING' => 'UNDER_REVIEW',
            'PHONE_INTERVIEW', 'TECHNICAL_ASSESSMENT', 'FACE_TO_FACE_INTERVIEW', 
            'PANEL_INTERVIEW', 'FINAL_INTERVIEW' => 'INTERVIEWED',
            'BACKGROUND_CHECK' => 'REFERENCE_CHECK',
            'OFFER_NEGOTIATION' => 'OFFERED',
            'ONBOARDING' => 'HIRED',
            default => 'UNDER_REVIEW',
        };
    }

    /**
     * رفض الطلب
     */
    public function reject(string $reason): bool
    {
        $this->update([
            'status' => 'REJECTED',
            'rejection_reason' => $reason,
            'decision_date' => now(),
        ]);

        // إرسال إشعار للمرشح
        $this->candidate->notify(new \App\Notifications\ApplicationRejected($this));

        return true;
    }

    /**
     * قبول الطلب وتقديم عرض
     */
    public function makeOffer(array $offerDetails): bool
    {
        $this->update([
            'status' => 'OFFERED',
            'salary_offered' => $offerDetails['salary'] ?? null,
            'offer_details' => $offerDetails,
            'benefits_offered' => $offerDetails['benefits'] ?? [],
            'decision_date' => now(),
        ]);

        // إرسال إشعار للمرشح
        $this->candidate->notify(new \App\Notifications\JobOfferMade($this));

        return true;
    }

    /**
     * توظيف المرشح
     */
    public function hire(\Carbon\Carbon $startDate = null): Employee
    {
        $this->update([
            'status' => 'HIRED',
            'start_date' => $startDate ?? now()->addWeeks(2),
            'decision_date' => now(),
        ]);

        // إنشاء سجل موظف جديد
        $employee = Employee::create([
            'first_name' => $this->candidate->first_name,
            'last_name' => $this->candidate->last_name,
            'email' => $this->candidate->email,
            'phone' => $this->candidate->phone,
            'position_id' => $this->jobPosting->position_id,
            'department_id' => $this->jobPosting->department_id,
            'hire_date' => $this->start_date,
            'basic_salary' => $this->salary_offered,
            'status' => 'PROBATION',
            'probation_end_date' => $this->start_date->addMonths(3),
        ]);

        // ربط المرشح بالموظف
        $this->candidate->update(['employee_id' => $employee->id]);

        // وضع علامة "تم الشغل" على الإعلان
        $this->jobPosting->markAsFilled($this);

        return $employee;
    }

    /**
     * جدولة مقابلة
     */
    public function scheduleInterview(array $interviewData): Interview
    {
        return $this->interviews()->create(array_merge($interviewData, [
            'job_application_id' => $this->id,
            'candidate_id' => $this->candidate_id,
            'job_posting_id' => $this->job_posting_id,
        ]));
    }

    /**
     * الحصول على التقدم في المراحل
     */
    public function getProgressPercentage(): float
    {
        $stages = array_keys(self::STAGES);
        $currentIndex = array_search($this->stage, $stages);
        
        if ($currentIndex === false) {
            return 0;
        }

        return ($currentIndex + 1) / count($stages) * 100;
    }

    /**
     * التحقق من إمكانية الانتقال للمرحلة التالية
     */
    public function canMoveToNextStage(): bool
    {
        return !in_array($this->status, ['HIRED', 'REJECTED', 'WITHDRAWN']);
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق حسب المرحلة
     */
    public function scopeByStage($query, string $stage)
    {
        return $query->where('stage', $stage);
    }

    /**
     * نطاق للطلبات النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['HIRED', 'REJECTED', 'WITHDRAWN']);
    }

    /**
     * نطاق حسب النقاط
     */
    public function scopeByScore($query, float $minScore)
    {
        return $query->where('ai_score', '>=', $minScore);
    }

    /**
     * نطاق للمرشحين المميزين
     */
    public function scopeTopCandidates($query, float $threshold = 80)
    {
        return $query->where('ai_score', '>=', $threshold)
            ->orderByDesc('ai_score');
    }

    /**
     * نطاق حسب مصدر التقديم
     */
    public function scopeBySource($query, string $source)
    {
        return $query->where('application_source', $source);
    }

    /**
     * نطاق للطلبات المتأخرة في المراجعة
     */
    public function scopeOverdue($query, int $days = 7)
    {
        return $query->where('status', 'SUBMITTED')
            ->where('applied_at', '<', now()->subDays($days));
    }
}
