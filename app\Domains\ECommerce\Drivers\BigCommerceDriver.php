<?php

namespace App\Domains\ECommerce\Drivers;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Exceptions\ECommerceApiException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * برنامج تشغيل BigCommerce
 * يدير التكامل مع منصة BigCommerce
 */
class BigCommerceDriver extends AbstractECommercePlatformDriver
{
    protected string $platformName = 'bigcommerce';
    protected string $apiVersion = 'v3';
    protected int $maxPageSize = 250;
    protected int $defaultPageSize = 50;
    protected int $maxRequestsPerSecond = 20;
    protected int $maxRequestsPerMinute = 400;

    /**
     * الحصول على نقطة نهاية الاختبار
     */
    protected function getTestEndpoint(): string
    {
        return 'store';
    }

    /**
     * الحصول على URL الأساسي للAPI
     */
    public function getApiBaseUrl(ECommerceIntegration $integration): string
    {
        $storeHash = $integration->authentication_config['store_hash'] ?? '';
        return "https://api.bigcommerce.com/stores/{$storeHash}/" . $this->apiVersion;
    }

    /**
     * الحصول على headers المصادقة
     */
    protected function getAuthHeaders(ECommerceIntegration $integration): array
    {
        $accessToken = $integration->authentication_config['access_token'] ?? '';

        return [
            'X-Auth-Token' => $accessToken,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * الحصول على معلومات المتجر
     */
    public function getStoreInfo(ECommerceIntegration $integration): array
    {
        $response = $this->makeApiRequest('GET', 'store', [], $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب المنتجات من BigCommerce
     */
    public function getProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'page' => $options['page'] ?? 1,
            'include' => 'variants,images,custom_fields,bulk_pricing_rules,primary_image,videos',
        ];

        if (isset($options['name'])) {
            $params['name'] = $options['name'];
        }

        if (isset($options['sku'])) {
            $params['sku'] = $options['sku'];
        }

        if (isset($options['type'])) {
            $params['type'] = $options['type'];
        }

        if (isset($options['availability'])) {
            $params['availability'] = $options['availability'];
        }

        if (isset($options['is_visible'])) {
            $params['is_visible'] = $options['is_visible'];
        }

        if (isset($options['date_modified'])) {
            $params['date_modified'] = $options['date_modified'];
        }

        if (isset($options['categories'])) {
            $params['categories:in'] = $options['categories'];
        }

        $response = $this->makeApiRequest('GET', 'catalog/products', $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب منتج واحد من BigCommerce
     */
    public function getProduct(ECommerceIntegration $integration, string $productId): array
    {
        $params = ['include' => 'variants,images,custom_fields,bulk_pricing_rules,primary_image,videos'];
        $response = $this->makeApiRequest('GET', "catalog/products/{$productId}", $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * إنشاء منتج في BigCommerce
     */
    public function createProduct(ECommerceIntegration $integration, array $productData): array
    {
        $data = $this->transformToExternalFormat($productData, 'product');
        $response = $this->makeApiRequest('POST', 'catalog/products', $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * تحديث منتج في BigCommerce
     */
    public function updateProduct(ECommerceIntegration $integration, string $productId, array $productData): array
    {
        $data = $this->transformToExternalFormat($productData, 'product');
        $response = $this->makeApiRequest('PUT', "catalog/products/{$productId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * حذف منتج من BigCommerce
     */
    public function deleteProduct(ECommerceIntegration $integration, string $productId): array
    {
        $this->makeApiRequest('DELETE', "catalog/products/{$productId}", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب الطلبات من BigCommerce
     */
    public function getOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'page' => $options['page'] ?? 1,
        ];

        if (isset($options['status_id'])) {
            $params['status_id'] = $options['status_id'];
        }

        if (isset($options['min_date_created'])) {
            $params['min_date_created'] = $options['min_date_created'];
        }

        if (isset($options['max_date_created'])) {
            $params['max_date_created'] = $options['max_date_created'];
        }

        if (isset($options['min_date_modified'])) {
            $params['min_date_modified'] = $options['min_date_modified'];
        }

        if (isset($options['max_date_modified'])) {
            $params['max_date_modified'] = $options['max_date_modified'];
        }

        if (isset($options['customer_id'])) {
            $params['customer_id'] = $options['customer_id'];
        }

        if (isset($options['email'])) {
            $params['email'] = $options['email'];
        }

        $response = $this->makeApiRequest('GET', 'orders', $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب طلب واحد من BigCommerce
     */
    public function getOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $response = $this->makeApiRequest('GET', "orders/{$orderId}", [], $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب عناصر الطلب من BigCommerce
     */
    public function getOrderProducts(ECommerceIntegration $integration, string $orderId): array
    {
        $response = $this->makeApiRequest('GET', "orders/{$orderId}/products", [], $integration);
        return $response['data'] ?? [];
    }

    /**
     * تحديث طلب في BigCommerce
     */
    public function updateOrder(ECommerceIntegration $integration, string $orderId, array $orderData): array
    {
        $data = $this->transformToExternalFormat($orderData, 'order');
        $response = $this->makeApiRequest('PUT', "orders/{$orderId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب العملاء من BigCommerce
     */
    public function getCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'page' => $options['page'] ?? 1,
            'include' => 'addresses,store_credit,attributes,form_fields',
        ];

        if (isset($options['email'])) {
            $params['email:in'] = $options['email'];
        }

        if (isset($options['name'])) {
            $params['name:like'] = $options['name'];
        }

        if (isset($options['company'])) {
            $params['company:like'] = $options['company'];
        }

        if (isset($options['customer_group_id'])) {
            $params['customer_group_id'] = $options['customer_group_id'];
        }

        if (isset($options['date_created'])) {
            $params['date_created'] = $options['date_created'];
        }

        if (isset($options['date_modified'])) {
            $params['date_modified'] = $options['date_modified'];
        }

        $response = $this->makeApiRequest('GET', 'customers', $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب عميل واحد من BigCommerce
     */
    public function getCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $params = ['include' => 'addresses,store_credit,attributes,form_fields'];
        $response = $this->makeApiRequest('GET', "customers/{$customerId}", $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * إنشاء عميل في BigCommerce
     */
    public function createCustomer(ECommerceIntegration $integration, array $customerData): array
    {
        $data = $this->transformToExternalFormat($customerData, 'customer');
        $response = $this->makeApiRequest('POST', 'customers', $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * تحديث عميل في BigCommerce
     */
    public function updateCustomer(ECommerceIntegration $integration, string $customerId, array $customerData): array
    {
        $data = $this->transformToExternalFormat($customerData, 'customer');
        $response = $this->makeApiRequest('PUT', "customers/{$customerId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * حذف عميل من BigCommerce
     */
    public function deleteCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $this->makeApiRequest('DELETE', "customers/{$customerId}", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب الفئات من BigCommerce
     */
    public function getCategories(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? 250,
            'page' => $options['page'] ?? 1,
            'include' => 'image',
        ];

        if (isset($options['parent_id'])) {
            $params['parent_id'] = $options['parent_id'];
        }

        if (isset($options['name'])) {
            $params['name:like'] = $options['name'];
        }

        if (isset($options['is_visible'])) {
            $params['is_visible'] = $options['is_visible'];
        }

        $response = $this->makeApiRequest('GET', 'catalog/categories', $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب فئة واحدة من BigCommerce
     */
    public function getCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $params = ['include' => 'image'];
        $response = $this->makeApiRequest('GET', "catalog/categories/{$categoryId}", $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * إنشاء فئة في BigCommerce
     */
    public function createCategory(ECommerceIntegration $integration, array $categoryData): array
    {
        $data = $this->transformCategoryToBigCommerce($categoryData);
        $response = $this->makeApiRequest('POST', 'catalog/categories', $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * تحديث فئة في BigCommerce
     */
    public function updateCategory(ECommerceIntegration $integration, string $categoryId, array $categoryData): array
    {
        $data = $this->transformCategoryToBigCommerce($categoryData);
        $response = $this->makeApiRequest('PUT', "catalog/categories/{$categoryId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * حذف فئة من BigCommerce
     */
    public function deleteCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $this->makeApiRequest('DELETE', "catalog/categories/{$categoryId}", [], $integration);
        return ['success' => true];
    }

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $products = $this->getProducts($integration, $options);

        return [
            'total' => count($products),
            'processed' => count($products),
            'successful' => count($products),
            'failed' => 0,
            'data' => $products,
        ];
    }

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $orders = $this->getOrders($integration, $options);

        return [
            'total' => count($orders),
            'processed' => count($orders),
            'successful' => count($orders),
            'failed' => 0,
            'data' => $orders,
        ];
    }

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $customers = $this->getCustomers($integration, $options);

        return [
            'total' => count($customers),
            'processed' => count($customers),
            'successful' => count($customers),
            'failed' => 0,
            'data' => $customers,
        ];
    }

    /**
     * مزامنة الفئات
     */
    public function syncCategories(ECommerceIntegration $integration, array $options = []): array
    {
        $categories = $this->getCategories($integration, $options);

        return [
            'total' => count($categories),
            'processed' => count($categories),
            'successful' => count($categories),
            'failed' => 0,
            'data' => $categories,
        ];
    }

    /**
     * معالجة webhook من BigCommerce
     */
    public function processWebhook(ECommerceIntegration $integration, array $payload, array $headers = []): array
    {
        $scope = $payload['scope'] ?? '';
        $storeId = $payload['store_id'] ?? '';
        $data = $payload['data'] ?? [];

        return [
            'success' => true,
            'scope' => $scope,
            'store_id' => $storeId,
            'data' => $data,
        ];
    }

    /**
     * التحقق من توقيع webhook
     */
    public function verifyWebhookSignature(Request $request, ECommerceIntegration $integration): bool
    {
        // BigCommerce doesn't use HMAC signatures for webhooks
        // Instead, verify the request comes from BigCommerce by checking headers
        $userAgent = $request->userAgent();
        return str_contains($userAgent, 'BigCommerce');
    }

    /**
     * تحويل البيانات إلى تنسيق BigCommerce
     */
    public function transformToExternalFormat(array $data, string $entityType): array
    {
        return match ($entityType) {
            'product' => $this->transformProductToBigCommerce($data),
            'order' => $this->transformOrderToBigCommerce($data),
            'customer' => $this->transformCustomerToBigCommerce($data),
            'category' => $this->transformCategoryToBigCommerce($data),
            default => $data,
        };
    }

    /**
     * تحويل البيانات من تنسيق BigCommerce
     */
    public function transformFromExternalFormat(array $data, string $entityType): array
    {
        return match ($entityType) {
            'product' => $this->transformProductFromBigCommerce($data),
            'order' => $this->transformOrderFromBigCommerce($data),
            'customer' => $this->transformCustomerFromBigCommerce($data),
            'category' => $this->transformCategoryFromBigCommerce($data),
            default => $data,
        };
    }

    /**
     * تحويل منتج إلى تنسيق BigCommerce
     */
    protected function transformProductToBigCommerce(array $data): array
    {
        $transformed = [
            'name' => $data['name'] ?? '',
            'type' => $data['type'] ?? 'physical',
            'sku' => $data['sku'] ?? '',
            'description' => $data['description'] ?? '',
            'weight' => $data['weight'] ?? 0,
            'width' => $data['width'] ?? 0,
            'depth' => $data['depth'] ?? 0,
            'height' => $data['height'] ?? 0,
            'price' => $data['price'] ?? 0,
            'cost_price' => $data['cost_price'] ?? 0,
            'retail_price' => $data['retail_price'] ?? 0,
            'sale_price' => $data['sale_price'] ?? 0,
            'categories' => $data['categories'] ?? [],
            'brand_id' => $data['brand_id'] ?? 0,
            'inventory_level' => $data['inventory_level'] ?? 0,
            'inventory_tracking' => $data['inventory_tracking'] ?? 'none',
            'is_visible' => $data['is_visible'] ?? true,
            'is_featured' => $data['is_featured'] ?? false,
            'condition' => $data['condition'] ?? 'New',
            'availability' => $data['availability'] ?? 'available',
            'upc' => $data['upc'] ?? '',
            'mpn' => $data['mpn'] ?? '',
            'gtin' => $data['gtin'] ?? '',
            'search_keywords' => $data['search_keywords'] ?? '',
            'page_title' => $data['page_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
        ];

        // إضافة الصور إذا كانت موجودة
        if (isset($data['images']) && is_array($data['images'])) {
            $transformed['images'] = array_map(function ($image) {
                return [
                    'image_file' => $image['url'] ?? '',
                    'is_thumbnail' => $image['is_thumbnail'] ?? false,
                    'sort_order' => $image['sort_order'] ?? 0,
                    'description' => $image['description'] ?? '',
                ];
            }, $data['images']);
        }

        return $transformed;
    }

    /**
     * تحويل منتج من تنسيق BigCommerce
     */
    protected function transformProductFromBigCommerce(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'name' => $data['name'] ?? '',
            'sku' => $data['sku'] ?? '',
            'description' => $data['description'] ?? '',
            'price' => $data['price'] ?? 0,
            'cost_price' => $data['cost_price'] ?? 0,
            'weight' => $data['weight'] ?? 0,
            'inventory_level' => $data['inventory_level'] ?? 0,
            'is_visible' => $data['is_visible'] ?? true,
            'is_featured' => $data['is_featured'] ?? false,
            'categories' => $data['categories'] ?? [],
            'brand_id' => $data['brand_id'] ?? 0,
            'condition' => $data['condition'] ?? 'New',
            'availability' => $data['availability'] ?? 'available',
            'type' => $data['type'] ?? 'physical',
            'total_sold' => $data['total_sold'] ?? 0,
            'view_count' => $data['view_count'] ?? 0,
            'date_created' => $data['date_created'] ?? null,
            'date_modified' => $data['date_modified'] ?? null,
            'images' => $data['images'] ?? [],
            'variants' => $data['variants'] ?? [],
            'custom_fields' => $data['custom_fields'] ?? [],
            'upc' => $data['upc'] ?? '',
            'mpn' => $data['mpn'] ?? '',
            'gtin' => $data['gtin'] ?? '',
            'search_keywords' => $data['search_keywords'] ?? '',
            'page_title' => $data['page_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
        ];
    }

    /**
     * تحويل طلب إلى تنسيق BigCommerce
     */
    protected function transformOrderToBigCommerce(array $data): array
    {
        return [
            'status_id' => $data['status_id'] ?? 1,
            'customer_id' => $data['customer_id'] ?? 0,
            'date_created' => $data['date_created'] ?? null,
            'date_modified' => $data['date_modified'] ?? null,
            'subtotal_ex_tax' => $data['subtotal_ex_tax'] ?? 0,
            'subtotal_inc_tax' => $data['subtotal_inc_tax'] ?? 0,
            'total_ex_tax' => $data['total_ex_tax'] ?? 0,
            'total_inc_tax' => $data['total_inc_tax'] ?? 0,
            'payment_method' => $data['payment_method'] ?? '',
            'payment_status' => $data['payment_status'] ?? '',
            'currency_code' => $data['currency_code'] ?? 'USD',
            'staff_notes' => $data['staff_notes'] ?? '',
            'customer_message' => $data['customer_message'] ?? '',
            'billing_address' => $data['billing_address'] ?? [],
            'products' => $data['products'] ?? [],
        ];
    }

    /**
     * تحويل طلب من تنسيق BigCommerce
     */
    protected function transformOrderFromBigCommerce(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'status_id' => $data['status_id'] ?? 1,
            'status' => $data['status'] ?? '',
            'customer_id' => $data['customer_id'] ?? 0,
            'date_created' => $data['date_created'] ?? null,
            'date_modified' => $data['date_modified'] ?? null,
            'subtotal_ex_tax' => $data['subtotal_ex_tax'] ?? 0,
            'subtotal_inc_tax' => $data['subtotal_inc_tax'] ?? 0,
            'total_ex_tax' => $data['total_ex_tax'] ?? 0,
            'total_inc_tax' => $data['total_inc_tax'] ?? 0,
            'payment_method' => $data['payment_method'] ?? '',
            'payment_status' => $data['payment_status'] ?? '',
            'currency_code' => $data['currency_code'] ?? 'USD',
            'staff_notes' => $data['staff_notes'] ?? '',
            'customer_message' => $data['customer_message'] ?? '',
            'billing_address' => $data['billing_address'] ?? [],
            'shipping_addresses' => $data['shipping_addresses'] ?? [],
            'products' => $data['products'] ?? [],
            'coupons' => $data['coupons'] ?? [],
        ];
    }

    /**
     * تحويل عميل إلى تنسيق BigCommerce
     */
    protected function transformCustomerToBigCommerce(array $data): array
    {
        return [
            'email' => $data['email'] ?? '',
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'company' => $data['company'] ?? '',
            'phone' => $data['phone'] ?? '',
            'notes' => $data['notes'] ?? '',
            'tax_exempt_category' => $data['tax_exempt_category'] ?? '',
            'customer_group_id' => $data['customer_group_id'] ?? 0,
            'addresses' => $data['addresses'] ?? [],
            'authentication' => $data['authentication'] ?? [],
            'accepts_product_review_abandoned_cart_emails' => $data['accepts_marketing'] ?? false,
            'store_credit_amounts' => $data['store_credit_amounts'] ?? [],
            'origin_channel_id' => $data['origin_channel_id'] ?? 1,
            'channel_ids' => $data['channel_ids'] ?? [1],
        ];
    }

    /**
     * تحويل عميل من تنسيق BigCommerce
     */
    protected function transformCustomerFromBigCommerce(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'email' => $data['email'] ?? '',
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'company' => $data['company'] ?? '',
            'phone' => $data['phone'] ?? '',
            'notes' => $data['notes'] ?? '',
            'customer_group_id' => $data['customer_group_id'] ?? 0,
            'date_created' => $data['date_created'] ?? null,
            'date_modified' => $data['date_modified'] ?? null,
            'addresses' => $data['addresses'] ?? [],
            'store_credit_amounts' => $data['store_credit_amounts'] ?? [],
            'accepts_marketing' => $data['accepts_product_review_abandoned_cart_emails'] ?? false,
            'registration_ip_address' => $data['registration_ip_address'] ?? '',
            'channel_ids' => $data['channel_ids'] ?? [],
        ];
    }

    /**
     * تحويل فئة إلى تنسيق BigCommerce
     */
    protected function transformCategoryToBigCommerce(array $data): array
    {
        return [
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? '',
            'parent_id' => $data['parent_id'] ?? 0,
            'sort_order' => $data['sort_order'] ?? 0,
            'page_title' => $data['page_title'] ?? '',
            'meta_keywords' => $data['meta_keywords'] ?? [],
            'meta_description' => $data['meta_description'] ?? '',
            'layout_file' => $data['layout_file'] ?? '',
            'is_visible' => $data['is_visible'] ?? true,
            'search_keywords' => $data['search_keywords'] ?? '',
            'default_product_sort' => $data['default_product_sort'] ?? 'use_store_settings',
            'image_url' => $data['image_url'] ?? '',
        ];
    }

    /**
     * تحويل فئة من تنسيق BigCommerce
     */
    protected function transformCategoryFromBigCommerce(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? '',
            'parent_id' => $data['parent_id'] ?? 0,
            'sort_order' => $data['sort_order'] ?? 0,
            'page_title' => $data['page_title'] ?? '',
            'meta_keywords' => $data['meta_keywords'] ?? [],
            'meta_description' => $data['meta_description'] ?? '',
            'layout_file' => $data['layout_file'] ?? '',
            'is_visible' => $data['is_visible'] ?? true,
            'search_keywords' => $data['search_keywords'] ?? '',
            'default_product_sort' => $data['default_product_sort'] ?? 'use_store_settings',
            'image_url' => $data['image_url'] ?? '',
            'custom_url' => $data['custom_url'] ?? null,
        ];
    }

    /**
     * الحصول على الحقول المطلوبة
     */
    public function getRequiredFields(): array
    {
        return [
            'store_hash',
            'access_token',
        ];
    }

    /**
     * الحصول على الحقول الاختيارية
     */
    public function getOptionalFields(): array
    {
        return [
            'webhook_secret',
            'client_id',
            'client_secret',
        ];
    }

    /**
     * الحصول على العمليات المدعومة
     */
    public function getSupportedOperations(): array
    {
        return [
            'products.read', 'products.write',
            'orders.read', 'orders.write',
            'customers.read', 'customers.write',
            'categories.read', 'categories.write',
            'webhooks.read', 'webhooks.write',
            'inventory.read', 'inventory.write',
            'coupons.read', 'coupons.write',
            'reports.read',
        ];
    }

    /**
     * الحصول على أنواع المزامنة المدعومة
     */
    public function getSupportedSyncTypes(): array
    {
        return ['full', 'incremental', 'real-time'];
    }

    /**
     * الحصول على أنواع الأحداث المدعومة
     */
    public function getSupportedEventTypes(): array
    {
        return [
            'store/product/created', 'store/product/updated', 'store/product/deleted',
            'store/order/created', 'store/order/updated', 'store/order/archived',
            'store/customer/created', 'store/customer/updated', 'store/customer/deleted',
            'store/category/created', 'store/category/updated', 'store/category/deleted',
            'store/inventory/order/updated',
            'store/cart/created', 'store/cart/updated', 'store/cart/deleted',
            'store/app/uninstalled',
        ];
    }

    /**
     * الحصول على تنسيقات البيانات المدعومة
     */
    public function getSupportedDataFormats(): array
    {
        return ['json'];
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    public function getDefaultConfiguration(): array
    {
        return [
            'api_version' => 'v3',
            'timeout' => 30,
            'max_retries' => 3,
            'retry_delay' => 1000,
            'max_page_size' => 250,
            'default_page_size' => 50,
            'max_requests_per_second' => 20,
            'max_requests_per_minute' => 400,
            'webhook_events' => [
                'store/product/created',
                'store/product/updated',
                'store/order/created',
                'store/order/updated',
                'store/customer/created',
                'store/customer/updated',
            ],
        ];
    }

    // تنفيذ باقي الطرق المطلوبة من الواجهة

    /**
     * إنشاء طلب في BigCommerce
     */
    public function createOrder(ECommerceIntegration $integration, array $orderData): array
    {
        $data = $this->transformToExternalFormat($orderData, 'order');
        $response = $this->makeApiRequest('POST', 'orders', $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * إلغاء طلب في BigCommerce
     */
    public function cancelOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $data = ['status_id' => 5]; // 5 = Cancelled in BigCommerce
        $response = $this->makeApiRequest('PUT', "orders/{$orderId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب المخزون من BigCommerce
     */
    public function getInventory(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'page' => $options['page'] ?? 1,
        ];

        if (isset($options['product_id'])) {
            $params['product_id'] = $options['product_id'];
        }

        $response = $this->makeApiRequest('GET', 'catalog/products', $params, $integration);
        $products = $response['data'] ?? [];

        // تحويل بيانات المنتجات إلى بيانات مخزون
        return array_map(function ($product) {
            return [
                'product_id' => $product['id'],
                'sku' => $product['sku'],
                'inventory_level' => $product['inventory_level'],
                'inventory_warning_level' => $product['inventory_warning_level'],
                'inventory_tracking' => $product['inventory_tracking'],
            ];
        }, $products);
    }

    /**
     * تحديث المخزون في BigCommerce
     */
    public function updateInventory(ECommerceIntegration $integration, string $productId, int $quantity): array
    {
        $data = ['inventory_level' => $quantity];
        $response = $this->makeApiRequest('PUT', "catalog/products/{$productId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * جلب الكوبونات من BigCommerce
     */
    public function getCoupons(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'page' => $options['page'] ?? 1,
        ];

        if (isset($options['name'])) {
            $params['name'] = $options['name'];
        }

        if (isset($options['type'])) {
            $params['type'] = $options['type'];
        }

        $response = $this->makeApiRequest('GET', 'coupons', $params, $integration);
        return $response['data'] ?? [];
    }

    /**
     * إنشاء كوبون في BigCommerce
     */
    public function createCoupon(ECommerceIntegration $integration, array $couponData): array
    {
        $data = [
            'name' => $couponData['name'] ?? '',
            'type' => $couponData['type'] ?? 'per_item_discount',
            'amount' => $couponData['amount'] ?? 0,
            'min_purchase' => $couponData['min_purchase'] ?? 0,
            'expires' => $couponData['expires'] ?? null,
            'enabled' => $couponData['enabled'] ?? true,
            'code' => $couponData['code'] ?? '',
            'applies_to' => $couponData['applies_to'] ?? [],
            'num_uses' => $couponData['num_uses'] ?? 0,
            'max_uses' => $couponData['max_uses'] ?? null,
            'max_uses_per_customer' => $couponData['max_uses_per_customer'] ?? null,
            'restricted_to' => $couponData['restricted_to'] ?? [],
            'shipping_methods' => $couponData['shipping_methods'] ?? [],
        ];

        $response = $this->makeApiRequest('POST', 'coupons', $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * تحديث كوبون في BigCommerce
     */
    public function updateCoupon(ECommerceIntegration $integration, string $couponId, array $couponData): array
    {
        $data = [
            'name' => $couponData['name'] ?? '',
            'type' => $couponData['type'] ?? 'per_item_discount',
            'amount' => $couponData['amount'] ?? 0,
            'min_purchase' => $couponData['min_purchase'] ?? 0,
            'expires' => $couponData['expires'] ?? null,
            'enabled' => $couponData['enabled'] ?? true,
            'code' => $couponData['code'] ?? '',
            'applies_to' => $couponData['applies_to'] ?? [],
            'max_uses' => $couponData['max_uses'] ?? null,
            'max_uses_per_customer' => $couponData['max_uses_per_customer'] ?? null,
            'restricted_to' => $couponData['restricted_to'] ?? [],
            'shipping_methods' => $couponData['shipping_methods'] ?? [],
        ];

        $response = $this->makeApiRequest('PUT', "coupons/{$couponId}", $data, $integration);
        return $response['data'] ?? [];
    }

    /**
     * حذف كوبون من BigCommerce
     */
    public function deleteCoupon(ECommerceIntegration $integration, string $couponId): array
    {
        $this->makeApiRequest('DELETE', "coupons/{$couponId}", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب التقارير من BigCommerce
     */
    public function getReports(ECommerceIntegration $integration, array $options = []): array
    {
        // BigCommerce doesn't have a general reports endpoint
        // Return combined data from different endpoints
        return [
            'sales' => $this->getSalesReport($integration, $options),
            'products' => $this->getProductsReport($integration, $options),
            'customers' => $this->getCustomersReport($integration, $options),
        ];
    }

    /**
     * جلب تقرير المبيعات
     */
    public function getSalesReport(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? 50,
            'page' => $options['page'] ?? 1,
        ];

        if (isset($options['min_date_created'])) {
            $params['min_date_created'] = $options['min_date_created'];
        }

        if (isset($options['max_date_created'])) {
            $params['max_date_created'] = $options['max_date_created'];
        }

        $orders = $this->getOrders($integration, $params);

        $totalSales = 0;
        $totalOrders = count($orders);
        $totalItems = 0;

        foreach ($orders as $order) {
            $totalSales += $order['total_inc_tax'] ?? 0;
            $totalItems += $order['items_total'] ?? 0;
        }

        return [
            'total_sales' => $totalSales,
            'total_orders' => $totalOrders,
            'total_items' => $totalItems,
            'average_order_value' => $totalOrders > 0 ? $totalSales / $totalOrders : 0,
            'orders' => $orders,
        ];
    }

    /**
     * جلب تقرير المنتجات
     */
    public function getProductsReport(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? 50,
            'page' => $options['page'] ?? 1,
        ];

        $products = $this->getProducts($integration, $params);

        $totalProducts = count($products);
        $totalValue = 0;
        $lowStockProducts = 0;

        foreach ($products as $product) {
            $totalValue += ($product['price'] ?? 0) * ($product['inventory_level'] ?? 0);
            if (($product['inventory_level'] ?? 0) <= ($product['inventory_warning_level'] ?? 0)) {
                $lowStockProducts++;
            }
        }

        return [
            'total_products' => $totalProducts,
            'total_inventory_value' => $totalValue,
            'low_stock_products' => $lowStockProducts,
            'products' => $products,
        ];
    }

    /**
     * جلب تقرير العملاء
     */
    public function getCustomersReport(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? 50,
            'page' => $options['page'] ?? 1,
        ];

        $customers = $this->getCustomers($integration, $params);

        return [
            'total_customers' => count($customers),
            'customers' => $customers,
        ];
    }

    /**
     * مزامنة المخزون
     */
    public function syncInventory(ECommerceIntegration $integration, array $options = []): array
    {
        $inventory = $this->getInventory($integration, $options);

        return [
            'total' => count($inventory),
            'processed' => count($inventory),
            'successful' => count($inventory),
            'failed' => 0,
            'data' => $inventory,
        ];
    }

    // الطرق المفقودة من الواجهة
    public function createWebhook(ECommerceIntegration $integration, array $webhookData): array { return []; }
    public function updateWebhook(ECommerceIntegration $integration, string $webhookId, array $webhookData): array { return []; }
    public function deleteWebhook(ECommerceIntegration $integration, string $webhookId): array { return []; }
    public function getWebhooks(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function getApiLimits(ECommerceIntegration $integration): array { return []; }
    public function getApiUsage(ECommerceIntegration $integration): array { return []; }
    public function refreshAccessToken(ECommerceIntegration $integration): array { return []; }
    public function revokeAccess(ECommerceIntegration $integration): array { return []; }
    public function getAppInfo(ECommerceIntegration $integration): array { return []; }
    public function updateAppSettings(ECommerceIntegration $integration, array $settings): array { return []; }
    public function getStoreStats(ECommerceIntegration $integration): array { return []; }
    public function getPlanInfo(ECommerceIntegration $integration): array { return []; }
    public function getSupportedCountries(ECommerceIntegration $integration): array { return []; }
    public function getSupportedCurrencies(ECommerceIntegration $integration): array { return []; }
    public function getSupportedLanguages(ECommerceIntegration $integration): array { return []; }
    public function getSupportedPaymentMethods(ECommerceIntegration $integration): array { return []; }
    public function getSupportedShippingMethods(ECommerceIntegration $integration): array { return []; }
    public function getSupportedTaxes(ECommerceIntegration $integration): array { return []; }
    public function validateDataForExport(array $data, string $entityType): array { return ['valid' => true]; }
    public function validateDataForImport(array $data, string $entityType): array { return ['valid' => true]; }
    public function getRequiredHeaders(ECommerceIntegration $integration): array { return []; }
    public function getRequiredQueryParams(ECommerceIntegration $integration): array { return []; }
    public function prepareApiRequest(string $method, string $endpoint, array $data = []): array { return []; }
    public function buildNextPageUrl(array $pagination): ?string { return null; }
    public function buildPreviousPageUrl(array $pagination): ?string { return null; }
}
