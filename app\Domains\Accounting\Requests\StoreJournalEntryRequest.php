<?php

namespace App\Domains\Accounting\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Store Journal Entry Request
 * طلب إنشاء قيد يومية جديد
 */
class StoreJournalEntryRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Domains\Accounting\Models\JournalEntry::class);
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            'entry_date' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'description' => [
                'required',
                'string',
                'max:500',
            ],
            'reference' => [
                'nullable',
                'string',
                'max:100',
            ],
            'source_type' => [
                'nullable',
                'string',
                'max:50',
            ],
            'source_id' => [
                'nullable',
                'integer',
            ],
            'currency' => [
                'nullable',
                'string',
                'size:3',
                Rule::in(['SAR', 'USD', 'EUR', 'GBP', 'AED', 'EGP']),
            ],
            'exchange_rate' => [
                'nullable',
                'numeric',
                'min:0.01',
            ],
            'accounting_standard' => [
                'nullable',
                'string',
                Rule::in(['IFRS', 'PCGM', 'GAAP', 'SOCOMA']),
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            
            // خطوط القيد
            'lines' => [
                'required',
                'array',
                'min:2',
            ],
            'lines.*.account_id' => [
                'required',
                'integer',
                'exists:accounts,id',
            ],
            'lines.*.debit_amount' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'lines.*.credit_amount' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'lines.*.description' => [
                'nullable',
                'string',
                'max:255',
            ],
            'lines.*.cost_center_id' => [
                'nullable',
                'integer',
                'exists:cost_centers,id',
            ],
            'lines.*.project_id' => [
                'nullable',
                'integer',
                'exists:projects,id',
            ],
            'lines.*.department_id' => [
                'nullable',
                'integer',
                'exists:departments,id',
            ],
            'lines.*.employee_id' => [
                'nullable',
                'integer',
                'exists:employees,id',
            ],
            'lines.*.tax_code' => [
                'nullable',
                'string',
                'max:20',
            ],
            'lines.*.analytics_tags' => [
                'nullable',
                'array',
            ],
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'entry_date.required' => 'تاريخ القيد مطلوب',
            'entry_date.date' => 'تاريخ القيد غير صحيح',
            'entry_date.before_or_equal' => 'تاريخ القيد لا يمكن أن يكون في المستقبل',
            
            'description.required' => 'وصف القيد مطلوب',
            'description.max' => 'وصف القيد يجب ألا يزيد عن 500 حرف',
            
            'reference.max' => 'المرجع يجب ألا يزيد عن 100 حرف',
            'source_type.max' => 'نوع المصدر يجب ألا يزيد عن 50 حرف',
            
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'currency.in' => 'العملة غير مدعومة',
            
            'exchange_rate.numeric' => 'سعر الصرف يجب أن يكون رقم',
            'exchange_rate.min' => 'سعر الصرف يجب أن يكون أكبر من صفر',
            
            'accounting_standard.in' => 'المعيار المحاسبي غير مدعوم',
            
            'notes.max' => 'الملاحظات يجب ألا تزيد عن 1000 حرف',
            
            'lines.required' => 'خطوط القيد مطلوبة',
            'lines.array' => 'خطوط القيد يجب أن تكون مصفوفة',
            'lines.min' => 'يجب إضافة خطين على الأقل للقيد',
            
            'lines.*.account_id.required' => 'الحساب مطلوب',
            'lines.*.account_id.exists' => 'الحساب غير موجود',
            
            'lines.*.debit_amount.numeric' => 'المبلغ المدين يجب أن يكون رقم',
            'lines.*.debit_amount.min' => 'المبلغ المدين يجب أن يكون موجب أو صفر',
            
            'lines.*.credit_amount.numeric' => 'المبلغ الدائن يجب أن يكون رقم',
            'lines.*.credit_amount.min' => 'المبلغ الدائن يجب أن يكون موجب أو صفر',
            
            'lines.*.description.max' => 'وصف الخط يجب ألا يزيد عن 255 حرف',
            
            'lines.*.cost_center_id.exists' => 'مركز التكلفة غير موجود',
            'lines.*.project_id.exists' => 'المشروع غير موجود',
            'lines.*.department_id.exists' => 'القسم غير موجود',
            'lines.*.employee_id.exists' => 'الموظف غير موجود',
            
            'lines.*.tax_code.max' => 'رمز الضريبة يجب ألا يزيد عن 20 حرف',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'entry_date' => 'تاريخ القيد',
            'description' => 'وصف القيد',
            'reference' => 'المرجع',
            'source_type' => 'نوع المصدر',
            'source_id' => 'معرف المصدر',
            'currency' => 'العملة',
            'exchange_rate' => 'سعر الصرف',
            'accounting_standard' => 'المعيار المحاسبي',
            'notes' => 'الملاحظات',
            'lines' => 'خطوط القيد',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'currency' => $this->currency ?? 'SAR',
            'exchange_rate' => $this->exchange_rate ?? 1.0,
            'accounting_standard' => $this->accounting_standard ?? 'IFRS',
        ]);

        // تنظيف بيانات الخطوط
        if ($this->has('lines')) {
            $lines = collect($this->lines)->map(function ($line) {
                return array_merge($line, [
                    'debit_amount' => (float) ($line['debit_amount'] ?? 0),
                    'credit_amount' => (float) ($line['credit_amount'] ?? 0),
                ]);
            })->toArray();

            $this->merge(['lines' => $lines]);
        }
    }

    /**
     * التحقق من صحة البيانات بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من توازن القيد
            if ($this->has('lines')) {
                $totalDebits = 0;
                $totalCredits = 0;

                foreach ($this->lines as $index => $line) {
                    $debitAmount = (float) ($line['debit_amount'] ?? 0);
                    $creditAmount = (float) ($line['credit_amount'] ?? 0);

                    // التحقق من أن كل خط له مبلغ واحد فقط
                    if ($debitAmount > 0 && $creditAmount > 0) {
                        $validator->errors()->add("lines.{$index}", 'لا يمكن أن يحتوي الخط على مبلغ مدين ودائن معاً');
                    }

                    // التحقق من أن كل خط له مبلغ
                    if ($debitAmount == 0 && $creditAmount == 0) {
                        $validator->errors()->add("lines.{$index}", 'يجب إدخال مبلغ مدين أو دائن');
                    }

                    $totalDebits += $debitAmount;
                    $totalCredits += $creditAmount;
                }

                // التحقق من توازن القيد
                if (abs($totalDebits - $totalCredits) > 0.01) {
                    $validator->errors()->add('lines', 'القيد غير متوازن: المدين = ' . $totalDebits . '، الدائن = ' . $totalCredits);
                }

                // التحقق من أن إجمالي القيد أكبر من صفر
                if ($totalDebits == 0 || $totalCredits == 0) {
                    $validator->errors()->add('lines', 'إجمالي القيد يجب أن يكون أكبر من صفر');
                }
            }

            // التحقق من الحسابات المطلوبة لمراكز التكلفة والمشاريع
            if ($this->has('lines')) {
                foreach ($this->lines as $index => $line) {
                    $account = \App\Domains\Accounting\Models\Account::find($line['account_id']);
                    
                    if ($account) {
                        // التحقق من مركز التكلفة المطلوب
                        if ($account->require_cost_center && empty($line['cost_center_id'])) {
                            $validator->errors()->add("lines.{$index}.cost_center_id", 'مركز التكلفة مطلوب لهذا الحساب');
                        }

                        // التحقق من المشروع المطلوب
                        if ($account->require_project && empty($line['project_id'])) {
                            $validator->errors()->add("lines.{$index}.project_id", 'المشروع مطلوب لهذا الحساب');
                        }

                        // التحقق من السماح بالإدخال اليدوي
                        if (!$account->allow_manual_entry) {
                            $validator->errors()->add("lines.{$index}.account_id", 'هذا الحساب لا يسمح بالإدخال اليدوي');
                        }

                        // التحقق من أن الحساب نشط
                        if (!$account->is_active) {
                            $validator->errors()->add("lines.{$index}.account_id", 'هذا الحساب غير نشط');
                        }
                    }
                }
            }

            // التحقق من سعر الصرف للعملات الأجنبية
            if ($this->currency !== 'SAR' && $this->exchange_rate <= 0) {
                $validator->errors()->add('exchange_rate', 'سعر الصرف مطلوب ويجب أن يكون أكبر من صفر للعملات الأجنبية');
            }
        });
    }
}
