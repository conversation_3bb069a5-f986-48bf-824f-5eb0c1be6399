<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تفاعل الرسالة - Message Reaction
 * يدير التفاعلات على الرسائل (إعجاب، حب، ضحك، إلخ)
 */
class MessageReaction extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'message_id',
        'user_id',
        'emoji',
        'reaction_type',
    ];

    /**
     * العلاقة مع الرسالة
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class, 'message_id');
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    /**
     * الحصول على التفاعلات الشائعة
     */
    public static function getPopularReactions(): array
    {
        return [
            '👍' => 'إعجاب',
            '❤️' => 'حب',
            '😂' => 'ضحك',
            '😮' => 'مفاجأة',
            '😢' => 'حزن',
            '😡' => 'غضب',
            '👏' => 'تصفيق',
            '🎉' => 'احتفال',
            '✅' => 'موافق',
            '❌' => 'رفض',
        ];
    }

    /**
     * فلترة حسب نوع التفاعل
     */
    public function scopeOfType($query, string $emoji)
    {
        return $query->where('emoji', $emoji);
    }

    /**
     * فلترة حسب المستخدم
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }
}
