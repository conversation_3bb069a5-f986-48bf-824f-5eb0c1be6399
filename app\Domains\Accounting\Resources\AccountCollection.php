<?php

namespace App\Domains\Accounting\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Account Collection
 * مجموعة موارد الحسابات
 */
class AccountCollection extends ResourceCollection
{
    /**
     * تحويل المجموعة إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->transform(function ($account) {
                return [
                    'id' => $account->id,
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'account_name_en' => $account->account_name_en,
                    'account_type' => $account->account_type,
                    'account_type_label' => $this->getAccountTypeLabel($account->account_type),
                    'account_category' => $account->account_category,
                    'account_category_label' => $this->getAccountCategoryLabel($account->account_category),
                    'level' => $account->level,
                    'is_active' => $account->is_active,
                    'current_balance' => $account->current_balance,
                    'currency_code' => $account->currency_code,
                    'currency_symbol' => $this->getCurrencySymbol($account->currency_code),

                    // الحساب الأب
                    'parent_account' => $account->parentAccount ? [
                        'id' => $account->parentAccount->id,
                        'account_code' => $account->parentAccount->account_code,
                        'account_name' => $account->parentAccount->account_name,
                    ] : null,

                    // عدد الحسابات الفرعية
                    'sub_accounts_count' => $account->subAccounts()->count(),

                    // آخر نشاط
                    'last_activity' => $account->journalEntryDetails()
                        ->whereHas('journalEntry', function ($query) {
                            $query->where('status', 'POSTED');
                        })
                        ->latest()
                        ->first()?->created_at?->format('Y-m-d'),

                    // الروابط
                    'links' => [
                        'self' => route('api.accounting.accounts.show', $account->id),
                        'edit' => route('api.accounting.accounts.edit', $account->id),
                    ],

                    // الأذونات
                    'permissions' => [
                        'can_view' => auth()->user()?->can('view', $account) ?? false,
                        'can_edit' => auth()->user()?->can('update', $account) ?? false,
                        'can_delete' => auth()->user()?->can('delete', $account) ?? false,
                    ],
                ];
            }),

            // معلومات التصفح
            'pagination' => [
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'has_more_pages' => $this->hasMorePages(),
            ],

            // الروابط
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
        ];
    }

    /**
     * البيانات الإضافية مع المجموعة
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'summary' => $this->getSummary(),
                'filters' => $this->getAvailableFilters(),
                'account_types' => [
                    'asset' => 'أصول',
                    'liability' => 'خصوم',
                    'equity' => 'حقوق ملكية',
                    'revenue' => 'إيرادات',
                    'expense' => 'مصروفات',
                ],
                'account_categories' => [
                    'current_assets' => 'أصول متداولة',
                    'fixed_assets' => 'أصول ثابتة',
                    'intangible_assets' => 'أصول غير ملموسة',
                    'current_liabilities' => 'خصوم متداولة',
                    'long_term_liabilities' => 'خصوم طويلة الأجل',
                    'capital' => 'رأس المال',
                    'retained_earnings' => 'أرباح محتجزة',
                    'operating_revenue' => 'إيرادات تشغيلية',
                    'other_revenue' => 'إيرادات أخرى',
                    'cost_of_goods_sold' => 'تكلفة البضاعة المباعة',
                    'operating_expenses' => 'مصروفات تشغيلية',
                    'administrative_expenses' => 'مصروفات إدارية',
                    'financial_expenses' => 'مصروفات مالية',
                    'other_expenses' => 'مصروفات أخرى',
                ],
            ],
        ];
    }

    /**
     * الحصول على ملخص المجموعة
     */
    protected function getSummary(): array
    {
        $accounts = $this->collection;

        return [
            'total_accounts' => $accounts->count(),
            'active_accounts' => $accounts->where('is_active', true)->count(),
            'inactive_accounts' => $accounts->where('is_active', false)->count(),
            'by_type' => [
                'assets' => $accounts->where('account_type', 'asset')->count(),
                'liabilities' => $accounts->where('account_type', 'liability')->count(),
                'equity' => $accounts->where('account_type', 'equity')->count(),
                'revenue' => $accounts->where('account_type', 'revenue')->count(),
                'expenses' => $accounts->where('account_type', 'expense')->count(),
            ],
            'by_level' => [
                'level_1' => $accounts->where('level', 1)->count(),
                'level_2' => $accounts->where('level', 2)->count(),
                'level_3' => $accounts->where('level', 3)->count(),
                'level_4_plus' => $accounts->where('level', '>=', 4)->count(),
            ],
            'total_balance' => [
                'assets' => $accounts->where('account_type', 'asset')->sum('current_balance'),
                'liabilities' => $accounts->where('account_type', 'liability')->sum('current_balance'),
                'equity' => $accounts->where('account_type', 'equity')->sum('current_balance'),
            ],
        ];
    }

    /**
     * الحصول على المرشحات المتاحة
     */
    protected function getAvailableFilters(): array
    {
        $accounts = $this->collection;

        return [
            'account_types' => $accounts->pluck('account_type')->unique()->values()->toArray(),
            'account_categories' => $accounts->pluck('account_category')->unique()->values()->toArray(),
            'currencies' => $accounts->pluck('currency_code')->unique()->values()->toArray(),
            'levels' => $accounts->pluck('level')->unique()->sort()->values()->toArray(),
            'has_parent' => $accounts->whereNotNull('parent_account_id')->count() > 0,
            'has_sub_accounts' => $accounts->filter(function ($account) {
                return $account->subAccounts()->count() > 0;
            })->count() > 0,
        ];
    }

    /**
     * الحصول على تسمية نوع الحساب
     */
    protected function getAccountTypeLabel(string $type): string
    {
        $labels = [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];

        return $labels[$type] ?? $type;
    }

    /**
     * الحصول على تسمية فئة الحساب
     */
    protected function getAccountCategoryLabel(string $category): string
    {
        $labels = [
            'current_assets' => 'أصول متداولة',
            'fixed_assets' => 'أصول ثابتة',
            'intangible_assets' => 'أصول غير ملموسة',
            'current_liabilities' => 'خصوم متداولة',
            'long_term_liabilities' => 'خصوم طويلة الأجل',
            'capital' => 'رأس المال',
            'retained_earnings' => 'أرباح محتجزة',
            'operating_revenue' => 'إيرادات تشغيلية',
            'other_revenue' => 'إيرادات أخرى',
            'cost_of_goods_sold' => 'تكلفة البضاعة المباعة',
            'operating_expenses' => 'مصروفات تشغيلية',
            'administrative_expenses' => 'مصروفات إدارية',
            'financial_expenses' => 'مصروفات مالية',
            'other_expenses' => 'مصروفات أخرى',
        ];

        return $labels[$category] ?? $category;
    }

    /**
     * الحصول على رمز العملة
     */
    protected function getCurrencySymbol(string $currencyCode): string
    {
        return match ($currencyCode) {
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'AED' => 'د.إ',
            'EGP' => 'ج.م',
            default => $currencyCode,
        };
    }
}
