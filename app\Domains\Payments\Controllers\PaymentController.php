<?php

namespace App\Domains\Payments\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Payments\Services\PaymentService;
use App\Domains\Payments\Services\FraudDetectionService;
use App\Domains\Payments\Services\CurrencyExchangeService;
use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\PaymentGateway;
use App\Domains\Payments\Models\DigitalWallet;
use App\Domains\Payments\Resources\PaymentTransactionResource;
use App\Domains\Payments\Requests\ProcessPaymentRequest;
use App\Domains\Payments\Requests\RefundPaymentRequest;
use App\Domains\Payments\Requests\CreateWalletRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

/**
 * تحكم المدفوعات الرئيسي
 * يدير جميع عمليات الدفع والمحافظ والتحويلات
 */
class PaymentController extends Controller
{
    protected PaymentService $paymentService;
    protected FraudDetectionService $fraudService;
    protected CurrencyExchangeService $currencyService;

    public function __construct(
        PaymentService $paymentService,
        FraudDetectionService $fraudService,
        CurrencyExchangeService $currencyService
    ) {
        $this->paymentService = $paymentService;
        $this->fraudService = $fraudService;
        $this->currencyService = $currencyService;
    }

    /**
     * معالجة دفعة جديدة
     */
    public function processPayment(ProcessPaymentRequest $request): JsonResponse
    {
        try {
            $paymentData = $request->validated();
            $paymentData['user_id'] = Auth::id();
            $paymentData['ip_address'] = $request->ip();
            $paymentData['user_agent'] = $request->userAgent();

            $transaction = $this->paymentService->processPayment($paymentData);

            return response()->json([
                'success' => true,
                'message' => 'تم بدء معالجة الدفع بنجاح',
                'data' => new PaymentTransactionResource($transaction),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في معالجة الدفع: ' . $e->getMessage(),
                'error_code' => 'PAYMENT_PROCESSING_FAILED',
            ], 400);
        }
    }

    /**
     * معالجة دفع من المحفظة الرقمية
     */
    public function processWalletPayment(ProcessPaymentRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $paymentData = $request->validated();

            $transaction = $this->paymentService->processWalletPayment($user, $paymentData);

            return response()->json([
                'success' => true,
                'message' => 'تم الدفع من المحفظة بنجاح',
                'data' => new PaymentTransactionResource($transaction),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في الدفع من المحفظة: ' . $e->getMessage(),
                'error_code' => 'WALLET_PAYMENT_FAILED',
            ], 400);
        }
    }

    /**
     * معالجة استرداد الأموال
     */
    public function processRefund(RefundPaymentRequest $request, PaymentTransaction $transaction): JsonResponse
    {
        try {
            $this->authorize('refund', $transaction);

            $refundData = $request->validated();
            $result = $this->paymentService->processRefund(
                $transaction,
                $refundData['amount'] ?? null,
                $refundData['reason'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'تم استرداد الأموال بنجاح',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في استرداد الأموال: ' . $e->getMessage(),
                'error_code' => 'REFUND_FAILED',
            ], 400);
        }
    }

    /**
     * الحصول على طرق الدفع المتاحة
     */
    public function getAvailablePaymentMethods(Request $request): JsonResponse
    {
        $country = $request->get('country', 'SA');
        $currency = $request->get('currency', 'SAR');
        $amount = $request->get('amount', 100);

        $methods = $this->paymentService->getAvailablePaymentMethods($country, $currency, $amount);

        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * الحصول على أسعار الصرف
     */
    public function getExchangeRates(Request $request): JsonResponse
    {
        $baseCurrency = $request->get('base_currency', 'USD');
        $rates = $this->currencyService->getAllRatesForCurrency($baseCurrency);

        return response()->json([
            'success' => true,
            'data' => $rates,
        ]);
    }

    /**
     * تحويل العملة
     */
    public function convertCurrency(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
        ]);

        $conversion = $this->currencyService->convertAmount(
            $request->amount,
            $request->from_currency,
            $request->to_currency
        );

        return response()->json([
            'success' => true,
            'data' => $conversion,
        ]);
    }

    /**
     * إنشاء محفظة رقمية
     */
    public function createWallet(CreateWalletRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $walletData = $request->validated();
            $walletData['user_id'] = $user->id;

            $wallet = DigitalWallet::create($walletData);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المحفظة بنجاح',
                'data' => $wallet,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء المحفظة: ' . $e->getMessage(),
                'error_code' => 'WALLET_CREATION_FAILED',
            ], 400);
        }
    }

    /**
     * شحن المحفظة الرقمية
     */
    public function topUpWallet(Request $request, DigitalWallet $wallet): JsonResponse
    {
        try {
            $this->authorize('update', $wallet);

            $request->validate([
                'amount' => 'required|numeric|min:1',
                'payment_method' => 'required|string',
                'description' => 'nullable|string|max:255',
            ]);

            // معالجة شحن المحفظة عبر بوابة دفع
            $paymentData = [
                'amount' => $request->amount,
                'currency' => $wallet->currency,
                'payment_method' => $request->payment_method,
                'customer_email' => $wallet->user->email,
                'description' => $request->description ?? 'شحن المحفظة الرقمية',
                'payable_type' => DigitalWallet::class,
                'payable_id' => $wallet->id,
                'metadata' => [
                    'wallet_id' => $wallet->wallet_id,
                    'operation' => 'top_up',
                ],
            ];

            $transaction = $this->paymentService->processPayment($paymentData);

            // إذا نجح الدفع، أضف الرصيد للمحفظة
            if ($transaction->isSuccessful()) {
                $wallet->addBalance(
                    $request->amount,
                    $request->description ?? 'شحن المحفظة',
                    ['transaction_id' => $transaction->transaction_id]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'تم شحن المحفظة بنجاح',
                'data' => [
                    'transaction' => new PaymentTransactionResource($transaction),
                    'wallet' => $wallet->fresh(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في شحن المحفظة: ' . $e->getMessage(),
                'error_code' => 'WALLET_TOPUP_FAILED',
            ], 400);
        }
    }

    /**
     * تحويل بين المحافظ
     */
    public function transferBetweenWallets(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'from_wallet_id' => 'required|exists:digital_wallets,id',
                'to_wallet_id' => 'required|exists:digital_wallets,id|different:from_wallet_id',
                'amount' => 'required|numeric|min:0.01',
                'description' => 'nullable|string|max:255',
            ]);

            $fromWallet = DigitalWallet::findOrFail($request->from_wallet_id);
            $toWallet = DigitalWallet::findOrFail($request->to_wallet_id);

            $this->authorize('update', $fromWallet);

            // التحقق من الرصيد والحدود
            if (!$fromWallet->hasSufficientBalance($request->amount)) {
                throw new \Exception('الرصيد غير كافي');
            }

            $limitsCheck = $fromWallet->checkTransactionLimits($request->amount, 'transfer');
            if (!$limitsCheck['valid']) {
                throw new \Exception('تجاوز حدود التحويل: ' . implode(', ', $limitsCheck['errors']));
            }

            // تنفيذ التحويل
            \DB::transaction(function () use ($fromWallet, $toWallet, $request) {
                $description = $request->description ?? 'تحويل بين المحافظ';

                $fromWallet->deductBalance(
                    $request->amount,
                    "تحويل إلى {$toWallet->wallet_id} - {$description}",
                    ['to_wallet_id' => $toWallet->wallet_id]
                );

                $toWallet->addBalance(
                    $request->amount,
                    "تحويل من {$fromWallet->wallet_id} - {$description}",
                    ['from_wallet_id' => $fromWallet->wallet_id]
                );
            });

            return response()->json([
                'success' => true,
                'message' => 'تم التحويل بنجاح',
                'data' => [
                    'from_wallet' => $fromWallet->fresh(),
                    'to_wallet' => $toWallet->fresh(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في التحويل: ' . $e->getMessage(),
                'error_code' => 'WALLET_TRANSFER_FAILED',
            ], 400);
        }
    }

    /**
     * الحصول على تاريخ المحفظة
     */
    public function getWalletHistory(Request $request, DigitalWallet $wallet): JsonResponse
    {
        $this->authorize('view', $wallet);

        $transactions = $wallet->transactions()
            ->when($request->type, fn($q) => $q->where('type', $request->type))
            ->when($request->date_from, fn($q) => $q->where('created_at', '>=', $request->date_from))
            ->when($request->date_to, fn($q) => $q->where('created_at', '<=', $request->date_to))
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * الحصول على إحصائيات المدفوعات
     */
    public function getPaymentStatistics(Request $request): JsonResponse
    {
        $filters = $request->only([
            'date_from', 'date_to', 'currency', 'gateway_id', 'country'
        ]);

        $statistics = $this->paymentService->getPaymentStatistics($filters);

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * تحليل مخاطر المعاملة
     */
    public function analyzeFraud(Request $request): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'required|exists:payment_transactions,id',
        ]);

        $transaction = PaymentTransaction::findOrFail($request->transaction_id);
        $this->authorize('view', $transaction);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        return response()->json([
            'success' => true,
            'data' => $analysis,
        ]);
    }

    /**
     * الحصول على حالة المعاملة
     */
    public function getTransactionStatus(PaymentTransaction $transaction): JsonResponse
    {
        $this->authorize('view', $transaction);

        return response()->json([
            'success' => true,
            'data' => new PaymentTransactionResource($transaction->load(['gateway', 'paymentMethod', 'refunds'])),
        ]);
    }

    /**
     * إلغاء المعاملة
     */
    public function cancelTransaction(PaymentTransaction $transaction): JsonResponse
    {
        try {
            $this->authorize('cancel', $transaction);

            if (!in_array($transaction->status, ['pending', 'processing'])) {
                throw new \Exception('لا يمكن إلغاء هذه المعاملة');
            }

            $transaction->updateStatus('cancelled', [
                'cancelled_at' => now(),
                'cancelled_by' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء المعاملة بنجاح',
                'data' => new PaymentTransactionResource($transaction),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إلغاء المعاملة: ' . $e->getMessage(),
                'error_code' => 'TRANSACTION_CANCELLATION_FAILED',
            ], 400);
        }
    }

    /**
     * الحصول على البوابات المتاحة
     */
    public function getAvailableGateways(Request $request): JsonResponse
    {
        $country = $request->get('country', 'SA');
        $currency = $request->get('currency', 'SAR');
        $paymentMethod = $request->get('payment_method', 'card');

        $gateways = PaymentGateway::active()
            ->forCountry($country)
            ->forCurrency($currency)
            ->forPaymentMethod($paymentMethod)
            ->orderByPriority()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $gateways->map(function ($gateway) {
                return [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'provider' => $gateway->provider,
                    'type' => $gateway->type,
                    'logo_url' => $gateway->logo_url,
                    'supported_currencies' => $gateway->supported_currencies,
                    'supported_payment_methods' => $gateway->supported_payment_methods,
                ];
            }),
        ]);
    }

    /**
     * اختبار بوابة الدفع
     */
    public function testGateway(PaymentGateway $gateway): JsonResponse
    {
        try {
            $this->authorize('test', $gateway);

            $driver = \App\Domains\Payments\Factories\PaymentGatewayFactory::create($gateway);
            $result = $driver->testConnection();

            return response()->json([
                'success' => true,
                'message' => 'تم اختبار البوابة بنجاح',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في اختبار البوابة: ' . $e->getMessage(),
                'error_code' => 'GATEWAY_TEST_FAILED',
            ], 400);
        }
    }
}
