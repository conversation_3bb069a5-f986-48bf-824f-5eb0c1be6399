<?php

namespace App\Domains\Accounting\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use App\Domains\Accounting\Requests\StoreAccountRequest;
use App\Domains\Accounting\Requests\UpdateAccountRequest;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

/**
 * Web Account Controller
 * تحكم واجهة الويب للحسابات
 */
class AccountController extends Controller
{
    protected AdvancedAccountingEngine $accountingEngine;

    public function __construct(AdvancedAccountingEngine $accountingEngine)
    {
        $this->accountingEngine = $accountingEngine;
        $this->middleware('auth');
    }

    /**
     * عرض قائمة الحسابات
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Account::class);

        $query = Account::with(['parentAccount', 'subAccounts'])
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('account_name', 'like', "%{$search}%")
                          ->orWhere('account_code', 'like', "%{$search}%")
                          ->orWhere('account_name_en', 'like', "%{$search}%");
                });
            })
            ->when($request->type, function ($q, $type) {
                $q->where('account_type', $type);
            })
            ->when($request->category, function ($q, $category) {
                $q->where('account_category', $category);
            });

        $accounts = $query->orderBy('account_code')->paginate(20);

        $accountTypes = [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];

        return view('accounting::accounts.index', compact('accounts', 'accountTypes'));
    }

    /**
     * عرض نموذج إنشاء حساب جديد
     */
    public function create(): View
    {
        $this->authorize('create', Account::class);

        $parentAccounts = Account::whereNull('parent_account_id')
            ->orderBy('account_code')
            ->get();

        $accountTypes = [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];

        $accountCategories = [
            'current_assets' => 'أصول متداولة',
            'fixed_assets' => 'أصول ثابتة',
            'current_liabilities' => 'خصوم متداولة',
            'long_term_liabilities' => 'خصوم طويلة الأجل',
            'capital' => 'رأس المال',
            'retained_earnings' => 'أرباح محتجزة',
            'operating_revenue' => 'إيرادات تشغيلية',
            'other_revenue' => 'إيرادات أخرى',
            'operating_expenses' => 'مصروفات تشغيلية',
            'administrative_expenses' => 'مصروفات إدارية',
        ];

        return view('accounting::accounts.create', compact('parentAccounts', 'accountTypes', 'accountCategories'));
    }

    /**
     * حفظ حساب جديد
     */
    public function store(StoreAccountRequest $request): RedirectResponse
    {
        $this->authorize('create', Account::class);

        DB::beginTransaction();
        try {
            $account = Account::create([
                ...$request->validated(),
                'created_by' => auth()->id(),
                'level' => $this->calculateAccountLevel($request->parent_account_id),
            ]);

            if ($request->opening_balance) {
                $account->updateCurrentBalance();
            }

            DB::commit();

            return redirect()->route('accounting.accounts.index')
                           ->with('success', 'تم إنشاء الحساب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'حدث خطأ أثناء إنشاء الحساب: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل حساب
     */
    public function show(Account $account): View
    {
        $this->authorize('view', $account);

        $account->load([
            'parentAccount',
            'subAccounts',
            'journalEntryDetails.journalEntry',
            'creator',
            'updater'
        ]);

        // الحصول على آخر 20 معاملة
        $recentTransactions = $account->journalEntryDetails()
            ->with(['journalEntry'])
            ->whereHas('journalEntry', function ($q) {
                $q->where('status', 'POSTED');
            })
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('accounting::accounts.show', compact('account', 'recentTransactions'));
    }

    /**
     * عرض نموذج تعديل حساب
     */
    public function edit(Account $account): View
    {
        $this->authorize('update', $account);

        $parentAccounts = Account::whereNull('parent_account_id')
            ->where('id', '!=', $account->id)
            ->orderBy('account_code')
            ->get();

        $accountTypes = [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];

        $accountCategories = [
            'current_assets' => 'أصول متداولة',
            'fixed_assets' => 'أصول ثابتة',
            'current_liabilities' => 'خصوم متداولة',
            'long_term_liabilities' => 'خصوم طويلة الأجل',
            'capital' => 'رأس المال',
            'retained_earnings' => 'أرباح محتجزة',
            'operating_revenue' => 'إيرادات تشغيلية',
            'other_revenue' => 'إيرادات أخرى',
            'operating_expenses' => 'مصروفات تشغيلية',
            'administrative_expenses' => 'مصروفات إدارية',
        ];

        return view('accounting::accounts.edit', compact('account', 'parentAccounts', 'accountTypes', 'accountCategories'));
    }

    /**
     * تحديث حساب
     */
    public function update(UpdateAccountRequest $request, Account $account): RedirectResponse
    {
        $this->authorize('update', $account);

        DB::beginTransaction();
        try {
            $account->update([
                ...$request->validated(),
                'updated_by' => auth()->id(),
            ]);

            if ($request->has('parent_account_id')) {
                $account->update([
                    'level' => $this->calculateAccountLevel($request->parent_account_id)
                ]);
            }

            DB::commit();

            return redirect()->route('accounting.accounts.show', $account)
                           ->with('success', 'تم تحديث الحساب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'حدث خطأ أثناء تحديث الحساب: ' . $e->getMessage());
        }
    }

    /**
     * حذف حساب
     */
    public function destroy(Account $account): RedirectResponse
    {
        $this->authorize('delete', $account);

        if ($account->journalEntryDetails()->exists()) {
            return back()->with('error', 'لا يمكن حذف الحساب لوجود معاملات مرتبطة به');
        }

        if ($account->subAccounts()->exists()) {
            return back()->with('error', 'لا يمكن حذف الحساب لوجود حسابات فرعية');
        }

        $account->delete();

        return redirect()->route('accounting.accounts.index')
                       ->with('success', 'تم حذف الحساب بنجاح');
    }

    /**
     * عرض دليل الحسابات
     */
    public function chartView(): View
    {
        $this->authorize('viewAny', Account::class);

        $accounts = Account::with(['subAccounts' => function ($query) {
            $query->orderBy('account_code');
        }])
        ->whereNull('parent_account_id')
        ->orderBy('account_code')
        ->get();

        return view('accounting::accounts.chart', compact('accounts'));
    }

    /**
     * عرض تاريخ الحساب
     */
    public function history(Request $request, Account $account): View
    {
        $this->authorize('viewHistory', $account);

        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $transactions = $account->journalEntryDetails()
            ->with(['journalEntry'])
            ->whereHas('journalEntry', function ($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('entry_date', [$dateFrom, $dateTo])
                  ->where('status', 'POSTED');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        $runningBalance = $account->opening_balance;
        foreach ($transactions->items() as $transaction) {
            if ($transaction->entry_type === 'debit') {
                $runningBalance += $account->is_debit_nature ? $transaction->amount : -$transaction->amount;
            } else {
                $runningBalance += $account->is_credit_nature ? $transaction->amount : -$transaction->amount;
            }
            $transaction->running_balance = $runningBalance;
        }

        return view('accounting::accounts.history', compact('account', 'transactions', 'dateFrom', 'dateTo'));
    }

    /**
     * عرض رصيد الحساب
     */
    public function balance(Account $account): View
    {
        $this->authorize('view', $account);

        $currentBalance = $account->calculateCurrentBalance();
        $account->updateCurrentBalance();

        $monthlyBalances = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i)->endOfMonth();
            $monthlyBalances[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->translatedFormat('F Y'),
                'balance' => $account->getBalanceAsOf($date->format('Y-m-d')),
            ];
        }

        return view('accounting::accounts.balance', compact('account', 'currentBalance', 'monthlyBalances'));
    }

    /**
     * عرض نموذج تعديل الرصيد
     */
    public function adjustBalanceForm(Account $account): View
    {
        $this->authorize('adjustBalance', $account);

        return view('accounting::accounts.adjust-balance', compact('account'));
    }

    /**
     * تعديل رصيد الحساب
     */
    public function adjustBalance(Request $request, Account $account): RedirectResponse
    {
        $this->authorize('adjustBalance', $account);

        $request->validate([
            'adjustment_amount' => 'required|numeric',
            'adjustment_type' => 'required|in:debit,credit',
            'description' => 'required|string|max:255',
            'reference' => 'nullable|string|max:100',
        ]);

        DB::beginTransaction();
        try {
            $entryData = [
                'entry_date' => now(),
                'description' => $request->description,
                'reference' => $request->reference,
                'source_type' => 'BALANCE_ADJUSTMENT',
                'lines' => [
                    [
                        'account_id' => $account->id,
                        'debit_amount' => $request->adjustment_type === 'debit' ? $request->adjustment_amount : 0,
                        'credit_amount' => $request->adjustment_type === 'credit' ? $request->adjustment_amount : 0,
                        'description' => $request->description,
                    ],
                    [
                        'account_id' => $this->getAdjustmentAccount(),
                        'debit_amount' => $request->adjustment_type === 'credit' ? $request->adjustment_amount : 0,
                        'credit_amount' => $request->adjustment_type === 'debit' ? $request->adjustment_amount : 0,
                        'description' => 'تعديل رصيد - ' . $account->account_name,
                    ],
                ],
            ];

            $entry = $this->accountingEngine->createJournalEntry($entryData);
            $account->updateCurrentBalance();

            DB::commit();

            return redirect()->route('accounting.accounts.show', $account)
                           ->with('success', 'تم تعديل رصيد الحساب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'حدث خطأ أثناء تعديل الرصيد: ' . $e->getMessage());
        }
    }

    /**
     * استيراد الحسابات
     */
    public function import(Request $request): RedirectResponse
    {
        $this->authorize('import', Account::class);

        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx',
        ]);

        try {
            $importedCount = $this->accountingEngine->importAccounts($request->file('file'));

            return back()->with('success', "تم استيراد {$importedCount} حساب بنجاح");

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء الاستيراد: ' . $e->getMessage());
        }
    }

    /**
     * تصدير الحسابات
     */
    public function export(Request $request)
    {
        $this->authorize('export', Account::class);

        $format = $request->input('format', 'excel');

        try {
            return $this->accountingEngine->exportAccounts($format);

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء التصدير: ' . $e->getMessage());
        }
    }

    /**
     * حساب مستوى الحساب
     */
    protected function calculateAccountLevel(?int $parentAccountId): int
    {
        if (!$parentAccountId) {
            return 1;
        }

        $parentAccount = Account::find($parentAccountId);
        return $parentAccount ? $parentAccount->level + 1 : 1;
    }

    /**
     * الحصول على حساب التعديل
     */
    protected function getAdjustmentAccount(): int
    {
        $adjustmentAccount = Account::where('account_code', '9999')->first();

        if (!$adjustmentAccount) {
            $adjustmentAccount = Account::create([
                'account_code' => '9999',
                'account_name' => 'تعديلات الأرصدة',
                'account_name_en' => 'Balance Adjustments',
                'account_type' => 'equity',
                'account_category' => 'retained_earnings',
                'is_system_account' => true,
                'allow_manual_entry' => false,
                'created_by' => auth()->id(),
            ]);
        }

        return $adjustmentAccount->id;
    }
}
