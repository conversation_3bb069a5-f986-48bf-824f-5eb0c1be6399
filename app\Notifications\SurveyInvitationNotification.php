<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Domains\Projects\Models\ProjectSurvey;

/**
 * إشعار دعوة الاستبيان - Survey Invitation Notification
 */
class SurveyInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected ProjectSurvey $survey;

    public function __construct(ProjectSurvey $survey)
    {
        $this->survey = $survey;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('دعوة للمشاركة في استبيان: ' . $this->survey->title)
                    ->line('تم دعوتك للمشاركة في استبيان المشروع.')
                    ->line('عنوان الاستبيان: ' . $this->survey->title)
                    ->line('وصف الاستبيان: ' . $this->survey->description)
                    ->action('المشاركة في الاستبيان', url('/projects/' . $this->survey->project_id . '/surveys/' . $this->survey->id))
                    ->line('شكراً لمشاركتك!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'survey_invitation',
            'survey_id' => $this->survey->id,
            'project_id' => $this->survey->project_id,
            'title' => $this->survey->title,
            'message' => 'تم دعوتك للمشاركة في استبيان: ' . $this->survey->title,
        ];
    }
}
