<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج وسم قاعدة المعرفة - Knowledge Base Tag
 */
class KnowledgeBaseTag extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'usage_count',
        'is_featured',
    ];

    protected $casts = [
        'usage_count' => 'integer',
        'is_featured' => 'boolean',
    ];

    public function articles(): BelongsToMany
    {
        return $this->belongsToMany(KnowledgeBaseArticle::class, 'knowledge_base_article_tags');
    }

    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    public function decrementUsage(): void
    {
        $this->decrement('usage_count');
    }

    public function scopePopular($query, int $limit = 20)
    {
        return $query->orderBy('usage_count', 'desc')->limit($limit);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function getUrlAttribute(): string
    {
        return route('knowledge-base.tag', ['slug' => $this->slug]);
    }
}
