<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Accounting\Controllers\AccountController;
use App\Domains\Accounting\Controllers\InvoiceController;
use App\Domains\Accounting\Controllers\JournalEntryController;
use App\Domains\Accounting\Controllers\PaymentController;
use App\Domains\Accounting\Controllers\BankAccountController;
use App\Domains\Accounting\Controllers\FinancialReportController;
use App\Domains\Accounting\Controllers\BudgetController;
use App\Domains\Accounting\Controllers\AccountingPeriodController;
use App\Domains\Accounting\Controllers\TaxRateController;
use App\Domains\Accounting\Controllers\AccountingAnalyticsController;

/*
|--------------------------------------------------------------------------
| Accounting API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام المحاسبة
|
*/

// Chart of Accounts Management
Route::apiResource('accounts', AccountController::class);
Route::prefix('accounts')->group(function () {
    Route::get('chart', [AccountController::class, 'getChartOfAccounts']);
    Route::post('import-chart', [AccountController::class, 'importChartOfAccounts']);
    Route::get('{account}/balance', [AccountController::class, 'getAccountBalance']);
    Route::get('{account}/history', [AccountController::class, 'getAccountHistory']);
    Route::get('{account}/trial-balance', [AccountController::class, 'getTrialBalance']);
    Route::post('{account}/adjust-balance', [AccountController::class, 'adjustBalance']);
    Route::post('bulk-update', [AccountController::class, 'bulkUpdate']);
    Route::get('search', [AccountController::class, 'search']);
    Route::get('by-type/{type}', [AccountController::class, 'getAccountsByType']);
    Route::get('by-category/{category}', [AccountController::class, 'getAccountsByCategory']);
});

// Invoice Management
Route::apiResource('invoices', InvoiceController::class);
Route::prefix('invoices')->group(function () {
    Route::post('{invoice}/send', [InvoiceController::class, 'sendInvoice']);
    Route::post('{invoice}/approve', [InvoiceController::class, 'approveInvoice']);
    Route::post('{invoice}/reject', [InvoiceController::class, 'rejectInvoice']);
    Route::post('{invoice}/cancel', [InvoiceController::class, 'cancelInvoice']);
    Route::post('{invoice}/duplicate', [InvoiceController::class, 'duplicateInvoice']);
    Route::get('{invoice}/pdf', [InvoiceController::class, 'generatePDF']);
    Route::get('{invoice}/payments', [InvoiceController::class, 'getInvoicePayments']);
    Route::post('{invoice}/record-payment', [InvoiceController::class, 'recordPayment']);
    Route::get('overdue', [InvoiceController::class, 'getOverdueInvoices']);
    Route::get('recurring', [InvoiceController::class, 'getRecurringInvoices']);
    Route::post('bulk-send', [InvoiceController::class, 'bulkSendInvoices']);
    Route::get('analytics', [InvoiceController::class, 'getInvoiceAnalytics']);
});

// Journal Entries Management
Route::apiResource('journal-entries', JournalEntryController::class);
Route::prefix('journal-entries')->group(function () {
    Route::post('{entry}/post', [JournalEntryController::class, 'postEntry']);
    Route::post('{entry}/reverse', [JournalEntryController::class, 'reverseEntry']);
    Route::post('{entry}/approve', [JournalEntryController::class, 'approveEntry']);
    Route::get('unposted', [JournalEntryController::class, 'getUnpostedEntries']);
    Route::get('pending-approval', [JournalEntryController::class, 'getPendingApprovalEntries']);
    Route::post('auto-generate', [JournalEntryController::class, 'autoGenerateEntries']);
    Route::get('templates', [JournalEntryController::class, 'getEntryTemplates']);
    Route::post('from-template', [JournalEntryController::class, 'createFromTemplate']);
    Route::get('audit-trail', [JournalEntryController::class, 'getAuditTrail']);
});

// Payment Management
Route::apiResource('payments', PaymentController::class);
Route::prefix('payments')->group(function () {
    Route::post('{payment}/approve', [PaymentController::class, 'approvePayment']);
    Route::post('{payment}/reject', [PaymentController::class, 'rejectPayment']);
    Route::post('{payment}/void', [PaymentController::class, 'voidPayment']);
    Route::get('pending', [PaymentController::class, 'getPendingPayments']);
    Route::get('by-method/{method}', [PaymentController::class, 'getPaymentsByMethod']);
    Route::post('bulk-approve', [PaymentController::class, 'bulkApprovePayments']);
    Route::get('reconciliation', [PaymentController::class, 'getReconciliationData']);
    Route::post('auto-match', [PaymentController::class, 'autoMatchPayments']);
});

// Bank Account Management
Route::apiResource('bank-accounts', BankAccountController::class);
Route::prefix('bank-accounts')->group(function () {
    Route::post('{account}/reconcile', [BankAccountController::class, 'reconcileAccount']);
    Route::get('{account}/transactions', [BankAccountController::class, 'getTransactions']);
    Route::post('{account}/import-transactions', [BankAccountController::class, 'importTransactions']);
    Route::get('{account}/balance', [BankAccountController::class, 'getBalance']);
    Route::post('{account}/auto-categorize', [BankAccountController::class, 'autoCategorizeTransactions']);
    Route::get('reconciliation-status', [BankAccountController::class, 'getReconciliationStatus']);
});

// Financial Reports
Route::prefix('reports')->group(function () {
    Route::get('balance-sheet', [FinancialReportController::class, 'getBalanceSheet']);
    Route::get('income-statement', [FinancialReportController::class, 'getIncomeStatement']);
    Route::get('cash-flow', [FinancialReportController::class, 'getCashFlowStatement']);
    Route::get('trial-balance', [FinancialReportController::class, 'getTrialBalance']);
    Route::get('general-ledger', [FinancialReportController::class, 'getGeneralLedger']);
    Route::get('accounts-receivable', [FinancialReportController::class, 'getAccountsReceivableReport']);
    Route::get('accounts-payable', [FinancialReportController::class, 'getAccountsPayableReport']);
    Route::get('aged-receivables', [FinancialReportController::class, 'getAgedReceivablesReport']);
    Route::get('aged-payables', [FinancialReportController::class, 'getAgedPayablesReport']);
    Route::get('profit-loss', [FinancialReportController::class, 'getProfitLossReport']);
    Route::get('budget-variance', [FinancialReportController::class, 'getBudgetVarianceReport']);
    Route::get('custom', [FinancialReportController::class, 'generateCustomReport']);
    Route::post('schedule', [FinancialReportController::class, 'scheduleReport']);
    Route::get('scheduled', [FinancialReportController::class, 'getScheduledReports']);
});

// Budget Management
Route::apiResource('budgets', BudgetController::class);
Route::prefix('budgets')->group(function () {
    Route::post('{budget}/approve', [BudgetController::class, 'approveBudget']);
    Route::post('{budget}/activate', [BudgetController::class, 'activateBudget']);
    Route::get('{budget}/variance', [BudgetController::class, 'getBudgetVariance']);
    Route::get('{budget}/performance', [BudgetController::class, 'getBudgetPerformance']);
    Route::post('{budget}/revise', [BudgetController::class, 'reviseBudget']);
    Route::get('templates', [BudgetController::class, 'getBudgetTemplates']);
    Route::post('from-template', [BudgetController::class, 'createFromTemplate']);
    Route::get('alerts', [BudgetController::class, 'getBudgetAlerts']);
});

// Accounting Periods
Route::apiResource('periods', AccountingPeriodController::class);
Route::prefix('periods')->group(function () {
    Route::post('{period}/close', [AccountingPeriodController::class, 'closePeriod']);
    Route::post('{period}/reopen', [AccountingPeriodController::class, 'reopenPeriod']);
    Route::get('{period}/closing-entries', [AccountingPeriodController::class, 'getClosingEntries']);
    Route::post('{period}/year-end-close', [AccountingPeriodController::class, 'yearEndClose']);
    Route::get('current', [AccountingPeriodController::class, 'getCurrentPeriod']);
    Route::get('open', [AccountingPeriodController::class, 'getOpenPeriods']);
});

// Tax Rates Management
Route::apiResource('tax-rates', TaxRateController::class);
Route::prefix('tax-rates')->group(function () {
    Route::get('by-country/{country}', [TaxRateController::class, 'getTaxRatesByCountry']);
    Route::get('current', [TaxRateController::class, 'getCurrentTaxRates']);
    Route::post('calculate', [TaxRateController::class, 'calculateTax']);
    Route::get('compliance-check', [TaxRateController::class, 'checkCompliance']);
});

// Analytics and Insights
Route::prefix('analytics')->group(function () {
    Route::get('dashboard', [AccountingAnalyticsController::class, 'getDashboard']);
    Route::get('financial-health', [AccountingAnalyticsController::class, 'getFinancialHealth']);
    Route::get('cash-flow-forecast', [AccountingAnalyticsController::class, 'getCashFlowForecast']);
    Route::get('expense-analysis', [AccountingAnalyticsController::class, 'getExpenseAnalysis']);
    Route::get('revenue-analysis', [AccountingAnalyticsController::class, 'getRevenueAnalysis']);
    Route::get('profitability-analysis', [AccountingAnalyticsController::class, 'getProfitabilityAnalysis']);
    Route::get('trend-analysis', [AccountingAnalyticsController::class, 'getTrendAnalysis']);
    Route::get('ratio-analysis', [AccountingAnalyticsController::class, 'getRatioAnalysis']);
    Route::get('benchmarking', [AccountingAnalyticsController::class, 'getBenchmarkingData']);
    Route::get('predictive-insights', [AccountingAnalyticsController::class, 'getPredictiveInsights']);
});

// AI and Automation
Route::prefix('ai')->group(function () {
    Route::post('categorize-transaction', [AccountingAnalyticsController::class, 'categorizeTransaction']);
    Route::post('detect-anomalies', [AccountingAnalyticsController::class, 'detectAnomalies']);
    Route::post('generate-insights', [AccountingAnalyticsController::class, 'generateInsights']);
    Route::post('predict-cash-flow', [AccountingAnalyticsController::class, 'predictCashFlow']);
    Route::post('recommend-actions', [AccountingAnalyticsController::class, 'recommendActions']);
    Route::post('ocr-invoice', [AccountingAnalyticsController::class, 'processInvoiceOCR']);
    Route::post('smart-reconciliation', [AccountingAnalyticsController::class, 'smartReconciliation']);
});

// Import/Export
Route::prefix('import-export')->group(function () {
    Route::post('import-accounts', [AccountController::class, 'importAccounts']);
    Route::post('import-transactions', [JournalEntryController::class, 'importTransactions']);
    Route::post('import-invoices', [InvoiceController::class, 'importInvoices']);
    Route::get('export-accounts', [AccountController::class, 'exportAccounts']);
    Route::get('export-transactions', [JournalEntryController::class, 'exportTransactions']);
    Route::get('export-reports', [FinancialReportController::class, 'exportReports']);
    Route::post('backup-data', [AccountingAnalyticsController::class, 'backupAccountingData']);
    Route::post('restore-data', [AccountingAnalyticsController::class, 'restoreAccountingData']);
});

// Compliance and Audit
Route::prefix('compliance')->group(function () {
    Route::get('audit-trail', [AccountingAnalyticsController::class, 'getAuditTrail']);
    Route::get('compliance-status', [AccountingAnalyticsController::class, 'getComplianceStatus']);
    Route::post('generate-audit-report', [AccountingAnalyticsController::class, 'generateAuditReport']);
    Route::get('regulatory-reports', [AccountingAnalyticsController::class, 'getRegulatoryReports']);
    Route::post('validate-entries', [AccountingAnalyticsController::class, 'validateEntries']);
});

// System Administration
Route::prefix('admin')->group(function () {
    Route::get('system-health', [AccountingAnalyticsController::class, 'getSystemHealth']);
    Route::post('recalculate-balances', [AccountController::class, 'recalculateAllBalances']);
    Route::post('fix-data-integrity', [AccountingAnalyticsController::class, 'fixDataIntegrity']);
    Route::get('performance-metrics', [AccountingAnalyticsController::class, 'getPerformanceMetrics']);
    Route::post('optimize-database', [AccountingAnalyticsController::class, 'optimizeDatabase']);
});
