<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\TaskDependency;
use App\Domains\Projects\Events\TaskCreated;
use App\Domains\Projects\Events\TaskUpdated;
use App\Domains\Projects\Events\TaskStatusChanged;
use App\Domains\Projects\Events\TaskAssigned;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة إدارة المهام المتقدمة
 * إدارة شاملة للمهام مع جميع الميزات المتقدمة
 */
class TaskManagementService
{
    /**
     * إنشاء مهمة جديدة
     */
    public function createTask(array $taskData): Task
    {
        DB::beginTransaction();

        try {
            // إنشاء المهمة الأساسية
            $task = Task::create([
                'title' => $taskData['title'],
                'description' => $taskData['description'] ?? null,
                'task_number' => $taskData['task_number'] ?? $this->generateTaskNumber($taskData['project_id']),
                'project_id' => $taskData['project_id'],
                'parent_id' => $taskData['parent_id'] ?? null,
                'epic_id' => $taskData['epic_id'] ?? null,
                'assignee_id' => $taskData['assignee_id'] ?? null,
                'reporter_id' => $taskData['reporter_id'] ?? auth()->id(),
                'reviewer_id' => $taskData['reviewer_id'] ?? null,
                'status' => $taskData['status'] ?? 'TODO',
                'priority' => $taskData['priority'] ?? 'MEDIUM',
                'type' => $taskData['type'] ?? 'TASK',
                'category' => $taskData['category'] ?? 'DEVELOPMENT',
                'start_date' => $taskData['start_date'] ?? null,
                'due_date' => $taskData['due_date'] ?? null,
                'estimated_hours' => $taskData['estimated_hours'] ?? null,
                'remaining_hours' => $taskData['remaining_hours'] ?? $taskData['estimated_hours'] ?? null,
                'progress_percentage' => $taskData['progress_percentage'] ?? 0,
                'story_points' => $taskData['story_points'] ?? null,
                'complexity_score' => $taskData['complexity_score'] ?? null,
                'sprint_id' => $taskData['sprint_id'] ?? null,
                'milestone_id' => $taskData['milestone_id'] ?? null,
                'risk_level' => $taskData['risk_level'] ?? 'LOW',
                'quality_gate' => $taskData['quality_gate'] ?? 'BASIC',
                'labels' => $taskData['labels'] ?? [],
                'tags' => $taskData['tags'] ?? [],
                'custom_fields' => $taskData['custom_fields'] ?? [],
                'is_billable' => $taskData['is_billable'] ?? true,
                'hourly_rate' => $taskData['hourly_rate'] ?? null,
                'notes' => $taskData['notes'] ?? null,
                'metadata' => $taskData['metadata'] ?? [],
            ]);

            // إضافة معايير القبول
            if (isset($taskData['acceptance_criteria'])) {
                foreach ($taskData['acceptance_criteria'] as $criteria) {
                    $task->acceptanceCriteria()->create([
                        'description' => $criteria['description'],
                        'is_completed' => $criteria['is_completed'] ?? false,
                    ]);
                }
            }

            // إضافة التبعيات
            if (isset($taskData['dependencies'])) {
                foreach ($taskData['dependencies'] as $dependency) {
                    $this->addTaskDependency($task, $dependency['task_id'], $dependency['type']);
                }
            }

            // إنشاء المهام الفرعية
            if (isset($taskData['subtasks'])) {
                foreach ($taskData['subtasks'] as $subtaskData) {
                    $this->createTask(array_merge($subtaskData, [
                        'project_id' => $task->project_id,
                        'parent_id' => $task->id,
                    ]));
                }
            }

            // إضافة المتابعين
            if (isset($taskData['watchers'])) {
                $task->watchers()->sync($taskData['watchers']);
            }

            // معالجة المرفقات
            if (isset($taskData['attachments'])) {
                $this->handleTaskAttachments($task, $taskData['attachments']);
            }

            // إرسال الإشعارات
            $this->sendTaskNotifications($task, $taskData);

            // إطلاق الحدث
            Event::dispatch(new TaskCreated($task));

            DB::commit();

            return $task->load(['project', 'assignee', 'reporter', 'parent']);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تحديث المهمة
     */
    public function updateTask(Task $task, array $updateData): Task
    {
        DB::beginTransaction();

        try {
            $originalData = $task->toArray();

            // تحديث البيانات الأساسية
            $task->update($updateData);

            // تحديث معايير القبول إذا تم تقديمها
            if (isset($updateData['acceptance_criteria'])) {
                $task->acceptanceCriteria()->delete();
                foreach ($updateData['acceptance_criteria'] as $criteria) {
                    $task->acceptanceCriteria()->create([
                        'description' => $criteria['description'],
                        'is_completed' => $criteria['is_completed'] ?? false,
                    ]);
                }
            }

            // تحديث المتابعين
            if (isset($updateData['watchers'])) {
                $task->watchers()->sync($updateData['watchers']);
            }

            // تحديث التقدم التلقائي للمهمة الأب
            if ($task->parent_id) {
                $this->updateParentTaskProgress($task->parent);
            }

            // تحديث تقدم المشروع إذا كان التحديث التلقائي مفعلاً
            if ($task->project->auto_calculate_progress) {
                $this->updateProjectProgress($task->project);
            }

            // إطلاق الحدث
            Event::dispatch(new TaskUpdated($task, $originalData));

            DB::commit();

            return $task->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تغيير حالة المهمة
     */
    public function changeTaskStatus(Task $task, string $newStatus, ?string $comment = null): Task
    {
        DB::beginTransaction();

        try {
            $oldStatus = $task->status;

            // التحقق من صحة التغيير
            $this->validateStatusChange($task, $newStatus);

            // تحديث الحالة
            $task->update([
                'status' => $newStatus,
                'completed_at' => $newStatus === 'DONE' ? now() : null,
                'progress_percentage' => $newStatus === 'DONE' ? 100 : $task->progress_percentage,
            ]);

            // إضافة تعليق إذا تم تقديمه
            if ($comment) {
                $task->comments()->create([
                    'user_id' => auth()->id(),
                    'content' => $comment,
                    'type' => 'STATUS_CHANGE',
                ]);
            }

            // تسجيل النشاط
            $task->activities()->create([
                'user_id' => auth()->id(),
                'action' => 'status_changed',
                'description' => "تم تغيير حالة المهمة من {$oldStatus} إلى {$newStatus}",
                'metadata' => [
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'comment' => $comment,
                ],
            ]);

            // تحديث تقدم المهمة الأب
            if ($task->parent_id) {
                $this->updateParentTaskProgress($task->parent);
            }

            // تحديث تقدم المشروع
            if ($task->project->auto_calculate_progress) {
                $this->updateProjectProgress($task->project);
            }

            // إطلاق الحدث
            Event::dispatch(new TaskStatusChanged($task, $oldStatus, $newStatus));

            DB::commit();

            return $task->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تعيين المهمة لمستخدم
     */
    public function assignTask(Task $task, int $assigneeId, ?string $comment = null): Task
    {
        DB::beginTransaction();

        try {
            $oldAssigneeId = $task->assignee_id;

            $task->update(['assignee_id' => $assigneeId]);

            // إضافة تعليق
            if ($comment) {
                $task->comments()->create([
                    'user_id' => auth()->id(),
                    'content' => $comment,
                    'type' => 'ASSIGNMENT',
                ]);
            }

            // تسجيل النشاط
            $assignee = \App\Models\User::find($assigneeId);
            $task->activities()->create([
                'user_id' => auth()->id(),
                'action' => 'task_assigned',
                'description' => "تم تعيين المهمة لـ {$assignee->name}",
                'metadata' => [
                    'old_assignee_id' => $oldAssigneeId,
                    'new_assignee_id' => $assigneeId,
                    'comment' => $comment,
                ],
            ]);

            // إطلاق الحدث
            Event::dispatch(new TaskAssigned($task, $assigneeId, $oldAssigneeId));

            DB::commit();

            return $task->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تحديث تقدم المهمة
     */
    public function updateTaskProgress(Task $task, int $progressPercentage, ?string $comment = null): Task
    {
        DB::beginTransaction();

        try {
            $oldProgress = $task->progress_percentage;

            $task->update([
                'progress_percentage' => $progressPercentage,
                'status' => $progressPercentage === 100 ? 'DONE' : 
                           ($progressPercentage > 0 ? 'IN_PROGRESS' : $task->status),
                'completed_at' => $progressPercentage === 100 ? now() : null,
            ]);

            // إضافة تعليق
            if ($comment) {
                $task->comments()->create([
                    'user_id' => auth()->id(),
                    'content' => $comment,
                    'type' => 'PROGRESS_UPDATE',
                ]);
            }

            // تسجيل النشاط
            $task->activities()->create([
                'user_id' => auth()->id(),
                'action' => 'progress_updated',
                'description' => "تم تحديث تقدم المهمة من {$oldProgress}% إلى {$progressPercentage}%",
                'metadata' => [
                    'old_progress' => $oldProgress,
                    'new_progress' => $progressPercentage,
                    'comment' => $comment,
                ],
            ]);

            // تحديث تقدم المهمة الأب
            if ($task->parent_id) {
                $this->updateParentTaskProgress($task->parent);
            }

            // تحديث تقدم المشروع
            if ($task->project->auto_calculate_progress) {
                $this->updateProjectProgress($task->project);
            }

            DB::commit();

            return $task->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إضافة تبعية للمهمة
     */
    public function addTaskDependency(Task $task, int $dependentTaskId, string $dependencyType): void
    {
        // التحقق من عدم وجود تبعية دائرية
        if ($this->wouldCreateCircularDependency($task->id, $dependentTaskId)) {
            throw new \Exception('لا يمكن إضافة هذه التبعية لأنها ستؤدي إلى تبعية دائرية');
        }

        TaskDependency::updateOrCreate([
            'task_id' => $task->id,
            'dependent_task_id' => $dependentTaskId,
        ], [
            'dependency_type' => $dependencyType,
        ]);
    }

    /**
     * الحصول على بيانات Kanban Board
     */
    public function getKanbanBoardData(int $projectId, ?int $sprintId = null): array
    {
        $cacheKey = "kanban_board_{$projectId}" . ($sprintId ? "_{$sprintId}" : '');

        return Cache::remember($cacheKey, 300, function () use ($projectId, $sprintId) {
            $query = Task::where('project_id', $projectId)
                        ->with(['assignee', 'labels', 'attachments']);

            if ($sprintId) {
                $query->where('sprint_id', $sprintId);
            }

            $tasks = $query->get();

            $columns = [
                'TODO' => ['name' => 'قائمة المهام', 'tasks' => []],
                'IN_PROGRESS' => ['name' => 'قيد التنفيذ', 'tasks' => []],
                'IN_REVIEW' => ['name' => 'قيد المراجعة', 'tasks' => []],
                'TESTING' => ['name' => 'قيد الاختبار', 'tasks' => []],
                'DONE' => ['name' => 'مكتملة', 'tasks' => []],
            ];

            foreach ($tasks as $task) {
                $columns[$task->status]['tasks'][] = [
                    'id' => $task->id,
                    'title' => $task->title,
                    'task_number' => $task->task_number,
                    'priority' => $task->priority,
                    'assignee' => $task->assignee ? [
                        'id' => $task->assignee->id,
                        'name' => $task->assignee->name,
                        'avatar' => $task->assignee->avatar,
                    ] : null,
                    'due_date' => $task->due_date?->format('Y-m-d'),
                    'is_overdue' => $task->due_date && $task->due_date < now() && $task->status !== 'DONE',
                    'story_points' => $task->story_points,
                    'labels' => $task->labels,
                    'attachments_count' => $task->attachments->count(),
                    'comments_count' => $task->comments->count(),
                ];
            }

            return $columns;
        });
    }

    /**
     * تحديث ترتيب المهام في Kanban
     */
    public function updateKanbanOrder(array $tasks): void
    {
        DB::beginTransaction();

        try {
            foreach ($tasks as $taskData) {
                Task::where('id', $taskData['id'])->update([
                    'status' => $taskData['status'],
                    'kanban_order' => $taskData['order'],
                ]);
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * الحصول على بيانات تتبع الوقت للمهمة
     */
    public function getTimeTrackingData(Task $task): array
    {
        return [
            'total_logged_hours' => $task->timeEntries->sum('hours'),
            'billable_hours' => $task->timeEntries->where('is_billable', true)->sum('hours'),
            'estimated_hours' => $task->estimated_hours,
            'remaining_hours' => $task->remaining_hours,
            'time_utilization' => $task->estimated_hours > 0 ? 
                round(($task->timeEntries->sum('hours') / $task->estimated_hours) * 100, 2) : null,
            'recent_entries' => $task->timeEntries()
                ->with('user')
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($entry) {
                    return [
                        'id' => $entry->id,
                        'user' => $entry->user->name,
                        'hours' => $entry->hours,
                        'description' => $entry->description,
                        'date' => $entry->start_time->format('Y-m-d'),
                        'is_billable' => $entry->is_billable,
                    ];
                }),
        ];
    }

    /**
     * الحصول على تاريخ التقدم
     */
    public function getProgressHistory(Task $task): Collection
    {
        return $task->activities()
            ->where('action', 'progress_updated')
            ->orderBy('created_at')
            ->get()
            ->map(function ($activity) {
                return [
                    'date' => $activity->created_at->format('Y-m-d'),
                    'progress' => $activity->metadata['new_progress'] ?? 0,
                    'user' => $activity->user->name,
                ];
            });
    }

    /**
     * الحصول على المهام ذات الصلة
     */
    public function getRelatedTasks(Task $task): Collection
    {
        return Task::where('project_id', $task->project_id)
            ->where('id', '!=', $task->id)
            ->where(function ($query) use ($task) {
                $query->where('assignee_id', $task->assignee_id)
                      ->orWhere('epic_id', $task->epic_id)
                      ->orWhere('milestone_id', $task->milestone_id);
            })
            ->limit(10)
            ->get(['id', 'title', 'task_number', 'status', 'priority']);
    }

    /**
     * الحصول على المشاكل المعطلة
     */
    public function getBlockingIssues(Task $task): Collection
    {
        return $task->dependencies()
            ->where('dependency_type', 'BLOCKS')
            ->whereHas('dependentTask', function ($query) {
                $query->whereNotIn('status', ['DONE', 'CANCELLED']);
            })
            ->with('dependentTask')
            ->get()
            ->pluck('dependentTask');
    }

    /**
     * الحصول على مقاييس الأداء
     */
    public function getPerformanceMetrics(Task $task): array
    {
        $createdAt = $task->created_at;
        $completedAt = $task->completed_at;
        $dueDate = $task->due_date;

        return [
            'cycle_time' => $completedAt ? $createdAt->diffInHours($completedAt) : null,
            'lead_time' => $completedAt && $dueDate ? $dueDate->diffInHours($completedAt) : null,
            'is_on_time' => $completedAt && $dueDate ? $completedAt <= $dueDate : null,
            'efficiency_score' => $this->calculateEfficiencyScore($task),
            'quality_score' => $task->quality_score,
            'complexity_vs_effort' => $this->calculateComplexityVsEffort($task),
        ];
    }

    /**
     * إنشاء رقم المهمة
     */
    protected function generateTaskNumber(int $projectId): string
    {
        $project = Project::find($projectId);
        $projectCode = $project ? $project->code : 'PROJ';
        
        $lastTask = Task::where('project_id', $projectId)
                       ->orderBy('id', 'desc')
                       ->first();

        $sequence = $lastTask ? (int) substr($lastTask->task_number, -4) + 1 : 1;

        return "{$projectCode}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * التحقق من صحة تغيير الحالة
     */
    protected function validateStatusChange(Task $task, string $newStatus): void
    {
        $validTransitions = [
            'TODO' => ['IN_PROGRESS', 'CANCELLED'],
            'IN_PROGRESS' => ['IN_REVIEW', 'TESTING', 'DONE', 'TODO', 'CANCELLED'],
            'IN_REVIEW' => ['TESTING', 'DONE', 'IN_PROGRESS', 'CANCELLED'],
            'TESTING' => ['DONE', 'IN_REVIEW', 'IN_PROGRESS', 'CANCELLED'],
            'DONE' => ['IN_PROGRESS'], // يمكن إعادة فتح المهمة
            'CANCELLED' => ['TODO'], // يمكن إعادة تفعيل المهمة
        ];

        $currentStatus = $task->status;
        
        if (!isset($validTransitions[$currentStatus]) || 
            !in_array($newStatus, $validTransitions[$currentStatus])) {
            throw new \Exception("لا يمكن تغيير حالة المهمة من {$currentStatus} إلى {$newStatus}");
        }

        // التحقق من اكتمال معايير القبول عند الانتهاء
        if ($newStatus === 'DONE') {
            $incompleteCriteria = $task->acceptanceCriteria()
                ->where('is_completed', false)
                ->count();
            
            if ($incompleteCriteria > 0) {
                throw new \Exception('يجب إكمال جميع معايير القبول قبل إنهاء المهمة');
            }
        }
    }

    /**
     * التحقق من التبعية الدائرية
     */
    protected function wouldCreateCircularDependency(int $taskId, int $dependentTaskId): bool
    {
        // تنفيذ خوارزمية للتحقق من التبعية الدائرية
        $visited = [];
        return $this->hasCircularDependency($dependentTaskId, $taskId, $visited);
    }

    /**
     * التحقق من وجود تبعية دائرية
     */
    protected function hasCircularDependency(int $currentTaskId, int $targetTaskId, array &$visited): bool
    {
        if ($currentTaskId === $targetTaskId) {
            return true;
        }

        if (in_array($currentTaskId, $visited)) {
            return false;
        }

        $visited[] = $currentTaskId;

        $dependencies = TaskDependency::where('task_id', $currentTaskId)
            ->pluck('dependent_task_id');

        foreach ($dependencies as $dependentId) {
            if ($this->hasCircularDependency($dependentId, $targetTaskId, $visited)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تحديث تقدم المهمة الأب
     */
    protected function updateParentTaskProgress(Task $parentTask): void
    {
        $children = $parentTask->children;
        
        if ($children->count() === 0) {
            return;
        }

        $averageProgress = $children->avg('progress_percentage');
        
        $parentTask->update([
            'progress_percentage' => round($averageProgress),
            'status' => $averageProgress === 100 ? 'DONE' : 
                       ($averageProgress > 0 ? 'IN_PROGRESS' : $parentTask->status),
        ]);
    }

    /**
     * تحديث تقدم المشروع
     */
    protected function updateProjectProgress(Project $project): void
    {
        $tasks = $project->tasks()->whereNull('parent_id')->get();
        
        if ($tasks->count() === 0) {
            return;
        }

        $averageProgress = $tasks->avg('progress_percentage');
        
        $project->update([
            'progress_percentage' => round($averageProgress),
        ]);
    }

    /**
     * معالجة مرفقات المهمة
     */
    protected function handleTaskAttachments(Task $task, array $attachments): void
    {
        foreach ($attachments as $attachment) {
            // معالجة رفع الملفات
            // يمكن تطوير هذا حسب نظام إدارة الملفات المستخدم
        }
    }

    /**
     * إرسال إشعارات المهمة
     */
    protected function sendTaskNotifications(Task $task, array $taskData): void
    {
        // إرسال إشعار للمكلف
        if (($taskData['notify_assignee'] ?? true) && $task->assignee_id) {
            // إرسال إشعار
        }

        // إرسال إشعار لمدير المشروع
        if (($taskData['notify_project_manager'] ?? true) && $task->project->project_manager_id) {
            // إرسال إشعار
        }

        // إرسال إشعار للمتابعين
        if (($taskData['notify_watchers'] ?? false) && isset($taskData['watchers'])) {
            // إرسال إشعارات
        }
    }

    /**
     * حساب درجة الكفاءة
     */
    protected function calculateEfficiencyScore(Task $task): ?float
    {
        if (!$task->estimated_hours || !$task->timeEntries->count()) {
            return null;
        }

        $actualHours = $task->timeEntries->sum('hours');
        $estimatedHours = $task->estimated_hours;

        if ($actualHours <= $estimatedHours) {
            return 100;
        }

        return max(0, 100 - (($actualHours - $estimatedHours) / $estimatedHours * 50));
    }

    /**
     * حساب التعقيد مقابل الجهد
     */
    protected function calculateComplexityVsEffort(Task $task): ?array
    {
        if (!$task->complexity_score || !$task->timeEntries->count()) {
            return null;
        }

        $actualHours = $task->timeEntries->sum('hours');
        $complexityScore = $task->complexity_score;

        return [
            'complexity_score' => $complexityScore,
            'actual_hours' => $actualHours,
            'hours_per_complexity_point' => round($actualHours / $complexityScore, 2),
            'efficiency_rating' => $this->getEfficiencyRating($actualHours, $complexityScore),
        ];
    }

    /**
     * الحصول على تقييم الكفاءة
     */
    protected function getEfficiencyRating(float $actualHours, int $complexityScore): string
    {
        $ratio = $actualHours / $complexityScore;

        if ($ratio <= 2) return 'EXCELLENT';
        if ($ratio <= 4) return 'GOOD';
        if ($ratio <= 6) return 'AVERAGE';
        return 'POOR';
    }
}
