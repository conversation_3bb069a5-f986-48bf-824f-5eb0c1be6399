<?php

namespace App\Domains\Taxation\Services;

use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Models\TaxRule;
use App\Domains\Taxation\Models\TaxReturn;
use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\JournalEntry;
use Illuminate\Support\Facades\Cache;


/**
 * محرك الضرائب الديناميكي
 * يدعم حساب الضرائب المعقدة والتكامل مع الهيئات الضريبية
 */
class DynamicTaxEngine
{
    protected TaxSystem $taxSystem;
    protected array $cachedRules = [];

    public function __construct(?TaxSystem $taxSystem = null)
    {
        $this->taxSystem = $taxSystem ?? TaxSystem::getDefault() ??
            throw new \Exception('لم يتم تعيين نظام ضريبي افتراضي');
    }

    /**
     * حساب ضريبة القيمة المضافة
     */
    public function calculateVAT(float $amount, string $category = 'GENERAL', array $context = []): array
    {
        $rules = $this->getTaxRules('VAT', $category);

        if (empty($rules)) {
            // استخدام المعدل الافتراضي
            return $this->taxSystem->calculateVAT($amount, 'standard');
        }

        $bestRule = $this->selectBestRule($rules, $amount, $context);
        return $bestRule->calculateTax($amount, $context);
    }

    /**
     * حساب ضريبة الشركات
     */
    public function calculateCorporateTax(float $taxableIncome, array $context = []): array
    {
        $rules = $this->getTaxRules('CORPORATE_TAX');

        if (empty($rules)) {
            return $this->taxSystem->calculateCorporateTax($taxableIncome);
        }

        $totalTax = 0;
        $breakdown = [];

        foreach ($rules as $rule) {
            $calculation = $rule->calculateTax($taxableIncome, $context);
            if ($calculation['applicable']) {
                $totalTax += $calculation['tax_amount'];
                $breakdown[] = $calculation;
            }
        }

        return [
            'taxable_income' => $taxableIncome,
            'total_tax' => round($totalTax, 2),
            'net_income' => round($taxableIncome - $totalTax, 2),
            'effective_rate' => $taxableIncome > 0 ? round(($totalTax / $taxableIncome) * 100, 2) : 0,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * حساب ضريبة الدخل الشخصي
     */
    public function calculatePersonalTax(float $income, array $context = []): array
    {
        $rules = $this->getTaxRules('INCOME_TAX');

        if (empty($rules)) {
            return $this->taxSystem->calculatePersonalTax($income);
        }

        // تطبيق القواعد التصاعدية
        $progressiveRule = $rules->where('calculation_method', 'PROGRESSIVE')->first();

        if ($progressiveRule) {
            return $progressiveRule->calculateTax($income, $context);
        }

        // تطبيق القواعد العادية
        $totalTax = 0;
        $breakdown = [];

        foreach ($rules as $rule) {
            $calculation = $rule->calculateTax($income, $context);
            if ($calculation['applicable']) {
                $totalTax += $calculation['tax_amount'];
                $breakdown[] = $calculation;
            }
        }

        return [
            'gross_income' => $income,
            'total_tax' => round($totalTax, 2),
            'net_income' => round($income - $totalTax, 2),
            'effective_rate' => $income > 0 ? round(($totalTax / $income) * 100, 2) : 0,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * حساب ضريبة الاستقطاع
     */
    public function calculateWithholdingTax(float $amount, string $category, array $context = []): array
    {
        $rules = $this->getTaxRules('WITHHOLDING_TAX', $category);

        if (empty($rules)) {
            return [
                'base_amount' => $amount,
                'tax_amount' => 0,
                'net_amount' => $amount,
                'applicable' => false,
            ];
        }

        $bestRule = $this->selectBestRule($rules, $amount, $context);
        $calculation = $bestRule->calculateTax($amount, $context);

        return [
            'base_amount' => $amount,
            'tax_rate' => $calculation['tax_rate'] ?? 0,
            'tax_amount' => $calculation['tax_amount'],
            'net_amount' => $amount - $calculation['tax_amount'],
            'applicable' => $calculation['applicable'],
            'rule_name' => $calculation['rule_name'] ?? '',
        ];
    }

    /**
     * حساب الضمان الاجتماعي
     */
    public function calculateSocialSecurity(float $salary, array $context = []): array
    {
        $rules = $this->getTaxRules('SOCIAL_SECURITY');

        if (empty($rules)) {
            return $this->taxSystem->calculateSocialSecurity($salary);
        }

        $employeeContribution = 0;
        $employerContribution = 0;
        $breakdown = [];

        foreach ($rules as $rule) {
            $calculation = $rule->calculateTax($salary, $context);
            if ($calculation['applicable']) {
                $contributionType = $rule->metadata['contribution_type'] ?? 'employee';

                if ($contributionType === 'employee') {
                    $employeeContribution += $calculation['tax_amount'];
                } elseif ($contributionType === 'employer') {
                    $employerContribution += $calculation['tax_amount'];
                } else {
                    // مشترك
                    $employeeContribution += $calculation['tax_amount'] / 2;
                    $employerContribution += $calculation['tax_amount'] / 2;
                }

                $breakdown[] = $calculation;
            }
        }

        return [
            'gross_salary' => $salary,
            'employee_contribution' => round($employeeContribution, 2),
            'employer_contribution' => round($employerContribution, 2),
            'total_contribution' => round($employeeContribution + $employerContribution, 2),
            'net_salary' => round($salary - $employeeContribution, 2),
            'breakdown' => $breakdown,
        ];
    }

    /**
     * حساب ضرائب الفاتورة
     */
    public function calculateInvoiceTaxes(Invoice $invoice): array
    {
        $totalVAT = 0;
        $totalWithholding = 0;
        $itemBreakdown = [];

        foreach ($invoice->items as $item) {
            $lineTotal = $item->quantity * $item->unit_price - $item->discount_amount;

            // حساب ضريبة القيمة المضافة
            $vatCalculation = $this->calculateVAT($lineTotal, $item->product->category ?? 'GENERAL');

            // حساب ضريبة الاستقطاع (إن وجدت)
            $withholdingCalculation = $this->calculateWithholdingTax(
                $lineTotal,
                $item->product->category ?? 'GENERAL',
                ['customer_type' => $invoice->customer->type ?? 'individual']
            );

            $totalVAT += $vatCalculation['vat_amount'] ?? $vatCalculation['tax_amount'] ?? 0;
            $totalWithholding += $withholdingCalculation['tax_amount'] ?? 0;

            $itemBreakdown[] = [
                'item_id' => $item->id,
                'description' => $item->description,
                'line_total' => $lineTotal,
                'vat' => $vatCalculation,
                'withholding' => $withholdingCalculation,
            ];
        }

        return [
            'subtotal' => $invoice->subtotal,
            'total_vat' => round($totalVAT, 2),
            'total_withholding' => round($totalWithholding, 2),
            'total_amount' => round($invoice->subtotal + $totalVAT - $totalWithholding, 2),
            'item_breakdown' => $itemBreakdown,
        ];
    }

    /**
     * إنشاء إقرار ضريبي تلقائي
     */
    public function generateTaxReturn(string $returnType, int $taxYear, array $parameters = []): TaxReturn
    {
        $periodDates = $this->calculatePeriodDates($returnType, $taxYear, $parameters);

        $taxReturn = TaxReturn::create([
            'tax_system_id' => $this->taxSystem->id,
            'company_id' => $parameters['company_id'] ?? null,
            'return_type' => $returnType,
            'tax_period_type' => $this->getPeriodType($returnType),
            'tax_year' => $taxYear,
            'period_start_date' => $periodDates['start'],
            'period_end_date' => $periodDates['end'],
            'due_date' => $periodDates['due'],
            'status' => 'DRAFT',
            'submission_method' => 'ELECTRONIC',
        ]);

        // جمع البيانات المالية
        $this->populateFinancialData($taxReturn, $parameters);

        // حساب الضرائب
        $taxReturn->calculateTaxes();

        return $taxReturn;
    }

    /**
     * الحصول على قواعد الضرائب
     */
    protected function getTaxRules(string $taxType, ?string $category = null): \Illuminate\Database\Eloquent\Collection
    {
        $cacheKey = "tax_rules_{$this->taxSystem->id}_{$taxType}" . ($category ? "_{$category}" : '');

        return Cache::remember($cacheKey, 3600, function () use ($taxType, $category) {
            $query = $this->taxSystem->taxRules()
                ->active()
                ->byTaxType($taxType)
                ->applicableOn()
                ->orderedByPriority();

            if ($category) {
                $query->byCategory($category);
            }

            return $query->get();
        });
    }

    /**
     * اختيار أفضل قاعدة
     */
    protected function selectBestRule(\Illuminate\Database\Eloquent\Collection $rules, float $amount, array $context): TaxRule
    {
        // ترتيب القواعد حسب الأولوية والدقة
        foreach ($rules as $rule) {
            if ($rule->isApplicable($amount, $context)) {
                return $rule;
            }
        }

        // إذا لم توجد قاعدة مطابقة، استخدم الأولى
        return $rules->first() ?? throw new \Exception('لا توجد قواعد ضريبية مطبقة');
    }

    /**
     * حساب تواريخ الفترة
     */
    protected function calculatePeriodDates(string $returnType, int $taxYear, array $parameters): array
    {
        $month = $parameters['month'] ?? null;
        $quarter = $parameters['quarter'] ?? null;

        return match ($returnType) {
            'VAT_MONTHLY' => $this->getMonthlyPeriod($taxYear, $month),
            'VAT_QUARTERLY' => $this->getQuarterlyPeriod($taxYear, $quarter),
            'CORPORATE_ANNUAL', 'INCOME_ANNUAL' => $this->getAnnualPeriod($taxYear),
            default => $this->getAnnualPeriod($taxYear),
        };
    }

    /**
     * الحصول على فترة شهرية
     */
    protected function getMonthlyPeriod(int $year, ?int $month): array
    {
        $month = $month ?? now()->month;
        $start = \Carbon\Carbon::create($year, $month, 1);
        $end = $start->copy()->endOfMonth();
        $due = $end->copy()->addDays(20); // 20 يوم بعد نهاية الشهر

        return [
            'start' => $start,
            'end' => $end,
            'due' => $due,
        ];
    }

    /**
     * الحصول على فترة ربع سنوية
     */
    protected function getQuarterlyPeriod(int $year, ?int $quarter): array
    {
        $quarter = $quarter ?? ceil(now()->month / 3);
        $startMonth = ($quarter - 1) * 3 + 1;

        $start = \Carbon\Carbon::create($year, $startMonth, 1);
        $end = $start->copy()->addMonths(2)->endOfMonth();
        $due = $end->copy()->addDays(30); // 30 يوم بعد نهاية الربع

        return [
            'start' => $start,
            'end' => $end,
            'due' => $due,
        ];
    }

    /**
     * الحصول على فترة سنوية
     */
    protected function getAnnualPeriod(int $year): array
    {
        $start = \Carbon\Carbon::create($year, 1, 1);
        $end = $start->copy()->endOfYear();
        $due = \Carbon\Carbon::create($year + 1, 3, 31); // 31 مارس من السنة التالية

        return [
            'start' => $start,
            'end' => $end,
            'due' => $due,
        ];
    }

    /**
     * الحصول على نوع الفترة
     */
    protected function getPeriodType(string $returnType): string
    {
        return match ($returnType) {
            'VAT_MONTHLY', 'WITHHOLDING_MONTHLY', 'SOCIAL_SECURITY_MONTHLY', 'PAYROLL_MONTHLY' => 'MONTHLY',
            'VAT_QUARTERLY' => 'QUARTERLY',
            'CORPORATE_ANNUAL', 'INCOME_ANNUAL', 'ANNUAL_DECLARATION' => 'ANNUAL',
            default => 'ANNUAL',
        };
    }

    /**
     * ملء البيانات المالية
     */
    protected function populateFinancialData(TaxReturn $taxReturn, array $parameters): void
    {
        // جمع البيانات من القيود المحاسبية
        $journalEntries = JournalEntry::whereBetween('entry_date', [
            $taxReturn->period_start_date,
            $taxReturn->period_end_date
        ])->where('status', 'POSTED')->get();

        $totalRevenue = $journalEntries->where('account.account_type', 'REVENUE')->sum('credit_amount');
        $totalExpenses = $journalEntries->where('account.account_type', 'EXPENSE')->sum('debit_amount');

        $taxReturn->update([
            'total_revenue' => $totalRevenue,
            'total_expenses' => $totalExpenses,
            'taxable_income' => $totalRevenue - $totalExpenses,
        ]);
    }

    /**
     * تطبيق قواعد ضريبية على مبلغ
     */
    public function applyTaxRules(float $amount, string $taxType, array $context = []): array
    {
        $rules = $this->getTaxRules($taxType, $context['category'] ?? null);
        $results = [];
        $totalTax = 0;

        foreach ($rules as $rule) {
            $calculation = $rule->calculateTax($amount, $context);
            if ($calculation['applicable']) {
                $results[] = $calculation;
                $totalTax += $calculation['tax_amount'];
            }
        }

        return [
            'base_amount' => $amount,
            'total_tax' => round($totalTax, 2),
            'net_amount' => round($amount - $totalTax, 2),
            'rules_applied' => $results,
        ];
    }

    /**
     * مسح الكاش
     */
    public function clearCache(): void
    {
        Cache::tags(['tax_rules', "tax_system_{$this->taxSystem->id}"])->flush();
    }
}
