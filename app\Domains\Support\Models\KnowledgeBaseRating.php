<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تقييم مقال قاعدة المعرفة - Knowledge Base Rating
 */
class KnowledgeBaseRating extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'article_id',
        'user_id',
        'rating',
        'is_helpful',
        'feedback',
        'rated_at',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_helpful' => 'boolean',
        'rated_at' => 'datetime',
    ];

    public function article(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBaseArticle::class, 'article_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    public function getHelpfulnessLabelAttribute(): string
    {
        if (is_null($this->is_helpful)) {
            return 'غير محدد';
        }

        return $this->is_helpful ? 'مفيد' : 'غير مفيد';
    }
}
