import { Config } from 'ziggy-js';

declare global {
  interface Window {
    Ziggy: Config;
  }
}

export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string;
  created_at: string;
  updated_at: string;
}

export interface PageProps {
  auth: {
    user: User;
  };
  ziggy: Config & { location: string };
  flash: {
    message?: string;
    error?: string;
    success?: string;
  };
}

export interface PaginatedData<T> {
  data: T[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
  meta?: any;
}
