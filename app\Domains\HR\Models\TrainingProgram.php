<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج برنامج التدريب - منصة التدريب والتطوير المتقدمة
 */
class TrainingProgram extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'title',
        'title_ar',
        'title_en',
        'title_fr',
        'description',
        'description_ar',
        'description_en',
        'description_fr',
        'category_id',
        'instructor_id',
        'provider_type',
        'provider_name',
        'delivery_method',
        'format',
        'level',
        'duration_hours',
        'duration_days',
        'max_participants',
        'min_participants',
        'cost_per_participant',
        'currency',
        'location',
        'venue',
        'online_platform',
        'meeting_link',
        'materials_included',
        'prerequisites',
        'learning_objectives',
        'target_audience',
        'skills_covered',
        'competencies_addressed',
        'certification_provided',
        'certification_body',
        'validity_period_months',
        'status',
        'is_mandatory',
        'is_recurring',
        'recurrence_pattern',
        'tags',
        'rating',
        'feedback_summary',
        'completion_rate',
        'effectiveness_score',
        'metadata',
    ];

    protected $casts = [
        'duration_hours' => 'decimal:1',
        'cost_per_participant' => 'decimal:2',
        'materials_included' => 'array',
        'prerequisites' => 'array',
        'learning_objectives' => 'array',
        'target_audience' => 'array',
        'skills_covered' => 'array',
        'competencies_addressed' => 'array',
        'is_mandatory' => 'boolean',
        'is_recurring' => 'boolean',
        'certification_provided' => 'boolean',
        'recurrence_pattern' => 'array',
        'tags' => 'array',
        'rating' => 'decimal:2',
        'completion_rate' => 'decimal:2',
        'effectiveness_score' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * أنواع مقدمي التدريب
     */
    public const PROVIDER_TYPES = [
        'INTERNAL' => 'داخلي',
        'EXTERNAL' => 'خارجي',
        'ONLINE_PLATFORM' => 'منصة إلكترونية',
        'UNIVERSITY' => 'جامعة',
        'CERTIFICATION_BODY' => 'جهة اعتماد',
        'CONSULTANT' => 'استشاري',
    ];

    /**
     * طرق التقديم
     */
    public const DELIVERY_METHODS = [
        'CLASSROOM' => 'قاعة دراسية',
        'ONLINE' => 'عبر الإنترنت',
        'HYBRID' => 'مختلط',
        'WORKSHOP' => 'ورشة عمل',
        'SEMINAR' => 'ندوة',
        'WEBINAR' => 'ندوة إلكترونية',
        'SELF_PACED' => 'ذاتي السرعة',
        'MENTORING' => 'توجيه',
        'ON_THE_JOB' => 'أثناء العمل',
    ];

    /**
     * تنسيقات التدريب
     */
    public const FORMATS = [
        'VIDEO' => 'فيديو',
        'PRESENTATION' => 'عرض تقديمي',
        'DOCUMENT' => 'مستند',
        'INTERACTIVE' => 'تفاعلي',
        'SIMULATION' => 'محاكاة',
        'CASE_STUDY' => 'دراسة حالة',
        'QUIZ' => 'اختبار',
        'ASSIGNMENT' => 'مهمة',
        'PROJECT' => 'مشروع',
    ];

    /**
     * مستويات التدريب
     */
    public const LEVELS = [
        'BEGINNER' => 'مبتدئ',
        'INTERMEDIATE' => 'متوسط',
        'ADVANCED' => 'متقدم',
        'EXPERT' => 'خبير',
        'ALL_LEVELS' => 'جميع المستويات',
    ];

    /**
     * حالات البرنامج
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'ACTIVE' => 'نشط',
        'SCHEDULED' => 'مجدول',
        'IN_PROGRESS' => 'جاري',
        'COMPLETED' => 'مكتمل',
        'CANCELLED' => 'ملغي',
        'SUSPENDED' => 'معلق',
        'ARCHIVED' => 'مؤرشف',
    ];

    /**
     * فئة التدريب
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(TrainingCategory::class);
    }

    /**
     * المدرب
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'instructor_id');
    }

    /**
     * جلسات التدريب
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(TrainingSession::class);
    }

    /**
     * تسجيلات الموظفين
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(TrainingEnrollment::class);
    }

    /**
     * المواد التدريبية
     */
    public function materials(): HasMany
    {
        return $this->hasMany(TrainingMaterial::class);
    }

    /**
     * التقييمات
     */
    public function assessments(): HasMany
    {
        return $this->hasMany(TrainingAssessment::class);
    }

    /**
     * المستندات
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(\App\Domains\Shared\Models\Document::class, 'documentable');
    }

    /**
     * الحصول على العنوان المترجم
     */
    public function getLocalizedTitle(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->title_ar ?? $this->title,
            'en' => $this->title_en ?? $this->title,
            'fr' => $this->title_fr ?? $this->title,
            default => $this->title,
        };
    }

    /**
     * الحصول على الوصف المترجم
     */
    public function getLocalizedDescription(string $locale = 'ar'): string
    {
        switch($locale) {
            case 'ar':
                return $this->description_ar ?? $this->description;
            case 'en':
                return $this->description_en ?? $this->description;
            case 'fr':
                return $this->description_fr ?? $this->description;
            default:
                return $this->description;
        }
    }

    /**
     * الحصول على عدد المشاركين المسجلين
     */
    public function getEnrolledCountAttribute(): int
    {
        return $this->enrollments()->where('status', 'ENROLLED')->count();
    }

    /**
     * الحصول على عدد المشاركين المكملين
     */
    public function getCompletedCountAttribute(): int
    {
        return $this->enrollments()->where('status', 'COMPLETED')->count();
    }

    /**
     * التحقق من توفر مقاعد
     */
    public function hasAvailableSeats(): bool
    {
        if (!$this->max_participants) {
            return true;
        }

        return $this->enrolled_count < $this->max_participants;
    }

    /**
     * التحقق من الحد الأدنى للمشاركين
     */
    public function hasMinimumParticipants(): bool
    {
        if (!$this->min_participants) {
            return true;
        }

        return $this->enrolled_count >= $this->min_participants;
    }

    /**
     * تسجيل موظف في البرنامج
     */
    public function enrollEmployee(Employee $employee, array $data = []): TrainingEnrollment
    {
        return $this->enrollments()->create(array_merge([
            'employee_id' => $employee->id,
            'training_program_id' => $this->id,
            'enrolled_date' => now(),
            'status' => 'ENROLLED',
            'enrollment_type' => $data['enrollment_type'] ?? 'VOLUNTARY',
            'priority' => $data['priority'] ?? 'NORMAL',
        ], $data));
    }

    /**
     * إلغاء تسجيل موظف
     */
    public function unenrollEmployee(Employee $employee, string $reason = null): bool
    {
        $enrollment = $this->enrollments()
            ->where('employee_id', $employee->id)
            ->where('status', 'ENROLLED')
            ->first();

        if (!$enrollment) {
            return false;
        }

        return $enrollment->cancel($reason);
    }

    /**
     * إنشاء جلسة تدريبية
     */
    public function createSession(array $sessionData): TrainingSession
    {
        return $this->sessions()->create(array_merge($sessionData, [
            'training_program_id' => $this->id,
            'instructor_id' => $sessionData['instructor_id'] ?? $this->instructor_id,
        ]));
    }

    /**
     * إضافة مادة تدريبية
     */
    public function addMaterial(array $materialData): TrainingMaterial
    {
        return $this->materials()->create(array_merge($materialData, [
            'training_program_id' => $this->id,
        ]));
    }

    /**
     * حساب معدل الإكمال
     */
    public function calculateCompletionRate(): void
    {
        $totalEnrollments = $this->enrollments()->count();
        $completedEnrollments = $this->completed_count;

        $this->completion_rate = $totalEnrollments > 0 ?
            ($completedEnrollments / $totalEnrollments) * 100 : 0;

        $this->save();
    }

    /**
     * حساب نقاط الفعالية
     */
    public function calculateEffectivenessScore(): void
    {
        $feedbacks = $this->enrollments()
            ->whereNotNull('feedback_rating')
            ->pluck('feedback_rating');

        if ($feedbacks->count() > 0) {
            $this->effectiveness_score = $feedbacks->avg();
            $this->rating = $this->effectiveness_score;
        }

        $this->save();
    }

    /**
     * الحصول على الموظفين المؤهلين
     */
    public function getEligibleEmployees(): \Illuminate\Database\Eloquent\Collection
    {
        $query = Employee::active();

        // تطبيق معايير الجمهور المستهدف
        if ($this->target_audience) {
            foreach ($this->target_audience as $criteria) {
                switch ($criteria['type']) {
                    case 'department':
                        $query->whereIn('department_id', $criteria['values']);
                        break;
                    case 'position':
                        $query->whereIn('position_id', $criteria['values']);
                        break;
                    case 'level':
                        $query->whereHas('position', function ($q) use ($criteria) {
                            $q->whereIn('level', $criteria['values']);
                        });
                        break;
                    case 'experience':
                        $query->where('total_experience_years', '>=', $criteria['min_years']);
                        break;
                }
            }
        }

        return $query->get();
    }

    /**
     * إرسال دعوات التدريب
     */
    public function sendInvitations(array $employeeIds = null): int
    {
        $employees = $employeeIds ?
            Employee::whereIn('id', $employeeIds)->get() :
            $this->getEligibleEmployees();

        $invitationsSent = 0;

        foreach ($employees as $employee) {
            // التحقق من عدم التسجيل المسبق
            if (!$this->enrollments()->where('employee_id', $employee->id)->exists()) {
                $employee->notify(new \App\Notifications\TrainingInvitation($this));
                $invitationsSent++;
            }
        }

        return $invitationsSent;
    }

    /**
     * إنشاء شهادة إكمال
     */
    public function generateCertificate(Employee $employee): ?array
    {
        if (!$this->certification_provided) {
            return null;
        }

        $enrollment = $this->enrollments()
            ->where('employee_id', $employee->id)
            ->where('status', 'COMPLETED')
            ->first();

        if (!$enrollment) {
            return null;
        }

        return [
            'certificate_id' => 'CERT-' . $this->id . '-' . $employee->id . '-' . now()->format('Ymd'),
            'program_title' => $this->title,
            'employee_name' => $employee->full_name,
            'completion_date' => $enrollment->completed_date,
            'duration_hours' => $this->duration_hours,
            'certification_body' => $this->certification_body,
            'validity_period' => $this->validity_period_months,
            'expiry_date' => $this->validity_period_months ?
                $enrollment->completed_date->addMonths($this->validity_period_months) : null,
            'skills_covered' => $this->skills_covered,
            'instructor' => $this->instructor?->full_name,
        ];
    }

    /**
     * الحصول على تقرير البرنامج
     */
    public function getReport(): array
    {
        return [
            'program_info' => [
                'title' => $this->title,
                'category' => $this->category?->name,
                'instructor' => $this->instructor?->full_name,
                'duration' => $this->duration_hours . ' ساعة',
                'cost_per_participant' => $this->cost_per_participant,
            ],
            'enrollment_stats' => [
                'total_enrolled' => $this->enrolled_count,
                'completed' => $this->completed_count,
                'in_progress' => $this->enrollments()->where('status', 'IN_PROGRESS')->count(),
                'dropped_out' => $this->enrollments()->where('status', 'DROPPED_OUT')->count(),
                'completion_rate' => $this->completion_rate,
            ],
            'feedback_stats' => [
                'average_rating' => $this->rating,
                'effectiveness_score' => $this->effectiveness_score,
                'total_feedback' => $this->enrollments()->whereNotNull('feedback_rating')->count(),
            ],
            'financial_summary' => [
                'total_cost' => $this->enrolled_count * $this->cost_per_participant,
                'cost_per_completion' => $this->completed_count > 0 ?
                    ($this->enrolled_count * $this->cost_per_participant) / $this->completed_count : 0,
            ],
        ];
    }

    /**
     * نطاق للبرامج النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'ACTIVE');
    }

    /**
     * نطاق حسب الفئة
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * نطاق حسب طريقة التقديم
     */
    public function scopeByDeliveryMethod($query, string $method)
    {
        return $query->where('delivery_method', $method);
    }

    /**
     * نطاق حسب المستوى
     */
    public function scopeByLevel($query, string $level)
    {
        return $query->where('level', $level);
    }

    /**
     * نطاق للبرامج الإلزامية
     */
    public function scopeMandatory($query)
    {
        return $query->where('is_mandatory', true);
    }

    /**
     * نطاق للبرامج المتاحة للتسجيل
     */
    public function scopeAvailableForEnrollment($query)
    {
        return $query->where('status', 'ACTIVE')
            ->where(function ($q) {
                $q->whereNull('max_participants')
                  ->orWhereRaw('(SELECT COUNT(*) FROM training_enrollments WHERE training_program_id = training_programs.id AND status = "ENROLLED") < max_participants');
            });
    }

    /**
     * نطاق للبحث النصي
     */
    public function scopeSearch($query, string $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('title', 'LIKE', "%{$term}%")
              ->orWhere('title_ar', 'LIKE', "%{$term}%")
              ->orWhere('title_en', 'LIKE', "%{$term}%")
              ->orWhere('description', 'LIKE', "%{$term}%")
              ->orWhereJsonContains('skills_covered', $term)
              ->orWhereJsonContains('tags', $term);
        });
    }

    /**
     * نطاق حسب المهارة
     */
    public function scopeBySkill($query, string $skill)
    {
        return $query->whereJsonContains('skills_covered', $skill);
    }

    /**
     * نطاق حسب نطاق التكلفة
     */
    public function scopeByCostRange($query, float $minCost, float $maxCost = null)
    {
        $query->where('cost_per_participant', '>=', $minCost);

        if ($maxCost) {
            $query->where('cost_per_participant', '<=', $maxCost);
        }

        return $query;
    }
}
