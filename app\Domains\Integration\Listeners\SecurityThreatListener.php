<?php

namespace App\Domains\Integration\Listeners;

use App\Domains\Integration\Events\SecurityThreatDetected;
use App\Domains\Integration\Models\SecurityIncident;
use App\Domains\Integration\Services\Security\AdvancedSecurityManager;
use App\Domains\Integration\Services\Monitoring\AlertManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

/**
 * Security Threat Listener
 * 
 * Handles security threat detection events and implements
 * automated response and mitigation strategies
 */
class SecurityThreatListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected AdvancedSecurityManager $securityManager;
    protected AlertManager $alertManager;

    /**
     * Create the event listener.
     */
    public function __construct(
        AdvancedSecurityManager $securityManager,
        AlertManager $alertManager
    ) {
        $this->securityManager = $securityManager;
        $this->alertManager = $alertManager;
    }

    /**
     * Handle the event.
     */
    public function handle(SecurityThreatDetected $event): void
    {
        try {
            Log::warning('Security threat detected', [
                'request_id' => $event->requestId,
                'threat_type' => $event->threatType,
                'severity' => $event->severity,
                'source_ip' => $event->sourceIp,
                'gateway_id' => $event->gateway?->gateway_id,
            ]);

            // Create security incident record
            $incident = $this->createSecurityIncident($event);

            // Apply immediate mitigation
            $this->applyImmediateMitigation($event, $incident);

            // Update threat intelligence
            $this->updateThreatIntelligence($event);

            // Send security alerts
            $this->sendSecurityAlerts($event, $incident);

            // Update security metrics
            $this->updateSecurityMetrics($event);

            // Trigger automated response
            $this->triggerAutomatedResponse($event, $incident);

            // Log security event
            $this->logSecurityEvent($event, $incident);

        } catch (\Exception $e) {
            Log::error('Failed to handle security threat event', [
                'request_id' => $event->requestId,
                'threat_type' => $event->threatType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Create security incident record
     */
    protected function createSecurityIncident(SecurityThreatDetected $event): SecurityIncident
    {
        return SecurityIncident::create([
            'incident_id' => uniqid('sec_'),
            'request_id' => $event->requestId,
            'gateway_id' => $event->gateway?->id,
            'threat_type' => $event->threatType,
            'severity' => $event->severity,
            'source_ip' => $event->sourceIp,
            'user_agent' => $event->userAgent,
            'threat_data' => $event->threatData,
            'mitigation_actions' => $event->mitigationActions,
            'status' => 'detected',
            'detected_at' => $event->detectedAt,
            'priority' => $event->getIncidentPriority(),
            'confidence_score' => $event->threatData['confidence_score'] ?? 0.8,
            'attack_vector' => $event->threatData['attack_vector'] ?? 'unknown',
            'geolocation' => $event->threatData['geolocation'] ?? [],
            'reputation_data' => $event->threatData['reputation_data'] ?? [],
        ]);
    }

    /**
     * Apply immediate mitigation measures
     */
    protected function applyImmediateMitigation(SecurityThreatDetected $event, SecurityIncident $incident): void
    {
        $mitigationActions = [];

        // Block IP if threat is severe
        if (in_array($event->severity, ['critical', 'high'])) {
            $this->blockSourceIp($event->sourceIp, $event->threatType);
            $mitigationActions[] = "Blocked IP: {$event->sourceIp}";
        }

        // Apply rate limiting
        if ($event->threatType === 'ddos' || $event->threatType === 'brute_force') {
            $this->applyStrictRateLimiting($event->sourceIp);
            $mitigationActions[] = "Applied strict rate limiting to IP: {$event->sourceIp}";
        }

        // Enable additional monitoring
        $this->enableEnhancedMonitoring($event->sourceIp, $event->threatType);
        $mitigationActions[] = "Enabled enhanced monitoring";

        // Update gateway security settings
        if ($event->gateway && $event->requiresImmediateResponse()) {
            $this->updateGatewaySecurity($event->gateway, $event->threatType);
            $mitigationActions[] = "Updated gateway security settings";
        }

        // Update incident with applied mitigations
        $incident->update([
            'mitigation_actions' => array_merge($incident->mitigation_actions, $mitigationActions),
            'status' => 'mitigated',
            'mitigated_at' => now(),
        ]);
    }

    /**
     * Update threat intelligence database
     */
    protected function updateThreatIntelligence(SecurityThreatDetected $event): void
    {
        $threatIntelligence = $event->getThreatIntelligence();
        
        // Store threat indicators
        $this->storeThreatIndicators($threatIntelligence);

        // Update IP reputation
        $this->updateIpReputation($event->sourceIp, $event->threatType, $event->severity);

        // Update attack patterns
        $this->updateAttackPatterns($event->threatType, $event->threatData);

        // Share threat intelligence (if configured)
        $this->shareThreatIntelligence($threatIntelligence);
    }

    /**
     * Send security alerts to administrators
     */
    protected function sendSecurityAlerts(SecurityThreatDetected $event, SecurityIncident $incident): void
    {
        $alertData = [
            'incident_id' => $incident->incident_id,
            'threat_type' => $event->threatType,
            'severity' => $event->severity,
            'source_ip' => $event->sourceIp,
            'gateway' => $event->gateway?->name,
            'description' => $event->getThreatDescription(),
            'recommendations' => $event->getRecommendedMitigations(),
            'detected_at' => $event->detectedAt->toISOString(),
        ];

        // Send immediate alert for critical threats
        if ($event->severity === 'critical') {
            $this->alertManager->sendImmediateAlert('security_threat_critical', $alertData);
        }

        // Send regular alert
        $this->alertManager->sendAlert('security_threat_detected', $alertData);

        // Send to security team
        $this->alertManager->sendSecurityTeamAlert($alertData);
    }

    /**
     * Update security metrics
     */
    protected function updateSecurityMetrics(SecurityThreatDetected $event): void
    {
        $metricsKey = "security_metrics:" . now()->format('Y-m-d-H');
        
        // Increment threat counters
        Cache::increment($metricsKey . ':total_threats');
        Cache::increment($metricsKey . ':' . $event->threatType);
        Cache::increment($metricsKey . ':severity_' . $event->severity);

        // Track source IPs
        $ipKey = $metricsKey . ':source_ips';
        $sourceIps = Cache::get($ipKey, []);
        $sourceIps[$event->sourceIp] = ($sourceIps[$event->sourceIp] ?? 0) + 1;
        Cache::put($ipKey, $sourceIps, 90000);

        // Track by gateway
        if ($event->gateway) {
            Cache::increment($metricsKey . ':gateway_' . $event->gateway->id);
        }

        // Set expiration
        Cache::expire($metricsKey . ':total_threats', 90000);
        Cache::expire($metricsKey . ':' . $event->threatType, 90000);
        Cache::expire($metricsKey . ':severity_' . $event->severity, 90000);
    }

    /**
     * Trigger automated response based on threat type
     */
    protected function triggerAutomatedResponse(SecurityThreatDetected $event, SecurityIncident $incident): void
    {
        $responseConfig = config('integration.security.automated_response', []);
        
        if (!($responseConfig['enabled'] ?? false)) {
            return;
        }

        $responses = $responseConfig['responses'][$event->threatType] ?? [];
        
        foreach ($responses as $response) {
            try {
                $this->executeAutomatedResponse($response, $event, $incident);
            } catch (\Exception $e) {
                Log::error('Failed to execute automated response', [
                    'response' => $response,
                    'incident_id' => $incident->incident_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Execute specific automated response
     */
    protected function executeAutomatedResponse(array $response, SecurityThreatDetected $event, SecurityIncident $incident): void
    {
        $action = $response['action'] ?? '';
        
        switch ($action) {
            case 'block_ip':
                $this->blockSourceIp($event->sourceIp, $event->threatType, $response['duration'] ?? 3600);
                break;
                
            case 'enable_captcha':
                $this->enableCaptchaForIp($event->sourceIp);
                break;
                
            case 'require_2fa':
                $this->requireTwoFactorAuth($event->sourceIp);
                break;
                
            case 'scale_up':
                $this->triggerAutoScaling($event->gateway);
                break;
                
            case 'enable_waf':
                $this->enableWebApplicationFirewall($event->gateway, $event->threatType);
                break;
                
            case 'quarantine_session':
                $this->quarantineUserSession($event->requestId);
                break;
        }
    }

    /**
     * Log security event for audit trail
     */
    protected function logSecurityEvent(SecurityThreatDetected $event, SecurityIncident $incident): void
    {
        DB::table('security_audit_log')->insert([
            'incident_id' => $incident->incident_id,
            'event_type' => 'threat_detected',
            'threat_type' => $event->threatType,
            'severity' => $event->severity,
            'source_ip' => $event->sourceIp,
            'user_agent' => $event->userAgent,
            'gateway_id' => $event->gateway?->id,
            'request_id' => $event->requestId,
            'threat_data' => json_encode($event->threatData),
            'mitigation_actions' => json_encode($event->mitigationActions),
            'detected_at' => $event->detectedAt,
            'created_at' => now(),
        ]);
    }

    // Placeholder methods for security actions
    protected function blockSourceIp(string $ip, string $reason, int $duration = 3600): void
    {
        Cache::put("blocked_ip:{$ip}", [
            'reason' => $reason,
            'blocked_at' => now(),
            'expires_at' => now()->addSeconds($duration),
        ], $duration);
    }

    protected function applyStrictRateLimiting(string $ip): void
    {
        Cache::put("strict_rate_limit:{$ip}", true, 3600);
    }

    protected function enableEnhancedMonitoring(string $ip, string $threatType): void
    {
        Cache::put("enhanced_monitoring:{$ip}", [
            'threat_type' => $threatType,
            'enabled_at' => now(),
        ], 7200);
    }

    protected function updateGatewaySecurity($gateway, string $threatType): void
    {
        // Implementation depends on gateway type
    }

    protected function storeThreatIndicators(array $threatIntelligence): void
    {
        // Store in threat intelligence database
    }

    protected function updateIpReputation(string $ip, string $threatType, string $severity): void
    {
        // Update IP reputation score
    }

    protected function updateAttackPatterns(string $threatType, array $threatData): void
    {
        // Update attack pattern database
    }

    protected function shareThreatIntelligence(array $threatIntelligence): void
    {
        // Share with external threat intelligence platforms
    }

    protected function enableCaptchaForIp(string $ip): void
    {
        Cache::put("require_captcha:{$ip}", true, 3600);
    }

    protected function requireTwoFactorAuth(string $ip): void
    {
        Cache::put("require_2fa:{$ip}", true, 3600);
    }

    protected function triggerAutoScaling($gateway): void
    {
        // Trigger auto-scaling if available
    }

    protected function enableWebApplicationFirewall($gateway, string $threatType): void
    {
        // Enable WAF rules specific to threat type
    }

    protected function quarantineUserSession(string $requestId): void
    {
        // Quarantine user session for investigation
    }
}
