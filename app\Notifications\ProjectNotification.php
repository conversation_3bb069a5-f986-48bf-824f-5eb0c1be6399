<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * إشعار المشروع العام - Project Notification
 */
class ProjectNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected array $content;
    protected array $channels;

    public function __construct(array $content, array $channels = ['database', 'mail'])
    {
        $this->content = $content;
        $this->channels = $channels;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return $this->channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject($this->content['subject'] ?? 'إشعار من نظام إدارة المشاريع')
                    ->line($this->content['body'] ?? 'لديك إشعار جديد')
                    ->action('عرض التفاصيل', $this->content['action_url'] ?? url('/'))
                    ->line('شكراً لاستخدام نظام إدارة المشاريع!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->content;
    }
}
