<?php

namespace App\Domains\Projects\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\Projects\Models\Task;

/**
 * طلب إنشاء مهمة جديدة
 * تحقق شامل من البيانات مع دعم جميع الميزات المتقدمة
 */
class StoreTaskRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create-tasks');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:5000',
            'task_number' => [
                'nullable',
                'string',
                'max:50',
                'unique:tasks,task_number',
                'regex:/^[A-Z0-9_-]+$/'
            ],
            'project_id' => 'required|exists:projects,id',
            'parent_id' => 'nullable|exists:tasks,id',
            'epic_id' => 'nullable|exists:epics,id',

            // Assignment
            'assignee_id' => 'nullable|exists:users,id',
            'reporter_id' => 'nullable|exists:users,id',
            'reviewer_id' => 'nullable|exists:users,id',

            // Status and Priority
            'status' => [
                'nullable',
                Rule::in(array_keys(Task::STATUSES))
            ],
            'priority' => [
                'nullable',
                Rule::in(array_keys(Task::PRIORITIES))
            ],
            'type' => [
                'nullable',
                Rule::in(array_keys(Task::TYPES))
            ],
            'category' => [
                'nullable',
                Rule::in(array_keys(Task::CATEGORIES))
            ],

            // Dates and Time
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:start_date',
            'estimated_hours' => 'nullable|numeric|min:0|max:1000',
            'remaining_hours' => 'nullable|numeric|min:0',

            // Progress and Completion
            'progress_percentage' => 'nullable|integer|between:0,100',
            'story_points' => 'nullable|integer|min:1|max:100',
            'complexity_score' => 'nullable|integer|between:1,10',

            // Sprint and Milestone
            'sprint_id' => 'nullable|exists:sprints,id',
            'milestone_id' => 'nullable|exists:milestones,id',

            // Risk and Quality
            'risk_level' => 'nullable|string|in:LOW,MEDIUM,HIGH,CRITICAL',
            'quality_gate' => 'nullable|string|in:NONE,BASIC,STANDARD,STRICT',

            // Labels and Tags
            'labels' => 'nullable|array',
            'labels.*' => 'string|max:50',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',

            // Dependencies
            'dependencies' => 'nullable|array',
            'dependencies.*.task_id' => 'required|exists:tasks,id',
            'dependencies.*.type' => 'required|string|in:BLOCKS,DEPENDS_ON,RELATES_TO',

            // Acceptance Criteria
            'acceptance_criteria' => 'nullable|array',
            'acceptance_criteria.*.description' => 'required|string|max:500',
            'acceptance_criteria.*.is_completed' => 'boolean',

            // Subtasks
            'subtasks' => 'nullable|array',
            'subtasks.*.title' => 'required|string|max:255',
            'subtasks.*.description' => 'nullable|string|max:1000',
            'subtasks.*.assignee_id' => 'nullable|exists:users,id',
            'subtasks.*.estimated_hours' => 'nullable|numeric|min:0',
            'subtasks.*.due_date' => 'nullable|date',

            // Watchers
            'watchers' => 'nullable|array',
            'watchers.*' => 'exists:users,id',

            // Custom Fields
            'custom_fields' => 'nullable|array',

            // Attachments
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max

            // Notifications
            'notify_assignee' => 'boolean',
            'notify_watchers' => 'boolean',
            'notify_project_manager' => 'boolean',

            // Template
            'template_id' => 'nullable|exists:task_templates,id',

            // Additional Settings
            'is_billable' => 'boolean',
            'hourly_rate' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:2000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'title.required' => 'عنوان المهمة مطلوب',
            'title.max' => 'عنوان المهمة لا يجب أن يتجاوز 255 حرف',
            'description.max' => 'وصف المهمة لا يجب أن يتجاوز 5000 حرف',
            'task_number.unique' => 'رقم المهمة مستخدم مسبقاً',
            'task_number.regex' => 'رقم المهمة يجب أن يحتوي على أحرف كبيرة وأرقام وشرطات فقط',
            'project_id.required' => 'المشروع مطلوب',
            'project_id.exists' => 'المشروع المحدد غير موجود',
            'parent_id.exists' => 'المهمة الأب المحددة غير موجودة',
            'epic_id.exists' => 'الملحمة المحددة غير موجودة',

            // Assignment
            'assignee_id.exists' => 'المكلف المحدد غير موجود',
            'reporter_id.exists' => 'المبلغ المحدد غير موجود',
            'reviewer_id.exists' => 'المراجع المحدد غير موجود',

            // Status and Priority
            'status.in' => 'حالة المهمة المحددة غير صحيحة',
            'priority.in' => 'أولوية المهمة المحددة غير صحيحة',
            'type.in' => 'نوع المهمة المحدد غير صحيح',
            'category.in' => 'فئة المهمة المحددة غير صحيحة',

            // Dates and Time
            'due_date.after_or_equal' => 'تاريخ الاستحقاق يجب أن يكون بعد أو يساوي تاريخ البداية',
            'estimated_hours.numeric' => 'الساعات المقدرة يجب أن تكون رقماً',
            'estimated_hours.min' => 'الساعات المقدرة لا يمكن أن تكون سالبة',
            'estimated_hours.max' => 'الساعات المقدرة لا يمكن أن تتجاوز 1000 ساعة',
            'remaining_hours.numeric' => 'الساعات المتبقية يجب أن تكون رقماً',
            'remaining_hours.min' => 'الساعات المتبقية لا يمكن أن تكون سالبة',

            // Progress and Completion
            'progress_percentage.between' => 'نسبة التقدم يجب أن تكون بين 0 و 100',
            'story_points.min' => 'نقاط القصة يجب أن تكون على الأقل 1',
            'story_points.max' => 'نقاط القصة لا يمكن أن تتجاوز 100',
            'complexity_score.between' => 'درجة التعقيد يجب أن تكون بين 1 و 10',

            // Sprint and Milestone
            'sprint_id.exists' => 'السبرنت المحدد غير موجود',
            'milestone_id.exists' => 'المعلم المحدد غير موجود',

            // Risk and Quality
            'risk_level.in' => 'مستوى المخاطر المحدد غير صحيح',
            'quality_gate.in' => 'بوابة الجودة المحددة غير صحيحة',

            // Dependencies
            'dependencies.*.task_id.required' => 'معرف المهمة مطلوب للتبعية',
            'dependencies.*.task_id.exists' => 'المهمة المحددة في التبعية غير موجودة',
            'dependencies.*.type.required' => 'نوع التبعية مطلوب',
            'dependencies.*.type.in' => 'نوع التبعية المحدد غير صحيح',

            // Acceptance Criteria
            'acceptance_criteria.*.description.required' => 'وصف معيار القبول مطلوب',
            'acceptance_criteria.*.description.max' => 'وصف معيار القبول لا يجب أن يتجاوز 500 حرف',

            // Subtasks
            'subtasks.*.title.required' => 'عنوان المهمة الفرعية مطلوب',
            'subtasks.*.title.max' => 'عنوان المهمة الفرعية لا يجب أن يتجاوز 255 حرف',
            'subtasks.*.description.max' => 'وصف المهمة الفرعية لا يجب أن يتجاوز 1000 حرف',
            'subtasks.*.assignee_id.exists' => 'مكلف المهمة الفرعية غير موجود',
            'subtasks.*.estimated_hours.numeric' => 'ساعات المهمة الفرعية يجب أن تكون رقماً',
            'subtasks.*.estimated_hours.min' => 'ساعات المهمة الفرعية لا يمكن أن تكون سالبة',

            // Watchers
            'watchers.*.exists' => 'المتابع المحدد غير موجود',

            // Attachments
            'attachments.*.file' => 'المرفق يجب أن يكون ملفاً',
            'attachments.*.max' => 'حجم المرفق لا يجب أن يتجاوز 10 ميجابايت',

            // Template
            'template_id.exists' => 'قالب المهمة المحدد غير موجود',

            // Additional Settings
            'hourly_rate.numeric' => 'السعر بالساعة يجب أن يكون رقماً',
            'hourly_rate.min' => 'السعر بالساعة لا يمكن أن يكون سالباً',
            'notes.max' => 'الملاحظات لا يجب أن تتجاوز 2000 حرف',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('title')) {
            $this->merge([
                'title' => trim($this->title)
            ]);
        }

        // إنشاء رقم المهمة تلقائياً إذا لم يتم تقديمه
        if (!$this->has('task_number') && $this->has('project_id')) {
            $this->merge([
                'task_number' => $this->generateTaskNumber()
            ]);
        }

        // تعيين القيم الافتراضية
        $this->merge([
            'status' => $this->status ?? 'TODO',
            'priority' => $this->priority ?? 'MEDIUM',
            'type' => $this->type ?? 'TASK',
            'category' => $this->category ?? 'DEVELOPMENT',
            'risk_level' => $this->risk_level ?? 'LOW',
            'quality_gate' => $this->quality_gate ?? 'BASIC',
            'progress_percentage' => $this->progress_percentage ?? 0,
            'is_billable' => $this->is_billable ?? true,
            'notify_assignee' => $this->notify_assignee ?? true,
            'notify_watchers' => $this->notify_watchers ?? false,
            'notify_project_manager' => $this->notify_project_manager ?? true,
            'reporter_id' => $this->reporter_id ?? auth()->id(),
        ]);
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من عدم تعيين المهمة كأب لنفسها
            if ($this->parent_id && $this->parent_id == $this->id) {
                $validator->errors()->add('parent_id', 'لا يمكن للمهمة أن تكون أباً لنفسها');
            }

            // التحقق من أن المهمة الأب في نفس المشروع
            if ($this->parent_id && $this->project_id) {
                $parentTask = \App\Domains\Projects\Models\Task::find($this->parent_id);
                if ($parentTask && $parentTask->project_id != $this->project_id) {
                    $validator->errors()->add('parent_id', 'المهمة الأب يجب أن تكون في نفس المشروع');
                }
            }

            // التحقق من أن المكلف عضو في فريق المشروع
            if ($this->assignee_id && $this->project_id) {
                $project = \App\Domains\Projects\Models\Project::find($this->project_id);
                if ($project) {
                    $teamMemberIds = $project->team()->pluck('user_id')->toArray();
                    if (!in_array($this->assignee_id, $teamMemberIds)) {
                        $validator->errors()->add('assignee_id', 'المكلف يجب أن يكون عضواً في فريق المشروع');
                    }
                }
            }

            // التحقق من صحة التبعيات
            if ($this->dependencies) {
                foreach ($this->dependencies as $index => $dependency) {
                    if (isset($dependency['task_id']) && $dependency['task_id'] == $this->id) {
                        $validator->errors()->add("dependencies.{$index}.task_id", 'لا يمكن للمهمة أن تعتمد على نفسها');
                    }
                }
            }

            // التحقق من صحة الساعات المتبقية
            if ($this->estimated_hours && $this->remaining_hours) {
                if ($this->remaining_hours > $this->estimated_hours) {
                    $validator->errors()->add('remaining_hours', 'الساعات المتبقية لا يمكن أن تكون أكثر من الساعات المقدرة');
                }
            }

            // التحقق من صحة نقاط القصة للمهام من نوع Story
            if ($this->type === 'STORY' && !$this->story_points) {
                $validator->errors()->add('story_points', 'نقاط القصة مطلوبة للمهام من نوع Story');
            }

            // التحقق من تاريخ الاستحقاق مع تاريخ نهاية المشروع
            if ($this->due_date && $this->project_id) {
                $project = \App\Domains\Projects\Models\Project::find($this->project_id);
                if ($project && $project->end_date) {
                    $dueDate = \Carbon\Carbon::parse($this->due_date);
                    $projectEndDate = \Carbon\Carbon::parse($project->end_date);
                    
                    if ($dueDate > $projectEndDate) {
                        $validator->errors()->add('due_date', 'تاريخ استحقاق المهمة لا يمكن أن يكون بعد تاريخ نهاية المشروع');
                    }
                }
            }

            // التحقق من السبرنت
            if ($this->sprint_id && $this->project_id) {
                $sprint = \App\Domains\Projects\Models\Sprint::find($this->sprint_id);
                if ($sprint && $sprint->project_id != $this->project_id) {
                    $validator->errors()->add('sprint_id', 'السبرنت يجب أن يكون في نفس المشروع');
                }
            }

            // التحقق من المعلم
            if ($this->milestone_id && $this->project_id) {
                $milestone = \App\Domains\Projects\Models\Milestone::find($this->milestone_id);
                if ($milestone && $milestone->project_id != $this->project_id) {
                    $validator->errors()->add('milestone_id', 'المعلم يجب أن يكون في نفس المشروع');
                }
            }
        });
    }

    /**
     * إنشاء رقم المهمة
     */
    protected function generateTaskNumber(): string
    {
        if (!$this->project_id) {
            return 'TASK-' . time();
        }

        $project = \App\Domains\Projects\Models\Project::find($this->project_id);
        $projectCode = $project ? $project->code : 'PROJ';
        
        $lastTask = \App\Domains\Projects\Models\Task::where('project_id', $this->project_id)
                                                   ->orderBy('id', 'desc')
                                                   ->first();

        $sequence = $lastTask ? (int) substr($lastTask->task_number, -4) + 1 : 1;

        return "{$projectCode}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }
}
