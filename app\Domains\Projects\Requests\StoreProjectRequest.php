<?php

namespace App\Domains\Projects\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\Projects\Models\Project;

/**
 * طلب إنشاء مشروع جديد
 * تحقق شامل من البيانات مع دعم جميع الميزات المتقدمة
 */
class StoreProjectRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create-projects');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'code' => [
                'nullable',
                'string',
                'max:50',
                'unique:projects,code',
                'regex:/^[A-Z0-9_-]+$/'
            ],
            'client_id' => 'nullable|exists:clients,id',
            'project_manager_id' => 'required|exists:users,id',
            'parent_id' => 'nullable|exists:projects,id',

            // Status and Priority
            'status' => [
                'nullable',
                Rule::in(array_keys(Project::STATUSES))
            ],
            'priority' => [
                'nullable',
                Rule::in(array_keys(Project::PRIORITIES))
            ],
            'category' => [
                'nullable',
                Rule::in(array_keys(Project::CATEGORIES))
            ],
            'methodology' => [
                'nullable',
                Rule::in(array_keys(Project::METHODOLOGIES))
            ],

            // Dates
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'planned_start_date' => 'nullable|date',
            'planned_end_date' => 'nullable|date|after:planned_start_date',

            // Budget and Financial
            'budget' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'is_billable' => 'boolean',
            'hourly_rate' => 'nullable|numeric|min:0',
            'fixed_price' => 'nullable|numeric|min:0',
            'billing_type' => 'nullable|string|in:HOURLY,FIXED,MILESTONE',

            // Settings
            'visibility' => 'nullable|string|in:PUBLIC,PRIVATE,TEAM,CLIENT',
            'auto_calculate_progress' => 'boolean',
            'enable_time_tracking' => 'boolean',
            'enable_expenses' => 'boolean',
            'enable_documents' => 'boolean',
            'enable_chat' => 'boolean',
            'enable_gantt' => 'boolean',
            'enable_kanban' => 'boolean',
            'enable_calendar' => 'boolean',

            // Team Members
            'team_members' => 'nullable|array',
            'team_members.*.user_id' => 'required|exists:users,id',
            'team_members.*.role' => 'required|string|max:100',
            'team_members.*.permissions' => 'nullable|array',
            'team_members.*.hourly_rate' => 'nullable|numeric|min:0',

            // Milestones
            'milestones' => 'nullable|array',
            'milestones.*.name' => 'required|string|max:255',
            'milestones.*.description' => 'nullable|string|max:1000',
            'milestones.*.due_date' => 'required|date',
            'milestones.*.budget' => 'nullable|numeric|min:0',

            // Custom Fields
            'custom_fields' => 'nullable|array',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',

            // Notifications
            'notification_settings' => 'nullable|array',
            'notification_settings.email_notifications' => 'boolean',
            'notification_settings.slack_notifications' => 'boolean',
            'notification_settings.milestone_alerts' => 'boolean',
            'notification_settings.deadline_alerts' => 'boolean',

            // Risk Management
            'risk_level' => 'nullable|string|in:LOW,MEDIUM,HIGH,CRITICAL',
            'risk_factors' => 'nullable|array',
            'risk_factors.*' => 'string|max:255',

            // Quality Assurance
            'quality_standards' => 'nullable|array',
            'testing_requirements' => 'nullable|string|max:2000',
            'acceptance_criteria' => 'nullable|string|max:2000',

            // Integration Settings
            'repository_url' => 'nullable|url',
            'deployment_url' => 'nullable|url',
            'staging_url' => 'nullable|url',
            'documentation_url' => 'nullable|url',

            // Templates and Automation
            'template_id' => 'nullable|exists:project_templates,id',
            'auto_create_tasks' => 'boolean',
            'auto_assign_tasks' => 'boolean',

            // Compliance and Standards
            'compliance_requirements' => 'nullable|array',
            'industry_standards' => 'nullable|array',
            'security_level' => 'nullable|string|in:LOW,MEDIUM,HIGH,CRITICAL',

            // Additional Settings
            'notes' => 'nullable|string|max:2000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'name.required' => 'اسم المشروع مطلوب',
            'name.max' => 'اسم المشروع لا يجب أن يتجاوز 255 حرف',
            'description.max' => 'وصف المشروع لا يجب أن يتجاوز 2000 حرف',
            'code.unique' => 'رمز المشروع مستخدم مسبقاً',
            'code.regex' => 'رمز المشروع يجب أن يحتوي على أحرف كبيرة وأرقام وشرطات فقط',
            'client_id.exists' => 'العميل المحدد غير موجود',
            'project_manager_id.required' => 'مدير المشروع مطلوب',
            'project_manager_id.exists' => 'مدير المشروع المحدد غير موجود',
            'parent_id.exists' => 'المشروع الأب المحدد غير موجود',

            // Status and Priority
            'status.in' => 'حالة المشروع المحددة غير صحيحة',
            'priority.in' => 'أولوية المشروع المحددة غير صحيحة',
            'category.in' => 'فئة المشروع المحددة غير صحيحة',
            'methodology.in' => 'منهجية المشروع المحددة غير صحيحة',

            // Dates
            'start_date.required' => 'تاريخ بداية المشروع مطلوب',
            'start_date.after_or_equal' => 'تاريخ بداية المشروع لا يمكن أن يكون في الماضي',
            'end_date.required' => 'تاريخ نهاية المشروع مطلوب',
            'end_date.after' => 'تاريخ نهاية المشروع يجب أن يكون بعد تاريخ البداية',
            'planned_end_date.after' => 'تاريخ النهاية المخطط يجب أن يكون بعد تاريخ البداية المخطط',

            // Budget and Financial
            'budget.numeric' => 'الميزانية يجب أن تكون رقماً',
            'budget.min' => 'الميزانية لا يمكن أن تكون سالبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'hourly_rate.numeric' => 'السعر بالساعة يجب أن يكون رقماً',
            'hourly_rate.min' => 'السعر بالساعة لا يمكن أن يكون سالباً',
            'fixed_price.numeric' => 'السعر الثابت يجب أن يكون رقماً',
            'fixed_price.min' => 'السعر الثابت لا يمكن أن يكون سالباً',
            'billing_type.in' => 'نوع الفوترة المحدد غير صحيح',

            // Settings
            'visibility.in' => 'مستوى الرؤية المحدد غير صحيح',

            // Team Members
            'team_members.array' => 'أعضاء الفريق يجب أن يكونوا مصفوفة',
            'team_members.*.user_id.required' => 'معرف المستخدم مطلوب لكل عضو فريق',
            'team_members.*.user_id.exists' => 'المستخدم المحدد غير موجود',
            'team_members.*.role.required' => 'دور عضو الفريق مطلوب',
            'team_members.*.hourly_rate.numeric' => 'السعر بالساعة لعضو الفريق يجب أن يكون رقماً',

            // Milestones
            'milestones.array' => 'المعالم يجب أن تكون مصفوفة',
            'milestones.*.name.required' => 'اسم المعلم مطلوب',
            'milestones.*.due_date.required' => 'تاريخ استحقاق المعلم مطلوب',
            'milestones.*.budget.numeric' => 'ميزانية المعلم يجب أن تكون رقماً',

            // Risk Management
            'risk_level.in' => 'مستوى المخاطر المحدد غير صحيح',

            // Security
            'security_level.in' => 'مستوى الأمان المحدد غير صحيح',

            // URLs
            'repository_url.url' => 'رابط المستودع غير صحيح',
            'deployment_url.url' => 'رابط النشر غير صحيح',
            'staging_url.url' => 'رابط الاختبار غير صحيح',
            'documentation_url.url' => 'رابط التوثيق غير صحيح',

            // Templates
            'template_id.exists' => 'قالب المشروع المحدد غير موجود',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        // إنشاء رمز المشروع تلقائياً إذا لم يتم تقديمه
        if (!$this->has('code') && $this->has('name')) {
            $this->merge([
                'code' => $this->generateProjectCode($this->name)
            ]);
        }

        // تعيين القيم الافتراضية
        $this->merge([
            'status' => $this->status ?? 'PLANNING',
            'priority' => $this->priority ?? 'MEDIUM',
            'currency' => $this->currency ?? 'SAR',
            'visibility' => $this->visibility ?? 'TEAM',
            'methodology' => $this->methodology ?? 'AGILE',
            'category' => $this->category ?? 'GENERAL',
            'is_billable' => $this->is_billable ?? true,
            'auto_calculate_progress' => $this->auto_calculate_progress ?? true,
            'enable_time_tracking' => $this->enable_time_tracking ?? true,
            'enable_expenses' => $this->enable_expenses ?? true,
            'enable_documents' => $this->enable_documents ?? true,
            'enable_chat' => $this->enable_chat ?? true,
            'enable_gantt' => $this->enable_gantt ?? true,
            'enable_kanban' => $this->enable_kanban ?? true,
            'enable_calendar' => $this->enable_calendar ?? true,
            'risk_level' => $this->risk_level ?? 'MEDIUM',
            'security_level' => $this->security_level ?? 'MEDIUM',
        ]);
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من عدم تعيين المشروع كأب لنفسه
            if ($this->parent_id && $this->parent_id == $this->id) {
                $validator->errors()->add('parent_id', 'لا يمكن للمشروع أن يكون أباً لنفسه');
            }

            // التحقق من صحة التواريخ المخططة
            if ($this->planned_start_date && $this->planned_end_date) {
                if ($this->planned_start_date >= $this->planned_end_date) {
                    $validator->errors()->add('planned_end_date', 'تاريخ النهاية المخطط يجب أن يكون بعد تاريخ البداية المخطط');
                }
            }

            // التحقق من صحة الميزانية والسعر
            if ($this->billing_type === 'FIXED' && !$this->fixed_price) {
                $validator->errors()->add('fixed_price', 'السعر الثابت مطلوب عند اختيار الفوترة الثابتة');
            }

            if ($this->billing_type === 'HOURLY' && !$this->hourly_rate) {
                $validator->errors()->add('hourly_rate', 'السعر بالساعة مطلوب عند اختيار الفوترة بالساعة');
            }

            // التحقق من صحة أعضاء الفريق
            if ($this->team_members) {
                $userIds = collect($this->team_members)->pluck('user_id')->toArray();
                if (count($userIds) !== count(array_unique($userIds))) {
                    $validator->errors()->add('team_members', 'لا يمكن إضافة نفس المستخدم أكثر من مرة');
                }

                // التحقق من أن مدير المشروع ضمن أعضاء الفريق
                if (!in_array($this->project_manager_id, $userIds)) {
                    $this->merge([
                        'team_members' => array_merge($this->team_members ?? [], [[
                            'user_id' => $this->project_manager_id,
                            'role' => 'Project Manager',
                            'permissions' => ['all'],
                        ]])
                    ]);
                }
            }

            // التحقق من صحة المعالم
            if ($this->milestones) {
                foreach ($this->milestones as $index => $milestone) {
                    if (isset($milestone['due_date'])) {
                        $dueDate = \Carbon\Carbon::parse($milestone['due_date']);
                        $endDate = \Carbon\Carbon::parse($this->end_date);
                        
                        if ($dueDate > $endDate) {
                            $validator->errors()->add("milestones.{$index}.due_date", 'تاريخ استحقاق المعلم لا يمكن أن يكون بعد تاريخ نهاية المشروع');
                        }
                    }
                }
            }

            // التحقق من مدة المشروع
            $startDate = \Carbon\Carbon::parse($this->start_date);
            $endDate = \Carbon\Carbon::parse($this->end_date);
            $duration = $startDate->diffInDays($endDate);

            if ($duration > 365 * 3) { // 3 سنوات
                $validator->errors()->add('end_date', 'مدة المشروع لا يمكن أن تتجاوز 3 سنوات');
            }
        });
    }

    /**
     * إنشاء رمز المشروع
     */
    protected function generateProjectCode(string $name): string
    {
        $code = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));
        $code = substr($code, 0, 10);
        
        // إضافة رقم تسلسلي إذا كان الرمز موجوداً
        $originalCode = $code;
        $counter = 1;
        
        while (Project::where('code', $code)->exists()) {
            $code = $originalCode . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }
}
