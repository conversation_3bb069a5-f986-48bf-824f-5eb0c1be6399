<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج السبرنت - Sprint Management for Agile/Scrum
 * يدعم إدارة السبرنتات في المنهجيات الرشيقة
 */
class Sprint extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'sprint_number',
        'start_date',
        'end_date',
        'status',
        'goal',
        'capacity',
        'velocity',
        'committed_story_points',
        'completed_story_points',
        'burndown_data',
        'retrospective_notes',
        'demo_notes',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'capacity' => 'integer',
        'velocity' => 'decimal:2',
        'committed_story_points' => 'integer',
        'completed_story_points' => 'integer',
        'burndown_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * العلاقة مع اجتماعات السبرنت
     */
    public function meetings(): HasMany
    {
        return $this->hasMany(SprintMeeting::class);
    }

    /**
     * الحصول على مدة السبرنت بالأيام
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->status === 'COMPLETED') {
            return 0;
        }
        
        return now()->diffInDays($this->end_date, false);
    }

    /**
     * الحصول على نسبة الإنجاز
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->committed_story_points === 0) {
            return 0;
        }
        
        return ($this->completed_story_points / $this->committed_story_points) * 100;
    }

    /**
     * الحصول على السرعة المحققة
     */
    public function getActualVelocityAttribute(): float
    {
        return $this->completed_story_points;
    }

    /**
     * التحقق من كون السبرنت نشط
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'ACTIVE' && 
               now()->between($this->start_date, $this->end_date);
    }

    /**
     * التحقق من انتهاء السبرنت
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->end_date->isPast() && $this->status !== 'COMPLETED';
    }

    /**
     * بدء السبرنت
     */
    public function start(int $startedBy): bool
    {
        if ($this->status !== 'PLANNED') {
            return false;
        }

        $this->update([
            'status' => 'ACTIVE',
            'metadata' => array_merge($this->metadata ?? [], [
                'started_by' => $startedBy,
                'started_at' => now(),
            ]),
        ]);

        // تحديث حالة المهام
        $this->tasks()->update(['status' => 'TODO']);

        // إشعار الفريق
        $this->notifyTeam('sprint_started');

        return true;
    }

    /**
     * إنهاء السبرنت
     */
    public function complete(int $completedBy, array $retrospectiveData = []): bool
    {
        if ($this->status !== 'ACTIVE') {
            return false;
        }

        // حساب النقاط المكتملة
        $completedPoints = $this->tasks()
                               ->where('status', 'COMPLETED')
                               ->sum('story_points');

        $this->update([
            'status' => 'COMPLETED',
            'completed_story_points' => $completedPoints,
            'velocity' => $completedPoints,
            'retrospective_notes' => $retrospectiveData['retrospective_notes'] ?? null,
            'demo_notes' => $retrospectiveData['demo_notes'] ?? null,
            'metadata' => array_merge($this->metadata ?? [], [
                'completed_by' => $completedBy,
                'completed_at' => now(),
                'retrospective_data' => $retrospectiveData,
            ]),
        ]);

        // نقل المهام غير المكتملة للسبرنت التالي
        $this->moveIncompleteTasks();

        // إشعار الفريق
        $this->notifyTeam('sprint_completed');

        return true;
    }

    /**
     * إضافة مهمة للسبرنت
     */
    public function addTask(int $taskId): bool
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->project_id !== $this->project_id) {
            return false;
        }

        $task->update(['sprint_id' => $this->id]);

        // تحديث النقاط المخصصة
        $this->increment('committed_story_points', $task->story_points ?? 0);

        return true;
    }

    /**
     * إزالة مهمة من السبرنت
     */
    public function removeTask(int $taskId): bool
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->sprint_id !== $this->id) {
            return false;
        }

        $task->update(['sprint_id' => null]);

        // تحديث النقاط المخصصة
        $this->decrement('committed_story_points', $task->story_points ?? 0);

        return true;
    }

    /**
     * تحديث بيانات Burndown Chart
     */
    public function updateBurndownData(): void
    {
        $burndownData = $this->burndown_data ?? [];
        $today = now()->format('Y-m-d');

        $remainingPoints = $this->committed_story_points - $this->getCompletedPointsAsOf(now());
        
        $burndownData[$today] = [
            'date' => $today,
            'remaining_points' => $remainingPoints,
            'completed_points' => $this->getCompletedPointsAsOf(now()),
            'ideal_remaining' => $this->calculateIdealRemaining(now()),
        ];

        $this->update(['burndown_data' => $burndownData]);
    }

    /**
     * الحصول على النقاط المكتملة حتى تاريخ معين
     */
    protected function getCompletedPointsAsOf(\Carbon\Carbon $date): int
    {
        return $this->tasks()
                   ->where('status', 'COMPLETED')
                   ->where('completed_at', '<=', $date)
                   ->sum('story_points');
    }

    /**
     * حساب النقاط المثالية المتبقية
     */
    protected function calculateIdealRemaining(\Carbon\Carbon $date): float
    {
        $totalDays = $this->duration;
        $daysPassed = $this->start_date->diffInDays($date) + 1;
        
        if ($daysPassed >= $totalDays) {
            return 0;
        }
        
        $dailyBurnRate = $this->committed_story_points / $totalDays;
        return $this->committed_story_points - ($dailyBurnRate * $daysPassed);
    }

    /**
     * نقل المهام غير المكتملة
     */
    protected function moveIncompleteTasks(): void
    {
        $incompleteTasks = $this->tasks()
                               ->whereNotIn('status', ['COMPLETED', 'CANCELLED'])
                               ->get();

        // البحث عن السبرنت التالي
        $nextSprint = $this->project->sprints()
                                   ->where('sprint_number', '>', $this->sprint_number)
                                   ->where('status', 'PLANNED')
                                   ->orderBy('sprint_number')
                                   ->first();

        if ($nextSprint) {
            foreach ($incompleteTasks as $task) {
                $task->update(['sprint_id' => $nextSprint->id]);
                $nextSprint->increment('committed_story_points', $task->story_points ?? 0);
            }
        } else {
            // إزالة من السبرنت إذا لم يوجد سبرنت تالي
            $incompleteTasks->each(function ($task) {
                $task->update(['sprint_id' => null]);
            });
        }
    }

    /**
     * إشعار الفريق
     */
    protected function notifyTeam(string $event): void
    {
        $teamMembers = $this->project->teamMembers;
        
        foreach ($teamMembers as $member) {
            $member->notify(new \App\Notifications\SprintNotification($this, $event));
        }
    }

    /**
     * إنشاء اجتماع سبرنت
     */
    public function createMeeting(string $type, array $meetingData): SprintMeeting
    {
        return $this->meetings()->create(array_merge($meetingData, [
            'type' => $type, // PLANNING, DAILY, REVIEW, RETROSPECTIVE
            'sprint_id' => $this->id,
        ]));
    }

    /**
     * الحصول على إحصائيات السبرنت
     */
    public function getStatistics(): array
    {
        return [
            'total_tasks' => $this->tasks()->count(),
            'completed_tasks' => $this->tasks()->where('status', 'COMPLETED')->count(),
            'in_progress_tasks' => $this->tasks()->where('status', 'IN_PROGRESS')->count(),
            'todo_tasks' => $this->tasks()->where('status', 'TODO')->count(),
            'committed_points' => $this->committed_story_points,
            'completed_points' => $this->completed_story_points,
            'completion_percentage' => $this->completion_percentage,
            'velocity' => $this->actual_velocity,
            'days_remaining' => $this->days_remaining,
            'burndown_trend' => $this->analyzeBurndownTrend(),
        ];
    }

    /**
     * تحليل اتجاه Burndown
     */
    protected function analyzeBurndownTrend(): string
    {
        if (empty($this->burndown_data) || count($this->burndown_data) < 2) {
            return 'INSUFFICIENT_DATA';
        }

        $data = array_values($this->burndown_data);
        $recent = array_slice($data, -3); // آخر 3 أيام

        $trend = 0;
        for ($i = 1; $i < count($recent); $i++) {
            $trend += $recent[$i]['remaining_points'] - $recent[$i-1]['remaining_points'];
        }

        if ($trend < -5) {
            return 'AHEAD_OF_SCHEDULE';
        } elseif ($trend > 5) {
            return 'BEHIND_SCHEDULE';
        } else {
            return 'ON_TRACK';
        }
    }

    /**
     * البحث في السبرنتات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('goal', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة السبرنتات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'ACTIVE');
    }

    /**
     * فلترة السبرنتات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'COMPLETED');
    }

    /**
     * ترتيب حسب رقم السبرنت
     */
    public function scopeOrderByNumber($query, string $direction = 'asc')
    {
        return $query->orderBy('sprint_number', $direction);
    }
}
