<?php

namespace App\Domains\Integration\Exceptions;

use Exception;

/**
 * Validation Exception
 * Thrown when data validation fails
 */
class ValidationException extends Exception
{
    protected array $errors;
    protected array $context;

    public function __construct(
        string $message = 'Validation failed',
        array $errors = [],
        array $context = [],
        int $code = 0,
        Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->errors = $errors;
        $this->context = $context;
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get validation context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Check if there are validation errors
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Get first validation error
     */
    public function getFirstError(): ?string
    {
        return $this->errors[0] ?? null;
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'errors' => $this->errors,
            'context' => $this->context,
            'code' => $this->getCode(),
        ];
    }
}
