<?php

namespace App\Domains\Taxation\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\Taxation\Models\TaxReturn;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أمان الإقرارات الضريبية
 * Tax Return Security Policy
 */
class TaxReturnPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي إقرار ضريبي
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_tax_returns') || 
               $user->hasRole(['admin', 'tax_manager', 'tax_specialist', 'financial_manager', 'accountant']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الإقرار الضريبي
     */
    public function view(Employee $user, TaxReturn $taxReturn): bool
    {
        // المدراء وموظفو الضرائب يمكنهم رؤية جميع الإقرارات
        if ($user->hasRole(['admin', 'tax_manager', 'tax_specialist', 'financial_manager'])) {
            return true;
        }

        // المحاسبون يمكنهم رؤية الإقرارات للمراجعة
        if ($user->hasRole('accountant')) {
            return true;
        }

        // المراجعون يمكنهم رؤية الإقرارات للمراجعة
        if ($user->hasRole('auditor')) {
            return true;
        }

        return $user->hasPermissionTo('view_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء إقرار ضريبي
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_tax_returns') || 
               $user->hasRole(['admin', 'tax_manager', 'tax_specialist']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الإقرار الضريبي
     */
    public function update(Employee $user, TaxReturn $taxReturn): bool
    {
        // منع تعديل الإقرارات المقدمة
        if ($taxReturn->status === 'submitted') {
            return $user->hasRole(['admin', 'tax_manager']);
        }

        // منع تعديل الإقرارات المعتمدة
        if ($taxReturn->status === 'approved') {
            return false;
        }

        // المدراء وموظفو الضرائب يمكنهم تحديث الإقرارات
        if ($user->hasRole(['admin', 'tax_manager', 'tax_specialist'])) {
            return true;
        }

        return $user->hasPermissionTo('update_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الإقرار الضريبي
     */
    public function delete(Employee $user, TaxReturn $taxReturn): bool
    {
        // منع حذف الإقرارات المقدمة أو المعتمدة
        if (in_array($taxReturn->status, ['submitted', 'approved'])) {
            return false;
        }

        // فقط المدراء يمكنهم حذف الإقرارات
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('delete_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تقديم الإقرار الضريبي
     */
    public function submit(Employee $user, TaxReturn $taxReturn): bool
    {
        // يجب أن يكون الإقرار في حالة مسودة أو معتمد
        if (!in_array($taxReturn->status, ['draft', 'approved'])) {
            return false;
        }

        // التحقق من اكتمال البيانات المطلوبة
        if (!$taxReturn->is_complete) {
            return false;
        }

        return $user->hasRole(['admin', 'tax_manager', 'tax_specialist']) || 
               $user->hasPermissionTo('submit_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه اعتماد الإقرار الضريبي
     */
    public function approve(Employee $user, TaxReturn $taxReturn): bool
    {
        // يجب أن يكون الإقرار في حالة مسودة
        if ($taxReturn->status !== 'draft') {
            return false;
        }

        // منع اعتماد الإقرار من قبل منشئه
        if ($taxReturn->created_by === $user->id) {
            return false;
        }

        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('approve_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل الإقرار الضريبي
     */
    public function amend(Employee $user, TaxReturn $taxReturn): bool
    {
        // يجب أن يكون الإقرار مقدم
        if ($taxReturn->status !== 'submitted') {
            return false;
        }

        // التحقق من فترة السماح للتعديل
        $amendmentDeadline = $taxReturn->submission_date->addDays(config('taxation.tax_returns.amendment_deadline_days', 90));
        if (now()->isAfter($amendmentDeadline)) {
            return false;
        }

        return $user->hasRole(['admin', 'tax_manager', 'tax_specialist']) || 
               $user->hasPermissionTo('amend_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حساب الالتزام الضريبي
     */
    public function calculateLiability(Employee $user, TaxReturn $taxReturn): bool
    {
        return $this->update($user, $taxReturn);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه التحقق من صحة الإقرار
     */
    public function validate(Employee $user, TaxReturn $taxReturn): bool
    {
        return $this->view($user, $taxReturn);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تنزيل PDF للإقرار
     */
    public function downloadPdf(Employee $user, TaxReturn $taxReturn): bool
    {
        return $this->view($user, $taxReturn);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المستندات الداعمة
     */
    public function viewSupportingDocuments(Employee $user, TaxReturn $taxReturn): bool
    {
        return $this->view($user, $taxReturn);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه رفع المستندات الداعمة
     */
    public function uploadSupportingDocuments(Employee $user, TaxReturn $taxReturn): bool
    {
        // منع رفع المستندات للإقرارات المعتمدة
        if ($taxReturn->status === 'approved') {
            return false;
        }

        return $this->update($user, $taxReturn);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء إقرار تلقائي
     */
    public function autoGenerate(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager', 'tax_specialist']) || 
               $user->hasPermissionTo('auto_generate_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تقديم إقرارات متعددة
     */
    public function bulkSubmit(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('bulk_submit_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الإقرارات المتأخرة
     */
    public function viewOverdue(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager', 'tax_specialist', 'financial_manager']) || 
               $user->hasPermissionTo('view_overdue_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المواعيد النهائية
     */
    public function viewDeadlines(Employee $user): bool
    {
        return $this->viewAny($user);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التعديلات
     */
    public function viewAmendments(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager', 'tax_specialist', 'auditor']) || 
               $user->hasPermissionTo('view_tax_return_amendments');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير الإقرارات
     */
    public function export(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager', 'auditor']) || 
               $user->hasPermissionTo('export_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد الإقرارات
     */
    public function import(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('import_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تاريخ الإقرار
     */
    public function viewHistory(Employee $user, TaxReturn $taxReturn): bool
    {
        return $this->view($user, $taxReturn) && 
               ($user->hasRole(['admin', 'tax_manager', 'tax_specialist', 'auditor']) || 
                $user->hasPermissionTo('view_tax_return_history'));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء مراجعة للإقرار
     */
    public function audit(Employee $user, TaxReturn $taxReturn): bool
    {
        return $user->hasRole(['admin', 'auditor', 'tax_manager']) || 
               $user->hasPermissionTo('audit_tax_returns');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات الحساسة
     */
    public function viewSensitiveData(Employee $user, TaxReturn $taxReturn): bool
    {
        // البيانات الحساسة مثل المبالغ المالية والحسابات
        if ($user->hasRole(['admin', 'tax_manager', 'financial_manager'])) {
            return true;
        }

        return $user->hasPermissionTo('view_sensitive_tax_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة الفترات الضريبية
     */
    public function managePeriods(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('manage_tax_periods');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تكوين الإعدادات الضريبية
     */
    public function manageSettings(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('manage_tax_settings');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه الوصول للتقارير المتقدمة
     */
    public function viewAdvancedReports(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager', 'financial_manager', 'auditor']) || 
               $user->hasPermissionTo('view_advanced_tax_reports');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة التكامل مع الهيئات الضريبية
     */
    public function manageAuthorityIntegration(Employee $user): bool
    {
        return $user->hasRole(['admin', 'tax_manager']) || 
               $user->hasPermissionTo('manage_tax_authority_integration');
    }
}
