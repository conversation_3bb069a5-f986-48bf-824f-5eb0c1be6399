<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قالب المعلم - Milestone Template
 */
class MilestoneTemplate extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'template_id',
        'name',
        'description',
        'type',
        'days_from_start',
        'deliverables',
        'success_criteria',
        'dependencies',
    ];

    protected $casts = [
        'days_from_start' => 'integer',
        'deliverables' => 'array',
        'success_criteria' => 'array',
        'dependencies' => 'array',
    ];

    public function template(): BelongsTo
    {
        return $this->belongsTo(ProjectTemplate::class, 'template_id');
    }

    public function duplicate(int $newTemplateId): self
    {
        return self::create([
            'template_id' => $newTemplateId,
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type,
            'days_from_start' => $this->days_from_start,
            'deliverables' => $this->deliverables,
            'success_criteria' => $this->success_criteria,
            'dependencies' => $this->dependencies,
        ]);
    }
}
