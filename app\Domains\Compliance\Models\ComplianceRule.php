<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * نموذج قواعد الامتثال
 * يدير جميع القواعد القانونية والتنظيمية لكل دولة
 */
class ComplianceRule extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'rule_id',
        'country_id',
        'rule_category',
        'rule_type',
        'rule_name_ar',
        'rule_name_en',
        'rule_description_ar',
        'rule_description_en',
        'authority_name',
        'legal_reference',
        'regulation_number',
        'effective_date',
        'expiry_date',
        'rule_conditions',
        'compliance_requirements',
        'validation_rules',
        'penalty_structure',
        'exemption_criteria',
        'applicable_entities',
        'frequency',
        'deadline_calculation',
        'notification_rules',
        'escalation_rules',
        'automation_config',
        'integration_endpoints',
        'documentation_requirements',
        'audit_requirements',
        'reporting_templates',
        'risk_level',
        'priority',
        'status',
        'last_updated_by_authority',
        'next_review_date',
        'compliance_score_impact',
        'metadata',
    ];

    protected $casts = [
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'rule_conditions' => 'array',
        'compliance_requirements' => 'array',
        'validation_rules' => 'array',
        'penalty_structure' => 'array',
        'exemption_criteria' => 'array',
        'applicable_entities' => 'array',
        'deadline_calculation' => 'array',
        'notification_rules' => 'array',
        'escalation_rules' => 'array',
        'automation_config' => 'array',
        'integration_endpoints' => 'array',
        'documentation_requirements' => 'array',
        'audit_requirements' => 'array',
        'reporting_templates' => 'array',
        'last_updated_by_authority' => 'datetime',
        'next_review_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * فئات القواعد
     */
    const RULE_CATEGORIES = [
        'tax' => 'الضرائب',
        'social_security' => 'الضمان الاجتماعي',
        'e_invoicing' => 'الفوترة الإلكترونية',
        'accounting' => 'المحاسبة',
        'labor' => 'العمل',
        'customs' => 'الجمارك',
        'banking' => 'البنوك',
        'insurance' => 'التأمين',
        'licensing' => 'التراخيص',
        'environmental' => 'البيئة',
        'data_protection' => 'حماية البيانات',
        'anti_money_laundering' => 'مكافحة غسل الأموال',
    ];

    /**
     * أنواع القواعد
     */
    const RULE_TYPES = [
        'mandatory' => 'إلزامي',
        'optional' => 'اختياري',
        'conditional' => 'مشروط',
        'temporary' => 'مؤقت',
        'emergency' => 'طوارئ',
    ];

    /**
     * مستويات المخاطر
     */
    const RISK_LEVELS = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
    ];

    /**
     * حالات القاعدة
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'pending' => 'قيد الانتظار',
        'suspended' => 'معلق',
        'expired' => 'منتهي الصلاحية',
        'under_review' => 'قيد المراجعة',
    ];

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع أنشطة الامتثال
     */
    public function complianceActivities(): HasMany
    {
        return $this->hasMany(ComplianceActivity::class);
    }

    /**
     * العلاقة مع تنبيهات الامتثال
     */
    public function complianceAlerts(): HasMany
    {
        return $this->hasMany(ComplianceAlert::class);
    }

    /**
     * التحقق من انطباق القاعدة على كيان معين
     */
    public function isApplicableToEntity(array $entityData): bool
    {
        $applicableEntities = $this->applicable_entities ?? [];
        
        if (empty($applicableEntities)) {
            return true; // تنطبق على جميع الكيانات
        }

        foreach ($applicableEntities as $criteria) {
            if ($this->checkEntityCriteria($criteria, $entityData)) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص معايير الكيان
     */
    protected function checkEntityCriteria(array $criteria, array $entityData): bool
    {
        foreach ($criteria as $field => $expectedValue) {
            $actualValue = $entityData[$field] ?? null;
            
            if (is_array($expectedValue)) {
                if (!in_array($actualValue, $expectedValue)) {
                    return false;
                }
            } else {
                if ($actualValue !== $expectedValue) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * التحقق من الامتثال للقاعدة
     */
    public function checkCompliance(array $data): array
    {
        $validationRules = $this->validation_rules ?? [];
        $complianceResult = [
            'rule_id' => $this->rule_id,
            'rule_name' => $this->rule_name_ar,
            'compliant' => true,
            'violations' => [],
            'warnings' => [],
            'score' => 100,
            'checked_at' => now(),
        ];

        foreach ($validationRules as $rule) {
            $result = $this->validateRule($rule, $data);
            
            if (!$result['valid']) {
                $complianceResult['compliant'] = false;
                $complianceResult['violations'][] = $result;
                $complianceResult['score'] -= $result['penalty_points'] ?? 10;
            } elseif ($result['warning']) {
                $complianceResult['warnings'][] = $result;
                $complianceResult['score'] -= $result['warning_points'] ?? 5;
            }
        }

        $complianceResult['score'] = max(0, $complianceResult['score']);

        return $complianceResult;
    }

    /**
     * التحقق من قاعدة واحدة
     */
    protected function validateRule(array $rule, array $data): array
    {
        $field = $rule['field'];
        $operator = $rule['operator'];
        $expectedValue = $rule['value'];
        $actualValue = $data[$field] ?? null;

        $result = [
            'rule' => $rule,
            'field' => $field,
            'expected' => $expectedValue,
            'actual' => $actualValue,
            'valid' => false,
            'warning' => false,
            'message' => '',
        ];

        switch ($operator) {
            case 'required':
                $result['valid'] = !empty($actualValue);
                $result['message'] = $result['valid'] ? 'الحقل مطلوب ومتوفر' : 'الحقل مطلوب ومفقود';
                break;

            case 'equals':
                $result['valid'] = $actualValue == $expectedValue;
                $result['message'] = $result['valid'] ? 'القيمة متطابقة' : 'القيمة غير متطابقة';
                break;

            case 'greater_than':
                $result['valid'] = $actualValue > $expectedValue;
                $result['message'] = $result['valid'] ? 'القيمة أكبر من المطلوب' : 'القيمة أقل من المطلوب';
                break;

            case 'less_than':
                $result['valid'] = $actualValue < $expectedValue;
                $result['message'] = $result['valid'] ? 'القيمة أقل من المطلوب' : 'القيمة أكبر من المطلوب';
                break;

            case 'between':
                $min = $expectedValue[0];
                $max = $expectedValue[1];
                $result['valid'] = $actualValue >= $min && $actualValue <= $max;
                $result['message'] = $result['valid'] ? 'القيمة ضمن النطاق المطلوب' : 'القيمة خارج النطاق المطلوب';
                break;

            case 'in_array':
                $result['valid'] = in_array($actualValue, (array) $expectedValue);
                $result['message'] = $result['valid'] ? 'القيمة ضمن القيم المسموحة' : 'القيمة غير مسموحة';
                break;

            case 'date_before':
                $result['valid'] = Carbon::parse($actualValue)->isBefore(Carbon::parse($expectedValue));
                $result['message'] = $result['valid'] ? 'التاريخ قبل الموعد المحدد' : 'التاريخ بعد الموعد المحدد';
                break;

            case 'date_after':
                $result['valid'] = Carbon::parse($actualValue)->isAfter(Carbon::parse($expectedValue));
                $result['message'] = $result['valid'] ? 'التاريخ بعد الموعد المحدد' : 'التاريخ قبل الموعد المحدد';
                break;

            case 'regex':
                $result['valid'] = preg_match($expectedValue, $actualValue);
                $result['message'] = $result['valid'] ? 'التنسيق صحيح' : 'التنسيق غير صحيح';
                break;

            default:
                $result['valid'] = true;
                $result['warning'] = true;
                $result['message'] = 'نوع التحقق غير مدعوم';
        }

        return $result;
    }

    /**
     * حساب الموعد النهائي
     */
    public function calculateDeadline(Carbon $baseDate = null): ?Carbon
    {
        $baseDate = $baseDate ?? now();
        $deadlineConfig = $this->deadline_calculation ?? [];

        if (empty($deadlineConfig)) {
            return null;
        }

        $deadline = $baseDate->copy();

        switch ($deadlineConfig['type']) {
            case 'fixed_date':
                $deadline = Carbon::parse($deadlineConfig['date']);
                break;

            case 'days_after':
                $deadline->addDays($deadlineConfig['days']);
                break;

            case 'end_of_month':
                $deadline->endOfMonth();
                if (isset($deadlineConfig['additional_days'])) {
                    $deadline->addDays($deadlineConfig['additional_days']);
                }
                break;

            case 'end_of_quarter':
                $deadline->endOfQuarter();
                if (isset($deadlineConfig['additional_days'])) {
                    $deadline->addDays($deadlineConfig['additional_days']);
                }
                break;

            case 'end_of_year':
                $deadline->endOfYear();
                if (isset($deadlineConfig['additional_days'])) {
                    $deadline->addDays($deadlineConfig['additional_days']);
                }
                break;

            case 'business_days_after':
                $businessDays = $deadlineConfig['business_days'];
                while ($businessDays > 0) {
                    $deadline->addDay();
                    if ($deadline->isWeekday()) {
                        $businessDays--;
                    }
                }
                break;
        }

        // تجنب العطل الرسمية
        if (isset($deadlineConfig['skip_holidays']) && $deadlineConfig['skip_holidays']) {
            $deadline = $this->adjustForHolidays($deadline);
        }

        return $deadline;
    }

    /**
     * تعديل التاريخ لتجنب العطل الرسمية
     */
    protected function adjustForHolidays(Carbon $date): Carbon
    {
        $holidays = $this->country->getPublicHolidays($date->year);
        
        while (in_array($date->format('Y-m-d'), $holidays) || $date->isWeekend()) {
            $date->addDay();
        }

        return $date;
    }

    /**
     * حساب الغرامة
     */
    public function calculatePenalty(array $violationData): array
    {
        $penaltyStructure = $this->penalty_structure ?? [];
        
        if (empty($penaltyStructure)) {
            return ['penalty' => 0, 'currency' => $this->country->currency];
        }

        $penalty = 0;
        $penaltyType = $penaltyStructure['type'] ?? 'fixed';

        switch ($penaltyType) {
            case 'fixed':
                $penalty = $penaltyStructure['amount'] ?? 0;
                break;

            case 'percentage':
                $baseAmount = $violationData['amount'] ?? 0;
                $percentage = $penaltyStructure['percentage'] ?? 0;
                $penalty = $baseAmount * ($percentage / 100);
                break;

            case 'daily':
                $dailyAmount = $penaltyStructure['daily_amount'] ?? 0;
                $daysLate = $violationData['days_late'] ?? 0;
                $penalty = $dailyAmount * $daysLate;
                break;

            case 'tiered':
                $tiers = $penaltyStructure['tiers'] ?? [];
                $amount = $violationData['amount'] ?? 0;
                
                foreach ($tiers as $tier) {
                    if ($amount >= $tier['min_amount'] && $amount <= ($tier['max_amount'] ?? PHP_FLOAT_MAX)) {
                        $penalty = $tier['penalty'];
                        break;
                    }
                }
                break;
        }

        // تطبيق الحد الأدنى والأقصى
        $minPenalty = $penaltyStructure['min_penalty'] ?? 0;
        $maxPenalty = $penaltyStructure['max_penalty'] ?? PHP_FLOAT_MAX;
        
        $penalty = max($minPenalty, min($penalty, $maxPenalty));

        return [
            'penalty' => $penalty,
            'currency' => $this->country->currency,
            'calculation_method' => $penaltyType,
            'calculated_at' => now(),
        ];
    }

    /**
     * التحقق من الإعفاء
     */
    public function checkExemption(array $entityData): array
    {
        $exemptionCriteria = $this->exemption_criteria ?? [];
        
        if (empty($exemptionCriteria)) {
            return ['exempt' => false, 'reason' => null];
        }

        foreach ($exemptionCriteria as $exemption) {
            if ($this->checkExemptionCriteria($exemption, $entityData)) {
                return [
                    'exempt' => true,
                    'reason' => $exemption['reason'] ?? 'يستوفي معايير الإعفاء',
                    'exemption_type' => $exemption['type'] ?? 'general',
                ];
            }
        }

        return ['exempt' => false, 'reason' => null];
    }

    /**
     * فحص معايير الإعفاء
     */
    protected function checkExemptionCriteria(array $exemption, array $entityData): bool
    {
        $conditions = $exemption['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            $field = $condition['field'];
            $operator = $condition['operator'];
            $value = $condition['value'];
            $actualValue = $entityData[$field] ?? null;

            $result = match ($operator) {
                '=' => $actualValue == $value,
                '!=' => $actualValue != $value,
                '>' => $actualValue > $value,
                '<' => $actualValue < $value,
                'in' => in_array($actualValue, (array) $value),
                'not_in' => !in_array($actualValue, (array) $value),
                default => false,
            };

            if (!$result) {
                return false;
            }
        }

        return true;
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>', now());
                    });
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('rule_category', $category);
    }

    public function scopeByRiskLevel($query, string $riskLevel)
    {
        return $query->where('risk_level', $riskLevel);
    }

    public function scopeMandatory($query)
    {
        return $query->where('rule_type', 'mandatory');
    }

    public function scopeDueForReview($query)
    {
        return $query->where('next_review_date', '<=', now());
    }

    /**
     * التحقق من انتهاء صلاحية القاعدة
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * التحقق من الحاجة للمراجعة
     */
    public function needsReview(): bool
    {
        return $this->next_review_date && $this->next_review_date->isPast();
    }

    /**
     * الحصول على تأثير القاعدة على نقاط الامتثال
     */
    public function getComplianceScoreImpact(): int
    {
        return $this->compliance_score_impact ?? match ($this->risk_level) {
            'critical' => 25,
            'high' => 15,
            'medium' => 10,
            'low' => 5,
            default => 5,
        };
    }
}
