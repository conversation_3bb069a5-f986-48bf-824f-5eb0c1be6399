<?php

return [
    /*
    |--------------------------------------------------------------------------
    | نظام إدارة المشاريع - إعدادات عامة
    |--------------------------------------------------------------------------
    */

    'default_settings' => [
        'methodology' => 'AGILE',
        'currency' => 'MAD',
        'timezone' => 'Africa/Casablanca',
        'working_hours_per_day' => 8,
        'working_days_per_week' => 5,
        'default_hourly_rate' => 50,
        'auto_assign_tasks' => false,
        'auto_update_progress' => true,
        'require_time_approval' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | أنواع المشاريع المدعومة
    |--------------------------------------------------------------------------
    */

    'project_types' => [
        'SOFTWARE_DEVELOPMENT' => 'تطوير البرمجيات',
        'WEB_DEVELOPMENT' => 'تطوير المواقع',
        'MOBILE_APP' => 'تطبيقات الجوال',
        'MARKETING_CAMPAIGN' => 'حملة تسويقية',
        'CONSTRUCTION' => 'إنشاءات',
        'RESEARCH' => 'بحث وتطوير',
        'EVENT_PLANNING' => 'تخطيط الفعاليات',
        'PRODUCT_LAUNCH' => 'إطلاق منتج',
        'TRAINING_PROGRAM' => 'برنامج تدريبي',
        'CONSULTING' => 'استشارات',
        'GENERAL' => 'عام',
    ],

    /*
    |--------------------------------------------------------------------------
    | منهجيات إدارة المشاريع
    |--------------------------------------------------------------------------
    */

    'methodologies' => [
        'AGILE' => [
            'name' => 'أجايل',
            'description' => 'منهجية رشيقة للتطوير السريع',
            'features' => ['sprints', 'user_stories', 'daily_standups', 'retrospectives'],
            'default_sprint_duration' => 14, // أيام
            'story_points_enabled' => true,
        ],
        'SCRUM' => [
            'name' => 'سكرم',
            'description' => 'إطار عمل أجايل منظم',
            'features' => ['sprints', 'scrum_master', 'product_owner', 'ceremonies'],
            'default_sprint_duration' => 14,
            'story_points_enabled' => true,
        ],
        'KANBAN' => [
            'name' => 'كانبان',
            'description' => 'نظام بصري لإدارة التدفق',
            'features' => ['kanban_board', 'wip_limits', 'continuous_flow'],
            'default_columns' => ['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE'],
        ],
        'WATERFALL' => [
            'name' => 'الشلال',
            'description' => 'منهجية تسلسلية تقليدية',
            'features' => ['sequential_phases', 'detailed_planning', 'documentation'],
            'phases' => ['ANALYSIS', 'DESIGN', 'DEVELOPMENT', 'TESTING', 'DEPLOYMENT'],
        ],
        'HYBRID' => [
            'name' => 'مختلط',
            'description' => 'دمج عدة منهجيات',
            'features' => ['flexible_approach', 'adaptive_planning'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | حالات المشاريع
    |--------------------------------------------------------------------------
    */

    'project_statuses' => [
        'PLANNING' => 'تخطيط',
        'IN_PROGRESS' => 'قيد التنفيذ',
        'ON_HOLD' => 'متوقف مؤقتاً',
        'COMPLETED' => 'مكتمل',
        'CANCELLED' => 'ملغي',
        'ARCHIVED' => 'مؤرشف',
    ],

    /*
    |--------------------------------------------------------------------------
    | مستويات الأولوية
    |--------------------------------------------------------------------------
    */

    'priority_levels' => [
        'LOW' => ['name' => 'منخفض', 'color' => '#28a745', 'weight' => 1],
        'MEDIUM' => ['name' => 'متوسط', 'color' => '#ffc107', 'weight' => 2],
        'HIGH' => ['name' => 'عالي', 'color' => '#fd7e14', 'weight' => 3],
        'CRITICAL' => ['name' => 'حرج', 'color' => '#dc3545', 'weight' => 4],
    ],

    /*
    |--------------------------------------------------------------------------
    | أنواع المهام
    |--------------------------------------------------------------------------
    */

    'task_types' => [
        'TASK' => 'مهمة',
        'USER_STORY' => 'قصة مستخدم',
        'BUG' => 'خطأ',
        'FEATURE' => 'ميزة',
        'EPIC' => 'ملحمة',
        'SPIKE' => 'استكشاف',
        'RESEARCH' => 'بحث',
        'DOCUMENTATION' => 'توثيق',
        'TESTING' => 'اختبار',
        'DEPLOYMENT' => 'نشر',
    ],

    /*
    |--------------------------------------------------------------------------
    | حالات المهام
    |--------------------------------------------------------------------------
    */

    'task_statuses' => [
        'TODO' => ['name' => 'قائمة المهام', 'color' => '#6c757d'],
        'IN_PROGRESS' => ['name' => 'قيد التنفيذ', 'color' => '#007bff'],
        'REVIEW' => ['name' => 'قيد المراجعة', 'color' => '#ffc107'],
        'TESTING' => ['name' => 'قيد الاختبار', 'color' => '#17a2b8'],
        'COMPLETED' => ['name' => 'مكتملة', 'color' => '#28a745'],
        'CANCELLED' => ['name' => 'ملغية', 'color' => '#dc3545'],
        'ON_HOLD' => ['name' => 'متوقفة', 'color' => '#6f42c1'],
    ],

    /*
    |--------------------------------------------------------------------------
    | أدوار الفريق
    |--------------------------------------------------------------------------
    */

    'team_roles' => [
        'PROJECT_MANAGER' => 'مدير المشروع',
        'TEAM_LEAD' => 'قائد الفريق',
        'DEVELOPER' => 'مطور',
        'DESIGNER' => 'مصمم',
        'TESTER' => 'مختبر',
        'ANALYST' => 'محلل',
        'ARCHITECT' => 'مهندس معماري',
        'SCRUM_MASTER' => 'سكرم ماستر',
        'PRODUCT_OWNER' => 'مالك المنتج',
        'STAKEHOLDER' => 'صاحب مصلحة',
        'CLIENT' => 'عميل',
        'MEMBER' => 'عضو',
    ],

    /*
    |--------------------------------------------------------------------------
    | صلاحيات الفريق
    |--------------------------------------------------------------------------
    */

    'team_permissions' => [
        'VIEW' => 'عرض',
        'COMMENT' => 'تعليق',
        'EDIT_TASKS' => 'تعديل المهام',
        'CREATE_TASKS' => 'إنشاء المهام',
        'DELETE_TASKS' => 'حذف المهام',
        'MANAGE_TEAM' => 'إدارة الفريق',
        'MANAGE_FILES' => 'إدارة الملفات',
        'MANAGE_BUDGET' => 'إدارة الميزانية',
        'APPROVE_TIME' => 'موافقة الوقت',
        'MANAGE_RISKS' => 'إدارة المخاطر',
        'ADMIN' => 'إدارة كاملة',
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الملفات
    |--------------------------------------------------------------------------
    */

    'files' => [
        'max_file_size' => 50 * 1024 * 1024, // 50MB
        'allowed_extensions' => [
            'documents' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
            'spreadsheets' => ['xls', 'xlsx', 'csv', 'ods'],
            'presentations' => ['ppt', 'pptx', 'odp'],
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
            'videos' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
            'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac'],
            'archives' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'code' => ['php', 'js', 'html', 'css', 'json', 'xml', 'sql'],
        ],
        'storage_limit' => 5 * 1024 * 1024 * 1024, // 5GB per project
        'virus_scan_enabled' => true,
        'auto_extract_text' => true,
        'generate_thumbnails' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات تتبع الوقت
    |--------------------------------------------------------------------------
    */

    'time_tracking' => [
        'require_description' => true,
        'auto_stop_idle' => true,
        'idle_timeout_minutes' => 15,
        'round_to_minutes' => 15,
        'allow_manual_entry' => true,
        'require_approval' => true,
        'billable_by_default' => true,
        'track_breaks' => true,
        'screenshot_frequency' => 0, // 0 = disabled, minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التكامل
    |--------------------------------------------------------------------------
    */

    'integrations' => [
        'slack' => [
            'enabled' => true,
            'webhook_timeout' => 30,
        ],
        'teams' => [
            'enabled' => true,
            'webhook_timeout' => 30,
        ],
        'google_calendar' => [
            'enabled' => true,
            'sync_frequency' => 'hourly',
        ],
        'zoom' => [
            'enabled' => true,
            'auto_record' => false,
        ],
        'github' => [
            'enabled' => true,
            'auto_link_commits' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الإشعارات
    |--------------------------------------------------------------------------
    */

    'notifications' => [
        'channels' => ['database', 'mail', 'push', 'slack'],
        'default_preferences' => [
            'task_assigned' => ['database', 'mail'],
            'task_completed' => ['database'],
            'deadline_approaching' => ['database', 'mail', 'push'],
            'project_status_changed' => ['database', 'mail'],
            'comment_added' => ['database'],
            'file_uploaded' => ['database'],
        ],
        'digest_frequency' => 'daily', // daily, weekly, never
        'quiet_hours' => [
            'enabled' => true,
            'start' => '22:00',
            'end' => '08:00',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التقارير
    |--------------------------------------------------------------------------
    */

    'reports' => [
        'auto_generate' => true,
        'frequency' => 'weekly',
        'recipients' => ['project_manager', 'stakeholders'],
        'include_charts' => true,
        'export_formats' => ['pdf', 'excel', 'csv'],
        'retention_days' => 365,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأمان
    |--------------------------------------------------------------------------
    */

    'security' => [
        'require_2fa_for_managers' => false,
        'session_timeout_minutes' => 480, // 8 hours
        'password_expiry_days' => 90,
        'audit_log_retention_days' => 365,
        'ip_whitelist_enabled' => false,
        'file_encryption_enabled' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأداء
    |--------------------------------------------------------------------------
    */

    'performance' => [
        'cache_duration' => 3600, // 1 hour
        'pagination_size' => 25,
        'search_results_limit' => 100,
        'file_preview_cache_days' => 7,
        'analytics_batch_size' => 1000,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات العمل عن بُعد
    |--------------------------------------------------------------------------
    */

    'remote_work' => [
        'offline_mode_enabled' => true,
        'sync_frequency_minutes' => 15,
        'offline_storage_days' => 7,
        'connection_timeout_seconds' => 30,
        'auto_sync_on_reconnect' => true,
        'conflict_resolution' => 'manual', // auto, manual
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأتمتة
    |--------------------------------------------------------------------------
    */

    'automation' => [
        'enabled' => true,
        'max_rules_per_project' => 50,
        'execution_timeout_seconds' => 300,
        'retry_failed_actions' => true,
        'log_all_executions' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التحليلات
    |--------------------------------------------------------------------------
    */

    'analytics' => [
        'enabled' => true,
        'real_time_updates' => true,
        'data_retention_months' => 24,
        'anonymize_data' => false,
        'export_enabled' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات النسخ الاحتياطي
    |--------------------------------------------------------------------------
    */

    'backup' => [
        'enabled' => true,
        'frequency' => 'daily',
        'retention_days' => 30,
        'include_files' => true,
        'compress_backups' => true,
        'encrypt_backups' => true,
    ],
];
