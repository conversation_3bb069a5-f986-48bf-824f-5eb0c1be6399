<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء تخطيط البيانات للتجارة الإلكترونية
 */
class ECommerceMappingException extends ECommerceException
{
    protected string $entityType = '';
    protected string $sourceField = '';
    protected string $targetField = '';
    protected array $mappingConfig = [];
    protected mixed $sourceValue = null;

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $entityType = '',
        string $sourceField = '',
        string $targetField = '',
        array $mappingConfig = [],
        mixed $sourceValue = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->entityType = $entityType;
        $this->sourceField = $sourceField;
        $this->targetField = $targetField;
        $this->mappingConfig = $mappingConfig;
        $this->sourceValue = $sourceValue;
        $this->errorCode = 'MAPPING_ERROR';
    }

    /**
     * الحصول على نوع الكيان
     */
    public function getEntityType(): string
    {
        return $this->entityType;
    }

    /**
     * الحصول على حقل المصدر
     */
    public function getSourceField(): string
    {
        return $this->sourceField;
    }

    /**
     * الحصول على الحقل المستهدف
     */
    public function getTargetField(): string
    {
        return $this->targetField;
    }

    /**
     * الحصول على إعدادات التخطيط
     */
    public function getMappingConfig(): array
    {
        return $this->mappingConfig;
    }

    /**
     * الحصول على قيمة المصدر
     */
    public function getSourceValue(): mixed
    {
        return $this->sourceValue;
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بحقل مفقود
     */
    public function isMissingFieldError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'missing') ||
               str_contains(strtolower($this->getMessage()), 'not found');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بنوع البيانات
     */
    public function isDataTypeError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'type') ||
               str_contains(strtolower($this->getMessage()), 'cast');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالقيمة
     */
    public function isValueError(): bool
    {
        return str_contains(strtolower($this->getMessage()), 'value') ||
               str_contains(strtolower($this->getMessage()), 'invalid');
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'entity_type' => $this->getEntityType(),
            'source_field' => $this->getSourceField(),
            'target_field' => $this->getTargetField(),
            'mapping_config' => $this->getMappingConfig(),
            'source_value' => $this->getSourceValue(),
            'source_value_type' => gettype($this->getSourceValue()),
            'is_missing_field_error' => $this->isMissingFieldError(),
            'is_data_type_error' => $this->isDataTypeError(),
            'is_value_error' => $this->isValueError(),
        ]);
    }
}
