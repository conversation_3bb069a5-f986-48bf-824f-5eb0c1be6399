<?php

namespace App\Domains\HR\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد الموظف
 * تنسيق شامل لبيانات الموظف مع دعم المستويات المختلفة
 */
class EmployeeResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'employee_number' => $this->employee_number,
            
            // Basic Information
            'basic_info' => [
                'first_name' => $this->first_name,
                'middle_name' => $this->middle_name,
                'last_name' => $this->last_name,
                'full_name' => $this->full_name,
                'first_name_ar' => $this->first_name_ar,
                'middle_name_ar' => $this->middle_name_ar,
                'last_name_ar' => $this->last_name_ar,
                'full_name_ar' => $this->full_name_ar,
                'email' => $this->email,
                'phone' => $this->phone,
                'mobile' => $this->mobile,
                'profile_picture' => $this->profile_picture ? asset('storage/' . $this->profile_picture) : null,
            ],

            // Personal Information
            'personal_info' => [
                'date_of_birth' => $this->date_of_birth?->format('Y-m-d'),
                'age' => $this->age,
                'gender' => $this->gender,
                'gender_label' => $this->gender ? \App\Domains\HR\Models\Employee::GENDERS[$this->gender] ?? $this->gender : null,
                'marital_status' => $this->marital_status,
                'marital_status_label' => $this->marital_status ? \App\Domains\HR\Models\Employee::MARITAL_STATUSES[$this->marital_status] ?? $this->marital_status : null,
                'nationality' => $this->nationality,
            ],

            // Identification Documents
            'documents' => [
                'national_id' => $this->national_id,
                'passport_number' => $this->passport_number,
                'passport_expiry' => $this->passport_expiry?->format('Y-m-d'),
                'visa_number' => $this->visa_number,
                'visa_expiry' => $this->visa_expiry?->format('Y-m-d'),
                'iqama_number' => $this->iqama_number,
                'iqama_expiry' => $this->iqama_expiry?->format('Y-m-d'),
                'expiring_documents' => $this->getExpiringDocuments(),
            ],

            // Address Information
            'address' => [
                'address_line_1' => $this->address_line_1,
                'address_line_2' => $this->address_line_2,
                'city' => $this->city,
                'state' => $this->state,
                'postal_code' => $this->postal_code,
                'country' => $this->country,
                'full_address' => trim("{$this->address_line_1}, {$this->city}, {$this->country}"),
            ],

            // Employment Information
            'employment' => [
                'hire_date' => $this->hire_date?->format('Y-m-d'),
                'probation_end_date' => $this->probation_end_date?->format('Y-m-d'),
                'years_of_service' => $this->years_of_service,
                'contract_type' => $this->contract_type,
                'contract_type_label' => $this->contract_type ? \App\Domains\HR\Models\Employee::CONTRACT_TYPES[$this->contract_type] ?? $this->contract_type : null,
                'employment_type' => $this->employment_type,
                'employment_type_label' => $this->employment_type ? \App\Domains\HR\Models\Employee::EMPLOYMENT_TYPES[$this->employment_type] ?? $this->employment_type : null,
                'work_location' => $this->work_location,
                'status' => $this->status,
                'status_label' => $this->status ? \App\Domains\HR\Models\Employee::STATUSES[$this->status] ?? $this->status : null,
                'is_active' => $this->is_active,
                'is_probation_completed' => $this->isProbationPeriodCompleted(),
                'termination_date' => $this->termination_date?->format('Y-m-d'),
                'termination_reason' => $this->termination_reason,
                'rehire_eligible' => $this->rehire_eligible,
            ],

            // Organizational Structure
            'organization' => [
                'department' => $this->whenLoaded('department', function () {
                    return [
                        'id' => $this->department->id,
                        'name' => $this->department->name,
                        'name_en' => $this->department->name_en,
                        'code' => $this->department->code,
                    ];
                }),
                'position' => $this->whenLoaded('position', function () {
                    return [
                        'id' => $this->position->id,
                        'title' => $this->position->title,
                        'title_en' => $this->position->title_en,
                        'level' => $this->position->level,
                        'grade' => $this->position->grade,
                    ];
                }),
                'manager' => $this->whenLoaded('manager', function () {
                    return $this->manager ? [
                        'id' => $this->manager->id,
                        'name' => $this->manager->full_name,
                        'employee_number' => $this->manager->employee_number,
                        'position' => $this->manager->position?->title,
                        'email' => $this->manager->email,
                    ] : null;
                }),
                'subordinates_count' => $this->whenLoaded('subordinates', function () {
                    return $this->subordinates->count();
                }),
                'company' => $this->whenLoaded('company', function () {
                    return $this->company ? [
                        'id' => $this->company->id,
                        'name' => $this->company->name,
                        'code' => $this->company->code,
                    ] : null;
                }),
                'branch' => $this->whenLoaded('branch', function () {
                    return $this->branch ? [
                        'id' => $this->branch->id,
                        'name' => $this->branch->name,
                        'code' => $this->branch->code,
                        'location' => $this->branch->location,
                    ] : null;
                }),
            ],

            // Financial Information
            'financial' => [
                'basic_salary' => $this->basic_salary,
                'currency' => $this->currency,
                'salary_frequency' => $this->salary_frequency,
                'formatted_salary' => $this->currency . ' ' . number_format($this->basic_salary, 2),
                'gross_salary' => $this->when(
                    method_exists($this, 'calculateGrossSalary'),
                    fn() => $this->calculateGrossSalary()
                ),
                'net_salary' => $this->when(
                    method_exists($this, 'calculateNetSalary'),
                    fn() => $this->calculateNetSalary()
                ),
            ],

            // Banking Information
            'banking' => [
                'bank_name' => $this->bank_name,
                'bank_account_number' => $this->bank_account_number,
                'iban' => $this->iban,
            ],

            // Emergency Contact
            'emergency_contact' => [
                'name' => $this->emergency_contact_name,
                'relationship' => $this->emergency_contact_relationship,
                'phone' => $this->emergency_contact_phone,
            ],

            // Current Contract
            'current_contract' => $this->whenLoaded('currentContract', function () {
                return $this->currentContract ? new EmployeeContractResource($this->currentContract) : null;
            }),

            // Leave Balances
            'leave_balances' => $this->whenLoaded('leaveBalances', function () {
                return LeaveBalanceResource::collection($this->leaveBalances);
            }),

            // Recent Performance Reviews
            'recent_performance_reviews' => $this->whenLoaded('performanceReviews', function () {
                return PerformanceReviewResource::collection($this->performanceReviews->take(3));
            }),

            // Recent Trainings
            'recent_trainings' => $this->whenLoaded('trainings', function () {
                return TrainingResource::collection($this->trainings->take(3));
            }),

            // User Account
            'user_account' => $this->whenLoaded('user', function () {
                return $this->user ? [
                    'id' => $this->user->id,
                    'username' => $this->user->username,
                    'email' => $this->user->email,
                    'email_verified_at' => $this->user->email_verified_at,
                    'last_login_at' => $this->user->last_login_at,
                    'is_active' => $this->user->is_active,
                    'roles' => $this->user->roles->pluck('name'),
                    'permissions' => $this->user->getAllPermissions()->pluck('name'),
                ] : null;
            }),

            // System Information
            'system_info' => [
                'notes' => $this->notes,
                'metadata' => $this->metadata,
                'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
                'created_by' => $this->created_by,
                'updated_by' => $this->updated_by,
            ],

            // Computed Fields
            'computed' => [
                'display_name' => $this->full_name,
                'display_name_ar' => $this->full_name_ar ?: $this->full_name,
                'avatar_url' => $this->profile_picture ? asset('storage/' . $this->profile_picture) : $this->getDefaultAvatar(),
                'status_color' => $this->getStatusColor(),
                'contract_type_color' => $this->getContractTypeColor(),
                'years_of_service_formatted' => $this->getFormattedYearsOfService(),
                'next_birthday' => $this->getNextBirthday(),
                'days_until_birthday' => $this->getDaysUntilBirthday(),
            ],

            // Permissions (for current user)
            'permissions' => [
                'can_view' => $request->user()?->can('view', $this->resource),
                'can_update' => $request->user()?->can('update', $this->resource),
                'can_delete' => $request->user()?->can('delete', $this->resource),
                'can_terminate' => $request->user()?->can('terminate', $this->resource),
                'can_reactivate' => $request->user()?->can('reactivate', $this->resource),
            ],
        ];
    }

    /**
     * الحصول على الصورة الافتراضية
     */
    protected function getDefaultAvatar(): string
    {
        $gender = $this->gender === 'FEMALE' ? 'female' : 'male';
        return asset("images/avatars/default-{$gender}.png");
    }

    /**
     * الحصول على لون الحالة
     */
    protected function getStatusColor(): string
    {
        return match ($this->status) {
            'ACTIVE' => '#10B981',
            'PROBATION' => '#F59E0B',
            'SUSPENDED' => '#EF4444',
            'TERMINATED' => '#6B7280',
            'RESIGNED' => '#8B5CF6',
            'RETIRED' => '#3B82F6',
            'ON_LEAVE' => '#06B6D4',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على لون نوع العقد
     */
    protected function getContractTypeColor(): string
    {
        return match ($this->contract_type) {
            'PERMANENT' => '#10B981',
            'TEMPORARY' => '#F59E0B',
            'CONTRACT' => '#3B82F6',
            'PART_TIME' => '#8B5CF6',
            'INTERNSHIP' => '#06B6D4',
            'CONSULTANT' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على سنوات الخدمة منسقة
     */
    protected function getFormattedYearsOfService(): string
    {
        $years = floor($this->years_of_service);
        $months = round(($this->years_of_service - $years) * 12);

        if ($years == 0) {
            return "{$months} شهر";
        } elseif ($months == 0) {
            return "{$years} سنة";
        } else {
            return "{$years} سنة و {$months} شهر";
        }
    }

    /**
     * الحصول على تاريخ عيد الميلاد القادم
     */
    protected function getNextBirthday(): ?string
    {
        if (!$this->date_of_birth) {
            return null;
        }

        $birthday = $this->date_of_birth->copy()->year(now()->year);
        
        if ($birthday->isPast()) {
            $birthday->addYear();
        }

        return $birthday->format('Y-m-d');
    }

    /**
     * الحصول على عدد الأيام حتى عيد الميلاد
     */
    protected function getDaysUntilBirthday(): ?int
    {
        $nextBirthday = $this->getNextBirthday();
        
        if (!$nextBirthday) {
            return null;
        }

        return now()->diffInDays(\Carbon\Carbon::parse($nextBirthday));
    }
}
