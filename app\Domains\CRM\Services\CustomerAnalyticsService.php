<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\CustomerInteraction;
use App\Domains\CRM\Models\MarketingCampaign;
use App\Domains\CRM\Models\CustomerSatisfactionSurvey;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * خدمة تحليلات العملاء - Customer Analytics Service
 * تحليلات متقدمة وذكية لسلوك العملاء والأداء
 */
class CustomerAnalyticsService
{
    /**
     * لوحة تحكم تحليلات العملاء الرئيسية
     */
    public function getMainDashboard(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'overview_metrics' => $this->getOverviewMetrics($dateFrom, $dateTo),
            'customer_acquisition' => $this->getCustomerAcquisitionMetrics($dateFrom, $dateTo),
            'customer_retention' => $this->getCustomerRetentionMetrics($dateFrom, $dateTo),
            'customer_lifetime_value' => $this->getCustomerLifetimeValueMetrics($dateFrom, $dateTo),
            'sales_performance' => $this->getSalesPerformanceMetrics($dateFrom, $dateTo),
            'marketing_performance' => $this->getMarketingPerformanceMetrics($dateFrom, $dateTo),
            'customer_satisfaction' => $this->getCustomerSatisfactionMetrics($dateFrom, $dateTo),
            'trends_and_insights' => $this->getTrendsAndInsights($dateFrom, $dateTo),
            'predictive_analytics' => $this->getPredictiveAnalytics(),
        ];
    }

    /**
     * تحليل دورة حياة العملاء
     */
    public function analyzeCustomerLifecycle(): array
    {
        $customers = Customer::with(['ecommerceOrders', 'interactions', 'satisfactionSurveys'])->get();
        
        $lifecycleData = [
            'prospects' => 0,
            'new_customers' => 0,
            'active_customers' => 0,
            'loyal_customers' => 0,
            'vip_customers' => 0,
            'at_risk' => 0,
            'churned' => 0,
            'dormant' => 0,
        ];

        $lifecycleValues = array_fill_keys(array_keys($lifecycleData), 0);
        $lifecycleDetails = [];

        foreach ($customers as $customer) {
            $stage = $this->determineLifecycleStage($customer);
            $lifecycleData[$stage]++;
            $lifecycleValues[$stage] += $customer->total_spent;
            
            $lifecycleDetails[$stage][] = [
                'customer_id' => $customer->id,
                'customer_name' => $customer->full_name,
                'total_spent' => $customer->total_spent,
                'last_interaction' => $customer->last_contact_at,
                'satisfaction_score' => $customer->satisfaction_score,
            ];
        }

        return [
            'stage_counts' => $lifecycleData,
            'stage_values' => $lifecycleValues,
            'stage_details' => $lifecycleDetails,
            'lifecycle_flow' => $this->analyzeLifecycleFlow(),
            'stage_conversion_rates' => $this->calculateStageConversionRates(),
            'average_stage_duration' => $this->calculateAverageStageDuration(),
        ];
    }

    /**
     * تحليل RFM متقدم
     */
    public function performAdvancedRFMAnalysis(): array
    {
        $customers = Customer::with(['ecommerceOrders' => function ($query) {
            $query->where('status', 'completed');
        }])->get();

        $rfmData = [];
        $rfmSegments = [
            'champions' => [],
            'loyal_customers' => [],
            'potential_loyalists' => [],
            'new_customers' => [],
            'promising' => [],
            'need_attention' => [],
            'about_to_sleep' => [],
            'at_risk' => [],
            'cannot_lose_them' => [],
            'hibernating' => [],
            'lost' => [],
        ];

        foreach ($customers as $customer) {
            $orders = $customer->ecommerceOrders;
            
            if ($orders->isEmpty()) {
                continue;
            }

            $rfm = $this->calculateRFMScores($customer, $orders);
            $segment = $this->determineRFMSegment($rfm);
            
            $rfmData[$customer->id] = array_merge($rfm, [
                'customer' => $customer,
                'segment' => $segment,
            ]);
            
            $rfmSegments[$segment][] = $customer->id;
        }

        return [
            'rfm_data' => $rfmData,
            'segment_distribution' => array_map('count', $rfmSegments),
            'segment_values' => $this->calculateSegmentValues($rfmSegments),
            'rfm_trends' => $this->analyzeRFMTrends(),
            'segment_recommendations' => $this->generateSegmentRecommendations($rfmSegments),
        ];
    }

    /**
     * تحليل سلوك العملاء
     */
    public function analyzeBehavioralPatterns(): array
    {
        return [
            'interaction_patterns' => $this->analyzeInteractionPatterns(),
            'purchase_patterns' => $this->analyzePurchasePatterns(),
            'communication_preferences' => $this->analyzeCommunicationPreferences(),
            'seasonal_behavior' => $this->analyzeSeasonalBehavior(),
            'channel_preferences' => $this->analyzeChannelPreferences(),
            'product_affinity' => $this->analyzeProductAffinity(),
            'time_based_patterns' => $this->analyzeTimeBasedPatterns(),
            'geographic_patterns' => $this->analyzeGeographicPatterns(),
        ];
    }

    /**
     * تحليل الأتراب (Cohort Analysis)
     */
    public function performCohortAnalysis(string $period = 'monthly'): array
    {
        $cohorts = [];
        $retentionData = [];
        
        // تحديد فترات الأتراب
        $startDate = Customer::min('created_at');
        $endDate = now();
        
        $periods = $this->generatePeriods($startDate, $endDate, $period);
        
        foreach ($periods as $periodStart) {
            $periodEnd = $this->getPeriodEnd($periodStart, $period);
            
            // العملاء الجدد في هذه الفترة
            $cohortCustomers = Customer::whereBetween('created_at', [$periodStart, $periodEnd])
                                     ->pluck('id')
                                     ->toArray();
            
            if (empty($cohortCustomers)) {
                continue;
            }
            
            $cohortSize = count($cohortCustomers);
            $cohortLabel = $periodStart->format($period === 'monthly' ? 'Y-m' : 'Y-W');
            
            $cohorts[$cohortLabel] = [
                'period_start' => $periodStart,
                'period_end' => $periodEnd,
                'customer_ids' => $cohortCustomers,
                'cohort_size' => $cohortSize,
            ];
            
            // حساب معدلات الاحتفاظ
            $retentionData[$cohortLabel] = $this->calculateCohortRetention(
                $cohortCustomers, 
                $periodStart, 
                $period
            );
        }
        
        return [
            'cohorts' => $cohorts,
            'retention_data' => $retentionData,
            'average_retention' => $this->calculateAverageRetention($retentionData),
            'retention_trends' => $this->analyzeRetentionTrends($retentionData),
            'cohort_insights' => $this->generateCohortInsights($retentionData),
        ];
    }

    /**
     * تحليل قيمة العميل الدائمة (CLV)
     */
    public function analyzeCustomerLifetimeValue(): array
    {
        $customers = Customer::where('total_spent', '>', 0)->get();
        
        $clvData = [];
        $clvSegments = [
            'high_value' => [],
            'medium_value' => [],
            'low_value' => [],
        ];
        
        foreach ($customers as $customer) {
            $clv = $this->calculatePredictiveCLV($customer);
            $segment = $this->determineCLVSegment($clv);
            
            $clvData[$customer->id] = [
                'customer' => $customer,
                'current_clv' => $customer->lifetime_value,
                'predicted_clv' => $clv,
                'clv_growth_potential' => $clv - $customer->lifetime_value,
                'segment' => $segment,
            ];
            
            $clvSegments[$segment][] = $customer->id;
        }
        
        return [
            'clv_data' => $clvData,
            'segment_distribution' => array_map('count', $clvSegments),
            'clv_statistics' => $this->calculateCLVStatistics($clvData),
            'clv_trends' => $this->analyzeCLVTrends(),
            'optimization_opportunities' => $this->identifyCLVOptimizationOpportunities($clvData),
        ];
    }

    /**
     * تحليل مخاطر فقدان العملاء
     */
    public function analyzeChurnRisk(): array
    {
        $customers = Customer::active()->get();
        
        $churnAnalysis = [
            'high_risk' => [],
            'medium_risk' => [],
            'low_risk' => [],
        ];
        
        foreach ($customers as $customer) {
            $riskScore = $this->calculateChurnRiskScore($customer);
            $riskLevel = $this->determineRiskLevel($riskScore);
            
            $churnAnalysis[$riskLevel][] = [
                'customer' => $customer,
                'risk_score' => $riskScore,
                'risk_factors' => $this->identifyRiskFactors($customer),
                'recommended_actions' => $this->getChurnPreventionActions($riskLevel),
            ];
        }
        
        return [
            'risk_distribution' => array_map('count', $churnAnalysis),
            'risk_analysis' => $churnAnalysis,
            'churn_predictors' => $this->identifyChurnPredictors(),
            'prevention_strategies' => $this->generateChurnPreventionStrategies(),
            'early_warning_signals' => $this->identifyEarlyWarningSignals(),
        ];
    }

    /**
     * تحليل فعالية الحملات التسويقية
     */
    public function analyzeCampaignEffectiveness(): array
    {
        $campaigns = MarketingCampaign::with(['recipients', 'generatedOpportunities'])->get();
        
        $campaignAnalysis = [];
        
        foreach ($campaigns as $campaign) {
            $metrics = $this->calculateCampaignMetrics($campaign);
            $roi = $this->calculateCampaignROI($campaign);
            $attribution = $this->analyzeCampaignAttribution($campaign);
            
            $campaignAnalysis[$campaign->id] = [
                'campaign' => $campaign,
                'metrics' => $metrics,
                'roi' => $roi,
                'attribution' => $attribution,
                'effectiveness_score' => $this->calculateEffectivenessScore($metrics, $roi),
            ];
        }
        
        return [
            'campaign_analysis' => $campaignAnalysis,
            'top_performing_campaigns' => $this->getTopPerformingCampaigns($campaignAnalysis),
            'campaign_benchmarks' => $this->calculateCampaignBenchmarks($campaignAnalysis),
            'optimization_recommendations' => $this->generateCampaignOptimizationRecommendations($campaignAnalysis),
        ];
    }

    /**
     * تحليل رضا العملاء
     */
    public function analyzeSatisfactionMetrics(): array
    {
        $surveys = CustomerSatisfactionSurvey::completed()->with(['customer'])->get();
        
        return [
            'overall_satisfaction' => $this->calculateOverallSatisfaction($surveys),
            'nps_analysis' => $this->analyzeNPS($surveys),
            'csat_analysis' => $this->analyzeCSAT($surveys),
            'ces_analysis' => $this->analyzeCES($surveys),
            'satisfaction_trends' => $this->analyzeSatisfactionTrends($surveys),
            'satisfaction_drivers' => $this->identifySatisfactionDrivers($surveys),
            'improvement_opportunities' => $this->identifyImprovementOpportunities($surveys),
        ];
    }

    /**
     * تحليلات تنبؤية
     */
    public function generatePredictiveInsights(): array
    {
        return [
            'sales_forecast' => $this->generateSalesForecast(),
            'customer_growth_prediction' => $this->predictCustomerGrowth(),
            'churn_prediction' => $this->predictChurn(),
            'clv_prediction' => $this->predictCLV(),
            'market_trends' => $this->analyzeMarketTrends(),
            'seasonal_predictions' => $this->generateSeasonalPredictions(),
            'opportunity_scoring' => $this->generateOpportunityScoring(),
        ];
    }

    // دوال مساعدة للحسابات المعقدة
    
    protected function getOverviewMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        return [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'active_customers' => Customer::active()->count(),
            'total_revenue' => Customer::sum('total_spent'),
            'average_clv' => Customer::avg('lifetime_value'),
            'customer_acquisition_cost' => $this->calculateCAC($dateFrom, $dateTo),
            'customer_retention_rate' => $this->calculateRetentionRate($dateFrom, $dateTo),
            'churn_rate' => $this->calculateChurnRate($dateFrom, $dateTo),
        ];
    }

    protected function getCustomerAcquisitionMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        $newCustomers = Customer::whereBetween('created_at', [$dateFrom, $dateTo])->get();
        
        return [
            'total_new_customers' => $newCustomers->count(),
            'acquisition_by_source' => $newCustomers->groupBy('source')->map->count(),
            'acquisition_by_channel' => $this->groupAcquisitionByChannel($newCustomers),
            'acquisition_cost_by_source' => $this->calculateCACBySource($newCustomers),
            'acquisition_trends' => $this->analyzeAcquisitionTrends($dateFrom, $dateTo),
            'conversion_funnel' => $this->analyzeConversionFunnel($dateFrom, $dateTo),
        ];
    }

    protected function determineLifecycleStage(Customer $customer): string
    {
        $daysSinceCreation = $customer->created_at->diffInDays(now());
        $totalSpent = $customer->total_spent;
        $lastPurchase = $customer->last_purchase_at;
        $totalOrders = $customer->total_orders;

        if ($totalSpent == 0) {
            return 'prospects';
        } elseif ($daysSinceCreation <= 90 && $totalOrders <= 2) {
            return 'new_customers';
        } elseif ($totalSpent >= 100000 || $customer->tier === 'vip') {
            return 'vip_customers';
        } elseif ($totalOrders >= 10 && $lastPurchase && $lastPurchase->diffInDays(now()) <= 180) {
            return 'loyal_customers';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) <= 180) {
            return 'active_customers';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 365) {
            return 'churned';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 180) {
            return 'at_risk';
        } else {
            return 'dormant';
        }
    }

    protected function calculateRFMScores(Customer $customer, $orders): array
    {
        // Recency: أيام منذ آخر شراء
        $lastOrder = $orders->sortByDesc('created_at')->first();
        $recency = $lastOrder->created_at->diffInDays(now());

        // Frequency: عدد الطلبات
        $frequency = $orders->count();

        // Monetary: إجمالي الإنفاق
        $monetary = $orders->sum('total_amount');

        return [
            'recency' => $recency,
            'frequency' => $frequency,
            'monetary' => $monetary,
            'recency_score' => $this->calculateRecencyScore($recency),
            'frequency_score' => $this->calculateFrequencyScore($frequency),
            'monetary_score' => $this->calculateMonetaryScore($monetary),
        ];
    }

    protected function calculateRecencyScore(int $days): int
    {
        if ($days <= 30) return 5;
        if ($days <= 90) return 4;
        if ($days <= 180) return 3;
        if ($days <= 365) return 2;
        return 1;
    }

    protected function calculateFrequencyScore(int $orders): int
    {
        if ($orders >= 20) return 5;
        if ($orders >= 10) return 4;
        if ($orders >= 5) return 3;
        if ($orders >= 2) return 2;
        return 1;
    }

    protected function calculateMonetaryScore(float $amount): int
    {
        if ($amount >= 100000) return 5;
        if ($amount >= 50000) return 4;
        if ($amount >= 10000) return 3;
        if ($amount >= 1000) return 2;
        return 1;
    }

    protected function determineRFMSegment(array $rfm): string
    {
        $score = $rfm['recency_score'] . $rfm['frequency_score'] . $rfm['monetary_score'];
        
        // منطق تحديد الشريحة بناءً على النقاط
        if (in_array($score, ['555', '554', '544', '545', '454', '455', '445'])) {
            return 'champions';
        } elseif (in_array($score, ['543', '444', '435', '355', '354', '345', '344', '335'])) {
            return 'loyal_customers';
        } elseif (in_array($score, ['553', '551', '552', '541', '542', '533', '532', '531', '452', '451'])) {
            return 'potential_loyalists';
        } elseif (in_array($score, ['512', '511', '422', '421', '412', '411', '311'])) {
            return 'new_customers';
        } elseif (in_array($score, ['525', '524', '523', '522', '521', '515', '514', '513', '425', '424', '413', '414', '415', '315', '314', '313'])) {
            return 'promising';
        } elseif (in_array($score, ['535', '534', '443', '434', '343', '334', '325', '324'])) {
            return 'need_attention';
        } elseif (in_array($score, ['155', '154', '144', '214', '215', '115', '114'])) {
            return 'about_to_sleep';
        } elseif (in_array($score, ['255', '254', '245', '244', '253', '252', '243', '242', '235', '234', '225', '224', '153', '152', '145', '143', '142', '135', '134', '125', '124'])) {
            return 'at_risk';
        } elseif (in_array($score, ['155', '154', '144', '214', '215', '115', '114'])) {
            return 'cannot_lose_them';
        } elseif (in_array($score, ['332', '322', '231', '241', '251', '233', '232', '223', '222', '132', '123', '122', '212', '211'])) {
            return 'hibernating';
        } else {
            return 'lost';
        }
    }

    // دوال مساعدة إضافية (ستكون فارغة للآن)
    protected function analyzeLifecycleFlow(): array { return []; }
    protected function calculateStageConversionRates(): array { return []; }
    protected function calculateAverageStageuration(): array { return []; }
    protected function calculateSegmentValues(array $segments): array { return []; }
    protected function analyzeRFMTrends(): array { return []; }
    protected function generateSegmentRecommendations(array $segments): array { return []; }
    protected function analyzeInteractionPatterns(): array { return []; }
    protected function analyzePurchasePatterns(): array { return []; }
    protected function analyzeCommunicationPreferences(): array { return []; }
    protected function analyzeSeasonalBehavior(): array { return []; }
    protected function analyzeChannelPreferences(): array { return []; }
    protected function analyzeProductAffinity(): array { return []; }
    protected function analyzeTimeBasedPatterns(): array { return []; }
    protected function analyzeGeographicPatterns(): array { return []; }
    protected function generatePeriods($start, $end, $period): array { return []; }
    protected function getPeriodEnd($start, $period) { return $start; }
    protected function calculateCohortRetention(array $customers, $start, $period): array { return []; }
    protected function calculateAverageRetention(array $data): array { return []; }
    protected function analyzeRetentionTrends(array $data): array { return []; }
    protected function generateCohortInsights(array $data): array { return []; }
    protected function calculatePredictiveCLV(Customer $customer): float { return 0; }
    protected function determineCLVSegment(float $clv): string { return 'medium_value'; }
    protected function calculateCLVStatistics(array $data): array { return []; }
    protected function analyzeCLVTrends(): array { return []; }
    protected function identifyCLVOptimizationOpportunities(array $data): array { return []; }
    protected function calculateChurnRiskScore(Customer $customer): float { return 0; }
    protected function determineRiskLevel(float $score): string { return 'low_risk'; }
    protected function identifyRiskFactors(Customer $customer): array { return []; }
    protected function getChurnPreventionActions(string $level): array { return []; }
    protected function identifyChurnPredictors(): array { return []; }
    protected function generateChurnPreventionStrategies(): array { return []; }
    protected function identifyEarlyWarningSignals(): array { return []; }
    protected function calculateCAC($from, $to): float { return 0; }
    protected function calculateRetentionRate($from, $to): float { return 0; }
    protected function calculateChurnRate($from, $to): float { return 0; }
    protected function groupAcquisitionByChannel($customers): array { return []; }
    protected function calculateCACBySource($customers): array { return []; }
    protected function analyzeAcquisitionTrends($from, $to): array { return []; }
    protected function analyzeConversionFunnel($from, $to): array { return []; }
    protected function getCustomerRetentionMetrics($from, $to): array { return []; }
    protected function getCustomerLifetimeValueMetrics($from, $to): array { return []; }
    protected function getSalesPerformanceMetrics($from, $to): array { return []; }
    protected function getMarketingPerformanceMetrics($from, $to): array { return []; }
    protected function getCustomerSatisfactionMetrics($from, $to): array { return []; }
    protected function getTrendsAndInsights($from, $to): array { return []; }
    protected function getPredictiveAnalytics(): array { return []; }
    protected function calculateCampaignMetrics($campaign): array { return []; }
    protected function calculateCampaignROI($campaign): array { return []; }
    protected function analyzeCampaignAttribution($campaign): array { return []; }
    protected function calculateEffectivenessScore($metrics, $roi): float { return 0; }
    protected function getTopPerformingCampaigns($analysis): array { return []; }
    protected function calculateCampaignBenchmarks($analysis): array { return []; }
    protected function generateCampaignOptimizationRecommendations($analysis): array { return []; }
    protected function calculateOverallSatisfaction($surveys): array { return []; }
    protected function analyzeNPS($surveys): array { return []; }
    protected function analyzeCSAT($surveys): array { return []; }
    protected function analyzeCES($surveys): array { return []; }
    protected function analyzeSatisfactionTrends($surveys): array { return []; }
    protected function identifySatisfactionDrivers($surveys): array { return []; }
    protected function identifyImprovementOpportunities($surveys): array { return []; }
    protected function generateSalesForecast(): array { return []; }
    protected function predictCustomerGrowth(): array { return []; }
    protected function predictChurn(): array { return []; }
    protected function predictCLV(): array { return []; }
    protected function analyzeMarketTrends(): array { return []; }
    protected function generateSeasonalPredictions(): array { return []; }
    protected function generateOpportunityScoring(): array { return []; }
}
