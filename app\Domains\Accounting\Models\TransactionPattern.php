<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج نمط المعاملة
 * يحفظ أنماط المعاملات للتصنيف الذكي
 */
class TransactionPattern extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'pattern_name',
        'description_pattern',
        'amount_range_min',
        'amount_range_max',
        'vendor_pattern',
        'category',
        'suggested_account_id',
        'confidence_score',
        'usage_count',
        'is_active',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'amount_range_min' => 'decimal:2',
        'amount_range_max' => 'decimal:2',
        'confidence_score' => 'decimal:2',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الحساب المقترح
     */
    public function suggestedAccount()
    {
        return $this->belongsTo(Account::class, 'suggested_account_id');
    }

    /**
     * التحقق من تطابق النمط مع المعاملة
     */
    public function matches(BankTransaction $transaction): bool
    {
        $score = 0;
        $maxScore = 0;

        // فحص نمط الوصف
        if ($this->description_pattern) {
            $maxScore += 40;
            if (preg_match('/' . $this->description_pattern . '/i', $transaction->description)) {
                $score += 40;
            }
        }

        // فحص نطاق المبلغ
        if ($this->amount_range_min || $this->amount_range_max) {
            $maxScore += 20;
            $amount = abs($transaction->amount);
            
            $minMatch = !$this->amount_range_min || $amount >= $this->amount_range_min;
            $maxMatch = !$this->amount_range_max || $amount <= $this->amount_range_max;
            
            if ($minMatch && $maxMatch) {
                $score += 20;
            }
        }

        // فحص نمط المورد
        if ($this->vendor_pattern) {
            $maxScore += 30;
            if (preg_match('/' . $this->vendor_pattern . '/i', $transaction->description)) {
                $score += 30;
            }
        }

        // فحص الفئة
        if ($this->category) {
            $maxScore += 10;
            // يمكن إضافة منطق فحص الفئة هنا
        }

        return $maxScore > 0 && ($score / $maxScore) >= 0.6; // 60% تطابق على الأقل
    }

    /**
     * حساب نقاط التطابق
     */
    public function calculateMatchScore(BankTransaction $transaction): float
    {
        $score = 0;
        $maxScore = 0;

        // فحص نمط الوصف
        if ($this->description_pattern) {
            $maxScore += 40;
            if (preg_match('/' . $this->description_pattern . '/i', $transaction->description)) {
                $score += 40;
            }
        }

        // فحص نطاق المبلغ
        if ($this->amount_range_min || $this->amount_range_max) {
            $maxScore += 20;
            $amount = abs($transaction->amount);
            
            $minMatch = !$this->amount_range_min || $amount >= $this->amount_range_min;
            $maxMatch = !$this->amount_range_max || $amount <= $this->amount_range_max;
            
            if ($minMatch && $maxMatch) {
                $score += 20;
            }
        }

        // فحص نمط المورد
        if ($this->vendor_pattern) {
            $maxScore += 30;
            if (preg_match('/' . $this->vendor_pattern . '/i', $transaction->description)) {
                $score += 30;
            }
        }

        // فحص الفئة
        if ($this->category) {
            $maxScore += 10;
            // يمكن إضافة منطق فحص الفئة هنا
        }

        return $maxScore > 0 ? ($score / $maxScore) * 100 : 0;
    }

    /**
     * زيادة عداد الاستخدام
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        
        // تحديث نقاط الثقة بناءً على الاستخدام
        $this->updateConfidenceScore();
    }

    /**
     * تحديث نقاط الثقة
     */
    protected function updateConfidenceScore(): void
    {
        // كلما زاد الاستخدام، زادت الثقة
        $baseScore = 50;
        $usageBonus = min($this->usage_count * 2, 40); // حد أقصى 40 نقطة إضافية
        
        $this->update([
            'confidence_score' => min($baseScore + $usageBonus, 95) // حد أقصى 95%
        ]);
    }

    /**
     * إنشاء نمط جديد من المعاملة
     */
    public static function createFromTransaction(BankTransaction $transaction, string $category, int $accountId): self
    {
        return self::create([
            'pattern_name' => "نمط تلقائي - {$category}",
            'description_pattern' => self::extractPattern($transaction->description),
            'amount_range_min' => $transaction->amount * 0.8, // نطاق ±20%
            'amount_range_max' => $transaction->amount * 1.2,
            'category' => $category,
            'suggested_account_id' => $accountId,
            'confidence_score' => 60, // ثقة متوسطة للأنماط الجديدة
            'usage_count' => 1,
            'is_active' => true,
        ]);
    }

    /**
     * استخراج نمط من النص
     */
    protected static function extractPattern(string $description): string
    {
        // إزالة الأرقام والتواريخ
        $pattern = preg_replace('/\d+/', '', $description);
        
        // إزالة المسافات الزائدة
        $pattern = preg_replace('/\s+/', ' ', trim($pattern));
        
        // تحويل لنمط regex بسيط
        return preg_quote($pattern, '/');
    }

    /**
     * البحث في الأنماط
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('pattern_name', 'LIKE', "%{$search}%")
              ->orWhere('description_pattern', 'LIKE', "%{$search}%")
              ->orWhere('category', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة الأنماط النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * ترتيب حسب الثقة
     */
    public function scopeOrderByConfidence($query)
    {
        return $query->orderBy('confidence_score', 'desc');
    }

    /**
     * ترتيب حسب الاستخدام
     */
    public function scopeOrderByUsage($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    /**
     * الحصول على أفضل الأنماط
     */
    public function scopeTopPatterns($query, int $limit = 10)
    {
        return $query->active()
                    ->orderByConfidence()
                    ->orderByUsage()
                    ->limit($limit);
    }
}
