<?php

namespace App\Domains\CRM\Listeners;

use App\Domains\CRM\Events\CustomerCreated;
use App\Domains\CRM\Services\MarketingAutomationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * مستمع إرسال بريد الترحيب
 * Send Welcome Email Listener
 */
class SendWelcomeEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * خدمة التسويق الآلي
     */
    protected MarketingAutomationService $marketingService;

    /**
     * Create the event listener.
     */
    public function __construct(MarketingAutomationService $marketingService)
    {
        $this->marketingService = $marketingService;
    }

    /**
     * Handle the event.
     */
    public function handle(CustomerCreated $event): void
    {
        try {
            $customer = $event->customer;

            // التحقق من إعدادات الترحيب
            if (!config('crm.automation.welcome_email.enabled', true)) {
                return;
            }

            // التحقق من وجود بريد إلكتروني
            if (!$customer->email) {
                Log::info("Customer {$customer->id} has no email for welcome message");
                return;
            }

            // التحقق من عدم إرسال بريد ترحيب مسبقاً
            if ($customer->metadata['welcome_email_sent'] ?? false) {
                return;
            }

            // إرسال بريد الترحيب
            $this->marketingService->sendWelcomeEmail($customer);

            // تحديث حالة الإرسال
            $customer->update([
                'metadata' => array_merge($customer->metadata ?? [], [
                    'welcome_email_sent' => true,
                    'welcome_email_sent_at' => now(),
                ])
            ]);

            Log::info("Welcome email sent to customer {$customer->id}");

        } catch (\Exception $e) {
            Log::error("Failed to send welcome email to customer {$event->customer->id}: " . $e->getMessage());
            
            // إعادة المحاولة
            $this->release(60);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(CustomerCreated $event, \Throwable $exception): void
    {
        Log::error("Welcome email job failed for customer {$event->customer->id}: " . $exception->getMessage());
    }
}
