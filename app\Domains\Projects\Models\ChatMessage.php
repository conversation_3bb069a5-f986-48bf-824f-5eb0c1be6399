<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج رسالة المحادثة
 * يدعم أنواع مختلفة من الرسائل والتفاعلات
 */
class ChatMessage extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'chat_id',
        'sender_id',
        'parent_id',
        'content',
        'type',
        'attachments',
        'is_edited',
        'is_pinned',
        'is_system',
        'pinned_by',
        'pinned_at',
        'edited_at',
        'metadata',
    ];

    protected $casts = [
        'attachments' => 'array',
        'is_edited' => 'boolean',
        'is_pinned' => 'boolean',
        'is_system' => 'boolean',
        'pinned_at' => 'datetime',
        'edited_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المحادثة
     */
    public function chat(): BelongsTo
    {
        return $this->belongsTo(ProjectChat::class, 'chat_id');
    }

    /**
     * العلاقة مع المرسل
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'sender_id');
    }

    /**
     * العلاقة مع الرسالة الأب (للردود)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * العلاقة مع الردود
     */
    public function replies(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * العلاقة مع التفاعلات
     */
    public function reactions(): HasMany
    {
        return $this->hasMany(MessageReaction::class, 'message_id');
    }

    /**
     * العلاقة مع المستخدمين الذين قرأوا الرسالة
     */
    public function readBy(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Domains\HR\Models\Employee::class,
            'message_reads',
            'message_id',
            'employee_id'
        )->withTimestamps();
    }

    /**
     * الحصول على عدد التفاعلات
     */
    public function getReactionCountsAttribute(): array
    {
        return $this->reactions()
                    ->selectRaw('emoji, COUNT(*) as count')
                    ->groupBy('emoji')
                    ->pluck('count', 'emoji')
                    ->toArray();
    }

    /**
     * الحصول على عدد الردود
     */
    public function getRepliesCountAttribute(): int
    {
        return $this->replies()->count();
    }

    /**
     * التحقق من قراءة المستخدم للرسالة
     */
    public function isReadBy(int $userId): bool
    {
        return $this->readBy()->where('employee_id', $userId)->exists();
    }

    /**
     * تحديث محتوى الرسالة
     */
    public function updateContent(string $newContent, int $editedBy): void
    {
        $this->update([
            'content' => $newContent,
            'is_edited' => true,
            'edited_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'edit_history' => array_merge($this->metadata['edit_history'] ?? [], [
                    [
                        'edited_by' => $editedBy,
                        'edited_at' => now(),
                        'previous_content' => $this->getOriginal('content'),
                    ]
                ]),
                'mentions' => $this->extractMentions($newContent),
                'links' => $this->extractLinks($newContent),
            ]),
        ]);

        // إشعار المذكورين الجدد
        $this->notifyNewMentions();
    }

    /**
     * حذف الرسالة
     */
    public function deleteMessage(int $deletedBy): void
    {
        $this->update([
            'content' => '[تم حذف هذه الرسالة]',
            'type' => 'DELETED',
            'metadata' => array_merge($this->metadata ?? [], [
                'deleted_by' => $deletedBy,
                'deleted_at' => now(),
                'original_content' => $this->getOriginal('content'),
            ]),
        ]);
    }

    /**
     * إضافة تفاعل
     */
    public function addReaction(int $userId, string $emoji): MessageReaction
    {
        // إزالة التفاعل السابق للمستخدم إذا وجد
        $this->reactions()->where('user_id', $userId)->delete();

        return $this->reactions()->create([
            'user_id' => $userId,
            'emoji' => $emoji,
        ]);
    }

    /**
     * إزالة تفاعل
     */
    public function removeReaction(int $userId, string $emoji = null): void
    {
        $query = $this->reactions()->where('user_id', $userId);
        
        if ($emoji) {
            $query->where('emoji', $emoji);
        }
        
        $query->delete();
    }

    /**
     * تحديد الرسالة كمقروءة
     */
    public function markAsReadBy(int $userId): void
    {
        $this->readBy()->syncWithoutDetaching([$userId => ['created_at' => now()]]);
    }

    /**
     * الرد على الرسالة
     */
    public function reply(int $senderId, string $content, string $type = 'TEXT'): self
    {
        return self::create([
            'chat_id' => $this->chat_id,
            'parent_id' => $this->id,
            'sender_id' => $senderId,
            'content' => $content,
            'type' => $type,
            'metadata' => [
                'mentions' => $this->extractMentions($content),
                'links' => $this->extractLinks($content),
            ],
        ]);
    }

    /**
     * إرسال ملف
     */
    public static function sendFile(int $chatId, int $senderId, array $fileData): self
    {
        return self::create([
            'chat_id' => $chatId,
            'sender_id' => $senderId,
            'content' => $fileData['name'],
            'type' => 'FILE',
            'attachments' => [$fileData],
            'metadata' => [
                'file_type' => $fileData['type'],
                'file_size' => $fileData['size'],
            ],
        ]);
    }

    /**
     * إرسال صورة
     */
    public static function sendImage(int $chatId, int $senderId, array $imageData): self
    {
        return self::create([
            'chat_id' => $chatId,
            'sender_id' => $senderId,
            'content' => $imageData['name'],
            'type' => 'IMAGE',
            'attachments' => [$imageData],
            'metadata' => [
                'image_dimensions' => $imageData['dimensions'] ?? null,
                'file_size' => $imageData['size'],
            ],
        ]);
    }

    /**
     * مشاركة رابط
     */
    public static function shareLink(int $chatId, int $senderId, string $url, string $description = null): self
    {
        $linkPreview = self::generateLinkPreview($url);
        
        return self::create([
            'chat_id' => $chatId,
            'sender_id' => $senderId,
            'content' => $description ?? $url,
            'type' => 'LINK',
            'metadata' => [
                'url' => $url,
                'link_preview' => $linkPreview,
                'links' => [$url],
            ],
        ]);
    }

    /**
     * توليد معاينة الرابط
     */
    protected static function generateLinkPreview(string $url): array
    {
        try {
            // يمكن استخدام مكتبة مثل Embed لجلب معلومات الرابط
            return [
                'title' => 'عنوان الصفحة',
                'description' => 'وصف الصفحة',
                'image' => null,
                'domain' => parse_url($url, PHP_URL_HOST),
            ];
        } catch (\Exception $e) {
            return [
                'title' => $url,
                'description' => null,
                'image' => null,
                'domain' => parse_url($url, PHP_URL_HOST),
            ];
        }
    }

    /**
     * الحصول على تاريخ التحرير المنسق
     */
    public function getFormattedEditedAtAttribute(): ?string
    {
        return $this->edited_at ? $this->edited_at->diffForHumans() : null;
    }

    /**
     * التحقق من إمكانية تحرير الرسالة
     */
    public function canBeEditedBy(int $userId): bool
    {
        // يمكن للمرسل تحرير رسالته خلال 15 دقيقة
        return $this->sender_id === $userId && 
               $this->created_at->diffInMinutes(now()) <= 15 &&
               !$this->is_system;
    }

    /**
     * التحقق من إمكانية حذف الرسالة
     */
    public function canBeDeletedBy(int $userId): bool
    {
        // يمكن للمرسل أو مدير المشروع حذف الرسالة
        return $this->sender_id === $userId || 
               $this->chat->project->project_manager_id === $userId;
    }

    /**
     * استخراج الإشارات من النص
     */
    protected function extractMentions(string $content): array
    {
        preg_match_all('/@(\w+)/', $content, $matches);
        return $matches[1] ?? [];
    }

    /**
     * استخراج الروابط من النص
     */
    protected function extractLinks(string $content): array
    {
        preg_match_all('/https?:\/\/[^\s]+/', $content, $matches);
        return $matches[0] ?? [];
    }

    /**
     * إشعار المذكورين الجدد
     */
    protected function notifyNewMentions(): void
    {
        $newMentions = $this->metadata['mentions'] ?? [];
        $oldMentions = $this->metadata['edit_history'][0]['mentions'] ?? [];
        
        $addedMentions = array_diff($newMentions, $oldMentions);
        
        foreach ($addedMentions as $mention) {
            $employee = \App\Domains\HR\Models\Employee::where('username', $mention)->first();
            
            if ($employee && $employee->id !== $this->sender_id) {
                $employee->notify(new \App\Notifications\ChatMentionNotification($this, $this->chat));
            }
        }
    }

    /**
     * البحث في الرسائل
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('content', 'LIKE', "%{$search}%")
                    ->where('type', '!=', 'SYSTEM');
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة الرسائل المثبتة
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    /**
     * فلترة رسائل النظام
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * فلترة الرسائل العادية
     */
    public function scopeRegular($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * فلترة حسب المرسل
     */
    public function scopeFromSender($query, int $senderId)
    {
        return $query->where('sender_id', $senderId);
    }

    /**
     * فلترة الرسائل مع المرفقات
     */
    public function scopeWithAttachments($query)
    {
        return $query->whereNotNull('attachments')
                    ->whereJsonLength('attachments', '>', 0);
    }

    /**
     * ترتيب حسب التاريخ
     */
    public function scopeOrderByDate($query, string $direction = 'asc')
    {
        return $query->orderBy('created_at', $direction);
    }
}
