<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Exceptions\ECommerceTransformationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * خدمة تحويل البيانات من منصات التجارة الإلكترونية
 * تحول البيانات الخارجية إلى تنسيق النظام الداخلي
 */
class ECommerceDataTransformerService
{
    protected ECommerceMappingService $mapper;

    public function __construct(ECommerceMappingService $mapper)
    {
        $this->mapper = $mapper;
    }

    /**
     * تحويل بيانات المنتج
     */
    public function transformProduct(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            // تطبيق التخطيط الأساسي
            $mappedData = $this->mapper->mapProduct($externalData, $integration);
            
            // تطبيق التحويلات المخصصة
            $transformedData = $this->applyProductTransformations($mappedData, $integration);
            
            // تنظيف وتطبيع البيانات
            $normalizedData = $this->normalizeProductData($transformedData);
            
            // إضافة البيانات الوصفية
            $enrichedData = $this->enrichProductData($normalizedData, $integration);
            
            return $enrichedData;
            
        } catch (\Exception $e) {
            Log::error('Product transformation failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceTransformationException(
                'Failed to transform product data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تحويل بيانات الطلب
     */
    public function transformOrder(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            // تطبيق التخطيط الأساسي
            $mappedData = $this->mapper->mapOrder($externalData, $integration);
            
            // تطبيق التحويلات المخصصة
            $transformedData = $this->applyOrderTransformations($mappedData, $integration);
            
            // تنظيف وتطبيع البيانات
            $normalizedData = $this->normalizeOrderData($transformedData);
            
            // إضافة البيانات الوصفية
            $enrichedData = $this->enrichOrderData($normalizedData, $integration);
            
            return $enrichedData;
            
        } catch (\Exception $e) {
            Log::error('Order transformation failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceTransformationException(
                'Failed to transform order data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تحويل بيانات العميل
     */
    public function transformCustomer(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            // تطبيق التخطيط الأساسي
            $mappedData = $this->mapper->mapCustomer($externalData, $integration);
            
            // تطبيق التحويلات المخصصة
            $transformedData = $this->applyCustomerTransformations($mappedData, $integration);
            
            // تنظيف وتطبيع البيانات
            $normalizedData = $this->normalizeCustomerData($transformedData);
            
            // إضافة البيانات الوصفية
            $enrichedData = $this->enrichCustomerData($normalizedData, $integration);
            
            return $enrichedData;
            
        } catch (\Exception $e) {
            Log::error('Customer transformation failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceTransformationException(
                'Failed to transform customer data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تحويل بيانات عنصر الطلب
     */
    public function transformOrderItem(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            // تطبيق التخطيط الأساسي
            $mappedData = $this->mapper->mapOrderItem($externalData, $integration);
            
            // تطبيق التحويلات المخصصة
            $transformedData = $this->applyOrderItemTransformations($mappedData, $integration);
            
            // تنظيف وتطبيع البيانات
            $normalizedData = $this->normalizeOrderItemData($transformedData);
            
            return $normalizedData;
            
        } catch (\Exception $e) {
            Log::error('Order item transformation failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceTransformationException(
                'Failed to transform order item data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تطبيق تحويلات المنتج المخصصة
     */
    protected function applyProductTransformations(array $data, ECommerceIntegration $integration): array
    {
        $transformationRules = $integration->transformation_rules['products'] ?? [];
        
        foreach ($transformationRules as $rule) {
            $data = $this->applyTransformationRule($data, $rule);
        }
        
        return $data;
    }

    /**
     * تطبيق تحويلات الطلب المخصصة
     */
    protected function applyOrderTransformations(array $data, ECommerceIntegration $integration): array
    {
        $transformationRules = $integration->transformation_rules['orders'] ?? [];
        
        foreach ($transformationRules as $rule) {
            $data = $this->applyTransformationRule($data, $rule);
        }
        
        return $data;
    }

    /**
     * تطبيق تحويلات العميل المخصصة
     */
    protected function applyCustomerTransformations(array $data, ECommerceIntegration $integration): array
    {
        $transformationRules = $integration->transformation_rules['customers'] ?? [];
        
        foreach ($transformationRules as $rule) {
            $data = $this->applyTransformationRule($data, $rule);
        }
        
        return $data;
    }

    /**
     * تطبيق تحويلات عنصر الطلب المخصصة
     */
    protected function applyOrderItemTransformations(array $data, ECommerceIntegration $integration): array
    {
        $transformationRules = $integration->transformation_rules['order_items'] ?? [];
        
        foreach ($transformationRules as $rule) {
            $data = $this->applyTransformationRule($data, $rule);
        }
        
        return $data;
    }

    /**
     * تطبيق قاعدة تحويل
     */
    protected function applyTransformationRule(array $data, array $rule): array
    {
        $type = $rule['type'] ?? null;
        $field = $rule['field'] ?? null;
        $value = $rule['value'] ?? null;
        $condition = $rule['condition'] ?? null;

        if (!$field) {
            return $data;
        }

        // تحقق من الشرط إذا كان موجوداً
        if ($condition && !$this->evaluateCondition($data, $condition)) {
            return $data;
        }

        switch ($type) {
            case 'set_value':
                $data[$field] = $value;
                break;
                
            case 'set_default':
                if (!isset($data[$field]) || $data[$field] === null || $data[$field] === '') {
                    $data[$field] = $value;
                }
                break;
                
            case 'format_string':
                if (isset($data[$field])) {
                    $data[$field] = $this->formatString($data[$field], $value);
                }
                break;
                
            case 'format_number':
                if (isset($data[$field])) {
                    $data[$field] = $this->formatNumber($data[$field], $value);
                }
                break;
                
            case 'format_date':
                if (isset($data[$field])) {
                    $data[$field] = $this->formatDate($data[$field], $value);
                }
                break;
                
            case 'convert_currency':
                if (isset($data[$field])) {
                    $data[$field] = $this->convertCurrency($data[$field], $value);
                }
                break;
                
            case 'map_value':
                if (isset($data[$field])) {
                    $data[$field] = $this->mapValue($data[$field], $value);
                }
                break;
                
            case 'concatenate':
                $data[$field] = $this->concatenateFields($data, $value);
                break;
                
            case 'extract':
                if (isset($data[$field])) {
                    $data[$field] = $this->extractValue($data[$field], $value);
                }
                break;
                
            case 'calculate':
                $data[$field] = $this->calculateValue($data, $value);
                break;
        }

        return $data;
    }

    /**
     * تقييم شرط
     */
    protected function evaluateCondition(array $data, array $condition): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field || !isset($data[$field])) {
            return false;
        }

        $fieldValue = $data[$field];

        switch ($operator) {
            case '=':
                return $fieldValue == $value;
            case '!=':
                return $fieldValue != $value;
            case '>':
                return $fieldValue > $value;
            case '>=':
                return $fieldValue >= $value;
            case '<':
                return $fieldValue < $value;
            case '<=':
                return $fieldValue <= $value;
            case 'in':
                return in_array($fieldValue, (array) $value);
            case 'not_in':
                return !in_array($fieldValue, (array) $value);
            case 'contains':
                return str_contains($fieldValue, $value);
            case 'starts_with':
                return str_starts_with($fieldValue, $value);
            case 'ends_with':
                return str_ends_with($fieldValue, $value);
            case 'regex':
                return preg_match($value, $fieldValue);
            default:
                return true;
        }
    }

    /**
     * تنسيق النص
     */
    protected function formatString(string $value, array $options): string
    {
        $format = $options['format'] ?? null;

        switch ($format) {
            case 'uppercase':
                return strtoupper($value);
            case 'lowercase':
                return strtolower($value);
            case 'title':
                return ucwords($value);
            case 'slug':
                return Str::slug($value);
            case 'trim':
                return trim($value);
            case 'strip_tags':
                return strip_tags($value);
            case 'clean_html':
                return strip_tags(html_entity_decode($value));
            default:
                return $value;
        }
    }

    /**
     * تنسيق الرقم
     */
    protected function formatNumber($value, array $options): float
    {
        $decimals = $options['decimals'] ?? 2;
        $multiplier = $options['multiplier'] ?? 1;
        $divider = $options['divider'] ?? 1;

        $numericValue = is_numeric($value) ? (float) $value : 0;
        $result = ($numericValue * $multiplier) / $divider;

        return round($result, $decimals);
    }

    /**
     * تنسيق التاريخ
     */
    protected function formatDate($value, array $options): ?string
    {
        if (!$value) {
            return null;
        }

        $inputFormat = $options['input_format'] ?? null;
        $outputFormat = $options['output_format'] ?? 'Y-m-d H:i:s';

        try {
            if ($inputFormat) {
                $date = Carbon::createFromFormat($inputFormat, $value);
            } else {
                $date = Carbon::parse($value);
            }

            return $date->format($outputFormat);
        } catch (\Exception $e) {
            Log::warning('Date formatting failed', [
                'value' => $value,
                'options' => $options,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * تحويل العملة
     */
    protected function convertCurrency($value, array $options): float
    {
        $fromCurrency = $options['from'] ?? 'USD';
        $toCurrency = $options['to'] ?? 'USD';
        $rate = $options['rate'] ?? 1;

        if ($fromCurrency === $toCurrency) {
            return (float) $value;
        }

        return (float) $value * $rate;
    }

    /**
     * تخطيط القيمة
     */
    protected function mapValue($value, array $mapping): mixed
    {
        return $mapping[$value] ?? $value;
    }

    /**
     * دمج الحقول
     */
    protected function concatenateFields(array $data, array $options): string
    {
        $fields = $options['fields'] ?? [];
        $separator = $options['separator'] ?? ' ';
        $values = [];

        foreach ($fields as $field) {
            if (isset($data[$field]) && $data[$field] !== null && $data[$field] !== '') {
                $values[] = $data[$field];
            }
        }

        return implode($separator, $values);
    }

    /**
     * استخراج القيمة
     */
    protected function extractValue(string $value, array $options): ?string
    {
        $pattern = $options['pattern'] ?? null;
        $index = $options['index'] ?? 0;

        if (!$pattern) {
            return $value;
        }

        if (preg_match_all($pattern, $value, $matches)) {
            return $matches[$index][0] ?? null;
        }

        return null;
    }

    /**
     * حساب القيمة
     */
    protected function calculateValue(array $data, array $options): float
    {
        $expression = $options['expression'] ?? '';
        $variables = $options['variables'] ?? [];

        // استبدال المتغيرات في التعبير
        foreach ($variables as $variable => $field) {
            if (isset($data[$field])) {
                $value = is_numeric($data[$field]) ? $data[$field] : 0;
                $expression = str_replace("{{$variable}}", $value, $expression);
            }
        }

        // تقييم التعبير الرياضي (بشكل آمن)
        try {
            return eval("return $expression;");
        } catch (\Exception $e) {
            Log::warning('Calculation failed', [
                'expression' => $expression,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * تطبيع بيانات المنتج
     */
    protected function normalizeProductData(array $data): array
    {
        // تنظيف النصوص
        if (isset($data['name'])) {
            $data['name'] = trim(strip_tags($data['name']));
        }

        if (isset($data['description'])) {
            $data['description'] = trim($data['description']);
        }

        // تطبيع الأسعار
        if (isset($data['price'])) {
            $data['price'] = max(0, (float) $data['price']);
        }

        if (isset($data['sale_price'])) {
            $data['sale_price'] = max(0, (float) $data['sale_price']);
        }

        // تطبيع الكميات
        if (isset($data['inventory_quantity'])) {
            $data['inventory_quantity'] = max(0, (int) $data['inventory_quantity']);
        }

        // تطبيع الوزن
        if (isset($data['weight'])) {
            $data['weight'] = max(0, (float) $data['weight']);
        }

        return $data;
    }

    /**
     * تطبيع بيانات الطلب
     */
    protected function normalizeOrderData(array $data): array
    {
        // تطبيع المبالغ
        $amountFields = ['subtotal', 'total', 'total_tax', 'shipping_total', 'discount_total'];
        
        foreach ($amountFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = max(0, (float) $data[$field]);
            }
        }

        // تطبيع الكميات
        if (isset($data['item_count'])) {
            $data['item_count'] = max(0, (int) $data['item_count']);
        }

        if (isset($data['quantity_total'])) {
            $data['quantity_total'] = max(0, (int) $data['quantity_total']);
        }

        return $data;
    }

    /**
     * تطبيع بيانات العميل
     */
    protected function normalizeCustomerData(array $data): array
    {
        // تنظيف النصوص
        if (isset($data['first_name'])) {
            $data['first_name'] = trim($data['first_name']);
        }

        if (isset($data['last_name'])) {
            $data['last_name'] = trim($data['last_name']);
        }

        // تطبيع البريد الإلكتروني
        if (isset($data['email'])) {
            $data['email'] = strtolower(trim($data['email']));
        }

        // تطبيع رقم الهاتف
        if (isset($data['phone'])) {
            $data['phone'] = preg_replace('/[^0-9+]/', '', $data['phone']);
        }

        return $data;
    }

    /**
     * تطبيع بيانات عنصر الطلب
     */
    protected function normalizeOrderItemData(array $data): array
    {
        // تطبيع الكمية
        if (isset($data['quantity'])) {
            $data['quantity'] = max(1, (int) $data['quantity']);
        }

        // تطبيع الأسعار
        if (isset($data['price'])) {
            $data['price'] = max(0, (float) $data['price']);
        }

        if (isset($data['total'])) {
            $data['total'] = max(0, (float) $data['total']);
        }

        return $data;
    }

    /**
     * إثراء بيانات المنتج
     */
    protected function enrichProductData(array $data, ECommerceIntegration $integration): array
    {
        // إضافة slug إذا لم يكن موجوداً
        if (!isset($data['slug']) && isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // حساب الخصم
        if (isset($data['price']) && isset($data['sale_price']) && $data['sale_price'] > 0) {
            $data['discount_amount'] = $data['price'] - $data['sale_price'];
            $data['discount_percentage'] = ($data['discount_amount'] / $data['price']) * 100;
        }

        return $data;
    }

    /**
     * إثراء بيانات الطلب
     */
    protected function enrichOrderData(array $data, ECommerceIntegration $integration): array
    {
        // حساب متوسط قيمة العنصر
        if (isset($data['total']) && isset($data['item_count']) && $data['item_count'] > 0) {
            $data['average_item_value'] = $data['total'] / $data['item_count'];
        }

        return $data;
    }

    /**
     * إثراء بيانات العميل
     */
    protected function enrichCustomerData(array $data, ECommerceIntegration $integration): array
    {
        // إنشاء اسم العرض
        if (!isset($data['display_name'])) {
            if (isset($data['first_name']) && isset($data['last_name'])) {
                $data['display_name'] = trim($data['first_name'] . ' ' . $data['last_name']);
            } elseif (isset($data['first_name'])) {
                $data['display_name'] = $data['first_name'];
            } elseif (isset($data['email'])) {
                $data['display_name'] = $data['email'];
            }
        }

        return $data;
    }
}
