<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use App\Domains\Accounting\Requests\StoreAccountRequest;
use App\Domains\Accounting\Requests\UpdateAccountRequest;
use App\Domains\Accounting\Resources\AccountResource;
use App\Domains\Accounting\Resources\AccountCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * Account Controller
 * تحكم في الحسابات المحاسبية
 */
class AccountController extends Controller
{
    protected AdvancedAccountingEngine $accountingEngine;

    public function __construct(AdvancedAccountingEngine $accountingEngine)
    {
        $this->accountingEngine = $accountingEngine;
        $this->middleware('auth');
    }

    /**
     * عرض قائمة الحسابات
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Account::class);

        $query = Account::with(['parentAccount', 'subAccounts'])
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('account_name', 'like', "%{$search}%")
                          ->orWhere('account_code', 'like', "%{$search}%")
                          ->orWhere('account_name_en', 'like', "%{$search}%");
                });
            })
            ->when($request->type, function ($q, $type) {
                $q->where('account_type', $type);
            })
            ->when($request->category, function ($q, $category) {
                $q->where('account_category', $category);
            })
            ->when($request->active !== null, function ($q) use ($request) {
                $q->where('is_active', $request->boolean('active'));
            })
            ->when($request->parent_id, function ($q, $parentId) {
                if ($parentId === 'null') {
                    $q->whereNull('parent_account_id');
                } else {
                    $q->where('parent_account_id', $parentId);
                }
            });

        if ($request->tree) {
            $accounts = $query->whereNull('parent_account_id')
                             ->orderBy('account_code')
                             ->get();
            return response()->json([
                'success' => true,
                'data' => AccountResource::collection($accounts),
                'message' => 'تم جلب شجرة الحسابات بنجاح'
            ]);
        }

        $accounts = $query->orderBy('account_code')
                         ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new AccountCollection($accounts),
            'message' => 'تم جلب الحسابات بنجاح'
        ]);
    }

    /**
     * إنشاء حساب جديد
     */
    public function store(StoreAccountRequest $request): JsonResponse
    {
        $this->authorize('create', Account::class);

        DB::beginTransaction();
        try {
            $account = Account::create([
                ...$request->validated(),
                'created_by' => auth()->id(),
                'level' => $this->calculateAccountLevel($request->parent_account_id),
            ]);

            // تحديث الرصيد الافتتاحي إذا تم تحديده
            if ($request->opening_balance) {
                $account->updateCurrentBalance();
            }

            DB::commit();

            // مسح الكاش
            Cache::tags(['accounts'])->flush();

            return response()->json([
                'success' => true,
                'data' => new AccountResource($account->load(['parentAccount', 'subAccounts'])),
                'message' => 'تم إنشاء الحساب بنجاح'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الحساب: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل حساب محدد
     */
    public function show(Account $account): JsonResponse
    {
        $this->authorize('view', $account);

        $account->load([
            'parentAccount',
            'subAccounts',
            'journalEntryDetails.journalEntry',
            'creator',
            'updater'
        ]);

        return response()->json([
            'success' => true,
            'data' => new AccountResource($account),
            'message' => 'تم جلب تفاصيل الحساب بنجاح'
        ]);
    }

    /**
     * تحديث حساب
     */
    public function update(UpdateAccountRequest $request, Account $account): JsonResponse
    {
        $this->authorize('update', $account);

        DB::beginTransaction();
        try {
            $account->update([
                ...$request->validated(),
                'updated_by' => auth()->id(),
            ]);

            // إعادة حساب المستوى إذا تم تغيير الحساب الأب
            if ($request->has('parent_account_id')) {
                $account->update([
                    'level' => $this->calculateAccountLevel($request->parent_account_id)
                ]);
            }

            DB::commit();

            // مسح الكاش
            Cache::tags(['accounts'])->flush();

            return response()->json([
                'success' => true,
                'data' => new AccountResource($account->load(['parentAccount', 'subAccounts'])),
                'message' => 'تم تحديث الحساب بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الحساب: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف حساب
     */
    public function destroy(Account $account): JsonResponse
    {
        $this->authorize('delete', $account);

        // التحقق من وجود معاملات
        if ($account->journalEntryDetails()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الحساب لوجود معاملات مرتبطة به'
            ], 422);
        }

        // التحقق من وجود حسابات فرعية
        if ($account->subAccounts()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الحساب لوجود حسابات فرعية'
            ], 422);
        }

        $account->delete();

        // مسح الكاش
        Cache::tags(['accounts'])->flush();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الحساب بنجاح'
        ]);
    }

    /**
     * الحصول على رصيد الحساب
     */
    public function getAccountBalance(Account $account): JsonResponse
    {
        $this->authorize('view', $account);

        $balance = $account->calculateCurrentBalance();
        $account->updateCurrentBalance();

        return response()->json([
            'success' => true,
            'data' => [
                'account_id' => $account->id,
                'account_name' => $account->account_name,
                'account_code' => $account->account_code,
                'opening_balance' => $account->opening_balance,
                'current_balance' => $balance,
                'debit_balance' => $account->is_debit_nature && $balance > 0 ? $balance : 0,
                'credit_balance' => $account->is_credit_nature && $balance > 0 ? $balance : 0,
                'currency' => $account->currency_code,
                'last_updated' => now(),
            ],
            'message' => 'تم جلب رصيد الحساب بنجاح'
        ]);
    }

    /**
     * الحصول على تاريخ الحساب
     */
    public function getAccountHistory(Request $request, Account $account): JsonResponse
    {
        $this->authorize('viewHistory', $account);

        $dateFrom = $request->date_from;
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $history = $account->getAccountHistory($dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => [
                'account' => [
                    'id' => $account->id,
                    'name' => $account->account_name,
                    'code' => $account->account_code,
                ],
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'opening_balance' => $account->opening_balance,
                'transactions' => $history,
                'closing_balance' => end($history)['balance'] ?? $account->opening_balance,
            ],
            'message' => 'تم جلب تاريخ الحساب بنجاح'
        ]);
    }

    /**
     * تعديل رصيد الحساب
     */
    public function adjustBalance(Request $request, Account $account): JsonResponse
    {
        $this->authorize('adjustBalance', $account);

        $request->validate([
            'adjustment_amount' => 'required|numeric',
            'adjustment_type' => 'required|in:debit,credit',
            'description' => 'required|string|max:255',
            'reference' => 'nullable|string|max:100',
        ]);

        DB::beginTransaction();
        try {
            // إنشاء قيد تعديل الرصيد
            $entryData = [
                'entry_date' => now(),
                'description' => $request->description,
                'reference' => $request->reference,
                'source_type' => 'BALANCE_ADJUSTMENT',
                'lines' => [
                    [
                        'account_id' => $account->id,
                        'debit_amount' => $request->adjustment_type === 'debit' ? $request->adjustment_amount : 0,
                        'credit_amount' => $request->adjustment_type === 'credit' ? $request->adjustment_amount : 0,
                        'description' => $request->description,
                    ],
                    [
                        'account_id' => $this->getAdjustmentAccount(),
                        'debit_amount' => $request->adjustment_type === 'credit' ? $request->adjustment_amount : 0,
                        'credit_amount' => $request->adjustment_type === 'debit' ? $request->adjustment_amount : 0,
                        'description' => 'تعديل رصيد - ' . $account->account_name,
                    ],
                ],
            ];

            $entry = $this->accountingEngine->createJournalEntry($entryData);
            $account->updateCurrentBalance();

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'journal_entry' => $entry,
                    'new_balance' => $account->fresh()->current_balance,
                ],
                'message' => 'تم تعديل رصيد الحساب بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعديل الرصيد: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على دليل الحسابات
     */
    public function getChartOfAccounts(): JsonResponse
    {
        $this->authorize('viewAny', Account::class);

        $accounts = Cache::tags(['accounts'])->remember('chart_of_accounts', 3600, function () {
            return Account::with(['subAccounts' => function ($query) {
                $query->orderBy('account_code');
            }])
            ->whereNull('parent_account_id')
            ->orderBy('account_code')
            ->get();
        });

        return response()->json([
            'success' => true,
            'data' => AccountResource::collection($accounts),
            'message' => 'تم جلب دليل الحسابات بنجاح'
        ]);
    }

    /**
     * الحصول على ميزان المراجعة
     */
    public function getTrialBalance(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $accounts = Account::with(['journalEntryDetails' => function ($query) use ($dateFrom, $dateTo) {
            $query->whereHas('journalEntry', function ($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('entry_date', [$dateFrom, $dateTo])
                  ->where('status', 'POSTED');
            });
        }])
        ->where('allow_manual_entry', true)
        ->orderBy('account_code')
        ->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $debits = $account->journalEntryDetails->where('entry_type', 'debit')->sum('amount');
            $credits = $account->journalEntryDetails->where('entry_type', 'credit')->sum('amount');
            
            if ($debits > 0 || $credits > 0) {
                $trialBalance[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'debit_amount' => $debits,
                    'credit_amount' => $credits,
                ];
                
                $totalDebits += $debits;
                $totalCredits += $credits;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'period' => ['from' => $dateFrom, 'to' => $dateTo],
                'accounts' => $trialBalance,
                'totals' => [
                    'total_debits' => $totalDebits,
                    'total_credits' => $totalCredits,
                    'difference' => $totalDebits - $totalCredits,
                    'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
                ],
            ],
            'message' => 'تم إنشاء ميزان المراجعة بنجاح'
        ]);
    }

    /**
     * حساب مستوى الحساب
     */
    protected function calculateAccountLevel(?int $parentAccountId): int
    {
        if (!$parentAccountId) {
            return 1;
        }

        $parentAccount = Account::find($parentAccountId);
        return $parentAccount ? $parentAccount->level + 1 : 1;
    }

    /**
     * الحصول على حساب التعديل
     */
    protected function getAdjustmentAccount(): int
    {
        // يجب إنشاء حساب خاص بتعديلات الأرصدة
        $adjustmentAccount = Account::where('account_code', '9999')->first();
        
        if (!$adjustmentAccount) {
            $adjustmentAccount = Account::create([
                'account_code' => '9999',
                'account_name' => 'تعديلات الأرصدة',
                'account_name_en' => 'Balance Adjustments',
                'account_type' => 'equity',
                'account_category' => 'retained_earnings',
                'is_system_account' => true,
                'allow_manual_entry' => false,
                'created_by' => auth()->id(),
            ]);
        }

        return $adjustmentAccount->id;
    }
}
