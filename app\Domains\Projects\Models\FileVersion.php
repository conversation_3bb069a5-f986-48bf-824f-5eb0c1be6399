<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج إصدار الملف - File Version
 */
class FileVersion extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'file_id',
        'version_number',
        'file_path',
        'file_size',
        'created_by',
        'notes',
        'is_current',
    ];

    protected $casts = [
        'version_number' => 'integer',
        'file_size' => 'integer',
        'is_current' => 'boolean',
    ];

    public function file(): BelongsTo
    {
        return $this->belongsTo(ProjectFile::class, 'file_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    public function duplicate(int $newFileId): self
    {
        return self::create([
            'file_id' => $newFileId,
            'version_number' => $this->version_number,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'created_by' => $this->created_by,
            'notes' => $this->notes,
            'is_current' => $this->is_current,
        ]);
    }
}
