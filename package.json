{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.8.3", "vite": "^7.0.4"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@inertiajs/vue3": "^2.0.17", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "ziggy-js": "^2.5.3"}}