<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قالب المهمة - Task Template
 */
class TaskTemplate extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'template_id',
        'name',
        'description',
        'type',
        'priority',
        'estimated_hours',
        'story_points',
        'dependencies',
        'assignee_role',
        'position',
        'is_milestone',
        'template_data',
    ];

    protected $casts = [
        'estimated_hours' => 'decimal:2',
        'story_points' => 'integer',
        'dependencies' => 'array',
        'position' => 'integer',
        'is_milestone' => 'boolean',
        'template_data' => 'array',
    ];

    public function template(): BelongsTo
    {
        return $this->belongsTo(ProjectTemplate::class, 'template_id');
    }

    public function duplicate(int $newTemplateId): self
    {
        return self::create([
            'template_id' => $newTemplateId,
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type,
            'priority' => $this->priority,
            'estimated_hours' => $this->estimated_hours,
            'story_points' => $this->story_points,
            'dependencies' => $this->dependencies,
            'assignee_role' => $this->assignee_role,
            'position' => $this->position,
            'is_milestone' => $this->is_milestone,
            'template_data' => $this->template_data,
        ]);
    }
}
