<?php

namespace App\Domains\Payments\Services;

use App\Domains\Payments\Services\PaymentService;
use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\DigitalWallet;
use App\Domains\Payments\Models\MCPAgent;
use App\Domains\Payments\Models\MCPPaymentAuthorization;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * خدمة MCP للمدفوعات
 * تمكن الوكلاء الذكية من إجراء المدفوعات نيابة عن المستخدمين
 */
class MCPPaymentService
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * تسجيل وكيل ذكي جديد
     */
    public function registerAgent(array $agentData): MCPAgent
    {
        $agent = MCPAgent::create([
            'agent_id' => 'MCP_' . strtoupper(Str::random(12)),
            'name' => $agentData['name'],
            'description' => $agentData['description'],
            'capabilities' => $agentData['capabilities'] ?? [],
            'api_key' => $this->generateApiKey(),
            'webhook_url' => $agentData['webhook_url'] ?? null,
            'status' => 'active',
            'created_by' => $agentData['created_by'] ?? null,
            'metadata' => $agentData['metadata'] ?? [],
        ]);

        Log::info('تم تسجيل وكيل ذكي جديد', [
            'agent_id' => $agent->agent_id,
            'name' => $agent->name,
        ]);

        return $agent;
    }

    /**
     * طلب تفويض للدفع من وكيل ذكي
     */
    public function requestPaymentAuthorization(
        MCPAgent $agent,
        User $user,
        array $paymentData,
        array $authorizationRules = []
    ): MCPPaymentAuthorization {
        // التحقق من صلاحيات الوكيل
        $this->validateAgentCapabilities($agent, 'payment_processing');

        // إنشاء طلب تفويض
        $authorization = MCPPaymentAuthorization::create([
            'authorization_id' => 'AUTH_' . strtoupper(Str::random(12)),
            'agent_id' => $agent->id,
            'user_id' => $user->id,
            'payment_data' => $paymentData,
            'authorization_rules' => $authorizationRules,
            'status' => 'pending',
            'expires_at' => now()->addMinutes($authorizationRules['expiry_minutes'] ?? 30),
            'requested_at' => now(),
        ]);

        // إرسال إشعار للمستخدم
        $this->notifyUserForAuthorization($user, $authorization);

        Log::info('تم طلب تفويض دفع من وكيل ذكي', [
            'authorization_id' => $authorization->authorization_id,
            'agent_id' => $agent->agent_id,
            'user_id' => $user->id,
            'amount' => $paymentData['amount'] ?? 'غير محدد',
        ]);

        return $authorization;
    }

    /**
     * معالجة تفويض المستخدم
     */
    public function processUserAuthorization(
        MCPPaymentAuthorization $authorization,
        bool $approved,
        array $userConditions = []
    ): array {
        if ($authorization->isExpired()) {
            throw new \Exception('انتهت صلاحية طلب التفويض');
        }

        if ($approved) {
            $authorization->approve($userConditions);
            
            // تنفيذ الدفع تلقائياً إذا كان مفوضاً
            if ($authorization->auto_execute) {
                return $this->executeAuthorizedPayment($authorization);
            }
            
            return [
                'success' => true,
                'message' => 'تم الموافقة على التفويض',
                'authorization' => $authorization,
                'requires_execution' => true,
            ];
        } else {
            $authorization->reject();
            
            return [
                'success' => false,
                'message' => 'تم رفض التفويض',
                'authorization' => $authorization,
            ];
        }
    }

    /**
     * تنفيذ دفعة مفوضة
     */
    public function executeAuthorizedPayment(MCPPaymentAuthorization $authorization): array
    {
        try {
            // التحقق من صحة التفويض
            if (!$authorization->isApproved()) {
                throw new \Exception('التفويض غير مفعل');
            }

            if ($authorization->isExpired()) {
                throw new \Exception('انتهت صلاحية التفويض');
            }

            // التحقق من حدود التفويض
            $this->validateAuthorizationLimits($authorization);

            // تحضير بيانات الدفع
            $paymentData = $this->preparePaymentData($authorization);

            // تنفيذ الدفع
            $transaction = $this->paymentService->processPayment($paymentData);

            // تحديث التفويض
            $authorization->update([
                'status' => 'executed',
                'transaction_id' => $transaction->id,
                'executed_at' => now(),
            ]);

            // إشعار الوكيل بالنتيجة
            $this->notifyAgent($authorization->agent, 'payment_executed', [
                'authorization_id' => $authorization->authorization_id,
                'transaction_id' => $transaction->transaction_id,
                'status' => $transaction->status,
            ]);

            Log::info('تم تنفيذ دفعة مفوضة بنجاح', [
                'authorization_id' => $authorization->authorization_id,
                'transaction_id' => $transaction->transaction_id,
            ]);

            return [
                'success' => true,
                'message' => 'تم تنفيذ الدفع بنجاح',
                'transaction' => $transaction,
                'authorization' => $authorization,
            ];

        } catch (\Exception $e) {
            // تحديث التفويض بالفشل
            $authorization->update([
                'status' => 'failed',
                'failure_reason' => $e->getMessage(),
                'failed_at' => now(),
            ]);

            // إشعار الوكيل بالفشل
            $this->notifyAgent($authorization->agent, 'payment_failed', [
                'authorization_id' => $authorization->authorization_id,
                'error' => $e->getMessage(),
            ]);

            Log::error('فشل في تنفيذ دفعة مفوضة', [
                'authorization_id' => $authorization->authorization_id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'فشل في تنفيذ الدفع: ' . $e->getMessage(),
                'authorization' => $authorization,
            ];
        }
    }

    /**
     * معالجة طلب دفع من وكيل ذكي
     */
    public function processAgentPaymentRequest(
        string $agentApiKey,
        array $paymentRequest
    ): array {
        try {
            // التحقق من الوكيل
            $agent = $this->authenticateAgent($agentApiKey);
            
            // التحقق من المستخدم
            $user = User::findOrFail($paymentRequest['user_id']);
            
            // التحقق من وجود تفويض مسبق
            $existingAuth = $this->checkExistingAuthorization($agent, $user, $paymentRequest);
            
            if ($existingAuth && $existingAuth->isValid()) {
                // تنفيذ الدفع مباشرة
                return $this->executeAuthorizedPayment($existingAuth);
            }
            
            // طلب تفويض جديد
            $authorization = $this->requestPaymentAuthorization(
                $agent,
                $user,
                $paymentRequest,
                $paymentRequest['authorization_rules'] ?? []
            );
            
            return [
                'success' => true,
                'message' => 'تم إرسال طلب التفويض للمستخدم',
                'authorization_id' => $authorization->authorization_id,
                'requires_user_approval' => true,
            ];
            
        } catch (\Exception $e) {
            Log::error('فشل في معالجة طلب دفع من وكيل ذكي', [
                'agent_api_key' => substr($agentApiKey, 0, 8) . '...',
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'message' => 'فشل في معالجة الطلب: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * إنشاء تفويض دائم للوكيل
     */
    public function createPermanentAuthorization(
        MCPAgent $agent,
        User $user,
        array $authorizationRules
    ): MCPPaymentAuthorization {
        return MCPPaymentAuthorization::create([
            'authorization_id' => 'PERM_' . strtoupper(Str::random(12)),
            'agent_id' => $agent->id,
            'user_id' => $user->id,
            'payment_data' => [],
            'authorization_rules' => array_merge($authorizationRules, [
                'permanent' => true,
                'max_amount_per_transaction' => $authorizationRules['max_amount_per_transaction'] ?? 100,
                'max_amount_per_day' => $authorizationRules['max_amount_per_day'] ?? 1000,
                'allowed_payment_methods' => $authorizationRules['allowed_payment_methods'] ?? ['digital_wallet'],
            ]),
            'status' => 'approved',
            'auto_execute' => true,
            'approved_at' => now(),
        ]);
    }

    /**
     * الحصول على إحصائيات الوكيل
     */
    public function getAgentStatistics(MCPAgent $agent, array $filters = []): array
    {
        $query = MCPPaymentAuthorization::where('agent_id', $agent->id);
        
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }
        
        $totalRequests = $query->count();
        $approvedRequests = $query->clone()->where('status', 'approved')->count();
        $executedRequests = $query->clone()->where('status', 'executed')->count();
        $rejectedRequests = $query->clone()->where('status', 'rejected')->count();
        
        $totalAmount = $query->clone()
            ->where('status', 'executed')
            ->join('payment_transactions', 'mcp_payment_authorizations.transaction_id', '=', 'payment_transactions.id')
            ->sum('payment_transactions.amount');
        
        return [
            'agent_id' => $agent->agent_id,
            'agent_name' => $agent->name,
            'total_requests' => $totalRequests,
            'approved_requests' => $approvedRequests,
            'executed_requests' => $executedRequests,
            'rejected_requests' => $rejectedRequests,
            'approval_rate' => $totalRequests > 0 ? round(($approvedRequests / $totalRequests) * 100, 2) : 0,
            'execution_rate' => $approvedRequests > 0 ? round(($executedRequests / $approvedRequests) * 100, 2) : 0,
            'total_amount_processed' => $totalAmount,
            'average_transaction_amount' => $executedRequests > 0 ? round($totalAmount / $executedRequests, 2) : 0,
        ];
    }

    /**
     * التحقق من صلاحيات الوكيل
     */
    protected function validateAgentCapabilities(MCPAgent $agent, string $capability): void
    {
        if (!$agent->hasCapability($capability)) {
            throw new \Exception("الوكيل لا يملك صلاحية: {$capability}");
        }
        
        if (!$agent->isActive()) {
            throw new \Exception('الوكيل غير نشط');
        }
    }

    /**
     * التحقق من الوكيل
     */
    protected function authenticateAgent(string $apiKey): MCPAgent
    {
        $agent = MCPAgent::where('api_key', $apiKey)->first();
        
        if (!$agent) {
            throw new \Exception('مفتاح API غير صحيح');
        }
        
        if (!$agent->isActive()) {
            throw new \Exception('الوكيل غير نشط');
        }
        
        return $agent;
    }

    /**
     * التحقق من وجود تفويض مسبق
     */
    protected function checkExistingAuthorization(
        MCPAgent $agent,
        User $user,
        array $paymentRequest
    ): ?MCPPaymentAuthorization {
        return MCPPaymentAuthorization::where('agent_id', $agent->id)
            ->where('user_id', $user->id)
            ->where('status', 'approved')
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->whereJsonPath('authorization_rules->permanent', true)
            ->first();
    }

    /**
     * التحقق من حدود التفويض
     */
    protected function validateAuthorizationLimits(MCPPaymentAuthorization $authorization): void
    {
        $rules = $authorization->authorization_rules;
        $paymentData = $authorization->payment_data;
        
        // التحقق من الحد الأقصى للمعاملة
        if (isset($rules['max_amount_per_transaction'])) {
            if ($paymentData['amount'] > $rules['max_amount_per_transaction']) {
                throw new \Exception('المبلغ يتجاوز الحد الأقصى المسموح للمعاملة الواحدة');
            }
        }
        
        // التحقق من الحد اليومي
        if (isset($rules['max_amount_per_day'])) {
            $todayAmount = MCPPaymentAuthorization::where('agent_id', $authorization->agent_id)
                ->where('user_id', $authorization->user_id)
                ->where('status', 'executed')
                ->whereDate('executed_at', today())
                ->join('payment_transactions', 'mcp_payment_authorizations.transaction_id', '=', 'payment_transactions.id')
                ->sum('payment_transactions.amount');
            
            if (($todayAmount + $paymentData['amount']) > $rules['max_amount_per_day']) {
                throw new \Exception('المبلغ يتجاوز الحد اليومي المسموح');
            }
        }
        
        // التحقق من طرق الدفع المسموحة
        if (isset($rules['allowed_payment_methods'])) {
            if (!in_array($paymentData['payment_method'], $rules['allowed_payment_methods'])) {
                throw new \Exception('طريقة الدفع غير مسموحة');
            }
        }
    }

    /**
     * تحضير بيانات الدفع
     */
    protected function preparePaymentData(MCPPaymentAuthorization $authorization): array
    {
        $paymentData = $authorization->payment_data;
        
        return array_merge($paymentData, [
            'user_id' => $authorization->user_id,
            'metadata' => array_merge($paymentData['metadata'] ?? [], [
                'mcp_agent_id' => $authorization->agent->agent_id,
                'authorization_id' => $authorization->authorization_id,
                'processed_via_mcp' => true,
            ]),
        ]);
    }

    /**
     * إشعار المستخدم بطلب التفويض
     */
    protected function notifyUserForAuthorization(User $user, MCPPaymentAuthorization $authorization): void
    {
        // إرسال إشعار للمستخدم (بريد إلكتروني، SMS، push notification)
        // يمكن تطوير هذا حسب نظام الإشعارات المستخدم
        
        Log::info('تم إرسال إشعار تفويض للمستخدم', [
            'user_id' => $user->id,
            'authorization_id' => $authorization->authorization_id,
        ]);
    }

    /**
     * إشعار الوكيل
     */
    protected function notifyAgent(MCPAgent $agent, string $eventType, array $data): void
    {
        if ($agent->webhook_url) {
            // إرسال webhook للوكيل
            try {
                \Http::timeout(10)->post($agent->webhook_url, [
                    'event_type' => $eventType,
                    'agent_id' => $agent->agent_id,
                    'data' => $data,
                    'timestamp' => now()->toISOString(),
                ]);
            } catch (\Exception $e) {
                Log::warning('فشل في إرسال webhook للوكيل', [
                    'agent_id' => $agent->agent_id,
                    'webhook_url' => $agent->webhook_url,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * توليد مفتاح API
     */
    protected function generateApiKey(): string
    {
        return 'mcp_' . Str::random(32);
    }

    /**
     * إلغاء تفويض
     */
    public function revokeAuthorization(MCPPaymentAuthorization $authorization): void
    {
        $authorization->update([
            'status' => 'revoked',
            'revoked_at' => now(),
        ]);
        
        // إشعار الوكيل
        $this->notifyAgent($authorization->agent, 'authorization_revoked', [
            'authorization_id' => $authorization->authorization_id,
        ]);
    }

    /**
     * تعليق وكيل
     */
    public function suspendAgent(MCPAgent $agent, string $reason): void
    {
        $agent->update([
            'status' => 'suspended',
            'suspension_reason' => $reason,
            'suspended_at' => now(),
        ]);
        
        // إلغاء جميع التفويضات النشطة
        MCPPaymentAuthorization::where('agent_id', $agent->id)
            ->whereIn('status', ['pending', 'approved'])
            ->update([
                'status' => 'revoked',
                'revoked_at' => now(),
            ]);
    }
}
