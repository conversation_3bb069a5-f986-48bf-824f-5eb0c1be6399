<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\ProjectRisk;
use App\Domains\Projects\Models\ProjectIssue;
use App\Domains\Projects\Models\RiskMitigation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة إدارة المخاطر والمشكلات - Risk & Issue Management
 * تدير المخاطر والمشكلات في المشاريع بشكل استباقي
 */
class RiskManagementService
{
    protected array $riskCategories = [
        'TECHNICAL',
        'FINANCIAL',
        'OPERATIONAL',
        'STRATEGIC',
        'EXTERNAL',
        'REGULATORY',
        'RESOURCE',
        'SCHEDULE'
    ];

    protected array $riskLevels = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
    protected array $issueTypes = ['BUG', 'FEATURE_REQUEST', 'IMPROVEMENT', 'TASK', 'EPIC'];

    /**
     * تحديد وتسجيل مخاطر جديدة
     */
    public function identifyRisk(
        int $projectId,
        array $riskData,
        int $identifiedBy
    ): ProjectRisk {
        try {
            DB::beginTransaction();

            $risk = ProjectRisk::create([
                'project_id' => $projectId,
                'title' => $riskData['title'],
                'description' => $riskData['description'],
                'category' => $riskData['category'],
                'probability' => $riskData['probability'],
                'impact' => $riskData['impact'],
                'risk_score' => $this->calculateRiskScore($riskData['probability'], $riskData['impact']),
                'risk_level' => $this->determineRiskLevel($riskData['probability'], $riskData['impact']),
                'status' => 'IDENTIFIED',
                'identified_by' => $identifiedBy,
                'identified_at' => now(),
                'owner_id' => $riskData['owner_id'] ?? null,
                'due_date' => $riskData['due_date'] ?? null,
                'triggers' => $riskData['triggers'] ?? [],
                'early_warning_signs' => $riskData['early_warning_signs'] ?? [],
                'metadata' => $riskData['metadata'] ?? [],
            ]);

            // إنشاء خطة التخفيف الأولية
            if (!empty($riskData['mitigation_plan'])) {
                $this->createMitigationPlan($risk, $riskData['mitigation_plan'], $identifiedBy);
            }

            // تحديث مصفوفة المخاطر للمشروع
            $this->updateProjectRiskMatrix($projectId);

            DB::commit();

            // إشعار أصحاب المصلحة
            $this->notifyStakeholders($risk, 'risk_identified');

            return $risk;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في تحديد المخاطر', [
                'project_id' => $projectId,
                'risk_data' => $riskData,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * تقييم المخاطر
     */
    public function assessRisk(int $riskId, array $assessmentData, int $assessedBy): ProjectRisk
    {
        $risk = ProjectRisk::findOrFail($riskId);

        $risk->update([
            'probability' => $assessmentData['probability'],
            'impact' => $assessmentData['impact'],
            'risk_score' => $this->calculateRiskScore($assessmentData['probability'], $assessmentData['impact']),
            'risk_level' => $this->determineRiskLevel($assessmentData['probability'], $assessmentData['impact']),
            'assessment_notes' => $assessmentData['notes'] ?? null,
            'assessed_by' => $assessedBy,
            'assessed_at' => now(),
            'status' => 'ASSESSED',
        ]);

        // تسجيل تاريخ التقييم
        $risk->assessments()->create([
            'probability' => $assessmentData['probability'],
            'impact' => $assessmentData['impact'],
            'risk_score' => $risk->risk_score,
            'notes' => $assessmentData['notes'] ?? null,
            'assessed_by' => $assessedBy,
            'assessed_at' => now(),
        ]);

        // تحديث مصفوفة المخاطر
        $this->updateProjectRiskMatrix($risk->project_id);

        // إشعار إذا تغير مستوى المخاطر
        if ($risk->wasChanged('risk_level')) {
            $this->notifyRiskLevelChange($risk);
        }

        return $risk;
    }

    /**
     * إنشاء خطة تخفيف المخاطر
     */
    public function createMitigationPlan(
        ProjectRisk $risk,
        array $planData,
        int $createdBy
    ): RiskMitigation {
        $mitigation = $risk->mitigations()->create([
            'strategy' => $planData['strategy'], // AVOID, MITIGATE, TRANSFER, ACCEPT
            'description' => $planData['description'],
            'action_items' => $planData['action_items'] ?? [],
            'responsible_id' => $planData['responsible_id'],
            'target_date' => $planData['target_date'],
            'budget_required' => $planData['budget_required'] ?? 0,
            'success_criteria' => $planData['success_criteria'] ?? [],
            'status' => 'PLANNED',
            'created_by' => $createdBy,
        ]);

        // إنشاء مهام للعناصر القابلة للتنفيذ
        if (!empty($planData['action_items'])) {
            $this->createMitigationTasks($mitigation, $planData['action_items']);
        }

        // تحديث حالة المخاطر
        $risk->update(['status' => 'MITIGATION_PLANNED']);

        return $mitigation;
    }

    /**
     * مراقبة المخاطر
     */
    public function monitorRisks(int $projectId): array
    {
        $project = Project::findOrFail($projectId);
        $risks = $project->risks()->where('status', '!=', 'CLOSED')->get();

        $monitoringResults = [];

        foreach ($risks as $risk) {
            $monitoringResult = $this->monitorIndividualRisk($risk);
            $monitoringResults[] = $monitoringResult;

            // تحديث حالة المخاطر بناءً على المراقبة
            if ($monitoringResult['status_change_required']) {
                $this->updateRiskStatus($risk, $monitoringResult['new_status']);
            }
        }

        return [
            'project_id' => $projectId,
            'monitoring_date' => now(),
            'total_risks' => $risks->count(),
            'active_risks' => $risks->where('status', 'ACTIVE')->count(),
            'high_priority_risks' => $risks->where('risk_level', 'HIGH')->count(),
            'critical_risks' => $risks->where('risk_level', 'CRITICAL')->count(),
            'risks_requiring_attention' => collect($monitoringResults)->where('requires_attention', true)->count(),
            'detailed_results' => $monitoringResults,
        ];
    }

    /**
     * تسجيل مشكلة جديدة
     */
    public function reportIssue(
        int $projectId,
        array $issueData,
        int $reportedBy
    ): ProjectIssue {
        try {
            DB::beginTransaction();

            $issue = ProjectIssue::create([
                'project_id' => $projectId,
                'title' => $issueData['title'],
                'description' => $issueData['description'],
                'type' => $issueData['type'],
                'priority' => $issueData['priority'],
                'severity' => $issueData['severity'] ?? 'MEDIUM',
                'status' => 'OPEN',
                'reported_by' => $reportedBy,
                'reported_at' => now(),
                'assigned_to' => $issueData['assigned_to'] ?? null,
                'due_date' => $issueData['due_date'] ?? null,
                'affected_components' => $issueData['affected_components'] ?? [],
                'steps_to_reproduce' => $issueData['steps_to_reproduce'] ?? [],
                'expected_behavior' => $issueData['expected_behavior'] ?? null,
                'actual_behavior' => $issueData['actual_behavior'] ?? null,
                'environment' => $issueData['environment'] ?? [],
                'attachments' => $issueData['attachments'] ?? [],
                'metadata' => $issueData['metadata'] ?? [],
            ]);

            // ربط المشكلة بالمخاطر ذات الصلة
            if (!empty($issueData['related_risks'])) {
                $this->linkIssueToRisks($issue, $issueData['related_risks']);
            }

            // إنشاء مهمة لحل المشكلة
            if ($issueData['create_task'] ?? true) {
                $this->createIssueResolutionTask($issue);
            }

            DB::commit();

            // إشعار الفريق
            $this->notifyTeamOfNewIssue($issue);

            return $issue;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في تسجيل المشكلة', [
                'project_id' => $projectId,
                'issue_data' => $issueData,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * تحليل المخاطر والمشكلات
     */
    public function analyzeRisksAndIssues(int $projectId): array
    {
        $project = Project::with(['risks', 'issues'])->findOrFail($projectId);

        return [
            'risk_analysis' => $this->analyzeProjectRisks($project),
            'issue_analysis' => $this->analyzeProjectIssues($project),
            'correlation_analysis' => $this->analyzeRiskIssueCorrelation($project),
            'trend_analysis' => $this->analyzeTrends($project),
            'predictive_analysis' => $this->predictFutureRisks($project),
            'recommendations' => $this->generateRecommendations($project),
        ];
    }

    /**
     * إنشاء مصفوفة المخاطر
     */
    public function generateRiskMatrix(int $projectId): array
    {
        $risks = ProjectRisk::where('project_id', $projectId)
                           ->where('status', '!=', 'CLOSED')
                           ->get();

        $matrix = [];
        
        // إنشاء مصفوفة 5x5 (احتمالية × تأثير)
        for ($probability = 1; $probability <= 5; $probability++) {
            for ($impact = 1; $impact <= 5; $impact++) {
                $matrix[$probability][$impact] = [
                    'probability' => $probability,
                    'impact' => $impact,
                    'risk_score' => $probability * $impact,
                    'risk_level' => $this->determineRiskLevel($probability, $impact),
                    'risks' => $risks->filter(function ($risk) use ($probability, $impact) {
                        return $risk->probability == $probability && $risk->impact == $impact;
                    })->values(),
                ];
            }
        }

        return [
            'matrix' => $matrix,
            'summary' => [
                'total_risks' => $risks->count(),
                'critical_risks' => $risks->where('risk_level', 'CRITICAL')->count(),
                'high_risks' => $risks->where('risk_level', 'HIGH')->count(),
                'medium_risks' => $risks->where('risk_level', 'MEDIUM')->count(),
                'low_risks' => $risks->where('risk_level', 'LOW')->count(),
            ],
            'heat_map' => $this->generateRiskHeatMap($matrix),
        ];
    }

    /**
     * تقرير المخاطر والمشكلات
     */
    public function generateRiskReport(int $projectId, array $options = []): array
    {
        $project = Project::with(['risks', 'issues', 'projectManager'])->findOrFail($projectId);

        return [
            'project_overview' => [
                'name' => $project->name,
                'manager' => $project->projectManager->name,
                'status' => $project->status,
                'report_date' => now(),
            ],
            'executive_summary' => $this->generateExecutiveSummary($project),
            'risk_register' => $this->generateRiskRegister($project),
            'issue_log' => $this->generateIssueLog($project),
            'risk_matrix' => $this->generateRiskMatrix($projectId),
            'mitigation_status' => $this->getMitigationStatus($project),
            'lessons_learned' => $this->extractLessonsLearned($project),
            'action_items' => $this->getActionItems($project),
            'recommendations' => $this->generateDetailedRecommendations($project),
        ];
    }

    // دوال مساعدة
    protected function calculateRiskScore(int $probability, int $impact): int
    {
        return $probability * $impact;
    }

    protected function determineRiskLevel(int $probability, int $impact): string
    {
        $score = $this->calculateRiskScore($probability, $impact);

        return match (true) {
            $score >= 20 => 'CRITICAL',
            $score >= 12 => 'HIGH',
            $score >= 6 => 'MEDIUM',
            default => 'LOW',
        };
    }

    protected function updateProjectRiskMatrix(int $projectId): void
    {
        $project = Project::findOrFail($projectId);
        $risks = $project->risks()->where('status', '!=', 'CLOSED')->get();

        $riskSummary = [
            'total_risks' => $risks->count(),
            'critical_risks' => $risks->where('risk_level', 'CRITICAL')->count(),
            'high_risks' => $risks->where('risk_level', 'HIGH')->count(),
            'average_risk_score' => $risks->avg('risk_score'),
            'last_updated' => now(),
        ];

        $project->update(['risk_summary' => $riskSummary]);
    }

    protected function monitorIndividualRisk(ProjectRisk $risk): array
    {
        // فحص المؤشرات المبكرة
        $earlyWarningTriggered = $this->checkEarlyWarningSignals($risk);
        
        // فحص تواريخ الاستحقاق
        $overdue = $risk->due_date && $risk->due_date->isPast();
        
        // فحص حالة خطط التخفيف
        $mitigationStatus = $this->checkMitigationStatus($risk);

        return [
            'risk_id' => $risk->id,
            'current_status' => $risk->status,
            'early_warning_triggered' => $earlyWarningTriggered,
            'is_overdue' => $overdue,
            'mitigation_status' => $mitigationStatus,
            'requires_attention' => $earlyWarningTriggered || $overdue || $mitigationStatus['behind_schedule'],
            'status_change_required' => $this->shouldChangeStatus($risk, $earlyWarningTriggered, $overdue),
            'new_status' => $this->determineNewStatus($risk, $earlyWarningTriggered, $overdue),
            'recommendations' => $this->generateRiskRecommendations($risk),
        ];
    }

    protected function createMitigationTasks(RiskMitigation $mitigation, array $actionItems): void
    {
        foreach ($actionItems as $item) {
            $mitigation->project->tasks()->create([
                'title' => $item['title'],
                'description' => $item['description'] ?? null,
                'assignee_id' => $item['assignee_id'] ?? $mitigation->responsible_id,
                'due_date' => $item['due_date'] ?? $mitigation->target_date,
                'priority' => 'HIGH',
                'type' => 'RISK_MITIGATION',
                'metadata' => [
                    'risk_id' => $mitigation->risk_id,
                    'mitigation_id' => $mitigation->id,
                ],
            ]);
        }
    }

    protected function notifyStakeholders(ProjectRisk $risk, string $event): void
    {
        // إشعار مدير المشروع ومالك المخاطر
        $stakeholders = collect([$risk->project->projectManager, $risk->owner])
                       ->filter()
                       ->unique('id');

        foreach ($stakeholders as $stakeholder) {
            $stakeholder->notify(new \App\Notifications\RiskNotification($risk, $event));
        }
    }

    // دوال إضافية للتحليل والمراقبة
    protected function notifyRiskLevelChange(ProjectRisk $risk): void { /* تنفيذ إشعار تغيير مستوى المخاطر */ }
    protected function updateRiskStatus(ProjectRisk $risk, string $newStatus): void { /* تنفيذ تحديث حالة المخاطر */ }
    protected function linkIssueToRisks(ProjectIssue $issue, array $riskIds): void { /* تنفيذ ربط المشكلة بالمخاطر */ }
    protected function createIssueResolutionTask(ProjectIssue $issue): void { /* تنفيذ إنشاء مهمة حل المشكلة */ }
    protected function notifyTeamOfNewIssue(ProjectIssue $issue): void { /* تنفيذ إشعار الفريق */ }
    protected function analyzeProjectRisks(Project $project): array { return []; }
    protected function analyzeProjectIssues(Project $project): array { return []; }
    protected function analyzeRiskIssueCorrelation(Project $project): array { return []; }
    protected function analyzeTrends(Project $project): array { return []; }
    protected function predictFutureRisks(Project $project): array { return []; }
    protected function generateRecommendations(Project $project): array { return []; }
    protected function generateRiskHeatMap(array $matrix): array { return []; }
    protected function generateExecutiveSummary(Project $project): array { return []; }
    protected function generateRiskRegister(Project $project): array { return []; }
    protected function generateIssueLog(Project $project): array { return []; }
    protected function getMitigationStatus(Project $project): array { return []; }
    protected function extractLessonsLearned(Project $project): array { return []; }
    protected function getActionItems(Project $project): array { return []; }
    protected function generateDetailedRecommendations(Project $project): array { return []; }
    protected function checkEarlyWarningSignals(ProjectRisk $risk): bool { return false; }
    protected function checkMitigationStatus(ProjectRisk $risk): array { return []; }
    protected function shouldChangeStatus(ProjectRisk $risk, bool $earlyWarning, bool $overdue): bool { return false; }
    protected function determineNewStatus(ProjectRisk $risk, bool $earlyWarning, bool $overdue): string { return $risk->status; }
    protected function generateRiskRecommendations(ProjectRisk $risk): array { return []; }
}
