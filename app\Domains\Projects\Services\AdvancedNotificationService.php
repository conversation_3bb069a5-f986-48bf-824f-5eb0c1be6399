<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\NotificationRule;
use App\Domains\Projects\Models\NotificationTemplate;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

/**
 * خدمة الإشعارات المتقدمة - Advanced Notification Service
 * تدير الإشعارات الذكية والمخصصة للمشاريع
 */
class AdvancedNotificationService
{
    protected array $notificationChannels = [
        'database',
        'mail',
        'sms',
        'push',
        'slack',
        'teams',
        'webhook',
    ];

    protected array $notificationTypes = [
        'TASK_ASSIGNED',
        'TASK_COMPLETED',
        'TASK_OVERDUE',
        'MILESTONE_REACHED',
        'PROJECT_STATUS_CHANGED',
        'DEADLINE_APPROACHING',
        'BUDGET_THRESHOLD',
        'TEAM_MEMBER_ADDED',
        'COMMENT_ADDED',
        'FILE_UPLOADED',
        'APPROVAL_REQUIRED',
        'RISK_IDENTIFIED',
        'ISSUE_REPORTED',
    ];

    /**
     * إرسال إشعار ذكي
     */
    public function sendSmartNotification(
        string $type,
        array $recipients,
        array $data,
        array $options = []
    ): array {
        $results = [];

        foreach ($recipients as $recipient) {
            try {
                // تحديد قنوات الإشعار المفضلة للمستلم
                $preferredChannels = $this->getPreferredChannels($recipient, $type);

                // تخصيص المحتوى حسب المستلم
                $personalizedContent = $this->personalizeContent($type, $data, $recipient);

                // تحديد التوقيت الأمثل للإرسال
                $optimalTime = $this->calculateOptimalTime($recipient, $type);

                // إرسال الإشعار
                $result = $this->sendNotificationToRecipient(
                    $recipient,
                    $type,
                    $personalizedContent,
                    $preferredChannels,
                    $optimalTime,
                    $options
                );

                $results[] = $result;

            } catch (\Exception $e) {
                Log::error('خطأ في إرسال الإشعار الذكي', [
                    'type' => $type,
                    'recipient' => $recipient,
                    'error' => $e->getMessage(),
                ]);

                $results[] = [
                    'recipient' => $recipient,
                    'status' => 'FAILED',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * إنشاء قاعدة إشعار مخصصة
     */
    public function createNotificationRule(array $ruleData, int $createdBy): NotificationRule
    {
        return NotificationRule::create([
            'name' => $ruleData['name'],
            'description' => $ruleData['description'] ?? null,
            'trigger_event' => $ruleData['trigger_event'],
            'conditions' => $ruleData['conditions'] ?? [],
            'recipients' => $ruleData['recipients'],
            'channels' => $ruleData['channels'],
            'template_id' => $ruleData['template_id'] ?? null,
            'is_active' => $ruleData['is_active'] ?? true,
            'priority' => $ruleData['priority'] ?? 'MEDIUM',
            'delay_minutes' => $ruleData['delay_minutes'] ?? 0,
            'frequency_limit' => $ruleData['frequency_limit'] ?? null,
            'scope' => $ruleData['scope'] ?? 'PROJECT',
            'target_id' => $ruleData['target_id'] ?? null,
            'created_by' => $createdBy,
            'metadata' => $ruleData['metadata'] ?? [],
        ]);
    }

    /**
     * إنشاء قالب إشعار
     */
    public function createNotificationTemplate(array $templateData, int $createdBy): NotificationTemplate
    {
        return NotificationTemplate::create([
            'name' => $templateData['name'],
            'type' => $templateData['type'],
            'subject_template' => $templateData['subject_template'],
            'body_template' => $templateData['body_template'],
            'variables' => $templateData['variables'] ?? [],
            'channels' => $templateData['channels'],
            'is_active' => $templateData['is_active'] ?? true,
            'language' => $templateData['language'] ?? 'ar',
            'created_by' => $createdBy,
            'metadata' => $templateData['metadata'] ?? [],
        ]);
    }

    /**
     * معالجة إشعارات المشروع
     */
    public function processProjectNotifications(int $projectId, string $event, array $eventData): array
    {
        $project = Project::findOrFail($projectId);
        
        // البحث عن قواعد الإشعار المطبقة
        $applicableRules = $this->getApplicableRules($project, $event);

        $processedNotifications = [];

        foreach ($applicableRules as $rule) {
            try {
                // تقييم الشروط
                if ($this->evaluateRuleConditions($rule, $eventData)) {
                    // تحديد المستلمين
                    $recipients = $this->resolveRecipients($rule, $project, $eventData);

                    // إرسال الإشعارات
                    $result = $this->sendRuleBasedNotification($rule, $recipients, $eventData);
                    $processedNotifications[] = $result;

                    // تسجيل الإرسال
                    $this->logNotificationSent($rule, $recipients, $result);
                }
            } catch (\Exception $e) {
                Log::error('خطأ في معالجة قاعدة الإشعار', [
                    'rule_id' => $rule->id,
                    'project_id' => $projectId,
                    'event' => $event,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $processedNotifications;
    }

    /**
     * إرسال إشعارات التذكير
     */
    public function sendReminderNotifications(): array
    {
        $reminders = [
            'overdue_tasks' => $this->sendOverdueTaskReminders(),
            'approaching_deadlines' => $this->sendDeadlineReminders(),
            'pending_approvals' => $this->sendApprovalReminders(),
            'inactive_projects' => $this->sendInactiveProjectReminders(),
            'budget_alerts' => $this->sendBudgetAlerts(),
        ];

        return $reminders;
    }

    /**
     * إرسال تذكيرات المهام المتأخرة
     */
    protected function sendOverdueTaskReminders(): array
    {
        $overdueTasks = Task::where('due_date', '<', now())
                           ->whereNotIn('status', ['COMPLETED', 'CANCELLED'])
                           ->with(['assignee', 'project'])
                           ->get();

        $results = [];

        foreach ($overdueTasks as $task) {
            if ($task->assignee) {
                $result = $this->sendSmartNotification(
                    'TASK_OVERDUE',
                    [$task->assignee],
                    [
                        'task' => $task,
                        'project' => $task->project,
                        'days_overdue' => now()->diffInDays($task->due_date),
                    ]
                );
                $results[] = $result;
            }
        }

        return $results;
    }

    /**
     * إرسال تذكيرات المواعيد النهائية القريبة
     */
    protected function sendDeadlineReminders(): array
    {
        $upcomingTasks = Task::whereBetween('due_date', [now(), now()->addDays(3)])
                            ->whereNotIn('status', ['COMPLETED', 'CANCELLED'])
                            ->with(['assignee', 'project'])
                            ->get();

        $results = [];

        foreach ($upcomingTasks as $task) {
            if ($task->assignee) {
                $result = $this->sendSmartNotification(
                    'DEADLINE_APPROACHING',
                    [$task->assignee],
                    [
                        'task' => $task,
                        'project' => $task->project,
                        'days_remaining' => now()->diffInDays($task->due_date),
                    ]
                );
                $results[] = $result;
            }
        }

        return $results;
    }

    /**
     * إدارة تفضيلات الإشعارات
     */
    public function updateNotificationPreferences(int $userId, array $preferences): array
    {
        $user = \App\Domains\HR\Models\Employee::findOrFail($userId);

        $currentPreferences = $user->notification_preferences ?? [];
        $updatedPreferences = array_merge($currentPreferences, $preferences);

        $user->update(['notification_preferences' => $updatedPreferences]);

        return $updatedPreferences;
    }

    /**
     * تحليل فعالية الإشعارات
     */
    public function analyzeNotificationEffectiveness(int $projectId = null): array
    {
        $query = \App\Models\DatabaseNotification::query();
        
        if ($projectId) {
            $query->whereJsonContains('data->project_id', $projectId);
        }

        $notifications = $query->get();

        return [
            'total_sent' => $notifications->count(),
            'read_rate' => $this->calculateReadRate($notifications),
            'response_rate' => $this->calculateResponseRate($notifications),
            'channel_effectiveness' => $this->analyzeChannelEffectiveness($notifications),
            'optimal_times' => $this->identifyOptimalTimes($notifications),
            'user_engagement' => $this->analyzeUserEngagement($notifications),
            'content_performance' => $this->analyzeContentPerformance($notifications),
        ];
    }

    /**
     * تحسين إعدادات الإشعارات
     */
    public function optimizeNotificationSettings(int $projectId): array
    {
        $project = Project::findOrFail($projectId);
        $analysis = $this->analyzeNotificationEffectiveness($projectId);

        $optimizations = [
            'recommended_channels' => $this->recommendOptimalChannels($analysis),
            'suggested_timing' => $this->suggestOptimalTiming($analysis),
            'content_improvements' => $this->suggestContentImprovements($analysis),
            'frequency_adjustments' => $this->suggestFrequencyAdjustments($analysis),
            'personalization_tips' => $this->generatePersonalizationTips($analysis),
        ];

        return $optimizations;
    }

    // دوال مساعدة
    protected function getPreferredChannels($recipient, string $type): array
    {
        $preferences = $recipient->notification_preferences ?? [];
        $typePreferences = $preferences[$type] ?? [];
        
        return $typePreferences['channels'] ?? ['database', 'mail'];
    }

    protected function personalizeContent(string $type, array $data, $recipient): array
    {
        // تخصيص المحتوى حسب المستلم
        $template = $this->getNotificationTemplate($type, $recipient->language ?? 'ar');
        
        return [
            'subject' => $this->renderTemplate($template->subject_template, $data, $recipient),
            'body' => $this->renderTemplate($template->body_template, $data, $recipient),
            'action_url' => $this->generateActionUrl($type, $data),
        ];
    }

    protected function calculateOptimalTime($recipient, string $type): \Carbon\Carbon
    {
        // حساب التوقيت الأمثل بناءً على سلوك المستلم
        $preferences = $recipient->notification_preferences ?? [];
        $optimalHour = $preferences['optimal_time'] ?? 9; // 9 صباحاً افتراضياً
        
        return now()->setHour($optimalHour)->setMinute(0)->setSecond(0);
    }

    protected function sendNotificationToRecipient(
        $recipient,
        string $type,
        array $content,
        array $channels,
        \Carbon\Carbon $scheduledTime,
        array $options
    ): array {
        $notificationClass = $this->getNotificationClass($type);
        
        if ($scheduledTime->isFuture()) {
            // جدولة الإشعار
            Queue::later($scheduledTime, function () use ($recipient, $notificationClass, $content, $channels) {
                $recipient->notify(new $notificationClass($content, $channels));
            });
            
            return [
                'recipient' => $recipient->id,
                'status' => 'SCHEDULED',
                'scheduled_time' => $scheduledTime,
            ];
        } else {
            // إرسال فوري
            $recipient->notify(new $notificationClass($content, $channels));
            
            return [
                'recipient' => $recipient->id,
                'status' => 'SENT',
                'sent_time' => now(),
            ];
        }
    }

    protected function getApplicableRules(Project $project, string $event): array
    {
        return NotificationRule::where('trigger_event', $event)
                              ->where('is_active', true)
                              ->where(function ($query) use ($project) {
                                  $query->where('scope', 'GLOBAL')
                                        ->orWhere(function ($q) use ($project) {
                                            $q->where('scope', 'PROJECT')
                                              ->where('target_id', $project->id);
                                        });
                              })
                              ->orderBy('priority')
                              ->get()
                              ->toArray();
    }

    protected function evaluateRuleConditions($rule, array $eventData): bool
    {
        // تقييم شروط القاعدة
        return true; // مبسط للمثال
    }

    protected function resolveRecipients($rule, Project $project, array $eventData): array
    {
        // تحديد المستلمين بناءً على قاعدة الإشعار
        return $project->teamMembers->toArray();
    }

    protected function sendRuleBasedNotification($rule, array $recipients, array $eventData): array
    {
        // إرسال إشعار بناءً على القاعدة
        return ['status' => 'SUCCESS', 'recipients_count' => count($recipients)];
    }

    protected function logNotificationSent($rule, array $recipients, array $result): void
    {
        // تسجيل إرسال الإشعار
    }

    // دوال إضافية للتحليل والتحسين
    protected function sendApprovalReminders(): array { return []; }
    protected function sendInactiveProjectReminders(): array { return []; }
    protected function sendBudgetAlerts(): array { return []; }
    protected function calculateReadRate($notifications): float { return 0; }
    protected function calculateResponseRate($notifications): float { return 0; }
    protected function analyzeChannelEffectiveness($notifications): array { return []; }
    protected function identifyOptimalTimes($notifications): array { return []; }
    protected function analyzeUserEngagement($notifications): array { return []; }
    protected function analyzeContentPerformance($notifications): array { return []; }
    protected function recommendOptimalChannels(array $analysis): array { return []; }
    protected function suggestOptimalTiming(array $analysis): array { return []; }
    protected function suggestContentImprovements(array $analysis): array { return []; }
    protected function suggestFrequencyAdjustments(array $analysis): array { return []; }
    protected function generatePersonalizationTips(array $analysis): array { return []; }
    protected function getNotificationTemplate(string $type, string $language): object { return (object) ['subject_template' => '', 'body_template' => '']; }
    protected function renderTemplate(string $template, array $data, $recipient): string { return $template; }
    protected function generateActionUrl(string $type, array $data): string { return ''; }
    protected function getNotificationClass(string $type): string { return \App\Notifications\ProjectNotification::class; }
}
