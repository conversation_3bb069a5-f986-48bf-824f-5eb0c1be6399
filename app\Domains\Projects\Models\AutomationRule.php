<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج قاعدة الأتمتة - Automation Rule
 * يدير قواعد الأتمتة والإجراءات التلقائية في المشاريع
 */
class AutomationRule extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'trigger_event',
        'conditions',
        'actions',
        'is_active',
        'priority',
        'execution_delay',
        'max_executions',
        'execution_count',
        'scope',
        'target_id',
        'template_id',
        'project_id',
        'created_by',
        'last_executed_at',
        'metadata',
    ];

    protected $casts = [
        'conditions' => 'array',
        'actions' => 'array',
        'is_active' => 'boolean',
        'execution_delay' => 'integer',
        'max_executions' => 'integer',
        'execution_count' => 'integer',
        'last_executed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع القالب
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(ProjectTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    /**
     * العلاقة مع سجلات التنفيذ
     */
    public function executions(): HasMany
    {
        return $this->hasMany(AutomationExecution::class, 'rule_id');
    }

    /**
     * التحقق من إمكانية التنفيذ
     */
    public function getCanExecuteAttribute(): bool
    {
        return $this->is_active && 
               ($this->max_executions === null || $this->execution_count < $this->max_executions);
    }

    /**
     * الحصول على نسبة الاستخدام
     */
    public function getUsagePercentageAttribute(): float
    {
        if ($this->max_executions === null) {
            return 0;
        }
        
        return ($this->execution_count / $this->max_executions) * 100;
    }

    /**
     * التحقق من انتهاء صلاحية القاعدة
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->max_executions !== null && 
               $this->execution_count >= $this->max_executions;
    }

    /**
     * تنفيذ القاعدة
     */
    public function execute(array $eventData): array
    {
        if (!$this->can_execute) {
            throw new \Exception('لا يمكن تنفيذ هذه القاعدة');
        }

        $execution = $this->executions()->create([
            'event_data' => $eventData,
            'started_at' => now(),
            'status' => 'RUNNING',
        ]);

        try {
            // تقييم الشروط
            if (!$this->evaluateConditions($eventData)) {
                $execution->update([
                    'status' => 'SKIPPED',
                    'completed_at' => now(),
                    'result' => ['message' => 'الشروط غير محققة'],
                ]);
                
                return ['status' => 'SKIPPED', 'reason' => 'الشروط غير محققة'];
            }

            // تنفيذ الإجراءات
            $results = $this->executeActions($eventData);

            $execution->update([
                'status' => 'COMPLETED',
                'completed_at' => now(),
                'result' => $results,
            ]);

            // تحديث عداد التنفيذ
            $this->increment('execution_count');
            $this->update(['last_executed_at' => now()]);

            // إيقاف القاعدة إذا وصلت للحد الأقصى
            if ($this->is_expired) {
                $this->update(['is_active' => false]);
            }

            return ['status' => 'SUCCESS', 'results' => $results];

        } catch (\Exception $e) {
            $execution->update([
                'status' => 'FAILED',
                'completed_at' => now(),
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * تقييم الشروط
     */
    protected function evaluateConditions(array $eventData): bool
    {
        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $eventData)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * تقييم شرط واحد
     */
    protected function evaluateCondition(array $condition, array $eventData): bool
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];
        $actualValue = data_get($eventData, $field);

        return match ($operator) {
            'equals' => $actualValue == $value,
            'not_equals' => $actualValue != $value,
            'greater_than' => $actualValue > $value,
            'less_than' => $actualValue < $value,
            'greater_than_or_equal' => $actualValue >= $value,
            'less_than_or_equal' => $actualValue <= $value,
            'contains' => str_contains($actualValue, $value),
            'not_contains' => !str_contains($actualValue, $value),
            'in' => in_array($actualValue, $value),
            'not_in' => !in_array($actualValue, $value),
            'is_null' => is_null($actualValue),
            'is_not_null' => !is_null($actualValue),
            default => false,
        };
    }

    /**
     * تنفيذ الإجراءات
     */
    protected function executeActions(array $eventData): array
    {
        $results = [];

        foreach ($this->actions as $action) {
            $result = $this->executeAction($action, $eventData);
            $results[] = $result;
        }

        return $results;
    }

    /**
     * تنفيذ إجراء واحد
     */
    protected function executeAction(array $action, array $eventData): array
    {
        $actionType = $action['type'];
        $actionData = $action['data'] ?? [];

        return match ($actionType) {
            'create_task' => $this->createTaskAction($actionData, $eventData),
            'update_task' => $this->updateTaskAction($actionData, $eventData),
            'assign_task' => $this->assignTaskAction($actionData, $eventData),
            'send_notification' => $this->sendNotificationAction($actionData, $eventData),
            'send_email' => $this->sendEmailAction($actionData, $eventData),
            'update_project_status' => $this->updateProjectStatusAction($actionData, $eventData),
            'create_milestone' => $this->createMilestoneAction($actionData, $eventData),
            'log_activity' => $this->logActivityAction($actionData, $eventData),
            'webhook' => $this->webhookAction($actionData, $eventData),
            default => ['status' => 'UNKNOWN_ACTION', 'action_type' => $actionType],
        };
    }

    /**
     * إجراء إنشاء مهمة
     */
    protected function createTaskAction(array $actionData, array $eventData): array
    {
        // منطق إنشاء مهمة
        return ['status' => 'SUCCESS', 'action' => 'create_task'];
    }

    /**
     * إجراء تحديث مهمة
     */
    protected function updateTaskAction(array $actionData, array $eventData): array
    {
        // منطق تحديث مهمة
        return ['status' => 'SUCCESS', 'action' => 'update_task'];
    }

    /**
     * إجراء تعيين مهمة
     */
    protected function assignTaskAction(array $actionData, array $eventData): array
    {
        // منطق تعيين مهمة
        return ['status' => 'SUCCESS', 'action' => 'assign_task'];
    }

    /**
     * إجراء إرسال إشعار
     */
    protected function sendNotificationAction(array $actionData, array $eventData): array
    {
        // منطق إرسال إشعار
        return ['status' => 'SUCCESS', 'action' => 'send_notification'];
    }

    /**
     * إجراء إرسال بريد إلكتروني
     */
    protected function sendEmailAction(array $actionData, array $eventData): array
    {
        // منطق إرسال بريد إلكتروني
        return ['status' => 'SUCCESS', 'action' => 'send_email'];
    }

    /**
     * إجراء تحديث حالة المشروع
     */
    protected function updateProjectStatusAction(array $actionData, array $eventData): array
    {
        // منطق تحديث حالة المشروع
        return ['status' => 'SUCCESS', 'action' => 'update_project_status'];
    }

    /**
     * إجراء إنشاء معلم
     */
    protected function createMilestoneAction(array $actionData, array $eventData): array
    {
        // منطق إنشاء معلم
        return ['status' => 'SUCCESS', 'action' => 'create_milestone'];
    }

    /**
     * إجراء تسجيل نشاط
     */
    protected function logActivityAction(array $actionData, array $eventData): array
    {
        // منطق تسجيل نشاط
        return ['status' => 'SUCCESS', 'action' => 'log_activity'];
    }

    /**
     * إجراء Webhook
     */
    protected function webhookAction(array $actionData, array $eventData): array
    {
        // منطق استدعاء Webhook
        return ['status' => 'SUCCESS', 'action' => 'webhook'];
    }

    /**
     * تفعيل القاعدة
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * إلغاء تفعيل القاعدة
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * إعادة تعيين عداد التنفيذ
     */
    public function resetExecutionCount(): bool
    {
        return $this->update(['execution_count' => 0]);
    }

    /**
     * نسخ القاعدة
     */
    public function duplicate(string $newName, int $createdBy): self
    {
        return self::create([
            'name' => $newName,
            'description' => $this->description . ' (نسخة)',
            'trigger_event' => $this->trigger_event,
            'conditions' => $this->conditions,
            'actions' => $this->actions,
            'is_active' => false, // تبدأ غير مفعلة
            'priority' => $this->priority,
            'execution_delay' => $this->execution_delay,
            'max_executions' => $this->max_executions,
            'scope' => $this->scope,
            'target_id' => $this->target_id,
            'template_id' => $this->template_id,
            'project_id' => $this->project_id,
            'created_by' => $createdBy,
        ]);
    }

    /**
     * البحث في القواعد
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('trigger_event', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة القواعد النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب الحدث المحفز
     */
    public function scopeForTrigger($query, string $trigger)
    {
        return $query->where('trigger_event', $trigger);
    }

    /**
     * فلترة حسب النطاق
     */
    public function scopeForScope($query, string $scope)
    {
        return $query->where('scope', $scope);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * ترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'HIGH', 'MEDIUM', 'LOW')");
    }

    /**
     * فلترة القواعد المنتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('max_executions')
                    ->whereRaw('execution_count >= max_executions');
    }
}
