<?php

namespace App\Domains\Accounting\Commands;

use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Generate Chart of Accounts Command
 * أمر إنشاء دليل الحسابات
 */
class GenerateChartOfAccountsCommand extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'accounting:generate-chart-of-accounts 
                            {template=saudi_gaap : Chart of accounts template (saudi_gaap, ifrs, us_gaap)}
                            {--force : Force regeneration even if accounts exist}
                            {--dry-run : Show what would be created without actually creating}';

    /**
     * وصف الأمر
     */
    protected $description = 'Generate chart of accounts based on selected template';

    protected AdvancedAccountingEngine $accountingEngine;

    public function __construct(AdvancedAccountingEngine $accountingEngine)
    {
        parent::__construct();
        $this->accountingEngine = $accountingEngine;
    }

    /**
     * تنفيذ الأمر
     */
    public function handle(): int
    {
        $template = $this->argument('template');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info("🏗️  إنشاء دليل الحسابات باستخدام قالب: {$template}");

        // التحقق من وجود حسابات مسبقاً
        if (!$force && Account::count() > 0) {
            $this->error('❌ يوجد حسابات مسبقاً. استخدم --force للكتابة فوقها');
            return self::FAILURE;
        }

        // التحقق من صحة القالب
        if (!$this->isValidTemplate($template)) {
            $this->error("❌ قالب غير صحيح: {$template}");
            $this->line('القوالب المتاحة: saudi_gaap, ifrs, us_gaap');
            return self::FAILURE;
        }

        try {
            $accounts = $this->getAccountsForTemplate($template);

            if ($dryRun) {
                $this->showDryRunResults($accounts);
                return self::SUCCESS;
            }

            $this->createAccounts($accounts, $force);

            $this->info("✅ تم إنشاء دليل الحسابات بنجاح");
            $this->info("📊 تم إنشاء " . count($accounts) . " حساب");

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ خطأ في إنشاء دليل الحسابات: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * التحقق من صحة القالب
     */
    protected function isValidTemplate(string $template): bool
    {
        return in_array($template, ['saudi_gaap', 'ifrs', 'us_gaap']);
    }

    /**
     * الحصول على الحسابات للقالب المحدد
     */
    protected function getAccountsForTemplate(string $template): array
    {
        return match ($template) {
            'saudi_gaap' => $this->getSaudiGAAPAccounts(),
            'ifrs' => $this->getIFRSAccounts(),
            'us_gaap' => $this->getUSGAAPAccounts(),
            default => [],
        };
    }

    /**
     * حسابات المعايير السعودية
     */
    protected function getSaudiGAAPAccounts(): array
    {
        return [
            // الأصول المتداولة
            ['code' => '1000', 'name' => 'الأصول المتداولة', 'name_en' => 'Current Assets', 'type' => 'asset', 'category' => 'current_assets', 'parent' => null],
            ['code' => '1100', 'name' => 'النقدية وما في حكمها', 'name_en' => 'Cash and Cash Equivalents', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1000'],
            ['code' => '1110', 'name' => 'النقدية في الصندوق', 'name_en' => 'Cash on Hand', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1100'],
            ['code' => '1120', 'name' => 'النقدية في البنوك', 'name_en' => 'Cash in Banks', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1100'],
            ['code' => '1200', 'name' => 'الاستثمارات قصيرة الأجل', 'name_en' => 'Short-term Investments', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1000'],
            ['code' => '1300', 'name' => 'الحسابات المدينة', 'name_en' => 'Accounts Receivable', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1000'],
            ['code' => '1310', 'name' => 'حسابات العملاء', 'name_en' => 'Customer Accounts', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1300'],
            ['code' => '1320', 'name' => 'أوراق القبض', 'name_en' => 'Notes Receivable', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1300'],
            ['code' => '1330', 'name' => 'مخصص الديون المشكوك فيها', 'name_en' => 'Allowance for Doubtful Debts', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1300'],
            ['code' => '1400', 'name' => 'المخزون', 'name_en' => 'Inventory', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1000'],
            ['code' => '1410', 'name' => 'مخزون البضائع', 'name_en' => 'Merchandise Inventory', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1400'],
            ['code' => '1420', 'name' => 'مخزون المواد الخام', 'name_en' => 'Raw Materials Inventory', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1400'],
            ['code' => '1500', 'name' => 'المصروفات المدفوعة مقدماً', 'name_en' => 'Prepaid Expenses', 'type' => 'asset', 'category' => 'current_assets', 'parent' => '1000'],

            // الأصول الثابتة
            ['code' => '2000', 'name' => 'الأصول الثابتة', 'name_en' => 'Fixed Assets', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => null],
            ['code' => '2100', 'name' => 'الأراضي', 'name_en' => 'Land', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2000'],
            ['code' => '2200', 'name' => 'المباني', 'name_en' => 'Buildings', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2000'],
            ['code' => '2210', 'name' => 'مجمع إهلاك المباني', 'name_en' => 'Accumulated Depreciation - Buildings', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2200'],
            ['code' => '2300', 'name' => 'المعدات والآلات', 'name_en' => 'Equipment and Machinery', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2000'],
            ['code' => '2310', 'name' => 'مجمع إهلاك المعدات', 'name_en' => 'Accumulated Depreciation - Equipment', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2300'],
            ['code' => '2400', 'name' => 'الأثاث والتجهيزات', 'name_en' => 'Furniture and Fixtures', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2000'],
            ['code' => '2410', 'name' => 'مجمع إهلاك الأثاث', 'name_en' => 'Accumulated Depreciation - Furniture', 'type' => 'asset', 'category' => 'fixed_assets', 'parent' => '2400'],

            // الخصوم المتداولة
            ['code' => '3000', 'name' => 'الخصوم المتداولة', 'name_en' => 'Current Liabilities', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => null],
            ['code' => '3100', 'name' => 'الحسابات الدائنة', 'name_en' => 'Accounts Payable', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3000'],
            ['code' => '3110', 'name' => 'حسابات الموردين', 'name_en' => 'Supplier Accounts', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3100'],
            ['code' => '3120', 'name' => 'أوراق الدفع', 'name_en' => 'Notes Payable', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3100'],
            ['code' => '3200', 'name' => 'المصروفات المستحقة', 'name_en' => 'Accrued Expenses', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3000'],
            ['code' => '3210', 'name' => 'الرواتب المستحقة', 'name_en' => 'Accrued Salaries', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3200'],
            ['code' => '3300', 'name' => 'الضرائب المستحقة', 'name_en' => 'Accrued Taxes', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3000'],
            ['code' => '3310', 'name' => 'ضريبة القيمة المضافة', 'name_en' => 'VAT Payable', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3300'],
            ['code' => '3320', 'name' => 'ضريبة الدخل المستحقة', 'name_en' => 'Income Tax Payable', 'type' => 'liability', 'category' => 'current_liabilities', 'parent' => '3300'],

            // الخصوم طويلة الأجل
            ['code' => '4000', 'name' => 'الخصوم طويلة الأجل', 'name_en' => 'Long-term Liabilities', 'type' => 'liability', 'category' => 'long_term_liabilities', 'parent' => null],
            ['code' => '4100', 'name' => 'القروض طويلة الأجل', 'name_en' => 'Long-term Loans', 'type' => 'liability', 'category' => 'long_term_liabilities', 'parent' => '4000'],
            ['code' => '4200', 'name' => 'السندات', 'name_en' => 'Bonds Payable', 'type' => 'liability', 'category' => 'long_term_liabilities', 'parent' => '4000'],

            // حقوق الملكية
            ['code' => '5000', 'name' => 'حقوق الملكية', 'name_en' => 'Equity', 'type' => 'equity', 'category' => 'capital', 'parent' => null],
            ['code' => '5100', 'name' => 'رأس المال', 'name_en' => 'Capital', 'type' => 'equity', 'category' => 'capital', 'parent' => '5000'],
            ['code' => '5200', 'name' => 'الأرباح المحتجزة', 'name_en' => 'Retained Earnings', 'type' => 'equity', 'category' => 'retained_earnings', 'parent' => '5000'],
            ['code' => '5300', 'name' => 'أرباح السنة الحالية', 'name_en' => 'Current Year Earnings', 'type' => 'equity', 'category' => 'retained_earnings', 'parent' => '5000'],

            // الإيرادات
            ['code' => '6000', 'name' => 'الإيرادات', 'name_en' => 'Revenue', 'type' => 'revenue', 'category' => 'operating_revenue', 'parent' => null],
            ['code' => '6100', 'name' => 'إيرادات المبيعات', 'name_en' => 'Sales Revenue', 'type' => 'revenue', 'category' => 'operating_revenue', 'parent' => '6000'],
            ['code' => '6200', 'name' => 'إيرادات الخدمات', 'name_en' => 'Service Revenue', 'type' => 'revenue', 'category' => 'operating_revenue', 'parent' => '6000'],
            ['code' => '6300', 'name' => 'إيرادات أخرى', 'name_en' => 'Other Revenue', 'type' => 'revenue', 'category' => 'other_revenue', 'parent' => '6000'],

            // المصروفات
            ['code' => '7000', 'name' => 'تكلفة البضاعة المباعة', 'name_en' => 'Cost of Goods Sold', 'type' => 'expense', 'category' => 'cost_of_goods_sold', 'parent' => null],
            ['code' => '7100', 'name' => 'تكلفة المواد', 'name_en' => 'Material Costs', 'type' => 'expense', 'category' => 'cost_of_goods_sold', 'parent' => '7000'],
            ['code' => '7200', 'name' => 'تكلفة العمالة', 'name_en' => 'Labor Costs', 'type' => 'expense', 'category' => 'cost_of_goods_sold', 'parent' => '7000'],

            ['code' => '8000', 'name' => 'المصروفات التشغيلية', 'name_en' => 'Operating Expenses', 'type' => 'expense', 'category' => 'operating_expenses', 'parent' => null],
            ['code' => '8100', 'name' => 'الرواتب والأجور', 'name_en' => 'Salaries and Wages', 'type' => 'expense', 'category' => 'operating_expenses', 'parent' => '8000'],
            ['code' => '8200', 'name' => 'الإيجارات', 'name_en' => 'Rent Expense', 'type' => 'expense', 'category' => 'operating_expenses', 'parent' => '8000'],
            ['code' => '8300', 'name' => 'المرافق العامة', 'name_en' => 'Utilities', 'type' => 'expense', 'category' => 'operating_expenses', 'parent' => '8000'],
            ['code' => '8400', 'name' => 'الإهلاك', 'name_en' => 'Depreciation Expense', 'type' => 'expense', 'category' => 'operating_expenses', 'parent' => '8000'],

            ['code' => '9000', 'name' => 'المصروفات الإدارية', 'name_en' => 'Administrative Expenses', 'type' => 'expense', 'category' => 'administrative_expenses', 'parent' => null],
            ['code' => '9100', 'name' => 'مصروفات إدارية عامة', 'name_en' => 'General Administrative Expenses', 'type' => 'expense', 'category' => 'administrative_expenses', 'parent' => '9000'],
            ['code' => '9200', 'name' => 'المصروفات المالية', 'name_en' => 'Financial Expenses', 'type' => 'expense', 'category' => 'financial_expenses', 'parent' => '9000'],
        ];
    }

    /**
     * حسابات IFRS
     */
    protected function getIFRSAccounts(): array
    {
        // يمكن إضافة حسابات IFRS هنا
        return $this->getSaudiGAAPAccounts(); // مؤقتاً
    }

    /**
     * حسابات US GAAP
     */
    protected function getUSGAAPAccounts(): array
    {
        // يمكن إضافة حسابات US GAAP هنا
        return $this->getSaudiGAAPAccounts(); // مؤقتاً
    }

    /**
     * عرض نتائج التشغيل التجريبي
     */
    protected function showDryRunResults(array $accounts): void
    {
        $this->info("🔍 التشغيل التجريبي - سيتم إنشاء الحسابات التالية:");
        $this->newLine();

        $table = [];
        foreach ($accounts as $account) {
            $table[] = [
                $account['code'],
                $account['name'],
                $account['type'],
                $account['parent'] ?? 'رئيسي',
            ];
        }

        $this->table(['الرمز', 'الاسم', 'النوع', 'الحساب الأب'], $table);
        $this->info("📊 إجمالي الحسابات: " . count($accounts));
    }

    /**
     * إنشاء الحسابات
     */
    protected function createAccounts(array $accounts, bool $force): void
    {
        DB::beginTransaction();

        try {
            if ($force) {
                $this->warn("⚠️  حذف الحسابات الموجودة...");
                Account::truncate();
            }

            $createdAccounts = [];
            $bar = $this->output->createProgressBar(count($accounts));
            $bar->start();

            // إنشاء الحسابات الرئيسية أولاً
            foreach ($accounts as $accountData) {
                if ($accountData['parent'] === null) {
                    $account = $this->createAccount($accountData, $createdAccounts);
                    $createdAccounts[$accountData['code']] = $account;
                }
                $bar->advance();
            }

            // إنشاء الحسابات الفرعية
            foreach ($accounts as $accountData) {
                if ($accountData['parent'] !== null) {
                    $account = $this->createAccount($accountData, $createdAccounts);
                    $createdAccounts[$accountData['code']] = $account;
                }
                $bar->advance();
            }

            $bar->finish();
            $this->newLine();

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إنشاء حساب واحد
     */
    protected function createAccount(array $accountData, array $createdAccounts): Account
    {
        $parentId = null;
        if ($accountData['parent']) {
            $parentId = $createdAccounts[$accountData['parent']]->id ?? null;
        }

        return Account::create([
            'account_code' => $accountData['code'],
            'account_name' => $accountData['name'],
            'account_name_en' => $accountData['name_en'],
            'account_type' => $accountData['type'],
            'account_category' => $accountData['category'],
            'parent_account_id' => $parentId,
            'level' => $parentId ? 2 : 1, // مبسط
            'is_active' => true,
            'allow_manual_entry' => true,
            'currency_code' => 'SAR',
            'created_by' => 1, // النظام
        ]);
    }
}
