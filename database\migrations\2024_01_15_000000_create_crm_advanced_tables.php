<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Customer Segments Table
        Schema::create('customer_segments', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['demographic', 'behavioral', 'geographic', 'psychographic', 'value_based', 'lifecycle', 'engagement', 'custom']);
            $table->json('criteria')->nullable();
            $table->json('conditions')->nullable();
            $table->boolean('is_dynamic')->default(false);
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->string('color', 7)->nullable();
            $table->string('icon')->nullable();
            $table->integer('priority')->default(0);
            $table->integer('target_size')->nullable();
            $table->integer('actual_size')->default(0);
            $table->timestamp('last_calculated_at')->nullable();
            $table->enum('calculation_frequency', ['real_time', 'hourly', 'daily', 'weekly', 'monthly', 'manual'])->default('daily');
            $table->json('tags')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['is_dynamic', 'calculation_frequency']);
            $table->foreign('created_by')->references('id')->on('employees')->onDelete('set null');
        });

        // Customer Segment Members Table
        Schema::create('customer_segment_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_segment_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->timestamp('added_at')->useCurrent();
            $table->decimal('score', 5, 2)->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->unique(['customer_segment_id', 'customer_id']);
            $table->index(['customer_id', 'added_at']);
        });

        // Opportunity Activities Table
        Schema::create('opportunity_activities', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('opportunity_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('contact_id')->nullable()->constrained()->onDelete('set null');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->enum('type', ['call', 'email', 'meeting', 'demo', 'presentation', 'proposal', 'negotiation', 'contract_review', 'follow_up', 'research', 'qualification', 'discovery', 'objection_handling', 'closing', 'stage_change', 'note', 'task', 'reminder', 'other']);
            $table->string('subject');
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            $table->enum('outcome', ['successful', 'partially_successful', 'unsuccessful', 'no_response', 'rescheduled', 'cancelled', 'follow_up_needed', 'information_gathered', 'objection_raised', 'interest_confirmed', 'decision_pending', 'budget_confirmed', 'timeline_confirmed', 'authority_identified', 'competitor_mentioned', 'proposal_requested', 'contract_sent', 'deal_won', 'deal_lost'])->nullable();
            $table->enum('status', ['planned', 'scheduled', 'in_progress', 'completed', 'cancelled', 'postponed', 'no_show'])->default('planned');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->integer('duration_minutes')->nullable();
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('occurred_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->boolean('follow_up_required')->default(false);
            $table->timestamp('follow_up_date')->nullable();
            $table->text('follow_up_notes')->nullable();
            $table->string('location')->nullable();
            $table->json('attendees')->nullable();
            $table->string('meeting_url')->nullable();
            $table->string('call_recording_url')->nullable();
            $table->string('email_message_id')->nullable();
            $table->enum('sentiment', ['very_positive', 'positive', 'neutral', 'negative', 'very_negative'])->nullable();
            $table->integer('score')->nullable();
            $table->enum('stage_impact', ['advance', 'maintain', 'regress', 'neutral'])->nullable();
            $table->integer('probability_change')->nullable();
            $table->decimal('value_impact', 15, 2)->nullable();
            $table->json('tags')->nullable();
            $table->json('custom_fields')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['opportunity_id', 'type']);
            $table->index(['customer_id', 'occurred_at']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['follow_up_required', 'follow_up_date']);
            $table->foreign('created_by')->references('id')->on('employees')->onDelete('set null');
        });

        // Opportunity Tasks Table
        Schema::create('opportunity_tasks', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('opportunity_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['qualification', 'discovery', 'demo', 'proposal', 'follow_up', 'negotiation', 'contract', 'research', 'presentation', 'meeting', 'call', 'email', 'documentation', 'approval', 'closing', 'other']);
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold', 'blocked'])->default('pending');
            $table->timestamp('due_date')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->decimal('estimated_hours', 5, 2)->nullable();
            $table->decimal('actual_hours', 5, 2)->nullable();
            $table->enum('stage_requirement', ['lead_to_qualified', 'qualified_to_proposal', 'proposal_to_negotiation', 'negotiation_to_contract', 'contract_to_won', 'optional'])->default('optional');
            $table->boolean('auto_generated')->default(false);
            $table->foreignId('depends_on_task_id')->nullable()->constrained('opportunity_tasks')->onDelete('set null');
            $table->integer('completion_percentage')->default(0);
            $table->text('notes')->nullable();
            $table->json('tags')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['opportunity_id', 'status']);
            $table->index(['assigned_to', 'due_date']);
            $table->index(['status', 'priority']);
            $table->index(['stage_requirement', 'status']);
            $table->foreign('assigned_to')->references('id')->on('employees')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('employees')->onDelete('set null');
        });

        // Customer Satisfaction Surveys Table
        Schema::create('customer_satisfaction_surveys', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('opportunity_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('interaction_id')->nullable()->constrained('customer_interactions')->onDelete('set null');
            $table->enum('survey_type', ['nps', 'csat', 'ces', 'general', 'post_purchase', 'post_support', 'post_interaction', 'periodic', 'exit']);
            $table->enum('trigger_event', ['order_completed', 'ticket_resolved', 'interaction_completed', 'project_completed', 'contract_signed', 'subscription_renewal', 'manual_trigger', 'scheduled', 'milestone_reached']);
            $table->enum('status', ['draft', 'sent', 'opened', 'completed', 'expired', 'cancelled'])->default('draft');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('opened_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->integer('rating')->nullable();
            $table->integer('nps_score')->nullable();
            $table->integer('csat_score')->nullable();
            $table->integer('ces_score')->nullable();
            $table->text('feedback')->nullable();
            $table->json('questions')->nullable();
            $table->json('responses')->nullable();
            $table->boolean('follow_up_required')->default(false);
            $table->boolean('follow_up_completed')->default(false);
            $table->string('survey_token')->unique();
            $table->string('language', 2)->default('ar');
            $table->enum('channel', ['email', 'sms', 'whatsapp', 'in_app', 'web', 'phone'])->default('email');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['customer_id', 'survey_type']);
            $table->index(['status', 'sent_at']);
            $table->index(['follow_up_required', 'follow_up_completed']);
            $table->index(['survey_type', 'completed_at']);
        });

        // Campaign Templates Table
        Schema::create('campaign_templates', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('category', ['welcome', 'promotional', 'transactional', 'newsletter', 'event', 'follow_up', 'birthday', 'seasonal', 'product_launch', 'feedback', 'reactivation', 'upsell', 'cross_sell', 'renewal', 'win_back', 'other']);
            $table->enum('type', ['email', 'sms', 'whatsapp', 'push_notification', 'social_media', 'landing_page', 'popup', 'banner']);
            $table->enum('channel', ['email', 'sms', 'whatsapp', 'facebook', 'instagram', 'linkedin', 'twitter', 'website', 'mobile_app']);
            $table->string('language', 2)->default('ar');
            $table->string('subject')->nullable();
            $table->text('content')->nullable();
            $table->longText('html_content')->nullable();
            $table->json('variables')->nullable();
            $table->json('design_settings')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->integer('usage_count')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->json('tags')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['category', 'type']);
            $table->index(['is_active', 'is_default']);
            $table->index(['channel', 'language']);
            $table->foreign('created_by')->references('id')->on('employees')->onDelete('set null');
        });

        // Lead Sources Table
        Schema::create('lead_sources', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['organic', 'paid', 'referral', 'direct', 'social', 'email', 'content', 'event', 'partnership', 'offline']);
            $table->enum('category', ['digital_marketing', 'traditional_marketing', 'sales_outreach', 'customer_referral', 'partner_referral', 'content_marketing', 'event_marketing', 'pr_media', 'word_of_mouth', 'other']);
            $table->enum('channel', ['website', 'google_ads', 'facebook_ads', 'linkedin_ads', 'instagram_ads', 'twitter_ads', 'youtube_ads', 'seo', 'email_marketing', 'content_marketing', 'webinar', 'trade_show', 'conference', 'cold_calling', 'cold_email', 'referral_program', 'affiliate', 'print_media', 'radio', 'tv', 'outdoor', 'direct_mail', 'telemarketing', 'word_of_mouth', 'other']);
            $table->decimal('cost_per_lead', 10, 2)->nullable();
            $table->decimal('conversion_rate', 5, 2)->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('tracking_code')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('utm_content')->nullable();
            $table->enum('attribution_model', ['first_touch', 'last_touch', 'linear', 'time_decay', 'position_based', 'data_driven'])->default('last_touch');
            $table->integer('quality_score')->nullable();
            $table->integer('priority')->default(0);
            $table->string('color', 7)->nullable();
            $table->string('icon')->nullable();
            $table->json('settings')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['category', 'channel']);
            $table->index(['utm_source', 'utm_medium']);
            $table->index(['quality_score', 'conversion_rate']);
        });

        // Campaign Segments Pivot Table
        Schema::create('campaign_segments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marketing_campaign_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_segment_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['marketing_campaign_id', 'customer_segment_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_segments');
        Schema::dropIfExists('lead_sources');
        Schema::dropIfExists('campaign_templates');
        Schema::dropIfExists('customer_satisfaction_surveys');
        Schema::dropIfExists('opportunity_tasks');
        Schema::dropIfExists('opportunity_activities');
        Schema::dropIfExists('customer_segment_members');
        Schema::dropIfExists('customer_segments');
    }
};
