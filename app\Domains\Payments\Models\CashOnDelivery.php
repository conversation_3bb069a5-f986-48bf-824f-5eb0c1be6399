<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * نموذج الدفع عند الاستلام
 * يدير عمليات الدفع النقدي عند التسليم
 */
class CashOnDelivery extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cod_id',
        'reference_number',
        'user_id',
        'deliverable_type',
        'deliverable_id',
        'amount',
        'currency',
        'delivery_fee',
        'total_amount',
        'status',
        'delivery_status',
        'customer_name',
        'customer_phone',
        'customer_email',
        'delivery_address',
        'delivery_instructions',
        'delivery_date',
        'delivery_time_slot',
        'assigned_driver_id',
        'driver_name',
        'driver_phone',
        'tracking_number',
        'pickup_location',
        'delivery_attempts',
        'max_delivery_attempts',
        'cash_collected',
        'cash_collected_at',
        'cash_collected_by',
        'payment_confirmed',
        'payment_confirmed_at',
        'payment_confirmed_by',
        'delivery_proof',
        'customer_signature',
        'delivery_notes',
        'return_reason',
        'returned_at',
        'metadata',
        'scheduled_at',
        'delivered_at',
        'failed_at',
        'failure_reason',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'cash_collected' => 'decimal:2',
        'delivery_address' => 'array',
        'delivery_proof' => 'array',
        'metadata' => 'array',
        'delivery_date' => 'date',
        'scheduled_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
        'cash_collected_at' => 'datetime',
        'payment_confirmed_at' => 'datetime',
        'returned_at' => 'datetime',
        'delivery_attempts' => 'integer',
        'max_delivery_attempts' => 'integer',
        'assigned_driver_id' => 'integer',
        'cash_collected_by' => 'integer',
        'payment_confirmed_by' => 'integer',
        'payment_confirmed' => 'boolean',
    ];

    /**
     * حالات الدفع عند الاستلام
     */
    const STATUSES = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'assigned' => 'تم تعيين السائق',
        'out_for_delivery' => 'في الطريق للتسليم',
        'delivered' => 'تم التسليم',
        'cash_collected' => 'تم تحصيل النقد',
        'payment_confirmed' => 'تم تأكيد الدفع',
        'failed' => 'فشل التسليم',
        'returned' => 'مرتد',
        'cancelled' => 'ملغي',
    ];

    /**
     * حالات التسليم
     */
    const DELIVERY_STATUSES = [
        'pending' => 'في الانتظار',
        'scheduled' => 'مجدول',
        'picked_up' => 'تم الاستلام من المرسل',
        'in_transit' => 'في الطريق',
        'out_for_delivery' => 'خرج للتسليم',
        'delivery_attempted' => 'تم محاولة التسليم',
        'delivered' => 'تم التسليم',
        'failed' => 'فشل التسليم',
        'returned_to_sender' => 'مرتد للمرسل',
    ];

    /**
     * فترات التسليم
     */
    const TIME_SLOTS = [
        'morning' => '9:00 ص - 12:00 م',
        'afternoon' => '12:00 م - 3:00 م',
        'evening' => '3:00 م - 6:00 م',
        'night' => '6:00 م - 9:00 م',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cod) {
            if (empty($cod->cod_id)) {
                $cod->cod_id = static::generateCodId();
            }
            
            if (empty($cod->reference_number)) {
                $cod->reference_number = static::generateReferenceNumber();
            }
            
            if (empty($cod->tracking_number)) {
                $cod->tracking_number = static::generateTrackingNumber();
            }
        });
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة المتعددة الأشكال مع الكيان القابل للتسليم
     */
    public function deliverable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع السائق
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_driver_id');
    }

    /**
     * توليد معرف COD
     */
    public static function generateCodId(): string
    {
        return 'COD_' . strtoupper(Str::random(10)) . '_' . time();
    }

    /**
     * توليد رقم المرجع
     */
    public static function generateReferenceNumber(): string
    {
        return 'CODR_' . date('Ymd') . '_' . strtoupper(Str::random(6));
    }

    /**
     * توليد رقم التتبع
     */
    public static function generateTrackingNumber(): string
    {
        return 'TRK_COD_' . strtoupper(Str::random(12));
    }

    /**
     * التحقق من إمكانية التسليم
     */
    public function canBeDelivered(): bool
    {
        return in_array($this->status, ['confirmed', 'assigned', 'out_for_delivery']) &&
               $this->delivery_attempts < $this->max_delivery_attempts;
    }

    /**
     * التحقق من تم التسليم
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * التحقق من تحصيل النقد
     */
    public function isCashCollected(): bool
    {
        return $this->status === 'cash_collected' || $this->payment_confirmed;
    }

    /**
     * تأكيد الطلب
     */
    public function confirm(): void
    {
        $this->update([
            'status' => 'confirmed',
            'delivery_status' => 'scheduled',
        ]);

        event(new \App\Domains\Payments\Events\CodOrderConfirmed($this));
    }

    /**
     * تعيين سائق
     */
    public function assignDriver(int $driverId, string $driverName, string $driverPhone): void
    {
        $this->update([
            'assigned_driver_id' => $driverId,
            'driver_name' => $driverName,
            'driver_phone' => $driverPhone,
            'status' => 'assigned',
            'delivery_status' => 'picked_up',
        ]);

        event(new \App\Domains\Payments\Events\CodDriverAssigned($this));
    }

    /**
     * بدء التسليم
     */
    public function startDelivery(): void
    {
        $this->update([
            'status' => 'out_for_delivery',
            'delivery_status' => 'out_for_delivery',
        ]);

        event(new \App\Domains\Payments\Events\CodDeliveryStarted($this));
    }

    /**
     * محاولة التسليم
     */
    public function attemptDelivery(array $attemptData = []): void
    {
        $this->increment('delivery_attempts');
        
        $this->update([
            'delivery_status' => 'delivery_attempted',
            'metadata' => array_merge($this->metadata ?? [], [
                'delivery_attempts_log' => array_merge(
                    $this->metadata['delivery_attempts_log'] ?? [],
                    [
                        [
                            'attempt_number' => $this->delivery_attempts,
                            'attempted_at' => now(),
                            'notes' => $attemptData['notes'] ?? null,
                            'reason' => $attemptData['reason'] ?? null,
                        ]
                    ]
                )
            ]),
        ]);

        // التحقق من الحد الأقصى للمحاولات
        if ($this->delivery_attempts >= $this->max_delivery_attempts) {
            $this->markAsFailed('تجاوز الحد الأقصى لمحاولات التسليم');
        }

        event(new \App\Domains\Payments\Events\CodDeliveryAttempted($this));
    }

    /**
     * تأكيد التسليم
     */
    public function confirmDelivery(array $deliveryData): void
    {
        $this->update([
            'status' => 'delivered',
            'delivery_status' => 'delivered',
            'delivered_at' => now(),
            'delivery_proof' => $deliveryData['proof'] ?? [],
            'customer_signature' => $deliveryData['signature'] ?? null,
            'delivery_notes' => $deliveryData['notes'] ?? null,
        ]);

        event(new \App\Domains\Payments\Events\CodDeliveryCompleted($this));
    }

    /**
     * تحصيل النقد
     */
    public function collectCash(float $amount, int $collectedBy, array $collectionData = []): void
    {
        $this->update([
            'status' => 'cash_collected',
            'cash_collected' => $amount,
            'cash_collected_at' => now(),
            'cash_collected_by' => $collectedBy,
            'metadata' => array_merge($this->metadata ?? [], [
                'collection_data' => $collectionData,
            ]),
        ]);

        event(new \App\Domains\Payments\Events\CodCashCollected($this));
    }

    /**
     * تأكيد الدفع
     */
    public function confirmPayment(int $confirmedBy, array $confirmationData = []): void
    {
        $this->update([
            'status' => 'payment_confirmed',
            'payment_confirmed' => true,
            'payment_confirmed_at' => now(),
            'payment_confirmed_by' => $confirmedBy,
            'metadata' => array_merge($this->metadata ?? [], [
                'confirmation_data' => $confirmationData,
            ]),
        ]);

        // إنشاء معاملة دفع في النظام
        $this->createPaymentTransaction();

        event(new \App\Domains\Payments\Events\CodPaymentConfirmed($this));
    }

    /**
     * إنشاء معاملة دفع
     */
    protected function createPaymentTransaction(): PaymentTransaction
    {
        return PaymentTransaction::create([
            'user_id' => $this->user_id,
            'payable_type' => $this->deliverable_type,
            'payable_id' => $this->deliverable_id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'fees' => $this->delivery_fee,
            'net_amount' => $this->amount,
            'status' => 'succeeded',
            'payment_method' => 'cash_on_delivery',
            'customer_email' => $this->customer_email,
            'customer_phone' => $this->customer_phone,
            'description' => 'دفع عند الاستلام - ' . $this->cod_id,
            'metadata' => [
                'cod_id' => $this->cod_id,
                'tracking_number' => $this->tracking_number,
                'delivered_at' => $this->delivered_at,
                'cash_collected_at' => $this->cash_collected_at,
            ],
            'processed_at' => $this->cash_collected_at,
        ]);
    }

    /**
     * تحديد فشل التسليم
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'delivery_status' => 'failed',
            'failed_at' => now(),
            'failure_reason' => $reason,
        ]);

        event(new \App\Domains\Payments\Events\CodDeliveryFailed($this));
    }

    /**
     * إرجاع الطلب
     */
    public function returnOrder(string $reason): void
    {
        $this->update([
            'status' => 'returned',
            'delivery_status' => 'returned_to_sender',
            'returned_at' => now(),
            'return_reason' => $reason,
        ]);

        event(new \App\Domains\Payments\Events\CodOrderReturned($this));
    }

    /**
     * إلغاء الطلب
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'failure_reason' => $reason,
        ]);

        event(new \App\Domains\Payments\Events\CodOrderCancelled($this));
    }

    /**
     * جدولة التسليم
     */
    public function scheduleDelivery(\DateTime $deliveryDate, string $timeSlot): void
    {
        $this->update([
            'delivery_date' => $deliveryDate,
            'delivery_time_slot' => $timeSlot,
            'scheduled_at' => now(),
            'delivery_status' => 'scheduled',
        ]);
    }

    /**
     * تحديث عنوان التسليم
     */
    public function updateDeliveryAddress(array $address): void
    {
        $this->update(['delivery_address' => $address]);
    }

    /**
     * الحصول على عنوان التسليم المنسق
     */
    public function getFormattedAddress(): string
    {
        $address = $this->delivery_address;
        
        if (!$address) {
            return '';
        }

        $parts = [
            $address['street'] ?? '',
            $address['building'] ?? '',
            $address['floor'] ?? '',
            $address['apartment'] ?? '',
            $address['district'] ?? '',
            $address['city'] ?? '',
            $address['postal_code'] ?? '',
        ];

        return implode(', ', array_filter($parts));
    }

    /**
     * الحصول على حالة التسليم المترجمة
     */
    public function getStatusLabel(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على حالة التسليم المترجمة
     */
    public function getDeliveryStatusLabel(): string
    {
        return self::DELIVERY_STATUSES[$this->delivery_status] ?? $this->delivery_status;
    }

    /**
     * الحصول على فترة التسليم المترجمة
     */
    public function getTimeSlotLabel(): string
    {
        return self::TIME_SLOTS[$this->delivery_time_slot] ?? $this->delivery_time_slot;
    }

    /**
     * حساب رسوم التسليم
     */
    public function calculateDeliveryFee(): float
    {
        // حساب مبسط - يمكن تطويره ليعتمد على المسافة والوزن
        $baseFee = 10.00;
        $distanceFee = 0; // يمكن حسابه بناءً على المسافة
        $weightFee = 0; // يمكن حسابه بناءً على الوزن
        
        return $baseFee + $distanceFee + $weightFee;
    }

    /**
     * Scope للطلبات المؤكدة
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope للطلبات قيد التسليم
     */
    public function scopeOutForDelivery($query)
    {
        return $query->where('status', 'out_for_delivery');
    }

    /**
     * Scope للطلبات المسلمة
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope للطلبات المجدولة لتاريخ معين
     */
    public function scopeScheduledFor($query, $date)
    {
        return $query->where('delivery_date', $date);
    }

    /**
     * Scope للطلبات المعينة لسائق
     */
    public function scopeAssignedToDriver($query, int $driverId)
    {
        return $query->where('assigned_driver_id', $driverId);
    }

    /**
     * Scope للطلبات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('delivery_date', '<', now()->toDateString())
            ->whereNotIn('status', ['delivered', 'failed', 'returned', 'cancelled']);
    }
}
