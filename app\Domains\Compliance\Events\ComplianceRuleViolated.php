<?php

namespace App\Domains\Compliance\Events;

use App\Domains\Compliance\Models\ComplianceRule;
use App\Domains\Compliance\Models\Company;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث مخالفة قاعدة الامتثال
 */
class ComplianceRuleViolated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public ComplianceRule $rule,
        public Company $company,
        public array $violationData,
        public string $severity = 'medium'
    ) {}

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'rule_id' => $this->rule->id,
            'rule_name' => $this->rule->rule_name_ar,
            'company_id' => $this->company->id,
            'company_name' => $this->company->name,
            'country_code' => $this->rule->country->code,
            'violation_data' => $this->violationData,
            'severity' => $this->severity,
            'occurred_at' => now(),
        ];
    }
}
