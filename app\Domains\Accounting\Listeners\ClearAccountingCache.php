<?php

namespace App\Domains\Accounting\Listeners;

use App\Domains\Accounting\Events\AccountCreated;
use App\Domains\Accounting\Events\AccountUpdated;
use App\Domains\Accounting\Events\JournalEntryCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Clear Accounting Cache Listener
 * مستمع مسح كاش المحاسبة
 */
class ClearAccountingCache implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * معالجة حدث إنشاء حساب
     */
    public function handleAccountCreated(AccountCreated $event): void
    {
        $this->clearAccountRelatedCache();
        
        Log::info('تم مسح كاش الحسابات بعد إنشاء حساب جديد', [
            'account_id' => $event->account->id,
            'account_code' => $event->account->account_code,
        ]);
    }

    /**
     * معالجة حدث تحديث حساب
     */
    public function handleAccountUpdated(AccountUpdated $event): void
    {
        $this->clearAccountRelatedCache();
        
        Log::info('تم مسح كاش الحسابات بعد تحديث حساب', [
            'account_id' => $event->account->id,
            'account_code' => $event->account->account_code,
            'changes' => $event->changes,
        ]);
    }

    /**
     * معالجة حدث إنشاء قيد يومية
     */
    public function handleJournalEntryCreated(JournalEntryCreated $event): void
    {
        $this->clearFinancialReportsCache();
        $this->clearDashboardCache();
        
        Log::info('تم مسح كاش التقارير المالية بعد إنشاء قيد يومية', [
            'journal_entry_id' => $event->journalEntry->id,
            'entry_number' => $event->journalEntry->entry_number,
        ]);
    }

    /**
     * مسح كاش الحسابات
     */
    protected function clearAccountRelatedCache(): void
    {
        $cacheTags = [
            'accounts',
            'chart_of_accounts',
            'parent_accounts',
            'bank_accounts',
            'manual_entry_accounts',
        ];

        foreach ($cacheTags as $tag) {
            Cache::tags([$tag])->flush();
        }

        // مسح كاش محدد
        $cacheKeys = [
            'accounts_statistics',
            'trial_balance_*',
            'accounts_by_type_*',
            'accounts_by_category_*',
        ];

        foreach ($cacheKeys as $pattern) {
            if (str_contains($pattern, '*')) {
                // مسح الكاش بالنمط
                $this->clearCacheByPattern($pattern);
            } else {
                Cache::forget($pattern);
            }
        }
    }

    /**
     * مسح كاش التقارير المالية
     */
    protected function clearFinancialReportsCache(): void
    {
        $cacheTags = [
            'financial_reports',
            'balance_sheet',
            'income_statement',
            'cash_flow',
            'trial_balance',
        ];

        foreach ($cacheTags as $tag) {
            Cache::tags([$tag])->flush();
        }

        // مسح كاش التقارير المحددة
        $reportPatterns = [
            'balance_sheet_*',
            'income_statement_*',
            'cash_flow_*',
            'trial_balance_*',
            'general_ledger_*',
            'accounts_receivable_*',
            'accounts_payable_*',
        ];

        foreach ($reportPatterns as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * مسح كاش لوحة التحكم
     */
    protected function clearDashboardCache(): void
    {
        Cache::tags(['accounting_dashboard'])->flush();
        
        // مسح كاش لوحة التحكم لجميع المستخدمين
        $this->clearCacheByPattern('accounting_dashboard_*');
    }

    /**
     * مسح الكاش بالنمط
     */
    protected function clearCacheByPattern(string $pattern): void
    {
        try {
            // استخدام Redis إذا كان متاحاً
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $redis = Cache::getStore()->getRedis();
                $keys = $redis->keys(str_replace('*', '*', $pattern));
                
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // للمخازن الأخرى، نحتاج لطريقة مختلفة
                // يمكن تنفيذ آلية مخصصة هنا
                Log::warning('لا يمكن مسح الكاش بالنمط للمخزن الحالي', [
                    'pattern' => $pattern,
                    'store' => get_class(Cache::getStore()),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('خطأ في مسح الكاش بالنمط', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed($event, \Throwable $exception): void
    {
        Log::error('فشل في مسح كاش المحاسبة', [
            'event' => get_class($event),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
