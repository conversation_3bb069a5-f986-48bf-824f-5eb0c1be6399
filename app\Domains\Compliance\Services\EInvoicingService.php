<?php

namespace App\Domains\Compliance\Services;

use App\Domains\Compliance\Models\Country;
use App\Domains\Compliance\Models\EInvoicingConfiguration;
use App\Domains\Compliance\Models\ElectronicInvoice;
use App\Domains\Compliance\Models\EInvoiceSubmissionLog;
use App\Models\Company;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة الفوترة الإلكترونية المتقدمة
 * تدير جميع عمليات الفوترة الإلكترونية لجميع الدول
 */
class EInvoicingService
{
    /**
     * إنشاء فاتورة إلكترونية
     */
    public function createElectronicInvoice(array $invoiceData, Company $company): ElectronicInvoice
    {
        // التحقق من دعم الفوترة الإلكترونية
        $country = Country::where('code', $invoiceData['country_code'] ?? $company->country_code)->first();
        if (!$country || !$country->isEInvoicingMandatory()) {
            throw new \Exception('الفوترة الإلكترونية غير مطلوبة أو غير مدعومة لهذه الدولة');
        }

        // إنشاء الفاتورة
        $invoice = ElectronicInvoice::createFromInvoiceData($invoiceData, $company);

        // التحقق من صحة البيانات
        $validationResults = $invoice->validateInvoice();
        if (!$validationResults['valid']) {
            $errors = collect($validationResults['errors'])->flatten()->toArray();
            throw new \Exception('بيانات الفاتورة غير صحيحة: ' . implode(', ', $errors));
        }

        // تحديث حالة الفاتورة
        $invoice->update(['submission_status' => 'ready_to_submit']);

        return $invoice;
    }

    /**
     * إرسال فاتورة إلكترونية
     */
    public function submitElectronicInvoice(ElectronicInvoice $invoice): array
    {
        try {
            $response = $invoice->submitInvoice();

            // تسجيل النتيجة
            $this->logInvoiceActivity($invoice, 'submitted', $response);

            return $response;

        } catch (\Exception $e) {
            $this->logInvoiceActivity($invoice, 'submission_failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * إرسال دفعة من الفواتير
     */
    public function submitInvoiceBatch(Collection $invoices): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($invoices as $invoice) {
            try {
                $result = $this->submitElectronicInvoice($invoice);
                $results[] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'status' => 'success',
                    'response' => $result,
                ];
                $successCount++;

            } catch (\Exception $e) {
                $results[] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                ];
                $failureCount++;
            }
        }

        return [
            'total_invoices' => $invoices->count(),
            'successful_submissions' => $successCount,
            'failed_submissions' => $failureCount,
            'success_rate' => $invoices->count() > 0 ? round(($successCount / $invoices->count()) * 100, 2) : 0,
            'results' => $results,
        ];
    }

    /**
     * فحص حالة الفاتورة
     */
    public function checkInvoiceStatus(ElectronicInvoice $invoice): array
    {
        if (!$invoice->submission_reference) {
            return [
                'status' => 'not_submitted',
                'message' => 'الفاتورة لم يتم إرسالها بعد',
            ];
        }

        $eInvoicingConfig = $invoice->eInvoicingConfiguration;
        $statusResponse = $eInvoicingConfig->checkInvoiceStatus($invoice->submission_reference);

        // تحديث حالة الفاتورة بناءً على الاستجابة
        if ($statusResponse['success']) {
            $this->updateInvoiceStatusFromResponse($invoice, $statusResponse['status']);
        }

        return $statusResponse;
    }

    /**
     * إعادة إرسال الفواتير الفاشلة
     */
    public function retryFailedInvoices(Country $country, int $maxRetries = 3): array
    {
        $failedInvoices = ElectronicInvoice::byCountry($country->code)
            ->failed()
            ->where('retry_count', '<', $maxRetries)
            ->get();

        $retryResults = [];

        foreach ($failedInvoices as $invoice) {
            try {
                // زيادة عداد المحاولات
                $invoice->increment('retry_count');
                $invoice->update(['last_retry_at' => now()]);

                // إعادة المحاولة
                $result = $this->submitElectronicInvoice($invoice);

                $retryResults[] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'retry_attempt' => $invoice->retry_count,
                    'status' => 'success',
                    'response' => $result,
                ];

            } catch (\Exception $e) {
                $retryResults[] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'retry_attempt' => $invoice->retry_count,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return [
            'total_retried' => count($retryResults),
            'results' => $retryResults,
        ];
    }

    /**
     * الحصول على إحصائيات الفوترة الإلكترونية
     */
    public function getEInvoicingStatistics(Country $country, array $dateRange = null): array
    {
        $dateRange = $dateRange ?? [now()->subDays(30), now()];

        $query = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('created_at', $dateRange);

        $totalInvoices = $query->count();
        $submittedInvoices = $query->clone()->submitted()->count();
        $clearedInvoices = $query->clone()->cleared()->count();
        $failedInvoices = $query->clone()->failed()->count();

        $submissionRate = $totalInvoices > 0 ? round(($submittedInvoices / $totalInvoices) * 100, 2) : 0;
        $clearanceRate = $submittedInvoices > 0 ? round(($clearedInvoices / $submittedInvoices) * 100, 2) : 0;
        $failureRate = $totalInvoices > 0 ? round(($failedInvoices / $totalInvoices) * 100, 2) : 0;

        return [
            'period' => [
                'from' => $dateRange[0]->format('Y-m-d'),
                'to' => $dateRange[1]->format('Y-m-d'),
            ],
            'totals' => [
                'total_invoices' => $totalInvoices,
                'submitted_invoices' => $submittedInvoices,
                'cleared_invoices' => $clearedInvoices,
                'failed_invoices' => $failedInvoices,
                'pending_invoices' => $totalInvoices - $submittedInvoices - $failedInvoices,
            ],
            'rates' => [
                'submission_rate' => $submissionRate,
                'clearance_rate' => $clearanceRate,
                'failure_rate' => $failureRate,
                'success_rate' => $submissionRate - $failureRate,
            ],
            'breakdown_by_type' => $this->getInvoiceTypeBreakdown($country, $dateRange),
            'breakdown_by_status' => $this->getStatusBreakdown($country, $dateRange),
            'daily_trend' => $this->getDailySubmissionTrend($country, $dateRange),
            'error_analysis' => $this->getErrorAnalysis($country, $dateRange),
        ];
    }

    /**
     * تحليل الأخطاء الشائعة
     */
    public function getErrorAnalysis(Country $country, array $dateRange): array
    {
        $failedInvoices = ElectronicInvoice::byCountry($country->code)
            ->failed()
            ->whereBetween('created_at', $dateRange)
            ->get();

        $errorCounts = [];
        $totalErrors = 0;

        foreach ($failedInvoices as $invoice) {
            $errors = $invoice->error_messages ?? [];
            foreach ($errors as $error) {
                $errorKey = $this->categorizeError($error);
                $errorCounts[$errorKey] = ($errorCounts[$errorKey] ?? 0) + 1;
                $totalErrors++;
            }
        }

        // ترتيب الأخطاء حسب التكرار
        arsort($errorCounts);

        $topErrors = array_slice($errorCounts, 0, 10, true);

        return [
            'total_errors' => $totalErrors,
            'unique_error_types' => count($errorCounts),
            'top_errors' => array_map(function ($count) use ($totalErrors) {
                return [
                    'count' => $count,
                    'percentage' => $totalErrors > 0 ? round(($count / $totalErrors) * 100, 2) : 0,
                ];
            }, $topErrors),
            'error_categories' => $this->getErrorCategories($errorCounts),
            'recommendations' => $this->generateErrorRecommendations($topErrors),
        ];
    }

    /**
     * تصنيف الأخطاء
     */
    protected function categorizeError(string $error): string
    {
        $error = strtolower($error);

        if (str_contains($error, 'validation') || str_contains($error, 'invalid')) {
            return 'validation_error';
        } elseif (str_contains($error, 'network') || str_contains($error, 'timeout')) {
            return 'network_error';
        } elseif (str_contains($error, 'authentication') || str_contains($error, 'unauthorized')) {
            return 'authentication_error';
        } elseif (str_contains($error, 'certificate') || str_contains($error, 'signature')) {
            return 'certificate_error';
        } elseif (str_contains($error, 'format') || str_contains($error, 'xml') || str_contains($error, 'json')) {
            return 'format_error';
        } elseif (str_contains($error, 'tax') || str_contains($error, 'vat')) {
            return 'tax_calculation_error';
        } else {
            return 'other_error';
        }
    }

    /**
     * الحصول على فئات الأخطاء
     */
    protected function getErrorCategories(array $errorCounts): array
    {
        $categories = [
            'validation_error' => 'أخطاء التحقق',
            'network_error' => 'أخطاء الشبكة',
            'authentication_error' => 'أخطاء المصادقة',
            'certificate_error' => 'أخطاء الشهادات',
            'format_error' => 'أخطاء التنسيق',
            'tax_calculation_error' => 'أخطاء حساب الضرائب',
            'other_error' => 'أخطاء أخرى',
        ];

        $categoryCounts = [];
        foreach ($errorCounts as $errorType => $count) {
            $category = $categories[$errorType] ?? 'أخطاء أخرى';
            $categoryCounts[$category] = ($categoryCounts[$category] ?? 0) + $count;
        }

        return $categoryCounts;
    }

    /**
     * توليد توصيات لحل الأخطاء
     */
    protected function generateErrorRecommendations(array $topErrors): array
    {
        $recommendations = [];

        foreach (array_keys($topErrors) as $errorType) {
            $recommendation = match ($errorType) {
                'validation_error' => 'تحقق من صحة البيانات المدخلة وتطابقها مع المتطلبات',
                'network_error' => 'تحقق من اتصال الإنترنت وإعدادات الشبكة',
                'authentication_error' => 'تحقق من صحة بيانات المصادقة والشهادات',
                'certificate_error' => 'تحديث الشهادات الرقمية والتأكد من صحتها',
                'format_error' => 'مراجعة تنسيق البيانات والتأكد من مطابقتها للمعايير',
                'tax_calculation_error' => 'مراجعة حسابات الضرائب والمعدلات المطبقة',
                default => 'مراجعة السجلات وتحليل الخطأ بالتفصيل',
            };

            $recommendations[] = [
                'error_type' => $errorType,
                'recommendation' => $recommendation,
                'priority' => $this->getErrorPriority($errorType),
            ];
        }

        return $recommendations;
    }

    /**
     * تحديد أولوية الخطأ
     */
    protected function getErrorPriority(string $errorType): string
    {
        return match ($errorType) {
            'authentication_error', 'certificate_error' => 'high',
            'validation_error', 'tax_calculation_error' => 'medium',
            'network_error', 'format_error' => 'low',
            default => 'low',
        };
    }

    /**
     * مراقبة الأداء في الوقت الفعلي
     */
    public function getRealtimePerformance(Country $country): array
    {
        $cacheKey = "einvoicing_performance_{$country->code}";

        return Cache::remember($cacheKey, 300, function () use ($country) {
            $last24Hours = [now()->subDay(), now()];
            $lastHour = [now()->subHour(), now()];

            return [
                'current_status' => $this->getCurrentSystemStatus($country),
                'last_24_hours' => [
                    'total_submissions' => ElectronicInvoice::byCountry($country->code)
                        ->whereBetween('submitted_at', $last24Hours)
                        ->count(),
                    'success_rate' => $this->calculateSuccessRate($country, $last24Hours),
                    'average_processing_time' => $this->calculateAverageProcessingTime($country, $last24Hours),
                ],
                'last_hour' => [
                    'submissions' => ElectronicInvoice::byCountry($country->code)
                        ->whereBetween('submitted_at', $lastHour)
                        ->count(),
                    'success_rate' => $this->calculateSuccessRate($country, $lastHour),
                ],
                'system_health' => $this->checkSystemHealth($country),
                'alerts' => $this->getActiveAlerts($country),
            ];
        });
    }

    /**
     * فحص صحة النظام
     */
    protected function checkSystemHealth(Country $country): array
    {
        $eInvoicingConfig = $country->eInvoicingSystem;
        if (!$eInvoicingConfig) {
            return ['status' => 'not_configured', 'message' => 'نظام الفوترة الإلكترونية غير مكون'];
        }

        // فحص الاتصال مع API
        $apiHealth = $this->checkAPIHealth($eInvoicingConfig);

        // فحص الشهادات
        $certificateHealth = $this->checkCertificateHealth($eInvoicingConfig);

        // فحص معدل الأخطاء
        $errorRate = $this->calculateRecentErrorRate($country);

        $overallHealth = 'healthy';
        if ($errorRate > 10 || !$apiHealth['healthy'] || !$certificateHealth['healthy']) {
            $overallHealth = 'degraded';
        }
        if ($errorRate > 25 || !$apiHealth['accessible']) {
            $overallHealth = 'unhealthy';
        }

        return [
            'status' => $overallHealth,
            'api_health' => $apiHealth,
            'certificate_health' => $certificateHealth,
            'error_rate' => $errorRate,
            'last_check' => now(),
        ];
    }

    /**
     * فحص صحة API
     */
    protected function checkAPIHealth(EInvoicingConfiguration $config): array
    {
        try {
            $testEndpoint = $config->production_environment['health_check_endpoint'] ??
                           $config->production_environment['submission_endpoint'];

            $response = Http::timeout(10)->get($testEndpoint);

            return [
                'accessible' => true,
                'healthy' => $response->successful(),
                'response_time' => $response->transferStats?->getTransferTime() ?? 0,
                'status_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'accessible' => false,
                'healthy' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * فحص صحة الشهادات
     */
    protected function checkCertificateHealth(EInvoicingConfiguration $config): array
    {
        $certificateConfig = $config->certificate_config ?? [];

        if (empty($certificateConfig)) {
            return ['healthy' => true, 'message' => 'لا توجد شهادات مطلوبة'];
        }

        // فحص انتهاء صلاحية الشهادة
        $expiryDate = $certificateConfig['expiry_date'] ?? null;
        if ($expiryDate) {
            $expiryDate = Carbon::parse($expiryDate);
            $daysUntilExpiry = now()->diffInDays($expiryDate, false);

            if ($daysUntilExpiry < 0) {
                return ['healthy' => false, 'message' => 'الشهادة منتهية الصلاحية'];
            } elseif ($daysUntilExpiry < 30) {
                return ['healthy' => true, 'warning' => "الشهادة ستنتهي خلال {$daysUntilExpiry} يوم"];
            }
        }

        return ['healthy' => true, 'message' => 'الشهادة صالحة'];
    }

    /**
     * حساب معدل الأخطاء الحديث
     */
    protected function calculateRecentErrorRate(Country $country): float
    {
        $recentPeriod = [now()->subHours(6), now()];

        $totalSubmissions = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $recentPeriod)
            ->count();

        $failedSubmissions = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $recentPeriod)
            ->failed()
            ->count();

        return $totalSubmissions > 0 ? round(($failedSubmissions / $totalSubmissions) * 100, 2) : 0;
    }

    /**
     * الحصول على التنبيهات النشطة
     */
    protected function getActiveAlerts(Country $country): array
    {
        $alerts = [];

        // تحقق من معدل الأخطاء العالي
        $errorRate = $this->calculateRecentErrorRate($country);
        if ($errorRate > 15) {
            $alerts[] = [
                'type' => 'high_error_rate',
                'severity' => $errorRate > 25 ? 'critical' : 'warning',
                'message' => "معدل أخطاء عالي: {$errorRate}%",
                'action' => 'review_failed_submissions',
            ];
        }

        // تحقق من الشهادات
        $eInvoicingConfig = $country->eInvoicingSystem;
        if ($eInvoicingConfig) {
            $certificateHealth = $this->checkCertificateHealth($eInvoicingConfig);
            if (isset($certificateHealth['warning'])) {
                $alerts[] = [
                    'type' => 'certificate_expiry',
                    'severity' => 'warning',
                    'message' => $certificateHealth['warning'],
                    'action' => 'renew_certificate',
                ];
            }
        }

        return $alerts;
    }

    /**
     * تحديث حالة الفاتورة من الاستجابة
     */
    protected function updateInvoiceStatusFromResponse(ElectronicInvoice $invoice, array $statusData): void
    {
        $newStatus = $statusData['status'] ?? $invoice->submission_status;
        $newClearanceStatus = $statusData['clearance_status'] ?? $invoice->clearance_status;

        $updateData = [
            'submission_status' => $newStatus,
            'clearance_status' => $newClearanceStatus,
            'government_response' => array_merge($invoice->government_response ?? [], $statusData),
        ];

        if ($newStatus === 'acknowledged' && !$invoice->acknowledged_at) {
            $updateData['acknowledged_at'] = now();
        }

        if ($newClearanceStatus === 'cleared' && !$invoice->cleared_at) {
            $updateData['cleared_at'] = now();
        }

        $invoice->update($updateData);
    }

    /**
     * تسجيل نشاط الفاتورة
     */
    protected function logInvoiceActivity(ElectronicInvoice $invoice, string $activity, array $data = []): void
    {
        Log::info("E-Invoice Activity: {$activity}", [
            'invoice_id' => $invoice->id,
            'invoice_number' => $invoice->invoice_number,
            'country' => $invoice->country->code,
            'company_id' => $invoice->company_id,
            'activity' => $activity,
            'data' => $data,
        ]);
    }

    // طرق مساعدة إضافية
    protected function getInvoiceTypeBreakdown(Country $country, array $dateRange): array
    {
        return ElectronicInvoice::byCountry($country->code)
            ->whereBetween('created_at', $dateRange)
            ->selectRaw('invoice_type, COUNT(*) as count')
            ->groupBy('invoice_type')
            ->pluck('count', 'invoice_type')
            ->toArray();
    }

    protected function getStatusBreakdown(Country $country, array $dateRange): array
    {
        return ElectronicInvoice::byCountry($country->code)
            ->whereBetween('created_at', $dateRange)
            ->selectRaw('submission_status, COUNT(*) as count')
            ->groupBy('submission_status')
            ->pluck('count', 'submission_status')
            ->toArray();
    }

    protected function getDailySubmissionTrend(Country $country, array $dateRange): array
    {
        return ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $dateRange)
            ->selectRaw('DATE(submitted_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }

    protected function getCurrentSystemStatus(Country $country): string
    {
        $recentFailureRate = $this->calculateRecentErrorRate($country);

        if ($recentFailureRate > 25) {
            return 'critical';
        } elseif ($recentFailureRate > 10) {
            return 'degraded';
        } else {
            return 'operational';
        }
    }

    protected function calculateSuccessRate(Country $country, array $dateRange): float
    {
        $total = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $dateRange)
            ->count();

        $successful = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $dateRange)
            ->submitted()
            ->count();

        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    protected function calculateAverageProcessingTime(Country $country, array $dateRange): float
    {
        $invoices = ElectronicInvoice::byCountry($country->code)
            ->whereBetween('submitted_at', $dateRange)
            ->whereNotNull('cleared_at')
            ->get();

        if ($invoices->isEmpty()) {
            return 0;
        }

        $totalProcessingTime = $invoices->sum(function ($invoice) {
            return $invoice->submitted_at->diffInSeconds($invoice->cleared_at);
        });

        return round($totalProcessingTime / $invoices->count(), 2);
    }

    /**
     * الحصول على حالة النظام للدولة
     */
    public function getStatus(Country $country): array
    {
        $eInvoicingConfig = $country->eInvoicingSystem;

        if (!$eInvoicingConfig || !$eInvoicingConfig->is_active) {
            return [
                'status' => 'not_configured',
                'message' => 'نظام الفوترة الإلكترونية غير مكون أو غير نشط',
            ];
        }

        $recentStats = $this->getEInvoicingStatistics($country, [now()->subDays(7), now()]);

        return [
            'status' => 'active',
            'system_name' => $eInvoicingConfig->system_name_ar,
            'authority' => $eInvoicingConfig->authority_name,
            'mandatory_from' => $eInvoicingConfig->mandatory_from,
            'recent_statistics' => $recentStats,
            'system_health' => $this->checkSystemHealth($country),
        ];
    }

    /**
     * الحصول على إجمالي الفواتير المرسلة
     */
    public function getTotalInvoicesSubmitted(string $countryCode): int
    {
        return ElectronicInvoice::byCountry($countryCode)
            ->submitted()
            ->count();
    }

    /**
     * الحصول على معدل نجاح الإرسال
     */
    public function getSubmissionSuccessRate(string $countryCode): float
    {
        $total = ElectronicInvoice::byCountry($countryCode)->count();
        $successful = ElectronicInvoice::byCountry($countryCode)->submitted()->count();

        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * الحصول على وقت آخر إرسال
     */
    public function getLastSubmissionTime(string $countryCode): ?Carbon
    {
        $lastInvoice = ElectronicInvoice::byCountry($countryCode)
            ->submitted()
            ->latest('submitted_at')
            ->first();

        return $lastInvoice?->submitted_at;
    }
}
