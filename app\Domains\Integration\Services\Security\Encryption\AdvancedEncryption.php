<?php

namespace App\Domains\Integration\Services\Security\Encryption;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Advanced Encryption Service
 * 
 * Provides enterprise-grade encryption capabilities including
 * AES-256, RSA, elliptic curve cryptography, and key management
 */
class AdvancedEncryption
{
    protected array $config;
    protected array $keyStore;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'default_cipher' => 'AES-256-GCM',
            'key_rotation_interval' => 86400 * 30, // 30 days
            'key_derivation_iterations' => 100000,
            'rsa_key_size' => 4096,
            'ec_curve' => 'secp384r1',
            'hash_algorithm' => 'sha256',
        ], $config);

        $this->initializeKeyStore();
    }

    /**
     * Encrypt data using specified algorithm
     */
    public function encrypt(string $data, array $options = []): array
    {
        try {
            $cipher = $options['cipher'] ?? $this->config['default_cipher'];
            $keyId = $options['key_id'] ?? 'default';
            
            switch ($cipher) {
                case 'AES-256-GCM':
                    return $this->encryptAESGCM($data, $keyId);
                case 'AES-256-CBC':
                    return $this->encryptAESCBC($data, $keyId);
                case 'RSA-OAEP':
                    return $this->encryptRSA($data, $keyId);
                case 'ChaCha20-Poly1305':
                    return $this->encryptChaCha20($data, $keyId);
                default:
                    throw new \Exception("Unsupported cipher: {$cipher}");
            }
        } catch (\Exception $e) {
            Log::error('Encryption failed', [
                'cipher' => $cipher ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Decrypt data
     */
    public function decrypt(array $encryptedData): string
    {
        try {
            $cipher = $encryptedData['cipher'];
            $keyId = $encryptedData['key_id'];
            
            switch ($cipher) {
                case 'AES-256-GCM':
                    return $this->decryptAESGCM($encryptedData, $keyId);
                case 'AES-256-CBC':
                    return $this->decryptAESCBC($encryptedData, $keyId);
                case 'RSA-OAEP':
                    return $this->decryptRSA($encryptedData, $keyId);
                case 'ChaCha20-Poly1305':
                    return $this->decryptChaCha20($encryptedData, $keyId);
                default:
                    throw new \Exception("Unsupported cipher: {$cipher}");
            }
        } catch (\Exception $e) {
            Log::error('Decryption failed', [
                'cipher' => $encryptedData['cipher'] ?? 'unknown',
                'key_id' => $encryptedData['key_id'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Generate digital signature
     */
    public function sign(string $data, string $keyId = 'default'): array
    {
        try {
            $privateKey = $this->getPrivateKey($keyId);
            $signature = '';
            
            if (openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                return [
                    'data' => $data,
                    'signature' => base64_encode($signature),
                    'key_id' => $keyId,
                    'algorithm' => 'SHA256withRSA',
                    'timestamp' => time(),
                ];
            } else {
                throw new \Exception('Failed to generate signature');
            }
        } catch (\Exception $e) {
            Log::error('Signing failed', [
                'key_id' => $keyId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Verify digital signature
     */
    public function verify(array $signedData): bool
    {
        try {
            $publicKey = $this->getPublicKey($signedData['key_id']);
            $signature = base64_decode($signedData['signature']);
            
            $result = openssl_verify(
                $signedData['data'],
                $signature,
                $publicKey,
                OPENSSL_ALGO_SHA256
            );
            
            return $result === 1;
        } catch (\Exception $e) {
            Log::error('Signature verification failed', [
                'key_id' => $signedData['key_id'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Generate cryptographic hash
     */
    public function hash(string $data, array $options = []): string
    {
        $algorithm = $options['algorithm'] ?? $this->config['hash_algorithm'];
        $salt = $options['salt'] ?? random_bytes(32);
        $iterations = $options['iterations'] ?? $this->config['key_derivation_iterations'];
        
        if ($options['use_pbkdf2'] ?? false) {
            return base64_encode(hash_pbkdf2($algorithm, $data, $salt, $iterations, 32, true));
        }
        
        return hash($algorithm, $data . $salt);
    }

    /**
     * Generate secure random key
     */
    public function generateKey(int $length = 32): string
    {
        return random_bytes($length);
    }

    /**
     * Derive key from password
     */
    public function deriveKey(string $password, string $salt, int $length = 32): string
    {
        return hash_pbkdf2(
            $this->config['hash_algorithm'],
            $password,
            $salt,
            $this->config['key_derivation_iterations'],
            $length,
            true
        );
    }

    /**
     * Encrypt using AES-256-GCM
     */
    protected function encryptAESGCM(string $data, string $keyId): array
    {
        $key = $this->getSymmetricKey($keyId);
        $iv = random_bytes(12); // 96-bit IV for GCM
        $tag = '';
        
        $encrypted = openssl_encrypt($data, 'AES-256-GCM', $key, OPENSSL_RAW_DATA, $iv, $tag);
        
        if ($encrypted === false) {
            throw new \Exception('AES-GCM encryption failed');
        }
        
        return [
            'cipher' => 'AES-256-GCM',
            'key_id' => $keyId,
            'data' => base64_encode($encrypted),
            'iv' => base64_encode($iv),
            'tag' => base64_encode($tag),
            'timestamp' => time(),
        ];
    }

    /**
     * Decrypt using AES-256-GCM
     */
    protected function decryptAESGCM(array $encryptedData, string $keyId): string
    {
        $key = $this->getSymmetricKey($keyId);
        $encrypted = base64_decode($encryptedData['data']);
        $iv = base64_decode($encryptedData['iv']);
        $tag = base64_decode($encryptedData['tag']);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-GCM', $key, OPENSSL_RAW_DATA, $iv, $tag);
        
        if ($decrypted === false) {
            throw new \Exception('AES-GCM decryption failed');
        }
        
        return $decrypted;
    }

    /**
     * Encrypt using AES-256-CBC
     */
    protected function encryptAESCBC(string $data, string $keyId): array
    {
        $key = $this->getSymmetricKey($keyId);
        $iv = random_bytes(16); // 128-bit IV for CBC
        
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        
        if ($encrypted === false) {
            throw new \Exception('AES-CBC encryption failed');
        }
        
        return [
            'cipher' => 'AES-256-CBC',
            'key_id' => $keyId,
            'data' => base64_encode($encrypted),
            'iv' => base64_encode($iv),
            'timestamp' => time(),
        ];
    }

    /**
     * Decrypt using AES-256-CBC
     */
    protected function decryptAESCBC(array $encryptedData, string $keyId): string
    {
        $key = $this->getSymmetricKey($keyId);
        $encrypted = base64_decode($encryptedData['data']);
        $iv = base64_decode($encryptedData['iv']);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        
        if ($decrypted === false) {
            throw new \Exception('AES-CBC decryption failed');
        }
        
        return $decrypted;
    }

    /**
     * Encrypt using RSA-OAEP
     */
    protected function encryptRSA(string $data, string $keyId): array
    {
        $publicKey = $this->getPublicKey($keyId);
        $encrypted = '';
        
        if (openssl_public_encrypt($data, $encrypted, $publicKey, OPENSSL_PKCS1_OAEP_PADDING)) {
            return [
                'cipher' => 'RSA-OAEP',
                'key_id' => $keyId,
                'data' => base64_encode($encrypted),
                'timestamp' => time(),
            ];
        } else {
            throw new \Exception('RSA encryption failed');
        }
    }

    /**
     * Decrypt using RSA-OAEP
     */
    protected function decryptRSA(array $encryptedData, string $keyId): string
    {
        $privateKey = $this->getPrivateKey($keyId);
        $encrypted = base64_decode($encryptedData['data']);
        $decrypted = '';
        
        if (openssl_private_decrypt($encrypted, $decrypted, $privateKey, OPENSSL_PKCS1_OAEP_PADDING)) {
            return $decrypted;
        } else {
            throw new \Exception('RSA decryption failed');
        }
    }

    /**
     * Encrypt using ChaCha20-Poly1305
     */
    protected function encryptChaCha20(string $data, string $keyId): array
    {
        if (!function_exists('sodium_crypto_aead_chacha20poly1305_encrypt')) {
            throw new \Exception('Sodium extension not available for ChaCha20-Poly1305');
        }
        
        $key = $this->getSymmetricKey($keyId, 32);
        $nonce = random_bytes(SODIUM_CRYPTO_AEAD_CHACHA20POLY1305_NPUBBYTES);
        
        $encrypted = sodium_crypto_aead_chacha20poly1305_encrypt($data, '', $nonce, $key);
        
        return [
            'cipher' => 'ChaCha20-Poly1305',
            'key_id' => $keyId,
            'data' => base64_encode($encrypted),
            'nonce' => base64_encode($nonce),
            'timestamp' => time(),
        ];
    }

    /**
     * Decrypt using ChaCha20-Poly1305
     */
    protected function decryptChaCha20(array $encryptedData, string $keyId): string
    {
        if (!function_exists('sodium_crypto_aead_chacha20poly1305_decrypt')) {
            throw new \Exception('Sodium extension not available for ChaCha20-Poly1305');
        }
        
        $key = $this->getSymmetricKey($keyId, 32);
        $encrypted = base64_decode($encryptedData['data']);
        $nonce = base64_decode($encryptedData['nonce']);
        
        $decrypted = sodium_crypto_aead_chacha20poly1305_decrypt($encrypted, '', $nonce, $key);
        
        if ($decrypted === false) {
            throw new \Exception('ChaCha20-Poly1305 decryption failed');
        }
        
        return $decrypted;
    }

    /**
     * Initialize key store
     */
    protected function initializeKeyStore(): void
    {
        $this->keyStore = Cache::remember('encryption_keys', 3600, function () {
            return [
                'symmetric' => [],
                'asymmetric' => [],
            ];
        });
        
        // Ensure default keys exist
        if (!isset($this->keyStore['symmetric']['default'])) {
            $this->generateSymmetricKey('default');
        }
        
        if (!isset($this->keyStore['asymmetric']['default'])) {
            $this->generateAsymmetricKeyPair('default');
        }
    }

    /**
     * Generate symmetric key
     */
    protected function generateSymmetricKey(string $keyId, int $length = 32): void
    {
        $key = random_bytes($length);
        $this->keyStore['symmetric'][$keyId] = [
            'key' => base64_encode($key),
            'created_at' => time(),
            'algorithm' => 'AES-256',
        ];
        
        $this->saveKeyStore();
    }

    /**
     * Generate asymmetric key pair
     */
    protected function generateAsymmetricKeyPair(string $keyId): void
    {
        $config = [
            'digest_alg' => 'sha256',
            'private_key_bits' => $this->config['rsa_key_size'],
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];
        
        $resource = openssl_pkey_new($config);
        if (!$resource) {
            throw new \Exception('Failed to generate RSA key pair');
        }
        
        openssl_pkey_export($resource, $privateKey);
        $publicKey = openssl_pkey_get_details($resource)['key'];
        
        $this->keyStore['asymmetric'][$keyId] = [
            'private_key' => $privateKey,
            'public_key' => $publicKey,
            'created_at' => time(),
            'algorithm' => 'RSA-' . $this->config['rsa_key_size'],
        ];
        
        $this->saveKeyStore();
    }

    /**
     * Get symmetric key
     */
    protected function getSymmetricKey(string $keyId, int $expectedLength = 32): string
    {
        if (!isset($this->keyStore['symmetric'][$keyId])) {
            $this->generateSymmetricKey($keyId, $expectedLength);
        }
        
        $key = base64_decode($this->keyStore['symmetric'][$keyId]['key']);
        
        if (strlen($key) !== $expectedLength) {
            throw new \Exception("Key length mismatch for key ID: {$keyId}");
        }
        
        return $key;
    }

    /**
     * Get private key
     */
    protected function getPrivateKey(string $keyId): string
    {
        if (!isset($this->keyStore['asymmetric'][$keyId])) {
            $this->generateAsymmetricKeyPair($keyId);
        }
        
        return $this->keyStore['asymmetric'][$keyId]['private_key'];
    }

    /**
     * Get public key
     */
    protected function getPublicKey(string $keyId): string
    {
        if (!isset($this->keyStore['asymmetric'][$keyId])) {
            $this->generateAsymmetricKeyPair($keyId);
        }
        
        return $this->keyStore['asymmetric'][$keyId]['public_key'];
    }

    /**
     * Save key store to cache
     */
    protected function saveKeyStore(): void
    {
        Cache::put('encryption_keys', $this->keyStore, 86400);
    }

    /**
     * Rotate encryption keys
     */
    public function rotateKeys(): void
    {
        foreach ($this->keyStore['symmetric'] as $keyId => $keyData) {
            if (time() - $keyData['created_at'] > $this->config['key_rotation_interval']) {
                $this->generateSymmetricKey($keyId);
                Log::info('Symmetric key rotated', ['key_id' => $keyId]);
            }
        }
        
        foreach ($this->keyStore['asymmetric'] as $keyId => $keyData) {
            if (time() - $keyData['created_at'] > $this->config['key_rotation_interval']) {
                $this->generateAsymmetricKeyPair($keyId);
                Log::info('Asymmetric key pair rotated', ['key_id' => $keyId]);
            }
        }
    }

    /**
     * Export public key for external use
     */
    public function exportPublicKey(string $keyId): string
    {
        return $this->getPublicKey($keyId);
    }

    /**
     * Import public key from external source
     */
    public function importPublicKey(string $keyId, string $publicKey): void
    {
        // Validate the public key
        $resource = openssl_pkey_get_public($publicKey);
        if (!$resource) {
            throw new \Exception('Invalid public key format');
        }
        
        $this->keyStore['asymmetric'][$keyId] = [
            'public_key' => $publicKey,
            'created_at' => time(),
            'algorithm' => 'RSA-imported',
            'imported' => true,
        ];
        
        $this->saveKeyStore();
    }
}
