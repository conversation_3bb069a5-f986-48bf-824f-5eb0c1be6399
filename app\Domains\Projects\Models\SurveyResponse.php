<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج إجابة الاستبيان - Survey Response
 */
class SurveyResponse extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'survey_id',
        'respondent_id',
        'responses',
        'submitted_at',
        'ip_address',
    ];

    protected $casts = [
        'responses' => 'array',
        'submitted_at' => 'datetime',
    ];

    public function survey(): BelongsTo
    {
        return $this->belongsTo(ProjectSurvey::class, 'survey_id');
    }

    public function respondent(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'respondent_id');
    }
}
