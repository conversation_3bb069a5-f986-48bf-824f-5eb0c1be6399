<?php

namespace App\Domains\Integration\Contracts;

use App\Domains\Integration\Models\ApiEndpoint;

/**
 * Transformation Interface
 * Defines the contract for data transformation pipelines
 */
interface TransformationInterface
{
    /**
     * Transform data using the pipeline
     *
     * @param array $data The data to transform
     * @param array $config Transformation configuration
     * @param ApiEndpoint $endpoint The API endpoint context
     * @param array $context Additional context data
     * @return array Transformed data
     */
    public function transform(array $data, array $config, ApiEndpoint $endpoint, array $context = []): array;

    /**
     * Validate data against schema
     *
     * @param array $data The data to validate
     * @param array $schema Validation schema
     * @return array Validation result
     */
    public function validate(array $data, array $schema): array;

    /**
     * Process data through transformation steps
     *
     * @param array $data The data to process
     * @param array $steps Processing steps
     * @param array $context Processing context
     * @return array Processed data
     */
    public function process(array $data, array $steps, array $context = []): array;
}
