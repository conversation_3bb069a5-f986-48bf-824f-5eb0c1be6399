<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج موافقة المشروع - Project Approval
 * يدير عمليات الموافقة في المشاريع
 */
class ProjectApproval extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'workflow_id',
        'project_id',
        'requester_id',
        'subject',
        'description',
        'priority',
        'status',
        'current_step',
        'data',
        'due_date',
        'completed_at',
        'escalated_to',
        'escalation_reason',
        'escalated_at',
        'rejection_reason',
        'metadata',
    ];

    protected $casts = [
        'data' => 'array',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'escalated_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع سير العمل
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'workflow_id');
    }

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع مقدم الطلب
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'requester_id');
    }

    /**
     * العلاقة مع المصعد إليه
     */
    public function escalatedToUser(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'escalated_to');
    }

    /**
     * العلاقة مع خطوات الموافقة
     */
    public function approvalSteps(): HasMany
    {
        return $this->hasMany(ApprovalStep::class, 'approval_id');
    }

    /**
     * التحقق من التأخير
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['APPROVED', 'REJECTED', 'CANCELLED']);
    }

    /**
     * الحصول على الخطوة الحالية
     */
    public function getCurrentStepAttribute(): ?WorkflowStep
    {
        return $this->workflow->steps()
                             ->where('step_number', $this->current_step)
                             ->first();
    }
}
