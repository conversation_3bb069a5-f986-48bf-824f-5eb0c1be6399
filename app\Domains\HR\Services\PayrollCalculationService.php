<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\Payslip;
use App\Domains\HR\Models\AttendanceRecord;
use App\Domains\HR\Models\LeaveRequest;
use App\Domains\Taxation\Services\DynamicTaxEngine;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة حساب الرواتب المتقدمة
 * تدعم أنظمة الضمان الاجتماعي متعددة الدول والحسابات المعقدة
 */
class PayrollCalculationService
{
    protected DynamicTaxEngine $taxEngine;
    protected array $countryConfigs;

    public function __construct(DynamicTaxEngine $taxEngine)
    {
        $this->taxEngine = $taxEngine;
        $this->loadCountryConfigurations();
    }

    /**
     * تحميل إعدادات الدول
     */
    protected function loadCountryConfigurations(): void
    {
        $this->countryConfigs = [
            'MA' => [ // المغرب
                'social_security' => [
                    'cnss' => ['employee' => 4.48, 'employer' => 16.46, 'max_salary' => 6000],
                    'amo' => ['employee' => 2.26, 'employer' => 3.39, 'max_salary' => null],
                ],
                'income_tax_brackets' => [
                    ['min' => 0, 'max' => 30000, 'rate' => 0],
                    ['min' => 30000, 'max' => 50000, 'rate' => 10],
                    ['min' => 50000, 'max' => 60000, 'rate' => 20],
                    ['min' => 60000, 'max' => 80000, 'rate' => 30],
                    ['min' => 80000, 'max' => 180000, 'rate' => 34],
                    ['min' => 180000, 'max' => PHP_FLOAT_MAX, 'rate' => 38],
                ],
            ],
            'SA' => [ // السعودية
                'social_security' => [
                    'gosi' => ['employee' => 10, 'employer' => 12, 'max_salary' => 45000],
                ],
                'income_tax_brackets' => [], // لا توجد ضريبة دخل للسعوديين
            ],
            'AE' => [ // الإمارات
                'social_security' => [
                    'pension' => ['employee' => 5, 'employer' => 12.5, 'max_salary' => null],
                ],
                'income_tax_brackets' => [], // لا توجد ضريبة دخل
            ],
            'EG' => [ // مصر
                'social_security' => [
                    'insurance' => ['employee' => 14, 'employer' => 18.75, 'max_salary' => 8800],
                ],
                'income_tax_brackets' => [
                    ['min' => 0, 'max' => 15000, 'rate' => 0],
                    ['min' => 15000, 'max' => 30000, 'rate' => 10],
                    ['min' => 30000, 'max' => 45000, 'rate' => 15],
                    ['min' => 45000, 'max' => 60000, 'rate' => 20],
                    ['min' => 60000, 'max' => PHP_FLOAT_MAX, 'rate' => 25],
                ],
            ],
        ];
    }

    /**
     * حساب كشف راتب موظف
     */
    public function calculatePayslip(Employee $employee, int $month, int $year): Payslip
    {
        $period = \Carbon\Carbon::create($year, $month, 1);
        $startDate = $period->copy()->startOfMonth();
        $endDate = $period->copy()->endOfMonth();

        // التحقق من وجود كشف راتب مسبق
        $existingPayslip = Payslip::where('employee_id', $employee->id)
            ->where('pay_period_start', $startDate)
            ->where('pay_period_end', $endDate)
            ->first();

        if ($existingPayslip && $existingPayslip->status === 'FINALIZED') {
            return $existingPayslip;
        }

        DB::beginTransaction();
        try {
            // حساب المكونات
            $earnings = $this->calculateEarnings($employee, $startDate, $endDate);
            $deductions = $this->calculateDeductions($employee, $earnings['gross_salary'], $startDate, $endDate);
            $benefits = $this->calculateBenefits($employee, $startDate, $endDate);

            $grossSalary = $earnings['gross_salary'];
            $totalDeductions = array_sum($deductions);
            $totalBenefits = array_sum($benefits);
            $netSalary = $grossSalary - $totalDeductions + $totalBenefits;

            // إنشاء أو تحديث كشف الراتب
            $payslip = Payslip::updateOrCreate(
                [
                    'employee_id' => $employee->id,
                    'pay_period_start' => $startDate,
                    'pay_period_end' => $endDate,
                ],
                [
                    'basic_salary' => $employee->basic_salary,
                    'gross_salary' => $grossSalary,
                    'net_salary' => $netSalary,
                    'total_deductions' => $totalDeductions,
                    'total_benefits' => $totalBenefits,
                    'earnings_breakdown' => $earnings,
                    'deductions_breakdown' => $deductions,
                    'benefits_breakdown' => $benefits,
                    'currency' => $employee->currency ?? 'MAD',
                    'status' => 'CALCULATED',
                    'calculated_at' => now(),
                ]
            );

            DB::commit();
            return $payslip;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('خطأ في حساب كشف الراتب', [
                'employee_id' => $employee->id,
                'period' => $period->format('Y-m'),
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * حساب الأرباح والمكاسب
     */
    protected function calculateEarnings(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        $basicSalary = $employee->basic_salary;
        $workingDays = $this->getWorkingDaysInMonth($startDate, $endDate);
        $attendedDays = $this->getAttendedDays($employee, $startDate, $endDate);
        
        // حساب الراتب الأساسي المتناسب
        $proportionalBasicSalary = ($basicSalary / $workingDays) * $attendedDays;

        // حساب الساعات الإضافية
        $overtimeHours = $this->getOvertimeHours($employee, $startDate, $endDate);
        $overtimeRate = $this->getOvertimeRate($employee);
        $overtimePay = $overtimeHours * $overtimeRate;

        // العلاوات الثابتة
        $allowances = $this->getEmployeeAllowances($employee);

        // المكافآت والحوافز
        $bonuses = $this->getEmployeeBonuses($employee, $startDate, $endDate);

        // بدل النقل
        $transportAllowance = $allowances['transport'] ?? 0;

        // بدل السكن
        $housingAllowance = $allowances['housing'] ?? 0;

        // بدل الطعام
        $mealAllowance = $allowances['meal'] ?? 0;

        $grossSalary = $proportionalBasicSalary + $overtimePay + $transportAllowance + 
                      $housingAllowance + $mealAllowance + $bonuses;

        return [
            'basic_salary' => $proportionalBasicSalary,
            'overtime_pay' => $overtimePay,
            'overtime_hours' => $overtimeHours,
            'transport_allowance' => $transportAllowance,
            'housing_allowance' => $housingAllowance,
            'meal_allowance' => $mealAllowance,
            'bonuses' => $bonuses,
            'gross_salary' => $grossSalary,
            'working_days' => $workingDays,
            'attended_days' => $attendedDays,
        ];
    }

    /**
     * حساب الاستقطاعات
     */
    protected function calculateDeductions(Employee $employee, float $grossSalary, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        $country = $employee->company->country ?? 'MA';
        $deductions = [];

        // الضمان الاجتماعي
        $socialSecurityDeductions = $this->calculateSocialSecurityDeductions($employee, $grossSalary, $country);
        $deductions = array_merge($deductions, $socialSecurityDeductions);

        // ضريبة الدخل
        $incomeTax = $this->calculateIncomeTax($employee, $grossSalary, $country);
        $deductions['income_tax'] = $incomeTax;

        // استقطاعات الغياب
        $absenceDeductions = $this->calculateAbsenceDeductions($employee, $startDate, $endDate);
        $deductions['absence_deductions'] = $absenceDeductions;

        // استقطاعات التأخير
        $latenessDeductions = $this->calculateLatenessDeductions($employee, $startDate, $endDate);
        $deductions['lateness_deductions'] = $latenessDeductions;

        // القروض والسلف
        $loanDeductions = $this->calculateLoanDeductions($employee, $startDate, $endDate);
        $deductions['loan_deductions'] = $loanDeductions;

        // التأمين الطبي
        $medicalInsurance = $this->getEmployeeMedicalInsurance($employee);
        $deductions['medical_insurance'] = $medicalInsurance;

        // استقطاعات أخرى
        $otherDeductions = $this->getOtherDeductions($employee, $startDate, $endDate);
        $deductions = array_merge($deductions, $otherDeductions);

        return $deductions;
    }

    /**
     * حساب استقطاعات الضمان الاجتماعي
     */
    protected function calculateSocialSecurityDeductions(Employee $employee, float $grossSalary, string $country): array
    {
        $config = $this->countryConfigs[$country]['social_security'] ?? [];
        $deductions = [];

        foreach ($config as $scheme => $rates) {
            $maxSalary = $rates['max_salary'] ?? PHP_FLOAT_MAX;
            $applicableSalary = min($grossSalary, $maxSalary);
            
            $employeeRate = $rates['employee'] ?? 0;
            $deductionAmount = $applicableSalary * ($employeeRate / 100);
            
            $deductions[$scheme . '_employee'] = round($deductionAmount, 2);
        }

        return $deductions;
    }

    /**
     * حساب ضريبة الدخل
     */
    protected function calculateIncomeTax(Employee $employee, float $grossSalary, string $country): float
    {
        $brackets = $this->countryConfigs[$country]['income_tax_brackets'] ?? [];
        
        if (empty($brackets)) {
            return 0; // لا توجد ضريبة دخل
        }

        // حساب الراتب السنوي
        $annualSalary = $grossSalary * 12;
        $totalTax = 0;
        $remainingIncome = $annualSalary;

        foreach ($brackets as $bracket) {
            $min = $bracket['min'];
            $max = $bracket['max'];
            $rate = $bracket['rate'];

            if ($remainingIncome <= 0) break;

            $taxableInBracket = min($remainingIncome, $max - $min);
            $taxInBracket = $taxableInBracket * ($rate / 100);

            $totalTax += $taxInBracket;
            $remainingIncome -= $taxableInBracket;
        }

        // تحويل للضريبة الشهرية
        return round($totalTax / 12, 2);
    }

    /**
     * حساب المزايا
     */
    protected function calculateBenefits(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        $benefits = [];

        // مكافأة الحضور
        $attendanceBonus = $this->calculateAttendanceBonus($employee, $startDate, $endDate);
        $benefits['attendance_bonus'] = $attendanceBonus;

        // مكافأة الأداء
        $performanceBonus = $this->getPerformanceBonus($employee, $startDate, $endDate);
        $benefits['performance_bonus'] = $performanceBonus;

        // مزايا أخرى
        $otherBenefits = $this->getOtherBenefits($employee, $startDate, $endDate);
        $benefits = array_merge($benefits, $otherBenefits);

        return $benefits;
    }

    /**
     * الحصول على أيام العمل في الشهر
     */
    protected function getWorkingDaysInMonth(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int
    {
        $workingDays = 0;
        $current = $startDate->copy();

        while ($current <= $endDate) {
            if (!$this->isWeekend($current) && !$this->isPublicHoliday($current)) {
                $workingDays++;
            }
            $current->addDay();
        }

        return $workingDays;
    }

    /**
     * الحصول على أيام الحضور الفعلية
     */
    protected function getAttendedDays(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int
    {
        return AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->whereIn('status', ['PRESENT', 'LATE', 'EARLY_DEPARTURE'])
            ->count();
    }

    /**
     * الحصول على ساعات العمل الإضافية
     */
    protected function getOvertimeHours(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        return AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->sum('overtime_hours') ?? 0;
    }

    /**
     * الحصول على معدل الساعة الإضافية
     */
    protected function getOvertimeRate(Employee $employee): float
    {
        $hourlyRate = $employee->basic_salary / (30 * 8); // افتراض 8 ساعات يومياً
        return $hourlyRate * 1.5; // 150% من المعدل العادي
    }

    /**
     * حساب استقطاعات الغياب
     */
    protected function calculateAbsenceDeductions(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        $absentDays = AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->where('status', 'ABSENT')
            ->count();

        $dailyRate = $employee->basic_salary / 30;
        return $absentDays * $dailyRate;
    }

    /**
     * حساب استقطاعات التأخير
     */
    protected function calculateLatenessDeductions(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        $totalLateMinutes = AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_late', true)
            ->sum('late_minutes') ?? 0;

        $hourlyRate = $employee->basic_salary / (30 * 8);
        $minuteRate = $hourlyRate / 60;

        return $totalLateMinutes * $minuteRate;
    }

    /**
     * حساب مكافأة الحضور
     */
    protected function calculateAttendanceBonus(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        $attendanceRate = $this->getAttendanceRate($employee, $startDate, $endDate);
        
        if ($attendanceRate >= 100) {
            return $employee->basic_salary * 0.05; // 5% مكافأة حضور كامل
        } elseif ($attendanceRate >= 95) {
            return $employee->basic_salary * 0.03; // 3% مكافأة حضور ممتاز
        }

        return 0;
    }

    /**
     * حساب معدل الحضور
     */
    protected function getAttendanceRate(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        $workingDays = $this->getWorkingDaysInMonth($startDate, $endDate);
        $attendedDays = $this->getAttendedDays($employee, $startDate, $endDate);

        if ($workingDays == 0) return 100;

        return ($attendedDays / $workingDays) * 100;
    }

    /**
     * الحصول على علاوات الموظف
     */
    protected function getEmployeeAllowances(Employee $employee): array
    {
        // يمكن تطوير هذا لجلب العلاوات من قاعدة البيانات
        return $employee->metadata['allowances'] ?? [
            'transport' => 0,
            'housing' => 0,
            'meal' => 0,
        ];
    }

    /**
     * الحصول على مكافآت الموظف
     */
    protected function getEmployeeBonuses(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        // يمكن تطوير هذا لجلب المكافآت من قاعدة البيانات
        return 0;
    }

    /**
     * حساب استقطاعات القروض
     */
    protected function calculateLoanDeductions(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        // يمكن تطوير هذا لحساب أقساط القروض
        return 0;
    }

    /**
     * الحصول على التأمين الطبي
     */
    protected function getEmployeeMedicalInsurance(Employee $employee): float
    {
        return $employee->metadata['medical_insurance'] ?? 0;
    }

    /**
     * الحصول على مكافأة الأداء
     */
    protected function getPerformanceBonus(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): float
    {
        // يمكن تطوير هذا لحساب مكافآت الأداء
        return 0;
    }

    /**
     * الحصول على استقطاعات أخرى
     */
    protected function getOtherDeductions(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        return [];
    }

    /**
     * الحصول على مزايا أخرى
     */
    protected function getOtherBenefits(Employee $employee, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): array
    {
        return [];
    }

    /**
     * التحقق من كون اليوم عطلة أسبوعية
     */
    protected function isWeekend(\Carbon\Carbon $date): bool
    {
        return in_array($date->dayOfWeek, [5, 6]); // الجمعة والسبت
    }

    /**
     * التحقق من كون اليوم عطلة رسمية
     */
    protected function isPublicHoliday(\Carbon\Carbon $date): bool
    {
        $holidays = config('hr.public_holidays', []);
        return in_array($date->format('m-d'), $holidays);
    }

    /**
     * حساب الراتب الإجمالي
     */
    public function calculateGrossSalary(Employee $employee): float
    {
        $allowances = $this->getEmployeeAllowances($employee);
        return $employee->basic_salary + array_sum($allowances);
    }

    /**
     * حساب الراتب الصافي
     */
    public function calculateNetSalary(Employee $employee): float
    {
        $grossSalary = $this->calculateGrossSalary($employee);
        $country = $employee->company->country ?? 'MA';
        
        $socialSecurityDeductions = $this->calculateSocialSecurityDeductions($employee, $grossSalary, $country);
        $incomeTax = $this->calculateIncomeTax($employee, $grossSalary, $country);
        
        $totalDeductions = array_sum($socialSecurityDeductions) + $incomeTax;
        
        return $grossSalary - $totalDeductions;
    }
}
