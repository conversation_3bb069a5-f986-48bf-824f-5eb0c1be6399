<?php

use Illuminate\Support\Facades\Route;
use App\Domains\CRM\Controllers\CustomerController;
use App\Domains\CRM\Controllers\OpportunityController;
use App\Domains\CRM\Controllers\CustomerInteractionController;
use App\Domains\CRM\Controllers\MarketingCampaignController;
use App\Domains\CRM\Controllers\CustomerSegmentController;
use App\Domains\CRM\Controllers\OpportunityActivityController;
use App\Domains\CRM\Controllers\OpportunityTaskController;
use App\Domains\CRM\Controllers\CustomerSatisfactionSurveyController;
use App\Domains\CRM\Controllers\CampaignTemplateController;
use App\Domains\CRM\Controllers\LeadSourceController;
use App\Domains\CRM\Controllers\CRMAnalyticsController;
use App\Domains\CRM\Controllers\CRMReportingController;

/*
|--------------------------------------------------------------------------
| CRM API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام إدارة العملاء
|
*/

// Customer Management Routes
Route::apiResource('customers', CustomerController::class);
Route::prefix('customers')->group(function () {
    Route::get('{customer}/profile-360', [CustomerController::class, 'getProfile360']);
    Route::get('{customer}/interactions', [CustomerController::class, 'getInteractions']);
    Route::get('{customer}/opportunities', [CustomerController::class, 'getOpportunities']);
    Route::get('{customer}/analytics', [CustomerController::class, 'getAnalytics']);
    Route::post('{customer}/segments', [CustomerController::class, 'addToSegment']);
    Route::delete('{customer}/segments/{segment}', [CustomerController::class, 'removeFromSegment']);
    Route::post('{customer}/merge', [CustomerController::class, 'mergeCustomers']);
    Route::get('{customer}/lifecycle', [CustomerController::class, 'getLifecycleStage']);
    Route::post('{customer}/calculate-clv', [CustomerController::class, 'calculateCLV']);
    Route::post('{customer}/calculate-churn-risk', [CustomerController::class, 'calculateChurnRisk']);
});

// Opportunity Management Routes
Route::apiResource('opportunities', OpportunityController::class);
Route::prefix('opportunities')->group(function () {
    Route::post('{opportunity}/stage', [OpportunityController::class, 'updateStage']);
    Route::post('{opportunity}/probability', [OpportunityController::class, 'updateProbability']);
    Route::get('{opportunity}/activities', [OpportunityController::class, 'getActivities']);
    Route::get('{opportunity}/tasks', [OpportunityController::class, 'getTasks']);
    Route::post('{opportunity}/clone', [OpportunityController::class, 'cloneOpportunity']);
    Route::get('pipeline/analysis', [OpportunityController::class, 'getPipelineAnalysis']);
    Route::get('forecasting', [OpportunityController::class, 'getForecasting']);
});

// Customer Interactions Routes
Route::apiResource('interactions', CustomerInteractionController::class);
Route::prefix('interactions')->group(function () {
    Route::post('{interaction}/sentiment', [CustomerInteractionController::class, 'analyzeSentiment']);
    Route::post('{interaction}/follow-up', [CustomerInteractionController::class, 'createFollowUp']);
    Route::get('analytics', [CustomerInteractionController::class, 'getAnalytics']);
});

// Marketing Campaigns Routes
Route::apiResource('campaigns', MarketingCampaignController::class);
Route::prefix('campaigns')->group(function () {
    Route::post('{campaign}/send', [MarketingCampaignController::class, 'sendCampaign']);
    Route::post('{campaign}/test', [MarketingCampaignController::class, 'sendTestCampaign']);
    Route::get('{campaign}/analytics', [MarketingCampaignController::class, 'getAnalytics']);
    Route::post('{campaign}/clone', [MarketingCampaignController::class, 'cloneCampaign']);
    Route::get('{campaign}/recipients', [MarketingCampaignController::class, 'getRecipients']);
    Route::post('automation/execute', [MarketingCampaignController::class, 'executeAutomation']);
});

// Customer Segments Routes
Route::apiResource('segments', CustomerSegmentController::class);
Route::prefix('segments')->group(function () {
    Route::post('{segment}/recalculate', [CustomerSegmentController::class, 'recalculate']);
    Route::get('{segment}/customers', [CustomerSegmentController::class, 'getCustomers']);
    Route::post('{segment}/export', [CustomerSegmentController::class, 'exportCustomers']);
    Route::get('rfm-analysis', [CustomerSegmentController::class, 'getRFMAnalysis']);
    Route::post('behavioral/create', [CustomerSegmentController::class, 'createBehavioralSegments']);
});

// Opportunity Activities Routes
Route::apiResource('opportunity-activities', OpportunityActivityController::class);
Route::prefix('opportunity-activities')->group(function () {
    Route::post('{activity}/complete', [OpportunityActivityController::class, 'markAsCompleted']);
    Route::post('{activity}/follow-up', [OpportunityActivityController::class, 'scheduleFollowUp']);
    Route::get('overdue', [OpportunityActivityController::class, 'getOverdue']);
    Route::get('analytics', [OpportunityActivityController::class, 'getAnalytics']);
});

// Opportunity Tasks Routes
Route::apiResource('opportunity-tasks', OpportunityTaskController::class);
Route::prefix('opportunity-tasks')->group(function () {
    Route::post('{task}/start', [OpportunityTaskController::class, 'startTask']);
    Route::post('{task}/complete', [OpportunityTaskController::class, 'completeTask']);
    Route::post('{task}/progress', [OpportunityTaskController::class, 'updateProgress']);
    Route::get('overdue', [OpportunityTaskController::class, 'getOverdue']);
    Route::get('due-today', [OpportunityTaskController::class, 'getDueToday']);
});

// Customer Satisfaction Surveys Routes
Route::apiResource('satisfaction-surveys', CustomerSatisfactionSurveyController::class);
Route::prefix('satisfaction-surveys')->group(function () {
    Route::post('{survey}/send', [CustomerSatisfactionSurveyController::class, 'sendSurvey']);
    Route::post('{survey}/complete', [CustomerSatisfactionSurveyController::class, 'completeSurvey']);
    Route::get('analytics', [CustomerSatisfactionSurveyController::class, 'getAnalytics']);
    Route::get('nps-analysis', [CustomerSatisfactionSurveyController::class, 'getNPSAnalysis']);
    Route::post('automation/trigger', [CustomerSatisfactionSurveyController::class, 'triggerAutomatedSurveys']);
});

// Campaign Templates Routes
Route::apiResource('campaign-templates', CampaignTemplateController::class);
Route::prefix('campaign-templates')->group(function () {
    Route::post('{template}/duplicate', [CampaignTemplateController::class, 'duplicate']);
    Route::post('{template}/create-campaign', [CampaignTemplateController::class, 'createCampaign']);
    Route::get('{template}/preview', [CampaignTemplateController::class, 'getPreview']);
    Route::post('{template}/validate', [CampaignTemplateController::class, 'validateTemplate']);
    Route::post('{template}/set-default', [CampaignTemplateController::class, 'setAsDefault']);
});

// Lead Sources Routes
Route::apiResource('lead-sources', LeadSourceController::class);
Route::prefix('lead-sources')->group(function () {
    Route::post('{source}/update-stats', [LeadSourceController::class, 'updateStats']);
    Route::get('{source}/performance', [LeadSourceController::class, 'getPerformanceAnalysis']);
    Route::get('{source}/comparison', [LeadSourceController::class, 'getPerformanceComparison']);
    Route::post('{source}/generate-report', [LeadSourceController::class, 'generatePerformanceReport']);
    Route::get('analytics', [LeadSourceController::class, 'getAnalytics']);
});

// CRM Analytics Routes
Route::prefix('analytics')->group(function () {
    Route::get('dashboard', [CRMAnalyticsController::class, 'getMainDashboard']);
    Route::get('customer-lifecycle', [CRMAnalyticsController::class, 'getCustomerLifecycleAnalysis']);
    Route::get('rfm-analysis', [CRMAnalyticsController::class, 'getRFMAnalysis']);
    Route::get('behavioral-patterns', [CRMAnalyticsController::class, 'getBehavioralPatterns']);
    Route::get('cohort-analysis', [CRMAnalyticsController::class, 'getCohortAnalysis']);
    Route::get('clv-analysis', [CRMAnalyticsController::class, 'getCLVAnalysis']);
    Route::get('churn-analysis', [CRMAnalyticsController::class, 'getChurnAnalysis']);
    Route::get('campaign-effectiveness', [CRMAnalyticsController::class, 'getCampaignEffectiveness']);
    Route::get('satisfaction-metrics', [CRMAnalyticsController::class, 'getSatisfactionMetrics']);
    Route::get('predictive-insights', [CRMAnalyticsController::class, 'getPredictiveInsights']);
});

// CRM Reporting Routes
Route::prefix('reports')->group(function () {
    Route::get('executive-dashboard', [CRMReportingController::class, 'getExecutiveDashboard']);
    Route::get('sales-report', [CRMReportingController::class, 'getSalesReport']);
    Route::get('marketing-report', [CRMReportingController::class, 'getMarketingReport']);
    Route::get('customer-satisfaction-report', [CRMReportingController::class, 'getCustomerSatisfactionReport']);
    Route::get('customer-analysis-report', [CRMReportingController::class, 'getCustomerAnalysisReport']);
    Route::get('team-performance-report', [CRMReportingController::class, 'getTeamPerformanceReport']);
    Route::post('custom-report', [CRMReportingController::class, 'generateCustomReport']);
    Route::post('export', [CRMReportingController::class, 'exportReport']);
});

// Automation Routes
Route::prefix('automation')->group(function () {
    Route::post('sales/execute', [CustomerController::class, 'executeSalesAutomation']);
    Route::post('marketing/execute', [MarketingCampaignController::class, 'executeMarketingAutomation']);
    Route::post('segments/update', [CustomerSegmentController::class, 'updateDynamicSegments']);
    Route::post('integration/sync', [CustomerController::class, 'syncIntegrations']);
});

// Search and Filters
Route::prefix('search')->group(function () {
    Route::get('customers', [CustomerController::class, 'search']);
    Route::get('opportunities', [OpportunityController::class, 'search']);
    Route::get('interactions', [CustomerInteractionController::class, 'search']);
    Route::get('campaigns', [MarketingCampaignController::class, 'search']);
    Route::get('global', [CustomerController::class, 'globalSearch']);
});

// Bulk Operations
Route::prefix('bulk')->group(function () {
    Route::post('customers/update', [CustomerController::class, 'bulkUpdate']);
    Route::post('customers/delete', [CustomerController::class, 'bulkDelete']);
    Route::post('opportunities/update-stage', [OpportunityController::class, 'bulkUpdateStage']);
    Route::post('campaigns/send', [MarketingCampaignController::class, 'bulkSendCampaigns']);
    Route::post('segments/assign', [CustomerSegmentController::class, 'bulkAssignToSegment']);
});

// Import/Export Routes
Route::prefix('import-export')->group(function () {
    Route::post('customers/import', [CustomerController::class, 'importCustomers']);
    Route::get('customers/export', [CustomerController::class, 'exportCustomers']);
    Route::post('opportunities/import', [OpportunityController::class, 'importOpportunities']);
    Route::get('opportunities/export', [OpportunityController::class, 'exportOpportunities']);
    Route::get('interactions/export', [CustomerInteractionController::class, 'exportInteractions']);
});
