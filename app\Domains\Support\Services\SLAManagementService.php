<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\Ticket;
use App\Domains\Support\Models\TicketSlaLog;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * خدمة إدارة SLA - SLA Management Service
 */
class SLAManagementService
{
    protected array $slaSettings;

    public function __construct()
    {
        $this->slaSettings = config('support.sla');
    }

    /**
     * تحديد SLA للتذكرة
     */
    public function setSLA(Ticket $ticket): void
    {
        $priority = $ticket->priority;
        $prioritySettings = config("support.priority_levels.{$priority}");

        if (!$prioritySettings) {
            return;
        }

        $responseTime = $prioritySettings['sla_response_minutes'];
        $resolutionTime = $prioritySettings['sla_resolution_hours'] * 60; // تحويل لدقائق

        $dueAt = $this->calculateSLADueTime($ticket->created_at, $resolutionTime);

        $ticket->update([
            'sla_due_at' => $dueAt,
            'sla_response_due_at' => $this->calculateSLADueTime($ticket->created_at, $responseTime),
        ]);

        // تسجيل SLA
        $this->logSLAEvent($ticket, 'sla_set', [
            'response_time_minutes' => $responseTime,
            'resolution_time_minutes' => $resolutionTime,
            'due_at' => $dueAt,
        ]);
    }

    /**
     * إعادة حساب SLA عند تغيير الأولوية
     */
    public function recalculateSLA(Ticket $ticket): void
    {
        $this->setSLA($ticket);
        
        $this->logSLAEvent($ticket, 'sla_recalculated', [
            'reason' => 'priority_changed',
            'new_priority' => $ticket->priority,
        ]);
    }

    /**
     * حساب وقت استحقاق SLA مع مراعاة ساعات العمل
     */
    protected function calculateSLADueTime(Carbon $startTime, int $minutes): Carbon
    {
        if (!$this->slaSettings['business_hours_only']) {
            return $startTime->copy()->addMinutes($minutes);
        }

        $dueTime = $startTime->copy();
        $remainingMinutes = $minutes;

        while ($remainingMinutes > 0) {
            // التحقق من يوم العمل
            if ($this->isWorkingDay($dueTime)) {
                $workingHours = $this->getWorkingHours($dueTime);
                
                if ($workingHours) {
                    [$startHour, $endHour] = $workingHours;
                    $dayStart = $dueTime->copy()->setTimeFromTimeString($startHour);
                    $dayEnd = $dueTime->copy()->setTimeFromTimeString($endHour);

                    // إذا كان الوقت الحالي قبل بداية العمل
                    if ($dueTime->lt($dayStart)) {
                        $dueTime = $dayStart->copy();
                    }

                    // حساب الدقائق المتاحة في هذا اليوم
                    $availableMinutes = $dayEnd->diffInMinutes($dueTime);
                    
                    if ($remainingMinutes <= $availableMinutes) {
                        // يمكن إنهاء SLA في نفس اليوم
                        return $dueTime->addMinutes($remainingMinutes);
                    } else {
                        // الانتقال لليوم التالي
                        $remainingMinutes -= $availableMinutes;
                        $dueTime = $dueTime->addDay()->startOfDay();
                    }
                } else {
                    // يوم عطلة، الانتقال لليوم التالي
                    $dueTime = $dueTime->addDay()->startOfDay();
                }
            } else {
                // يوم عطلة، الانتقال لليوم التالي
                $dueTime = $dueTime->addDay()->startOfDay();
            }
        }

        return $dueTime;
    }

    /**
     * التحقق من انتهاك SLA
     */
    public function checkSLABreach(Ticket $ticket): array
    {
        $breaches = [];

        // فحص SLA الاستجابة
        if ($ticket->sla_response_due_at && !$ticket->first_response_at) {
            if (now()->isAfter($ticket->sla_response_due_at)) {
                $breaches[] = [
                    'type' => 'response',
                    'due_at' => $ticket->sla_response_due_at,
                    'breach_minutes' => now()->diffInMinutes($ticket->sla_response_due_at),
                ];
            }
        }

        // فحص SLA الحل
        if ($ticket->sla_due_at && !$ticket->resolved_at) {
            if (now()->isAfter($ticket->sla_due_at)) {
                $breaches[] = [
                    'type' => 'resolution',
                    'due_at' => $ticket->sla_due_at,
                    'breach_minutes' => now()->diffInMinutes($ticket->sla_due_at),
                ];
            }
        }

        return $breaches;
    }

    /**
     * إرسال تحذيرات SLA
     */
    public function sendSLAWarnings(): void
    {
        $warningThreshold = $this->slaSettings['warning_threshold_percentage'] / 100;

        $tickets = Ticket::whereNotIn('status', ['resolved', 'closed'])
            ->whereNotNull('sla_due_at')
            ->get();

        foreach ($tickets as $ticket) {
            $totalTime = $ticket->created_at->diffInMinutes($ticket->sla_due_at);
            $elapsedTime = $ticket->created_at->diffInMinutes(now());
            $progress = $elapsedTime / $totalTime;

            if ($progress >= $warningThreshold && !$this->hasRecentWarning($ticket)) {
                $this->sendSLAWarning($ticket, $progress);
            }
        }
    }

    /**
     * الحصول على مقاييس أداء SLA
     */
    public function getPerformanceMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        $tickets = Ticket::whereBetween('created_at', [$dateFrom, $dateTo])->get();

        return [
            'total_tickets' => $tickets->count(),
            'response_sla_compliance' => $this->calculateResponseSLACompliance($tickets),
            'resolution_sla_compliance' => $this->calculateResolutionSLACompliance($tickets),
            'average_response_time' => $this->calculateAverageResponseTime($tickets),
            'average_resolution_time' => $this->calculateAverageResolutionTime($tickets),
            'sla_breaches_by_priority' => $this->getSLABreachesByPriority($tickets),
            'sla_trends' => $this->getSLATrends($dateFrom, $dateTo),
        ];
    }

    /**
     * تسجيل حدث SLA
     */
    protected function logSLAEvent(Ticket $ticket, string $event, array $data = []): void
    {
        TicketSlaLog::create([
            'ticket_id' => $ticket->id,
            'event_type' => $event,
            'event_data' => $data,
            'created_at' => now(),
        ]);
    }

    /**
     * التحقق من يوم العمل
     */
    protected function isWorkingDay(Carbon $date): bool
    {
        $dayName = strtolower($date->format('l'));
        $businessHours = $this->slaSettings['business_hours'];

        // فحص العطل الرسمية
        if (in_array($date->format('Y-m-d'), $this->slaSettings['holidays'])) {
            return false;
        }

        return isset($businessHours[$dayName]) && $businessHours[$dayName] !== null;
    }

    /**
     * الحصول على ساعات العمل لليوم
     */
    protected function getWorkingHours(Carbon $date): ?array
    {
        $dayName = strtolower($date->format('l'));
        $businessHours = $this->slaSettings['business_hours'];

        return $businessHours[$dayName] ?? null;
    }

    /**
     * التحقق من وجود تحذير حديث
     */
    protected function hasRecentWarning(Ticket $ticket): bool
    {
        return TicketSlaLog::where('ticket_id', $ticket->id)
            ->where('event_type', 'sla_warning')
            ->where('created_at', '>=', now()->subHours(2))
            ->exists();
    }

    /**
     * إرسال تحذير SLA
     */
    protected function sendSLAWarning(Ticket $ticket, float $progress): void
    {
        $this->logSLAEvent($ticket, 'sla_warning', [
            'progress_percentage' => round($progress * 100, 2),
            'time_remaining_minutes' => now()->diffInMinutes($ticket->sla_due_at, false),
        ]);

        // إرسال الإشعارات (سيتم تنفيذها في خدمة الإشعارات)
        event(new \App\Events\SLAWarningTriggered($ticket, $progress));
    }

    // دوال حساب المقاييس
    protected function calculateResponseSLACompliance($tickets): float
    {
        $ticketsWithResponse = $tickets->whereNotNull('first_response_at');
        
        if ($ticketsWithResponse->isEmpty()) {
            return 100;
        }

        $compliantTickets = $ticketsWithResponse->filter(function ($ticket) {
            return $ticket->sla_response_due_at && 
                   $ticket->first_response_at->lte($ticket->sla_response_due_at);
        });

        return round(($compliantTickets->count() / $ticketsWithResponse->count()) * 100, 2);
    }

    protected function calculateResolutionSLACompliance($tickets): float
    {
        $resolvedTickets = $tickets->whereNotNull('resolved_at');
        
        if ($resolvedTickets->isEmpty()) {
            return 100;
        }

        $compliantTickets = $resolvedTickets->filter(function ($ticket) {
            return $ticket->sla_due_at && 
                   $ticket->resolved_at->lte($ticket->sla_due_at);
        });

        return round(($compliantTickets->count() / $resolvedTickets->count()) * 100, 2);
    }

    protected function calculateAverageResponseTime($tickets): float
    {
        $ticketsWithResponse = $tickets->whereNotNull('first_response_at');
        
        if ($ticketsWithResponse->isEmpty()) {
            return 0;
        }

        $totalMinutes = $ticketsWithResponse->sum(function ($ticket) {
            return $ticket->created_at->diffInMinutes($ticket->first_response_at);
        });

        return round($totalMinutes / $ticketsWithResponse->count(), 2);
    }

    protected function calculateAverageResolutionTime($tickets): float
    {
        $resolvedTickets = $tickets->whereNotNull('resolved_at');
        
        if ($resolvedTickets->isEmpty()) {
            return 0;
        }

        $totalMinutes = $resolvedTickets->sum(function ($ticket) {
            return $ticket->created_at->diffInMinutes($ticket->resolved_at);
        });

        return round($totalMinutes / $resolvedTickets->count(), 2);
    }

    protected function getSLABreachesByPriority($tickets): array
    {
        $breaches = [];
        $priorities = ['critical', 'high', 'medium', 'low'];

        foreach ($priorities as $priority) {
            $priorityTickets = $tickets->where('priority', $priority);
            $breachedTickets = $priorityTickets->filter(function ($ticket) {
                return $ticket->is_sla_breached;
            });

            $breaches[$priority] = [
                'total' => $priorityTickets->count(),
                'breached' => $breachedTickets->count(),
                'compliance_rate' => $priorityTickets->count() > 0 
                    ? round((1 - ($breachedTickets->count() / $priorityTickets->count())) * 100, 2)
                    : 100,
            ];
        }

        return $breaches;
    }

    protected function getSLATrends(Carbon $dateFrom, Carbon $dateTo): array
    {
        // تحليل الاتجاهات الأسبوعية
        $weeks = [];
        $current = $dateFrom->copy()->startOfWeek();

        while ($current->lte($dateTo)) {
            $weekEnd = $current->copy()->endOfWeek();
            $weekTickets = Ticket::whereBetween('created_at', [$current, $weekEnd])->get();

            $weeks[] = [
                'week_start' => $current->format('Y-m-d'),
                'total_tickets' => $weekTickets->count(),
                'response_compliance' => $this->calculateResponseSLACompliance($weekTickets),
                'resolution_compliance' => $this->calculateResolutionSLACompliance($weekTickets),
            ];

            $current->addWeek();
        }

        return $weeks;
    }
}
