<?php

namespace App\Domains\Taxation\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج قاعدة الضريبة
 * يحدد كيفية حساب الضرائب لفئات مختلفة من المنتجات والخدمات
 */
class TaxRule extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'tax_system_id',
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'tax_type',
        'category',
        'rate',
        'fixed_amount',
        'calculation_method',
        'applicable_from',
        'applicable_to',
        'min_amount',
        'max_amount',
        'conditions',
        'exemptions',
        'is_active',
        'priority',
        'description',
        'legal_reference',
        'metadata',
    ];

    protected $casts = [
        'rate' => 'decimal:4',
        'fixed_amount' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'applicable_from' => 'date',
        'applicable_to' => 'date',
        'conditions' => 'array',
        'exemptions' => 'array',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * أنواع الضرائب
     */
    public const TAX_TYPES = [
        'VAT' => 'ضريبة القيمة المضافة',
        'CORPORATE_TAX' => 'ضريبة الشركات',
        'INCOME_TAX' => 'ضريبة الدخل',
        'WITHHOLDING_TAX' => 'ضريبة الاستقطاع',
        'SOCIAL_SECURITY' => 'الضمان الاجتماعي',
        'CUSTOMS_DUTY' => 'رسوم جمركية',
        'EXCISE_TAX' => 'ضريبة الاستهلاك',
        'PROPERTY_TAX' => 'ضريبة العقارات',
        'STAMP_DUTY' => 'رسم الطابع',
    ];

    /**
     * فئات المنتجات والخدمات
     */
    public const CATEGORIES = [
        'GENERAL' => 'عام',
        'FOOD_BASIC' => 'مواد غذائية أساسية',
        'FOOD_LUXURY' => 'مواد غذائية كمالية',
        'MEDICAL' => 'طبية',
        'EDUCATION' => 'تعليمية',
        'TRANSPORT' => 'نقل',
        'ACCOMMODATION' => 'إقامة',
        'FINANCIAL' => 'خدمات مالية',
        'INSURANCE' => 'تأمين',
        'REAL_ESTATE' => 'عقارات',
        'TECHNOLOGY' => 'تكنولوجيا',
        'ENTERTAINMENT' => 'ترفيه',
        'TOBACCO' => 'تبغ',
        'ALCOHOL' => 'كحول',
        'FUEL' => 'وقود',
        'UTILITIES' => 'مرافق',
    ];

    /**
     * طرق الحساب
     */
    public const CALCULATION_METHODS = [
        'PERCENTAGE' => 'نسبة مئوية',
        'FIXED' => 'مبلغ ثابت',
        'PROGRESSIVE' => 'تصاعدي',
        'REGRESSIVE' => 'تنازلي',
        'COMPOUND' => 'مركب',
        'TIERED' => 'متدرج',
    ];

    /**
     * النظام الضريبي
     */
    public function taxSystem(): BelongsTo
    {
        return $this->belongsTo(TaxSystem::class);
    }

    /**
     * حساب الضريبة
     */
    public function calculateTax(float $amount, array $context = []): array
    {
        // التحقق من الشروط
        if (!$this->isApplicable($amount, $context)) {
            return [
                'applicable' => false,
                'base_amount' => $amount,
                'tax_amount' => 0,
                'total_amount' => $amount,
                'rule_name' => $this->name,
            ];
        }

        $taxAmount = match ($this->calculation_method) {
            'PERCENTAGE' => $this->calculatePercentage($amount),
            'FIXED' => $this->calculateFixed($amount),
            'PROGRESSIVE' => $this->calculateProgressive($amount),
            'TIERED' => $this->calculateTiered($amount),
            default => 0,
        };

        return [
            'applicable' => true,
            'base_amount' => $amount,
            'tax_rate' => $this->rate,
            'tax_amount' => round($taxAmount, 2),
            'total_amount' => round($amount + $taxAmount, 2),
            'rule_name' => $this->name,
            'rule_id' => $this->id,
            'calculation_method' => $this->calculation_method,
        ];
    }

    /**
     * التحقق من قابلية التطبيق
     */
    protected function isApplicable(float $amount, array $context): bool
    {
        // التحقق من النشاط
        if (!$this->is_active) {
            return false;
        }

        // التحقق من التاريخ
        $now = now();
        if ($this->applicable_from && $now < $this->applicable_from) {
            return false;
        }
        if ($this->applicable_to && $now > $this->applicable_to) {
            return false;
        }

        // التحقق من الحد الأدنى والأقصى
        if ($this->min_amount && $amount < $this->min_amount) {
            return false;
        }
        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }

        // التحقق من الشروط المخصصة
        if ($this->conditions) {
            return $this->evaluateConditions($context);
        }

        // التحقق من الاستثناءات
        if ($this->exemptions) {
            return !$this->evaluateExemptions($context);
        }

        return true;
    }

    /**
     * حساب النسبة المئوية
     */
    protected function calculatePercentage(float $amount): float
    {
        return $amount * ($this->rate / 100);
    }

    /**
     * حساب المبلغ الثابت
     */
    protected function calculateFixed(float $amount): float
    {
        return $this->fixed_amount ?? 0;
    }

    /**
     * حساب تصاعدي
     */
    protected function calculateProgressive(float $amount): float
    {
        $brackets = $this->metadata['brackets'] ?? [];
        $totalTax = 0;
        $remainingAmount = $amount;

        foreach ($brackets as $bracket) {
            $min = $bracket['min'] ?? 0;
            $max = $bracket['max'] ?? PHP_FLOAT_MAX;
            $rate = $bracket['rate'] ?? 0;

            if ($remainingAmount <= 0) break;

            $taxableInBracket = min($remainingAmount, $max - $min);
            $taxInBracket = $taxableInBracket * ($rate / 100);

            $totalTax += $taxInBracket;
            $remainingAmount -= $taxableInBracket;
        }

        return $totalTax;
    }

    /**
     * حساب متدرج
     */
    protected function calculateTiered(float $amount): float
    {
        $tiers = $this->metadata['tiers'] ?? [];

        foreach ($tiers as $tier) {
            $min = $tier['min'] ?? 0;
            $max = $tier['max'] ?? PHP_FLOAT_MAX;
            $rate = $tier['rate'] ?? 0;

            if ($amount >= $min && $amount <= $max) {
                return $amount * ($rate / 100);
            }
        }

        return 0;
    }

    /**
     * تقييم الشروط
     */
    protected function evaluateConditions(array $context): bool
    {
        foreach ($this->conditions as $condition) {
            $field = $condition['field'] ?? '';
            $operator = $condition['operator'] ?? '=';
            $value = $condition['value'] ?? '';

            $contextValue = $context[$field] ?? null;

            $result = match ($operator) {
                '=' => $contextValue == $value,
                '!=' => $contextValue != $value,
                '>' => $contextValue > $value,
                '<' => $contextValue < $value,
                '>=' => $contextValue >= $value,
                '<=' => $contextValue <= $value,
                'in' => in_array($contextValue, (array) $value),
                'not_in' => !in_array($contextValue, (array) $value),
                'contains' => str_contains($contextValue, $value),
                default => false,
            };

            if (!$result) {
                return false;
            }
        }

        return true;
    }

    /**
     * تقييم الاستثناءات
     */
    protected function evaluateExemptions(array $context): bool
    {
        return $this->evaluateConditions($context);
    }

    /**
     * الحصول على الاسم المترجم
     */
    public function getLocalizedName(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            default => $this->name,
        };
    }

    /**
     * إنشاء قاعدة ضريبة القيمة المضافة
     */
    public static function createVATRule(TaxSystem $taxSystem, string $category, float $rate): self
    {
        return static::create([
            'tax_system_id' => $taxSystem->id,
            'name' => "ضريبة القيمة المضافة - {$category}",
            'tax_type' => 'VAT',
            'category' => $category,
            'rate' => $rate,
            'calculation_method' => 'PERCENTAGE',
            'is_active' => true,
            'priority' => 1,
        ]);
    }

    /**
     * إنشاء قاعدة ضريبة الاستقطاع
     */
    public static function createWithholdingTaxRule(TaxSystem $taxSystem, string $category, float $rate): self
    {
        return static::create([
            'tax_system_id' => $taxSystem->id,
            'name' => "ضريبة الاستقطاع - {$category}",
            'tax_type' => 'WITHHOLDING_TAX',
            'category' => $category,
            'rate' => $rate,
            'calculation_method' => 'PERCENTAGE',
            'is_active' => true,
            'priority' => 2,
        ]);
    }

    /**
     * نطاق للقواعد النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق حسب نوع الضريبة
     */
    public function scopeByTaxType($query, string $taxType)
    {
        return $query->where('tax_type', $taxType);
    }

    /**
     * نطاق حسب الفئة
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * نطاق للقواعد المطبقة في تاريخ معين
     */
    public function scopeApplicableOn($query, $date = null)
    {
        $date = $date ?? now();

        return $query->where(function ($q) use ($date) {
            $q->whereNull('applicable_from')
              ->orWhere('applicable_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('applicable_to')
              ->orWhere('applicable_to', '>=', $date);
        });
    }

    /**
     * نطاق مرتب حسب الأولوية
     */
    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }
}
