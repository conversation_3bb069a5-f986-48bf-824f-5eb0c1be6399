<?php

namespace App\Domains\Compliance\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد قاعدة الامتثال
 */
class ComplianceRuleResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rule_id' => $this->rule_id,
            'rule_name_ar' => $this->rule_name_ar,
            'rule_name_en' => $this->rule_name_en,
            'rule_description_ar' => $this->rule_description_ar,
            'rule_description_en' => $this->rule_description_en,
            'rule_category' => $this->rule_category,
            'rule_category_label' => $this->rule_category ? 
                \App\Domains\Compliance\Models\ComplianceRule::RULE_CATEGORIES[$this->rule_category] : null,
            'rule_type' => $this->rule_type,
            'rule_type_label' => $this->rule_type ? 
                \App\Domains\Compliance\Models\ComplianceRule::RULE_TYPES[$this->rule_type] : null,
            'authority_name' => $this->authority_name,
            'legal_reference' => $this->legal_reference,
            'regulation_number' => $this->regulation_number,
            'effective_date' => $this->effective_date?->format('Y-m-d'),
            'expiry_date' => $this->expiry_date?->format('Y-m-d'),
            'risk_level' => $this->risk_level,
            'risk_level_label' => $this->risk_level ? 
                \App\Domains\Compliance\Models\ComplianceRule::RISK_LEVELS[$this->risk_level] : null,
            'priority' => $this->priority,
            'status' => $this->status,
            'status_label' => $this->status ? 
                \App\Domains\Compliance\Models\ComplianceRule::STATUSES[$this->status] : null,
            'compliance_score_impact' => $this->getComplianceScoreImpact(),
            'is_expired' => $this->isExpired(),
            'needs_review' => $this->needsReview(),
            'next_review_date' => $this->next_review_date?->format('Y-m-d'),
            'last_updated_by_authority' => $this->last_updated_by_authority?->format('Y-m-d H:i:s'),
            
            // العلاقات
            'country' => $this->whenLoaded('country', function () {
                return [
                    'id' => $this->country->id,
                    'name_ar' => $this->country->name_ar,
                    'name_en' => $this->country->name_en,
                    'code' => $this->country->code,
                ];
            }),

            // البيانات المفصلة (فقط عند الطلب)
            $this->mergeWhen($request->get('include_details'), [
                'rule_conditions' => $this->rule_conditions,
                'compliance_requirements' => $this->compliance_requirements,
                'validation_rules' => $this->validation_rules,
                'penalty_structure' => $this->penalty_structure,
                'exemption_criteria' => $this->exemption_criteria,
                'applicable_entities' => $this->applicable_entities,
                'deadline_calculation' => $this->deadline_calculation,
                'notification_rules' => $this->notification_rules,
                'escalation_rules' => $this->escalation_rules,
                'automation_config' => $this->automation_config,
                'integration_endpoints' => $this->integration_endpoints,
                'documentation_requirements' => $this->documentation_requirements,
                'audit_requirements' => $this->audit_requirements,
                'reporting_templates' => $this->reporting_templates,
                'metadata' => $this->metadata,
            ]),

            // الإحصائيات (فقط عند الطلب)
            $this->mergeWhen($request->get('include_stats'), [
                'activities_count' => $this->whenCounted('complianceActivities'),
                'alerts_count' => $this->whenCounted('complianceAlerts'),
                'violations_count' => $this->getViolationsCount(),
                'compliance_rate' => $this->getComplianceRate(),
            ]),

            // التواريخ
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * الحصول على عدد المخالفات
     */
    protected function getViolationsCount(): int
    {
        // منطق حساب عدد المخالفات
        return 0; // مثال
    }

    /**
     * الحصول على معدل الامتثال
     */
    protected function getComplianceRate(): float
    {
        // منطق حساب معدل الامتثال
        return 95.0; // مثال
    }
}
