<?php

namespace App\Domains\Projects\Events;

use App\Domains\Projects\Models\Project;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنشاء مشروع جديد
 */
class ProjectCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Project $project;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Project $project)
    {
        $this->project = $project;
    }

    /**
     * الحصول على القنوات التي يجب بث الحدث عليها
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('projects'),
            new PrivateChannel('project.' . $this->project->id),
            new PrivateChannel('user.' . $this->project->project_manager_id),
        ];
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'project.created';
    }

    /**
     * البيانات المرسلة مع الحدث
     */
    public function broadcastWith(): array
    {
        return [
            'project' => [
                'id' => $this->project->id,
                'name' => $this->project->name,
                'code' => $this->project->code,
                'status' => $this->project->status,
                'project_manager' => $this->project->projectManager?->name,
                'created_at' => $this->project->created_at->toISOString(),
            ],
            'message' => "تم إنشاء مشروع جديد: {$this->project->name}",
            'timestamp' => now()->toISOString(),
        ];
    }
}
