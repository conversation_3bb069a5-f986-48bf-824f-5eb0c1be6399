<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\AttendanceRecord;
use App\Domains\HR\Models\WorkShift;
use App\Domains\HR\Events\EmployeeCheckedIn;
use App\Domains\HR\Events\EmployeeCheckedOut;
use App\Domains\HR\Events\AttendanceAnomalyDetected;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * خدمة إدارة الحضور والانصراف المتقدمة
 * نظام شامل لتتبع الحضور مع الذكاء الاصطناعي
 */
class AttendanceManagementService
{
    /**
     * تسجيل حضور الموظف
     */
    public function checkIn(int $employeeId, array $data = []): AttendanceRecord
    {
        DB::beginTransaction();

        try {
            $employee = Employee::findOrFail($employeeId);
            $today = now()->format('Y-m-d');

            // التحقق من وجود سجل حضور لليوم
            $existingRecord = AttendanceRecord::where('employee_id', $employeeId)
                                            ->whereDate('date', $today)
                                            ->first();

            if ($existingRecord && $existingRecord->actual_check_in) {
                throw new \Exception('تم تسجيل الحضور مسبقاً لهذا اليوم');
            }

            // إنشاء أو تحديث سجل الحضور
            $attendanceRecord = $existingRecord ?: new AttendanceRecord();
            
            $checkInData = array_merge([
                'employee_id' => $employeeId,
                'date' => $today,
                'actual_check_in' => now(),
                'check_in_method' => $data['method'] ?? 'MANUAL',
                'check_in_device' => $data['device'] ?? null,
                'check_in_ip' => request()->ip(),
                'check_in_location' => $data['location'] ?? null,
                'biometric_check_in' => $data['biometric'] ?? false,
                'face_recognition_check_in' => $data['face_recognition'] ?? false,
            ], $data);

            // تحديد الوردية والجدولة
            $this->setShiftAndSchedule($attendanceRecord, $employee);

            $attendanceRecord->fill($checkInData);
            $attendanceRecord->save();

            // حساب التأخير
            $attendanceRecord->update([
                'late_minutes' => $attendanceRecord->calculateLateMinutes(),
            ]);

            // التحقق من الشذوذ
            $this->detectAttendanceAnomalies($attendanceRecord);

            DB::commit();

            // إرسال الأحداث
            Event::dispatch(new EmployeeCheckedIn($attendanceRecord));

            return $attendanceRecord;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تسجيل انصراف الموظف
     */
    public function checkOut(int $employeeId, array $data = []): AttendanceRecord
    {
        DB::beginTransaction();

        try {
            $today = now()->format('Y-m-d');

            $attendanceRecord = AttendanceRecord::where('employee_id', $employeeId)
                                               ->whereDate('date', $today)
                                               ->first();

            if (!$attendanceRecord || !$attendanceRecord->actual_check_in) {
                throw new \Exception('لم يتم تسجيل الحضور لهذا اليوم');
            }

            if ($attendanceRecord->actual_check_out) {
                throw new \Exception('تم تسجيل الانصراف مسبقاً لهذا اليوم');
            }

            $checkOutData = array_merge([
                'actual_check_out' => now(),
                'check_out_method' => $data['method'] ?? 'MANUAL',
                'check_out_device' => $data['device'] ?? null,
                'check_out_ip' => request()->ip(),
                'check_out_location' => $data['location'] ?? null,
                'biometric_check_out' => $data['biometric'] ?? false,
                'face_recognition_check_out' => $data['face_recognition'] ?? false,
            ], $data);

            $attendanceRecord->update($checkOutData);

            // حساب الساعات والانصراف المبكر
            $attendanceRecord->update([
                'total_hours' => $attendanceRecord->calculateTotalHours(),
                'regular_hours' => min($attendanceRecord->calculateTotalHours(), $attendanceRecord->getRegularWorkingHours()),
                'overtime_hours' => $attendanceRecord->calculateOvertimeHours(),
                'early_departure_minutes' => $attendanceRecord->calculateEarlyDepartureMinutes(),
            ]);

            // تحديث الحالة
            $this->updateAttendanceStatus($attendanceRecord);

            // التحقق من الشذوذ
            $this->detectAttendanceAnomalies($attendanceRecord);

            DB::commit();

            // إرسال الأحداث
            Event::dispatch(new EmployeeCheckedOut($attendanceRecord));

            return $attendanceRecord;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تسجيل بداية الاستراحة
     */
    public function startBreak(int $employeeId): AttendanceRecord
    {
        $today = now()->format('Y-m-d');

        $attendanceRecord = AttendanceRecord::where('employee_id', $employeeId)
                                           ->whereDate('date', $today)
                                           ->first();

        if (!$attendanceRecord || !$attendanceRecord->actual_check_in) {
            throw new \Exception('لم يتم تسجيل الحضور لهذا اليوم');
        }

        if ($attendanceRecord->break_start) {
            throw new \Exception('تم بدء الاستراحة مسبقاً');
        }

        $attendanceRecord->update(['break_start' => now()]);

        return $attendanceRecord;
    }

    /**
     * تسجيل نهاية الاستراحة
     */
    public function endBreak(int $employeeId): AttendanceRecord
    {
        $today = now()->format('Y-m-d');

        $attendanceRecord = AttendanceRecord::where('employee_id', $employeeId)
                                           ->whereDate('date', $today)
                                           ->first();

        if (!$attendanceRecord || !$attendanceRecord->break_start) {
            throw new \Exception('لم يتم بدء الاستراحة');
        }

        if ($attendanceRecord->break_end) {
            throw new \Exception('تم إنهاء الاستراحة مسبقاً');
        }

        $attendanceRecord->update([
            'break_end' => now(),
            'break_hours' => $attendanceRecord->break_start->diffInHours(now(), true),
        ]);

        return $attendanceRecord;
    }

    /**
     * تحديد الوردية والجدولة
     */
    protected function setShiftAndSchedule(AttendanceRecord $attendanceRecord, Employee $employee): void
    {
        $today = now();
        $dayOfWeek = $today->format('l');

        // البحث عن الوردية المناسبة
        $shift = $employee->shifts()
                          ->where('is_active', true)
                          ->whereJsonContains('working_days', strtoupper($dayOfWeek))
                          ->first();

        if ($shift) {
            $attendanceRecord->shift_id = $shift->id;
            $attendanceRecord->schedule_type = $shift->shift_type;
            $attendanceRecord->scheduled_check_in = $today->copy()->setTimeFromTimeString($shift->start_time);
            $attendanceRecord->scheduled_check_out = $today->copy()->setTimeFromTimeString($shift->end_time);
        } else {
            // استخدام الجدولة الافتراضية
            $attendanceRecord->schedule_type = 'REGULAR';
            $attendanceRecord->scheduled_check_in = $today->copy()->setTime(8, 0);
            $attendanceRecord->scheduled_check_out = $today->copy()->setTime(17, 0);
        }

        // التحقق من العطل والإجازات
        $attendanceRecord->is_weekend = $today->isWeekend();
        $attendanceRecord->is_holiday = $this->isHoliday($today);
    }

    /**
     * تحديث حالة الحضور
     */
    protected function updateAttendanceStatus(AttendanceRecord $attendanceRecord): void
    {
        $status = 'PRESENT';

        if ($attendanceRecord->late_minutes > 0) {
            $status = 'LATE';
        }

        if ($attendanceRecord->early_departure_minutes > 0) {
            $status = 'EARLY_DEPARTURE';
        }

        if ($attendanceRecord->overtime_hours > 0) {
            $status = 'OVERTIME';
        }

        if ($attendanceRecord->is_half_day) {
            $status = 'HALF_DAY';
        }

        if ($attendanceRecord->is_remote_work) {
            $status = 'REMOTE';
        }

        if ($attendanceRecord->is_holiday) {
            $status = 'HOLIDAY';
        }

        if ($attendanceRecord->is_weekend) {
            $status = 'WEEKEND';
        }

        $attendanceRecord->update(['status' => $status]);
    }

    /**
     * كشف الشذوذ في الحضور
     */
    protected function detectAttendanceAnomalies(AttendanceRecord $attendanceRecord): void
    {
        $anomalies = [];

        // تحقق من ساعات العمل المفرطة
        if ($attendanceRecord->total_hours > 12) {
            $anomalies[] = 'excessive_working_hours';
        }

        // تحقق من التأخير المفرط
        if ($attendanceRecord->late_minutes > 120) {
            $anomalies[] = 'excessive_lateness';
        }

        // تحقق من العمل في أيام العطل
        if ($attendanceRecord->is_weekend || $attendanceRecord->is_holiday) {
            $anomalies[] = 'weekend_holiday_work';
        }

        // تحقق من تضارب المواقع
        if ($this->hasLocationConflict($attendanceRecord)) {
            $anomalies[] = 'location_conflict';
        }

        // تحقق من أنماط الحضور غير العادية
        if ($this->hasUnusualAttendancePattern($attendanceRecord)) {
            $anomalies[] = 'unusual_pattern';
        }

        if (!empty($anomalies)) {
            Event::dispatch(new AttendanceAnomalyDetected($attendanceRecord, $anomalies));
        }
    }

    /**
     * التحقق من تضارب المواقع
     */
    protected function hasLocationConflict(AttendanceRecord $attendanceRecord): bool
    {
        if (!$attendanceRecord->check_in_location || !$attendanceRecord->check_out_location) {
            return false;
        }

        $checkInLat = $attendanceRecord->check_in_location['latitude'] ?? null;
        $checkInLng = $attendanceRecord->check_in_location['longitude'] ?? null;
        $checkOutLat = $attendanceRecord->check_out_location['latitude'] ?? null;
        $checkOutLng = $attendanceRecord->check_out_location['longitude'] ?? null;

        if (!$checkInLat || !$checkInLng || !$checkOutLat || !$checkOutLng) {
            return false;
        }

        // حساب المسافة بين نقطتي الحضور والانصراف
        $distance = $this->calculateDistance($checkInLat, $checkInLng, $checkOutLat, $checkOutLng);

        // إذا كانت المسافة أكبر من 1 كم، قد يكون هناك تضارب
        return $distance > 1000;
    }

    /**
     * حساب المسافة بين نقطتين جغرافيتين
     */
    protected function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371000; // متر

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * التحقق من أنماط الحضور غير العادية
     */
    protected function hasUnusualAttendancePattern(AttendanceRecord $attendanceRecord): bool
    {
        $employee = $attendanceRecord->employee;
        $lastWeekRecords = AttendanceRecord::where('employee_id', $employee->id)
                                          ->whereBetween('date', [now()->subWeek(), now()])
                                          ->get();

        if ($lastWeekRecords->count() < 3) {
            return false;
        }

        $avgCheckIn = $lastWeekRecords->avg(function ($record) {
            return $record->actual_check_in ? $record->actual_check_in->hour * 60 + $record->actual_check_in->minute : 0;
        });

        $currentCheckIn = $attendanceRecord->actual_check_in ? 
            $attendanceRecord->actual_check_in->hour * 60 + $attendanceRecord->actual_check_in->minute : 0;

        // إذا كان الفرق أكبر من ساعتين عن المتوسط
        return abs($currentCheckIn - $avgCheckIn) > 120;
    }

    /**
     * التحقق من العطل الرسمية
     */
    protected function isHoliday(Carbon $date): bool
    {
        // يمكن تطوير هذه الدالة للتحقق من العطل الرسمية
        return false;
    }

    /**
     * إنشاء سجلات حضور للفترة المحددة
     */
    public function generateAttendanceSchedule(int $employeeId, Carbon $startDate, Carbon $endDate): int
    {
        $employee = Employee::findOrFail($employeeId);
        $generated = 0;

        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            // تحقق من عدم وجود سجل مسبق
            $existingRecord = AttendanceRecord::where('employee_id', $employeeId)
                                            ->whereDate('date', $currentDate)
                                            ->first();

            if (!$existingRecord) {
                $attendanceRecord = new AttendanceRecord([
                    'employee_id' => $employeeId,
                    'date' => $currentDate->format('Y-m-d'),
                    'status' => 'ABSENT',
                ]);

                $this->setShiftAndSchedule($attendanceRecord, $employee);
                $attendanceRecord->save();
                $generated++;
            }

            $currentDate->addDay();
        }

        return $generated;
    }

    /**
     * الحصول على تقرير الحضور للموظف
     */
    public function getAttendanceReport(int $employeeId, Carbon $startDate, Carbon $endDate): array
    {
        $records = AttendanceRecord::where('employee_id', $employeeId)
                                  ->whereBetween('date', [$startDate, $endDate])
                                  ->orderBy('date')
                                  ->get();

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $workingDays = $records->where('status', '!=', 'WEEKEND')->where('status', '!=', 'HOLIDAY')->count();
        $presentDays = $records->whereIn('status', ['PRESENT', 'LATE', 'EARLY_DEPARTURE', 'OVERTIME'])->count();
        $absentDays = $records->where('status', 'ABSENT')->count();
        $lateDays = $records->where('late_minutes', '>', 0)->count();
        $overtimeDays = $records->where('overtime_hours', '>', 0)->count();

        return [
            'employee_id' => $employeeId,
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'total_days' => $totalDays,
                'working_days' => $workingDays,
            ],
            'summary' => [
                'present_days' => $presentDays,
                'absent_days' => $absentDays,
                'late_days' => $lateDays,
                'overtime_days' => $overtimeDays,
                'attendance_rate' => $workingDays > 0 ? round(($presentDays / $workingDays) * 100, 2) : 0,
                'punctuality_rate' => $presentDays > 0 ? round((($presentDays - $lateDays) / $presentDays) * 100, 2) : 0,
            ],
            'totals' => [
                'total_hours' => $records->sum('total_hours'),
                'regular_hours' => $records->sum('regular_hours'),
                'overtime_hours' => $records->sum('overtime_hours'),
                'break_hours' => $records->sum('break_hours'),
                'late_minutes' => $records->sum('late_minutes'),
                'early_departure_minutes' => $records->sum('early_departure_minutes'),
            ],
            'records' => $records->toArray(),
        ];
    }

    /**
     * الحصول على إحصائيات الحضور العامة
     */
    public function getAttendanceStatistics(array $filters = []): array
    {
        $query = AttendanceRecord::query();

        // تطبيق المرشحات
        if (isset($filters['department_id'])) {
            $query->whereHas('employee', function ($q) use ($filters) {
                $q->where('department_id', $filters['department_id']);
            });
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('date', [$filters['start_date'], $filters['end_date']]);
        }

        $records = $query->get();

        return [
            'total_records' => $records->count(),
            'present_records' => $records->whereIn('status', ['PRESENT', 'LATE', 'EARLY_DEPARTURE', 'OVERTIME'])->count(),
            'absent_records' => $records->where('status', 'ABSENT')->count(),
            'late_records' => $records->where('late_minutes', '>', 0)->count(),
            'overtime_records' => $records->where('overtime_hours', '>', 0)->count(),
            'average_working_hours' => $records->avg('total_hours'),
            'total_overtime_hours' => $records->sum('overtime_hours'),
            'average_late_minutes' => $records->where('late_minutes', '>', 0)->avg('late_minutes'),
            'by_status' => $records->groupBy('status')->map->count(),
            'by_method' => $records->groupBy('check_in_method')->map->count(),
        ];
    }

    /**
     * تحديث جميع سجلات الحضور للفترة المحددة
     */
    public function recalculateAttendance(Carbon $startDate, Carbon $endDate): int
    {
        $records = AttendanceRecord::whereBetween('date', [$startDate, $endDate])->get();
        $updated = 0;

        foreach ($records as $record) {
            if ($record->actual_check_in && $record->actual_check_out) {
                $record->update([
                    'total_hours' => $record->calculateTotalHours(),
                    'regular_hours' => min($record->calculateTotalHours(), $record->getRegularWorkingHours()),
                    'overtime_hours' => $record->calculateOvertimeHours(),
                    'late_minutes' => $record->calculateLateMinutes(),
                    'early_departure_minutes' => $record->calculateEarlyDepartureMinutes(),
                ]);

                $this->updateAttendanceStatus($record);
                $updated++;
            }
        }

        return $updated;
    }
}
