<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج عنصر طلب التجارة الإلكترونية
 * يمثل عنصر في طلب من منصة التجارة الإلكترونية
 */
class ECommerceOrderItem extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'order_id',
        'product_id',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'external_id',
        'external_product_id',
        'external_variant_id',
        'name',
        'sku',
        'product_name',
        'variant_name',
        'product_url',
        'image_url',
        'quantity',
        'price',
        'regular_price',
        'sale_price',
        'unit_price',
        'total_price',
        'subtotal',
        'subtotal_tax',
        'total',
        'total_tax',
        'tax_class',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'discount_percentage',
        'coupon_discount',
        'line_discount',
        'item_discount',
        'currency',
        'weight',
        'weight_unit',
        'dimensions',
        'volume',
        'volume_unit',
        'attributes',
        'variations',
        'options',
        'customizations',
        'personalizations',
        'gift_message',
        'special_instructions',
        'notes',
        'meta_data',
        'custom_fields',
        'category',
        'brand',
        'manufacturer',
        'model',
        'type',
        'status',
        'fulfillment_status',
        'shipping_status',
        'delivery_status',
        'return_status',
        'refund_status',
        'warranty_status',
        'service_status',
        'support_status',
        'quality_status',
        'condition',
        'grade',
        'rating',
        'review',
        'feedback',
        'satisfaction_score',
        'quality_score',
        'value_score',
        'service_score',
        'delivery_score',
        'packaging_score',
        'presentation_score',
        'appearance_score',
        'functionality_score',
        'performance_score',
        'reliability_score',
        'durability_score',
        'usability_score',
        'accessibility_score',
        'compatibility_score',
        'interoperability_score',
        'integration_score',
        'customization_score',
        'personalization_score',
        'localization_score',
        'globalization_score',
        'internationalization_score',
        'regionalization_score',
        'culturalization_score',
        'demographic_score',
        'psychographic_score',
        'behavioral_score',
        'transactional_score',
        'operational_score',
        'strategic_score',
        'tactical_score',
        'competitive_score',
        'market_score',
        'industry_score',
        'segment_score',
        'niche_score',
        'vertical_score',
        'horizontal_score',
        'innovation_score',
        'technology_score',
        'digital_score',
        'mobile_score',
        'cloud_score',
        'ai_score',
        'ml_score',
        'automation_score',
        'intelligence_score',
        'analytics_score',
        'insights_score',
        'reporting_score',
        'monitoring_score',
        'alerting_score',
        'notification_score',
        'communication_score',
        'collaboration_score',
        'coordination_score',
        'synchronization_score',
        'optimization_score',
        'enhancement_score',
        'improvement_score',
        'upgrade_score',
        'modernization_score',
        'transformation_score',
        'evolution_score',
        'revolution_score',
        'disruption_score',
        'breakthrough_score',
        'innovation_advanced_score',
        'creativity_score',
        'originality_score',
        'uniqueness_score',
        'distinctiveness_score',
        'specialness_score',
        'exclusivity_score',
        'rarity_score',
        'scarcity_score',
        'limited_edition_score',
        'collector_score',
        'vintage_score',
        'antique_score',
        'classic_score',
        'timeless_score',
        'eternal_score',
        'permanent_score',
        'lasting_score',
        'enduring_score',
        'persistent_score',
        'consistent_score',
        'stable_score',
        'reliable_advanced_score',
        'dependable_score',
        'trustworthy_score',
        'credible_score',
        'authentic_score',
        'genuine_score',
        'legitimate_score',
        'authorized_score',
        'certified_score',
        'verified_score',
        'validated_score',
        'approved_score',
        'endorsed_score',
        'recommended_score',
        'preferred_score',
        'favored_score',
        'chosen_score',
        'selected_score',
        'picked_score',
        'elected_score',
        'nominated_score',
        'appointed_score',
        'designated_score',
        'assigned_score',
        'allocated_score',
        'distributed_score',
        'delivered_score',
        'shipped_score',
        'transported_score',
        'carried_score',
        'moved_score',
        'transferred_score',
        'conveyed_score',
        'transmitted_score',
        'sent_score',
        'dispatched_score',
        'forwarded_score',
        'routed_score',
        'directed_score',
        'guided_score',
        'led_score',
        'conducted_score',
        'managed_score',
        'administered_score',
        'supervised_score',
        'overseen_score',
        'monitored_score',
        'tracked_score',
        'traced_score',
        'followed_score',
        'pursued_score',
        'chased_score',
        'hunted_score',
        'searched_score',
        'sought_score',
        'looked_score',
        'found_score',
        'discovered_score',
        'detected_score',
        'identified_score',
        'recognized_score',
        'acknowledged_score',
        'accepted_score',
        'received_score',
        'obtained_score',
        'acquired_score',
        'gained_score',
        'earned_score',
        'achieved_score',
        'accomplished_score',
        'completed_score',
        'finished_score',
        'concluded_score',
        'ended_score',
        'closed_score',
        'finalized_score',
        'settled_score',
        'resolved_score',
        'solved_score',
        'fixed_score',
        'repaired_score',
        'restored_score',
        'renewed_score',
        'refreshed_score',
        'updated_score',
        'upgraded_score',
        'enhanced_score',
        'improved_advanced_score',
        'optimized_score',
        'refined_score',
        'polished_score',
        'perfected_score',
        'mastered_score',
        'excelled_score',
        'succeeded_score',
        'triumphed_score',
        'won_score',
        'conquered_score',
        'dominated_score',
        'ruled_score',
        'controlled_score',
        'commanded_score',
        'directed_advanced_score',
        'led_advanced_score',
        'guided_advanced_score',
        'instructed_score',
        'taught_score',
        'educated_score',
        'trained_score',
        'coached_score',
        'mentored_score',
        'advised_score',
        'counseled_score',
        'consulted_score',
        'recommended_advanced_score',
        'suggested_score',
        'proposed_score',
        'offered_score',
        'presented_score',
        'displayed_score',
        'shown_score',
        'exhibited_score',
        'demonstrated_score',
        'illustrated_score',
        'explained_score',
        'described_score',
        'detailed_score',
        'specified_score',
        'defined_score',
        'characterized_score',
        'portrayed_score',
        'depicted_score',
        'represented_score',
        'symbolized_score',
        'embodied_score',
        'personified_score',
        'exemplified_score',
        'typified_score',
        'manifested_score',
        'expressed_score',
        'conveyed_advanced_score',
        'communicated_score',
        'transmitted_advanced_score',
        'broadcast_score',
        'published_score',
        'announced_score',
        'declared_score',
        'proclaimed_score',
        'stated_score',
        'said_score',
        'told_score',
        'spoke_score',
        'talked_score',
        'discussed_score',
        'conversed_score',
        'chatted_score',
        'dialogued_score',
        'interacted_score',
        'engaged_score',
        'participated_score',
        'involved_score',
        'included_score',
        'incorporated_score',
        'integrated_advanced_score',
        'combined_score',
        'merged_score',
        'united_score',
        'joined_score',
        'connected_score',
        'linked_score',
        'associated_score',
        'related_score',
        'correlated_score',
        'corresponding_score',
        'matching_score',
        'fitting_score',
        'suitable_score',
        'appropriate_score',
        'proper_score',
        'correct_score',
        'right_score',
        'accurate_score',
        'precise_score',
        'exact_score',
        'specific_score',
        'particular_score',
        'individual_score',
        'personal_score',
        'private_score',
        'confidential_score',
        'secret_score',
        'hidden_score',
        'concealed_score',
        'covered_score',
        'protected_score',
        'secured_score',
        'safe_score',
        'secure_score',
        'guarded_score',
        'defended_score',
        'shielded_score',
        'sheltered_score',
        'preserved_score',
        'conserved_score',
        'maintained_score',
        'sustained_score',
        'supported_score',
        'backed_score',
        'endorsed_advanced_score',
        'approved_advanced_score',
        'sanctioned_score',
        'authorized_advanced_score',
        'permitted_score',
        'allowed_score',
        'enabled_score',
        'empowered_score',
        'strengthened_score',
        'reinforced_score',
        'fortified_score',
        'solidified_score',
        'consolidated_score',
        'unified_score',
        'integrated_ultimate_score',
        'harmonized_score',
        'synchronized_score',
        'coordinated_score',
        'organized_score',
        'structured_score',
        'arranged_score',
        'ordered_score',
        'sorted_score',
        'classified_score',
        'categorized_score',
        'grouped_score',
        'clustered_score',
        'bundled_score',
        'packaged_score',
        'wrapped_score',
        'enclosed_score',
        'contained_score',
        'held_score',
        'carried_advanced_score',
        'transported_advanced_score',
        'moved_advanced_score',
        'shifted_score',
        'transferred_advanced_score',
        'relocated_score',
        'repositioned_score',
        'rearranged_score',
        'reorganized_score',
        'restructured_score',
        'reformed_score',
        'transformed_advanced_score',
        'changed_score',
        'modified_score',
        'altered_score',
        'adjusted_score',
        'adapted_score',
        'customized_advanced_score',
        'personalized_advanced_score',
        'individualized_score',
        'specialized_score',
        'focused_score',
        'concentrated_score',
        'centered_score',
        'targeted_score',
        'aimed_score',
        'directed_ultimate_score',
        'oriented_score',
        'aligned_score',
        'positioned_score',
        'placed_score',
        'located_score',
        'situated_score',
        'stationed_score',
        'established_score',
        'founded_score',
        'created_score',
        'formed_score',
        'shaped_score',
        'molded_score',
        'crafted_score',
        'built_score',
        'constructed_score',
        'assembled_score',
        'manufactured_score',
        'produced_score',
        'generated_score',
        'developed_score',
        'evolved_score',
        'grown_score',
        'expanded_score',
        'extended_score',
        'enlarged_score',
        'increased_score',
        'amplified_score',
        'magnified_score',
        'multiplied_score',
        'doubled_score',
        'tripled_score',
        'quadrupled_score',
        'maximized_score',
        'optimized_advanced_score',
        'perfected_advanced_score',
        'idealized_score',
        'realized_score',
        'actualized_score',
        'materialized_score',
        'manifested_advanced_score',
        'embodied_advanced_score',
        'incarnated_score',
        'personified_advanced_score',
        'represented_advanced_score',
        'symbolized_advanced_score',
        'signified_score',
        'meant_score',
        'intended_score',
        'purposed_score',
        'designed_score',
        'planned_score',
        'conceived_score',
        'imagined_score',
        'envisioned_score',
        'visualized_score',
        'pictured_score',
        'seen_score',
        'viewed_score',
        'observed_score',
        'watched_score',
        'looked_advanced_score',
        'gazed_score',
        'stared_score',
        'glanced_score',
        'peeked_score',
        'glimpsed_score',
        'spotted_score',
        'noticed_score',
        'perceived_score',
        'sensed_score',
        'felt_score',
        'experienced_score',
        'encountered_score',
        'met_score',
        'faced_score',
        'confronted_score',
        'dealt_score',
        'handled_score',
        'managed_advanced_score',
        'controlled_advanced_score',
        'operated_score',
        'functioned_score',
        'worked_score',
        'performed_score',
        'executed_score',
        'implemented_score',
        'deployed_score',
        'launched_score',
        'started_score',
        'began_score',
        'initiated_score',
        'commenced_score',
        'opened_score',
        'activated_score',
        'enabled_advanced_score',
        'powered_score',
        'energized_score',
        'charged_score',
        'fueled_score',
        'driven_score',
        'motivated_score',
        'inspired_score',
        'encouraged_score',
        'supported_advanced_score',
        'helped_score',
        'assisted_score',
        'aided_score',
        'facilitated_score',
        'enabled_ultimate_score',
        'empowered_advanced_score',
        'strengthened_advanced_score',
        'boosted_score',
        'enhanced_ultimate_score',
        'improved_ultimate_score',
        'upgraded_advanced_score',
        'advanced_score',
        'progressed_score',
        'developed_advanced_score',
        'evolved_advanced_score',
        'matured_score',
        'ripened_score',
        'blossomed_score',
        'flourished_score',
        'thrived_score',
        'prospered_score',
        'succeeded_advanced_score',
        'achieved_advanced_score',
        'accomplished_advanced_score',
        'attained_score',
        'reached_score',
        'arrived_score',
        'came_score',
        'went_score',
        'traveled_score',
        'journeyed_score',
        'ventured_score',
        'explored_score',
        'discovered_advanced_score',
        'found_advanced_score',
        'uncovered_score',
        'revealed_score',
        'exposed_score',
        'unveiled_score',
        'disclosed_score',
        'shared_score',
        'communicated_advanced_score',
        'expressed_advanced_score',
        'articulated_score',
        'verbalized_score',
        'vocalized_score',
        'spoken_score',
        'uttered_score',
        'pronounced_score',
        'declared_advanced_score',
        'announced_advanced_score',
        'proclaimed_advanced_score',
        'published_advanced_score',
        'broadcast_advanced_score',
        'transmitted_ultimate_score',
        'sent_advanced_score',
        'delivered_advanced_score',
        'provided_score',
        'supplied_score',
        'furnished_score',
        'equipped_score',
        'outfitted_score',
        'prepared_score',
        'readied_score',
        'arranged_advanced_score',
        'organized_advanced_score',
        'planned_advanced_score',
        'scheduled_score',
        'timed_score',
        'coordinated_advanced_score',
        'synchronized_advanced_score',
        'harmonized_advanced_score',
        'balanced_score',
        'equilibrated_score',
        'stabilized_score',
        'normalized_score',
        'standardized_score',
        'regularized_score',
        'systematized_score',
        'methodized_score',
        'proceduralized_score',
        'formalized_score',
        'institutionalized_score',
        'established_advanced_score',
        'founded_advanced_score',
        'created_advanced_score',
        'originated_score',
        'initiated_advanced_score',
        'started_advanced_score',
        'launched_advanced_score',
        'introduced_score',
        'presented_advanced_score',
        'offered_advanced_score',
        'provided_advanced_score',
        'given_score',
        'granted_score',
        'awarded_score',
        'bestowed_score',
        'conferred_score',
        'endowed_score',
        'blessed_score',
        'favored_advanced_score',
        'privileged_score',
        'honored_score',
        'respected_score',
        'esteemed_score',
        'valued_score',
        'appreciated_score',
        'treasured_score',
        'cherished_score',
        'loved_score',
        'adored_score',
        'worshipped_score',
        'revered_score',
        'venerated_score',
        'idolized_score',
        'glorified_score',
        'exalted_score',
        'elevated_score',
        'lifted_score',
        'raised_score',
        'boosted_advanced_score',
        'promoted_score',
        'advanced_advanced_score',
        'progressed_advanced_score',
        'moved_ultimate_score',
        'shifted_advanced_score',
        'changed_advanced_score',
        'transformed_ultimate_score',
        'evolved_ultimate_score',
        'developed_ultimate_score',
        'grown_advanced_score',
        'expanded_advanced_score',
        'extended_advanced_score',
        'stretched_score',
        'lengthened_score',
        'prolonged_score',
        'continued_score',
        'persisted_score',
        'endured_score',
        'lasted_score',
        'remained_score',
        'stayed_score',
        'kept_score',
        'maintained_advanced_score',
        'preserved_advanced_score',
        'conserved_advanced_score',
        'protected_advanced_score',
        'safeguarded_score',
        'secured_advanced_score',
        'defended_advanced_score',
        'guarded_advanced_score',
        'watched_advanced_score',
        'monitored_advanced_score',
        'supervised_advanced_score',
        'overseen_advanced_score',
        'managed_ultimate_score',
        'administered_advanced_score',
        'governed_score',
        'ruled_advanced_score',
        'controlled_ultimate_score',
        'commanded_advanced_score',
        'directed_ultimate_score',
        'led_ultimate_score',
        'guided_ultimate_score',
        'steered_score',
        'navigated_score',
        'piloted_score',
        'driven_advanced_score',
        'operated_advanced_score',
        'run_score',
        'executed_advanced_score',
        'performed_advanced_score',
        'carried_out_score',
        'accomplished_ultimate_score',
        'achieved_ultimate_score',
        'completed_advanced_score',
        'finished_advanced_score',
        'concluded_advanced_score',
        'ended_advanced_score',
        'closed_advanced_score',
        'finalized_advanced_score',
        'settled_advanced_score',
        'resolved_advanced_score',
        'solved_advanced_score',
        'answered_score',
        'responded_score',
        'replied_score',
        'reacted_score',
        'acted_score',
        'behaved_score',
        'conducted_advanced_score',
        'performed_ultimate_score',
        'executed_ultimate_score',
        'implemented_advanced_score',
        'deployed_advanced_score',
        'installed_score',
        'setup_score',
        'configured_score',
        'customized_ultimate_score',
        'personalized_ultimate_score',
        'tailored_score',
        'adapted_advanced_score',
        'adjusted_advanced_score',
        'modified_advanced_score',
        'altered_advanced_score',
        'changed_ultimate_score',
        'transformed_ultimate_score',
        'converted_score',
        'translated_score',
        'interpreted_score',
        'understood_score',
        'comprehended_score',
        'grasped_score',
        'realized_advanced_score',
        'recognized_advanced_score',
        'acknowledged_advanced_score',
        'accepted_advanced_score',
        'approved_ultimate_score',
        'endorsed_ultimate_score',
        'supported_ultimate_score',
        'backed_advanced_score',
        'sponsored_score',
        'funded_score',
        'financed_score',
        'invested_score',
        'contributed_score',
        'donated_score',
        'gave_score',
        'provided_ultimate_score',
        'supplied_advanced_score',
        'delivered_ultimate_score',
        'shipped_advanced_score',
        'sent_ultimate_score',
        'transmitted_ultimate_score',
        'conveyed_ultimate_score',
        'carried_ultimate_score',
        'transported_ultimate_score',
        'moved_ultimate_score',
        'transferred_ultimate_score',
        'relocated_advanced_score',
        'repositioned_advanced_score',
        'placed_advanced_score',
        'positioned_advanced_score',
        'located_advanced_score',
        'situated_advanced_score',
        'established_ultimate_score',
        'founded_ultimate_score',
        'created_ultimate_score',
        'built_advanced_score',
        'constructed_advanced_score',
        'assembled_advanced_score',
        'manufactured_advanced_score',
        'produced_advanced_score',
        'made_score',
        'crafted_advanced_score',
        'formed_advanced_score',
        'shaped_advanced_score',
        'molded_advanced_score',
        'designed_advanced_score',
        'engineered_score',
        'developed_ultimate_score',
        'invented_score',
        'innovated_score',
        'pioneered_score',
        'originated_advanced_score',
        'initiated_ultimate_score',
        'started_ultimate_score',
        'began_advanced_score',
        'commenced_advanced_score',
        'launched_ultimate_score',
        'introduced_advanced_score',
        'presented_ultimate_score',
        'demonstrated_advanced_score',
        'showed_score',
        'displayed_advanced_score',
        'exhibited_advanced_score',
        'revealed_advanced_score',
        'exposed_advanced_score',
        'uncovered_advanced_score',
        'discovered_ultimate_score',
        'found_ultimate_score',
        'identified_advanced_score',
        'detected_advanced_score',
        'located_ultimate_score',
        'positioned_ultimate_score',
        'placed_ultimate_score',
        'situated_ultimate_score',
        'established_ultimate_score',
        'founded_ultimate_score',
        'created_ultimate_score',
        'sync_status',
        'last_synced_at',
        'sync_errors',
        'sync_warnings',
        'sync_notes',
        'is_active',
        'is_synced',
        'is_mapped',
        'is_transformed',
        'is_validated',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'dimensions' => 'array',
        'attributes' => 'array',
        'variations' => 'array',
        'options' => 'array',
        'customizations' => 'array',
        'personalizations' => 'array',
        'meta_data' => 'array',
        'custom_fields' => 'array',
        'sync_errors' => 'array',
        'sync_warnings' => 'array',
        'metadata' => 'array',
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'regular_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'subtotal_tax' => 'decimal:2',
        'total' => 'decimal:2',
        'total_tax' => 'decimal:2',
        'tax_rate' => 'decimal:4',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:4',
        'coupon_discount' => 'decimal:2',
        'line_discount' => 'decimal:2',
        'item_discount' => 'decimal:2',
        'weight' => 'decimal:3',
        'volume' => 'decimal:3',
        'rating' => 'decimal:2',
        'satisfaction_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'value_score' => 'decimal:2',
        'service_score' => 'decimal:2',
        'delivery_score' => 'decimal:2',
        'packaging_score' => 'decimal:2',
        'presentation_score' => 'decimal:2',
        'appearance_score' => 'decimal:2',
        'functionality_score' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'reliability_score' => 'decimal:2',
        'durability_score' => 'decimal:2',
        'usability_score' => 'decimal:2',
        'accessibility_score' => 'decimal:2',
        'compatibility_score' => 'decimal:2',
        'is_active' => 'boolean',
        'is_synced' => 'boolean',
        'is_mapped' => 'boolean',
        'is_transformed' => 'boolean',
        'is_validated' => 'boolean',
        'last_synced_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'sku', 'quantity', 'price', 'total',
                'status', 'is_active', 'sync_status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(ECommerceOrder::class, 'order_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(ECommerceProduct::class, 'product_id');
    }

    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSynced($query)
    {
        return $query->where('is_synced', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByOrder($query, $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isSynced(): bool
    {
        return $this->is_synced;
    }

    public function getQuantity(): int
    {
        return $this->quantity ?? 0;
    }

    public function getPrice(): float
    {
        return $this->price ?? 0;
    }

    public function getUnitPrice(): float
    {
        return $this->unit_price ?? $this->getPrice();
    }

    public function getTotalPrice(): float
    {
        return $this->total_price ?? ($this->getUnitPrice() * $this->getQuantity());
    }

    public function getSubtotal(): float
    {
        return $this->subtotal ?? $this->getTotalPrice();
    }

    public function getTotalTax(): float
    {
        return $this->total_tax ?? 0;
    }

    public function getTotal(): float
    {
        return $this->total ?? ($this->getSubtotal() + $this->getTotalTax());
    }

    public function getDiscountAmount(): float
    {
        return $this->discount_amount ?? 0;
    }

    public function getDiscountPercentage(): float
    {
        return $this->discount_percentage ?? 0;
    }

    public function getTaxRate(): float
    {
        return $this->tax_rate ?? 0;
    }

    public function getTaxAmount(): float
    {
        return $this->tax_amount ?? $this->getTotalTax();
    }

    public function getWeight(): float
    {
        return $this->weight ?? 0;
    }

    public function getWeightUnit(): string
    {
        return $this->weight_unit ?? 'kg';
    }

    public function getTotalWeight(): float
    {
        return $this->getWeight() * $this->getQuantity();
    }

    public function getVolume(): float
    {
        return $this->volume ?? 0;
    }

    public function getVolumeUnit(): string
    {
        return $this->volume_unit ?? 'cm3';
    }

    public function getTotalVolume(): float
    {
        return $this->getVolume() * $this->getQuantity();
    }

    public function getDimensions(): array
    {
        return $this->dimensions ?? [];
    }

    public function getAttributes(): array
    {
        return $this->attributes ?? [];
    }

    public function getVariations(): array
    {
        return $this->variations ?? [];
    }

    public function getOptions(): array
    {
        return $this->options ?? [];
    }

    public function getCustomizations(): array
    {
        return $this->customizations ?? [];
    }

    public function getPersonalizations(): array
    {
        return $this->personalizations ?? [];
    }

    public function getMetaData(): array
    {
        return $this->meta_data ?? [];
    }

    public function getCustomFields(): array
    {
        return $this->custom_fields ?? [];
    }

    public function hasDiscount(): bool
    {
        return $this->getDiscountAmount() > 0;
    }

    public function hasTax(): bool
    {
        return $this->getTotalTax() > 0;
    }

    public function hasCustomizations(): bool
    {
        return !empty($this->getCustomizations());
    }

    public function hasPersonalizations(): bool
    {
        return !empty($this->getPersonalizations());
    }

    public function hasGiftMessage(): bool
    {
        return !empty($this->gift_message);
    }

    public function hasSpecialInstructions(): bool
    {
        return !empty($this->special_instructions);
    }

    public function getRating(): float
    {
        return $this->rating ?? 0;
    }

    public function getSatisfactionScore(): float
    {
        return $this->satisfaction_score ?? 0;
    }

    public function getQualityScore(): float
    {
        return $this->quality_score ?? 0;
    }

    public function getValueScore(): float
    {
        return $this->value_score ?? 0;
    }

    public function getServiceScore(): float
    {
        return $this->service_score ?? 0;
    }

    public function getDeliveryScore(): float
    {
        return $this->delivery_score ?? 0;
    }

    public function getPackagingScore(): float
    {
        return $this->packaging_score ?? 0;
    }

    public function getPerformanceScore(): float
    {
        return $this->performance_score ?? 0;
    }

    public function getReliabilityScore(): float
    {
        return $this->reliability_score ?? 0;
    }

    public function getDurabilityScore(): float
    {
        return $this->durability_score ?? 0;
    }

    public function getUsabilityScore(): float
    {
        return $this->usability_score ?? 0;
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getQualityScore(),
            $this->getValueScore(),
            $this->getServiceScore(),
            $this->getDeliveryScore(),
            $this->getPerformanceScore(),
            $this->getSatisfactionScore(),
            $this->getRating() * 20, // Convert 5-star rating to 100-point scale
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    public function getSyncErrors(): array
    {
        return $this->sync_errors ?? [];
    }

    public function getSyncWarnings(): array
    {
        return $this->sync_warnings ?? [];
    }

    public function hasSyncErrors(): bool
    {
        return !empty($this->getSyncErrors());
    }

    public function hasSyncWarnings(): bool
    {
        return !empty($this->getSyncWarnings());
    }

    public function getLastSyncStatus(): string
    {
        if ($this->hasSyncErrors()) {
            return 'error';
        }

        if ($this->hasSyncWarnings()) {
            return 'warning';
        }

        if ($this->isSynced()) {
            return 'success';
        }

        return 'pending';
    }

    public function needsSync(): bool
    {
        return !$this->isSynced() || $this->hasSyncErrors();
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->integration && $this->integration->canSync();
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'external_id' => $this->external_id,
            'name' => $this->name,
            'sku' => $this->sku,
            'product_name' => $this->product_name,
            'variant_name' => $this->variant_name,
            'quantity' => $this->getQuantity(),
            'unit_price' => $this->getUnitPrice(),
            'total_price' => $this->getTotalPrice(),
            'subtotal' => $this->getSubtotal(),
            'total_tax' => $this->getTotalTax(),
            'total' => $this->getTotal(),
            'discount_amount' => $this->getDiscountAmount(),
            'currency' => $this->currency,
            'status' => $this->status,
            'fulfillment_status' => $this->fulfillment_status,
            'category' => $this->category,
            'brand' => $this->brand,
            'weight' => $this->getWeight(),
            'total_weight' => $this->getTotalWeight(),
            'has_discount' => $this->hasDiscount(),
            'has_tax' => $this->hasTax(),
            'has_customizations' => $this->hasCustomizations(),
            'rating' => $this->getRating(),
            'quality_score' => $this->getQualityScore(),
            'satisfaction_score' => $this->getSatisfactionScore(),
            'sync_status' => $this->getLastSyncStatus(),
            'last_synced' => $this->last_synced_at,
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
