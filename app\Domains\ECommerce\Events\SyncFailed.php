<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث فشل المزامنة
 * يتم إطلاقه عند فشل عملية المزامنة
 */
class SyncFailed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public ECommerceSyncLog $syncLog;
    public \Exception $exception;
    public array $errorContext;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceIntegration $integration,
        ECommerceSyncLog $syncLog,
        \Exception $exception,
        array $errorContext = []
    ) {
        $this->integration = $integration;
        $this->syncLog = $syncLog;
        $this->exception = $exception;
        $this->errorContext = $errorContext;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'sync_log_id' => $this->syncLog->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'sync_type' => $this->syncLog->sync_type,
            'entity_type' => $this->syncLog->entity_type,
            'sync_direction' => $this->syncLog->sync_direction,
            'sync_mode' => $this->syncLog->sync_mode,
            'started_at' => $this->syncLog->started_at,
            'failed_at' => $this->syncLog->completed_at ?? now(),
            'duration' => $this->syncLog->duration,
            'records_total' => $this->syncLog->records_total,
            'records_processed' => $this->syncLog->records_processed,
            'records_successful' => $this->syncLog->records_successful,
            'records_failed' => $this->syncLog->records_failed,
            'error_message' => $this->exception->getMessage(),
            'error_code' => $this->exception->getCode(),
            'error_type' => get_class($this->exception),
            'error_context' => $this->errorContext,
        ];
    }

    /**
     * الحصول على رسالة الخطأ
     */
    public function getErrorMessage(): string
    {
        return $this->exception->getMessage();
    }

    /**
     * الحصول على رمز الخطأ
     */
    public function getErrorCode(): int
    {
        return $this->exception->getCode();
    }

    /**
     * الحصول على نوع الخطأ
     */
    public function getErrorType(): string
    {
        return get_class($this->exception);
    }

    /**
     * تحديد ما إذا كان الخطأ قابل للإعادة
     */
    public function isRetryable(): bool
    {
        // تحديد الأخطاء القابلة للإعادة
        $retryableErrors = [
            'timeout',
            'connection',
            'network',
            'rate limit',
            'server error',
            '5xx',
        ];

        $errorMessage = strtolower($this->getErrorMessage());
        
        foreach ($retryableErrors as $retryableError) {
            if (str_contains($errorMessage, $retryableError)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تحديد ما إذا كان الخطأ مؤقت
     */
    public function isTemporary(): bool
    {
        return $this->isRetryable();
    }
}
