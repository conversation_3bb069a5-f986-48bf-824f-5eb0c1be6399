<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * نموذج سجلات طلبات API المتقدمة
 * يسجل جميع طلبات API مع تحليلات متقدمة
 */
class ApiRequestLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_gateway_id',
        'api_endpoint_id',
        'api_key_id',
        'request_id',
        'session_id',
        'trace_id',
        'parent_trace_id',
        'method',
        'endpoint',
        'full_url',
        'protocol',
        'version',
        'request_headers',
        'request_data',
        'request_size',
        'response_headers',
        'response_data',
        'response_size',
        'status_code',
        'status',
        'response_time',
        'processing_time',
        'queue_time',
        'database_time',
        'cache_time',
        'external_api_time',
        'memory_usage',
        'cpu_usage',
        'error_message',
        'error_code',
        'error_type',
        'stack_trace',
        'ip_address',
        'user_agent',
        'referer',
        'origin',
        'country_code',
        'city',
        'isp',
        'device_type',
        'browser',
        'os',
        'user_id',
        'company_id',
        'rate_limit_hit',
        'cache_hit',
        'cache_miss',
        'circuit_breaker_state',
        'retry_count',
        'webhook_delivery_id',
        'correlation_id',
        'business_context',
        'security_flags',
        'compliance_flags',
        'analytics_tags',
        'custom_metrics',
        'metadata',
        'created_at',
    ];

    protected $casts = [
        'request_headers' => 'array',
        'request_data' => 'array',
        'response_headers' => 'array',
        'response_data' => 'array',
        'security_flags' => 'array',
        'compliance_flags' => 'array',
        'analytics_tags' => 'array',
        'custom_metrics' => 'array',
        'business_context' => 'array',
        'metadata' => 'array',
        'rate_limit_hit' => 'boolean',
        'cache_hit' => 'boolean',
        'cache_miss' => 'boolean',
        'response_time' => 'float',
        'processing_time' => 'float',
        'queue_time' => 'float',
        'database_time' => 'float',
        'cache_time' => 'float',
        'external_api_time' => 'float',
        'memory_usage' => 'integer',
        'cpu_usage' => 'float',
    ];

    /**
     * حالات الطلب
     */
    const STATUSES = [
        'success' => 'نجح',
        'failed' => 'فشل',
        'timeout' => 'انتهت المهلة',
        'rate_limited' => 'محدود المعدل',
        'unauthorized' => 'غير مخول',
        'forbidden' => 'محظور',
        'not_found' => 'غير موجود',
        'server_error' => 'خطأ خادم',
        'circuit_breaker' => 'قاطع الدائرة',
        'retry_exhausted' => 'استنفدت المحاولات',
    ];

    /**
     * أنواع الأخطاء
     */
    const ERROR_TYPES = [
        'validation' => 'خطأ تحقق',
        'authentication' => 'خطأ مصادقة',
        'authorization' => 'خطأ تخويل',
        'rate_limit' => 'تجاوز الحد',
        'timeout' => 'انتهاء المهلة',
        'network' => 'خطأ شبكة',
        'database' => 'خطأ قاعدة بيانات',
        'external_api' => 'خطأ API خارجي',
        'business_logic' => 'خطأ منطق عمل',
        'system' => 'خطأ نظام',
        'security' => 'خطأ أمني',
        'compliance' => 'خطأ امتثال',
    ];

    /**
     * أنواع الأجهزة
     */
    const DEVICE_TYPES = [
        'desktop' => 'سطح مكتب',
        'mobile' => 'جوال',
        'tablet' => 'لوحي',
        'bot' => 'روبوت',
        'api_client' => 'عميل API',
        'webhook' => 'ويب هوك',
        'unknown' => 'غير معروف',
    ];

    /**
     * العلاقة مع بوابة API
     */
    public function apiGateway(): BelongsTo
    {
        return $this->belongsTo(ApiGateway::class);
    }

    /**
     * العلاقة مع نقطة النهاية
     */
    public function apiEndpoint(): BelongsTo
    {
        return $this->belongsTo(ApiEndpoint::class);
    }

    /**
     * العلاقة مع مفتاح API
     */
    public function apiKey(): BelongsTo
    {
        return $this->belongsTo(ApiKey::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Compliance\Models\Company::class);
    }

    /**
     * تسجيل طلب API جديد
     */
    public static function logRequest(array $data): self
    {
        // إضافة معرفات فريدة
        $data['request_id'] = $data['request_id'] ?? uniqid('req_');
        $data['trace_id'] = $data['trace_id'] ?? uniqid('trace_');
        $data['session_id'] = $data['session_id'] ?? session()->getId();

        // تحليل User Agent
        if (isset($data['user_agent'])) {
            $userAgentData = self::parseUserAgent($data['user_agent']);
            $data = array_merge($data, $userAgentData);
        }

        // تحليل IP
        if (isset($data['ip_address'])) {
            $ipData = self::analyzeIp($data['ip_address']);
            $data = array_merge($data, $ipData);
        }

        // حساب أحجام البيانات
        $data['request_size'] = isset($data['request_data']) ? strlen(json_encode($data['request_data'])) : 0;
        $data['response_size'] = isset($data['response_data']) ? strlen(json_encode($data['response_data'])) : 0;

        // إضافة معلومات الأداء
        $data['memory_usage'] = memory_get_usage(true);
        $data['cpu_usage'] = sys_getloadavg()[0] ?? 0;

        return self::create($data);
    }

    /**
     * الحصول على إحصائيات الأداء
     */
    public static function getPerformanceStats(array $filters = []): array
    {
        $query = self::query();

        // تطبيق المرشحات
        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['api_gateway_id'])) {
            $query->where('api_gateway_id', $filters['api_gateway_id']);
        }

        if (isset($filters['endpoint'])) {
            $query->where('endpoint', 'like', "%{$filters['endpoint']}%");
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = "success" THEN 1 END) as successful_requests,
            COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_requests,
            AVG(response_time) as avg_response_time,
            MIN(response_time) as min_response_time,
            MAX(response_time) as max_response_time,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY response_time) as median_response_time,
            PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY response_time) as p95_response_time,
            PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY response_time) as p99_response_time,
            AVG(memory_usage) as avg_memory_usage,
            MAX(memory_usage) as max_memory_usage,
            AVG(cpu_usage) as avg_cpu_usage,
            MAX(cpu_usage) as max_cpu_usage,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size,
            COUNT(CASE WHEN cache_hit = 1 THEN 1 END) as cache_hits,
            COUNT(CASE WHEN cache_miss = 1 THEN 1 END) as cache_misses,
            COUNT(CASE WHEN rate_limit_hit = 1 THEN 1 END) as rate_limit_hits
        ')->first();

        // حساب معدلات إضافية
        $successRate = $stats->total_requests > 0 ? 
            ($stats->successful_requests / $stats->total_requests) * 100 : 0;

        $cacheHitRate = ($stats->cache_hits + $stats->cache_misses) > 0 ? 
            ($stats->cache_hits / ($stats->cache_hits + $stats->cache_misses)) * 100 : 0;

        return [
            'total_requests' => $stats->total_requests,
            'successful_requests' => $stats->successful_requests,
            'failed_requests' => $stats->failed_requests,
            'success_rate' => round($successRate, 2),
            'avg_response_time' => round($stats->avg_response_time, 3),
            'min_response_time' => round($stats->min_response_time, 3),
            'max_response_time' => round($stats->max_response_time, 3),
            'median_response_time' => round($stats->median_response_time, 3),
            'p95_response_time' => round($stats->p95_response_time, 3),
            'p99_response_time' => round($stats->p99_response_time, 3),
            'avg_memory_usage' => self::formatBytes($stats->avg_memory_usage),
            'max_memory_usage' => self::formatBytes($stats->max_memory_usage),
            'avg_cpu_usage' => round($stats->avg_cpu_usage, 2),
            'max_cpu_usage' => round($stats->max_cpu_usage, 2),
            'total_data_transferred' => self::formatBytes($stats->total_request_size + $stats->total_response_size),
            'cache_hit_rate' => round($cacheHitRate, 2),
            'rate_limit_hits' => $stats->rate_limit_hits,
        ];
    }

    /**
     * الحصول على إحصائيات الاستخدام حسب الوقت
     */
    public static function getUsageByTime(string $interval = 'hour', int $limit = 24): array
    {
        $groupBy = match ($interval) {
            'minute' => "DATE_FORMAT(created_at, '%Y-%m-%d %H:%i')",
            'hour' => "DATE_FORMAT(created_at, '%Y-%m-%d %H:00')",
            'day' => "DATE_FORMAT(created_at, '%Y-%m-%d')",
            'week' => "YEARWEEK(created_at)",
            'month' => "DATE_FORMAT(created_at, '%Y-%m')",
            default => "DATE_FORMAT(created_at, '%Y-%m-%d %H:00')",
        };

        return self::selectRaw("
            {$groupBy} as time_period,
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_requests,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_requests,
            AVG(response_time) as avg_response_time
        ")
        ->where('created_at', '>=', now()->sub($interval, $limit))
        ->groupBy('time_period')
        ->orderBy('time_period', 'desc')
        ->limit($limit)
        ->get()
        ->toArray();
    }

    /**
     * الحصول على أكثر نقاط النهاية استخداماً
     */
    public static function getTopEndpoints(int $limit = 10): array
    {
        return self::selectRaw('
            endpoint,
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = "success" THEN 1 END) as successful_requests,
            AVG(response_time) as avg_response_time
        ')
        ->groupBy('endpoint')
        ->orderBy('total_requests', 'desc')
        ->limit($limit)
        ->get()
        ->toArray();
    }

    /**
     * الحصول على أكثر الأخطاء شيوعاً
     */
    public static function getTopErrors(int $limit = 10): array
    {
        return self::selectRaw('
            error_type,
            error_code,
            error_message,
            COUNT(*) as error_count,
            MAX(created_at) as last_occurrence
        ')
        ->whereNotNull('error_message')
        ->groupBy('error_type', 'error_code', 'error_message')
        ->orderBy('error_count', 'desc')
        ->limit($limit)
        ->get()
        ->toArray();
    }

    /**
     * الحصول على إحصائيات الموقع الجغرافي
     */
    public static function getGeographicStats(): array
    {
        return self::selectRaw('
            country_code,
            city,
            COUNT(*) as request_count,
            COUNT(DISTINCT ip_address) as unique_ips
        ')
        ->whereNotNull('country_code')
        ->groupBy('country_code', 'city')
        ->orderBy('request_count', 'desc')
        ->get()
        ->toArray();
    }

    /**
     * الحصول على إحصائيات الأجهزة
     */
    public static function getDeviceStats(): array
    {
        return self::selectRaw('
            device_type,
            browser,
            os,
            COUNT(*) as request_count
        ')
        ->groupBy('device_type', 'browser', 'os')
        ->orderBy('request_count', 'desc')
        ->get()
        ->toArray();
    }

    /**
     * البحث في السجلات
     */
    public static function search(array $criteria): \Illuminate\Database\Eloquent\Collection
    {
        $query = self::query();

        if (isset($criteria['request_id'])) {
            $query->where('request_id', $criteria['request_id']);
        }

        if (isset($criteria['trace_id'])) {
            $query->where('trace_id', $criteria['trace_id']);
        }

        if (isset($criteria['ip_address'])) {
            $query->where('ip_address', $criteria['ip_address']);
        }

        if (isset($criteria['user_id'])) {
            $query->where('user_id', $criteria['user_id']);
        }

        if (isset($criteria['endpoint'])) {
            $query->where('endpoint', 'like', "%{$criteria['endpoint']}%");
        }

        if (isset($criteria['status'])) {
            $query->where('status', $criteria['status']);
        }

        if (isset($criteria['error_type'])) {
            $query->where('error_type', $criteria['error_type']);
        }

        if (isset($criteria['start_date'])) {
            $query->where('created_at', '>=', $criteria['start_date']);
        }

        if (isset($criteria['end_date'])) {
            $query->where('created_at', '<=', $criteria['end_date']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * تنظيف السجلات القديمة
     */
    public static function cleanup(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return self::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * تحليل User Agent
     */
    protected static function parseUserAgent(string $userAgent): array
    {
        // تحليل بسيط للـ User Agent
        // في التطبيق الحقيقي، يمكن استخدام مكتبة متخصصة
        
        $data = [
            'device_type' => 'unknown',
            'browser' => 'unknown',
            'os' => 'unknown',
        ];

        // تحديد نوع الجهاز
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            $data['device_type'] = 'mobile';
        } elseif (preg_match('/Tablet/', $userAgent)) {
            $data['device_type'] = 'tablet';
        } elseif (preg_match('/bot|crawler|spider/i', $userAgent)) {
            $data['device_type'] = 'bot';
        } else {
            $data['device_type'] = 'desktop';
        }

        // تحديد المتصفح
        if (preg_match('/Chrome/', $userAgent)) {
            $data['browser'] = 'Chrome';
        } elseif (preg_match('/Firefox/', $userAgent)) {
            $data['browser'] = 'Firefox';
        } elseif (preg_match('/Safari/', $userAgent)) {
            $data['browser'] = 'Safari';
        } elseif (preg_match('/Edge/', $userAgent)) {
            $data['browser'] = 'Edge';
        }

        // تحديد نظام التشغيل
        if (preg_match('/Windows/', $userAgent)) {
            $data['os'] = 'Windows';
        } elseif (preg_match('/Mac/', $userAgent)) {
            $data['os'] = 'macOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            $data['os'] = 'Linux';
        } elseif (preg_match('/Android/', $userAgent)) {
            $data['os'] = 'Android';
        } elseif (preg_match('/iOS/', $userAgent)) {
            $data['os'] = 'iOS';
        }

        return $data;
    }

    /**
     * تحليل IP
     */
    protected static function analyzeIp(string $ip): array
    {
        // تحليل بسيط للـ IP
        // في التطبيق الحقيقي، يمكن استخدام خدمة GeoIP
        
        return [
            'country_code' => 'XX',
            'city' => 'Unknown',
            'isp' => 'Unknown',
        ];
    }

    /**
     * تنسيق البايتات
     */
    protected static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * الحصول على معرف الارتباط
     */
    public function getCorrelationId(): string
    {
        return $this->correlation_id ?? $this->trace_id ?? $this->request_id;
    }

    /**
     * التحقق من وجود أخطاء أمنية
     */
    public function hasSecurityFlags(): bool
    {
        return !empty($this->security_flags);
    }

    /**
     * التحقق من وجود مشاكل امتثال
     */
    public function hasComplianceFlags(): bool
    {
        return !empty($this->compliance_flags);
    }

    /**
     * الحصول على مدة الاستجابة بالتصنيف
     */
    public function getResponseTimeCategory(): string
    {
        return match (true) {
            $this->response_time < 0.1 => 'excellent',
            $this->response_time < 0.5 => 'good',
            $this->response_time < 1.0 => 'acceptable',
            $this->response_time < 2.0 => 'slow',
            default => 'very_slow',
        };
    }
}
