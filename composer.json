{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.1", "inertiajs/inertia-laravel": "^2.0", "intervention/image": "^3.11", "laravel/framework": "^12.0", "laravel/horizon": "^5.33", "laravel/passport": "^13.0", "laravel/sanctum": "^4.2", "laravel/slack-notification-channel": "^3.6", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.29", "mcamara/laravel-localization": "^2.3", "rap2hpoutre/fast-excel": "^5.6", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-permission": "^6.21", "spatie/laravel-query-builder": "^6.3", "stripe/stripe-php": "^17.4"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/dusk": "^8.3", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "laravel/telescope": "^5.10", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}