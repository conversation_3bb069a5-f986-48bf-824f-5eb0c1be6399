<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج خط القيد المحاسبي
 * يمثل الخطوط الفردية في القيد المحاسبي
 */
class JournalEntryLine extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'journal_entry_id',
        'account_id',
        'debit_amount',
        'credit_amount',
        'description',
        'cost_center_id',
        'project_id',
        'department_id',
        'employee_id',
        'tax_code',
        'analytics_tags',
        'line_number',
        'reference',
        'metadata',
    ];

    protected $casts = [
        'debit_amount' => 'decimal:2',
        'credit_amount' => 'decimal:2',
        'analytics_tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع القيد المحاسبي
     */
    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    /**
     * العلاقة مع الحساب
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * العلاقة مع مركز التكلفة
     */
    public function costCenter(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Projects\Models\CostCenter::class);
    }

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Projects\Models\Project::class);
    }

    /**
     * العلاقة مع القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Department::class);
    }

    /**
     * العلاقة مع الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class);
    }

    /**
     * الحصول على المبلغ الصافي
     */
    public function getNetAmountAttribute(): float
    {
        return $this->debit_amount - $this->credit_amount;
    }

    /**
     * التحقق من كون الخط مدين
     */
    public function isDebit(): bool
    {
        return $this->debit_amount > 0;
    }

    /**
     * التحقق من كون الخط دائن
     */
    public function isCredit(): bool
    {
        return $this->credit_amount > 0;
    }

    /**
     * الحصول على نوع الخط
     */
    public function getTypeAttribute(): string
    {
        return $this->isDebit() ? 'DEBIT' : 'CREDIT';
    }

    /**
     * الحصول على المبلغ الأساسي
     */
    public function getAmountAttribute(): float
    {
        return $this->debit_amount ?: $this->credit_amount;
    }

    /**
     * تحديث رصيد الحساب
     */
    public function updateAccountBalance(): void
    {
        $account = $this->account;
        
        if ($this->debit_amount > 0) {
            $account->increment('debit_balance', $this->debit_amount);
        }
        
        if ($this->credit_amount > 0) {
            $account->increment('credit_balance', $this->credit_amount);
        }

        $account->updateCurrentBalance();
    }

    /**
     * عكس تأثير الخط على رصيد الحساب
     */
    public function reverseAccountBalance(): void
    {
        $account = $this->account;
        
        if ($this->debit_amount > 0) {
            $account->decrement('debit_balance', $this->debit_amount);
        }
        
        if ($this->credit_amount > 0) {
            $account->decrement('credit_balance', $this->credit_amount);
        }

        $account->updateCurrentBalance();
    }

    /**
     * التحقق من صحة الخط
     */
    public function validate(): array
    {
        $errors = [];

        // التحقق من وجود مبلغ
        if ($this->debit_amount == 0 && $this->credit_amount == 0) {
            $errors[] = 'يجب أن يحتوي الخط على مبلغ مدين أو دائن';
        }

        // التحقق من عدم وجود مبلغ مدين ودائن معاً
        if ($this->debit_amount > 0 && $this->credit_amount > 0) {
            $errors[] = 'لا يمكن أن يحتوي الخط على مبلغ مدين ودائن معاً';
        }

        // التحقق من صحة الحساب
        if (!$this->account || !$this->account->is_active) {
            $errors[] = 'الحساب غير صحيح أو غير نشط';
        }

        return $errors;
    }

    /**
     * نسخ الخط
     */
    public function duplicate(int $newJournalEntryId): self
    {
        return self::create([
            'journal_entry_id' => $newJournalEntryId,
            'account_id' => $this->account_id,
            'debit_amount' => $this->debit_amount,
            'credit_amount' => $this->credit_amount,
            'description' => $this->description,
            'cost_center_id' => $this->cost_center_id,
            'project_id' => $this->project_id,
            'department_id' => $this->department_id,
            'employee_id' => $this->employee_id,
            'tax_code' => $this->tax_code,
            'analytics_tags' => $this->analytics_tags,
            'reference' => $this->reference,
            'metadata' => $this->metadata,
        ]);
    }

    /**
     * تحويل إلى مصفوفة للتصدير
     */
    public function toExportArray(): array
    {
        return [
            'account_code' => $this->account->code,
            'account_name' => $this->account->name,
            'debit_amount' => $this->debit_amount,
            'credit_amount' => $this->credit_amount,
            'description' => $this->description,
            'cost_center' => $this->costCenter?->name,
            'project' => $this->project?->name,
            'department' => $this->department?->name,
        ];
    }

    /**
     * البحث في الخطوط
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('description', 'LIKE', "%{$search}%")
              ->orWhere('reference', 'LIKE', "%{$search}%")
              ->orWhereHas('account', function ($accountQuery) use ($search) {
                  $accountQuery->where('name', 'LIKE', "%{$search}%")
                              ->orWhere('code', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * فلترة حسب الحساب
     */
    public function scopeForAccount($query, int $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    /**
     * فلترة حسب المشروع
     */
    public function scopeForProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * فلترة حسب مركز التكلفة
     */
    public function scopeForCostCenter($query, int $costCenterId)
    {
        return $query->where('cost_center_id', $costCenterId);
    }

    /**
     * فلترة الخطوط المدينة
     */
    public function scopeDebits($query)
    {
        return $query->where('debit_amount', '>', 0);
    }

    /**
     * فلترة الخطوط الدائنة
     */
    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * فلترة حسب الفترة
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
            $q->whereBetween('entry_date', [$startDate, $endDate]);
        });
    }
}
