<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الحساب المحاسبي
 * يدعم الدليل المحاسبي العام المغربي (PCGM) والمعايير الدولية
 */
class Account extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'code',
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'description',
        'account_type',
        'parent_id',
        'level',
        'is_active',
        'is_system',
        'balance',
        'debit_balance',
        'credit_balance',
        'currency',
        'tax_code',
        'opening_balance',
        'opening_balance_date',
        'chart_of_accounts_standard', // PCGM, IFRS, etc.
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'balance' => 'decimal:2',
        'debit_balance' => 'decimal:2',
        'credit_balance' => 'decimal:2',
        'opening_balance' => 'decimal:2',
        'opening_balance_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * أنواع الحسابات المحاسبية
     */
    public const ACCOUNT_TYPES = [
        'ASSET' => 'أصول',
        'LIABILITY' => 'خصوم',
        'EQUITY' => 'حقوق الملكية',
        'REVENUE' => 'إيرادات',
        'EXPENSE' => 'مصروفات',
        'COST_OF_GOODS_SOLD' => 'تكلفة البضاعة المباعة',
    ];

    /**
     * معايير الدليل المحاسبي المدعومة
     */
    public const CHART_STANDARDS = [
        'PCGM' => 'الدليل المحاسبي العام المغربي',
        'IFRS' => 'المعايير الدولية للتقارير المالية',
        'GAAP' => 'مبادئ المحاسبة المقبولة عموماً',
    ];

    /**
     * الحساب الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * الحسابات الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * جميع الحسابات الفرعية (متداخلة)
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * القيود المحاسبية المرتبطة بهذا الحساب
     */
    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class);
    }

    /**
     * التحقق من كون الحساب حساب أصول
     */
    public function isAsset(): bool
    {
        return $this->account_type === 'ASSET';
    }

    /**
     * التحقق من كون الحساب حساب خصوم
     */
    public function isLiability(): bool
    {
        return $this->account_type === 'LIABILITY';
    }

    /**
     * التحقق من كون الحساب حساب إيرادات
     */
    public function isRevenue(): bool
    {
        return $this->account_type === 'REVENUE';
    }

    /**
     * التحقق من كون الحساب حساب مصروفات
     */
    public function isExpense(): bool
    {
        return $this->account_type === 'EXPENSE';
    }

    /**
     * حساب الرصيد الحالي للحساب
     */
    public function calculateBalance(): float
    {
        $totalDebits = $this->journalEntries()->sum('debit_amount');
        $totalCredits = $this->journalEntries()->sum('credit_amount');

        // للأصول والمصروفات: الرصيد = المدين - الدائن
        if ($this->isAsset() || $this->isExpense()) {
            return $totalDebits - $totalCredits;
        }

        // للخصوم والإيرادات وحقوق الملكية: الرصيد = الدائن - المدين
        return $totalCredits - $totalDebits;
    }

    /**
     * تحديث رصيد الحساب
     */
    public function updateBalance(): void
    {
        $this->balance = $this->calculateBalance();
        $this->save();
    }

    /**
     * الحصول على اسم الحساب حسب اللغة
     */
    public function getLocalizedName(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            default => $this->name,
        };
    }

    /**
     * نطاق للحسابات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق للحسابات الرئيسية (بدون أب)
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * نطاق حسب نوع الحساب
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * نطاق حسب معيار الدليل المحاسبي
     */
    public function scopeOfStandard($query, string $standard)
    {
        return $query->where('chart_of_accounts_standard', $standard);
    }
}
