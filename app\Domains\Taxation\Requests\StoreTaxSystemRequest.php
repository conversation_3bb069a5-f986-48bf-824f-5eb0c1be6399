<?php

namespace App\Domains\Taxation\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب إنشاء نظام ضريبي جديد
 * تحقق شامل من البيانات مع دعم جميع الميزات المتقدمة
 */
class StoreTaxSystemRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create-tax-systems');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'name' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'country_code' => 'required|string|size:2|uppercase',
            'currency' => 'required|string|size:3|uppercase',
            'timezone' => 'required|string|max:50',

            // Status and Settings
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'version' => 'nullable|string|max:20',
            'effective_from' => 'required|date',
            'effective_to' => 'nullable|date|after:effective_from',

            // VAT Configuration
            'vat_enabled' => 'boolean',
            'vat_rates' => 'nullable|array',
            'vat_rates.*.name' => 'required_with:vat_rates|string|max:100',
            'vat_rates.*.name_ar' => 'required_with:vat_rates|string|max:100',
            'vat_rates.*.rate' => 'required_with:vat_rates|numeric|between:0,100',
            'vat_rates.*.is_default' => 'boolean',
            'vat_rates.*.conditions' => 'nullable|array',

            // Corporate Tax
            'corporate_tax_enabled' => 'boolean',
            'corporate_tax_rate' => 'nullable|numeric|between:0,100',
            'corporate_tax_threshold' => 'nullable|numeric|min:0',

            // Withholding Tax
            'withholding_tax_enabled' => 'boolean',
            'withholding_tax_rates' => 'nullable|array',
            'withholding_tax_rates.*.category' => 'required_with:withholding_tax_rates|string|max:100',
            'withholding_tax_rates.*.rate' => 'required_with:withholding_tax_rates|numeric|between:0,100',
            'withholding_tax_rates.*.threshold' => 'nullable|numeric|min:0',

            // Excise Tax
            'excise_tax_enabled' => 'boolean',
            'excise_tax_categories' => 'nullable|array',
            'excise_tax_categories.*.name' => 'required_with:excise_tax_categories|string|max:100',
            'excise_tax_categories.*.rate' => 'required_with:excise_tax_categories|numeric|min:0',
            'excise_tax_categories.*.unit' => 'required_with:excise_tax_categories|string|in:PERCENTAGE,FIXED_AMOUNT',

            // Customs Duty
            'customs_duty_enabled' => 'boolean',
            'customs_duty_rates' => 'nullable|array',
            'customs_duty_rates.*.hs_code' => 'required_with:customs_duty_rates|string|max:20',
            'customs_duty_rates.*.rate' => 'required_with:customs_duty_rates|numeric|between:0,100',
            'customs_duty_rates.*.description' => 'nullable|string|max:255',

            // Tax Periods
            'tax_periods' => 'nullable|array',
            'tax_periods.*.type' => 'required_with:tax_periods|string|in:MONTHLY,QUARTERLY,ANNUALLY',
            'tax_periods.*.tax_type' => 'required_with:tax_periods|string|in:VAT,CORPORATE,WITHHOLDING,EXCISE',
            'tax_periods.*.due_days' => 'required_with:tax_periods|integer|between:1,365',

            // Authority Integration
            'authority_integration' => 'nullable|array',
            'authority_integration.enabled' => 'boolean',
            'authority_integration.api_endpoint' => 'nullable|url',
            'authority_integration.api_key' => 'nullable|string|max:255',
            'authority_integration.certificate_path' => 'nullable|string|max:500',
            'authority_integration.test_mode' => 'boolean',

            // Compliance Settings
            'compliance_settings' => 'nullable|array',
            'compliance_settings.require_digital_signature' => 'boolean',
            'compliance_settings.require_tax_number' => 'boolean',
            'compliance_settings.auto_calculate_penalties' => 'boolean',
            'compliance_settings.penalty_rate' => 'nullable|numeric|between:0,100',

            // Localization
            'localization' => 'nullable|array',
            'localization.date_format' => 'nullable|string|max:20',
            'localization.number_format' => 'nullable|string|max:20',
            'localization.decimal_places' => 'nullable|integer|between:0,6',
            'localization.thousand_separator' => 'nullable|string|max:1',
            'localization.decimal_separator' => 'nullable|string|max:1',

            // Reporting Settings
            'reporting_settings' => 'nullable|array',
            'reporting_settings.default_format' => 'nullable|string|in:PDF,XML,EXCEL,JSON',
            'reporting_settings.include_zero_amounts' => 'boolean',
            'reporting_settings.group_by_category' => 'boolean',
            'reporting_settings.show_calculations' => 'boolean',

            // Notification Settings
            'notification_settings' => 'nullable|array',
            'notification_settings.email_enabled' => 'boolean',
            'notification_settings.sms_enabled' => 'boolean',
            'notification_settings.reminder_days' => 'nullable|array',
            'notification_settings.reminder_days.*' => 'integer|between:1,365',

            // Advanced Features
            'features' => 'nullable|array',
            'features.auto_reverse_charge' => 'boolean',
            'features.multi_currency_support' => 'boolean',
            'features.tax_exemption_handling' => 'boolean',
            'features.installment_payments' => 'boolean',
            'features.audit_trail' => 'boolean',

            // Custom Fields
            'custom_fields' => 'nullable|array',
            'metadata' => 'nullable|array',
            'notes' => 'nullable|string|max:2000',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'name.required' => 'اسم النظام الضريبي مطلوب',
            'name.max' => 'اسم النظام الضريبي لا يجب أن يتجاوز 255 حرف',
            'name_ar.required' => 'الاسم العربي مطلوب',
            'name_en.required' => 'الاسم الإنجليزي مطلوب',
            'country_code.required' => 'رمز الدولة مطلوب',
            'country_code.size' => 'رمز الدولة يجب أن يكون حرفين',
            'currency.required' => 'العملة مطلوبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'timezone.required' => 'المنطقة الزمنية مطلوبة',
            'effective_from.required' => 'تاريخ بداية السريان مطلوب',
            'effective_to.after' => 'تاريخ انتهاء السريان يجب أن يكون بعد تاريخ البداية',

            // VAT Configuration
            'vat_rates.*.name.required_with' => 'اسم معدل ضريبة القيمة المضافة مطلوب',
            'vat_rates.*.rate.required_with' => 'معدل ضريبة القيمة المضافة مطلوب',
            'vat_rates.*.rate.between' => 'معدل ضريبة القيمة المضافة يجب أن يكون بين 0 و 100',

            // Corporate Tax
            'corporate_tax_rate.between' => 'معدل ضريبة الشركات يجب أن يكون بين 0 و 100',
            'corporate_tax_threshold.min' => 'حد ضريبة الشركات لا يمكن أن يكون سالباً',

            // Withholding Tax
            'withholding_tax_rates.*.category.required_with' => 'فئة ضريبة الاستقطاع مطلوبة',
            'withholding_tax_rates.*.rate.required_with' => 'معدل ضريبة الاستقطاع مطلوب',
            'withholding_tax_rates.*.rate.between' => 'معدل ضريبة الاستقطاع يجب أن يكون بين 0 و 100',

            // Excise Tax
            'excise_tax_categories.*.name.required_with' => 'اسم فئة الضريبة الانتقائية مطلوب',
            'excise_tax_categories.*.rate.required_with' => 'معدل الضريبة الانتقائية مطلوب',
            'excise_tax_categories.*.unit.in' => 'وحدة الضريبة الانتقائية غير صحيحة',

            // Customs Duty
            'customs_duty_rates.*.hs_code.required_with' => 'رمز النظام المنسق مطلوب',
            'customs_duty_rates.*.rate.required_with' => 'معدل الرسوم الجمركية مطلوب',

            // Tax Periods
            'tax_periods.*.type.in' => 'نوع الفترة الضريبية غير صحيح',
            'tax_periods.*.tax_type.in' => 'نوع الضريبة غير صحيح',
            'tax_periods.*.due_days.between' => 'أيام الاستحقاق يجب أن تكون بين 1 و 365',

            // Authority Integration
            'authority_integration.api_endpoint.url' => 'رابط API غير صحيح',

            // Compliance Settings
            'compliance_settings.penalty_rate.between' => 'معدل الغرامة يجب أن يكون بين 0 و 100',

            // Localization
            'localization.decimal_places.between' => 'عدد الخانات العشرية يجب أن يكون بين 0 و 6',

            // Reporting Settings
            'reporting_settings.default_format.in' => 'تنسيق التقرير الافتراضي غير صحيح',

            // Notification Settings
            'notification_settings.reminder_days.*.between' => 'أيام التذكير يجب أن تكون بين 1 و 365',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        if ($this->has('country_code')) {
            $this->merge([
                'country_code' => strtoupper(trim($this->country_code))
            ]);
        }

        if ($this->has('currency')) {
            $this->merge([
                'currency' => strtoupper(trim($this->currency))
            ]);
        }

        // تعيين القيم الافتراضية
        $this->merge([
            'is_active' => $this->is_active ?? true,
            'is_default' => $this->is_default ?? false,
            'vat_enabled' => $this->vat_enabled ?? true,
            'corporate_tax_enabled' => $this->corporate_tax_enabled ?? false,
            'withholding_tax_enabled' => $this->withholding_tax_enabled ?? false,
            'excise_tax_enabled' => $this->excise_tax_enabled ?? false,
            'customs_duty_enabled' => $this->customs_duty_enabled ?? false,
        ]);
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من وجود نظام افتراضي واحد فقط
            if ($this->is_default) {
                $existingDefault = \App\Domains\Taxation\Models\TaxSystem::where('is_default', true)->first();
                if ($existingDefault) {
                    $validator->errors()->add('is_default', 'يوجد نظام ضريبي افتراضي بالفعل');
                }
            }

            // التحقق من صحة رموز الدول
            if ($this->country_code) {
                $validCountries = config('taxation.supported_countries', []);
                if (!empty($validCountries) && !in_array($this->country_code, $validCountries)) {
                    $validator->errors()->add('country_code', 'رمز الدولة غير مدعوم');
                }
            }

            // التحقق من صحة العملات
            if ($this->currency) {
                $validCurrencies = config('taxation.supported_currencies', []);
                if (!empty($validCurrencies) && !in_array($this->currency, $validCurrencies)) {
                    $validator->errors()->add('currency', 'العملة غير مدعومة');
                }
            }

            // التحقق من معدلات ضريبة القيمة المضافة
            if ($this->vat_enabled && $this->vat_rates) {
                $defaultCount = 0;
                foreach ($this->vat_rates as $index => $rate) {
                    if ($rate['is_default'] ?? false) {
                        $defaultCount++;
                    }
                }
                
                if ($defaultCount > 1) {
                    $validator->errors()->add('vat_rates', 'يمكن تعيين معدل واحد فقط كافتراضي');
                }
                
                if ($defaultCount === 0 && count($this->vat_rates) > 0) {
                    $validator->errors()->add('vat_rates', 'يجب تعيين معدل افتراضي واحد على الأقل');
                }
            }

            // التحقق من إعدادات التكامل
            if ($this->authority_integration['enabled'] ?? false) {
                if (empty($this->authority_integration['api_endpoint'])) {
                    $validator->errors()->add('authority_integration.api_endpoint', 'رابط API مطلوب عند تفعيل التكامل');
                }
            }

            // التحقق من الفترات الضريبية
            if ($this->tax_periods) {
                $combinations = [];
                foreach ($this->tax_periods as $index => $period) {
                    $combination = $period['type'] . '_' . $period['tax_type'];
                    if (in_array($combination, $combinations)) {
                        $validator->errors()->add("tax_periods.{$index}", 'لا يمكن تكرار نفس نوع الفترة لنفس نوع الضريبة');
                    }
                    $combinations[] = $combination;
                }
            }

            // التحقق من إعدادات التوطين
            if ($this->localization) {
                if (isset($this->localization['thousand_separator']) && 
                    isset($this->localization['decimal_separator']) &&
                    $this->localization['thousand_separator'] === $this->localization['decimal_separator']) {
                    $validator->errors()->add('localization.decimal_separator', 'فاصل الآلاف وفاصل العشرية يجب أن يكونا مختلفين');
                }
            }
        });
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * الحصول على إعدادات ضريبة القيمة المضافة
     */
    public function getVATSettings(): array
    {
        if (!$this->vat_enabled || !$this->vat_rates) {
            return [];
        }

        return [
            'enabled' => true,
            'rates' => $this->vat_rates,
            'default_rate' => collect($this->vat_rates)->firstWhere('is_default', true),
        ];
    }

    /**
     * الحصول على إعدادات ضريبة الشركات
     */
    public function getCorporateTaxSettings(): array
    {
        if (!$this->corporate_tax_enabled) {
            return ['enabled' => false];
        }

        return [
            'enabled' => true,
            'rate' => $this->corporate_tax_rate,
            'threshold' => $this->corporate_tax_threshold,
        ];
    }

    /**
     * الحصول على إعدادات التكامل
     */
    public function getIntegrationSettings(): array
    {
        return $this->authority_integration ?? [
            'enabled' => false,
            'test_mode' => true,
        ];
    }
}
