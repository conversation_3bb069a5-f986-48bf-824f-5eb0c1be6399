<?php

namespace App\Domains\Integration\Contracts;

/**
 * Data Processor Interface
 * Defines the contract for all data processors
 */
interface ProcessorInterface
{
    /**
     * Process data according to the processor's format
     *
     * @param mixed $data The data to process
     * @param array $options Processing options
     * @return array Processed data as array
     */
    public function process(mixed $data, array $options = []): array;

    /**
     * Validate data according to the processor's format
     *
     * @param mixed $data The data to validate
     * @param array $schema Validation schema/rules
     * @return bool True if valid, false otherwise
     */
    public function validate(mixed $data, array $schema = []): bool;

    /**
     * Transform data using mapping rules
     *
     * @param array $data The data to transform
     * @param array $mapping Transformation mapping rules
     * @return array Transformed data
     */
    public function transform(array $data, array $mapping): array;

    /**
     * Get supported data formats
     *
     * @return array List of supported formats/MIME types
     */
    public function getSupportedFormats(): array;

    /**
     * Check if a format is supported
     *
     * @param string $format The format to check
     * @return bool True if supported, false otherwise
     */
    public function supports(string $format): bool;
}
