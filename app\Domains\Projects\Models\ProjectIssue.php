<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج مشكلة المشروع - Project Issue Management
 * يدير تتبع وحل المشكلات في المشاريع
 */
class ProjectIssue extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'issue_number',
        'title',
        'description',
        'type',
        'priority',
        'severity',
        'status',
        'reported_by',
        'reported_at',
        'assigned_to',
        'resolved_by',
        'resolved_at',
        'due_date',
        'affected_components',
        'steps_to_reproduce',
        'expected_behavior',
        'actual_behavior',
        'environment',
        'attachments',
        'resolution_notes',
        'time_to_resolve',
        'labels',
        'metadata',
    ];

    protected $casts = [
        'reported_at' => 'datetime',
        'resolved_at' => 'datetime',
        'due_date' => 'datetime',
        'affected_components' => 'array',
        'steps_to_reproduce' => 'array',
        'environment' => 'array',
        'attachments' => 'array',
        'labels' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المبلغ
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'reported_by');
    }

    /**
     * العلاقة مع المكلف
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع الحلال
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'resolved_by');
    }

    /**
     * العلاقة مع التعليقات
     */
    public function comments(): HasMany
    {
        return $this->hasMany(IssueComment::class, 'issue_id');
    }

    /**
     * العلاقة مع المخاطر المرتبطة
     */
    public function relatedRisks(): BelongsToMany
    {
        return $this->belongsToMany(
            ProjectRisk::class,
            'issue_risk_relations',
            'issue_id',
            'risk_id'
        )->withTimestamps();
    }

    /**
     * العلاقة مع المهام المرتبطة
     */
    public function relatedTasks(): BelongsToMany
    {
        return $this->belongsToMany(
            Task::class,
            'issue_task_relations',
            'issue_id',
            'task_id'
        )->withTimestamps();
    }

    /**
     * توليد رقم المشكلة
     */
    public static function generateIssueNumber(int $projectId): string
    {
        $project = Project::findOrFail($projectId);
        $lastIssue = self::where('project_id', $projectId)
                         ->orderBy('issue_number', 'desc')
                         ->first();

        if (!$lastIssue) {
            return $project->code . '-001';
        }

        $lastNumber = (int) substr($lastIssue->issue_number, -3);
        $newNumber = $lastNumber + 1;

        return $project->code . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'CRITICAL' => '#dc3545',
            'HIGH' => '#fd7e14',
            'MEDIUM' => '#ffc107',
            'LOW' => '#28a745',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'OPEN' => '#dc3545',
            'IN_PROGRESS' => '#fd7e14',
            'RESOLVED' => '#28a745',
            'CLOSED' => '#6c757d',
            'REOPENED' => '#e83e8c',
            default => '#6c757d',
        };
    }

    /**
     * التحقق من التأخير
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['RESOLVED', 'CLOSED']);
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        return $this->due_date ? now()->diffInDays($this->due_date, false) : 0;
    }

    /**
     * الحصول على وقت الحل
     */
    public function getResolutionTimeAttribute(): ?int
    {
        if ($this->resolved_at && $this->reported_at) {
            return $this->reported_at->diffInHours($this->resolved_at);
        }
        
        return null;
    }

    /**
     * تعيين المشكلة
     */
    public function assignTo(int $employeeId, int $assignedBy): void
    {
        $this->update([
            'assigned_to' => $employeeId,
            'status' => $this->status === 'OPEN' ? 'IN_PROGRESS' : $this->status,
        ]);

        // إضافة تعليق
        $this->addComment($assignedBy, "تم تعيين المشكلة إلى " . $this->assignee->name, 'ASSIGNMENT');

        // إشعار المكلف
        $this->assignee->notify(new \App\Notifications\IssueAssignedNotification($this));
    }

    /**
     * بدء العمل على المشكلة
     */
    public function startWork(int $userId): void
    {
        $this->update(['status' => 'IN_PROGRESS']);
        
        $this->addComment($userId, "تم بدء العمل على المشكلة", 'STATUS_CHANGE');
    }

    /**
     * حل المشكلة
     */
    public function resolve(int $resolvedBy, string $resolutionNotes = null): void
    {
        $this->update([
            'status' => 'RESOLVED',
            'resolved_by' => $resolvedBy,
            'resolved_at' => now(),
            'resolution_notes' => $resolutionNotes,
            'time_to_resolve' => $this->resolution_time,
        ]);

        $this->addComment($resolvedBy, $resolutionNotes ?? "تم حل المشكلة", 'RESOLUTION');

        // إشعار المبلغ
        $this->reporter->notify(new \App\Notifications\IssueResolvedNotification($this));
    }

    /**
     * إغلاق المشكلة
     */
    public function close(int $closedBy, string $reason = null): void
    {
        $this->update(['status' => 'CLOSED']);
        
        $this->addComment($closedBy, $reason ?? "تم إغلاق المشكلة", 'CLOSURE');
    }

    /**
     * إعادة فتح المشكلة
     */
    public function reopen(int $reopenedBy, string $reason = null): void
    {
        $this->update([
            'status' => 'REOPENED',
            'resolved_by' => null,
            'resolved_at' => null,
            'resolution_notes' => null,
        ]);

        $this->addComment($reopenedBy, $reason ?? "تم إعادة فتح المشكلة", 'REOPENING');
    }

    /**
     * إضافة تعليق
     */
    public function addComment(int $userId, string $comment, string $type = 'COMMENT'): IssueComment
    {
        return $this->comments()->create([
            'user_id' => $userId,
            'comment' => $comment,
            'comment_type' => $type,
        ]);
    }

    /**
     * ربط بمخاطر
     */
    public function linkToRisks(array $riskIds): void
    {
        $this->relatedRisks()->syncWithoutDetaching($riskIds);
    }

    /**
     * ربط بمهام
     */
    public function linkToTasks(array $taskIds): void
    {
        $this->relatedTasks()->syncWithoutDetaching($taskIds);
    }

    /**
     * إنشاء مهمة من المشكلة
     */
    public function createTask(array $taskData, int $createdBy): Task
    {
        $task = $this->project->tasks()->create(array_merge($taskData, [
            'title' => $taskData['title'] ?? "حل المشكلة: {$this->title}",
            'description' => $taskData['description'] ?? $this->description,
            'priority' => $this->priority,
            'assignee_id' => $this->assigned_to,
            'type' => 'BUG_FIX',
            'metadata' => [
                'issue_id' => $this->id,
                'created_from_issue' => true,
            ],
        ]));

        $this->linkToTasks([$task->id]);

        return $task;
    }

    /**
     * تحديث الأولوية
     */
    public function updatePriority(string $newPriority, int $updatedBy, string $reason = null): void
    {
        $oldPriority = $this->priority;
        $this->update(['priority' => $newPriority]);

        $this->addComment(
            $updatedBy,
            "تم تغيير الأولوية من {$oldPriority} إلى {$newPriority}" . ($reason ? ": {$reason}" : ""),
            'PRIORITY_CHANGE'
        );
    }

    /**
     * البحث في المشكلات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('issue_number', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب المكلف
     */
    public function scopeAssignedTo($query, int $employeeId)
    {
        return $query->where('assigned_to', $employeeId);
    }

    /**
     * فلترة حسب المبلغ
     */
    public function scopeReportedBy($query, int $employeeId)
    {
        return $query->where('reported_by', $employeeId);
    }

    /**
     * فلترة المشكلات المفتوحة
     */
    public function scopeOpen($query)
    {
        return $query->whereIn('status', ['OPEN', 'IN_PROGRESS', 'REOPENED']);
    }

    /**
     * فلترة المشكلات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['RESOLVED', 'CLOSED']);
    }

    /**
     * فلترة المشكلات عالية الأولوية
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['HIGH', 'CRITICAL']);
    }

    /**
     * ترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW')");
    }

    /**
     * ترتيب حسب تاريخ الإبلاغ
     */
    public function scopeOrderByReported($query, string $direction = 'desc')
    {
        return $query->orderBy('reported_at', $direction);
    }
}
