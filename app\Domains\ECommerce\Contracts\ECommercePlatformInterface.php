<?php

namespace App\Domains\ECommerce\Contracts;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Http\Request;

/**
 * واجهة منصات التجارة الإلكترونية
 * تحدد الطرق المطلوبة لكل منصة
 */
interface ECommercePlatformInterface
{
    /**
     * اختبار الاتصال مع المنصة
     */
    public function testConnection(ECommerceIntegration $integration): array;

    /**
     * التحقق من صحة إعدادات التكامل
     */
    public function validateConfiguration(array $config): array;

    /**
     * الحصول على معلومات المتجر
     */
    public function getStoreInfo(ECommerceIntegration $integration): array;

    /**
     * جلب المنتجات من المنصة
     */
    public function getProducts(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب منتج واحد من المنصة
     */
    public function getProduct(ECommerceIntegration $integration, string $productId): array;

    /**
     * إنشاء منتج في المنصة
     */
    public function createProduct(ECommerceIntegration $integration, array $productData): array;

    /**
     * تحديث منتج في المنصة
     */
    public function updateProduct(ECommerceIntegration $integration, string $productId, array $productData): array;

    /**
     * حذف منتج من المنصة
     */
    public function deleteProduct(ECommerceIntegration $integration, string $productId): array;

    /**
     * جلب الطلبات من المنصة
     */
    public function getOrders(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب طلب واحد من المنصة
     */
    public function getOrder(ECommerceIntegration $integration, string $orderId): array;

    /**
     * إنشاء طلب في المنصة
     */
    public function createOrder(ECommerceIntegration $integration, array $orderData): array;

    /**
     * تحديث طلب في المنصة
     */
    public function updateOrder(ECommerceIntegration $integration, string $orderId, array $orderData): array;

    /**
     * إلغاء طلب في المنصة
     */
    public function cancelOrder(ECommerceIntegration $integration, string $orderId): array;

    /**
     * جلب العملاء من المنصة
     */
    public function getCustomers(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب عميل واحد من المنصة
     */
    public function getCustomer(ECommerceIntegration $integration, string $customerId): array;

    /**
     * إنشاء عميل في المنصة
     */
    public function createCustomer(ECommerceIntegration $integration, array $customerData): array;

    /**
     * تحديث عميل في المنصة
     */
    public function updateCustomer(ECommerceIntegration $integration, string $customerId, array $customerData): array;

    /**
     * حذف عميل من المنصة
     */
    public function deleteCustomer(ECommerceIntegration $integration, string $customerId): array;

    /**
     * جلب الفئات من المنصة
     */
    public function getCategories(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب فئة واحدة من المنصة
     */
    public function getCategory(ECommerceIntegration $integration, string $categoryId): array;

    /**
     * إنشاء فئة في المنصة
     */
    public function createCategory(ECommerceIntegration $integration, array $categoryData): array;

    /**
     * تحديث فئة في المنصة
     */
    public function updateCategory(ECommerceIntegration $integration, string $categoryId, array $categoryData): array;

    /**
     * حذف فئة من المنصة
     */
    public function deleteCategory(ECommerceIntegration $integration, string $categoryId): array;

    /**
     * جلب المخزون من المنصة
     */
    public function getInventory(ECommerceIntegration $integration, array $options = []): array;

    /**
     * تحديث المخزون في المنصة
     */
    public function updateInventory(ECommerceIntegration $integration, string $productId, int $quantity): array;

    /**
     * جلب الكوبونات من المنصة
     */
    public function getCoupons(ECommerceIntegration $integration, array $options = []): array;

    /**
     * إنشاء كوبون في المنصة
     */
    public function createCoupon(ECommerceIntegration $integration, array $couponData): array;

    /**
     * تحديث كوبون في المنصة
     */
    public function updateCoupon(ECommerceIntegration $integration, string $couponId, array $couponData): array;

    /**
     * حذف كوبون من المنصة
     */
    public function deleteCoupon(ECommerceIntegration $integration, string $couponId): array;

    /**
     * جلب التقارير من المنصة
     */
    public function getReports(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب تقرير مبيعات
     */
    public function getSalesReport(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب تقرير المنتجات
     */
    public function getProductsReport(ECommerceIntegration $integration, array $options = []): array;

    /**
     * جلب تقرير العملاء
     */
    public function getCustomersReport(ECommerceIntegration $integration, array $options = []): array;

    /**
     * مزامنة شاملة
     */
    public function sync(ECommerceIntegration $integration, string $syncType = 'full', array $options = []): array;

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(ECommerceIntegration $integration, array $options = []): array;

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(ECommerceIntegration $integration, array $options = []): array;

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(ECommerceIntegration $integration, array $options = []): array;

    /**
     * مزامنة الفئات
     */
    public function syncCategories(ECommerceIntegration $integration, array $options = []): array;

    /**
     * مزامنة المخزون
     */
    public function syncInventory(ECommerceIntegration $integration, array $options = []): array;

    /**
     * معالجة webhook
     */
    public function processWebhook(ECommerceIntegration $integration, array $payload, array $headers = []): array;

    /**
     * التحقق من توقيع webhook
     */
    public function verifyWebhookSignature(Request $request, ECommerceIntegration $integration): bool;

    /**
     * إنشاء webhook في المنصة
     */
    public function createWebhook(ECommerceIntegration $integration, array $webhookData): array;

    /**
     * تحديث webhook في المنصة
     */
    public function updateWebhook(ECommerceIntegration $integration, string $webhookId, array $webhookData): array;

    /**
     * حذف webhook من المنصة
     */
    public function deleteWebhook(ECommerceIntegration $integration, string $webhookId): array;

    /**
     * جلب قائمة webhooks من المنصة
     */
    public function getWebhooks(ECommerceIntegration $integration): array;

    /**
     * الحصول على حدود API
     */
    public function getApiLimits(ECommerceIntegration $integration): array;

    /**
     * الحصول على استخدام API
     */
    public function getApiUsage(ECommerceIntegration $integration): array;

    /**
     * تحديث رمز الوصول
     */
    public function refreshAccessToken(ECommerceIntegration $integration): array;

    /**
     * إلغاء تفويض التطبيق
     */
    public function revokeAccess(ECommerceIntegration $integration): array;

    /**
     * الحصول على معلومات التطبيق
     */
    public function getAppInfo(ECommerceIntegration $integration): array;

    /**
     * تحديث إعدادات التطبيق
     */
    public function updateAppSettings(ECommerceIntegration $integration, array $settings): array;

    /**
     * الحصول على إحصائيات المتجر
     */
    public function getStoreStats(ECommerceIntegration $integration): array;

    /**
     * الحصول على معلومات الخطة
     */
    public function getPlanInfo(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة الدول المدعومة
     */
    public function getSupportedCountries(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة العملات المدعومة
     */
    public function getSupportedCurrencies(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة اللغات المدعومة
     */
    public function getSupportedLanguages(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة طرق الدفع المدعومة
     */
    public function getSupportedPaymentMethods(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة طرق الشحن المدعومة
     */
    public function getSupportedShippingMethods(ECommerceIntegration $integration): array;

    /**
     * الحصول على قائمة الضرائب المدعومة
     */
    public function getSupportedTaxes(ECommerceIntegration $integration): array;

    /**
     * تحويل البيانات إلى تنسيق المنصة
     */
    public function transformToExternalFormat(array $data, string $entityType): array;

    /**
     * تحويل البيانات من تنسيق المنصة
     */
    public function transformFromExternalFormat(array $data, string $entityType): array;

    /**
     * التحقق من صحة البيانات قبل الإرسال
     */
    public function validateDataForExport(array $data, string $entityType): array;

    /**
     * التحقق من صحة البيانات بعد الاستيراد
     */
    public function validateDataForImport(array $data, string $entityType): array;

    /**
     * معالجة الأخطاء من المنصة
     */
    public function handleApiError(\Exception $exception, string $operation): array;

    /**
     * إعادة المحاولة للعمليات الفاشلة
     */
    public function retryOperation(callable $operation, int $maxRetries = 3): mixed;

    /**
     * تسجيل العمليات
     */
    public function logOperation(string $operation, array $data, array $result): void;

    /**
     * الحصول على إعدادات المنصة الافتراضية
     */
    public function getDefaultConfiguration(): array;

    /**
     * الحصول على قائمة الحقول المطلوبة للتكامل
     */
    public function getRequiredFields(): array;

    /**
     * الحصول على قائمة الحقول الاختيارية للتكامل
     */
    public function getOptionalFields(): array;

    /**
     * الحصول على قائمة العمليات المدعومة
     */
    public function getSupportedOperations(): array;

    /**
     * الحصول على قائمة أنواع المزامنة المدعومة
     */
    public function getSupportedSyncTypes(): array;

    /**
     * الحصول على قائمة أنواع الأحداث المدعومة
     */
    public function getSupportedEventTypes(): array;

    /**
     * الحصول على قائمة تنسيقات البيانات المدعومة
     */
    public function getSupportedDataFormats(): array;

    /**
     * الحصول على معلومات إصدار API
     */
    public function getApiVersion(): string;

    /**
     * الحصول على URL الأساسي للAPI
     */
    public function getApiBaseUrl(ECommerceIntegration $integration): string;

    /**
     * الحصول على headers المطلوبة للطلبات
     */
    public function getRequiredHeaders(ECommerceIntegration $integration): array;

    /**
     * الحصول على معاملات الاستعلام المطلوبة
     */
    public function getRequiredQueryParams(ECommerceIntegration $integration): array;

    /**
     * تحضير طلب API
     */
    public function prepareApiRequest(string $method, string $endpoint, array $data = []): array;

    /**
     * معالجة استجابة API
     */
    public function processApiResponse(array $response, string $operation): array;

    /**
     * التحقق من حالة الاستجابة
     */
    public function isSuccessResponse(array $response): bool;

    /**
     * استخراج البيانات من الاستجابة
     */
    public function extractDataFromResponse(array $response): array;

    /**
     * استخراج الأخطاء من الاستجابة
     */
    public function extractErrorsFromResponse(array $response): array;

    /**
     * استخراج التحذيرات من الاستجابة
     */
    public function extractWarningsFromResponse(array $response): array;

    /**
     * استخراج معلومات التصفح من الاستجابة
     */
    public function extractPaginationFromResponse(array $response): array;

    /**
     * بناء URL للصفحة التالية
     */
    public function buildNextPageUrl(array $pagination): ?string;

    /**
     * بناء URL للصفحة السابقة
     */
    public function buildPreviousPageUrl(array $pagination): ?string;

    /**
     * الحصول على الحد الأقصى لعدد العناصر في الصفحة
     */
    public function getMaxPageSize(): int;

    /**
     * الحصول على الحد الافتراضي لعدد العناصر في الصفحة
     */
    public function getDefaultPageSize(): int;

    /**
     * الحصول على الحد الأقصى لعدد الطلبات في الثانية
     */
    public function getMaxRequestsPerSecond(): int;

    /**
     * الحصول على الحد الأقصى لعدد الطلبات في الدقيقة
     */
    public function getMaxRequestsPerMinute(): int;

    /**
     * الحصول على الحد الأقصى لعدد الطلبات في الساعة
     */
    public function getMaxRequestsPerHour(): int;

    /**
     * الحصول على الحد الأقصى لعدد الطلبات في اليوم
     */
    public function getMaxRequestsPerDay(): int;

    /**
     * التحقق من توفر العملية
     */
    public function isOperationAvailable(string $operation): bool;

    /**
     * التحقق من توفر نوع المزامنة
     */
    public function isSyncTypeAvailable(string $syncType): bool;

    /**
     * التحقق من توفر نوع الحدث
     */
    public function isEventTypeAvailable(string $eventType): bool;

    /**
     * التحقق من توفر تنسيق البيانات
     */
    public function isDataFormatAvailable(string $format): bool;
}
