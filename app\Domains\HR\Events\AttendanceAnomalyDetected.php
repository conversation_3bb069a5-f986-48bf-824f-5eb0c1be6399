<?php

namespace App\Domains\HR\Events;

use App\Domains\HR\Models\AttendanceRecord;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث اكتشاف شذوذ في الحضور
 */
class AttendanceAnomalyDetected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public AttendanceRecord $attendanceRecord;
    public array $anomalies;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(AttendanceRecord $attendanceRecord, array $anomalies)
    {
        $this->attendanceRecord = $attendanceRecord;
        $this->anomalies = $anomalies;
    }
}
