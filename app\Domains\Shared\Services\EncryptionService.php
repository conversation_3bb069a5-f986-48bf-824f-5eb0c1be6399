<?php

namespace App\Domains\Shared\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Contracts\Encryption\DecryptException;

/**
 * خدمة التشفير المتقدمة
 */
class EncryptionService
{
    /**
     * تشفير البيانات الحساسة
     */
    public function encrypt($data): string
    {
        return Crypt::encrypt($data);
    }

    /**
     * فك تشفير البيانات
     */
    public function decrypt(string $encryptedData)
    {
        try {
            return Crypt::decrypt($encryptedData);
        } catch (DecryptException $e) {
            throw new \Exception('فشل في فك التشفير: ' . $e->getMessage());
        }
    }

    /**
     * تشفير البيانات المالية
     */
    public function encryptFinancialData(array $data): string
    {
        // إضافة طبقة حماية إضافية للبيانات المالية
        $serialized = serialize($data);
        $hash = hash('sha256', $serialized);
        
        return $this->encrypt([
            'data' => $serialized,
            'hash' => $hash,
            'timestamp' => time(),
        ]);
    }

    /**
     * فك تشفير البيانات المالية
     */
    public function decryptFinancialData(string $encryptedData): array
    {
        $decrypted = $this->decrypt($encryptedData);
        
        if (!is_array($decrypted) || !isset($decrypted['data'], $decrypted['hash'])) {
            throw new \Exception('بيانات مالية غير صالحة');
        }

        // التحقق من سلامة البيانات
        $expectedHash = hash('sha256', $decrypted['data']);
        if ($expectedHash !== $decrypted['hash']) {
            throw new \Exception('تم العبث بالبيانات المالية');
        }

        return unserialize($decrypted['data']);
    }

    /**
     * تشفير كلمات المرور
     */
    public function hashPassword(string $password): string
    {
        return bcrypt($password);
    }

    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * توليد رمز عشوائي آمن
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * توليد مفتاح API آمن
     */
    public function generateApiKey(): string
    {
        return 'hes_' . $this->generateSecureToken(40);
    }

    /**
     * تشفير رقم الهاتف
     */
    public function encryptPhone(string $phone): string
    {
        return $this->encrypt($phone);
    }

    /**
     * فك تشفير رقم الهاتف
     */
    public function decryptPhone(string $encryptedPhone): string
    {
        return $this->decrypt($encryptedPhone);
    }

    /**
     * تشفير البيانات الشخصية
     */
    public function encryptPersonalData(array $data): string
    {
        return $this->encrypt($data);
    }

    /**
     * فك تشفير البيانات الشخصية
     */
    public function decryptPersonalData(string $encryptedData): array
    {
        return $this->decrypt($encryptedData);
    }
}
