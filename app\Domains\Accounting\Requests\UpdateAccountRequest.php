<?php

namespace App\Domains\Accounting\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Update Account Request
 * طلب تحديث حساب
 */
class UpdateAccountRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('account'));
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        $account = $this->route('account');

        return [
            'account_code' => [
                'required',
                'string',
                'max:20',
                Rule::unique('accounts', 'account_code')->ignore($account->id),
                'regex:/^[A-Z0-9\-\.]+$/',
            ],
            'account_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('accounts', 'account_name')->ignore($account->id),
            ],
            'account_name_en' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('accounts', 'account_name_en')->ignore($account->id),
            ],
            'account_type' => [
                'required',
                'string',
                Rule::in(['asset', 'liability', 'equity', 'revenue', 'expense']),
            ],
            'account_category' => [
                'required',
                'string',
                Rule::in([
                    'current_assets',
                    'fixed_assets',
                    'intangible_assets',
                    'current_liabilities',
                    'long_term_liabilities',
                    'capital',
                    'retained_earnings',
                    'operating_revenue',
                    'other_revenue',
                    'cost_of_goods_sold',
                    'operating_expenses',
                    'administrative_expenses',
                    'financial_expenses',
                    'other_expenses',
                ]),
            ],
            'parent_account_id' => [
                'nullable',
                'integer',
                'exists:accounts,id',
                Rule::notIn([$account->id]), // منع الحساب من أن يكون أب لنفسه
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'currency_code' => [
                'nullable',
                'string',
                'size:3',
                Rule::in(['SAR', 'USD', 'EUR', 'GBP', 'AED', 'EGP']),
            ],
            'is_active' => [
                'boolean',
            ],
            'allow_manual_entry' => [
                'boolean',
            ],
            'require_cost_center' => [
                'boolean',
            ],
            'require_project' => [
                'boolean',
            ],
            'tax_account_id' => [
                'nullable',
                'integer',
                'exists:accounts,id',
            ],
            'bank_account_number' => [
                'nullable',
                'string',
                'max:50',
            ],
            'iban' => [
                'nullable',
                'string',
                'max:34',
                'regex:/^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/',
            ],
            'swift_code' => [
                'nullable',
                'string',
                'size:8',
                'regex:/^[A-Z]{6}[A-Z0-9]{2}$/',
            ],
            'bank_name' => [
                'nullable',
                'string',
                'max:100',
            ],
            'reconciliation_enabled' => [
                'boolean',
            ],
            'auto_reconciliation' => [
                'boolean',
            ],
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'account_code.required' => 'رمز الحساب مطلوب',
            'account_code.unique' => 'رمز الحساب موجود مسبقاً',
            'account_code.regex' => 'رمز الحساب يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط',
            'account_code.max' => 'رمز الحساب يجب ألا يزيد عن 20 حرف',
            
            'account_name.required' => 'اسم الحساب مطلوب',
            'account_name.unique' => 'اسم الحساب موجود مسبقاً',
            'account_name.max' => 'اسم الحساب يجب ألا يزيد عن 255 حرف',
            
            'account_name_en.unique' => 'الاسم الإنجليزي للحساب موجود مسبقاً',
            'account_name_en.max' => 'الاسم الإنجليزي يجب ألا يزيد عن 255 حرف',
            
            'account_type.required' => 'نوع الحساب مطلوب',
            'account_type.in' => 'نوع الحساب غير صحيح',
            
            'account_category.required' => 'فئة الحساب مطلوبة',
            'account_category.in' => 'فئة الحساب غير صحيحة',
            
            'parent_account_id.exists' => 'الحساب الأب غير موجود',
            'parent_account_id.not_in' => 'لا يمكن للحساب أن يكون أب لنفسه',
            
            'description.max' => 'الوصف يجب ألا يزيد عن 500 حرف',
            
            'currency_code.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'currency_code.in' => 'رمز العملة غير مدعوم',
            
            'bank_account_number.max' => 'رقم الحساب البنكي يجب ألا يزيد عن 50 حرف',
            
            'iban.regex' => 'رقم الآيبان غير صحيح',
            'iban.max' => 'رقم الآيبان يجب ألا يزيد عن 34 حرف',
            
            'swift_code.size' => 'رمز السويفت يجب أن يكون 8 أحرف',
            'swift_code.regex' => 'رمز السويفت غير صحيح',
            
            'bank_name.max' => 'اسم البنك يجب ألا يزيد عن 100 حرف',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'account_code' => 'رمز الحساب',
            'account_name' => 'اسم الحساب',
            'account_name_en' => 'الاسم الإنجليزي',
            'account_type' => 'نوع الحساب',
            'account_category' => 'فئة الحساب',
            'parent_account_id' => 'الحساب الأب',
            'description' => 'الوصف',
            'currency_code' => 'رمز العملة',
            'is_active' => 'نشط',
            'allow_manual_entry' => 'السماح بالإدخال اليدوي',
            'require_cost_center' => 'يتطلب مركز تكلفة',
            'require_project' => 'يتطلب مشروع',
            'tax_account_id' => 'حساب الضريبة',
            'bank_account_number' => 'رقم الحساب البنكي',
            'iban' => 'رقم الآيبان',
            'swift_code' => 'رمز السويفت',
            'bank_name' => 'اسم البنك',
            'reconciliation_enabled' => 'التسوية مفعلة',
            'auto_reconciliation' => 'التسوية التلقائية',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'account_code' => strtoupper($this->account_code),
            'is_active' => $this->boolean('is_active'),
            'allow_manual_entry' => $this->boolean('allow_manual_entry'),
            'require_cost_center' => $this->boolean('require_cost_center'),
            'require_project' => $this->boolean('require_project'),
            'reconciliation_enabled' => $this->boolean('reconciliation_enabled'),
            'auto_reconciliation' => $this->boolean('auto_reconciliation'),
        ]);
    }

    /**
     * التحقق من صحة البيانات بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $account = $this->route('account');

            // منع تغيير نوع الحساب إذا كان له معاملات
            if ($this->account_type !== $account->account_type) {
                if ($account->journalEntryDetails()->exists()) {
                    $validator->errors()->add('account_type', 'لا يمكن تغيير نوع الحساب لوجود معاملات مرتبطة به');
                }
            }

            // التحقق من أن الحساب الأب من نفس النوع أو متوافق
            if ($this->parent_account_id) {
                $parentAccount = \App\Domains\Accounting\Models\Account::find($this->parent_account_id);
                if ($parentAccount && $parentAccount->account_type !== $this->account_type) {
                    $validator->errors()->add('parent_account_id', 'الحساب الأب يجب أن يكون من نفس نوع الحساب');
                }

                // منع الحلقة المفرغة في التسلسل الهرمي
                if ($this->wouldCreateCircularReference($account, $this->parent_account_id)) {
                    $validator->errors()->add('parent_account_id', 'هذا الاختيار سيؤدي إلى حلقة مفرغة في التسلسل الهرمي');
                }
            }

            // التحقق من أن رمز الحساب يتبع التسلسل الهرمي
            if ($this->parent_account_id && $this->account_code) {
                $parentAccount = \App\Domains\Accounting\Models\Account::find($this->parent_account_id);
                if ($parentAccount && !str_starts_with($this->account_code, $parentAccount->account_code)) {
                    $validator->errors()->add('account_code', 'رمز الحساب يجب أن يبدأ برمز الحساب الأب');
                }
            }

            // منع إلغاء تفعيل الحساب إذا كان له حسابات فرعية نشطة
            if (!$this->boolean('is_active') && $account->is_active) {
                $activeSubAccounts = $account->subAccounts()->where('is_active', true)->count();
                if ($activeSubAccounts > 0) {
                    $validator->errors()->add('is_active', 'لا يمكن إلغاء تفعيل الحساب لوجود حسابات فرعية نشطة');
                }
            }

            // التحقق من أن الحساب البنكي يحتوي على البيانات المطلوبة
            if ($this->account_category === 'current_assets' && 
                str_contains(strtolower($this->account_name), 'بنك')) {
                if (!$this->bank_account_number && !$this->iban) {
                    $validator->errors()->add('bank_account_number', 'الحسابات البنكية تتطلب رقم حساب أو آيبان');
                }
            }

            // التحقق من صحة رقم الآيبان السعودي
            if ($this->iban && str_starts_with($this->iban, 'SA')) {
                if (strlen($this->iban) !== 24) {
                    $validator->errors()->add('iban', 'رقم الآيبان السعودي يجب أن يكون 24 رقم');
                }
            }
        });
    }

    /**
     * التحقق من وجود مرجع دائري في التسلسل الهرمي
     */
    protected function wouldCreateCircularReference($account, $newParentId): bool
    {
        if (!$newParentId) {
            return false;
        }

        $currentAccount = \App\Domains\Accounting\Models\Account::find($newParentId);
        
        while ($currentAccount) {
            if ($currentAccount->id === $account->id) {
                return true;
            }
            $currentAccount = $currentAccount->parentAccount;
        }

        return false;
    }
}
