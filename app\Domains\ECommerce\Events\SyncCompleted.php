<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث اكتمال المزامنة
 * يتم إطلاقه عند اكتمال عملية المزامنة بنجاح
 */
class SyncCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceIntegration $integration;
    public ECommerceSyncLog $syncLog;
    public array $syncResult;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(ECommerceIntegration $integration, ECommerceSyncLog $syncLog, array $syncResult = [])
    {
        $this->integration = $integration;
        $this->syncLog = $syncLog;
        $this->syncResult = $syncResult;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'integration_id' => $this->integration->id,
            'sync_log_id' => $this->syncLog->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'sync_type' => $this->syncLog->sync_type,
            'entity_type' => $this->syncLog->entity_type,
            'sync_direction' => $this->syncLog->sync_direction,
            'sync_mode' => $this->syncLog->sync_mode,
            'started_at' => $this->syncLog->started_at,
            'completed_at' => $this->syncLog->completed_at,
            'duration' => $this->syncLog->duration,
            'records_total' => $this->syncLog->records_total,
            'records_processed' => $this->syncLog->records_processed,
            'records_successful' => $this->syncLog->records_successful,
            'records_failed' => $this->syncLog->records_failed,
            'records_skipped' => $this->syncLog->records_skipped,
            'success_rate' => $this->syncLog->success_rate,
            'failure_rate' => $this->syncLog->failure_rate,
            'throughput' => $this->syncLog->throughput,
            'sync_result' => $this->syncResult,
        ];
    }

    /**
     * تحديد ما إذا كانت المزامنة ناجحة
     */
    public function isSuccessful(): bool
    {
        return $this->syncLog->is_successful;
    }

    /**
     * تحديد ما إذا كانت المزامنة جزئية
     */
    public function isPartial(): bool
    {
        return $this->syncLog->is_partial;
    }

    /**
     * الحصول على معدل النجاح
     */
    public function getSuccessRate(): float
    {
        return $this->syncLog->success_rate ?? 0;
    }
}
