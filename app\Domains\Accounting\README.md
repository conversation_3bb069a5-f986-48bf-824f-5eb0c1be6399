# 🧮 نظام المحاسبة المتقدم - Advanced Accounting System

نظام محاسبة شامل ومتقدم مبني بـ Laravel يدعم المعايير المحاسبية الدولية والمحلية مع ميزات الذكاء الاصطناعي.

## 🌟 الميزات الرئيسية

### 📊 المحاسبة الأساسية
- **دليل الحسابات المرن**: دعم التسلسل الهرمي متعدد المستويات
- **القيود اليومية**: إنشاء وإدارة القيود مع التحقق التلقائي من التوازن
- **الفواتير الذكية**: نظام فوترة متقدم مع دعم الفواتير المتكررة
- **إدارة المدفوعات**: تتبع ومعالجة المدفوعات مع التسوية التلقائية
- **التقارير المالية**: مجموعة شاملة من التقارير المالية

### 🤖 الذكاء الاصطناعي
- **تصنيف المعاملات التلقائي**: استخدام AI لتصنيف المعاملات
- **كشف الشذوذ**: اكتشاف المعاملات غير العادية تلقائياً
- **التحليل التنبؤي**: توقعات مالية ذكية
- **مسح الفواتير بـ OCR**: استخراج البيانات من الفواتير المصورة
- **المساعد المحاسبي الذكي**: مساعد AI للاستفسارات المحاسبية

### 🌍 الامتثال والمعايير
- **المعايير الدولية**: دعم IFRS, GAAP, SOCOMA
- **الامتثال المحلي**: دعم المتطلبات السعودية والعربية
- **الفوترة الإلكترونية**: تكامل مع هيئة الزكاة والضريبة والجمارك
- **التدقيق المتقدم**: مسار تدقيق شامل لجميع العمليات

### 🔗 التكامل
- **البنوك**: تكامل مع الأنظمة البنكية للتسوية التلقائية
- **بوابات الدفع**: دعم متعدد لبوابات الدفع الإلكترونية
- **أنظمة ERP**: تكامل مع SAP, Oracle, Odoo
- **APIs متقدمة**: واجهات برمجية شاملة

## 🏗️ البنية المعمارية

```
app/Domains/Accounting/
├── Controllers/           # تحكم API و Web
├── Models/               # نماذج البيانات
├── Services/             # خدمات الأعمال
├── Repositories/         # طبقة الوصول للبيانات
├── Events/               # الأحداث
├── Listeners/            # المستمعين
├── Jobs/                 # المهام المؤجلة
├── Commands/             # أوامر Artisan
├── Requests/             # طلبات التحقق
├── Resources/            # موارد API
├── Policies/             # سياسات الأذونات
├── Middleware/           # الوسطيات
├── Providers/            # مزودي الخدمات
└── Database/             # الهجرات والبذور
```

## 🚀 التثبيت والإعداد

### 1. تثبيت الحزمة

```bash
# إضافة Service Provider إلى config/app.php
App\Domains\Accounting\Providers\AccountingServiceProvider::class,
```

### 2. نشر الملفات

```bash
# نشر التكوين
php artisan vendor:publish --tag=accounting-config

# نشر الهجرات
php artisan vendor:publish --tag=accounting-migrations

# نشر العروض
php artisan vendor:publish --tag=accounting-views

# نشر الترجمات
php artisan vendor:publish --tag=accounting-lang
```

### 3. تشغيل الهجرات

```bash
php artisan migrate
```

### 4. إنشاء دليل الحسابات

```bash
# إنشاء دليل حسابات سعودي
php artisan accounting:generate-chart-of-accounts saudi_gaap

# إنشاء دليل حسابات IFRS
php artisan accounting:generate-chart-of-accounts ifrs

# عرض ما سيتم إنشاؤه دون التنفيذ
php artisan accounting:generate-chart-of-accounts saudi_gaap --dry-run
```

## ⚙️ التكوين

### ملف التكوين الأساسي

```php
// config/accounting.php
return [
    'default_currency' => 'SAR',
    'default_accounting_standard' => 'IFRS',
    'fiscal_year_start' => '01-01',
    
    'invoices' => [
        'number_format' => 'INV-{year}-{sequence}',
        'require_approval' => true,
        'auto_send' => false,
    ],
    
    'ai' => [
        'enabled' => true,
        'transaction_categorization' => true,
        'anomaly_detection' => true,
        'confidence_threshold' => 0.8,
    ],
];
```

### متغيرات البيئة

```env
# المحاسبة العامة
ACCOUNTING_DEFAULT_CURRENCY=SAR
ACCOUNTING_DEFAULT_STANDARD=IFRS
ACCOUNTING_FISCAL_YEAR_START=01-01

# الذكاء الاصطناعي
ACCOUNTING_AI_ENABLED=true
AI_CONFIDENCE_THRESHOLD=0.8

# التكامل مع الزكاة والضريبة
ZATCA_INTEGRATION_ENABLED=true
ZATCA_SANDBOX=true
ZATCA_API_URL=https://gw-fatoora.zatca.gov.sa

# البنوك
BANK_AUTO_IMPORT=true
BANK_RECONCILIATION_ENABLED=true
```

## 📝 الاستخدام

### إنشاء حساب جديد

```php
use App\Domains\Accounting\Models\Account;

$account = Account::create([
    'account_code' => '1100',
    'account_name' => 'النقدية في البنوك',
    'account_type' => 'asset',
    'account_category' => 'current_assets',
    'currency_code' => 'SAR',
    'is_active' => true,
]);
```

### إنشاء قيد يومية

```php
use App\Domains\Accounting\Services\AdvancedAccountingEngine;

$accountingEngine = app(AdvancedAccountingEngine::class);

$entry = $accountingEngine->createJournalEntry([
    'entry_date' => now(),
    'description' => 'قيد افتتاحي',
    'lines' => [
        [
            'account_id' => 1,
            'debit_amount' => 10000,
            'credit_amount' => 0,
            'description' => 'رصيد افتتاحي - نقدية',
        ],
        [
            'account_id' => 2,
            'debit_amount' => 0,
            'credit_amount' => 10000,
            'description' => 'رصيد افتتاحي - رأس المال',
        ],
    ],
]);
```

### إنشاء فاتورة

```php
use App\Domains\Accounting\Services\SmartInvoicingService;

$invoicingService = app(SmartInvoicingService::class);

$invoice = $invoicingService->createInvoice([
    'customer_id' => 1,
    'invoice_date' => now(),
    'due_date' => now()->addDays(30),
    'currency' => 'SAR',
    'items' => [
        [
            'description' => 'خدمة استشارية',
            'quantity' => 1,
            'unit_price' => 1000,
            'tax_rate' => 15,
        ],
    ],
]);
```

### إنشاء تقرير مالي

```php
use App\Domains\Accounting\Services\AdvancedFinancialAnalyticsService;

$analyticsService = app(AdvancedFinancialAnalyticsService::class);

// تقرير الميزانية العمومية
$balanceSheet = $analyticsService->generateBalanceSheet(now()->format('Y-m-d'));

// تقرير قائمة الدخل
$incomeStatement = $analyticsService->generateIncomeStatement(
    now()->startOfYear()->format('Y-m-d'),
    now()->format('Y-m-d')
);
```

## 🔌 APIs

### نقاط النهاية الرئيسية

```http
# الحسابات
GET    /api/accounting/accounts
POST   /api/accounting/accounts
GET    /api/accounting/accounts/{id}
PUT    /api/accounting/accounts/{id}
DELETE /api/accounting/accounts/{id}

# الفواتير
GET    /api/accounting/invoices
POST   /api/accounting/invoices
GET    /api/accounting/invoices/{id}
PUT    /api/accounting/invoices/{id}
POST   /api/accounting/invoices/{id}/send

# القيود اليومية
GET    /api/accounting/journal-entries
POST   /api/accounting/journal-entries
POST   /api/accounting/journal-entries/{id}/post

# التقارير
GET    /api/accounting/reports/balance-sheet
GET    /api/accounting/reports/income-statement
GET    /api/accounting/reports/trial-balance
```

### مثال على استخدام API

```javascript
// إنشاء فاتورة جديدة
const response = await fetch('/api/accounting/invoices', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token,
    },
    body: JSON.stringify({
        customer_id: 1,
        invoice_date: '2024-01-15',
        due_date: '2024-02-15',
        currency: 'SAR',
        items: [
            {
                description: 'خدمة تطوير',
                quantity: 10,
                unit_price: 500,
                tax_rate: 15
            }
        ]
    })
});

const invoice = await response.json();
```

## 🎯 الأحداث والمستمعين

### الأحداث المتاحة

```php
// أحداث الحسابات
AccountCreated::class
AccountUpdated::class
AccountDeleted::class

// أحداث الفواتير
InvoiceCreated::class
InvoiceSent::class
InvoicePaid::class

// أحداث القيود
JournalEntryCreated::class
JournalEntryPosted::class

// أحداث المدفوعات
PaymentReceived::class
PaymentApproved::class
```

### مثال على مستمع مخصص

```php
use App\Domains\Accounting\Events\InvoiceCreated;

class SendInvoiceNotification
{
    public function handle(InvoiceCreated $event)
    {
        // إرسال إشعار بإنشاء الفاتورة
        Mail::to($event->invoice->customer->email)
            ->send(new InvoiceCreatedMail($event->invoice));
    }
}
```

## 🔒 الأمان والأذونات

### الأذونات المتاحة

```php
// أذونات الحسابات
'view-accounts'
'create-accounts'
'update-accounts'
'delete-accounts'

// أذونات الفواتير
'view-invoices'
'create-invoices'
'update-invoices'
'send-invoices'
'approve-invoices'

// أذونات التقارير
'view-financial-reports'
'export-reports'
'schedule-reports'
```

### مثال على سياسة مخصصة

```php
class CustomAccountPolicy
{
    public function update(User $user, Account $account)
    {
        // السماح للمحاسبين الكبار فقط بتعديل الحسابات الرئيسية
        if ($account->level === 1) {
            return $user->hasRole('senior-accountant');
        }
        
        return $user->can('update-accounts');
    }
}
```

## 📊 التقارير المتاحة

### التقارير الأساسية
- **الميزانية العمومية** - Balance Sheet
- **قائمة الدخل** - Income Statement
- **قائمة التدفقات النقدية** - Cash Flow Statement
- **ميزان المراجعة** - Trial Balance
- **دفتر الأستاذ العام** - General Ledger

### التقارير التحليلية
- **تحليل الربحية** - Profitability Analysis
- **تحليل السيولة** - Liquidity Analysis
- **نسب الكفاءة** - Efficiency Ratios
- **تحليل الاتجاهات** - Trend Analysis
- **مقارنة الأداء** - Performance Comparison

## 🔧 المهام المجدولة

```php
// في app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // إنشاء الفواتير المتكررة
    $schedule->job(new GenerateRecurringInvoicesJob())
             ->daily()
             ->at('06:00');
    
    // التسوية البنكية التلقائية
    $schedule->job(new BankReconciliationJob())
             ->daily()
             ->at('02:00');
    
    // تحديث أسعار الصرف
    $schedule->command('accounting:update-exchange-rates')
             ->hourly();
}
```

## 🧪 الاختبارات

```bash
# تشغيل جميع اختبارات المحاسبة
php artisan test --testsuite=Accounting

# اختبار ميزة محددة
php artisan test tests/Feature/Accounting/InvoiceTest.php

# اختبار مع التغطية
php artisan test --coverage --testsuite=Accounting
```

## 📈 الأداء والتحسين

### نصائح الأداء
- استخدم الكاش للتقارير الثقيلة
- فعل الفهرسة على الحقول المهمة
- استخدم المعالجة المتوازية للمهام الكبيرة
- فعل ضغط البيانات للأرشيف

### مراقبة الأداء
```php
// في config/accounting.php
'performance' => [
    'cache_enabled' => true,
    'cache_duration' => 3600,
    'queue_enabled' => true,
    'parallel_processing' => true,
],
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة الاختبارات
4. تنفيذ الميزة
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

- **الوثائق**: [docs.hesabiai.com](https://docs.hesabiai.com)
- **المجتمع**: [community.hesabiai.com](https://community.hesabiai.com)
- **الدعم الفني**: <EMAIL>
- **GitHub Issues**: [github.com/hesabiai/accounting/issues](https://github.com/hesabiai/accounting/issues)

---

**تم تطويره بـ ❤️ من فريق حسابي AI**
