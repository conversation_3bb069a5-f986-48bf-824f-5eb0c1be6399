<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج القيد المحاسبي
 * يمثل المعاملات المحاسبية في دفتر اليومية الإلكتروني
 */
class JournalEntry extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'entry_number',
        'reference_number',
        'description',
        'entry_date',
        'posting_date',
        'period_id',
        'account_id',
        'debit_amount',
        'credit_amount',
        'currency',
        'exchange_rate',
        'base_currency_amount',
        'status',
        'source_type',
        'source_id',
        'document_reference',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
        'metadata',
    ];

    protected $casts = [
        'entry_date' => 'date',
        'posting_date' => 'date',
        'debit_amount' => 'decimal:2',
        'credit_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'base_currency_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * حالات القيد المحاسبي
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING' => 'في الانتظار',
        'APPROVED' => 'معتمد',
        'POSTED' => 'مرحل',
        'CANCELLED' => 'ملغي',
        'REVERSED' => 'معكوس',
    ];

    /**
     * أنواع مصادر القيود
     */
    public const SOURCE_TYPES = [
        'MANUAL' => 'يدوي',
        'INVOICE' => 'فاتورة',
        'PAYMENT' => 'دفعة',
        'RECEIPT' => 'إيصال',
        'ADJUSTMENT' => 'تسوية',
        'DEPRECIATION' => 'إهلاك',
        'ACCRUAL' => 'استحقاق',
        'REVERSAL' => 'عكس قيد',
        'OPENING_BALANCE' => 'رصيد افتتاحي',
        'CLOSING_BALANCE' => 'رصيد ختامي',
    ];

    /**
     * الحساب المحاسبي
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * الفترة المحاسبية
     */
    public function period(): BelongsTo
    {
        return $this->belongsTo(AccountingPeriod::class, 'period_id');
    }

    /**
     * المستخدم الذي أنشأ القيد
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * المستخدم الذي اعتمد القيد
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * المصدر المرتبط بالقيد (polymorphic)
     */
    public function source()
    {
        return $this->morphTo();
    }

    /**
     * القيود المرتبطة (نفس رقم القيد)
     */
    public function relatedEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class, 'entry_number', 'entry_number')
            ->where('id', '!=', $this->id);
    }

    /**
     * التحقق من توازن القيد
     */
    public function isBalanced(): bool
    {
        $totalDebits = static::where('entry_number', $this->entry_number)->sum('debit_amount');
        $totalCredits = static::where('entry_number', $this->entry_number)->sum('credit_amount');

        return abs($totalDebits - $totalCredits) < 0.01; // تسامح للأخطاء العشرية
    }

    /**
     * التحقق من كون القيد مدين
     */
    public function isDebit(): bool
    {
        return $this->debit_amount > 0;
    }

    /**
     * التحقق من كون القيد دائن
     */
    public function isCredit(): bool
    {
        return $this->credit_amount > 0;
    }

    /**
     * الحصول على المبلغ (مدين أو دائن)
     */
    public function getAmount(): float
    {
        return $this->debit_amount ?: $this->credit_amount;
    }

    /**
     * الحصول على نوع القيد (مدين/دائن)
     */
    public function getType(): string
    {
        return $this->isDebit() ? 'debit' : 'credit';
    }

    /**
     * عكس القيد
     */
    public function reverse(string $reason = null): self
    {
        $reversalEntry = $this->replicate();
        $reversalEntry->entry_number = $this->generateEntryNumber();
        $reversalEntry->reference_number = 'REV-' . $this->reference_number;
        $reversalEntry->description = 'عكس قيد: ' . $this->description;
        $reversalEntry->debit_amount = $this->credit_amount;
        $reversalEntry->credit_amount = $this->debit_amount;
        $reversalEntry->source_type = 'REVERSAL';
        $reversalEntry->source_id = $this->id;
        $reversalEntry->status = 'DRAFT';
        $reversalEntry->notes = $reason ? "سبب العكس: {$reason}" : null;
        $reversalEntry->save();

        return $reversalEntry;
    }

    /**
     * توليد رقم قيد جديد
     */
    public static function generateEntryNumber(): string
    {
        $year = now()->year;
        $lastEntry = static::whereYear('created_at', $year)
            ->orderBy('entry_number', 'desc')
            ->first();

        if (!$lastEntry) {
            return $year . '-0001';
        }

        $lastNumber = (int) substr($lastEntry->entry_number, -4);
        return $year . '-' . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    /**
     * نطاق للقيود المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * نطاق للقيود المرحلة
     */
    public function scopePosted($query)
    {
        return $query->where('status', 'POSTED');
    }

    /**
     * نطاق للقيود المدينة
     */
    public function scopeDebits($query)
    {
        return $query->where('debit_amount', '>', 0);
    }

    /**
     * نطاق للقيود الدائنة
     */
    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * نطاق حسب الفترة
     */
    public function scopeInPeriod($query, $periodId)
    {
        return $query->where('period_id', $periodId);
    }

    /**
     * نطاق حسب التاريخ
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('entry_date', [$startDate, $endDate]);
    }
}
