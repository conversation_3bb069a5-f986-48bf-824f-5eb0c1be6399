<?php

namespace App\Domains\Payments\Services;

use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة كشف الاحتيال والمخاطر
 * تحلل المعاملات وتكتشف الأنشطة المشبوهة
 */
class FraudDetectionService
{
    /**
     * تحليل معاملة للكشف عن الاحتيال
     */
    public function analyzeTransaction(PaymentTransaction $transaction): array
    {
        $riskScore = 0;
        $riskFactors = [];
        $recommendations = [];

        // تحليل المبلغ
        $amountAnalysis = $this->analyzeAmount($transaction);
        $riskScore += $amountAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $amountAnalysis['factors']);

        // تحليل الموقع الجغرافي
        $locationAnalysis = $this->analyzeLocation($transaction);
        $riskScore += $locationAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $locationAnalysis['factors']);

        // تحليل سلوك المستخدم
        $behaviorAnalysis = $this->analyzeBehavior($transaction);
        $riskScore += $behaviorAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $behaviorAnalysis['factors']);

        // تحليل طريقة الدفع
        $paymentMethodAnalysis = $this->analyzePaymentMethod($transaction);
        $riskScore += $paymentMethodAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $paymentMethodAnalysis['factors']);

        // تحليل التوقيت
        $timingAnalysis = $this->analyzeTiming($transaction);
        $riskScore += $timingAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $timingAnalysis['factors']);

        // تحليل الجهاز
        $deviceAnalysis = $this->analyzeDevice($transaction);
        $riskScore += $deviceAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $deviceAnalysis['factors']);

        // تحليل القوائم السوداء
        $blacklistAnalysis = $this->analyzeBlacklists($transaction);
        $riskScore += $blacklistAnalysis['score'];
        $riskFactors = array_merge($riskFactors, $blacklistAnalysis['factors']);

        // تحديد مستوى المخاطر
        $riskLevel = $this->determineRiskLevel($riskScore);

        // توليد التوصيات
        $recommendations = $this->generateRecommendations($riskLevel, $riskFactors);

        return [
            'risk_score' => min($riskScore, 100),
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'recommendations' => $recommendations,
            'analysis_details' => [
                'amount' => $amountAnalysis,
                'location' => $locationAnalysis,
                'behavior' => $behaviorAnalysis,
                'payment_method' => $paymentMethodAnalysis,
                'timing' => $timingAnalysis,
                'device' => $deviceAnalysis,
                'blacklist' => $blacklistAnalysis,
            ],
            'analyzed_at' => now(),
        ];
    }

    /**
     * تحليل المبلغ
     */
    protected function analyzeAmount(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // مبالغ كبيرة غير عادية
        if ($transaction->amount > 10000) {
            $score += 25;
            $factors[] = 'large_amount';
        }

        // مبالغ صغيرة جداً (قد تكون اختبار)
        if ($transaction->amount < 1) {
            $score += 15;
            $factors[] = 'very_small_amount';
        }

        // مقارنة مع متوسط معاملات المستخدم
        if ($transaction->user_id) {
            $userAverage = $this->getUserAverageTransactionAmount($transaction->user_id);
            if ($userAverage > 0 && $transaction->amount > ($userAverage * 5)) {
                $score += 20;
                $factors[] = 'unusual_amount_for_user';
            }
        }

        // مبالغ مستديرة (قد تكون مشبوهة)
        if ($transaction->amount == round($transaction->amount) && $transaction->amount >= 1000) {
            $score += 5;
            $factors[] = 'round_amount';
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل الموقع الجغرافي
     */
    protected function analyzeLocation(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // دول عالية المخاطر
        $highRiskCountries = ['AF', 'IR', 'KP', 'SY', 'SO'];
        if (in_array($transaction->country_code, $highRiskCountries)) {
            $score += 40;
            $factors[] = 'high_risk_country';
        }

        // تحليل IP
        if ($transaction->ip_address) {
            $ipAnalysis = $this->analyzeIPAddress($transaction->ip_address);
            $score += $ipAnalysis['score'];
            $factors = array_merge($factors, $ipAnalysis['factors']);
        }

        // مقارنة مع موقع المستخدم المعتاد
        if ($transaction->user_id) {
            $userCountries = $this->getUserCommonCountries($transaction->user_id);
            if (!in_array($transaction->country_code, $userCountries)) {
                $score += 15;
                $factors[] = 'unusual_country_for_user';
            }
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل سلوك المستخدم
     */
    protected function analyzeBehavior(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        if (!$transaction->user_id) {
            $score += 10;
            $factors[] = 'guest_transaction';
            return ['score' => $score, 'factors' => $factors];
        }

        // تحليل تكرار المعاملات
        $recentTransactions = $this->getRecentUserTransactions($transaction->user_id, 24);
        if (count($recentTransactions) > 10) {
            $score += 25;
            $factors[] = 'high_frequency_transactions';
        }

        // حساب جديد
        $user = User::find($transaction->user_id);
        if ($user && $user->created_at->diffInDays(now()) < 7) {
            $score += 15;
            $factors[] = 'new_account';
        }

        // معاملات متتالية بمبالغ متشابهة
        $similarAmountTransactions = $recentTransactions->filter(function ($t) use ($transaction) {
            return abs($t->amount - $transaction->amount) < ($transaction->amount * 0.1);
        });

        if ($similarAmountTransactions->count() >= 3) {
            $score += 20;
            $factors[] = 'repeated_similar_amounts';
        }

        // معاملات سريعة متتالية
        $veryRecentTransactions = $this->getRecentUserTransactions($transaction->user_id, 1);
        if (count($veryRecentTransactions) > 3) {
            $score += 30;
            $factors[] = 'rapid_successive_transactions';
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل طريقة الدفع
     */
    protected function analyzePaymentMethod(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // طرق دفع عالية المخاطر
        $highRiskMethods = ['cryptocurrency', 'prepaid_card'];
        if (in_array($transaction->payment_method, $highRiskMethods)) {
            $score += 20;
            $factors[] = 'high_risk_payment_method';
        }

        // تحليل طريقة الدفع المحفوظة
        if ($transaction->payment_method_id) {
            $paymentMethod = PaymentMethod::find($transaction->payment_method_id);
            if ($paymentMethod) {
                $methodRisk = $paymentMethod->calculateRiskScore();
                $score += $methodRisk * 0.3; // 30% من درجة مخاطر طريقة الدفع
                
                if ($methodRisk > 70) {
                    $factors[] = 'high_risk_saved_payment_method';
                }
            }
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل التوقيت
     */
    protected function analyzeTiming(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        $hour = $transaction->created_at->hour;
        $dayOfWeek = $transaction->created_at->dayOfWeek;

        // معاملات في أوقات غير عادية (منتصف الليل - فجر)
        if ($hour >= 2 && $hour <= 5) {
            $score += 10;
            $factors[] = 'unusual_time';
        }

        // معاملات في عطلة نهاية الأسبوع
        if (in_array($dayOfWeek, [5, 6])) { // جمعة وسبت
            $score += 5;
            $factors[] = 'weekend_transaction';
        }

        // معاملات في الأعياد
        if ($this->isHoliday($transaction->created_at)) {
            $score += 8;
            $factors[] = 'holiday_transaction';
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل الجهاز
     */
    protected function analyzeDevice(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        if (!$transaction->user_agent) {
            $score += 15;
            $factors[] = 'missing_user_agent';
            return ['score' => $score, 'factors' => $factors];
        }

        // تحليل User Agent
        $userAgent = strtolower($transaction->user_agent);

        // متصفحات قديمة أو غير شائعة
        if (strpos($userAgent, 'bot') !== false || strpos($userAgent, 'crawler') !== false) {
            $score += 30;
            $factors[] = 'bot_user_agent';
        }

        // أجهزة محمولة قديمة
        if (strpos($userAgent, 'mobile') !== false && strpos($userAgent, 'android 4') !== false) {
            $score += 10;
            $factors[] = 'old_mobile_device';
        }

        // مقارنة مع أجهزة المستخدم المعتادة
        if ($transaction->user_id) {
            $commonUserAgents = $this->getUserCommonUserAgents($transaction->user_id);
            if (!in_array($transaction->user_agent, $commonUserAgents)) {
                $score += 12;
                $factors[] = 'unusual_device_for_user';
            }
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل القوائم السوداء
     */
    protected function analyzeBlacklists(PaymentTransaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // فحص البريد الإلكتروني
        if ($this->isEmailBlacklisted($transaction->customer_email)) {
            $score += 50;
            $factors[] = 'blacklisted_email';
        }

        // فحص IP
        if ($transaction->ip_address && $this->isIPBlacklisted($transaction->ip_address)) {
            $score += 40;
            $factors[] = 'blacklisted_ip';
        }

        // فحص رقم الهاتف
        if ($transaction->customer_phone && $this->isPhoneBlacklisted($transaction->customer_phone)) {
            $score += 35;
            $factors[] = 'blacklisted_phone';
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحليل عنوان IP
     */
    protected function analyzeIPAddress(string $ipAddress): array
    {
        $score = 0;
        $factors = [];

        // فحص إذا كان IP من VPN أو Proxy
        if ($this->isVPNOrProxy($ipAddress)) {
            $score += 25;
            $factors[] = 'vpn_or_proxy';
        }

        // فحص إذا كان IP من Tor
        if ($this->isTorExit($ipAddress)) {
            $score += 40;
            $factors[] = 'tor_exit_node';
        }

        return [
            'score' => $score,
            'factors' => $factors,
        ];
    }

    /**
     * تحديد مستوى المخاطر
     */
    protected function determineRiskLevel(int $score): string
    {
        if ($score >= 80) {
            return 'very_high';
        } elseif ($score >= 60) {
            return 'high';
        } elseif ($score >= 40) {
            return 'medium';
        } elseif ($score >= 20) {
            return 'low';
        } else {
            return 'very_low';
        }
    }

    /**
     * توليد التوصيات
     */
    protected function generateRecommendations(string $riskLevel, array $riskFactors): array
    {
        $recommendations = [];

        switch ($riskLevel) {
            case 'very_high':
                $recommendations[] = 'block_transaction';
                $recommendations[] = 'manual_review';
                $recommendations[] = 'contact_customer';
                break;
                
            case 'high':
                $recommendations[] = 'require_additional_verification';
                $recommendations[] = 'manual_review';
                $recommendations[] = 'limit_transaction_amount';
                break;
                
            case 'medium':
                $recommendations[] = 'require_3d_secure';
                $recommendations[] = 'additional_monitoring';
                break;
                
            case 'low':
                $recommendations[] = 'standard_processing';
                $recommendations[] = 'light_monitoring';
                break;
                
            default:
                $recommendations[] = 'standard_processing';
        }

        // توصيات خاصة بعوامل المخاطر
        if (in_array('blacklisted_email', $riskFactors)) {
            $recommendations[] = 'verify_customer_identity';
        }

        if (in_array('high_risk_country', $riskFactors)) {
            $recommendations[] = 'enhanced_due_diligence';
        }

        if (in_array('new_account', $riskFactors)) {
            $recommendations[] = 'account_verification';
        }

        return array_unique($recommendations);
    }

    /**
     * الحصول على متوسط مبلغ معاملات المستخدم
     */
    protected function getUserAverageTransactionAmount(int $userId): float
    {
        return Cache::remember("user_avg_amount_{$userId}", 3600, function () use ($userId) {
            return PaymentTransaction::where('user_id', $userId)
                ->where('status', 'succeeded')
                ->where('created_at', '>=', now()->subDays(90))
                ->avg('amount') ?? 0;
        });
    }

    /**
     * الحصول على الدول الشائعة للمستخدم
     */
    protected function getUserCommonCountries(int $userId): array
    {
        return Cache::remember("user_countries_{$userId}", 3600, function () use ($userId) {
            return PaymentTransaction::where('user_id', $userId)
                ->where('created_at', '>=', now()->subDays(90))
                ->distinct()
                ->pluck('country_code')
                ->toArray();
        });
    }

    /**
     * الحصول على المعاملات الحديثة للمستخدم
     */
    protected function getRecentUserTransactions(int $userId, int $hours): \Illuminate\Database\Eloquent\Collection
    {
        return PaymentTransaction::where('user_id', $userId)
            ->where('created_at', '>=', now()->subHours($hours))
            ->get();
    }

    /**
     * الحصول على User Agents الشائعة للمستخدم
     */
    protected function getUserCommonUserAgents(int $userId): array
    {
        return Cache::remember("user_agents_{$userId}", 3600, function () use ($userId) {
            return PaymentTransaction::where('user_id', $userId)
                ->where('created_at', '>=', now()->subDays(30))
                ->distinct()
                ->pluck('user_agent')
                ->filter()
                ->toArray();
        });
    }

    /**
     * فحص إذا كان البريد في القائمة السوداء
     */
    protected function isEmailBlacklisted(string $email): bool
    {
        // فحص مبسط - في الواقع سيتصل بخدمة خارجية
        $blacklistedDomains = ['tempmail.com', '10minutemail.com', 'guerrillamail.com'];
        $domain = substr(strrchr($email, "@"), 1);
        
        return in_array($domain, $blacklistedDomains);
    }

    /**
     * فحص إذا كان IP في القائمة السوداء
     */
    protected function isIPBlacklisted(string $ip): bool
    {
        // فحص مبسط - في الواقع سيتصل بخدمة خارجية
        return Cache::remember("ip_blacklist_{$ip}", 3600, function () use ($ip) {
            // محاكاة استدعاء API خارجي
            return false;
        });
    }

    /**
     * فحص إذا كان الهاتف في القائمة السوداء
     */
    protected function isPhoneBlacklisted(string $phone): bool
    {
        // فحص مبسط
        return false;
    }

    /**
     * فحص إذا كان IP من VPN أو Proxy
     */
    protected function isVPNOrProxy(string $ip): bool
    {
        return Cache::remember("vpn_check_{$ip}", 3600, function () use ($ip) {
            // محاكاة استدعاء API خارجي
            return false;
        });
    }

    /**
     * فحص إذا كان IP من Tor
     */
    protected function isTorExit(string $ip): bool
    {
        return Cache::remember("tor_check_{$ip}", 3600, function () use ($ip) {
            // محاكاة استدعاء API خارجي
            return false;
        });
    }

    /**
     * فحص إذا كان التاريخ عطلة
     */
    protected function isHoliday(Carbon $date): bool
    {
        // فحص مبسط للأعياد الإسلامية والوطنية
        $holidays = [
            '01-01', // رأس السنة
            '09-23', // اليوم الوطني السعودي
            '12-02', // اليوم الوطني الإماراتي
            '07-23', // ثورة يوليو مصر
            '08-20', // ثورة الملك والشعب المغرب
        ];
        
        return in_array($date->format('m-d'), $holidays);
    }
}
