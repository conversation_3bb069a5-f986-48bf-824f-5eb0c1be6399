<?php

namespace App\Domains\Integration\Services\Transformation\Validators;

use App\Domains\Integration\Exceptions\ValidationException;
use Illuminate\Support\Facades\Log;

/**
 * JSON Schema Validator
 * Validates JSON data against JSON Schema specifications
 */
class JsonSchemaValidator
{
    protected array $config;
    protected array $schemas;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'schema_path' => storage_path('app/schemas/json'),
            'strict_mode' => true,
            'allow_additional_properties' => false,
        ], $config);
        
        $this->schemas = [];
    }

    /**
     * Validate JSON data against schema
     */
    public function validate(array $data, array $schema): array
    {
        try {
            $errors = [];
            $this->validateRecursive($data, $schema, '', $errors);
            
            return [
                'valid' => empty($errors),
                'errors' => $errors,
            ];
        } catch (\Exception $e) {
            Log::error('JSON schema validation failed', [
                'error' => $e->getMessage(),
                'schema' => $schema,
            ]);
            
            return [
                'valid' => false,
                'errors' => ['Validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Recursive validation helper
     */
    protected function validateRecursive(mixed $data, array $schema, string $path, array &$errors): void
    {
        // Type validation
        if (isset($schema['type'])) {
            if (!$this->validateType($data, $schema['type'])) {
                $errors[] = "Invalid type at '{$path}'. Expected {$schema['type']}, got " . gettype($data);
                return;
            }
        }

        // Required properties validation
        if (isset($schema['required']) && is_array($data)) {
            foreach ($schema['required'] as $requiredField) {
                if (!array_key_exists($requiredField, $data)) {
                    $errors[] = "Missing required property '{$requiredField}' at '{$path}'";
                }
            }
        }

        // Properties validation for objects
        if (isset($schema['properties']) && is_array($data)) {
            foreach ($schema['properties'] as $property => $propertySchema) {
                if (array_key_exists($property, $data)) {
                    $propertyPath = $path ? "{$path}.{$property}" : $property;
                    $this->validateRecursive($data[$property], $propertySchema, $propertyPath, $errors);
                }
            }

            // Additional properties validation
            if (!($schema['additionalProperties'] ?? $this->config['allow_additional_properties'])) {
                $allowedProperties = array_keys($schema['properties']);
                $actualProperties = array_keys($data);
                $additionalProperties = array_diff($actualProperties, $allowedProperties);
                
                foreach ($additionalProperties as $additionalProperty) {
                    $errors[] = "Additional property '{$additionalProperty}' not allowed at '{$path}'";
                }
            }
        }

        // Items validation for arrays
        if (isset($schema['items']) && is_array($data)) {
            foreach ($data as $index => $item) {
                $itemPath = $path ? "{$path}[{$index}]" : "[{$index}]";
                $this->validateRecursive($item, $schema['items'], $itemPath, $errors);
            }
        }

        // String validations
        if (is_string($data)) {
            $this->validateString($data, $schema, $path, $errors);
        }

        // Number validations
        if (is_numeric($data)) {
            $this->validateNumber($data, $schema, $path, $errors);
        }

        // Array validations
        if (is_array($data)) {
            $this->validateArray($data, $schema, $path, $errors);
        }

        // Enum validation
        if (isset($schema['enum'])) {
            if (!in_array($data, $schema['enum'], true)) {
                $allowedValues = implode(', ', $schema['enum']);
                $errors[] = "Value at '{$path}' must be one of: {$allowedValues}";
            }
        }

        // Pattern validation
        if (isset($schema['pattern']) && is_string($data)) {
            if (!preg_match('/' . $schema['pattern'] . '/', $data)) {
                $errors[] = "Value at '{$path}' does not match pattern: {$schema['pattern']}";
            }
        }

        // Format validation
        if (isset($schema['format']) && is_string($data)) {
            if (!$this->validateFormat($data, $schema['format'])) {
                $errors[] = "Value at '{$path}' does not match format: {$schema['format']}";
            }
        }
    }

    /**
     * Validate data type
     */
    protected function validateType(mixed $data, string $expectedType): bool
    {
        return match ($expectedType) {
            'string' => is_string($data),
            'number' => is_numeric($data),
            'integer' => is_int($data) || (is_numeric($data) && (int)$data == $data),
            'boolean' => is_bool($data),
            'array' => is_array($data) && array_is_list($data),
            'object' => is_array($data) && !array_is_list($data),
            'null' => is_null($data),
            default => false,
        };
    }

    /**
     * Validate string constraints
     */
    protected function validateString(string $data, array $schema, string $path, array &$errors): void
    {
        if (isset($schema['minLength']) && strlen($data) < $schema['minLength']) {
            $errors[] = "String at '{$path}' is too short. Minimum length: {$schema['minLength']}";
        }

        if (isset($schema['maxLength']) && strlen($data) > $schema['maxLength']) {
            $errors[] = "String at '{$path}' is too long. Maximum length: {$schema['maxLength']}";
        }
    }

    /**
     * Validate number constraints
     */
    protected function validateNumber(float|int $data, array $schema, string $path, array &$errors): void
    {
        if (isset($schema['minimum']) && $data < $schema['minimum']) {
            $errors[] = "Number at '{$path}' is too small. Minimum: {$schema['minimum']}";
        }

        if (isset($schema['maximum']) && $data > $schema['maximum']) {
            $errors[] = "Number at '{$path}' is too large. Maximum: {$schema['maximum']}";
        }

        if (isset($schema['exclusiveMinimum']) && $data <= $schema['exclusiveMinimum']) {
            $errors[] = "Number at '{$path}' must be greater than {$schema['exclusiveMinimum']}";
        }

        if (isset($schema['exclusiveMaximum']) && $data >= $schema['exclusiveMaximum']) {
            $errors[] = "Number at '{$path}' must be less than {$schema['exclusiveMaximum']}";
        }

        if (isset($schema['multipleOf']) && fmod($data, $schema['multipleOf']) !== 0.0) {
            $errors[] = "Number at '{$path}' must be a multiple of {$schema['multipleOf']}";
        }
    }

    /**
     * Validate array constraints
     */
    protected function validateArray(array $data, array $schema, string $path, array &$errors): void
    {
        $count = count($data);

        if (isset($schema['minItems']) && $count < $schema['minItems']) {
            $errors[] = "Array at '{$path}' has too few items. Minimum: {$schema['minItems']}";
        }

        if (isset($schema['maxItems']) && $count > $schema['maxItems']) {
            $errors[] = "Array at '{$path}' has too many items. Maximum: {$schema['maxItems']}";
        }

        if (isset($schema['uniqueItems']) && $schema['uniqueItems']) {
            if (count($data) !== count(array_unique($data, SORT_REGULAR))) {
                $errors[] = "Array at '{$path}' must contain unique items";
            }
        }
    }

    /**
     * Validate string format
     */
    protected function validateFormat(string $data, string $format): bool
    {
        return match ($format) {
            'email' => filter_var($data, FILTER_VALIDATE_EMAIL) !== false,
            'uri' => filter_var($data, FILTER_VALIDATE_URL) !== false,
            'date' => $this->validateDate($data, 'Y-m-d'),
            'time' => $this->validateDate($data, 'H:i:s'),
            'date-time' => $this->validateDateTime($data),
            'ipv4' => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false,
            'ipv6' => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false,
            'uuid' => $this->validateUuid($data),
            default => true, // Unknown formats are considered valid
        };
    }

    /**
     * Validate date format
     */
    protected function validateDate(string $data, string $format): bool
    {
        $date = \DateTime::createFromFormat($format, $data);
        return $date && $date->format($format) === $data;
    }

    /**
     * Validate datetime format
     */
    protected function validateDateTime(string $data): bool
    {
        return strtotime($data) !== false;
    }

    /**
     * Validate UUID format
     */
    protected function validateUuid(string $data): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $data) === 1;
    }

    /**
     * Load schema from file
     */
    public function loadSchema(string $schemaName): array
    {
        if (isset($this->schemas[$schemaName])) {
            return $this->schemas[$schemaName];
        }

        $schemaPath = $this->config['schema_path'] . "/{$schemaName}.json";
        
        if (!file_exists($schemaPath)) {
            throw new ValidationException("Schema file not found: {$schemaPath}");
        }

        $schemaContent = file_get_contents($schemaPath);
        $schema = json_decode($schemaContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new ValidationException("Invalid JSON in schema file: {$schemaPath}");
        }

        $this->schemas[$schemaName] = $schema;
        return $schema;
    }

    /**
     * Validate using named schema
     */
    public function validateWithSchema(array $data, string $schemaName): array
    {
        $schema = $this->loadSchema($schemaName);
        return $this->validate($data, $schema);
    }
}
