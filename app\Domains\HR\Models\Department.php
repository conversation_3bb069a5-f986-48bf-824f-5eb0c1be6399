<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج القسم - إدارة الهيكل التنظيمي
 */
class Department extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_ar',
        'name_en',
        'name_fr',
        'code',
        'description',
        'parent_id',
        'manager_id',
        'company_id',
        'branch_id',
        'cost_center_code',
        'budget_allocated',
        'budget_used',
        'is_active',
        'level',
        'sort_order',
        'metadata',
    ];

    protected $casts = [
        'budget_allocated' => 'decimal:2',
        'budget_used' => 'decimal:2',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * القسم الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * الأقسام الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    /**
     * جميع الأقسام الفرعية (متداخلة)
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * مدير القسم
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    /**
     * موظفي القسم
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * الموظفين النشطين
     */
    public function activeEmployees(): HasMany
    {
        return $this->employees()->active();
    }

    /**
     * المناصب في القسم
     */
    public function positions(): HasMany
    {
        return $this->hasMany(Position::class);
    }

    /**
     * الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * الفرع
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * الحصول على عدد الموظفين
     */
    public function getEmployeeCountAttribute(): int
    {
        return $this->employees()->count();
    }

    /**
     * الحصول على عدد الموظفين النشطين
     */
    public function getActiveEmployeeCountAttribute(): int
    {
        return $this->activeEmployees()->count();
    }

    /**
     * الحصول على إجمالي الرواتب
     */
    public function getTotalSalariesAttribute(): float
    {
        return $this->activeEmployees()->sum('basic_salary');
    }

    /**
     * الحصول على نسبة استخدام الميزانية
     */
    public function getBudgetUtilizationAttribute(): float
    {
        if ($this->budget_allocated <= 0) {
            return 0;
        }

        return ($this->budget_used / $this->budget_allocated) * 100;
    }

    /**
     * الحصول على الاسم المترجم
     */
    public function getLocalizedName(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            default => $this->name,
        };
    }

    /**
     * الحصول على المسار الهرمي
     */
    public function getHierarchyPath(): string
    {
        $path = [$this->name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * الحصول على جميع الموظفين (شامل الأقسام الفرعية)
     */
    public function getAllEmployees(): \Illuminate\Database\Eloquent\Collection
    {
        $employees = $this->employees;
        
        foreach ($this->children as $child) {
            $employees = $employees->merge($child->getAllEmployees());
        }

        return $employees;
    }

    /**
     * حساب إحصائيات القسم
     */
    public function getStatistics(): array
    {
        $allEmployees = $this->getAllEmployees();
        
        return [
            'total_employees' => $allEmployees->count(),
            'active_employees' => $allEmployees->where('is_active', true)->count(),
            'male_employees' => $allEmployees->where('gender', 'MALE')->count(),
            'female_employees' => $allEmployees->where('gender', 'FEMALE')->count(),
            'average_age' => $allEmployees->avg('age'),
            'average_years_of_service' => $allEmployees->avg('years_of_service'),
            'total_salaries' => $allEmployees->sum('basic_salary'),
            'budget_allocated' => $this->budget_allocated,
            'budget_used' => $this->budget_used,
            'budget_utilization' => $this->budget_utilization,
        ];
    }

    /**
     * نطاق للأقسام النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق للأقسام الرئيسية
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * نطاق حسب الشركة
     */
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * نطاق حسب الفرع
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * نطاق مرتب حسب الترتيب
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
