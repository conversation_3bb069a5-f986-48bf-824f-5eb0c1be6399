<?php

namespace App\Domains\Integration\Exceptions;

use Exception;

/**
 * Sanitization Exception
 * Thrown when data sanitization fails
 */
class SanitizationException extends Exception
{
    protected array $context;
    protected string $sanitizerName;

    public function __construct(
        string $message = 'Data sanitization failed',
        string $sanitizerName = '',
        array $context = [],
        int $code = 0,
        Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->sanitizerName = $sanitizerName;
        $this->context = $context;
    }

    /**
     * Get sanitizer name that failed
     */
    public function getSanitizerName(): string
    {
        return $this->sanitizerName;
    }

    /**
     * Get sanitization context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'sanitizer' => $this->sanitizerName,
            'context' => $this->context,
            'code' => $this->getCode(),
        ];
    }
}
