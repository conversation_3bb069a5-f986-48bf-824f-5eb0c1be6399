<?php

namespace App\Domains\Accounting\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Payment Resource
 */
class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'payment_number' => $this->payment_number,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'payment_method' => $this->payment_method,
            'payment_date' => $this->payment_date?->format('Y-m-d'),
            'status' => $this->status,
            'status_name' => $this->getStatusName(),
            'reference' => $this->reference,
            'description' => $this->description,
            'notes' => $this->notes,
            'exchange_rate' => $this->exchange_rate,
            'base_amount' => $this->base_amount,
            'fees' => $this->fees,
            'net_amount' => $this->net_amount,
            'approval_date' => $this->approval_date?->format('Y-m-d H:i:s'),
            'rejection_reason' => $this->rejection_reason,
            'void_reason' => $this->void_reason,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Relationships
            'payable' => $this->whenLoaded('payable', function () {
                return [
                    'id' => $this->payable->id,
                    'type' => class_basename($this->payable),
                    'name' => $this->payable->name ?? $this->payable->title ?? 'N/A',
                ];
            }),
            
            'bank_account' => $this->whenLoaded('bankAccount', function () {
                return [
                    'id' => $this->bankAccount->id,
                    'account_name' => $this->bankAccount->account_name,
                    'account_number' => $this->bankAccount->account_number,
                    'bank_name' => $this->bankAccount->bank_name,
                ];
            }),
            
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                    'email' => $this->creator->email,
                ];
            }),
            
            'approver' => $this->whenLoaded('approver', function () {
                return [
                    'id' => $this->approver->id,
                    'name' => $this->approver->name,
                    'email' => $this->approver->email,
                ];
            }),
            
            'journal_entry' => $this->whenLoaded('journalEntry', function () {
                return [
                    'id' => $this->journalEntry->id,
                    'entry_number' => $this->journalEntry->entry_number,
                    'entry_date' => $this->journalEntry->entry_date?->format('Y-m-d'),
                    'total_debit' => $this->journalEntry->total_debit,
                    'total_credit' => $this->journalEntry->total_credit,
                ];
            }),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'payment_methods' => [
                    'cash' => 'نقدي',
                    'bank_transfer' => 'تحويل بنكي',
                    'check' => 'شيك',
                    'credit_card' => 'بطاقة ائتمان',
                    'debit_card' => 'بطاقة خصم',
                    'online' => 'دفع إلكتروني',
                ],
                'statuses' => [
                    'pending' => 'معلق',
                    'approved' => 'معتمد',
                    'completed' => 'مكتمل',
                    'rejected' => 'مرفوض',
                    'cancelled' => 'ملغي',
                    'voided' => 'باطل',
                ],
            ],
        ];
    }
}
