<?php

namespace App\Domains\ECommerce\Exceptions;

/**
 * استثناء المزامنة للتجارة الإلكترونية
 */
class ECommerceSyncException extends ECommerceException
{
    protected string $syncType = '';
    protected string $entityType = '';
    protected array $syncStats = [];
    protected array $failedRecords = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        \Exception $previous = null,
        string $syncType = '',
        string $entityType = '',
        array $syncStats = [],
        array $failedRecords = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
        
        $this->syncType = $syncType;
        $this->entityType = $entityType;
        $this->syncStats = $syncStats;
        $this->failedRecords = $failedRecords;
        $this->errorCode = 'SYNC_ERROR';
    }

    /**
     * الحصول على نوع المزامنة
     */
    public function getSyncType(): string
    {
        return $this->syncType;
    }

    /**
     * الحصول على نوع الكيان
     */
    public function getEntityType(): string
    {
        return $this->entityType;
    }

    /**
     * الحصول على إحصائيات المزامنة
     */
    public function getSyncStats(): array
    {
        return $this->syncStats;
    }

    /**
     * الحصول على السجلات الفاشلة
     */
    public function getFailedRecords(): array
    {
        return $this->failedRecords;
    }

    /**
     * تحديد ما إذا كانت المزامنة جزئية
     */
    public function isPartialSync(): bool
    {
        $stats = $this->getSyncStats();
        return isset($stats['successful']) && isset($stats['failed']) && 
               $stats['successful'] > 0 && $stats['failed'] > 0;
    }

    /**
     * تحديد ما إذا كانت المزامنة فاشلة تماماً
     */
    public function isCompleteFailure(): bool
    {
        $stats = $this->getSyncStats();
        return isset($stats['successful']) && isset($stats['failed']) && 
               $stats['successful'] === 0 && $stats['failed'] > 0;
    }

    /**
     * الحصول على معدل النجاح
     */
    public function getSuccessRate(): float
    {
        $stats = $this->getSyncStats();
        $total = ($stats['successful'] ?? 0) + ($stats['failed'] ?? 0);
        
        if ($total === 0) {
            return 0;
        }
        
        return (($stats['successful'] ?? 0) / $total) * 100;
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'sync_type' => $this->getSyncType(),
            'entity_type' => $this->getEntityType(),
            'sync_stats' => $this->getSyncStats(),
            'failed_records_count' => count($this->getFailedRecords()),
            'is_partial_sync' => $this->isPartialSync(),
            'is_complete_failure' => $this->isCompleteFailure(),
            'success_rate' => $this->getSuccessRate(),
        ]);
    }
}
