<?php

namespace App\Domains\Taxation\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب تحديث النظام الضريبي
 * تحقق شامل مع مراعاة البيانات الموجودة
 */
class UpdateTaxSystemRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('update-tax-systems');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        $taxSystemId = $this->route('tax_system') ?? $this->route('id');

        return [
            // Basic Information
            'name' => 'sometimes|string|max:255',
            'name_ar' => 'sometimes|string|max:255',
            'name_en' => 'sometimes|string|max:255',
            'description' => 'sometimes|nullable|string|max:2000',
            'country_code' => 'sometimes|string|size:2|uppercase',
            'currency' => 'sometimes|string|size:3|uppercase',
            'timezone' => 'sometimes|string|max:50',

            // Status and Settings
            'is_active' => 'sometimes|boolean',
            'is_default' => 'sometimes|boolean',
            'version' => 'sometimes|nullable|string|max:20',
            'effective_from' => 'sometimes|date',
            'effective_to' => 'sometimes|nullable|date|after:effective_from',

            // VAT Configuration
            'vat_enabled' => 'sometimes|boolean',
            'vat_rates' => 'sometimes|nullable|array',
            'vat_rates.*.name' => 'required_with:vat_rates|string|max:100',
            'vat_rates.*.name_ar' => 'required_with:vat_rates|string|max:100',
            'vat_rates.*.rate' => 'required_with:vat_rates|numeric|between:0,100',
            'vat_rates.*.is_default' => 'boolean',
            'vat_rates.*.conditions' => 'nullable|array',

            // Corporate Tax
            'corporate_tax_enabled' => 'sometimes|boolean',
            'corporate_tax_rate' => 'sometimes|nullable|numeric|between:0,100',
            'corporate_tax_threshold' => 'sometimes|nullable|numeric|min:0',

            // Withholding Tax
            'withholding_tax_enabled' => 'sometimes|boolean',
            'withholding_tax_rates' => 'sometimes|nullable|array',
            'withholding_tax_rates.*.category' => 'required_with:withholding_tax_rates|string|max:100',
            'withholding_tax_rates.*.rate' => 'required_with:withholding_tax_rates|numeric|between:0,100',
            'withholding_tax_rates.*.threshold' => 'nullable|numeric|min:0',

            // Excise Tax
            'excise_tax_enabled' => 'sometimes|boolean',
            'excise_tax_categories' => 'sometimes|nullable|array',
            'excise_tax_categories.*.name' => 'required_with:excise_tax_categories|string|max:100',
            'excise_tax_categories.*.rate' => 'required_with:excise_tax_categories|numeric|min:0',
            'excise_tax_categories.*.unit' => 'required_with:excise_tax_categories|string|in:PERCENTAGE,FIXED_AMOUNT',

            // Customs Duty
            'customs_duty_enabled' => 'sometimes|boolean',
            'customs_duty_rates' => 'sometimes|nullable|array',
            'customs_duty_rates.*.hs_code' => 'required_with:customs_duty_rates|string|max:20',
            'customs_duty_rates.*.rate' => 'required_with:customs_duty_rates|numeric|between:0,100',
            'customs_duty_rates.*.description' => 'nullable|string|max:255',

            // Tax Periods
            'tax_periods' => 'sometimes|nullable|array',
            'tax_periods.*.type' => 'required_with:tax_periods|string|in:MONTHLY,QUARTERLY,ANNUALLY',
            'tax_periods.*.tax_type' => 'required_with:tax_periods|string|in:VAT,CORPORATE,WITHHOLDING,EXCISE',
            'tax_periods.*.due_days' => 'required_with:tax_periods|integer|between:1,365',

            // Authority Integration
            'authority_integration' => 'sometimes|nullable|array',
            'authority_integration.enabled' => 'boolean',
            'authority_integration.api_endpoint' => 'nullable|url',
            'authority_integration.api_key' => 'nullable|string|max:255',
            'authority_integration.certificate_path' => 'nullable|string|max:500',
            'authority_integration.test_mode' => 'boolean',

            // Compliance Settings
            'compliance_settings' => 'sometimes|nullable|array',
            'compliance_settings.require_digital_signature' => 'boolean',
            'compliance_settings.require_tax_number' => 'boolean',
            'compliance_settings.auto_calculate_penalties' => 'boolean',
            'compliance_settings.penalty_rate' => 'nullable|numeric|between:0,100',

            // Localization
            'localization' => 'sometimes|nullable|array',
            'localization.date_format' => 'nullable|string|max:20',
            'localization.number_format' => 'nullable|string|max:20',
            'localization.decimal_places' => 'nullable|integer|between:0,6',
            'localization.thousand_separator' => 'nullable|string|max:1',
            'localization.decimal_separator' => 'nullable|string|max:1',

            // Reporting Settings
            'reporting_settings' => 'sometimes|nullable|array',
            'reporting_settings.default_format' => 'nullable|string|in:PDF,XML,EXCEL,JSON',
            'reporting_settings.include_zero_amounts' => 'boolean',
            'reporting_settings.group_by_category' => 'boolean',
            'reporting_settings.show_calculations' => 'boolean',

            // Notification Settings
            'notification_settings' => 'sometimes|nullable|array',
            'notification_settings.email_enabled' => 'boolean',
            'notification_settings.sms_enabled' => 'boolean',
            'notification_settings.reminder_days' => 'nullable|array',
            'notification_settings.reminder_days.*' => 'integer|between:1,365',

            // Advanced Features
            'features' => 'sometimes|nullable|array',
            'features.auto_reverse_charge' => 'boolean',
            'features.multi_currency_support' => 'boolean',
            'features.tax_exemption_handling' => 'boolean',
            'features.installment_payments' => 'boolean',
            'features.audit_trail' => 'boolean',

            // Custom Fields
            'custom_fields' => 'sometimes|nullable|array',
            'metadata' => 'sometimes|nullable|array',
            'notes' => 'sometimes|nullable|string|max:2000',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'name.max' => 'اسم النظام الضريبي لا يجب أن يتجاوز 255 حرف',
            'name_ar.max' => 'الاسم العربي لا يجب أن يتجاوز 255 حرف',
            'name_en.max' => 'الاسم الإنجليزي لا يجب أن يتجاوز 255 حرف',
            'country_code.size' => 'رمز الدولة يجب أن يكون حرفين',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'effective_to.after' => 'تاريخ انتهاء السريان يجب أن يكون بعد تاريخ البداية',

            // VAT Configuration
            'vat_rates.*.name.required_with' => 'اسم معدل ضريبة القيمة المضافة مطلوب',
            'vat_rates.*.rate.required_with' => 'معدل ضريبة القيمة المضافة مطلوب',
            'vat_rates.*.rate.between' => 'معدل ضريبة القيمة المضافة يجب أن يكون بين 0 و 100',

            // Corporate Tax
            'corporate_tax_rate.between' => 'معدل ضريبة الشركات يجب أن يكون بين 0 و 100',
            'corporate_tax_threshold.min' => 'حد ضريبة الشركات لا يمكن أن يكون سالباً',

            // Withholding Tax
            'withholding_tax_rates.*.category.required_with' => 'فئة ضريبة الاستقطاع مطلوبة',
            'withholding_tax_rates.*.rate.required_with' => 'معدل ضريبة الاستقطاع مطلوب',
            'withholding_tax_rates.*.rate.between' => 'معدل ضريبة الاستقطاع يجب أن يكون بين 0 و 100',

            // Authority Integration
            'authority_integration.api_endpoint.url' => 'رابط API غير صحيح',

            // Compliance Settings
            'compliance_settings.penalty_rate.between' => 'معدل الغرامة يجب أن يكون بين 0 و 100',

            // Localization
            'localization.decimal_places.between' => 'عدد الخانات العشرية يجب أن يكون بين 0 و 6',

            // Reporting Settings
            'reporting_settings.default_format.in' => 'تنسيق التقرير الافتراضي غير صحيح',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        if ($this->has('country_code')) {
            $this->merge([
                'country_code' => strtoupper(trim($this->country_code))
            ]);
        }

        if ($this->has('currency')) {
            $this->merge([
                'currency' => strtoupper(trim($this->currency))
            ]);
        }
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $taxSystemId = $this->route('tax_system') ?? $this->route('id');

            // التحقق من وجود نظام افتراضي واحد فقط
            if ($this->has('is_default') && $this->is_default) {
                $existingDefault = \App\Domains\Taxation\Models\TaxSystem::where('is_default', true)
                    ->where('id', '!=', $taxSystemId)
                    ->first();
                if ($existingDefault) {
                    $validator->errors()->add('is_default', 'يوجد نظام ضريبي افتراضي بالفعل');
                }
            }

            // التحقق من معدلات ضريبة القيمة المضافة
            if ($this->has('vat_rates') && $this->vat_rates) {
                $defaultCount = 0;
                foreach ($this->vat_rates as $index => $rate) {
                    if ($rate['is_default'] ?? false) {
                        $defaultCount++;
                    }
                }
                
                if ($defaultCount > 1) {
                    $validator->errors()->add('vat_rates', 'يمكن تعيين معدل واحد فقط كافتراضي');
                }
            }

            // التحقق من إعدادات التكامل
            if ($this->has('authority_integration') && ($this->authority_integration['enabled'] ?? false)) {
                if (empty($this->authority_integration['api_endpoint'])) {
                    $validator->errors()->add('authority_integration.api_endpoint', 'رابط API مطلوب عند تفعيل التكامل');
                }
            }

            // التحقق من الفترات الضريبية
            if ($this->has('tax_periods') && $this->tax_periods) {
                $combinations = [];
                foreach ($this->tax_periods as $index => $period) {
                    $combination = $period['type'] . '_' . $period['tax_type'];
                    if (in_array($combination, $combinations)) {
                        $validator->errors()->add("tax_periods.{$index}", 'لا يمكن تكرار نفس نوع الفترة لنفس نوع الضريبة');
                    }
                    $combinations[] = $combination;
                }
            }

            // التحقق من إعدادات التوطين
            if ($this->has('localization') && $this->localization) {
                if (isset($this->localization['thousand_separator']) && 
                    isset($this->localization['decimal_separator']) &&
                    $this->localization['thousand_separator'] === $this->localization['decimal_separator']) {
                    $validator->errors()->add('localization.decimal_separator', 'فاصل الآلاف وفاصل العشرية يجب أن يكونا مختلفين');
                }
            }

            // التحقق من التغييرات الحساسة
            if ($this->hasSensitiveChanges()) {
                $taxSystem = \App\Domains\Taxation\Models\TaxSystem::find($taxSystemId);
                if ($taxSystem && $taxSystem->taxReturns()->whereIn('status', ['SUBMITTED', 'APPROVED'])->exists()) {
                    $validator->errors()->add('sensitive_changes', 'لا يمكن تعديل الإعدادات الحساسة مع وجود إقرارات مقدمة');
                }
            }
        });
    }

    /**
     * التحقق من وجود تغييرات حساسة
     */
    public function hasSensitiveChanges(): bool
    {
        $sensitiveFields = [
            'vat_rates',
            'corporate_tax_rate',
            'withholding_tax_rates',
            'excise_tax_categories',
            'customs_duty_rates',
            'tax_periods',
        ];

        foreach ($sensitiveFields as $field) {
            if ($this->has($field)) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على التغييرات الحساسة
     */
    public function getSensitiveChanges(): array
    {
        $sensitiveFields = [
            'vat_rates' => 'معدلات ضريبة القيمة المضافة',
            'corporate_tax_rate' => 'معدل ضريبة الشركات',
            'withholding_tax_rates' => 'معدلات ضريبة الاستقطاع',
            'excise_tax_categories' => 'فئات الضريبة الانتقائية',
            'customs_duty_rates' => 'معدلات الرسوم الجمركية',
            'tax_periods' => 'الفترات الضريبية',
        ];

        $changes = [];
        foreach ($sensitiveFields as $field => $label) {
            if ($this->has($field)) {
                $changes[$field] = $label;
            }
        }

        return $changes;
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value, $key) {
            // الاحتفاظ بالحقول المنطقية حتى لو كانت false
            if (in_array($key, [
                'is_active', 'is_default', 'vat_enabled', 'corporate_tax_enabled',
                'withholding_tax_enabled', 'excise_tax_enabled', 'customs_duty_enabled'
            ])) {
                return true;
            }
            return $value !== null && $value !== '';
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * التحقق من إمكانية التحديث
     */
    public function canUpdate(): bool
    {
        $taxSystemId = $this->route('tax_system') ?? $this->route('id');
        $taxSystem = \App\Domains\Taxation\Models\TaxSystem::find($taxSystemId);

        if (!$taxSystem) {
            return false;
        }

        // التحقق من وجود إقرارات مقدمة
        if ($this->hasSensitiveChanges()) {
            return !$taxSystem->taxReturns()
                ->whereIn('status', ['SUBMITTED', 'APPROVED'])
                ->exists();
        }

        return true;
    }
}
