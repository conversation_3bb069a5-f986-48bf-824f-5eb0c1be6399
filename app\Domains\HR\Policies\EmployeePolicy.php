<?php

namespace App\Domains\HR\Policies;

use App\Domains\HR\Models\Employee;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أمان الموظفين
 * Employee Security Policy
 */
class EmployeePolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي موظف
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_employees') || 
               $user->hasRole(['admin', 'hr_manager', 'hr_specialist', 'department_manager']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الموظف
     */
    public function view(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية يمكنهم رؤية جميع الموظفين
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم رؤية موظفي قسمهم
        if ($user->hasRole('department_manager')) {
            return $user->department_id === $employee->department_id;
        }

        // المشرفون يمكنهم رؤية موظفيهم المباشرين
        if ($user->hasRole('supervisor')) {
            return $employee->supervisor_id === $user->id;
        }

        // الموظف يمكنه رؤية ملفه الشخصي
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء موظف
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_employees') || 
               $user->hasRole(['admin', 'hr_manager', 'hr_specialist']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الموظف
     */
    public function update(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية يمكنهم تحديث جميع الموظفين
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم تحديث بيانات موظفي قسمهم (محدود)
        if ($user->hasRole('department_manager') && $user->department_id === $employee->department_id) {
            return true;
        }

        // الموظف يمكنه تحديث بياناته الشخصية المحدودة
        if ($user->id === $employee->id) {
            return $user->hasPermissionTo('update_own_profile');
        }

        return $user->hasPermissionTo('update_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الموظف
     */
    public function delete(Employee $user, Employee $employee): bool
    {
        // منع حذف الحساب الشخصي
        if ($user->id === $employee->id) {
            return false;
        }

        // منع حذف المدراء العامين
        if ($employee->hasRole('admin')) {
            return $user->hasRole('admin');
        }

        // فقط المدراء وموظفو الموارد البشرية يمكنهم حذف الموظفين
        return $user->hasRole(['admin', 'hr_manager']) || 
               $user->hasPermissionTo('delete_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الراتب
     */
    public function viewSalary(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية والمحاسبون
        if ($user->hasRole(['admin', 'hr_manager', 'payroll_specialist', 'financial_manager'])) {
            return true;
        }

        // الموظف يمكنه رؤية راتبه
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_employee_salaries');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الراتب
     */
    public function updateSalary(Employee $user, Employee $employee): bool
    {
        return $user->hasRole(['admin', 'hr_manager', 'payroll_specialist']) || 
               $user->hasPermissionTo('update_employee_salaries');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الحضور والانصراف
     */
    public function viewAttendance(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم رؤية حضور موظفي قسمهم
        if ($user->hasRole('department_manager') && $user->department_id === $employee->department_id) {
            return true;
        }

        // المشرفون يمكنهم رؤية حضور موظفيهم
        if ($user->hasRole('supervisor') && $employee->supervisor_id === $user->id) {
            return true;
        }

        // الموظف يمكنه رؤية حضوره
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_employee_attendance');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة الإجازات
     */
    public function manageLeaves(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم إدارة إجازات موظفي قسمهم
        if ($user->hasRole('department_manager') && $user->department_id === $employee->department_id) {
            return true;
        }

        // المشرفون يمكنهم إدارة إجازات موظفيهم
        if ($user->hasRole('supervisor') && $employee->supervisor_id === $user->id) {
            return true;
        }

        return $user->hasPermissionTo('manage_employee_leaves');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تقييم الأداء
     */
    public function viewPerformance(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم رؤية تقييم موظفي قسمهم
        if ($user->hasRole('department_manager') && $user->department_id === $employee->department_id) {
            return true;
        }

        // المشرفون يمكنهم رؤية تقييم موظفيهم
        if ($user->hasRole('supervisor') && $employee->supervisor_id === $user->id) {
            return true;
        }

        // الموظف يمكنه رؤية تقييمه
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_employee_performance');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء تقييم الأداء
     */
    public function performanceReview(Employee $user, Employee $employee): bool
    {
        // منع تقييم النفس
        if ($user->id === $employee->id) {
            return false;
        }

        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // مدراء الأقسام يمكنهم تقييم موظفي قسمهم
        if ($user->hasRole('department_manager') && $user->department_id === $employee->department_id) {
            return true;
        }

        // المشرفون يمكنهم تقييم موظفيهم المباشرين
        if ($user->hasRole('supervisor') && $employee->supervisor_id === $user->id) {
            return true;
        }

        return $user->hasPermissionTo('conduct_performance_reviews');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنهاء خدمة الموظف
     */
    public function terminate(Employee $user, Employee $employee): bool
    {
        // منع إنهاء الخدمة الذاتية
        if ($user->id === $employee->id) {
            return false;
        }

        // منع إنهاء خدمة المدراء العامين
        if ($employee->hasRole('admin')) {
            return $user->hasRole('admin');
        }

        return $user->hasRole(['admin', 'hr_manager']) || 
               $user->hasPermissionTo('terminate_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إعادة توظيف الموظف
     */
    public function rehire(Employee $user, Employee $employee): bool
    {
        return $user->hasRole(['admin', 'hr_manager']) || 
               $user->hasPermissionTo('rehire_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المستندات
     */
    public function viewDocuments(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // الموظف يمكنه رؤية مستنداته
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_employee_documents');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه رفع المستندات
     */
    public function uploadDocuments(Employee $user, Employee $employee): bool
    {
        // المدراء وموظفو الموارد البشرية
        if ($user->hasRole(['admin', 'hr_manager', 'hr_specialist'])) {
            return true;
        }

        // الموظف يمكنه رفع مستنداته الشخصية
        if ($user->id === $employee->id) {
            return $user->hasPermissionTo('upload_own_documents');
        }

        return $user->hasPermissionTo('upload_employee_documents');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التاريخ الوظيفي
     */
    public function viewHistory(Employee $user, Employee $employee): bool
    {
        return $this->view($user, $employee) && 
               ($user->hasRole(['admin', 'hr_manager', 'hr_specialist', 'auditor']) || 
                $user->hasPermissionTo('view_employee_history'));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير بيانات الموظفين
     */
    public function export(Employee $user): bool
    {
        return $user->hasRole(['admin', 'hr_manager', 'auditor']) || 
               $user->hasPermissionTo('export_employee_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد بيانات الموظفين
     */
    public function import(Employee $user): bool
    {
        return $user->hasRole(['admin', 'hr_manager']) || 
               $user->hasPermissionTo('import_employee_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء مراجعة للموظف
     */
    public function audit(Employee $user, Employee $employee): bool
    {
        return $user->hasRole(['admin', 'auditor', 'hr_manager']) || 
               $user->hasPermissionTo('audit_employees');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات الحساسة
     */
    public function viewSensitiveData(Employee $user, Employee $employee): bool
    {
        // البيانات الحساسة مثل الراتب، الهوية، البيانات الطبية
        if ($user->hasRole(['admin', 'hr_manager'])) {
            return true;
        }

        // الموظف يمكنه رؤية بياناته الحساسة
        if ($user->id === $employee->id) {
            return true;
        }

        return $user->hasPermissionTo('view_sensitive_employee_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل الأدوار والصلاحيات
     */
    public function manageRoles(Employee $user, Employee $employee): bool
    {
        // منع تعديل الأدوار الذاتية
        if ($user->id === $employee->id) {
            return false;
        }

        return $user->hasRole(['admin']) || 
               $user->hasPermissionTo('manage_employee_roles');
    }
}
