<?php

namespace App\Domains\Taxation\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Services\DynamicTaxEngine;
use App\Domains\Taxation\Services\TaxAuthorityIntegrationService;
use App\Domains\Taxation\Requests\StoreTaxSystemRequest;
use App\Domains\Taxation\Requests\UpdateTaxSystemRequest;
use App\Domains\Taxation\Resources\TaxSystemResource;
use App\Domains\Taxation\Resources\TaxSystemCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * متحكم الأنظمة الضريبية
 * إدارة شاملة للأنظمة الضريبية المختلفة
 */
class TaxSystemController extends Controller
{
    protected DynamicTaxEngine $taxEngine;
    protected TaxAuthorityIntegrationService $integrationService;

    public function __construct(
        DynamicTaxEngine $taxEngine,
        TaxAuthorityIntegrationService $integrationService
    ) {
        $this->taxEngine = $taxEngine;
        $this->integrationService = $integrationService;
    }

    /**
     * عرض قائمة الأنظمة الضريبية
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', TaxSystem::class);

        $query = TaxSystem::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_ar', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%")
                  ->orWhere('country_code', 'like', "%{$search}%");
            });
        }

        // التصفية حسب الدولة
        if ($request->filled('country_code')) {
            $query->where('country_code', $request->country_code);
        }

        // التصفية حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // التصفية للأنظمة الافتراضية
        if ($request->boolean('default_only')) {
            $query->where('is_default', true);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $taxSystems = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new TaxSystemCollection($taxSystems),
            'meta' => [
                'total' => $taxSystems->total(),
                'per_page' => $taxSystems->perPage(),
                'current_page' => $taxSystems->currentPage(),
                'last_page' => $taxSystems->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء نظام ضريبي جديد
     */
    public function store(StoreTaxSystemRequest $request): JsonResponse
    {
        $this->authorize('create', TaxSystem::class);

        DB::beginTransaction();

        try {
            $taxSystem = TaxSystem::create($request->validated());

            // إعداد القواعد الافتراضية
            $this->setupDefaultRules($taxSystem);

            // تحديث الكاش
            Cache::forget('tax_systems_active');
            Cache::forget('default_tax_system');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء النظام الضريبي بنجاح',
                'data' => new TaxSystemResource($taxSystem),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء النظام الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل نظام ضريبي محدد
     */
    public function show(int $id): JsonResponse
    {
        $taxSystem = TaxSystem::with(['taxRules', 'taxReturns'])->findOrFail($id);
        $this->authorize('view', $taxSystem);

        // إضافة معلومات إضافية
        $additionalData = [
            'statistics' => $this->getTaxSystemStatistics($taxSystem),
            'compliance_status' => $this->getComplianceStatus($taxSystem),
            'integration_status' => $this->getIntegrationStatus($taxSystem),
            'recent_updates' => $this->getRecentUpdates($taxSystem),
        ];

        return response()->json([
            'success' => true,
            'data' => new TaxSystemResource($taxSystem),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث النظام الضريبي
     */
    public function update(UpdateTaxSystemRequest $request, int $id): JsonResponse
    {
        $taxSystem = TaxSystem::findOrFail($id);
        $this->authorize('update', $taxSystem);

        DB::beginTransaction();

        try {
            $oldData = $taxSystem->toArray();
            $taxSystem->update($request->validated());

            // تسجيل التغييرات
            $this->logSystemChanges($taxSystem, $oldData, $request->validated());

            // تحديث الكاش
            Cache::forget('tax_systems_active');
            if ($taxSystem->is_default) {
                Cache::forget('default_tax_system');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث النظام الضريبي بنجاح',
                'data' => new TaxSystemResource($taxSystem),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث النظام الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف النظام الضريبي
     */
    public function destroy(int $id): JsonResponse
    {
        $taxSystem = TaxSystem::findOrFail($id);
        $this->authorize('delete', $taxSystem);

        try {
            // التحقق من عدم وجود بيانات مرتبطة
            if ($taxSystem->taxReturns()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف النظام الضريبي لوجود إقرارات ضريبية مرتبطة به',
                ], 422);
            }

            if ($taxSystem->is_default) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف النظام الضريبي الافتراضي',
                ], 422);
            }

            $taxSystem->delete();

            // تحديث الكاش
            Cache::forget('tax_systems_active');

            return response()->json([
                'success' => true,
                'message' => 'تم حذف النظام الضريبي بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف النظام الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تعيين النظام الضريبي كافتراضي
     */
    public function setAsDefault(int $id): JsonResponse
    {
        $taxSystem = TaxSystem::findOrFail($id);
        $this->authorize('update', $taxSystem);

        DB::beginTransaction();

        try {
            // إلغاء تعيين النظام الافتراضي الحالي
            TaxSystem::where('is_default', true)->update(['is_default' => false]);

            // تعيين النظام الجديد كافتراضي
            $taxSystem->update(['is_default' => true, 'is_active' => true]);

            // تحديث الكاش
            Cache::forget('default_tax_system');
            Cache::forget('tax_systems_active');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين النظام الضريبي كافتراضي بنجاح',
                'data' => new TaxSystemResource($taxSystem),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعيين النظام الضريبي كافتراضي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * اختبار الاتصال مع الهيئة الضريبية
     */
    public function testConnection(int $id): JsonResponse
    {
        $taxSystem = TaxSystem::findOrFail($id);
        $this->authorize('view', $taxSystem);

        try {
            $result = $this->integrationService->testConnection($taxSystem);

            return response()->json([
                'success' => true,
                'message' => 'تم اختبار الاتصال بنجاح',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في الاتصال مع الهيئة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * مزامنة البيانات مع الهيئة الضريبية
     */
    public function syncWithAuthority(int $id): JsonResponse
    {
        $taxSystem = TaxSystem::findOrFail($id);
        $this->authorize('update', $taxSystem);

        try {
            $result = $this->integrationService->syncTaxRates($taxSystem);

            return response()->json([
                'success' => true,
                'message' => 'تم مزامنة البيانات بنجاح',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة البيانات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات النظام الضريبي
     */
    protected function getTaxSystemStatistics(TaxSystem $taxSystem): array
    {
        return [
            'total_tax_rules' => $taxSystem->taxRules()->count(),
            'active_tax_rules' => $taxSystem->taxRules()->where('is_active', true)->count(),
            'total_tax_returns' => $taxSystem->taxReturns()->count(),
            'pending_returns' => $taxSystem->taxReturns()->where('status', 'PENDING')->count(),
            'submitted_returns' => $taxSystem->taxReturns()->where('status', 'SUBMITTED')->count(),
            'approved_returns' => $taxSystem->taxReturns()->where('status', 'APPROVED')->count(),
            'total_tax_collected' => $taxSystem->taxReturns()
                ->where('status', 'APPROVED')
                ->sum('total_tax_amount'),
            'last_sync_date' => $taxSystem->last_sync_at,
        ];
    }

    /**
     * الحصول على حالة الامتثال
     */
    protected function getComplianceStatus(TaxSystem $taxSystem): array
    {
        return [
            'is_compliant' => $taxSystem->is_compliant,
            'compliance_score' => $taxSystem->compliance_score,
            'last_compliance_check' => $taxSystem->last_compliance_check_at,
            'compliance_issues' => $taxSystem->compliance_issues ?? [],
            'next_compliance_check' => $taxSystem->next_compliance_check_at,
        ];
    }

    /**
     * الحصول على حالة التكامل
     */
    protected function getIntegrationStatus(TaxSystem $taxSystem): array
    {
        return [
            'is_connected' => $taxSystem->is_connected,
            'connection_status' => $taxSystem->connection_status,
            'last_connection_test' => $taxSystem->last_connection_test_at,
            'api_version' => $taxSystem->api_version,
            'supported_features' => $taxSystem->supported_features ?? [],
        ];
    }

    /**
     * الحصول على التحديثات الأخيرة
     */
    protected function getRecentUpdates(TaxSystem $taxSystem): array
    {
        return $taxSystem->activities()
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($activity) {
                return [
                    'description' => $activity->description,
                    'user' => $activity->causer?->name,
                    'date' => $activity->created_at->diffForHumans(),
                    'properties' => $activity->properties,
                ];
            })
            ->toArray();
    }

    /**
     * إعداد القواعد الافتراضية للنظام الضريبي
     */
    protected function setupDefaultRules(TaxSystem $taxSystem): void
    {
        // إنشاء قواعد ضريبة القيمة المضافة الافتراضية
        if (!empty($taxSystem->vat_rates)) {
            foreach ($taxSystem->vat_rates as $rate) {
                $taxSystem->taxRules()->create([
                    'name' => "VAT - {$rate['name']}",
                    'name_ar' => "ضريبة القيمة المضافة - {$rate['name_ar']}",
                    'type' => 'VAT',
                    'rate' => $rate['rate'],
                    'is_active' => true,
                    'effective_from' => now(),
                    'conditions' => $rate['conditions'] ?? [],
                ]);
            }
        }

        // إنشاء قواعد ضريبة الشركات
        if ($taxSystem->corporate_tax_rate > 0) {
            $taxSystem->taxRules()->create([
                'name' => 'Corporate Tax',
                'name_ar' => 'ضريبة الشركات',
                'type' => 'CORPORATE_TAX',
                'rate' => $taxSystem->corporate_tax_rate,
                'is_active' => true,
                'effective_from' => now(),
            ]);
        }
    }

    /**
     * تسجيل التغييرات في النظام
     */
    protected function logSystemChanges(TaxSystem $taxSystem, array $oldData, array $newData): void
    {
        $changes = [];
        foreach ($newData as $key => $value) {
            if (isset($oldData[$key]) && $oldData[$key] !== $value) {
                $changes[$key] = [
                    'old' => $oldData[$key],
                    'new' => $value,
                ];
            }
        }

        if (!empty($changes)) {
            activity()
                ->performedOn($taxSystem)
                ->causedBy(auth()->user())
                ->withProperties([
                    'changes' => $changes,
                    'system_name' => $taxSystem->name,
                ])
                ->log('تم تحديث النظام الضريبي');
        }
    }
}
