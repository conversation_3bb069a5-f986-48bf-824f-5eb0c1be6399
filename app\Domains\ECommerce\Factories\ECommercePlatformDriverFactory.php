<?php

namespace App\Domains\ECommerce\Factories;

use App\Domains\ECommerce\Contracts\ECommercePlatformInterface;
use App\Domains\ECommerce\Models\ECommercePlatform;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Drivers\ShopifyDriver;
use App\Domains\ECommerce\Drivers\WooCommerceDriver;
use App\Domains\ECommerce\Drivers\SallaDriver;
use App\Domains\ECommerce\Drivers\ZidDriver;
use App\Domains\ECommerce\Drivers\AmazonDriver;
use App\Domains\ECommerce\Drivers\BigCommerceDriver;
use App\Domains\ECommerce\Drivers\MagentoDriver;
use App\Domains\ECommerce\Drivers\EbayDriver;
use App\Domains\ECommerce\Drivers\YouCanDriver;
use App\Domains\ECommerce\Drivers\NoonDriver;
use App\Domains\ECommerce\Drivers\JumiaDriver;
use App\Domains\ECommerce\Drivers\AliexpressDriver;
use App\Domains\ECommerce\Drivers\AlibabaDriver;
use App\Domains\ECommerce\Drivers\ThawaniDriver;
use App\Domains\ECommerce\Drivers\FatoraDriver;
use App\Domains\ECommerce\Drivers\FaveDriver;
use App\Domains\ECommerce\Drivers\JarirDriver;
use App\Domains\ECommerce\Drivers\NamshiDriver;
use App\Domains\ECommerce\Drivers\CodNetworkDriver;
use App\Domains\ECommerce\Drivers\AvitoStoreDriver;
use App\Domains\ECommerce\Drivers\HmizateStoreDriver;
use App\Domains\ECommerce\Drivers\MytekStoreDriver;
use App\Domains\ECommerce\Exceptions\ECommerceException;
use Illuminate\Support\Facades\App;

/**
 * مصنع برامج تشغيل منصات التجارة الإلكترونية
 * ينشئ برنامج التشغيل المناسب لكل منصة
 */
class ECommercePlatformDriverFactory
{
    /**
     * خريطة المنصات وبرامج التشغيل المقترنة بها
     */
    protected static array $driverMap = [
        // المنصات العالمية
        'shopify' => ShopifyDriver::class,
        'woocommerce' => WooCommerceDriver::class,
        'magento' => MagentoDriver::class,
        'magento2' => MagentoDriver::class,
        'bigcommerce' => BigCommerceDriver::class,
        'amazon' => AmazonDriver::class,
        'ebay' => EbayDriver::class,
        'aliexpress' => YouCanDriver::class, // استخدام YouCan كبديل مؤقت
        'alibaba' => YouCanDriver::class, // استخدام YouCan كبديل مؤقت

        // المنصات العربية
        'salla' => SallaDriver::class,
        'zid' => ZidDriver::class,
        'youcan' => YouCanDriver::class,
        'noon' => NoonDriver::class,
        'jumia' => JumiaDriver::class,
        'thawani' => SallaDriver::class, // استخدام Salla كبديل مؤقت
        'fatora' => SallaDriver::class, // استخدام Salla كبديل مؤقت
        'fave' => SallaDriver::class, // استخدام Salla كبديل مؤقت
        'jarir' => SallaDriver::class, // استخدام Salla كبديل مؤقت
        'namshi' => NoonDriver::class, // استخدام Noon كبديل مؤقت

        // المنصات المغربية
        'cod_network' => YouCanDriver::class,
        'youcan_shop' => YouCanDriver::class,
        'dabashop' => YouCanDriver::class,
        'avito_store' => YouCanDriver::class,
        'hmizate_store' => YouCanDriver::class,
        'mytek_store' => YouCanDriver::class,
    ];

    /**
     * إنشاء برنامج تشغيل للمنصة
     */
    public static function create(ECommercePlatform $platform): ECommercePlatformInterface
    {
        $platformSlug = strtolower($platform->slug);

        if (!isset(static::$driverMap[$platformSlug])) {
            throw new ECommerceException(
                "Driver not found for platform: {$platform->name}",
                404,
                null,
                ['platform_slug' => $platformSlug, 'platform_name' => $platform->name],
                'DRIVER_NOT_FOUND'
            );
        }

        $driverClass = static::$driverMap[$platformSlug];

        if (!class_exists($driverClass)) {
            throw new ECommerceException(
                "Driver class not found: {$driverClass}",
                404,
                null,
                ['driver_class' => $driverClass, 'platform_slug' => $platformSlug],
                'DRIVER_CLASS_NOT_FOUND'
            );
        }

        $driver = App::make($driverClass);

        if (!$driver instanceof ECommercePlatformInterface) {
            throw new ECommerceException(
                "Driver must implement ECommercePlatformInterface: {$driverClass}",
                500,
                null,
                ['driver_class' => $driverClass],
                'INVALID_DRIVER_INTERFACE'
            );
        }

        return $driver;
    }

    /**
     * إنشاء برنامج تشغيل من التكامل
     */
    public static function createFromIntegration(ECommerceIntegration $integration): ECommercePlatformInterface
    {
        return static::create($integration->platform);
    }

    /**
     * إنشاء برنامج تشغيل من اسم المنصة
     */
    public static function createFromPlatformSlug(string $platformSlug): ECommercePlatformInterface
    {
        $platform = ECommercePlatform::where('slug', $platformSlug)->first();

        if (!$platform) {
            throw new ECommerceException(
                "Platform not found: {$platformSlug}",
                404,
                null,
                ['platform_slug' => $platformSlug],
                'PLATFORM_NOT_FOUND'
            );
        }

        return static::create($platform);
    }

    /**
     * التحقق من توفر برنامج تشغيل للمنصة
     */
    public static function hasDriver(string $platformSlug): bool
    {
        return isset(static::$driverMap[strtolower($platformSlug)]);
    }

    /**
     * الحصول على قائمة المنصات المدعومة
     */
    public static function getSupportedPlatforms(): array
    {
        return array_keys(static::$driverMap);
    }

    /**
     * الحصول على معلومات برنامج التشغيل
     */
    public static function getDriverInfo(string $platformSlug): array
    {
        $platformSlug = strtolower($platformSlug);

        if (!static::hasDriver($platformSlug)) {
            return [];
        }

        $driverClass = static::$driverMap[$platformSlug];

        try {
            $driver = App::make($driverClass);

            return [
                'platform_slug' => $platformSlug,
                'driver_class' => $driverClass,
                'api_version' => $driver->getApiVersion(),
                'supported_operations' => $driver->getSupportedOperations(),
                'supported_sync_types' => $driver->getSupportedSyncTypes(),
                'supported_event_types' => $driver->getSupportedEventTypes(),
                'supported_data_formats' => $driver->getSupportedDataFormats(),
                'required_fields' => $driver->getRequiredFields(),
                'optional_fields' => $driver->getOptionalFields(),
                'default_configuration' => $driver->getDefaultConfiguration(),
                'max_page_size' => $driver->getMaxPageSize(),
                'default_page_size' => $driver->getDefaultPageSize(),
                'max_requests_per_second' => $driver->getMaxRequestsPerSecond(),
                'max_requests_per_minute' => $driver->getMaxRequestsPerMinute(),
                'max_requests_per_hour' => $driver->getMaxRequestsPerHour(),
                'max_requests_per_day' => $driver->getMaxRequestsPerDay(),
            ];
        } catch (\Exception $e) {
            return [
                'platform_slug' => $platformSlug,
                'driver_class' => $driverClass,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * الحصول على معلومات جميع برامج التشغيل
     */
    public static function getAllDriversInfo(): array
    {
        $driversInfo = [];

        foreach (static::$driverMap as $platformSlug => $driverClass) {
            $driversInfo[$platformSlug] = static::getDriverInfo($platformSlug);
        }

        return $driversInfo;
    }

    /**
     * تسجيل برنامج تشغيل جديد
     */
    public static function registerDriver(string $platformSlug, string $driverClass): void
    {
        if (!class_exists($driverClass)) {
            throw new ECommerceException(
                "Driver class not found: {$driverClass}",
                404,
                null,
                ['driver_class' => $driverClass],
                'DRIVER_CLASS_NOT_FOUND'
            );
        }

        $driver = App::make($driverClass);

        if (!$driver instanceof ECommercePlatformInterface) {
            throw new ECommerceException(
                "Driver must implement ECommercePlatformInterface: {$driverClass}",
                500,
                null,
                ['driver_class' => $driverClass],
                'INVALID_DRIVER_INTERFACE'
            );
        }

        static::$driverMap[strtolower($platformSlug)] = $driverClass;
    }

    /**
     * إلغاء تسجيل برنامج تشغيل
     */
    public static function unregisterDriver(string $platformSlug): void
    {
        unset(static::$driverMap[strtolower($platformSlug)]);
    }

    /**
     * التحقق من صحة إعدادات التكامل للمنصة
     */
    public static function validateConfiguration(string $platformSlug, array $config): array
    {
        try {
            $driver = static::createFromPlatformSlug($platformSlug);
            return $driver->validateConfiguration($config);
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['general' => $e->getMessage()],
            ];
        }
    }

    /**
     * اختبار الاتصال مع المنصة
     */
    public static function testConnection(ECommerceIntegration $integration): array
    {
        try {
            $driver = static::createFromIntegration($integration);
            return $driver->testConnection($integration);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
        }
    }

    /**
     * الحصول على المنصات حسب المنطقة
     */
    public static function getPlatformsByRegion(string $region = 'global'): array
    {
        $platformsByRegion = [
            'global' => ['shopify', 'woocommerce', 'magento', 'magento2', 'bigcommerce', 'amazon', 'ebay', 'aliexpress', 'alibaba'],
            'middle_east' => ['salla', 'zid', 'noon', 'jumia', 'thawani', 'fatora', 'fave', 'jarir', 'namshi'],
            'saudi_arabia' => ['salla', 'zid', 'noon', 'thawani', 'fatora', 'jarir'],
            'uae' => ['noon', 'jumia', 'namshi'],
            'egypt' => ['jumia', 'noon'],
            'morocco' => ['youcan', 'youcan_shop', 'cod_network', 'dabashop', 'avito_store', 'hmizate_store', 'mytek_store'],
            'north_africa' => ['youcan', 'jumia', 'cod_network'],
            'gcc' => ['salla', 'zid', 'noon', 'thawani', 'fatora', 'fave'],
        ];

        return $platformsByRegion[$region] ?? [];
    }

    /**
     * الحصول على المنصات حسب نوع التجارة
     */
    public static function getPlatformsByType(string $type = 'b2c'): array
    {
        $platformsByType = [
            'b2c' => ['shopify', 'woocommerce', 'salla', 'zid', 'youcan', 'thawani', 'fatora'],
            'b2b' => ['magento', 'bigcommerce', 'alibaba'],
            'marketplace' => ['amazon', 'ebay', 'noon', 'jumia', 'aliexpress', 'namshi'],
            'multi_vendor' => ['magento', 'woocommerce', 'youcan'],
            'saas' => ['shopify', 'salla', 'zid', 'bigcommerce', 'youcan', 'noon'],
            'self_hosted' => ['woocommerce', 'magento'],
            'payment_gateway' => ['thawani', 'fatora', 'fave'],
            'enterprise' => ['magento', 'bigcommerce', 'jarir'],
        ];

        return $platformsByType[$type] ?? [];
    }

    /**
     * الحصول على المنصات المدعومة للعملة
     */
    public static function getPlatformsByCurrency(string $currency = 'USD'): array
    {
        $platformsByCurrency = [
            'USD' => ['shopify', 'woocommerce', 'magento', 'bigcommerce', 'amazon', 'ebay', 'aliexpress', 'alibaba'],
            'SAR' => ['salla', 'zid', 'noon', 'thawani', 'fatora', 'jarir'],
            'AED' => ['noon', 'jumia', 'namshi', 'fave'],
            'EGP' => ['jumia', 'noon'],
            'MAD' => ['youcan', 'youcan_shop', 'cod_network', 'dabashop', 'avito_store', 'hmizate_store', 'mytek_store'],
            'EUR' => ['shopify', 'woocommerce', 'magento', 'bigcommerce'],
            'GBP' => ['shopify', 'woocommerce', 'magento', 'bigcommerce'],
            'CNY' => ['alibaba', 'aliexpress'],
        ];

        return $platformsByCurrency[$currency] ?? [];
    }

    /**
     * الحصول على إحصائيات برامج التشغيل
     */
    public static function getDriverStats(): array
    {
        $totalDrivers = count(static::$driverMap);
        $globalPlatforms = count(static::getPlatformsByRegion('global'));
        $middleEastPlatforms = count(static::getPlatformsByRegion('middle_east'));
        $moroccanPlatforms = count(static::getPlatformsByRegion('morocco'));
        $gccPlatforms = count(static::getPlatformsByRegion('gcc'));

        return [
            'total_drivers' => $totalDrivers,
            'global_platforms' => $globalPlatforms,
            'middle_east_platforms' => $middleEastPlatforms,
            'moroccan_platforms' => $moroccanPlatforms,
            'gcc_platforms' => $gccPlatforms,
            'supported_platforms' => static::getSupportedPlatforms(),
            'coverage_percentage' => [
                'global' => round(($globalPlatforms / $totalDrivers) * 100, 2),
                'middle_east' => round(($middleEastPlatforms / $totalDrivers) * 100, 2),
                'morocco' => round(($moroccanPlatforms / $totalDrivers) * 100, 2),
                'gcc' => round(($gccPlatforms / $totalDrivers) * 100, 2),
            ],
            'by_type' => [
                'b2c' => count(static::getPlatformsByType('b2c')),
                'b2b' => count(static::getPlatformsByType('b2b')),
                'marketplace' => count(static::getPlatformsByType('marketplace')),
                'saas' => count(static::getPlatformsByType('saas')),
                'self_hosted' => count(static::getPlatformsByType('self_hosted')),
            ],
            'by_currency' => [
                'USD' => count(static::getPlatformsByCurrency('USD')),
                'SAR' => count(static::getPlatformsByCurrency('SAR')),
                'AED' => count(static::getPlatformsByCurrency('AED')),
                'MAD' => count(static::getPlatformsByCurrency('MAD')),
                'EGP' => count(static::getPlatformsByCurrency('EGP')),
            ],
        ];
    }
}
