<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DashboardWidget extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'component',
        'description',
        'category',
        'required_permissions',
        'default_size',
        'min_size',
        'max_size',
        'configuration_schema',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'required_permissions' => 'array',
        'default_size' => 'array',
        'min_size' => 'array',
        'max_size' => 'array',
        'configuration_schema' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * أنواع الودجات المتاحة
     */
    public const WIDGET_TYPES = [
        // ودجات المدير العام
        'kpi_overview' => 'نظرة عامة على مؤشرات الأداء',
        'revenue_chart' => 'مخطط الإيرادات',
        'profit_analysis' => 'تحليل الأرباح',
        'cash_flow' => 'التدفق النقدي',
        'project_status' => 'حالة المشاريع',
        'geographic_sales' => 'المبيعات الجغرافية',

        // ودجات المحاسب
        'financial_overview' => 'النظرة المالية العامة',
        'accounts_receivable' => 'الحسابات المدينة',
        'accounts_payable' => 'الحسابات الدائنة',
        'invoice_status' => 'حالة الفواتير',
        'tax_calendar' => 'تقويم الضرائب',
        'expense_tracking' => 'تتبع المصروفات',

        // ودجات مدير المشروع
        'project_overview' => 'نظرة عامة على المشاريع',
        'gantt_chart' => 'مخطط جانت',
        'task_progress' => 'تقدم المهام',
        'project_costs' => 'تكاليف المشروع',
        'team_workload' => 'عبء العمل للفريق',
        'milestone_tracker' => 'متتبع المعالم',

        // ودجات مشرف الدعم
        'ticket_overview' => 'نظرة عامة على التذاكر',
        'response_time' => 'وقت الاستجابة',
        'customer_satisfaction' => 'رضا العملاء',
        'ticket_heatmap' => 'خريطة حرارة التذاكر',
        'agent_performance' => 'أداء الوكلاء',

        // ودجات الموارد البشرية
        'employee_overview' => 'نظرة عامة على الموظفين',
        'attendance_chart' => 'مخطط الحضور',
        'leave_requests' => 'طلبات الإجازة',
        'performance_metrics' => 'مقاييس الأداء',
        'recruitment_pipeline' => 'خط أنابيب التوظيف',

        // ودجات عامة
        'welcome_widget' => 'ودجت الترحيب',
        'my_tasks' => 'مهامي',
        'recent_activity' => 'النشاط الحديث',
        'notifications' => 'الإشعارات',
        'calendar' => 'التقويم',
        'weather' => 'الطقس',
        'quick_actions' => 'الإجراءات السريعة',
        'search' => 'البحث',
        'time_tracking' => 'تتبع الوقت',
        'notes' => 'الملاحظات',
    ];

    /**
     * فئات الودجات
     */
    public const WIDGET_CATEGORIES = [
        'analytics' => 'التحليلات',
        'financial' => 'المالية',
        'projects' => 'المشاريع',
        'support' => 'الدعم الفني',
        'hr' => 'الموارد البشرية',
        'productivity' => 'الإنتاجية',
        'communication' => 'التواصل',
        'utilities' => 'الأدوات المساعدة',
    ];

    /**
     * جلب الودجات المتاحة للدور
     */
    public static function getAvailableForRole($role)
    {
        return static::where('is_active', true)
            ->where(function ($query) use ($role) {
                $query->whereJsonContains('required_permissions', $role)
                    ->orWhereJsonLength('required_permissions', 0)
                    ->orWhereNull('required_permissions');
            })
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * جلب الودجات حسب الفئة
     */
    public static function getByCategory($category)
    {
        return static::where('category', $category)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * التحقق من صلاحية المستخدم للودجت
     */
    public function canUserAccess($user)
    {
        if (empty($this->required_permissions)) {
            return true;
        }

        $userRole = $user->is_admin ? 'general_manager' : ($user->account_type ?? 'employee');

        return in_array($userRole, $this->required_permissions);
    }

    /**
     * جلب مخطط التكوين للودجت
     */
    public function getConfigurationSchema()
    {
        return $this->configuration_schema ?? [];
    }

    /**
     * التحقق من صحة تكوين الودجت
     */
    public function validateConfiguration($config)
    {
        $schema = $this->getConfigurationSchema();

        if (empty($schema)) {
            return true;
        }

        // يمكن إضافة منطق التحقق هنا
        return true;
    }
}
