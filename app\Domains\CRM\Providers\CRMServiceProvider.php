<?php

namespace App\Domains\CRM\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Domains\CRM\Services\AdvancedCRMService;
use App\Domains\CRM\Services\SalesAutomationService;
use App\Domains\CRM\Services\MarketingAutomationService;
use App\Domains\CRM\Services\CustomerSegmentationService;
use App\Domains\CRM\Services\CustomerAnalyticsService;
use App\Domains\CRM\Services\CRMIntegrationService;
use App\Domains\CRM\Services\CRMReportingService;
use App\Domains\CRM\Services\SalesForecastingService;

/**
 * CRM Domain Service Provider
 * مزود خدمات مجال إدارة العملاء
 */
class CRMServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل الخدمات الأساسية
        $this->app->singleton(AdvancedCRMService::class);
        $this->app->singleton(SalesAutomationService::class);
        $this->app->singleton(MarketingAutomationService::class);
        $this->app->singleton(CustomerSegmentationService::class);
        $this->app->singleton(CustomerAnalyticsService::class);
        $this->app->singleton(CRMIntegrationService::class);
        $this->app->singleton(CRMReportingService::class);
        $this->app->singleton(SalesForecastingService::class);

        // تسجيل التكوينات
        $this->mergeConfigFrom(
            config_path('crm.php'),
            'crm'
        );

        // تسجيل الواجهات والتنفيذات
        $this->registerContracts();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل المسارات
        $this->loadRoutes();

        // تحميل الـ Views
        $this->loadViews();

        // تحميل الـ Migrations
        $this->loadMigrations();

        // تحميل الترجمات
        $this->loadTranslations();

        // تسجيل السياسات
        $this->registerPolicies();

        // تسجيل نظام إدارة علاقات العملاء الذكي
        $this->registerIntelligentCRMSystem();

        // تسجيل نظام الذكاء الاصطناعي لتحليل العملاء
        $this->registerCustomerAIAnalytics();

        // تسجيل نظام التسويق الآلي المتقدم
        $this->registerAdvancedMarketingAutomation();

        // تسجيل خدمات التنبؤ بسلوك العملاء
        $this->registerCustomerBehaviorPrediction();

        // نشر الموارد المتقدمة لإدارة علاقات العملاء
        $this->publishAdvancedCRMAssets();
    }

    /**
     * تسجيل الواجهات والتنفيذات
     */
    protected function registerContracts(): void
    {
        $this->app->bind(
            \App\Domains\CRM\Contracts\CustomerRepositoryInterface::class,
            \App\Domains\CRM\Repositories\CustomerRepository::class
        );

        $this->app->bind(
            \App\Domains\CRM\Contracts\OpportunityRepositoryInterface::class,
            \App\Domains\CRM\Repositories\OpportunityRepository::class
        );

        $this->app->bind(
            \App\Domains\CRM\Contracts\CRMServiceInterface::class,
            AdvancedCRMService::class
        );
    }

    /**
     * تحميل المسارات
     */
    protected function loadRoutes(): void
    {
        if ($this->app->routesAreCached()) {
            return;
        }

        // API Routes
        Route::prefix('api/crm')
            ->middleware(['api', 'auth:sanctum'])
            ->namespace('App\Domains\CRM\Controllers')
            ->group(__DIR__ . '/../Routes/api.php');

        // Web Routes
        Route::prefix('crm')
            ->middleware(['web', 'auth'])
            ->namespace('App\Domains\CRM\Controllers')
            ->group(__DIR__ . '/../Routes/web.php');
    }

    /**
     * تحميل الـ Views
     */
    protected function loadViews(): void
    {
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'crm');
    }

    /**
     * تحميل الـ Migrations
     */
    protected function loadMigrations(): void
    {
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations');
    }

    /**
     * تحميل الترجمات
     */
    protected function loadTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'crm');
    }

    /**
     * تسجيل السياسات
     */
    protected function registerPolicies(): void
    {
        $policies = [
            \App\Domains\CRM\Models\Customer::class => \App\Domains\CRM\Policies\CustomerPolicy::class,
            \App\Domains\CRM\Models\Opportunity::class => \App\Domains\CRM\Policies\OpportunityPolicy::class,
            \App\Domains\CRM\Models\CustomerInteraction::class => \App\Domains\CRM\Policies\CustomerInteractionPolicy::class,
            \App\Domains\CRM\Models\MarketingCampaign::class => \App\Domains\CRM\Policies\MarketingCampaignPolicy::class,
        ];

        foreach ($policies as $model => $policy) {
            \Illuminate\Support\Facades\Gate::policy($model, $policy);
        }
    }

    /**
     * تسجيل الأحداث والمستمعين
     */
    protected function registerEvents(): void
    {
        $events = [
            \App\Domains\CRM\Events\CustomerCreated::class => [
                \App\Domains\CRM\Listeners\SendWelcomeEmail::class,
                \App\Domains\CRM\Listeners\CreateCustomerProfile::class,
            ],
            \App\Domains\CRM\Events\OpportunityWon::class => [
                \App\Domains\CRM\Listeners\CreateProject::class,
                \App\Domains\CRM\Listeners\SendCongratulationsEmail::class,
            ],
            \App\Domains\CRM\Events\CustomerInteractionCreated::class => [
                \App\Domains\CRM\Listeners\UpdateCustomerLastContact::class,
                \App\Domains\CRM\Listeners\AnalyzeSentiment::class,
            ],
        ];

        foreach ($events as $event => $listeners) {
            foreach ($listeners as $listener) {
                \Illuminate\Support\Facades\Event::listen($event, $listener);
            }
        }
    }



    /**
     * تسجيل نظام إدارة علاقات العملاء الذكي
     */
    protected function registerIntelligentCRMSystem(): void
    {
        // تسجيل محرك إدارة علاقات العملاء الذكي
        $this->app->singleton('crm.intelligent_engine', function ($app) {
            return new \App\Domains\CRM\Services\Intelligence\IntelligentCRMEngine(
                $app['db'],
                $app['cache.store'],
                $app['crm.ai_processor']
            );
        });

        // تسجيل خدمة إدارة دورة حياة العميل
        $this->app->singleton('crm.lifecycle_manager', function ($app) {
            return new \App\Domains\CRM\Services\Lifecycle\CustomerLifecycleManager(
                $app['crm.intelligent_engine'],
                $app['crm.behavior_analyzer'],
                $app['events']
            );
        });

        // تسجيل خدمة تجزئة العملاء الذكية
        $this->app->singleton('crm.smart_segmentation', function ($app) {
            return new \App\Domains\CRM\Services\Segmentation\IntelligentCustomerSegmentation(
                $app['crm.ml_model'],
                $app['crm.data_processor'],
                $app['cache.store']
            );
        });
    }

    /**
     * تسجيل نظام الذكاء الاصطناعي لتحليل العملاء
     */
    protected function registerCustomerAIAnalytics(): void
    {
        // تسجيل معالج الذكاء الاصطناعي للعملاء
        $this->app->singleton('crm.ai_processor', function ($app) {
            return new \App\Domains\CRM\Services\AI\CustomerAIProcessor(
                config('crm.ai_models'),
                $app['log']
            );
        });

        // تسجيل نموذج التعلم الآلي للعملاء
        $this->app->singleton('crm.ml_model', function ($app) {
            return new \App\Domains\CRM\Services\MachineLearning\CustomerPredictionModel(
                config('crm.ml_model_path'),
                $app['crm.ai_processor']
            );
        });

        // تسجيل محلل سلوك العملاء
        $this->app->singleton('crm.behavior_analyzer', function ($app) {
            return new \App\Domains\CRM\Services\Analytics\CustomerBehaviorAnalyzer(
                $app['db'],
                $app['crm.ai_processor'],
                $app['crm.interaction_tracker']
            );
        });

        // تسجيل متتبع التفاعلات
        $this->app->singleton('crm.interaction_tracker', function ($app) {
            return new \App\Domains\CRM\Services\Tracking\CustomerInteractionTracker(
                $app['db'],
                $app['events'],
                $app['cache.store']
            );
        });
    }

    /**
     * تسجيل نظام التسويق الآلي المتقدم
     */
    protected function registerAdvancedMarketingAutomation(): void
    {
        // تسجيل محرك التسويق الآلي
        $this->app->singleton('crm.automation_engine', function ($app) {
            return new \App\Domains\CRM\Services\Automation\AdvancedMarketingAutomationEngine(
                $app['crm.intelligent_engine'],
                $app['crm.campaign_optimizer'],
                $app['queue']
            );
        });

        // تسجيل محسن الحملات التسويقية
        $this->app->singleton('crm.campaign_optimizer', function ($app) {
            return new \App\Domains\CRM\Services\Optimization\CampaignOptimizer(
                $app['crm.ml_model'],
                $app['crm.performance_analyzer'],
                $app['cache.store']
            );
        });

        // تسجيل محلل أداء التسويق
        $this->app->singleton('crm.performance_analyzer', function ($app) {
            return new \App\Domains\CRM\Services\Analytics\MarketingPerformanceAnalyzer(
                $app['db'],
                $app['crm.ai_processor']
            );
        });
    }

    /**
     * تسجيل خدمات التنبؤ بسلوك العملاء
     */
    protected function registerCustomerBehaviorPrediction(): void
    {
        // تسجيل محرك التنبؤ بسلوك العملاء
        $this->app->singleton('crm.behavior_predictor', function ($app) {
            return new \App\Domains\CRM\Services\Prediction\CustomerBehaviorPredictor(
                $app['crm.ml_model'],
                $app['crm.behavior_analyzer'],
                $app['crm.data_processor']
            );
        });

        // تسجيل معالج البيانات المتقدم
        $this->app->singleton('crm.data_processor', function ($app) {
            return new \App\Domains\CRM\Services\DataProcessing\AdvancedCRMDataProcessor(
                $app['db'],
                $app['crm.ai_processor']
            );
        });

        // تسجيل خدمة التوصيات الشخصية
        $this->app->singleton('crm.personalization_engine', function ($app) {
            return new \App\Domains\CRM\Services\Personalization\PersonalizationEngine(
                $app['crm.behavior_predictor'],
                $app['crm.smart_segmentation'],
                $app['cache.store']
            );
        });
    }

    /**
     * نشر الموارد المتقدمة لإدارة علاقات العملاء
     */
    protected function publishAdvancedCRMAssets(): void
    {
        if ($this->app->runningInConsole()) {
            // نشر ملف التكوين
            $this->publishes([
                __DIR__ . '/../../../config/crm.php' => config_path('crm.php'),
            ], 'crm-config');

            // نشر الـ Views
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/crm'),
            ], 'crm-views');

            // نشر الترجمات
            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/crm'),
            ], 'crm-lang');

            // نشر الأصول
            $this->publishes([
                __DIR__ . '/../Resources/assets' => public_path('vendor/crm'),
            ], 'crm-assets');
        }
    }

    /**
     * الحصول على الخدمات المتقدمة لإدارة علاقات العملاء
     */
    public function provides(): array
    {
        return [
            // Intelligent CRM Core Services
            'crm.intelligent_engine',
            'crm.lifecycle_manager',
            'crm.smart_segmentation',

            // AI & Analytics Services
            'crm.ai_processor',
            'crm.ml_model',
            'crm.behavior_analyzer',
            'crm.interaction_tracker',

            // Marketing Automation
            'crm.automation_engine',
            'crm.campaign_optimizer',
            'crm.performance_analyzer',

            // Prediction & Personalization
            'crm.behavior_predictor',
            'crm.data_processor',
            'crm.personalization_engine',
        ];
    }
}
