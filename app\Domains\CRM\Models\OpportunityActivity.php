<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج نشاط الفرصة التجارية - Opportunity Activity
 * تسجيل جميع الأنشطة المرتبطة بالفرص التجارية
 */
class OpportunityActivity extends Model
{
    use HasFactory, HasUuid, HasFiles;

    protected $fillable = [
        'opportunity_id',
        'customer_id',
        'contact_id',
        'created_by',
        'type',
        'subject',
        'description',
        'notes',
        'outcome',
        'status',
        'priority',
        'duration_minutes',
        'scheduled_at',
        'occurred_at',
        'completed_at',
        'follow_up_required',
        'follow_up_date',
        'follow_up_notes',
        'location',
        'attendees',
        'meeting_url',
        'call_recording_url',
        'email_message_id',
        'sentiment',
        'score',
        'stage_impact',
        'probability_change',
        'value_impact',
        'tags',
        'custom_fields',
        'metadata',
    ];

    protected $casts = [
        'duration_minutes' => 'integer',
        'scheduled_at' => 'datetime',
        'occurred_at' => 'datetime',
        'completed_at' => 'datetime',
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'datetime',
        'attendees' => 'array',
        'score' => 'integer',
        'probability_change' => 'integer',
        'value_impact' => 'decimal:2',
        'tags' => 'array',
        'custom_fields' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع الأنشطة
     */
    const TYPES = [
        'call' => 'مكالمة',
        'email' => 'بريد إلكتروني',
        'meeting' => 'اجتماع',
        'demo' => 'عرض توضيحي',
        'presentation' => 'عرض تقديمي',
        'proposal' => 'عرض سعر',
        'negotiation' => 'مفاوضة',
        'contract_review' => 'مراجعة عقد',
        'follow_up' => 'متابعة',
        'research' => 'بحث',
        'qualification' => 'تأهيل',
        'discovery' => 'اكتشاف الاحتياجات',
        'objection_handling' => 'معالجة الاعتراضات',
        'closing' => 'إغلاق',
        'stage_change' => 'تغيير مرحلة',
        'note' => 'ملاحظة',
        'task' => 'مهمة',
        'reminder' => 'تذكير',
        'other' => 'أخرى',
    ];

    /**
     * نتائج الأنشطة
     */
    const OUTCOMES = [
        'successful' => 'ناجح',
        'partially_successful' => 'ناجح جزئياً',
        'unsuccessful' => 'غير ناجح',
        'no_response' => 'لا يوجد رد',
        'rescheduled' => 'تم إعادة الجدولة',
        'cancelled' => 'ملغي',
        'follow_up_needed' => 'يحتاج متابعة',
        'information_gathered' => 'تم جمع معلومات',
        'objection_raised' => 'تم إثارة اعتراض',
        'interest_confirmed' => 'تم تأكيد الاهتمام',
        'decision_pending' => 'قرار معلق',
        'budget_confirmed' => 'تم تأكيد الميزانية',
        'timeline_confirmed' => 'تم تأكيد الجدول الزمني',
        'authority_identified' => 'تم تحديد صاحب القرار',
        'competitor_mentioned' => 'تم ذكر منافس',
        'proposal_requested' => 'طلب عرض سعر',
        'contract_sent' => 'تم إرسال عقد',
        'deal_won' => 'تم كسب الصفقة',
        'deal_lost' => 'تم فقدان الصفقة',
    ];

    /**
     * حالات النشاط
     */
    const STATUSES = [
        'planned' => 'مخطط',
        'scheduled' => 'مجدول',
        'in_progress' => 'قيد التنفيذ',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي',
        'postponed' => 'مؤجل',
        'no_show' => 'لم يحضر',
    ];

    /**
     * مستويات الأولوية
     */
    const PRIORITIES = [
        'low' => 'منخفضة',
        'medium' => 'متوسطة',
        'high' => 'عالية',
        'urgent' => 'عاجلة',
    ];

    /**
     * مستويات المشاعر
     */
    const SENTIMENTS = [
        'very_positive' => 'إيجابي جداً',
        'positive' => 'إيجابي',
        'neutral' => 'محايد',
        'negative' => 'سلبي',
        'very_negative' => 'سلبي جداً',
    ];

    /**
     * تأثير على المرحلة
     */
    const STAGE_IMPACTS = [
        'advance' => 'تقدم للمرحلة التالية',
        'maintain' => 'البقاء في نفس المرحلة',
        'regress' => 'العودة للمرحلة السابقة',
        'neutral' => 'لا تأثير',
    ];

    /**
     * العلاقة مع الفرصة التجارية
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع جهة الاتصال
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * العلاقة مع منشئ النشاط
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية النتيجة
     */
    public function getOutcomeLabelAttribute(): string
    {
        return self::OUTCOMES[$this->outcome] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الأولوية
     */
    public function getPriorityLabelAttribute(): string
    {
        return self::PRIORITIES[$this->priority] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية المشاعر
     */
    public function getSentimentLabelAttribute(): string
    {
        return self::SENTIMENTS[$this->sentiment] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية تأثير المرحلة
     */
    public function getStageImpactLabelAttribute(): string
    {
        return self::STAGE_IMPACTS[$this->stage_impact] ?? 'غير محدد';
    }

    /**
     * الحصول على لون النوع
     */
    public function getTypeColorAttribute(): string
    {
        return match ($this->type) {
            'call' => '#28a745',
            'email' => '#007bff',
            'meeting' => '#6f42c1',
            'demo' => '#20c997',
            'presentation' => '#fd7e14',
            'proposal' => '#e83e8c',
            'negotiation' => '#ffc107',
            'contract_review' => '#6c757d',
            'follow_up' => '#17a2b8',
            'closing' => '#28a745',
            'stage_change' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون المشاعر
     */
    public function getSentimentColorAttribute(): string
    {
        return match ($this->sentiment) {
            'very_positive' => '#28a745',
            'positive' => '#20c997',
            'neutral' => '#6c757d',
            'negative' => '#fd7e14',
            'very_negative' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على أيقونة النوع
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'call' => 'fas fa-phone',
            'email' => 'fas fa-envelope',
            'meeting' => 'fas fa-users',
            'demo' => 'fas fa-desktop',
            'presentation' => 'fas fa-presentation',
            'proposal' => 'fas fa-file-contract',
            'negotiation' => 'fas fa-handshake',
            'contract_review' => 'fas fa-file-signature',
            'follow_up' => 'fas fa-redo',
            'research' => 'fas fa-search',
            'qualification' => 'fas fa-check-circle',
            'discovery' => 'fas fa-lightbulb',
            'objection_handling' => 'fas fa-shield-alt',
            'closing' => 'fas fa-trophy',
            'stage_change' => 'fas fa-arrow-right',
            'note' => 'fas fa-sticky-note',
            'task' => 'fas fa-tasks',
            'reminder' => 'fas fa-bell',
            default => 'fas fa-circle',
        };
    }

    /**
     * التحقق من كون النشاط مكتمل
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من كون النشاط مجدول
     */
    public function getIsScheduledAttribute(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * التحقق من كون النشاط متأخر
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->scheduled_at && 
               $this->scheduled_at->isPast() && 
               !$this->is_completed;
    }

    /**
     * التحقق من الحاجة لمتابعة
     */
    public function getNeedsFollowUpAttribute(): bool
    {
        return $this->follow_up_required && 
               $this->follow_up_date && 
               $this->follow_up_date->isPast();
    }

    /**
     * الحصول على المدة المنسقة
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'غير محدد';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return "{$hours} ساعة و {$minutes} دقيقة";
        }

        return "{$minutes} دقيقة";
    }

    /**
     * تحديد ما إذا كان النشاط إيجابي
     */
    public function getIsPositiveAttribute(): bool
    {
        return in_array($this->sentiment, ['positive', 'very_positive']) ||
               in_array($this->outcome, ['successful', 'partially_successful', 'interest_confirmed']);
    }

    /**
     * تحديد ما إذا كان النشاط سلبي
     */
    public function getIsNegativeAttribute(): bool
    {
        return in_array($this->sentiment, ['negative', 'very_negative']) ||
               in_array($this->outcome, ['unsuccessful', 'deal_lost', 'objection_raised']);
    }

    /**
     * الحصول على تأثير النشاط على الفرصة
     */
    public function getOpportunityImpactAttribute(): array
    {
        return [
            'probability_change' => $this->probability_change,
            'value_impact' => $this->value_impact,
            'stage_impact' => $this->stage_impact,
            'score_impact' => $this->score,
        ];
    }

    /**
     * تحديث حالة النشاط
     */
    public function markAsCompleted(array $data = []): bool
    {
        $updated = $this->update(array_merge([
            'status' => 'completed',
            'completed_at' => now(),
            'occurred_at' => $this->occurred_at ?? now(),
        ], $data));

        if ($updated) {
            // تحديث إحصائيات الفرصة
            $this->opportunity->updateInteractionStats($this->type);
            
            // تطبيق تأثير النشاط على الفرصة
            $this->applyImpactToOpportunity();
        }

        return $updated;
    }

    /**
     * تطبيق تأثير النشاط على الفرصة
     */
    protected function applyImpactToOpportunity(): void
    {
        $opportunity = $this->opportunity;

        // تحديث الاحتمالية
        if ($this->probability_change) {
            $newProbability = max(0, min(100, $opportunity->probability + $this->probability_change));
            $opportunity->update(['probability' => $newProbability]);
        }

        // تحديث القيمة
        if ($this->value_impact) {
            $newValue = max(0, $opportunity->value + $this->value_impact);
            $opportunity->update(['value' => $newValue]);
        }

        // تحديث المرحلة حسب التأثير
        if ($this->stage_impact === 'advance') {
            $this->advanceOpportunityStage();
        } elseif ($this->stage_impact === 'regress') {
            $this->regressOpportunityStage();
        }

        // تحديث النقاط
        $opportunity->updateScore();
    }

    /**
     * تقدم الفرصة للمرحلة التالية
     */
    protected function advanceOpportunityStage(): void
    {
        $opportunity = $this->opportunity;
        $currentStage = $opportunity->stage;

        $stageProgression = [
            'lead' => 'qualified',
            'qualified' => 'proposal',
            'proposal' => 'negotiation',
            'negotiation' => 'contract',
            'contract' => 'won',
        ];

        if (isset($stageProgression[$currentStage])) {
            $opportunity->updateStage($stageProgression[$currentStage], "تقدم تلقائي بناءً على النشاط: {$this->type}");
        }
    }

    /**
     * إرجاع الفرصة للمرحلة السابقة
     */
    protected function regressOpportunityStage(): void
    {
        $opportunity = $this->opportunity;
        $currentStage = $opportunity->stage;

        $stageRegression = [
            'won' => 'contract',
            'contract' => 'negotiation',
            'negotiation' => 'proposal',
            'proposal' => 'qualified',
            'qualified' => 'lead',
        ];

        if (isset($stageRegression[$currentStage])) {
            $opportunity->updateStage($stageRegression[$currentStage], "تراجع بناءً على النشاط: {$this->type}");
        }
    }

    /**
     * جدولة متابعة
     */
    public function scheduleFollowUp(\DateTime $date, string $notes = null): bool
    {
        return $this->update([
            'follow_up_required' => true,
            'follow_up_date' => $date,
            'follow_up_notes' => $notes,
        ]);
    }

    /**
     * فلترة الأنشطة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة الأنشطة المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * فلترة الأنشطة المجدولة
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * فلترة الأنشطة المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('scheduled_at', '<', now())
                    ->where('status', '!=', 'completed');
    }

    /**
     * فلترة الأنشطة التي تحتاج متابعة
     */
    public function scopeNeedsFollowUp($query)
    {
        return $query->where('follow_up_required', true)
                    ->where('follow_up_date', '<=', now());
    }

    /**
     * فلترة الأنشطة الإيجابية
     */
    public function scopePositive($query)
    {
        return $query->whereIn('sentiment', ['positive', 'very_positive'])
                    ->orWhereIn('outcome', ['successful', 'partially_successful', 'interest_confirmed']);
    }

    /**
     * فلترة الأنشطة السلبية
     */
    public function scopeNegative($query)
    {
        return $query->whereIn('sentiment', ['negative', 'very_negative'])
                    ->orWhereIn('outcome', ['unsuccessful', 'deal_lost', 'objection_raised']);
    }

    /**
     * فلترة حسب فترة زمنية
     */
    public function scopeBetweenDates($query, \DateTime $from, \DateTime $to)
    {
        return $query->whereBetween('occurred_at', [$from, $to]);
    }

    /**
     * البحث في الأنشطة
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('notes', 'LIKE', "%{$search}%");
        });
    }
}
