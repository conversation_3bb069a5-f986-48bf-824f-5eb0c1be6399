<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج سير عمل الموافقة - Approval Workflow
 */
class ApprovalWorkflow extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'type',
        'project_id',
        'is_active',
        'auto_start',
        'parallel_approval',
        'escalation_enabled',
        'escalation_hours',
        'settings',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'auto_start' => 'boolean',
        'parallel_approval' => 'boolean',
        'escalation_enabled' => 'boolean',
        'escalation_hours' => 'integer',
        'settings' => 'array',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function steps(): HasMany
    {
        return $this->hasMany(WorkflowStep::class, 'workflow_id');
    }

    public function approvals(): HasMany
    {
        return $this->hasMany(ProjectApproval::class, 'workflow_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }
}
