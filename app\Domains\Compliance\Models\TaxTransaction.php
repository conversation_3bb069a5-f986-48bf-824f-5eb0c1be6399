<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Models\Company;

/**
 * نموذج المعاملات الضريبية المتقدم
 * يدير جميع المعاملات الضريبية مع التتبع الكامل والامتثال
 */
class TaxTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_id',
        'company_id',
        'user_id',
        'tax_configuration_id',
        'country_id',
        'related_model_type',
        'related_model_id',
        'transaction_type',
        'transaction_date',
        'due_date',
        'period_start',
        'period_end',
        'base_amount',
        'taxable_amount',
        'exempt_amount',
        'tax_rate',
        'effective_rate',
        'tax_amount',
        'penalty_amount',
        'interest_amount',
        'total_amount',
        'paid_amount',
        'outstanding_amount',
        'currency',
        'exchange_rate',
        'base_currency_amount',
        'status',
        'payment_status',
        'filing_status',
        'submission_reference',
        'government_reference',
        'calculation_details',
        'exemption_details',
        'deduction_details',
        'supporting_documents',
        'audit_trail',
        'risk_assessment',
        'compliance_notes',
        'internal_notes',
        'submitted_at',
        'approved_at',
        'paid_at',
        'processed_by',
        'approved_by',
        'metadata',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'due_date' => 'date',
        'period_start' => 'date',
        'period_end' => 'date',
        'base_amount' => 'decimal:4',
        'taxable_amount' => 'decimal:4',
        'exempt_amount' => 'decimal:4',
        'tax_rate' => 'decimal:4',
        'effective_rate' => 'decimal:4',
        'tax_amount' => 'decimal:4',
        'penalty_amount' => 'decimal:4',
        'interest_amount' => 'decimal:4',
        'total_amount' => 'decimal:4',
        'paid_amount' => 'decimal:4',
        'outstanding_amount' => 'decimal:4',
        'exchange_rate' => 'decimal:6',
        'base_currency_amount' => 'decimal:4',
        'calculation_details' => 'array',
        'exemption_details' => 'array',
        'deduction_details' => 'array',
        'supporting_documents' => 'array',
        'audit_trail' => 'array',
        'risk_assessment' => 'array',
        'compliance_notes' => 'array',
        'metadata' => 'array',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * أنواع المعاملات الضريبية
     */
    const TRANSACTION_TYPES = [
        'sales_tax' => 'ضريبة المبيعات',
        'purchase_tax' => 'ضريبة المشتريات',
        'vat_output' => 'ضريبة القيمة المضافة - مخرجات',
        'vat_input' => 'ضريبة القيمة المضافة - مدخلات',
        'corporate_tax' => 'ضريبة الشركات',
        'income_tax' => 'ضريبة الدخل',
        'zakat' => 'الزكاة',
        'excise_tax' => 'ضريبة السلع الانتقائية',
        'withholding_tax' => 'ضريبة الاستقطاع',
        'customs_duty' => 'الرسوم الجمركية',
        'municipal_tax' => 'الضريبة البلدية',
        'property_tax' => 'ضريبة العقارات',
        'stamp_duty' => 'رسم الطابع',
        'transfer_tax' => 'ضريبة التحويل',
        'capital_gains_tax' => 'ضريبة أرباح رؤوس الأموال',
    ];

    /**
     * حالات المعاملة
     */
    const STATUSES = [
        'draft' => 'مسودة',
        'calculated' => 'محسوبة',
        'pending_review' => 'قيد المراجعة',
        'approved' => 'معتمدة',
        'submitted' => 'مقدمة',
        'accepted' => 'مقبولة',
        'rejected' => 'مرفوضة',
        'under_audit' => 'تحت المراجعة',
        'disputed' => 'متنازع عليها',
        'settled' => 'مسددة',
        'cancelled' => 'ملغية',
    ];

    /**
     * حالات الدفع
     */
    const PAYMENT_STATUSES = [
        'unpaid' => 'غير مدفوعة',
        'partially_paid' => 'مدفوعة جزئياً',
        'paid' => 'مدفوعة',
        'overpaid' => 'مدفوعة زائد',
        'refunded' => 'مستردة',
        'written_off' => 'مشطوبة',
    ];

    /**
     * حالات التقديم
     */
    const FILING_STATUSES = [
        'not_filed' => 'غير مقدمة',
        'pending_filing' => 'قيد التقديم',
        'filed' => 'مقدمة',
        'acknowledged' => 'مستلمة',
        'processed' => 'معالجة',
        'amended' => 'معدلة',
        'late_filed' => 'مقدمة متأخرة',
    ];

    /**
     * مستويات المخاطر
     */
    const RISK_LEVELS = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع تكوين الضريبة
     */
    public function taxConfiguration(): BelongsTo
    {
        return $this->belongsTo(TaxConfiguration::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع النموذج المرتبط (polymorphic)
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(TaxPayment::class);
    }

    /**
     * العلاقة مع التعديلات
     */
    public function adjustments(): HasMany
    {
        return $this->hasMany(TaxAdjustment::class);
    }

    /**
     * العلاقة مع المراجعات
     */
    public function audits(): HasMany
    {
        return $this->hasMany(TaxAudit::class);
    }

    /**
     * حساب الضريبة بناءً على التكوين
     */
    public function calculateTax(): array
    {
        $taxConfig = $this->taxConfiguration;
        if (!$taxConfig) {
            throw new \Exception('تكوين الضريبة غير موجود');
        }

        $context = $this->buildCalculationContext();
        $calculation = $taxConfig->calculateTax($this->base_amount, $context);

        // تحديث المعاملة بنتائج الحساب
        $this->update([
            'taxable_amount' => $calculation['base_amount'] - ($calculation['deductions_applied']['total_deduction'] ?? 0),
            'exempt_amount' => $calculation['exemptions_applied'] ? $this->base_amount : 0,
            'tax_rate' => $calculation['effective_rate'],
            'effective_rate' => $calculation['effective_rate'],
            'tax_amount' => $calculation['tax_amount'],
            'total_amount' => $this->base_amount + $calculation['tax_amount'],
            'outstanding_amount' => $this->base_amount + $calculation['tax_amount'],
            'calculation_details' => $calculation,
            'status' => 'calculated',
        ]);

        return $calculation;
    }

    /**
     * بناء سياق الحساب
     */
    protected function buildCalculationContext(): array
    {
        return [
            'transaction_type' => $this->transaction_type,
            'transaction_date' => $this->transaction_date,
            'company_id' => $this->company_id,
            'country_code' => $this->country->code,
            'currency' => $this->currency,
            'period_start' => $this->period_start,
            'period_end' => $this->period_end,
            'metadata' => $this->metadata ?? [],
        ];
    }

    /**
     * حساب الغرامات والفوائد
     */
    public function calculatePenaltiesAndInterest(): array
    {
        if (!$this->due_date || $this->due_date->isFuture()) {
            return [
                'penalty_amount' => 0,
                'interest_amount' => 0,
                'total_additional' => 0,
                'days_late' => 0,
            ];
        }

        $taxConfig = $this->taxConfiguration;
        $calculation = $taxConfig->calculatePenaltyAndInterest($this->tax_amount, $this->due_date);

        // تحديث المعاملة
        $this->update([
            'penalty_amount' => $calculation['penalty'],
            'interest_amount' => $calculation['interest'],
            'total_amount' => $this->base_amount + $this->tax_amount + $calculation['penalty'] + $calculation['interest'],
            'outstanding_amount' => $this->total_amount - $this->paid_amount,
        ]);

        return $calculation;
    }

    /**
     * تقديم المعاملة للسلطات
     */
    public function submitToAuthority(): array
    {
        if ($this->status !== 'approved') {
            throw new \Exception('يجب اعتماد المعاملة قبل التقديم');
        }

        $submissionData = $this->prepareSubmissionData();
        
        // محاكاة إرسال للسلطة المختصة
        $response = $this->sendToGovernmentAPI($submissionData);

        if ($response['success']) {
            $this->update([
                'status' => 'submitted',
                'filing_status' => 'filed',
                'submission_reference' => $response['reference'],
                'government_reference' => $response['government_reference'] ?? null,
                'submitted_at' => now(),
            ]);

            // تسجيل في سجل المراجعة
            $this->logAuditTrail('submitted', 'تم تقديم المعاملة للسلطة المختصة', $response);
        }

        return $response;
    }

    /**
     * تحضير بيانات التقديم
     */
    protected function prepareSubmissionData(): array
    {
        return [
            'transaction_id' => $this->transaction_id,
            'company_tax_number' => $this->company->tax_number,
            'transaction_type' => $this->transaction_type,
            'transaction_date' => $this->transaction_date->format('Y-m-d'),
            'period_start' => $this->period_start->format('Y-m-d'),
            'period_end' => $this->period_end->format('Y-m-d'),
            'base_amount' => $this->base_amount,
            'taxable_amount' => $this->taxable_amount,
            'tax_rate' => $this->tax_rate,
            'tax_amount' => $this->tax_amount,
            'currency' => $this->currency,
            'supporting_documents' => $this->supporting_documents,
            'calculation_details' => $this->calculation_details,
        ];
    }

    /**
     * إرسال للـ API الحكومي
     */
    protected function sendToGovernmentAPI(array $data): array
    {
        $country = $this->country;
        $integrations = $country->api_integrations ?? [];
        
        $taxAuthority = match ($country->code) {
            'MA' => 'DGI',
            'SA' => 'ZATCA',
            'AE' => 'FTA',
            'EG' => 'ETA',
            default => 'TAX_AUTHORITY',
        };

        if (!isset($integrations[$taxAuthority])) {
            throw new \Exception("تكامل {$taxAuthority} غير متوفر");
        }

        // محاكاة استجابة API
        return [
            'success' => true,
            'reference' => 'TXN_' . strtoupper(uniqid()),
            'government_reference' => $taxAuthority . '_' . strtoupper(uniqid()),
            'status' => 'accepted',
            'message' => 'تم استلام المعاملة بنجاح',
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * معالجة الدفع
     */
    public function processPayment(float $amount, array $paymentData = []): TaxPayment
    {
        $payment = TaxPayment::create([
            'tax_transaction_id' => $this->id,
            'payment_reference' => 'PAY_' . strtoupper(uniqid()),
            'amount' => $amount,
            'currency' => $this->currency,
            'payment_method' => $paymentData['method'] ?? 'bank_transfer',
            'payment_date' => $paymentData['date'] ?? now(),
            'bank_reference' => $paymentData['bank_reference'] ?? null,
            'status' => 'completed',
            'metadata' => $paymentData,
        ]);

        // تحديث المبلغ المدفوع
        $this->increment('paid_amount', $amount);
        $this->decrement('outstanding_amount', $amount);

        // تحديث حالة الدفع
        $this->updatePaymentStatus();

        // تسجيل في سجل المراجعة
        $this->logAuditTrail('payment_processed', "تم معالجة دفعة بمبلغ {$amount}", $paymentData);

        return $payment;
    }

    /**
     * تحديث حالة الدفع
     */
    protected function updatePaymentStatus(): void
    {
        $paymentStatus = match (true) {
            $this->paid_amount <= 0 => 'unpaid',
            $this->paid_amount >= $this->total_amount => 'paid',
            $this->paid_amount > $this->total_amount => 'overpaid',
            default => 'partially_paid',
        };

        $this->update(['payment_status' => $paymentStatus]);

        if ($paymentStatus === 'paid') {
            $this->update([
                'status' => 'settled',
                'paid_at' => now(),
            ]);
        }
    }

    /**
     * تقييم المخاطر
     */
    public function assessRisk(): array
    {
        $riskFactors = [];
        $riskScore = 0;

        // عوامل المخاطر المختلفة
        $factors = [
            'amount_threshold' => $this->assessAmountRisk(),
            'timing_risk' => $this->assessTimingRisk(),
            'compliance_history' => $this->assessComplianceHistory(),
            'documentation_completeness' => $this->assessDocumentationRisk(),
            'calculation_complexity' => $this->assessCalculationComplexity(),
        ];

        foreach ($factors as $factor => $assessment) {
            $riskFactors[$factor] = $assessment;
            $riskScore += $assessment['score'];
        }

        $averageScore = count($factors) > 0 ? $riskScore / count($factors) : 0;
        $riskLevel = $this->determineRiskLevel($averageScore);

        $riskAssessment = [
            'overall_score' => $averageScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'recommendations' => $this->generateRiskRecommendations($riskFactors),
            'assessed_at' => now(),
        ];

        $this->update(['risk_assessment' => $riskAssessment]);

        return $riskAssessment;
    }

    /**
     * تقييم مخاطر المبلغ
     */
    protected function assessAmountRisk(): array
    {
        $thresholds = $this->taxConfiguration->special_rules['risk_thresholds'] ?? [];
        $highThreshold = $thresholds['high_amount'] ?? 100000;
        $mediumThreshold = $thresholds['medium_amount'] ?? 50000;

        if ($this->tax_amount >= $highThreshold) {
            return ['score' => 80, 'level' => 'high', 'reason' => 'مبلغ ضريبة عالي'];
        } elseif ($this->tax_amount >= $mediumThreshold) {
            return ['score' => 50, 'level' => 'medium', 'reason' => 'مبلغ ضريبة متوسط'];
        } else {
            return ['score' => 20, 'level' => 'low', 'reason' => 'مبلغ ضريبة منخفض'];
        }
    }

    /**
     * تقييم مخاطر التوقيت
     */
    protected function assessTimingRisk(): array
    {
        if ($this->due_date && $this->due_date->isPast()) {
            $daysLate = $this->due_date->diffInDays(now());
            if ($daysLate > 30) {
                return ['score' => 90, 'level' => 'critical', 'reason' => "متأخر {$daysLate} يوم"];
            } elseif ($daysLate > 7) {
                return ['score' => 60, 'level' => 'high', 'reason' => "متأخر {$daysLate} يوم"];
            } else {
                return ['score' => 30, 'level' => 'medium', 'reason' => "متأخر {$daysLate} يوم"];
            }
        }

        return ['score' => 10, 'level' => 'low', 'reason' => 'في الوقت المحدد'];
    }

    /**
     * تسجيل سجل المراجعة
     */
    public function logAuditTrail(string $action, string $description, array $data = []): void
    {
        $auditTrail = $this->audit_trail ?? [];
        
        $auditTrail[] = [
            'action' => $action,
            'description' => $description,
            'user_id' => auth()->id(),
            'user_name' => auth()->user()?->name,
            'timestamp' => now()->toISOString(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $data,
        ];

        $this->update(['audit_trail' => $auditTrail]);
    }

    /**
     * Scopes للاستعلامات المتقدمة
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    public function scopeByTaxType($query, string $taxType)
    {
        return $query->whereHas('taxConfiguration', fn($q) => $q->where('tax_type', $taxType));
    }

    public function scopeByPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('payment_status', '!=', 'paid');
    }

    public function scopeHighRisk($query)
    {
        return $query->whereJsonPath('risk_assessment->risk_level', 'high')
                    ->orWhereJsonPath('risk_assessment->risk_level', 'critical');
    }

    public function scopePendingSubmission($query)
    {
        return $query->where('status', 'approved')
                    ->where('filing_status', 'not_filed');
    }

    // طرق مساعدة إضافية
    protected function assessComplianceHistory(): array
    {
        // منطق تقييم تاريخ الامتثال
        return ['score' => 20, 'level' => 'low', 'reason' => 'تاريخ امتثال جيد'];
    }

    protected function assessDocumentationRisk(): array
    {
        $documentsCount = count($this->supporting_documents ?? []);
        if ($documentsCount < 2) {
            return ['score' => 70, 'level' => 'high', 'reason' => 'وثائق داعمة ناقصة'];
        }
        return ['score' => 15, 'level' => 'low', 'reason' => 'وثائق داعمة كاملة'];
    }

    protected function assessCalculationComplexity(): array
    {
        $complexity = $this->calculation_details['method_used'] ?? 'percentage';
        return match ($complexity) {
            'progressive' => ['score' => 60, 'level' => 'medium', 'reason' => 'حساب تدريجي معقد'],
            'tiered' => ['score' => 50, 'level' => 'medium', 'reason' => 'حساب متدرج'],
            default => ['score' => 20, 'level' => 'low', 'reason' => 'حساب بسيط'],
        };
    }

    protected function determineRiskLevel(float $score): string
    {
        return match (true) {
            $score >= 80 => 'critical',
            $score >= 60 => 'high',
            $score >= 40 => 'medium',
            default => 'low',
        };
    }

    protected function generateRiskRecommendations(array $riskFactors): array
    {
        $recommendations = [];
        
        foreach ($riskFactors as $factor => $assessment) {
            if ($assessment['score'] >= 60) {
                $recommendations[] = match ($factor) {
                    'amount_threshold' => 'مراجعة إضافية للمبالغ الكبيرة',
                    'timing_risk' => 'تسريع عملية التقديم',
                    'compliance_history' => 'مراجعة تاريخ الامتثال',
                    'documentation_completeness' => 'استكمال الوثائق المطلوبة',
                    'calculation_complexity' => 'مراجعة دقة الحسابات',
                    default => 'مراجعة عامة مطلوبة',
                };
            }
        }
        
        return $recommendations;
    }
}
