<?php

namespace App\Domains\Integration\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\ApiEndpoint;
use App\Domains\Integration\Models\ApiKey;
use App\Domains\Integration\Services\ApiGatewayService;
use App\Domains\Integration\Services\LoadBalancer\AdvancedLoadBalancer;
use App\Domains\Integration\Services\CircuitBreaker\HystrixCircuitBreaker;
use App\Domains\Integration\Services\RateLimiter\DistributedRateLimiter;
use App\Domains\Integration\Services\Security\AdvancedSecurityManager;
use App\Domains\Integration\Services\Monitoring\RealTimeMonitor;
use App\Domains\Integration\Services\Analytics\AdvancedAnalytics;
use App\Domains\Integration\Services\Deployment\BlueGreenDeployment;
use App\Domains\Integration\Services\Deployment\CanaryDeployment;
use App\Domains\Integration\Services\AutoScaling\AutoScalingManager;
use App\Domains\Integration\Resources\ApiGatewayResource;
use App\Domains\Integration\Resources\ApiGatewayDetailResource;
use App\Domains\Integration\Requests\CreateApiGatewayRequest;
use App\Domains\Integration\Requests\UpdateApiGatewayRequest;
use App\Domains\Integration\Requests\DeploymentRequest;
use App\Domains\Integration\Requests\ScalingRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Enterprise-Grade API Gateway Controller
 *
 * Features:
 * - Advanced gateway management with microservices support
 * - Blue-green and canary deployment strategies
 * - Auto-scaling based on traffic patterns
 * - Real-time monitoring and analytics
 * - Circuit breaker management
 * - Load balancer configuration
 * - Security policy management
 * - Performance optimization
 * - Multi-tenant isolation
 * - Compliance reporting
 */
class ApiGatewayController extends Controller
{
    public function __construct(
        protected ApiGatewayService $gatewayService,
        protected AdvancedLoadBalancer $loadBalancer,
        protected HystrixCircuitBreaker $circuitBreaker,
        protected DistributedRateLimiter $rateLimiter,
        protected AdvancedSecurityManager $securityManager,
        protected RealTimeMonitor $monitor,
        protected AdvancedAnalytics $analytics,
        protected BlueGreenDeployment $blueGreenDeployment,
        protected CanaryDeployment $canaryDeployment,
        protected AutoScalingManager $autoScaling
    ) {
        $this->middleware('auth:sanctum');
        $this->middleware('throttle:api')->except(['processRequest']);
        $this->middleware('permission:manage-api-gateways')->except(['index', 'show', 'processRequest']);
    }

    /**
     * List API gateways with advanced filtering and analytics
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', ApiGateway::class);

        $query = ApiGateway::with([
            'endpoints' => fn($q) => $q->select(['id', 'api_gateway_id', 'path', 'method', 'status']),
            'apiKeys' => fn($q) => $q->select(['id', 'api_gateway_id', 'name', 'status']),
        ]);

        // Advanced filtering
        $this->applyAdvancedFilters($query, $request);

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $gateways = $query->paginate($request->get('per_page', 15));

        // Enrich with real-time metrics
        $enrichedGateways = $gateways->getCollection()->map(function ($gateway) {
            $realtimeMetrics = $this->monitor->getDashboardData($gateway->gateway_id, '1h');
            $gateway->real_time_metrics = $realtimeMetrics['overview'];
            $gateway->health_score = $realtimeMetrics['health_score'];
            return $gateway;
        });

        $gateways->setCollection($enrichedGateways);

        return response()->json([
            'success' => true,
            'data' => ApiGatewayResource::collection($gateways),
            'meta' => [
                'total' => $gateways->total(),
                'per_page' => $gateways->perPage(),
                'current_page' => $gateways->currentPage(),
                'last_page' => $gateways->lastPage(),
                'filters_applied' => $this->getAppliedFilters($request),
                'available_filters' => $this->getAvailableFilters(),
            ],
            'analytics' => [
                'total_gateways' => ApiGateway::count(),
                'active_gateways' => ApiGateway::where('is_active', true)->count(),
                'healthy_gateways' => ApiGateway::where('health_status', 'healthy')->count(),
                'average_health_score' => ApiGateway::avg('uptime_percentage'),
            ],
        ]);
    }

    /**
     * Show detailed gateway information with real-time metrics
     */
    public function show(string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::with([
            'endpoints',
            'apiKeys',
            'deployments' => fn($q) => $q->latest()->limit(5),
            'scalingEvents' => fn($q) => $q->latest()->limit(10),
            'healthChecks' => fn($q) => $q->latest()->limit(5),
            'securityIncidents' => fn($q) => $q->where('status', '!=', 'resolved')->latest(),
        ])->findOrFail($gatewayId);

        $this->authorize('view', $gateway);

        // Get real-time metrics
        $realtimeMetrics = $this->monitor->getDashboardData($gateway->gateway_id, '24h');

        // Get analytics data
        $analyticsData = $this->analytics->getGatewayAnalytics($gateway->gateway_id, '7d');

        // Get performance insights
        $performanceInsights = $this->analytics->getPerformanceInsights($gateway->gateway_id);

        // Get security status
        $securityStatus = $this->securityManager->getSecurityStatus($gateway);

        return response()->json([
            'success' => true,
            'data' => new ApiGatewayDetailResource($gateway),
            'metrics' => $realtimeMetrics,
            'analytics' => $analyticsData,
            'performance_insights' => $performanceInsights,
            'security_status' => $securityStatus,
            'recommendations' => $this->generateRecommendations($gateway, $realtimeMetrics),
        ]);
    }

    /**
     * Create new API gateway with advanced configuration
     */
    public function store(CreateApiGatewayRequest $request): JsonResponse
    {
        $this->authorize('create', ApiGateway::class);

        $gateway = ApiGateway::create($request->validated());

        // Initialize advanced configurations
        $this->initializeAdvancedConfigurations($gateway, $request);

        // Set up monitoring
        $this->monitor->setupGatewayMonitoring($gateway->gateway_id);

        // Configure security policies
        $this->securityManager->setupSecurityPolicies($gateway, $request->get('security_config', []));

        // Initialize load balancer
        $this->loadBalancer->initializeForGateway($gateway);

        return response()->json([
            'success' => true,
            'message' => 'API Gateway created successfully',
            'data' => new ApiGatewayResource($gateway),
        ], 201);
    }

    /**
     * Update API gateway with advanced features
     */
    public function update(UpdateApiGatewayRequest $request, string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::findOrFail($gatewayId);
        $this->authorize('update', $gateway);

        $oldConfig = $gateway->toArray();
        $gateway->update($request->validated());

        // Handle configuration changes
        $this->handleConfigurationChanges($gateway, $oldConfig, $request);

        // Update monitoring configuration
        $this->monitor->updateGatewayMonitoring($gateway->gateway_id, $request->get('monitoring_config', []));

        // Update security policies
        $this->securityManager->updateSecurityPolicies($gateway, $request->get('security_config', []));

        return response()->json([
            'success' => true,
            'message' => 'API Gateway updated successfully',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * Deploy gateway with advanced deployment strategies
     */
    public function deploy(DeploymentRequest $request, string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::findOrFail($gatewayId);
        $this->authorize('deploy', $gateway);

        $strategy = $request->get('strategy', 'rolling');
        $config = $request->get('config', []);

        try {
            $deployment = match ($strategy) {
                'blue_green' => $this->blueGreenDeployment->deploy($gateway, $config),
                'canary' => $this->canaryDeployment->deploy($gateway, $config),
                default => $this->performRollingDeployment($gateway, $config),
            };

            return response()->json([
                'success' => true,
                'message' => 'Deployment started successfully',
                'data' => [
                    'deployment_id' => $deployment->deployment_id,
                    'strategy' => $strategy,
                    'status' => $deployment->status,
                    'progress' => $deployment->progress_percentage,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Deployment failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Scale gateway instances
     */
    public function scale(ScalingRequest $request, string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::findOrFail($gatewayId);
        $this->authorize('scale', $gateway);

        $targetInstances = $request->get('target_instances');
        $scalingConfig = $request->get('config', []);

        try {
            $scalingEvent = $this->autoScaling->scaleGateway($gateway, $targetInstances, $scalingConfig);

            return response()->json([
                'success' => true,
                'message' => 'Scaling operation started',
                'data' => [
                    'scaling_event_id' => $scalingEvent->id,
                    'current_instances' => $scalingEvent->instances_before,
                    'target_instances' => $scalingEvent->target_instances,
                    'status' => $scalingEvent->status,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scaling failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get gateway health status
     */
    public function health(string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::findOrFail($gatewayId);
        $this->authorize('view', $gateway);

        $healthResults = $gateway->performHealthCheck();

        return response()->json([
            'success' => true,
            'data' => $healthResults,
        ]);
    }

    /**
     * Get gateway metrics and analytics
     */
    public function metrics(Request $request, string $gatewayId): JsonResponse
    {
        $gateway = ApiGateway::findOrFail($gatewayId);
        $this->authorize('view', $gateway);

        $timeRange = $request->get('time_range', '24h');
        $metrics = $this->monitor->getDashboardData($gateway->gateway_id, $timeRange);

        return response()->json([
            'success' => true,
            'data' => $metrics,
        ]);
    }

    /**
     * إنشاء بوابة جديدة
     */
    public function store(CreateApiGatewayRequest $request): JsonResponse
    {
        $gateway = ApiGateway::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'API Gateway created successfully',
            'data' => new ApiGatewayResource($gateway),
        ], 201);
    }

    /**
     * عرض بوابة محددة
     */
    public function show(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('view', $gateway);

        $gateway->load(['endpoints', 'apiKeys', 'requestLogs' => fn($q) => $q->latest()->limit(10)]);

        return response()->json([
            'success' => true,
            'data' => new ApiGatewayResource($gateway),
        ]);
    }

    /**
     * تحديث بوابة
     */
    public function update(ApiGateway $gateway, CreateApiGatewayRequest $request): JsonResponse
    {
        $this->authorize('update', $gateway);

        $gateway->update($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'API Gateway updated successfully',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * حذف بوابة
     */
    public function destroy(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('delete', $gateway);

        $gateway->delete();

        return response()->json([
            'success' => true,
            'message' => 'API Gateway deleted successfully',
        ]);
    }

    /**
     * معالجة طلب API
     */
    public function processRequest(string $gatewayId, Request $request): JsonResponse
    {
        try {
            $requestData = [
                'request_id' => uniqid('req_'),
                'method' => $request->method(),
                'path' => $request->path(),
                'full_url' => $request->fullUrl(),
                'headers' => $request->headers->all(),
                'query' => $request->query(),
                'body' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'protocol' => $request->getProtocolVersion(),
            ];

            $response = $this->gatewayService->processRequest($gatewayId, $requestData);

            return response()->json($response, $response['status_code'] ?? 200);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
                'code' => $e->getCode() ?: 500,
                'timestamp' => now()->toISOString(),
            ], $e->getCode() ?: 500);
        }
    }

    /**
     * فحص صحة البوابة
     */
    public function healthCheck(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('view', $gateway);

        try {
            $healthResults = $gateway->performHealthCheck();

            return response()->json([
                'success' => true,
                'data' => [
                    'gateway_id' => $gateway->gateway_id,
                    'status' => $gateway->status,
                    'health_status' => $gateway->health_status,
                    'last_health_check' => $gateway->last_health_check,
                    'uptime_percentage' => $gateway->uptime_percentage,
                    'health_checks' => $healthResults,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'gateway_id' => $gateway->gateway_id,
                'status' => 'error',
            ], 503);
        }
    }

    /**
     * تفعيل البوابة
     */
    public function activate(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        $gateway->update([
            'is_active' => true,
            'status' => 'active',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API Gateway activated successfully',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * إلغاء تفعيل البوابة
     */
    public function deactivate(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        $gateway->update([
            'is_active' => false,
            'status' => 'inactive',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API Gateway deactivated successfully',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * تفعيل وضع الصيانة
     */
    public function enableMaintenance(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        $gateway->update([
            'maintenance_mode' => true,
            'status' => 'maintenance',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Maintenance mode enabled',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * إلغاء وضع الصيانة
     */
    public function disableMaintenance(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        $gateway->update([
            'maintenance_mode' => false,
            'status' => 'active',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Maintenance mode disabled',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }

    /**
     * الحصول على إحصائيات البوابة
     */
    public function getStatistics(ApiGateway $gateway, Request $request): JsonResponse
    {
        $this->authorize('view', $gateway);

        $period = $request->get('period', '24h');
        $startDate = match ($period) {
            '1h' => now()->subHour(),
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subDay(),
        };

        $stats = [
            'gateway_info' => [
                'id' => $gateway->gateway_id,
                'name' => $gateway->name,
                'status' => $gateway->status,
                'uptime_percentage' => $gateway->uptime_percentage,
                'total_requests' => $gateway->total_requests,
                'successful_requests' => $gateway->successful_requests,
                'failed_requests' => $gateway->failed_requests,
                'average_response_time' => $gateway->average_response_time,
            ],
            'period_stats' => $gateway->requestLogs()
                ->where('created_at', '>=', $startDate)
                ->selectRaw('
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_requests,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_requests,
                    AVG(response_time) as avg_response_time,
                    MIN(response_time) as min_response_time,
                    MAX(response_time) as max_response_time
                ')
                ->first(),
            'endpoints_stats' => $gateway->endpoints()
                ->withCount(['requestLogs as total_requests' => fn($q) => $q->where('created_at', '>=', $startDate)])
                ->get()
                ->map(fn($endpoint) => [
                    'path' => $endpoint->path,
                    'method' => $endpoint->method,
                    'total_requests' => $endpoint->total_requests,
                    'status' => $endpoint->status,
                ]),
            'hourly_stats' => $gateway->requestLogs()
                ->where('created_at', '>=', $startDate)
                ->selectRaw('
                    DATE_FORMAT(created_at, "%Y-%m-%d %H:00") as hour,
                    COUNT(*) as requests,
                    AVG(response_time) as avg_response_time
                ')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * الحصول على سجلات الطلبات
     */
    public function getRequestLogs(ApiGateway $gateway, Request $request): JsonResponse
    {
        $this->authorize('view', $gateway);

        $logs = $gateway->requestLogs()
            ->with(['apiEndpoint', 'apiKey'])
            ->when($request->get('status'), fn($q) => $q->where('status', $request->get('status')))
            ->when($request->get('method'), fn($q) => $q->where('method', $request->get('method')))
            ->when($request->get('endpoint'), fn($q) => $q->where('endpoint', 'like', "%{$request->get('endpoint')}%"))
            ->when($request->get('start_date'), fn($q) => $q->where('created_at', '>=', $request->get('start_date')))
            ->when($request->get('end_date'), fn($q) => $q->where('created_at', '<=', $request->get('end_date')))
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => $logs,
        ]);
    }

    /**
     * تصدير سجلات الطلبات
     */
    public function exportLogs(ApiGateway $gateway, Request $request): JsonResponse
    {
        $this->authorize('view', $gateway);

        // هذا سيتم تطويره لاحقاً لتصدير السجلات بصيغ مختلفة
        return response()->json([
            'success' => true,
            'message' => 'Export functionality will be implemented',
        ]);
    }

    /**
     * مسح التخزين المؤقت
     */
    public function clearCache(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        // مسح التخزين المؤقت المرتبط بالبوابة
        \Cache::tags(["gateway:{$gateway->id}"])->flush();

        return response()->json([
            'success' => true,
            'message' => 'Gateway cache cleared successfully',
        ]);
    }

    /**
     * إعادة تشغيل البوابة
     */
    public function restart(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('update', $gateway);

        try {
            // إعادة تعيين الإحصائيات
            $gateway->update([
                'status' => 'active',
                'maintenance_mode' => false,
                'last_health_check' => now(),
            ]);

            // تنفيذ فحص صحة
            $gateway->performHealthCheck();

            return response()->json([
                'success' => true,
                'message' => 'API Gateway restarted successfully',
                'data' => new ApiGatewayResource($gateway->fresh()),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على تكوين البوابة
     */
    public function getConfiguration(ApiGateway $gateway): JsonResponse
    {
        $this->authorize('view', $gateway);

        return response()->json([
            'success' => true,
            'data' => [
                'load_balancer_config' => $gateway->load_balancer_config,
                'rate_limiting_config' => $gateway->rate_limiting_config,
                'caching_config' => $gateway->caching_config,
                'security_config' => $gateway->security_config,
                'monitoring_config' => $gateway->monitoring_config,
                'circuit_breaker_config' => $gateway->circuit_breaker_config,
                'retry_config' => $gateway->retry_config,
                'timeout_config' => $gateway->timeout_config,
                'cors_config' => $gateway->cors_config,
            ],
        ]);
    }

    /**
     * تحديث تكوين البوابة
     */
    public function updateConfiguration(ApiGateway $gateway, Request $request): JsonResponse
    {
        $this->authorize('update', $gateway);

        $validatedData = $request->validate([
            'load_balancer_config' => 'sometimes|array',
            'rate_limiting_config' => 'sometimes|array',
            'caching_config' => 'sometimes|array',
            'security_config' => 'sometimes|array',
            'monitoring_config' => 'sometimes|array',
            'circuit_breaker_config' => 'sometimes|array',
            'retry_config' => 'sometimes|array',
            'timeout_config' => 'sometimes|array',
            'cors_config' => 'sometimes|array',
        ]);

        $gateway->update($validatedData);

        return response()->json([
            'success' => true,
            'message' => 'Gateway configuration updated successfully',
            'data' => new ApiGatewayResource($gateway->fresh()),
        ]);
    }
}
