<?php

namespace App\Domains\CRM\Events;

use App\Domains\CRM\Models\Customer;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنشاء عميل جديد
 * Customer Created Event
 */
class CustomerCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * العميل الجديد
     */
    public Customer $customer;

    /**
     * بيانات إضافية
     */
    public array $metadata;

    /**
     * Create a new event instance.
     */
    public function __construct(Customer $customer, array $metadata = [])
    {
        $this->customer = $customer;
        $this->metadata = $metadata;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('crm.customers'),
            new PrivateChannel("crm.customer.{$this->customer->id}"),
            new PrivateChannel("crm.rep.{$this->customer->assigned_to}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'customer' => [
                'id' => $this->customer->id,
                'name' => $this->customer->full_name,
                'email' => $this->customer->email,
                'phone' => $this->customer->phone,
                'source' => $this->customer->source?->name,
                'assigned_to' => $this->customer->assignedTo?->full_name,
                'created_at' => $this->customer->created_at,
            ],
            'metadata' => $this->metadata,
            'timestamp' => now(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'customer.created';
    }
}
