<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج المعلم - Project Milestone
 * يمثل نقاط مهمة في المشروع
 */
class Milestone extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'due_date',
        'completed_at',
        'status',
        'type',
        'priority',
        'deliverables',
        'success_criteria',
        'dependencies',
        'responsible_id',
        'budget_allocation',
        'actual_cost',
        'progress_percentage',
        'metadata',
    ];

    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'datetime',
        'deliverables' => 'array',
        'success_criteria' => 'array',
        'dependencies' => 'array',
        'budget_allocation' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'progress_percentage' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المسؤول
     */
    public function responsible(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'responsible_id');
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * التحقق من التأخير
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               $this->status !== 'COMPLETED';
    }

    /**
     * إكمال المعلم
     */
    public function complete(): bool
    {
        $this->update([
            'status' => 'COMPLETED',
            'completed_at' => now(),
            'progress_percentage' => 100,
        ]);

        // تحديث تقدم المشروع
        $this->project->updateProgress();

        return true;
    }
}
