<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الفاتورة الذكية
 * يدعم الفوترة الإلكترونية والتوقيع الرقمي
 */
class Invoice extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'invoice_date',
        'due_date',
        'currency',
        'exchange_rate',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'balance_amount',
        'status',
        'payment_status',
        'invoice_type',
        'recurring_type',
        'recurring_interval',
        'next_invoice_date',
        'template_id',
        'notes',
        'terms_conditions',
        'digital_signature',
        'qr_code',
        'e_invoice_uuid',
        'tax_authority_status',
        'submitted_at',
        'approved_at',
        'metadata',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'next_invoice_date' => 'date',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'exchange_rate' => 'decimal:6',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * حالات الفاتورة
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING' => 'في الانتظار',
        'SENT' => 'مرسلة',
        'VIEWED' => 'تم عرضها',
        'APPROVED' => 'معتمدة',
        'REJECTED' => 'مرفوضة',
        'CANCELLED' => 'ملغية',
        'OVERDUE' => 'متأخرة',
    ];

    /**
     * حالات الدفع
     */
    public const PAYMENT_STATUSES = [
        'UNPAID' => 'غير مدفوعة',
        'PARTIAL' => 'مدفوعة جزئياً',
        'PAID' => 'مدفوعة',
        'OVERPAID' => 'مدفوعة زائد',
        'REFUNDED' => 'مستردة',
    ];

    /**
     * أنواع الفواتير
     */
    public const TYPES = [
        'STANDARD' => 'عادية',
        'PROFORMA' => 'أولية',
        'CREDIT_NOTE' => 'إشعار دائن',
        'DEBIT_NOTE' => 'إشعار مدين',
        'RECURRING' => 'متكررة',
        'SUBSCRIPTION' => 'اشتراك',
    ];

    /**
     * أنواع التكرار
     */
    public const RECURRING_TYPES = [
        'NONE' => 'لا يوجد',
        'DAILY' => 'يومي',
        'WEEKLY' => 'أسبوعي',
        'MONTHLY' => 'شهري',
        'QUARTERLY' => 'ربع سنوي',
        'YEARLY' => 'سنوي',
    ];

    /**
     * العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Customer::class);
    }

    /**
     * بنود الفاتورة
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * المدفوعات
     */
    public function payments(): MorphMany
    {
        return $this->morphMany(\App\Domains\Payments\Models\Payment::class, 'payable');
    }

    /**
     * القيود المحاسبية
     */
    public function journalEntries(): MorphMany
    {
        return $this->morphMany(JournalEntry::class, 'source');
    }

    /**
     * توليد رقم فاتورة جديد
     */
    public static function generateInvoiceNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastInvoice = static::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('invoice_number', 'desc')
            ->first();

        if (!$lastInvoice) {
            return "INV-{$year}{$month}-0001";
        }

        $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
        return "INV-{$year}{$month}-" . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    /**
     * حساب المجاميع
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items()->sum(DB::raw('quantity * unit_price'));
        $this->tax_amount = $this->items()->sum('tax_amount');
        $this->discount_amount = $this->items()->sum('discount_amount');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->balance_amount = $this->total_amount - $this->paid_amount;
    }

    /**
     * تحديث حالة الدفع
     */
    public function updatePaymentStatus(): void
    {
        $totalPaid = $this->payments()->where('status', 'COMPLETED')->sum('amount');
        $this->paid_amount = $totalPaid;

        if ($totalPaid == 0) {
            $this->payment_status = 'UNPAID';
        } elseif ($totalPaid < $this->total_amount) {
            $this->payment_status = 'PARTIAL';
        } elseif ($totalPaid == $this->total_amount) {
            $this->payment_status = 'PAID';
        } else {
            $this->payment_status = 'OVERPAID';
        }

        $this->balance_amount = $this->total_amount - $totalPaid;
        $this->save();
    }

    /**
     * إنشاء فاتورة متكررة
     */
    public function createRecurringInvoice(): ?self
    {
        if ($this->recurring_type === 'NONE' || !$this->next_invoice_date) {
            return null;
        }

        $newInvoice = $this->replicate();
        $newInvoice->invoice_number = static::generateInvoiceNumber();
        $newInvoice->invoice_date = $this->next_invoice_date;
        $newInvoice->due_date = $this->next_invoice_date->addDays(30);
        $newInvoice->status = 'DRAFT';
        $newInvoice->payment_status = 'UNPAID';
        $newInvoice->paid_amount = 0;
        $newInvoice->balance_amount = $this->total_amount;
        $newInvoice->digital_signature = null;
        $newInvoice->qr_code = null;
        $newInvoice->e_invoice_uuid = null;

        // حساب التاريخ التالي
        $newInvoice->next_invoice_date = $this->calculateNextInvoiceDate();

        $newInvoice->save();

        // نسخ البنود
        foreach ($this->items as $item) {
            $newItem = $item->replicate();
            $newItem->invoice_id = $newInvoice->id;
            $newItem->save();
        }

        return $newInvoice;
    }

    /**
     * حساب تاريخ الفاتورة التالية
     */
    protected function calculateNextInvoiceDate(): ?\Carbon\Carbon
    {
        if (!$this->next_invoice_date) {
            return null;
        }

        return match ($this->recurring_type) {
            'DAILY' => $this->next_invoice_date->addDay(),
            'WEEKLY' => $this->next_invoice_date->addWeek(),
            'MONTHLY' => $this->next_invoice_date->addMonth(),
            'QUARTERLY' => $this->next_invoice_date->addMonths(3),
            'YEARLY' => $this->next_invoice_date->addYear(),
            default => null,
        };
    }

    /**
     * توليد QR Code
     */
    public function generateQRCode(): string
    {
        $data = [
            'invoice_number' => $this->invoice_number,
            'total_amount' => $this->total_amount,
            'tax_amount' => $this->tax_amount,
            'invoice_date' => $this->invoice_date->format('Y-m-d'),
            'customer' => $this->customer->name ?? '',
        ];

        return base64_encode(json_encode($data));
    }

    /**
     * إرسال للهيئة الضريبية
     */
    public function submitToTaxAuthority(): bool
    {
        // تنفيذ إرسال الفاتورة للهيئة الضريبية
        // هذا يتطلب تكامل مع API الهيئة الضريبية المغربية

        $this->e_invoice_uuid = Str::uuid();
        $this->tax_authority_status = 'SUBMITTED';
        $this->submitted_at = now();

        return $this->save();
    }

    /**
     * نطاق للفواتير المستحقة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->where('payment_status', '!=', 'PAID');
    }

    /**
     * نطاق للفواتير المتكررة
     */
    public function scopeRecurring($query)
    {
        return $query->where('recurring_type', '!=', 'NONE');
    }

    /**
     * نطاق حسب حالة الدفع
     */
    public function scopeByPaymentStatus($query, string $status)
    {
        return $query->where('payment_status', $status);
    }
}
