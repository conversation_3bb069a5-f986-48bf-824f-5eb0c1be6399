<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج العميل
 */
class Customer extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'customer_code',
        'name',
        'email',
        'phone',
        'mobile',
        'website',
        'tax_number',
        'commercial_register',
        'customer_type',
        'payment_terms',
        'credit_limit',
        'currency_code',
        'language',
        'billing_address',
        'shipping_address',
        'contact_person',
        'contact_email',
        'contact_phone',
        'notes',
        'is_active',
        'tags',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'is_active' => 'boolean',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع العملاء
     */
    public const CUSTOMER_TYPES = [
        'INDIVIDUAL' => 'فرد',
        'COMPANY' => 'شركة',
        'GOVERNMENT' => 'جهة حكومية',
        'NON_PROFIT' => 'منظمة غير ربحية',
    ];

    /**
     * شروط الدفع
     */
    public const PAYMENT_TERMS = [
        'CASH' => 'نقداً',
        'NET_15' => '15 يوم',
        'NET_30' => '30 يوم',
        'NET_45' => '45 يوم',
        'NET_60' => '60 يوم',
        'NET_90' => '90 يوم',
        'CUSTOM' => 'مخصص',
    ];

    /**
     * العملات المدعومة
     */
    public const SUPPORTED_CURRENCIES = [
        'MAD' => 'درهم مغربي',
        'USD' => 'دولار أمريكي',
        'EUR' => 'يورو',
        'SAR' => 'ريال سعودي',
        'AED' => 'درهم إماراتي',
    ];

    /**
     * اللغات المدعومة
     */
    public const SUPPORTED_LANGUAGES = [
        'ar' => 'العربية',
        'en' => 'English',
        'fr' => 'Français',
    ];

    /**
     * الفواتير
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Invoice::class);
    }

    /**
     * المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Payment::class);
    }

    /**
     * من أنشأ العميل
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * من حدث العميل
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * الحصول على اسم نوع العميل
     */
    public function getCustomerTypeNameAttribute(): string
    {
        return self::CUSTOMER_TYPES[$this->customer_type] ?? $this->customer_type;
    }

    /**
     * الحصول على اسم شروط الدفع
     */
    public function getPaymentTermsNameAttribute(): string
    {
        return self::PAYMENT_TERMS[$this->payment_terms] ?? $this->payment_terms;
    }

    /**
     * الحصول على اسم العملة
     */
    public function getCurrencyNameAttribute(): string
    {
        return self::SUPPORTED_CURRENCIES[$this->currency_code] ?? $this->currency_code;
    }

    /**
     * الحصول على اسم اللغة
     */
    public function getLanguageNameAttribute(): string
    {
        return self::SUPPORTED_LANGUAGES[$this->language] ?? $this->language;
    }

    /**
     * فحص إذا كان العميل نشط
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * فحص إذا كان العميل شركة
     */
    public function isCompany(): bool
    {
        return $this->customer_type === 'COMPANY';
    }

    /**
     * فحص إذا كان العميل فرد
     */
    public function isIndividual(): bool
    {
        return $this->customer_type === 'INDIVIDUAL';
    }

    /**
     * حساب إجمالي المبيعات
     */
    public function getTotalSalesAttribute(): float
    {
        return $this->invoices()
            ->where('status', 'PAID')
            ->sum('total_amount');
    }

    /**
     * حساب الرصيد المستحق
     */
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->invoices()
            ->whereIn('payment_status', ['UNPAID', 'PARTIAL'])
            ->sum('balance_amount');
    }

    /**
     * حساب عدد الفواتير
     */
    public function getInvoicesCountAttribute(): int
    {
        return $this->invoices()->count();
    }

    /**
     * فحص إذا كان العميل تجاوز حد الائتمان
     */
    public function isOverCreditLimit(): bool
    {
        if (!$this->credit_limit) {
            return false;
        }

        return $this->getOutstandingBalanceAttribute() > $this->credit_limit;
    }

    /**
     * الحصول على الرصيد المتاح
     */
    public function getAvailableCreditAttribute(): float
    {
        if (!$this->credit_limit) {
            return 0;
        }

        return max(0, $this->credit_limit - $this->getOutstandingBalanceAttribute());
    }

    /**
     * تفعيل العميل
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * إلغاء تفعيل العميل
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * تحديث حد الائتمان
     */
    public function updateCreditLimit(float $newLimit): void
    {
        $this->update(['credit_limit' => $newLimit]);
    }

    /**
     * إضافة ملاحظة
     */
    public function addNote(string $note): void
    {
        $currentNotes = $this->notes ?? '';
        $timestamp = now()->format('Y-m-d H:i:s');
        $newNote = "[{$timestamp}] {$note}";
        
        $this->update([
            'notes' => $currentNotes ? "{$currentNotes}\n{$newNote}" : $newNote
        ]);
    }

    /**
     * Scope للعملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للعملاء غير النشطين
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope للعملاء حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('customer_type', $type);
    }

    /**
     * Scope للعملاء الذين تجاوزوا حد الائتمان
     */
    public function scopeOverCreditLimit($query)
    {
        return $query->whereNotNull('credit_limit')
            ->whereHas('invoices', function ($q) {
                $q->whereIn('payment_status', ['UNPAID', 'PARTIAL'])
                  ->havingRaw('SUM(balance_amount) > customers.credit_limit');
            });
    }

    /**
     * Scope للبحث
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('customer_code', 'like', "%{$search}%")
              ->orWhere('tax_number', 'like', "%{$search}%");
        });
    }
}
