<?php

namespace App\Domains\Taxation\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Taxation\Models\TaxReturn;
use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Services\TaxReturnService;
use App\Domains\Taxation\Services\TaxAuthorityIntegrationService;
use App\Domains\Taxation\Requests\StoreTaxReturnRequest;
use App\Domains\Taxation\Requests\UpdateTaxReturnRequest;
use App\Domains\Taxation\Resources\TaxReturnResource;
use App\Domains\Taxation\Resources\TaxReturnCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * متحكم الإقرارات الضريبية
 * إدارة شاملة للإقرارات الضريبية والتقديم الإلكتروني
 */
class TaxReturnController extends Controller
{
    protected TaxReturnService $taxReturnService;
    protected TaxAuthorityIntegrationService $integrationService;

    public function __construct(
        TaxReturnService $taxReturnService,
        TaxAuthorityIntegrationService $integrationService
    ) {
        $this->taxReturnService = $taxReturnService;
        $this->integrationService = $integrationService;
    }

    /**
     * عرض قائمة الإقرارات الضريبية
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', TaxReturn::class);

        $query = TaxReturn::with(['taxSystem', 'company', 'submittedBy']);

        // التصفية حسب النظام الضريبي
        if ($request->filled('tax_system_id')) {
            $query->where('tax_system_id', $request->tax_system_id);
        }

        // التصفية حسب الشركة
        if ($request->filled('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('return_number', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('company', function ($companyQuery) use ($search) {
                      $companyQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // التصفية حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب الفترة الضريبية
        if ($request->filled('tax_period_from') && $request->filled('tax_period_to')) {
            $query->whereBetween('tax_period_from', [$request->tax_period_from, $request->tax_period_to]);
        }

        // التصفية حسب تاريخ الاستحقاق
        if ($request->filled('due_date_from') && $request->filled('due_date_to')) {
            $query->whereBetween('due_date', [$request->due_date_from, $request->due_date_to]);
        }

        // التصفية للإقرارات المتأخرة
        if ($request->boolean('overdue_only')) {
            $query->where('due_date', '<', now())
                  ->whereNotIn('status', ['SUBMITTED', 'APPROVED', 'PAID']);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $taxReturns = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new TaxReturnCollection($taxReturns),
            'meta' => [
                'total' => $taxReturns->total(),
                'per_page' => $taxReturns->perPage(),
                'current_page' => $taxReturns->currentPage(),
                'last_page' => $taxReturns->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء إقرار ضريبي جديد
     */
    public function store(StoreTaxReturnRequest $request): JsonResponse
    {
        $this->authorize('create', TaxReturn::class);

        DB::beginTransaction();

        try {
            $taxReturn = $this->taxReturnService->createTaxReturn($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الإقرار الضريبي بنجاح',
                'data' => new TaxReturnResource($taxReturn->load(['taxSystem', 'company'])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الإقرار الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل إقرار ضريبي محدد
     */
    public function show(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::with([
            'taxSystem', 'company', 'submittedBy', 'approvedBy',
            'taxCalculations', 'attachments', 'activities'
        ])->findOrFail($id);

        $this->authorize('view', $taxReturn);

        // إضافة معلومات إضافية
        $additionalData = [
            'validation_results' => $this->taxReturnService->validateTaxReturn($taxReturn),
            'submission_status' => $this->getSubmissionStatus($taxReturn),
            'payment_status' => $this->getPaymentStatus($taxReturn),
            'compliance_check' => $this->getComplianceCheck($taxReturn),
        ];

        return response()->json([
            'success' => true,
            'data' => new TaxReturnResource($taxReturn),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث الإقرار الضريبي
     */
    public function update(UpdateTaxReturnRequest $request, int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('update', $taxReturn);

        // التحقق من إمكانية التحديث
        if (in_array($taxReturn->status, ['SUBMITTED', 'APPROVED', 'PAID'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تحديث الإقرار الضريبي بعد تقديمه',
            ], 422);
        }

        DB::beginTransaction();

        try {
            $taxReturn = $this->taxReturnService->updateTaxReturn($taxReturn, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الإقرار الضريبي بنجاح',
                'data' => new TaxReturnResource($taxReturn),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإقرار الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف الإقرار الضريبي
     */
    public function destroy(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('delete', $taxReturn);

        // التحقق من إمكانية الحذف
        if (in_array($taxReturn->status, ['SUBMITTED', 'APPROVED', 'PAID'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الإقرار الضريبي بعد تقديمه',
            ], 422);
        }

        try {
            $taxReturn->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإقرار الضريبي بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإقرار الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حساب الضرائب للإقرار
     */
    public function calculateTaxes(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('update', $taxReturn);

        try {
            $calculations = $this->taxReturnService->calculateTaxes($taxReturn);

            return response()->json([
                'success' => true,
                'message' => 'تم حساب الضرائب بنجاح',
                'data' => [
                    'tax_return' => new TaxReturnResource($taxReturn->fresh()),
                    'calculations' => $calculations,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حساب الضرائب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * التحقق من صحة الإقرار
     */
    public function validate(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('view', $taxReturn);

        try {
            $validationResults = $this->taxReturnService->validateTaxReturn($taxReturn);

            return response()->json([
                'success' => true,
                'message' => 'تم التحقق من الإقرار بنجاح',
                'data' => $validationResults,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من الإقرار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تقديم الإقرار الضريبي
     */
    public function submit(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('submit', $taxReturn);

        try {
            $result = $this->taxReturnService->submitTaxReturn($taxReturn);

            return response()->json([
                'success' => true,
                'message' => 'تم تقديم الإقرار الضريبي بنجاح',
                'data' => [
                    'tax_return' => new TaxReturnResource($taxReturn->fresh()),
                    'submission_result' => $result,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تقديم الإقرار الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إلغاء تقديم الإقرار
     */
    public function cancel(int $id): JsonResponse
    {
        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('cancel', $taxReturn);

        try {
            $result = $this->taxReturnService->cancelTaxReturn($taxReturn);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء تقديم الإقرار الضريبي بنجاح',
                'data' => [
                    'tax_return' => new TaxReturnResource($taxReturn->fresh()),
                    'cancellation_result' => $result,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء تقديم الإقرار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تصدير الإقرار الضريبي
     */
    public function export(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:pdf,xml,excel,json',
            'include_attachments' => 'boolean',
        ]);

        $taxReturn = TaxReturn::findOrFail($id);
        $this->authorize('view', $taxReturn);

        try {
            $filePath = $this->taxReturnService->exportTaxReturn(
                $taxReturn,
                $request->format,
                $request->boolean('include_attachments', false)
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تصدير الإقرار الضريبي بنجاح',
                'download_url' => asset('storage/' . $filePath),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير الإقرار الضريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على حالة التقديم
     */
    protected function getSubmissionStatus(TaxReturn $taxReturn): array
    {
        return [
            'is_submitted' => in_array($taxReturn->status, ['SUBMITTED', 'APPROVED', 'PAID']),
            'submission_date' => $taxReturn->submitted_at,
            'submission_reference' => $taxReturn->submission_reference,
            'authority_status' => $taxReturn->authority_status,
            'authority_response' => $taxReturn->authority_response,
        ];
    }

    /**
     * الحصول على حالة الدفع
     */
    protected function getPaymentStatus(TaxReturn $taxReturn): array
    {
        return [
            'total_amount_due' => $taxReturn->total_tax_amount,
            'amount_paid' => $taxReturn->amount_paid,
            'remaining_amount' => $taxReturn->total_tax_amount - $taxReturn->amount_paid,
            'is_fully_paid' => $taxReturn->amount_paid >= $taxReturn->total_tax_amount,
            'payment_due_date' => $taxReturn->payment_due_date,
            'is_overdue' => $taxReturn->payment_due_date && now() > $taxReturn->payment_due_date,
        ];
    }

    /**
     * الحصول على فحص الامتثال
     */
    protected function getComplianceCheck(TaxReturn $taxReturn): array
    {
        return [
            'is_compliant' => $taxReturn->is_compliant,
            'compliance_score' => $taxReturn->compliance_score,
            'compliance_issues' => $taxReturn->compliance_issues ?? [],
            'last_compliance_check' => $taxReturn->last_compliance_check_at,
        ];
    }
}
