<?php

namespace App\Domains\Integration\Services\Transformation\Sanitizers;

use App\Domains\Integration\Exceptions\SanitizationException;
use Illuminate\Support\Facades\Log;

/**
 * Data Sanitizer
 * Sanitizes and cleans data according to specified rules
 */
class DataSanitizer
{
    protected array $config;
    protected array $sanitizers;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'strict_mode' => false,
            'preserve_original' => false,
            'log_sanitization' => true,
        ], $config);
        
        $this->sanitizers = [];
        $this->registerDefaultSanitizers();
    }

    /**
     * Sanitize data using specified rules
     */
    public function sanitize(array $data, array $sanitizationRules): array
    {
        try {
            $sanitizedData = $data;
            $sanitizationLog = [];
            
            foreach ($sanitizationRules as $rule) {
                $field = $rule['field'] ?? null;
                $sanitizerName = $rule['sanitizer'] ?? null;
                $options = $rule['options'] ?? [];
                
                if (!$field || !$sanitizerName) {
                    continue;
                }
                
                $sanitizer = $this->getSanitizer($sanitizerName);
                if (!$sanitizer) {
                    Log::warning("Sanitizer not found: {$sanitizerName}");
                    continue;
                }
                
                $originalValue = $this->getNestedValue($sanitizedData, $field);
                if ($originalValue === null) {
                    continue;
                }
                
                $sanitizedValue = $sanitizer($originalValue, $options);
                
                // Log sanitization if enabled
                if ($this->config['log_sanitization'] && $originalValue !== $sanitizedValue) {
                    $sanitizationLog[] = [
                        'field' => $field,
                        'sanitizer' => $sanitizerName,
                        'original' => $originalValue,
                        'sanitized' => $sanitizedValue,
                    ];
                }
                
                $this->setNestedValue($sanitizedData, $field, $sanitizedValue);
            }
            
            // Add sanitization log if preserve_original is enabled
            if ($this->config['preserve_original'] && !empty($sanitizationLog)) {
                $sanitizedData['_sanitization_log'] = $sanitizationLog;
            }
            
            return $sanitizedData;
            
        } catch (\Exception $e) {
            Log::error('Data sanitization failed', [
                'error' => $e->getMessage(),
                'rules' => $sanitizationRules,
            ]);
            
            throw new SanitizationException('Data sanitization failed: ' . $e->getMessage());
        }
    }

    /**
     * Register default sanitizers
     */
    protected function registerDefaultSanitizers(): void
    {
        // String sanitizers
        $this->registerSanitizer('trim', function ($value, $options) {
            return is_string($value) ? trim($value, $options['characters'] ?? " \t\n\r\0\x0B") : $value;
        });

        $this->registerSanitizer('lowercase', function ($value, $options) {
            return is_string($value) ? strtolower($value) : $value;
        });

        $this->registerSanitizer('uppercase', function ($value, $options) {
            return is_string($value) ? strtoupper($value) : $value;
        });

        $this->registerSanitizer('strip_tags', function ($value, $options) {
            if (!is_string($value)) return $value;
            $allowedTags = $options['allowed_tags'] ?? '';
            return strip_tags($value, $allowedTags);
        });

        $this->registerSanitizer('html_entities', function ($value, $options) {
            if (!is_string($value)) return $value;
            $flags = $options['flags'] ?? ENT_QUOTES | ENT_HTML5;
            $encoding = $options['encoding'] ?? 'UTF-8';
            return htmlentities($value, $flags, $encoding);
        });

        $this->registerSanitizer('html_special_chars', function ($value, $options) {
            if (!is_string($value)) return $value;
            $flags = $options['flags'] ?? ENT_QUOTES | ENT_HTML5;
            $encoding = $options['encoding'] ?? 'UTF-8';
            return htmlspecialchars($value, $flags, $encoding);
        });

        // Number sanitizers
        $this->registerSanitizer('integer', function ($value, $options) {
            return (int) $value;
        });

        $this->registerSanitizer('float', function ($value, $options) {
            $precision = $options['precision'] ?? null;
            $float = (float) $value;
            return $precision !== null ? round($float, $precision) : $float;
        });

        $this->registerSanitizer('abs', function ($value, $options) {
            return is_numeric($value) ? abs($value) : $value;
        });

        // Email sanitizer
        $this->registerSanitizer('email', function ($value, $options) {
            if (!is_string($value)) return $value;
            return filter_var(trim(strtolower($value)), FILTER_SANITIZE_EMAIL);
        });

        // URL sanitizer
        $this->registerSanitizer('url', function ($value, $options) {
            if (!is_string($value)) return $value;
            return filter_var(trim($value), FILTER_SANITIZE_URL);
        });

        // Phone sanitizer
        $this->registerSanitizer('phone', function ($value, $options) {
            if (!is_string($value)) return $value;
            $keepPlus = $options['keep_plus'] ?? true;
            $pattern = $keepPlus ? '/[^\d+]/' : '/[^\d]/';
            return preg_replace($pattern, '', $value);
        });

        // Regex sanitizer
        $this->registerSanitizer('regex', function ($value, $options) {
            if (!is_string($value)) return $value;
            $pattern = $options['pattern'] ?? '';
            $replacement = $options['replacement'] ?? '';
            return preg_replace($pattern, $replacement, $value);
        });

        // Remove special characters
        $this->registerSanitizer('alphanumeric', function ($value, $options) {
            if (!is_string($value)) return $value;
            $allowSpaces = $options['allow_spaces'] ?? false;
            $pattern = $allowSpaces ? '/[^a-zA-Z0-9\s]/' : '/[^a-zA-Z0-9]/';
            return preg_replace($pattern, '', $value);
        });

        // Date sanitizer
        $this->registerSanitizer('date', function ($value, $options) {
            if (!is_string($value)) return $value;
            $format = $options['format'] ?? 'Y-m-d';
            try {
                $date = new \DateTime($value);
                return $date->format($format);
            } catch (\Exception $e) {
                return $value;
            }
        });

        // Boolean sanitizer
        $this->registerSanitizer('boolean', function ($value, $options) {
            if (is_bool($value)) return $value;
            if (is_string($value)) {
                $value = strtolower(trim($value));
                return in_array($value, ['true', '1', 'yes', 'on', 'y']);
            }
            return (bool) $value;
        });

        // JSON sanitizer
        $this->registerSanitizer('json', function ($value, $options) {
            if (is_array($value) || is_object($value)) {
                return json_encode($value, $options['flags'] ?? 0);
            }
            if (is_string($value)) {
                // Validate and re-encode JSON
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return json_encode($decoded, $options['flags'] ?? 0);
                }
            }
            return $value;
        });

        // Array sanitizer
        $this->registerSanitizer('array', function ($value, $options) {
            if (is_array($value)) {
                $unique = $options['unique'] ?? false;
                $filter_empty = $options['filter_empty'] ?? false;
                
                if ($filter_empty) {
                    $value = array_filter($value, function ($item) {
                        return !empty($item);
                    });
                }
                
                if ($unique) {
                    $value = array_unique($value);
                }
                
                return array_values($value); // Re-index
            }
            
            // Convert string to array
            if (is_string($value)) {
                $delimiter = $options['delimiter'] ?? ',';
                return array_map('trim', explode($delimiter, $value));
            }
            
            return $value;
        });

        // Length limiter
        $this->registerSanitizer('max_length', function ($value, $options) {
            if (!is_string($value)) return $value;
            $maxLength = $options['max_length'] ?? 255;
            $suffix = $options['suffix'] ?? '';
            
            if (strlen($value) > $maxLength) {
                return substr($value, 0, $maxLength - strlen($suffix)) . $suffix;
            }
            
            return $value;
        });

        // SQL injection prevention
        $this->registerSanitizer('sql_safe', function ($value, $options) {
            if (!is_string($value)) return $value;
            // Remove common SQL injection patterns
            $patterns = [
                '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i',
                '/[\'";]/',
                '/--/',
                '/\/\*.*?\*\//',
            ];
            
            foreach ($patterns as $pattern) {
                $value = preg_replace($pattern, '', $value);
            }
            
            return $value;
        });

        // XSS prevention
        $this->registerSanitizer('xss_safe', function ($value, $options) {
            if (!is_string($value)) return $value;
            
            // Remove script tags and javascript
            $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value);
            $value = preg_replace('/javascript:/i', '', $value);
            $value = preg_replace('/on\w+\s*=/i', '', $value);
            
            return $value;
        });
    }

    /**
     * Register a custom sanitizer
     */
    public function registerSanitizer(string $name, callable $sanitizer): void
    {
        $this->sanitizers[$name] = $sanitizer;
    }

    /**
     * Get sanitizer by name
     */
    protected function getSanitizer(string $name): ?callable
    {
        return $this->sanitizers[$name] ?? null;
    }

    /**
     * Get nested value from array using dot notation
     */
    protected function getNestedValue(array $data, string $key): mixed
    {
        $keys = explode('.', $key);
        $value = $data;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    /**
     * Set nested value using dot notation
     */
    protected function setNestedValue(array &$data, string $key, mixed $value): void
    {
        $keys = explode('.', $key);
        $current = &$data;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    /**
     * Sanitize entire array recursively
     */
    public function sanitizeRecursive(array $data, string $sanitizerName, array $options = []): array
    {
        $sanitizer = $this->getSanitizer($sanitizerName);
        if (!$sanitizer) {
            return $data;
        }
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->sanitizeRecursive($value, $sanitizerName, $options);
            } else {
                $data[$key] = $sanitizer($value, $options);
            }
        }
        
        return $data;
    }

    /**
     * Get list of available sanitizers
     */
    public function getAvailableSanitizers(): array
    {
        return array_keys($this->sanitizers);
    }
}
