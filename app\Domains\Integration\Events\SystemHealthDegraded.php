<?php

namespace App\Domains\Integration\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

/**
 * System Health Degraded Event
 * 
 * Fired when system health metrics indicate degradation
 */
class SystemHealthDegraded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $metric;
    public float $value;
    public float $threshold;
    public string $severity;
    public array $systemMetrics;
    public array $affectedComponents;
    public Carbon $detectedAt;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $metric,
        float $value,
        float $threshold,
        string $severity = 'warning',
        array $systemMetrics = [],
        array $affectedComponents = []
    ) {
        $this->metric = $metric;
        $this->value = $value;
        $this->threshold = $threshold;
        $this->severity = $severity;
        $this->systemMetrics = $systemMetrics;
        $this->affectedComponents = $affectedComponents;
        $this->detectedAt = now();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('system.health'),
            new PrivateChannel('admin.alerts'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'metric' => $this->metric,
            'value' => $this->value,
            'threshold' => $this->threshold,
            'severity' => $this->severity,
            'system_metrics' => $this->systemMetrics,
            'affected_components' => $this->affectedComponents,
            'detected_at' => $this->detectedAt->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'system.health.degraded';
    }

    /**
     * Get health degradation message
     */
    public function getHealthMessage(): string
    {
        $percentage = round((($this->value - $this->threshold) / $this->threshold) * 100, 1);
        
        return "System health degraded: {$this->metric} is {$percentage}% above threshold ({$this->value} > {$this->threshold})";
    }

    /**
     * Get recommended actions
     */
    public function getRecommendedActions(): array
    {
        return match ($this->metric) {
            'cpu_usage' => [
                'Scale up instances',
                'Optimize CPU-intensive processes',
                'Enable auto-scaling',
                'Review resource allocation',
            ],
            'memory_usage' => [
                'Increase memory allocation',
                'Clear memory caches',
                'Restart memory-intensive services',
                'Check for memory leaks',
            ],
            'disk_usage' => [
                'Clean up temporary files',
                'Archive old logs',
                'Increase disk space',
                'Optimize storage usage',
            ],
            'error_rate' => [
                'Check error logs',
                'Review recent deployments',
                'Validate external dependencies',
                'Enable circuit breakers',
            ],
            default => [
                'Monitor system closely',
                'Check system logs',
                'Review recent changes',
                'Contact system administrator',
            ],
        };
    }

    /**
     * Check if immediate action is required
     */
    public function requiresImmediateAction(): bool
    {
        return $this->severity === 'critical' || 
               ($this->severity === 'high' && $this->value > $this->threshold * 1.5);
    }

    /**
     * Get impact assessment
     */
    public function getImpactAssessment(): array
    {
        $impact = 'low';
        $description = 'Minor performance degradation expected';

        if ($this->severity === 'critical') {
            $impact = 'high';
            $description = 'Significant service disruption possible';
        } elseif ($this->severity === 'high') {
            $impact = 'medium';
            $description = 'Moderate performance impact expected';
        }

        return [
            'level' => $impact,
            'description' => $description,
            'affected_components' => $this->affectedComponents,
            'estimated_recovery_time' => $this->getEstimatedRecoveryTime(),
        ];
    }

    /**
     * Get estimated recovery time
     */
    protected function getEstimatedRecoveryTime(): string
    {
        return match ($this->severity) {
            'critical' => '15-30 minutes',
            'high' => '5-15 minutes',
            'medium' => '2-5 minutes',
            default => '1-2 minutes',
        };
    }
}
