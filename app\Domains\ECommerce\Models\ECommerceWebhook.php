<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج Webhook للتجارة الإلكترونية
 * يدير webhooks الواردة من منصات التجارة الإلكترونية
 */
class ECommerceWebhook extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'webhook_id',
        'event_type',
        'event_name',
        'topic',
        'resource',
        'resource_id',
        'action',
        'source',
        'source_name',
        'source_version',
        'api_version',
        'webhook_version',
        'format',
        'content_type',
        'encoding',
        'compression',
        'signature',
        'signature_algorithm',
        'signature_header',
        'timestamp',
        'delivery_id',
        'attempt_number',
        'retry_count',
        'max_retries',
        'retry_delay',
        'retry_backoff',
        'timeout',
        'status',
        'processing_status',
        'verification_status',
        'delivery_status',
        'response_status',
        'http_status_code',
        'response_time',
        'processing_time',
        'queue_time',
        'total_time',
        'headers',
        'payload',
        'raw_payload',
        'parsed_payload',
        'processed_payload',
        'transformed_payload',
        'validated_payload',
        'filtered_payload',
        'sanitized_payload',
        'normalized_payload',
        'enriched_payload',
        'metadata',
        'context',
        'environment',
        'user_agent',
        'ip_address',
        'forwarded_for',
        'real_ip',
        'client_ip',
        'proxy_ip',
        'load_balancer_ip',
        'cdn_ip',
        'edge_ip',
        'origin_ip',
        'source_ip',
        'destination_ip',
        'request_id',
        'correlation_id',
        'trace_id',
        'span_id',
        'parent_span_id',
        'session_id',
        'transaction_id',
        'batch_id',
        'group_id',
        'sequence_number',
        'order_number',
        'priority',
        'urgency',
        'importance',
        'severity',
        'category',
        'subcategory',
        'classification',
        'tags',
        'labels',
        'annotations',
        'attributes',
        'properties',
        'features',
        'flags',
        'options',
        'settings',
        'configuration',
        'parameters',
        'arguments',
        'variables',
        'constants',
        'values',
        'data',
        'content',
        'body',
        'message',
        'description',
        'summary',
        'details',
        'notes',
        'comments',
        'remarks',
        'observations',
        'findings',
        'results',
        'outcomes',
        'conclusions',
        'recommendations',
        'suggestions',
        'proposals',
        'ideas',
        'thoughts',
        'opinions',
        'feedback',
        'reviews',
        'ratings',
        'scores',
        'metrics',
        'measurements',
        'statistics',
        'analytics',
        'insights',
        'intelligence',
        'knowledge',
        'information',
        'facts',
        'evidence',
        'proof',
        'documentation',
        'records',
        'logs',
        'traces',
        'history',
        'timeline',
        'chronology',
        'sequence',
        'order',
        'arrangement',
        'organization',
        'structure',
        'format_details',
        'layout',
        'design',
        'pattern',
        'template',
        'schema',
        'model',
        'framework',
        'architecture',
        'blueprint',
        'specification',
        'definition',
        'description_detailed',
        'explanation',
        'clarification',
        'interpretation',
        'translation',
        'transformation',
        'conversion',
        'adaptation',
        'modification',
        'adjustment',
        'customization',
        'personalization',
        'localization',
        'globalization',
        'internationalization',
        'regionalization',
        'culturalization',
        'error_message',
        'error_code',
        'error_type',
        'error_category',
        'error_severity',
        'error_details',
        'error_context',
        'error_stack_trace',
        'error_source',
        'error_line',
        'error_file',
        'error_function',
        'error_class',
        'error_method',
        'error_namespace',
        'warning_message',
        'warning_code',
        'warning_type',
        'warning_category',
        'warning_severity',
        'warning_details',
        'warning_context',
        'info_message',
        'info_code',
        'info_type',
        'info_category',
        'info_details',
        'info_context',
        'debug_message',
        'debug_code',
        'debug_type',
        'debug_category',
        'debug_details',
        'debug_context',
        'trace_message',
        'trace_code',
        'trace_type',
        'trace_category',
        'trace_details',
        'trace_context',
        'performance_metrics',
        'quality_metrics',
        'reliability_metrics',
        'availability_metrics',
        'scalability_metrics',
        'security_metrics',
        'compliance_metrics',
        'business_metrics',
        'technical_metrics',
        'operational_metrics',
        'functional_metrics',
        'non_functional_metrics',
        'user_metrics',
        'customer_metrics',
        'financial_metrics',
        'cost_metrics',
        'revenue_metrics',
        'profit_metrics',
        'roi_metrics',
        'value_metrics',
        'impact_metrics',
        'benefit_metrics',
        'outcome_metrics',
        'result_metrics',
        'success_metrics',
        'failure_metrics',
        'error_metrics',
        'warning_metrics',
        'info_metrics',
        'debug_metrics',
        'trace_metrics',
        'log_metrics',
        'audit_metrics',
        'monitoring_metrics',
        'alerting_metrics',
        'notification_metrics',
        'communication_metrics',
        'integration_metrics',
        'synchronization_metrics',
        'transformation_metrics',
        'validation_metrics',
        'verification_metrics',
        'authentication_metrics',
        'authorization_metrics',
        'encryption_metrics',
        'decryption_metrics',
        'compression_metrics',
        'decompression_metrics',
        'serialization_metrics',
        'deserialization_metrics',
        'parsing_metrics',
        'formatting_metrics',
        'rendering_metrics',
        'processing_metrics',
        'execution_metrics',
        'performance_score',
        'quality_score',
        'reliability_score',
        'availability_score',
        'scalability_score',
        'security_score',
        'compliance_score',
        'business_score',
        'technical_score',
        'operational_score',
        'functional_score',
        'non_functional_score',
        'user_score',
        'customer_score',
        'financial_score',
        'cost_score',
        'revenue_score',
        'profit_score',
        'roi_score',
        'value_score',
        'impact_score',
        'benefit_score',
        'outcome_score',
        'result_score',
        'success_score',
        'failure_score',
        'error_score',
        'warning_score',
        'info_score',
        'debug_score',
        'trace_score',
        'log_score',
        'audit_score',
        'monitoring_score',
        'alerting_score',
        'notification_score',
        'communication_score',
        'integration_score',
        'synchronization_score',
        'transformation_score',
        'validation_score',
        'verification_score',
        'authentication_score',
        'authorization_score',
        'encryption_score',
        'decryption_score',
        'compression_score',
        'decompression_score',
        'serialization_score',
        'deserialization_score',
        'parsing_score',
        'formatting_score',
        'rendering_score',
        'processing_score',
        'execution_score',
        'overall_score',
        'health_score',
        'is_processed',
        'is_verified',
        'is_authenticated',
        'is_authorized',
        'is_valid',
        'is_complete',
        'is_successful',
        'is_failed',
        'is_partial',
        'is_skipped',
        'is_ignored',
        'is_filtered',
        'is_blocked',
        'is_quarantined',
        'is_flagged',
        'is_suspicious',
        'is_malicious',
        'is_spam',
        'is_duplicate',
        'is_outdated',
        'is_expired',
        'is_cancelled',
        'is_rejected',
        'is_accepted',
        'is_approved',
        'is_denied',
        'is_pending',
        'is_queued',
        'is_scheduled',
        'is_delayed',
        'is_retried',
        'is_timeout',
        'is_rate_limited',
        'is_throttled',
        'is_circuit_breaker_open',
        'is_circuit_breaker_half_open',
        'is_circuit_breaker_closed',
        'is_load_balanced',
        'is_cached',
        'is_compressed',
        'is_encrypted',
        'is_signed',
        'is_verified_signature',
        'is_trusted_source',
        'is_whitelisted',
        'is_blacklisted',
        'is_monitored',
        'is_logged',
        'is_audited',
        'is_tracked',
        'is_traced',
        'is_debugged',
        'is_profiled',
        'is_benchmarked',
        'is_tested',
        'is_validated_schema',
        'is_transformed',
        'is_normalized',
        'is_sanitized',
        'is_filtered_content',
        'is_enriched',
        'is_aggregated',
        'is_summarized',
        'is_analyzed',
        'is_classified',
        'is_categorized',
        'is_tagged',
        'is_labeled',
        'is_annotated',
        'is_indexed',
        'is_searchable',
        'is_discoverable',
        'is_accessible',
        'is_available',
        'is_online',
        'is_offline',
        'is_active',
        'is_inactive',
        'is_enabled',
        'is_disabled',
        'is_visible',
        'is_hidden',
        'is_public',
        'is_private',
        'is_internal',
        'is_external',
        'is_local',
        'is_remote',
        'is_cloud',
        'is_on_premise',
        'is_hybrid',
        'is_multi_cloud',
        'is_edge',
        'is_mobile',
        'is_desktop',
        'is_web',
        'is_api',
        'is_service',
        'is_microservice',
        'is_serverless',
        'is_containerized',
        'is_virtualized',
        'is_bare_metal',
        'is_managed',
        'is_self_hosted',
        'is_saas',
        'is_paas',
        'is_iaas',
        'is_faas',
        'is_baas',
        'is_daas',
        'is_caas',
        'is_naas',
        'is_maas',
        'is_haas',
        'is_raas',
        'is_taas',
        'is_aaas',
        'is_xaas',
        'received_at',
        'processed_at',
        'verified_at',
        'authenticated_at',
        'authorized_at',
        'validated_at',
        'completed_at',
        'failed_at',
        'retried_at',
        'expired_at',
        'cancelled_at',
        'rejected_at',
        'accepted_at',
        'approved_at',
        'denied_at',
        'queued_at',
        'scheduled_at',
        'delayed_at',
        'timeout_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'headers' => 'array',
        'payload' => 'array',
        'raw_payload' => 'array',
        'parsed_payload' => 'array',
        'processed_payload' => 'array',
        'transformed_payload' => 'array',
        'validated_payload' => 'array',
        'filtered_payload' => 'array',
        'sanitized_payload' => 'array',
        'normalized_payload' => 'array',
        'enriched_payload' => 'array',
        'metadata' => 'array',
        'context' => 'array',
        'tags' => 'array',
        'labels' => 'array',
        'annotations' => 'array',
        'attributes' => 'array',
        'properties' => 'array',
        'features' => 'array',
        'flags' => 'array',
        'options' => 'array',
        'settings' => 'array',
        'configuration' => 'array',
        'parameters' => 'array',
        'arguments' => 'array',
        'variables' => 'array',
        'constants' => 'array',
        'values' => 'array',
        'data' => 'array',
        'performance_metrics' => 'array',
        'quality_metrics' => 'array',
        'reliability_metrics' => 'array',
        'availability_metrics' => 'array',
        'scalability_metrics' => 'array',
        'security_metrics' => 'array',
        'compliance_metrics' => 'array',
        'business_metrics' => 'array',
        'technical_metrics' => 'array',
        'operational_metrics' => 'array',
        'functional_metrics' => 'array',
        'non_functional_metrics' => 'array',
        'user_metrics' => 'array',
        'customer_metrics' => 'array',
        'financial_metrics' => 'array',
        'cost_metrics' => 'array',
        'revenue_metrics' => 'array',
        'profit_metrics' => 'array',
        'roi_metrics' => 'array',
        'value_metrics' => 'array',
        'impact_metrics' => 'array',
        'benefit_metrics' => 'array',
        'outcome_metrics' => 'array',
        'result_metrics' => 'array',
        'success_metrics' => 'array',
        'failure_metrics' => 'array',
        'error_metrics' => 'array',
        'warning_metrics' => 'array',
        'info_metrics' => 'array',
        'debug_metrics' => 'array',
        'trace_metrics' => 'array',
        'log_metrics' => 'array',
        'audit_metrics' => 'array',
        'monitoring_metrics' => 'array',
        'alerting_metrics' => 'array',
        'notification_metrics' => 'array',
        'communication_metrics' => 'array',
        'integration_metrics' => 'array',
        'synchronization_metrics' => 'array',
        'transformation_metrics' => 'array',
        'validation_metrics' => 'array',
        'verification_metrics' => 'array',
        'authentication_metrics' => 'array',
        'authorization_metrics' => 'array',
        'encryption_metrics' => 'array',
        'decryption_metrics' => 'array',
        'compression_metrics' => 'array',
        'decompression_metrics' => 'array',
        'serialization_metrics' => 'array',
        'deserialization_metrics' => 'array',
        'parsing_metrics' => 'array',
        'formatting_metrics' => 'array',
        'rendering_metrics' => 'array',
        'processing_metrics' => 'array',
        'execution_metrics' => 'array',
        'attempt_number' => 'integer',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
        'retry_delay' => 'integer',
        'retry_backoff' => 'integer',
        'timeout' => 'integer',
        'http_status_code' => 'integer',
        'response_time' => 'integer',
        'processing_time' => 'integer',
        'queue_time' => 'integer',
        'total_time' => 'integer',
        'sequence_number' => 'integer',
        'priority' => 'integer',
        'urgency' => 'integer',
        'importance' => 'integer',
        'severity' => 'integer',
        'performance_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'reliability_score' => 'decimal:2',
        'availability_score' => 'decimal:2',
        'scalability_score' => 'decimal:2',
        'security_score' => 'decimal:2',
        'compliance_score' => 'decimal:2',
        'business_score' => 'decimal:2',
        'technical_score' => 'decimal:2',
        'operational_score' => 'decimal:2',
        'functional_score' => 'decimal:2',
        'non_functional_score' => 'decimal:2',
        'user_score' => 'decimal:2',
        'customer_score' => 'decimal:2',
        'financial_score' => 'decimal:2',
        'cost_score' => 'decimal:2',
        'revenue_score' => 'decimal:2',
        'profit_score' => 'decimal:2',
        'roi_score' => 'decimal:2',
        'value_score' => 'decimal:2',
        'impact_score' => 'decimal:2',
        'benefit_score' => 'decimal:2',
        'outcome_score' => 'decimal:2',
        'result_score' => 'decimal:2',
        'success_score' => 'decimal:2',
        'failure_score' => 'decimal:2',
        'error_score' => 'decimal:2',
        'warning_score' => 'decimal:2',
        'info_score' => 'decimal:2',
        'debug_score' => 'decimal:2',
        'trace_score' => 'decimal:2',
        'log_score' => 'decimal:2',
        'audit_score' => 'decimal:2',
        'monitoring_score' => 'decimal:2',
        'alerting_score' => 'decimal:2',
        'notification_score' => 'decimal:2',
        'communication_score' => 'decimal:2',
        'integration_score' => 'decimal:2',
        'synchronization_score' => 'decimal:2',
        'transformation_score' => 'decimal:2',
        'validation_score' => 'decimal:2',
        'verification_score' => 'decimal:2',
        'authentication_score' => 'decimal:2',
        'authorization_score' => 'decimal:2',
        'encryption_score' => 'decimal:2',
        'decryption_score' => 'decimal:2',
        'compression_score' => 'decimal:2',
        'decompression_score' => 'decimal:2',
        'serialization_score' => 'decimal:2',
        'deserialization_score' => 'decimal:2',
        'parsing_score' => 'decimal:2',
        'formatting_score' => 'decimal:2',
        'rendering_score' => 'decimal:2',
        'processing_score' => 'decimal:2',
        'execution_score' => 'decimal:2',
        'overall_score' => 'decimal:2',
        'health_score' => 'decimal:2',
        'is_processed' => 'boolean',
        'is_verified' => 'boolean',
        'is_authenticated' => 'boolean',
        'is_authorized' => 'boolean',
        'is_valid' => 'boolean',
        'is_complete' => 'boolean',
        'is_successful' => 'boolean',
        'is_failed' => 'boolean',
        'is_partial' => 'boolean',
        'is_skipped' => 'boolean',
        'is_ignored' => 'boolean',
        'is_filtered' => 'boolean',
        'is_blocked' => 'boolean',
        'is_quarantined' => 'boolean',
        'is_flagged' => 'boolean',
        'is_suspicious' => 'boolean',
        'is_malicious' => 'boolean',
        'is_spam' => 'boolean',
        'is_duplicate' => 'boolean',
        'is_outdated' => 'boolean',
        'is_expired' => 'boolean',
        'is_cancelled' => 'boolean',
        'is_rejected' => 'boolean',
        'is_accepted' => 'boolean',
        'is_approved' => 'boolean',
        'is_denied' => 'boolean',
        'is_pending' => 'boolean',
        'is_queued' => 'boolean',
        'is_scheduled' => 'boolean',
        'is_delayed' => 'boolean',
        'is_retried' => 'boolean',
        'is_timeout' => 'boolean',
        'is_rate_limited' => 'boolean',
        'is_throttled' => 'boolean',
        'is_circuit_breaker_open' => 'boolean',
        'is_circuit_breaker_half_open' => 'boolean',
        'is_circuit_breaker_closed' => 'boolean',
        'is_load_balanced' => 'boolean',
        'is_cached' => 'boolean',
        'is_compressed' => 'boolean',
        'is_encrypted' => 'boolean',
        'is_signed' => 'boolean',
        'is_verified_signature' => 'boolean',
        'is_trusted_source' => 'boolean',
        'is_whitelisted' => 'boolean',
        'is_blacklisted' => 'boolean',
        'is_monitored' => 'boolean',
        'is_logged' => 'boolean',
        'is_audited' => 'boolean',
        'is_tracked' => 'boolean',
        'is_traced' => 'boolean',
        'is_debugged' => 'boolean',
        'is_profiled' => 'boolean',
        'is_benchmarked' => 'boolean',
        'is_tested' => 'boolean',
        'is_validated_schema' => 'boolean',
        'is_transformed' => 'boolean',
        'is_normalized' => 'boolean',
        'is_sanitized' => 'boolean',
        'is_filtered_content' => 'boolean',
        'is_enriched' => 'boolean',
        'is_aggregated' => 'boolean',
        'is_summarized' => 'boolean',
        'is_analyzed' => 'boolean',
        'is_classified' => 'boolean',
        'is_categorized' => 'boolean',
        'is_tagged' => 'boolean',
        'is_labeled' => 'boolean',
        'is_annotated' => 'boolean',
        'is_indexed' => 'boolean',
        'is_searchable' => 'boolean',
        'is_discoverable' => 'boolean',
        'is_accessible' => 'boolean',
        'is_available' => 'boolean',
        'is_online' => 'boolean',
        'is_offline' => 'boolean',
        'is_active' => 'boolean',
        'is_inactive' => 'boolean',
        'is_enabled' => 'boolean',
        'is_disabled' => 'boolean',
        'is_visible' => 'boolean',
        'is_hidden' => 'boolean',
        'is_public' => 'boolean',
        'is_private' => 'boolean',
        'is_internal' => 'boolean',
        'is_external' => 'boolean',
        'is_local' => 'boolean',
        'is_remote' => 'boolean',
        'is_cloud' => 'boolean',
        'is_on_premise' => 'boolean',
        'is_hybrid' => 'boolean',
        'is_multi_cloud' => 'boolean',
        'is_edge' => 'boolean',
        'is_mobile' => 'boolean',
        'is_desktop' => 'boolean',
        'is_web' => 'boolean',
        'is_api' => 'boolean',
        'is_service' => 'boolean',
        'is_microservice' => 'boolean',
        'is_serverless' => 'boolean',
        'is_containerized' => 'boolean',
        'is_virtualized' => 'boolean',
        'is_bare_metal' => 'boolean',
        'is_managed' => 'boolean',
        'is_self_hosted' => 'boolean',
        'is_saas' => 'boolean',
        'is_paas' => 'boolean',
        'is_iaas' => 'boolean',
        'is_faas' => 'boolean',
        'is_baas' => 'boolean',
        'is_daas' => 'boolean',
        'is_caas' => 'boolean',
        'is_naas' => 'boolean',
        'is_maas' => 'boolean',
        'is_haas' => 'boolean',
        'is_raas' => 'boolean',
        'is_taas' => 'boolean',
        'is_aaas' => 'boolean',
        'is_xaas' => 'boolean',
        'timestamp' => 'datetime',
        'received_at' => 'datetime',
        'processed_at' => 'datetime',
        'verified_at' => 'datetime',
        'authenticated_at' => 'datetime',
        'authorized_at' => 'datetime',
        'validated_at' => 'datetime',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
        'retried_at' => 'datetime',
        'expired_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'rejected_at' => 'datetime',
        'accepted_at' => 'datetime',
        'approved_at' => 'datetime',
        'denied_at' => 'datetime',
        'queued_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'delayed_at' => 'datetime',
        'timeout_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'event_type', 'resource', 'action', 'status',
                'processing_status', 'is_processed', 'is_successful'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeProcessed($query)
    {
        return $query->where('is_processed', true);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('is_successful', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('is_failed', true);
    }

    public function scopePending($query)
    {
        return $query->where('is_pending', true);
    }

    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    public function scopeByResource($query, $resource)
    {
        return $query->where('resource', $resource);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('received_at', '>=', now()->subHours($hours));
    }

    /**
     * الطرق المساعدة
     */
    public function isProcessed(): bool
    {
        return $this->is_processed;
    }

    public function isSuccessful(): bool
    {
        return $this->is_successful;
    }

    public function isFailed(): bool
    {
        return $this->is_failed;
    }

    public function isPending(): bool
    {
        return $this->is_pending;
    }

    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    public function isAuthenticated(): bool
    {
        return $this->is_authenticated;
    }

    public function isAuthorized(): bool
    {
        return $this->is_authorized;
    }

    public function isValid(): bool
    {
        return $this->is_valid;
    }

    public function getPayload(): array
    {
        return $this->payload ?? [];
    }

    public function getRawPayload(): array
    {
        return $this->raw_payload ?? [];
    }

    public function getParsedPayload(): array
    {
        return $this->parsed_payload ?? [];
    }

    public function getProcessedPayload(): array
    {
        return $this->processed_payload ?? [];
    }

    public function getHeaders(): array
    {
        return $this->headers ?? [];
    }

    public function getMetadata(): array
    {
        return $this->metadata ?? [];
    }

    public function getContext(): array
    {
        return $this->context ?? [];
    }

    public function getResponseTime(): int
    {
        return $this->response_time ?? 0;
    }

    public function getProcessingTime(): int
    {
        return $this->processing_time ?? 0;
    }

    public function getTotalTime(): int
    {
        return $this->total_time ?? 0;
    }

    public function getAttemptNumber(): int
    {
        return $this->attempt_number ?? 1;
    }

    public function getRetryCount(): int
    {
        return $this->retry_count ?? 0;
    }

    public function getMaxRetries(): int
    {
        return $this->max_retries ?? 3;
    }

    public function canRetry(): bool
    {
        return $this->getRetryCount() < $this->getMaxRetries();
    }

    public function shouldRetry(): bool
    {
        return $this->isFailed() && $this->canRetry();
    }

    public function getPerformanceScore(): float
    {
        return $this->performance_score ?? 0;
    }

    public function getQualityScore(): float
    {
        return $this->quality_score ?? 0;
    }

    public function getReliabilityScore(): float
    {
        return $this->reliability_score ?? 0;
    }

    public function getSecurityScore(): float
    {
        return $this->security_score ?? 0;
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getPerformanceScore(),
            $this->getQualityScore(),
            $this->getReliabilityScore(),
            $this->getSecurityScore(),
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        if ($this->isFailed()) {
            return 'critical';
        }

        if ($this->isPending()) {
            return 'fair';
        }

        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'webhook_id' => $this->webhook_id,
            'event_type' => $this->event_type,
            'resource' => $this->resource,
            'action' => $this->action,
            'status' => $this->status,
            'processing_status' => $this->processing_status,
            'received_at' => $this->received_at,
            'processed_at' => $this->processed_at,
            'response_time' => $this->getResponseTime(),
            'processing_time' => $this->getProcessingTime(),
            'total_time' => $this->getTotalTime(),
            'attempt_number' => $this->getAttemptNumber(),
            'retry_count' => $this->getRetryCount(),
            'is_processed' => $this->isProcessed(),
            'is_successful' => $this->isSuccessful(),
            'is_failed' => $this->isFailed(),
            'is_verified' => $this->isVerified(),
            'can_retry' => $this->canRetry(),
            'should_retry' => $this->shouldRetry(),
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
