<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الشركة
 */
class Company extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'legal_name',
        'tax_number',
        'commercial_register',
        'industry',
        'company_type',
        'establishment_date',
        'capital',
        'currency',
        'fiscal_year_start',
        'fiscal_year_end',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'website',
        'logo',
        'legal_representative',
        'contact_person',
        'contact_email',
        'contact_phone',
        'bank_details',
        'tax_settings',
        'accounting_settings',
        'is_active',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'establishment_date' => 'datetime',
        'fiscal_year_start' => 'datetime',
        'fiscal_year_end' => 'datetime',
        'capital' => 'decimal:2',
        'address' => 'array',
        'bank_details' => 'array',
        'tax_settings' => 'array',
        'accounting_settings' => 'array',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * أنواع الشركات
     */
    public const COMPANY_TYPES = [
        'SARL' => 'شركة ذات مسؤولية محدودة',
        'SA' => 'شركة مساهمة',
        'SNC' => 'شركة التضامن',
        'SCS' => 'شركة التوصية البسيطة',
        'SCA' => 'شركة التوصية بالأسهم',
        'SOLE_PROPRIETORSHIP' => 'مؤسسة فردية',
        'PARTNERSHIP' => 'شراكة',
        'COOPERATIVE' => 'تعاونية',
        'NON_PROFIT' => 'منظمة غير ربحية',
    ];

    /**
     * القطاعات الصناعية
     */
    public const INDUSTRIES = [
        'AGRICULTURE' => 'الزراعة',
        'MANUFACTURING' => 'التصنيع',
        'CONSTRUCTION' => 'البناء والتشييد',
        'RETAIL' => 'التجارة بالتجزئة',
        'WHOLESALE' => 'التجارة بالجملة',
        'TRANSPORTATION' => 'النقل',
        'FINANCE' => 'المالية',
        'REAL_ESTATE' => 'العقارات',
        'TECHNOLOGY' => 'التكنولوجيا',
        'HEALTHCARE' => 'الرعاية الصحية',
        'EDUCATION' => 'التعليم',
        'HOSPITALITY' => 'الضيافة',
        'ENERGY' => 'الطاقة',
        'TELECOMMUNICATIONS' => 'الاتصالات',
        'MEDIA' => 'الإعلام',
        'CONSULTING' => 'الاستشارات',
        'OTHER' => 'أخرى',
    ];

    /**
     * العملات المدعومة
     */
    public const SUPPORTED_CURRENCIES = [
        'MAD' => 'درهم مغربي',
        'USD' => 'دولار أمريكي',
        'EUR' => 'يورو',
        'SAR' => 'ريال سعودي',
        'AED' => 'درهم إماراتي',
        'GBP' => 'جنيه إسترليني',
    ];

    /**
     * الأقسام
     */
    public function departments(): HasMany
    {
        return $this->hasMany(Department::class);
    }

    /**
     * الموظفين
     */
    public function employees(): HasMany
    {
        return $this->hasMany(\App\Domains\HR\Models\Employee::class);
    }

    /**
     * الإقرارات الضريبية
     */
    public function taxReturns(): HasMany
    {
        return $this->hasMany(\App\Domains\Taxation\Models\TaxReturn::class);
    }

    /**
     * من أنشأ الشركة
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * من حدث الشركة
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * الحصول على اسم نوع الشركة
     */
    public function getCompanyTypeNameAttribute(): string
    {
        return self::COMPANY_TYPES[$this->company_type] ?? $this->company_type;
    }

    /**
     * الحصول على اسم القطاع
     */
    public function getIndustryNameAttribute(): string
    {
        return self::INDUSTRIES[$this->industry] ?? $this->industry;
    }

    /**
     * الحصول على اسم العملة
     */
    public function getCurrencyNameAttribute(): string
    {
        return self::SUPPORTED_CURRENCIES[$this->currency] ?? $this->currency;
    }

    /**
     * فحص إذا كانت الشركة نشطة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * الحصول على عمر الشركة بالسنوات
     */
    public function getAgeAttribute(): int
    {
        return $this->establishment_date ?
            $this->establishment_date->diffInYears(now()) : 0;
    }

    /**
     * الحصول على عدد الموظفين
     */
    public function getEmployeesCountAttribute(): int
    {
        return $this->employees()->count();
    }

    /**
     * الحصول على عدد الموظفين النشطين
     */
    public function getActiveEmployeesCountAttribute(): int
    {
        return $this->employees()->where('is_active', true)->count();
    }

    /**
     * الحصول على عدد الأقسام
     */
    public function getDepartmentsCountAttribute(): int
    {
        return $this->departments()->count();
    }

    /**
     * فحص إذا كانت السنة المالية الحالية
     */
    public function isCurrentFiscalYear(): bool
    {
        $now = now();
        return $now->between($this->fiscal_year_start, $this->fiscal_year_end);
    }

    /**
     * الحصول على السنة المالية الحالية
     */
    public function getCurrentFiscalYear(): array
    {
        $now = now();
        $start = $this->fiscal_year_start->copy();
        $end = $this->fiscal_year_end->copy();

        // إذا كنا في السنة المالية الحالية
        if ($now->between($start, $end)) {
            return [
                'start' => $start,
                'end' => $end,
                'year' => $start->year,
            ];
        }

        // إذا كنا بعد انتهاء السنة المالية، احسب السنة التالية
        if ($now->gt($end)) {
            $start = $start->addYear();
            $end = $end->addYear();
        }

        return [
            'start' => $start,
            'end' => $end,
            'year' => $start->year,
        ];
    }

    /**
     * تفعيل الشركة
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * إلغاء تفعيل الشركة
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * تحديث رأس المال
     */
    public function updateCapital(float $newCapital): void
    {
        $this->update(['capital' => $newCapital]);
    }

    /**
     * تحديث السنة المالية
     */
    public function updateFiscalYear(string $startDate, string $endDate): void
    {
        $this->update([
            'fiscal_year_start' => $startDate,
            'fiscal_year_end' => $endDate,
        ]);
    }

    /**
     * تحديث الإعدادات الضريبية
     */
    public function updateTaxSettings(array $settings): void
    {
        $this->update(['tax_settings' => $settings]);
    }

    /**
     * تحديث الإعدادات المحاسبية
     */
    public function updateAccountingSettings(array $settings): void
    {
        $this->update(['accounting_settings' => $settings]);
    }

    /**
     * Scope للشركات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للشركات غير النشطة
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope للشركات حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('company_type', $type);
    }

    /**
     * Scope للشركات حسب القطاع
     */
    public function scopeInIndustry($query, string $industry)
    {
        return $query->where('industry', $industry);
    }

    /**
     * Scope للبحث
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('legal_name', 'like', "%{$search}%")
              ->orWhere('tax_number', 'like', "%{$search}%")
              ->orWhere('commercial_register', 'like', "%{$search}%");
        });
    }
}
