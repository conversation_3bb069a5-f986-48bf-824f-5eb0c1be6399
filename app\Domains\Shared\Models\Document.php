<?php

namespace App\Domains\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج الوثيقة
 */
class Document extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'documentable_type',
        'documentable_id',
        'title',
        'description',
        'file_name',
        'file_path',
        'file_size',
        'file_type',
        'mime_type',
        'category',
        'tags',
        'is_public',
        'is_required',
        'version',
        'status',
        'uploaded_by',
        'approved_by',
        'approved_at',
        'expires_at',
        'metadata',
    ];

    protected $casts = [
        'tags' => 'array',
        'metadata' => 'array',
        'is_public' => 'boolean',
        'is_required' => 'boolean',
        'file_size' => 'integer',
        'approved_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * فئات الوثائق
     */
    public const CATEGORIES = [
        'CONTRACT' => 'عقد',
        'INVOICE' => 'فاتورة',
        'RECEIPT' => 'إيصال',
        'CERTIFICATE' => 'شهادة',
        'LICENSE' => 'رخصة',
        'PERMIT' => 'تصريح',
        'REPORT' => 'تقرير',
        'POLICY' => 'سياسة',
        'PROCEDURE' => 'إجراء',
        'FORM' => 'نموذج',
        'LETTER' => 'خطاب',
        'MEMO' => 'مذكرة',
        'PRESENTATION' => 'عرض تقديمي',
        'SPREADSHEET' => 'جدول بيانات',
        'IMAGE' => 'صورة',
        'VIDEO' => 'فيديو',
        'AUDIO' => 'صوت',
        'OTHER' => 'أخرى',
    ];

    /**
     * حالات الوثيقة
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING_REVIEW' => 'في انتظار المراجعة',
        'APPROVED' => 'معتمدة',
        'REJECTED' => 'مرفوضة',
        'ARCHIVED' => 'مؤرشفة',
        'EXPIRED' => 'منتهية الصلاحية',
    ];

    /**
     * العنصر المرتبط بالوثيقة
     */
    public function documentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * من قام بالرفع
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'uploaded_by');
    }

    /**
     * من قام بالاعتماد
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * الحصول على اسم الفئة
     */
    public function getCategoryNameAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على حجم الملف المنسق
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'غير محدد';
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * فحص إذا كانت الوثيقة معتمدة
     */
    public function isApproved(): bool
    {
        return $this->status === 'APPROVED';
    }

    /**
     * فحص إذا كانت الوثيقة منتهية الصلاحية
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * فحص إذا كانت الوثيقة صورة
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * فحص إذا كانت الوثيقة PDF
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * فحص إذا كانت الوثيقة مستند Word
     */
    public function isWordDocument(): bool
    {
        return in_array($this->mime_type, [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]);
    }

    /**
     * فحص إذا كانت الوثيقة جدول Excel
     */
    public function isExcelDocument(): bool
    {
        return in_array($this->mime_type, [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]);
    }

    /**
     * الحصول على رابط التحميل
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('documents.download', $this->id);
    }

    /**
     * الحصول على رابط المعاينة
     */
    public function getPreviewUrlAttribute(): ?string
    {
        if ($this->isImage() || $this->isPdf()) {
            return route('documents.preview', $this->id);
        }
        
        return null;
    }

    /**
     * اعتماد الوثيقة
     */
    public function approve(int $approvedBy): void
    {
        $this->update([
            'status' => 'APPROVED',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);
    }

    /**
     * رفض الوثيقة
     */
    public function reject(): void
    {
        $this->update([
            'status' => 'REJECTED',
        ]);
    }

    /**
     * أرشفة الوثيقة
     */
    public function archive(): void
    {
        $this->update([
            'status' => 'ARCHIVED',
        ]);
    }

    /**
     * Scope للوثائق المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * Scope للوثائق العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope للوثائق حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope للوثائق غير المنتهية الصلاحية
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope للبحث في الوثائق
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('file_name', 'like', "%{$search}%");
        });
    }
}
