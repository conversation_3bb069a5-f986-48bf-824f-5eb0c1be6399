<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Domains\Projects\Models\Recognition;

/**
 * إشعار استلام الاعتراف - Recognition Received Notification
 */
class RecognitionReceivedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Recognition $recognition;

    public function __construct(Recognition $recognition)
    {
        $this->recognition = $recognition;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('تهانينا! لقد حصلت على اعتراف')
                    ->line('تهانينا! لقد حصلت على اعتراف من ' . $this->recognition->giver->name)
                    ->line('نوع الاعتراف: ' . $this->recognition->type)
                    ->line('الرسالة: ' . $this->recognition->message)
                    ->line('النقاط المكتسبة: ' . $this->recognition->points)
                    ->action('عرض التفاصيل', url('/projects/' . $this->recognition->project_id))
                    ->line('استمر في العمل الرائع!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'recognition_received',
            'recognition_id' => $this->recognition->id,
            'project_id' => $this->recognition->project_id,
            'giver_name' => $this->recognition->giver->name,
            'recognition_type' => $this->recognition->type,
            'message' => $this->recognition->message,
            'points' => $this->recognition->points,
        ];
    }
}
