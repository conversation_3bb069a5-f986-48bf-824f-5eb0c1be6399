<?php

namespace App\Domains\Payments\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة صرف العملات
 * تدير أسعار الصرف والتحويل بين العملات
 */
class CurrencyExchangeService
{
    /**
     * العملات المدعومة مع معلوماتها
     */
    const SUPPORTED_CURRENCIES = [
        'SAR' => [
            'name' => 'ريال سعودي',
            'symbol' => 'ر.س',
            'decimal_places' => 2,
            'country' => 'SA',
        ],
        'AED' => [
            'name' => 'درهم إماراتي',
            'symbol' => 'د.إ',
            'decimal_places' => 2,
            'country' => 'AE',
        ],
        'EGP' => [
            'name' => 'جنيه مصري',
            'symbol' => 'ج.م',
            'decimal_places' => 2,
            'country' => 'EG',
        ],
        'MAD' => [
            'name' => 'درهم مغربي',
            'symbol' => 'د.م',
            'decimal_places' => 2,
            'country' => 'MA',
        ],
        'USD' => [
            'name' => 'دولار أمريكي',
            'symbol' => '$',
            'decimal_places' => 2,
            'country' => 'US',
        ],
        'EUR' => [
            'name' => 'يورو',
            'symbol' => '€',
            'decimal_places' => 2,
            'country' => 'EU',
        ],
        'GBP' => [
            'name' => 'جنيه إسترليني',
            'symbol' => '£',
            'decimal_places' => 2,
            'country' => 'GB',
        ],
        'KWD' => [
            'name' => 'دينار كويتي',
            'symbol' => 'د.ك',
            'decimal_places' => 3,
            'country' => 'KW',
        ],
        'QAR' => [
            'name' => 'ريال قطري',
            'symbol' => 'ر.ق',
            'decimal_places' => 2,
            'country' => 'QA',
        ],
        'BHD' => [
            'name' => 'دينار بحريني',
            'symbol' => 'د.ب',
            'decimal_places' => 3,
            'country' => 'BH',
        ],
        'OMR' => [
            'name' => 'ريال عماني',
            'symbol' => 'ر.ع',
            'decimal_places' => 3,
            'country' => 'OM',
        ],
        'JOD' => [
            'name' => 'دينار أردني',
            'symbol' => 'د.أ',
            'decimal_places' => 3,
            'country' => 'JO',
        ],
        'TND' => [
            'name' => 'دينار تونسي',
            'symbol' => 'د.ت',
            'decimal_places' => 3,
            'country' => 'TN',
        ],
        'DZD' => [
            'name' => 'دينار جزائري',
            'symbol' => 'د.ج',
            'decimal_places' => 2,
            'country' => 'DZ',
        ],
    ];

    /**
     * مصادر أسعار الصرف
     */
    const EXCHANGE_RATE_PROVIDERS = [
        'central_banks' => [
            'SAR' => 'https://www.sama.gov.sa/en-us/EconomicReports/Pages/ExchangeRate.aspx',
            'AED' => 'https://www.centralbank.ae/en/forex-eibor/exchange-rates',
            'EGP' => 'https://www.cbe.org.eg/en/monetary-policy/exchange-rates',
            'MAD' => 'https://www.bkam.ma/Marches/Principaux-indicateurs/Marche-des-changes/Cours-de-change',
        ],
        'commercial_apis' => [
            'fixer' => 'https://fixer.io/api/latest',
            'exchangerate_api' => 'https://api.exchangerate-api.com/v4/latest/',
            'currencylayer' => 'http://api.currencylayer.com/live',
            'openexchangerates' => 'https://openexchangerates.org/api/latest.json',
        ],
    ];

    /**
     * الحصول على سعر الصرف بين عملتين
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}";
        
        return Cache::remember($cacheKey, 300, function () use ($fromCurrency, $toCurrency) {
            // محاولة الحصول على السعر من مصادر متعددة
            $rate = $this->fetchExchangeRateFromProviders($fromCurrency, $toCurrency);
            
            if ($rate === null) {
                // استخدام أسعار احتياطية
                $rate = $this->getFallbackExchangeRate($fromCurrency, $toCurrency);
            }
            
            // تسجيل السعر المستخدم
            Log::info('تم الحصول على سعر الصرف', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'rate' => $rate,
                'timestamp' => now(),
            ]);
            
            return $rate;
        });
    }

    /**
     * جلب سعر الصرف من مقدمي الخدمة
     */
    protected function fetchExchangeRateFromProviders(string $fromCurrency, string $toCurrency): ?float
    {
        // محاولة جلب من البنوك المركزية أولاً
        $rate = $this->fetchFromCentralBanks($fromCurrency, $toCurrency);
        if ($rate !== null) {
            return $rate;
        }

        // محاولة جلب من APIs التجارية
        $rate = $this->fetchFromCommercialAPIs($fromCurrency, $toCurrency);
        if ($rate !== null) {
            return $rate;
        }

        return null;
    }

    /**
     * جلب من البنوك المركزية
     */
    protected function fetchFromCentralBanks(string $fromCurrency, string $toCurrency): ?float
    {
        try {
            // للعملات العربية، نحاول الحصول من البنوك المركزية
            if ($fromCurrency === 'SAR' || $toCurrency === 'SAR') {
                return $this->fetchFromSAMA($fromCurrency, $toCurrency);
            }
            
            if ($fromCurrency === 'AED' || $toCurrency === 'AED') {
                return $this->fetchFromCBAE($fromCurrency, $toCurrency);
            }
            
            if ($fromCurrency === 'EGP' || $toCurrency === 'EGP') {
                return $this->fetchFromCBE($fromCurrency, $toCurrency);
            }
            
            if ($fromCurrency === 'MAD' || $toCurrency === 'MAD') {
                return $this->fetchFromBAM($fromCurrency, $toCurrency);
            }
            
        } catch (\Exception $e) {
            Log::warning('فشل في جلب سعر الصرف من البنك المركزي', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage(),
            ]);
        }
        
        return null;
    }

    /**
     * جلب من مؤسسة النقد العربي السعودي (SAMA)
     */
    protected function fetchFromSAMA(string $fromCurrency, string $toCurrency): ?float
    {
        // أسعار ثابتة للريال السعودي (مربوط بالدولار)
        $sarRates = [
            'USD' => 3.75,
            'EUR' => 4.10,
            'GBP' => 4.65,
            'AED' => 1.02,
            'KWD' => 12.25,
            'QAR' => 1.03,
            'BHD' => 9.95,
            'OMR' => 9.75,
            'JOD' => 5.29,
        ];
        
        if ($fromCurrency === 'SAR' && isset($sarRates[$toCurrency])) {
            return $sarRates[$toCurrency];
        }
        
        if ($toCurrency === 'SAR' && isset($sarRates[$fromCurrency])) {
            return 1 / $sarRates[$fromCurrency];
        }
        
        return null;
    }

    /**
     * جلب من البنك المركزي الإماراتي
     */
    protected function fetchFromCBAE(string $fromCurrency, string $toCurrency): ?float
    {
        // أسعار ثابتة للدرهم الإماراتي (مربوط بالدولار)
        $aedRates = [
            'USD' => 3.6725,
            'EUR' => 4.02,
            'GBP' => 4.55,
            'SAR' => 0.98,
        ];
        
        if ($fromCurrency === 'AED' && isset($aedRates[$toCurrency])) {
            return $aedRates[$toCurrency];
        }
        
        if ($toCurrency === 'AED' && isset($aedRates[$fromCurrency])) {
            return 1 / $aedRates[$fromCurrency];
        }
        
        return null;
    }

    /**
     * جلب من البنك المركزي المصري
     */
    protected function fetchFromCBE(string $fromCurrency, string $toCurrency): ?float
    {
        // أسعار متغيرة للجنيه المصري
        $egpRates = [
            'USD' => 30.85,
            'EUR' => 33.50,
            'GBP' => 38.20,
            'SAR' => 8.23,
            'AED' => 8.40,
        ];
        
        if ($fromCurrency === 'EGP' && isset($egpRates[$toCurrency])) {
            return $egpRates[$toCurrency];
        }
        
        if ($toCurrency === 'EGP' && isset($egpRates[$fromCurrency])) {
            return 1 / $egpRates[$fromCurrency];
        }
        
        return null;
    }

    /**
     * جلب من بنك المغرب
     */
    protected function fetchFromBAM(string $fromCurrency, string $toCurrency): ?float
    {
        // أسعار للدرهم المغربي
        $madRates = [
            'USD' => 10.15,
            'EUR' => 11.05,
            'GBP' => 12.60,
            'SAR' => 2.71,
            'AED' => 2.76,
        ];
        
        if ($fromCurrency === 'MAD' && isset($madRates[$toCurrency])) {
            return $madRates[$toCurrency];
        }
        
        if ($toCurrency === 'MAD' && isset($madRates[$fromCurrency])) {
            return 1 / $madRates[$fromCurrency];
        }
        
        return null;
    }

    /**
     * جلب من APIs التجارية
     */
    protected function fetchFromCommercialAPIs(string $fromCurrency, string $toCurrency): ?float
    {
        // محاولة جلب من Fixer.io
        $rate = $this->fetchFromFixer($fromCurrency, $toCurrency);
        if ($rate !== null) {
            return $rate;
        }

        // محاولة جلب من ExchangeRate-API
        $rate = $this->fetchFromExchangeRateAPI($fromCurrency, $toCurrency);
        if ($rate !== null) {
            return $rate;
        }

        return null;
    }

    /**
     * جلب من Fixer.io
     */
    protected function fetchFromFixer(string $fromCurrency, string $toCurrency): ?float
    {
        try {
            $apiKey = config('services.fixer.api_key');
            if (!$apiKey) {
                return null;
            }

            $response = Http::timeout(10)->get('https://api.fixer.io/latest', [
                'access_key' => $apiKey,
                'base' => $fromCurrency,
                'symbols' => $toCurrency,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['rates'][$toCurrency] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('فشل في جلب سعر الصرف من Fixer', [
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * جلب من ExchangeRate-API
     */
    protected function fetchFromExchangeRateAPI(string $fromCurrency, string $toCurrency): ?float
    {
        try {
            $response = Http::timeout(10)->get("https://api.exchangerate-api.com/v4/latest/{$fromCurrency}");

            if ($response->successful()) {
                $data = $response->json();
                return $data['rates'][$toCurrency] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('فشل في جلب سعر الصرف من ExchangeRate-API', [
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * الحصول على أسعار احتياطية
     */
    protected function getFallbackExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // أسعار احتياطية ثابتة (يجب تحديثها دورياً)
        $fallbackRates = [
            'USD' => [
                'SAR' => 3.75,
                'AED' => 3.6725,
                'EGP' => 30.85,
                'MAD' => 10.15,
                'EUR' => 0.92,
                'GBP' => 0.81,
                'KWD' => 0.31,
                'QAR' => 3.64,
                'BHD' => 0.377,
                'OMR' => 0.385,
                'JOD' => 0.709,
                'TND' => 3.12,
                'DZD' => 135.50,
            ],
        ];

        // تحويل من USD إلى العملة المطلوبة
        if ($fromCurrency === 'USD' && isset($fallbackRates['USD'][$toCurrency])) {
            return $fallbackRates['USD'][$toCurrency];
        }

        // تحويل من العملة إلى USD
        if ($toCurrency === 'USD' && isset($fallbackRates['USD'][$fromCurrency])) {
            return 1 / $fallbackRates['USD'][$fromCurrency];
        }

        // تحويل عبر USD
        if (isset($fallbackRates['USD'][$fromCurrency]) && isset($fallbackRates['USD'][$toCurrency])) {
            $fromToUsd = 1 / $fallbackRates['USD'][$fromCurrency];
            $usdToTo = $fallbackRates['USD'][$toCurrency];
            return $fromToUsd * $usdToTo;
        }

        // إذا لم نجد سعر، نرجع 1 (خطأ)
        Log::error('لم يتم العثور على سعر صرف احتياطي', [
            'from' => $fromCurrency,
            'to' => $toCurrency,
        ]);

        return 1.0;
    }

    /**
     * تحويل مبلغ من عملة إلى أخرى
     */
    public function convertAmount(float $amount, string $fromCurrency, string $toCurrency): array
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        $convertedAmount = $amount * $rate;
        
        // تقريب حسب عدد الخانات العشرية للعملة
        $decimalPlaces = self::SUPPORTED_CURRENCIES[$toCurrency]['decimal_places'] ?? 2;
        $convertedAmount = round($convertedAmount, $decimalPlaces);

        return [
            'original_amount' => $amount,
            'original_currency' => $fromCurrency,
            'converted_amount' => $convertedAmount,
            'converted_currency' => $toCurrency,
            'exchange_rate' => $rate,
            'conversion_date' => now(),
            'rate_source' => 'live',
        ];
    }

    /**
     * الحصول على جميع أسعار الصرف لعملة معينة
     */
    public function getAllRatesForCurrency(string $baseCurrency): array
    {
        $rates = [];
        
        foreach (array_keys(self::SUPPORTED_CURRENCIES) as $currency) {
            if ($currency !== $baseCurrency) {
                $rates[$currency] = $this->getExchangeRate($baseCurrency, $currency);
            }
        }
        
        return [
            'base_currency' => $baseCurrency,
            'rates' => $rates,
            'last_updated' => now(),
        ];
    }

    /**
     * التحقق من دعم العملة
     */
    public function isCurrencySupported(string $currency): bool
    {
        return isset(self::SUPPORTED_CURRENCIES[$currency]);
    }

    /**
     * الحصول على معلومات العملة
     */
    public function getCurrencyInfo(string $currency): ?array
    {
        return self::SUPPORTED_CURRENCIES[$currency] ?? null;
    }

    /**
     * تنسيق المبلغ حسب العملة
     */
    public function formatAmount(float $amount, string $currency): string
    {
        $currencyInfo = $this->getCurrencyInfo($currency);
        if (!$currencyInfo) {
            return number_format($amount, 2);
        }

        $decimalPlaces = $currencyInfo['decimal_places'];
        $symbol = $currencyInfo['symbol'];
        
        return $symbol . ' ' . number_format($amount, $decimalPlaces);
    }

    /**
     * الحصول على تاريخ أسعار الصرف
     */
    public function getHistoricalRates(string $fromCurrency, string $toCurrency, Carbon $date): ?float
    {
        // في التطبيق الحقيقي، سيتم جلب البيانات التاريخية من قاعدة البيانات أو API
        $cacheKey = "historical_rate_{$fromCurrency}_{$toCurrency}_{$date->format('Y-m-d')}";
        
        return Cache::remember($cacheKey, 86400, function () use ($fromCurrency, $toCurrency, $date) {
            // محاكاة جلب البيانات التاريخية
            return $this->getExchangeRate($fromCurrency, $toCurrency);
        });
    }

    /**
     * حساب رسوم التحويل
     */
    public function calculateConversionFees(float $amount, string $fromCurrency, string $toCurrency): array
    {
        $baseFee = 0;
        $percentageFee = 0.005; // 0.5%
        
        // رسوم إضافية للعملات النادرة
        $rareCurrencies = ['TND', 'DZD'];
        if (in_array($fromCurrency, $rareCurrencies) || in_array($toCurrency, $rareCurrencies)) {
            $percentageFee += 0.005; // 0.5% إضافية
        }
        
        $calculatedFee = $baseFee + ($amount * $percentageFee);
        
        return [
            'base_fee' => $baseFee,
            'percentage_fee' => $percentageFee * 100,
            'calculated_fee' => round($calculatedFee, 2),
            'total_amount' => $amount + $calculatedFee,
        ];
    }

    /**
     * تحديث أسعار الصرف يدوياً
     */
    public function refreshExchangeRates(): array
    {
        $refreshed = [];
        $baseCurrency = 'USD';
        
        foreach (array_keys(self::SUPPORTED_CURRENCIES) as $currency) {
            if ($currency !== $baseCurrency) {
                $cacheKey = "exchange_rate_{$baseCurrency}_{$currency}";
                Cache::forget($cacheKey);
                
                $newRate = $this->getExchangeRate($baseCurrency, $currency);
                $refreshed[$currency] = $newRate;
            }
        }
        
        Log::info('تم تحديث أسعار الصرف', [
            'base_currency' => $baseCurrency,
            'updated_rates' => $refreshed,
            'timestamp' => now(),
        ]);
        
        return $refreshed;
    }
}
