<?php

namespace App\Domains\Shared\Services;

/**
 * Google 2FA Service
 * خدمة المصادقة الثنائية من Google
 */
class Google2FA
{
    /**
     * توليد مفتاح سري جديد
     */
    public function generateSecretKey(int $length = 32): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';

        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }

        return $secret;
    }

    /**
     * توليد QR Code URL
     */
    public function getQRCodeUrl(string $company, string $holder, string $secret): string
    {
        $encodedCompany = urlencode($company);
        $encodedHolder = urlencode($holder);
        $encodedSecret = urlencode($secret);

        $otpAuthUrl = "otpauth://totp/{$encodedCompany}:{$encodedHolder}?secret={$encodedSecret}&issuer={$encodedCompany}";

        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($otpAuthUrl);
    }

    /**
     * التحقق من الرمز
     */
    public function verifyKey(string $secret, string $key, int $window = 1): bool
    {
        $timeSlice = floor(time() / 30);

        for ($i = -$window; $i <= $window; $i++) {
            $calculatedKey = $this->calculateCode($secret, $timeSlice + $i);
            if ($calculatedKey === $key) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على الرمز الحالي
     */
    public function getCurrentOtp(string $secret): string
    {
        $timeSlice = floor(time() / 30);
        return $this->calculateCode($secret, $timeSlice);
    }

    /**
     * حساب الرمز
     */
    protected function calculateCode(string $secret, int $timeSlice): string
    {
        $secretKey = $this->base32Decode($secret);

        // تحويل الوقت إلى binary
        $time = pack('N*', 0) . pack('N*', $timeSlice);

        // حساب HMAC
        $hash = hash_hmac('sha1', $time, $secretKey, true);

        // استخراج الرمز
        $offset = ord($hash[19]) & 0xf;
        $code = (
            ((ord($hash[$offset + 0]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % 1000000;

        return str_pad($code, 6, '0', STR_PAD_LEFT);
    }

    /**
     * فك تشفير Base32
     */
    protected function base32Decode(string $secret): string
    {
        $base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $base32charsFlipped = array_flip(str_split($base32chars));

        $paddingCharCount = substr_count($secret, '=');
        $allowedValues = [6, 4, 3, 1, 0];

        if (!in_array($paddingCharCount, $allowedValues)) {
            return false;
        }

        for ($i = 0; $i < 4; $i++) {
            if ($paddingCharCount == $allowedValues[$i] &&
                substr($secret, -($allowedValues[$i])) != str_repeat('=', $allowedValues[$i])) {
                return false;
            }
        }

        $secret = str_replace('=', '', $secret);
        $secret = str_split($secret);
        $binaryString = '';

        for ($i = 0; $i < count($secret); $i += 8) {
            $x = '';
            if (!in_array($secret[$i], $base32charsFlipped)) {
                return false;
            }
            for ($j = 0; $j < 8; $j++) {
                $x .= str_pad(base_convert(@$base32charsFlipped[@$secret[$i + $j]], 10, 2), 5, '0', STR_PAD_LEFT);
            }
            $eightBits = str_split($x, 8);
            for ($z = 0; $z < count($eightBits); $z++) {
                $binaryString .= (($y = chr(base_convert($eightBits[$z], 2, 10))) || ord($y) == 48) ? $y : '';
            }
        }

        return $binaryString;
    }

    /**
     * تشفير Base32
     */
    protected function base32Encode(string $input): string
    {
        $base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

        if (empty($input)) {
            return '';
        }

        $input = str_split($input);
        $binaryString = '';

        for ($i = 0; $i < count($input); $i++) {
            $binaryString .= str_pad(base_convert(ord($input[$i]), 10, 2), 8, '0', STR_PAD_LEFT);
        }

        $fiveBitBinaryArray = str_split($binaryString, 5);
        $base32 = '';
        $i = 0;

        while ($i < count($fiveBitBinaryArray)) {
            $base32 .= $base32chars[base_convert(str_pad($fiveBitBinaryArray[$i], 5, '0'), 2, 10)];
            $i++;
        }

        return $base32;
    }

    /**
     * إنشاء backup codes
     */
    public function generateBackupCodes(int $count = 10): array
    {
        $codes = [];

        for ($i = 0; $i < $count; $i++) {
            $codes[] = strtoupper(bin2hex(random_bytes(4)));
        }

        return $codes;
    }

    /**
     * التحقق من backup code
     */
    public function verifyBackupCode(array $backupCodes, string $code): bool
    {
        return in_array(strtoupper($code), $backupCodes);
    }

    /**
     * إزالة backup code مستخدم
     */
    public function removeUsedBackupCode(array $backupCodes, string $code): array
    {
        return array_values(array_filter($backupCodes, fn($backupCode) => $backupCode !== strtoupper($code)));
    }

    /**
     * التحقق من صحة المفتاح السري
     */
    public function isValidSecret(string $secret): bool
    {
        return preg_match('/^[A-Z2-7]+$/', $secret) && strlen($secret) >= 16;
    }

    /**
     * تنظيف المفتاح السري
     */
    public function sanitizeSecret(string $secret): string
    {
        return strtoupper(preg_replace('/[^A-Z2-7]/', '', $secret));
    }
}
