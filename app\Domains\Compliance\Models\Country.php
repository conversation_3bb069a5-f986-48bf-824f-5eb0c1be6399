<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Collection;

/**
 * نموذج الدولة مع الأنظمة القانونية والضريبية
 * يدير جميع المتطلبات القانونية والامتثال لكل دولة
 */
class Country extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name_ar',
        'name_en',
        'name_fr',
        'currency',
        'timezone',
        'locale',
        'accounting_system',
        'tax_system',
        'social_security_system',
        'e_invoicing_system',
        'banking_system',
        'regulatory_framework',
        'compliance_requirements',
        'api_integrations',
        'dashboard_config',
        'localization_settings',
        'business_rules',
        'reporting_requirements',
        'audit_requirements',
        'data_retention_policy',
        'privacy_regulations',
        'is_active',
        'implementation_date',
        'last_updated_regulations',
    ];

    protected $casts = [
        'accounting_system' => 'array',
        'tax_system' => 'array',
        'social_security_system' => 'array',
        'e_invoicing_system' => 'array',
        'banking_system' => 'array',
        'regulatory_framework' => 'array',
        'compliance_requirements' => 'array',
        'api_integrations' => 'array',
        'dashboard_config' => 'array',
        'localization_settings' => 'array',
        'business_rules' => 'array',
        'reporting_requirements' => 'array',
        'audit_requirements' => 'array',
        'data_retention_policy' => 'array',
        'privacy_regulations' => 'array',
        'is_active' => 'boolean',
        'implementation_date' => 'date',
        'last_updated_regulations' => 'datetime',
    ];

    /**
     * الدول المدعومة مع تكوينها الكامل
     */
    const SUPPORTED_COUNTRIES = [
        'MA' => [
            'name_ar' => 'المملكة المغربية',
            'name_en' => 'Kingdom of Morocco',
            'name_fr' => 'Royaume du Maroc',
            'currency' => 'MAD',
            'timezone' => 'Africa/Casablanca',
            'locale' => 'ar_MA',
            'accounting_system' => [
                'name' => 'PCGM',
                'full_name' => 'Plan Comptable Général Marocain',
                'chart_of_accounts' => 'moroccan_coa',
                'fiscal_year_start' => '01-01',
                'currency_precision' => 2,
                'required_books' => ['journal', 'ledger', 'balance_sheet', 'income_statement'],
            ],
            'tax_system' => [
                'vat_rates' => [
                    'standard' => 20.00,
                    'reduced_1' => 14.00,
                    'reduced_2' => 10.00,
                    'reduced_3' => 7.00,
                    'zero' => 0.00,
                ],
                'corporate_tax_rate' => 31.00,
                'bank_tax_rate' => 35.00,
                'income_tax_progressive' => true,
                'e_invoicing_mandatory' => true,
                'tax_authority' => 'DGI',
                'filing_frequency' => 'monthly',
            ],
            'social_security_system' => [
                'cnss_rate' => 26.82,
                'amo_rate' => 5.50,
                'employer_contribution' => 20.48,
                'employee_contribution' => 6.34,
                'reporting_frequency' => 'monthly',
            ],
        ],
        'SA' => [
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Kingdom of Saudi Arabia',
            'name_fr' => 'Royaume d\'Arabie Saoudite',
            'currency' => 'SAR',
            'timezone' => 'Asia/Riyadh',
            'locale' => 'ar_SA',
            'accounting_system' => [
                'name' => 'SAOB',
                'full_name' => 'Saudi Arabian Organization for Certified Public Accountants',
                'chart_of_accounts' => 'saudi_coa',
                'fiscal_year_start' => '01-01',
                'currency_precision' => 2,
                'zakat_applicable' => true,
            ],
            'tax_system' => [
                'vat_rates' => [
                    'standard' => 15.00,
                    'zero' => 0.00,
                    'exempt' => null,
                ],
                'zakat_rate' => 2.50,
                'e_invoicing_mandatory' => true,
                'fatoora_integration' => true,
                'tax_authority' => 'ZATCA',
                'filing_frequency' => 'quarterly',
            ],
            'social_security_system' => [
                'gosi_rate' => 22.00,
                'employer_contribution' => 12.00,
                'employee_contribution' => 10.00,
                'reporting_frequency' => 'monthly',
            ],
        ],
        'AE' => [
            'name_ar' => 'الإمارات العربية المتحدة',
            'name_en' => 'United Arab Emirates',
            'name_fr' => 'Émirats Arabes Unis',
            'currency' => 'AED',
            'timezone' => 'Asia/Dubai',
            'locale' => 'ar_AE',
            'accounting_system' => [
                'name' => 'IFRS',
                'full_name' => 'International Financial Reporting Standards',
                'chart_of_accounts' => 'ifrs_coa',
                'fiscal_year_start' => '01-01',
                'currency_precision' => 2,
            ],
            'tax_system' => [
                'vat_rates' => [
                    'standard' => 5.00,
                    'zero' => 0.00,
                    'exempt' => null,
                ],
                'excise_tax' => true,
                'e_invoicing_mandatory' => true,
                'tax_authority' => 'FTA',
                'filing_frequency' => 'quarterly',
            ],
            'social_security_system' => [
                'ssa_rate' => 17.50,
                'employer_contribution' => 12.50,
                'employee_contribution' => 5.00,
                'reporting_frequency' => 'monthly',
            ],
        ],
        // يمكن إضافة باقي الدول بنفس الطريقة
    ];

    /**
     * العلاقة مع الضرائب
     */
    public function taxes(): HasMany
    {
        return $this->hasMany(TaxConfiguration::class);
    }

    /**
     * العلاقة مع أنظمة الضمان الاجتماعي
     */
    public function socialSecuritySystems(): HasMany
    {
        return $this->hasMany(SocialSecurityConfiguration::class);
    }

    /**
     * العلاقة مع أنظمة الفوترة الإلكترونية
     */
    public function eInvoicingSystem(): HasOne
    {
        return $this->hasOne(EInvoicingConfiguration::class);
    }

    /**
     * العلاقة مع التكاملات الحكومية
     */
    public function governmentIntegrations(): HasMany
    {
        return $this->hasMany(GovernmentIntegration::class);
    }

    /**
     * العلاقة مع متطلبات الامتثال
     */
    public function complianceRules(): HasMany
    {
        return $this->hasMany(ComplianceRule::class);
    }

    /**
     * الحصول على معدل الضريبة حسب النوع
     */
    public function getTaxRate(string $taxType, string $category = 'standard'): float
    {
        $taxSystem = $this->tax_system;
        
        return match ($taxType) {
            'vat' => $taxSystem['vat_rates'][$category] ?? 0,
            'corporate' => $taxSystem['corporate_tax_rate'] ?? 0,
            'zakat' => $taxSystem['zakat_rate'] ?? 0,
            'income' => $taxSystem['income_tax_rate'] ?? 0,
            default => 0,
        };
    }

    /**
     * التحقق من إلزامية الفوترة الإلكترونية
     */
    public function isEInvoicingMandatory(): bool
    {
        return $this->tax_system['e_invoicing_mandatory'] ?? false;
    }

    /**
     * الحصول على تكوين لوحة التحكم للدولة
     */
    public function getDashboardConfig(): array
    {
        return $this->dashboard_config ?? $this->getDefaultDashboardConfig();
    }

    /**
     * تكوين لوحة التحكم الافتراضي
     */
    protected function getDefaultDashboardConfig(): array
    {
        return [
            'theme' => [
                'primary_color' => $this->getCountryPrimaryColor(),
                'secondary_color' => $this->getCountrySecondaryColor(),
                'flag_url' => "/flags/{$this->code}.svg",
                'rtl' => true,
            ],
            'widgets' => $this->getDefaultWidgets(),
            'reports' => $this->getDefaultReports(),
            'integrations' => $this->getDefaultIntegrations(),
            'compliance_alerts' => $this->getDefaultComplianceAlerts(),
        ];
    }

    /**
     * الحصول على اللون الأساسي للدولة
     */
    protected function getCountryPrimaryColor(): string
    {
        return match ($this->code) {
            'MA' => '#C1272D', // أحمر المغرب
            'SA' => '#006C35', // أخضر السعودية
            'AE' => '#FF0000', // أحمر الإمارات
            'KW' => '#007A3D', // أخضر الكويت
            'QA' => '#8D1B3D', // عنابي قطر
            'JO' => '#CE1126', // أحمر الأردن
            'EG' => '#CE1126', // أحمر مصر
            'TN' => '#E70013', // أحمر تونس
            'DZ' => '#006233', // أخضر الجزائر
            default => '#1f2937',
        };
    }

    /**
     * الحصول على اللون الثانوي للدولة
     */
    protected function getCountrySecondaryColor(): string
    {
        return match ($this->code) {
            'MA' => '#006233', // أخضر المغرب
            'SA' => '#FFFFFF', // أبيض السعودية
            'AE' => '#00732F', // أخضر الإمارات
            'KW' => '#FFFFFF', // أبيض الكويت
            'QA' => '#FFFFFF', // أبيض قطر
            'JO' => '#007A3D', // أخضر الأردن
            'EG' => '#FFFFFF', // أبيض مصر
            'TN' => '#FFFFFF', // أبيض تونس
            'DZ' => '#FFFFFF', // أبيض الجزائر
            default => '#374151',
        };
    }

    /**
     * الحصول على الويدجت الافتراضية للوحة التحكم
     */
    protected function getDefaultWidgets(): array
    {
        $baseWidgets = [
            'revenue_overview',
            'tax_summary',
            'compliance_status',
            'recent_transactions',
        ];

        $countrySpecificWidgets = match ($this->code) {
            'MA' => ['cnss_contributions', 'dgi_filings', 'tva_summary'],
            'SA' => ['zatca_status', 'gosi_contributions', 'zakat_summary', 'fatoora_compliance'],
            'AE' => ['fta_filings', 'vat_summary', 'excise_tax', 'ssa_contributions'],
            'KW' => ['kss_contributions', 'vat_preparation'],
            'QA' => ['qssa_contributions', 'vat_summary'],
            'JO' => ['gss_contributions', 'sales_tax_summary'],
            'EG' => ['eta_integration', 'social_insurance', 'vat_summary'],
            'TN' => ['cnss_contributions', 'tva_summary'],
            'DZ' => ['cnas_contributions', 'tva_summary'],
            default => [],
        };

        return array_merge($baseWidgets, $countrySpecificWidgets);
    }

    /**
     * الحصول على التقارير الافتراضية
     */
    protected function getDefaultReports(): array
    {
        return match ($this->code) {
            'MA' => [
                'monthly_tva_report',
                'annual_is_report',
                'cnss_monthly_report',
                'payroll_summary',
                'chart_of_accounts_report',
            ],
            'SA' => [
                'quarterly_vat_report',
                'zakat_annual_report',
                'gosi_monthly_report',
                'fatoora_compliance_report',
                'financial_statements',
            ],
            'AE' => [
                'quarterly_vat_report',
                'excise_tax_report',
                'ssa_monthly_report',
                'fta_compliance_report',
                'ifrs_financial_statements',
            ],
            default => [
                'financial_statements',
                'tax_summary_report',
                'payroll_report',
                'compliance_report',
            ],
        };
    }

    /**
     * الحصول على التكاملات الافتراضية
     */
    protected function getDefaultIntegrations(): array
    {
        return match ($this->code) {
            'MA' => [
                'dgi_api' => 'تكامل مع الإدارة العامة للضرائب',
                'cnss_api' => 'تكامل مع الصندوق الوطني للضمان الاجتماعي',
                'attijariwafa_bank' => 'تكامل مع بنك التجاري وفا بنك',
                'banque_populaire' => 'تكامل مع البنك الشعبي',
            ],
            'SA' => [
                'zatca_api' => 'تكامل مع هيئة الزكاة والضريبة والجمارك',
                'gosi_api' => 'تكامل مع التأمينات الاجتماعية',
                'fatoora_api' => 'تكامل مع منصة فاتورة',
                'qiwa_api' => 'تكامل مع منصة قوى',
            ],
            'AE' => [
                'fta_api' => 'تكامل مع الهيئة الاتحادية للضرائب',
                'ssa_api' => 'تكامل مع هيئة الضمان الاجتماعي',
                'mohre_api' => 'تكامل مع وزارة الموارد البشرية',
                'abudhabi_dof' => 'تكامل مع دائرة المالية أبوظبي',
            ],
            default => [],
        };
    }

    /**
     * الحصول على تنبيهات الامتثال الافتراضية
     */
    protected function getDefaultComplianceAlerts(): array
    {
        return [
            'tax_filing_deadlines',
            'social_security_deadlines',
            'regulatory_updates',
            'license_renewals',
            'audit_requirements',
            'data_retention_alerts',
        ];
    }

    /**
     * التحقق من متطلبات الامتثال
     */
    public function checkCompliance(): array
    {
        $complianceStatus = [];
        
        // فحص متطلبات الضرائب
        $complianceStatus['tax_compliance'] = $this->checkTaxCompliance();
        
        // فحص متطلبات الضمان الاجتماعي
        $complianceStatus['social_security_compliance'] = $this->checkSocialSecurityCompliance();
        
        // فحص متطلبات الفوترة الإلكترونية
        $complianceStatus['e_invoicing_compliance'] = $this->checkEInvoicingCompliance();
        
        // فحص متطلبات التقارير
        $complianceStatus['reporting_compliance'] = $this->checkReportingCompliance();
        
        return $complianceStatus;
    }

    /**
     * فحص امتثال الضرائب
     */
    protected function checkTaxCompliance(): array
    {
        // منطق فحص امتثال الضرائب
        return [
            'status' => 'compliant',
            'last_filing' => now()->subDays(15),
            'next_deadline' => now()->addDays(15),
            'outstanding_issues' => [],
        ];
    }

    /**
     * فحص امتثال الضمان الاجتماعي
     */
    protected function checkSocialSecurityCompliance(): array
    {
        // منطق فحص امتثال الضمان الاجتماعي
        return [
            'status' => 'compliant',
            'last_filing' => now()->subDays(10),
            'next_deadline' => now()->addDays(20),
            'outstanding_issues' => [],
        ];
    }

    /**
     * فحص امتثال الفوترة الإلكترونية
     */
    protected function checkEInvoicingCompliance(): array
    {
        if (!$this->isEInvoicingMandatory()) {
            return ['status' => 'not_applicable'];
        }

        return [
            'status' => 'compliant',
            'integration_status' => 'active',
            'last_sync' => now()->subHours(2),
            'pending_invoices' => 0,
        ];
    }

    /**
     * فحص امتثال التقارير
     */
    protected function checkReportingCompliance(): array
    {
        return [
            'status' => 'compliant',
            'required_reports' => $this->getDefaultReports(),
            'completed_reports' => [],
            'pending_reports' => [],
        ];
    }

    /**
     * الحصول على إعدادات الترجمة للدولة
     */
    public function getLocalizationSettings(): array
    {
        return $this->localization_settings ?? [
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimal_places' => 2,
            ],
            'currency_format' => [
                'symbol' => $this->getCurrencySymbol(),
                'position' => 'after',
                'space' => true,
            ],
            'address_format' => $this->getAddressFormat(),
        ];
    }

    /**
     * الحصول على رمز العملة
     */
    protected function getCurrencySymbol(): string
    {
        return match ($this->currency) {
            'MAD' => 'د.م',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'KWD' => 'د.ك',
            'QAR' => 'ر.ق',
            'JOD' => 'د.أ',
            'EGP' => 'ج.م',
            'TND' => 'د.ت',
            'DZD' => 'د.ج',
            default => $this->currency,
        };
    }

    /**
     * الحصول على تنسيق العنوان
     */
    protected function getAddressFormat(): array
    {
        return [
            'fields' => ['street', 'city', 'state', 'postal_code', 'country'],
            'required' => ['street', 'city', 'country'],
            'format' => '{street}\n{city}, {state} {postal_code}\n{country}',
        ];
    }

    /**
     * Scope للدول النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للدول التي تدعم الفوترة الإلكترونية
     */
    public function scopeWithEInvoicing($query)
    {
        return $query->whereJsonPath('tax_system->e_invoicing_mandatory', true);
    }

    /**
     * Scope للدول حسب المنطقة
     */
    public function scopeByRegion($query, string $region)
    {
        $regions = [
            'gulf' => ['SA', 'AE', 'KW', 'QA'],
            'maghreb' => ['MA', 'TN', 'DZ'],
            'levant' => ['JO'],
            'africa' => ['EG'],
        ];

        return $query->whereIn('code', $regions[$region] ?? []);
    }
}
