<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Accounting\Controllers\Web\AccountController;
use App\Domains\Accounting\Controllers\Web\InvoiceController;
use App\Domains\Accounting\Controllers\Web\JournalEntryController;
use App\Domains\Accounting\Controllers\Web\PaymentController;
use App\Domains\Accounting\Controllers\Web\ReportController;
use App\Domains\Accounting\Controllers\Web\DashboardController;

/*
|--------------------------------------------------------------------------
| Accounting Web Routes
|--------------------------------------------------------------------------
|
| مسارات الويب لنظام المحاسبة
|
*/

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('accounting.dashboard');
Route::get('/dashboard', [DashboardController::class, 'index'])->name('accounting.dashboard.main');

// Chart of Accounts
Route::prefix('accounts')->name('accounts.')->group(function () {
    Route::get('/', [AccountController::class, 'index'])->name('index');
    Route::get('/create', [AccountController::class, 'create'])->name('create');
    Route::post('/', [AccountController::class, 'store'])->name('store');
    Route::get('/{account}', [AccountController::class, 'show'])->name('show');
    Route::get('/{account}/edit', [AccountController::class, 'edit'])->name('edit');
    Route::put('/{account}', [AccountController::class, 'update'])->name('update');
    Route::delete('/{account}', [AccountController::class, 'destroy'])->name('destroy');
    Route::get('/{account}/history', [AccountController::class, 'history'])->name('history');
    Route::get('/{account}/balance', [AccountController::class, 'balance'])->name('balance');
    Route::post('/{account}/adjust-balance', [AccountController::class, 'adjustBalance'])->name('adjust-balance');
    Route::get('/chart/view', [AccountController::class, 'chartView'])->name('chart');
    Route::post('/import', [AccountController::class, 'import'])->name('import');
    Route::get('/export', [AccountController::class, 'export'])->name('export');
});

// Journal Entries
Route::prefix('journal-entries')->name('journal-entries.')->group(function () {
    Route::get('/', [JournalEntryController::class, 'index'])->name('index');
    Route::get('/create', [JournalEntryController::class, 'create'])->name('create');
    Route::post('/', [JournalEntryController::class, 'store'])->name('store');
    Route::get('/{entry}', [JournalEntryController::class, 'show'])->name('show');
    Route::get('/{entry}/edit', [JournalEntryController::class, 'edit'])->name('edit');
    Route::put('/{entry}', [JournalEntryController::class, 'update'])->name('update');
    Route::delete('/{entry}', [JournalEntryController::class, 'destroy'])->name('destroy');
    Route::post('/{entry}/post', [JournalEntryController::class, 'post'])->name('post');
    Route::post('/{entry}/reverse', [JournalEntryController::class, 'reverse'])->name('reverse');
    Route::post('/{entry}/approve', [JournalEntryController::class, 'approve'])->name('approve');
    Route::get('/templates/list', [JournalEntryController::class, 'templates'])->name('templates');
    Route::get('/unposted/list', [JournalEntryController::class, 'unposted'])->name('unposted');
    Route::get('/pending-approval/list', [JournalEntryController::class, 'pendingApproval'])->name('pending-approval');
});

// Invoices
Route::prefix('invoices')->name('invoices.')->group(function () {
    Route::get('/', [InvoiceController::class, 'index'])->name('index');
    Route::get('/create', [InvoiceController::class, 'create'])->name('create');
    Route::post('/', [InvoiceController::class, 'store'])->name('store');
    Route::get('/{invoice}', [InvoiceController::class, 'show'])->name('show');
    Route::get('/{invoice}/edit', [InvoiceController::class, 'edit'])->name('edit');
    Route::put('/{invoice}', [InvoiceController::class, 'update'])->name('update');
    Route::delete('/{invoice}', [InvoiceController::class, 'destroy'])->name('destroy');
    Route::get('/{invoice}/pdf', [InvoiceController::class, 'pdf'])->name('pdf');
    Route::post('/{invoice}/send', [InvoiceController::class, 'send'])->name('send');
    Route::post('/{invoice}/approve', [InvoiceController::class, 'approve'])->name('approve');
    Route::post('/{invoice}/cancel', [InvoiceController::class, 'cancel'])->name('cancel');
    Route::post('/{invoice}/duplicate', [InvoiceController::class, 'duplicate'])->name('duplicate');
    Route::get('/{invoice}/payments', [InvoiceController::class, 'payments'])->name('payments');
    Route::post('/{invoice}/record-payment', [InvoiceController::class, 'recordPayment'])->name('record-payment');
    Route::get('/overdue/list', [InvoiceController::class, 'overdue'])->name('overdue');
    Route::get('/recurring/list', [InvoiceController::class, 'recurring'])->name('recurring');
});

// Payments
Route::prefix('payments')->name('payments.')->group(function () {
    Route::get('/', [PaymentController::class, 'index'])->name('index');
    Route::get('/create', [PaymentController::class, 'create'])->name('create');
    Route::post('/', [PaymentController::class, 'store'])->name('store');
    Route::get('/{payment}', [PaymentController::class, 'show'])->name('show');
    Route::get('/{payment}/edit', [PaymentController::class, 'edit'])->name('edit');
    Route::put('/{payment}', [PaymentController::class, 'update'])->name('update');
    Route::delete('/{payment}', [PaymentController::class, 'destroy'])->name('destroy');
    Route::post('/{payment}/approve', [PaymentController::class, 'approve'])->name('approve');
    Route::post('/{payment}/reject', [PaymentController::class, 'reject'])->name('reject');
    Route::post('/{payment}/void', [PaymentController::class, 'void'])->name('void');
    Route::get('/pending/list', [PaymentController::class, 'pending'])->name('pending');
    Route::get('/reconciliation/view', [PaymentController::class, 'reconciliation'])->name('reconciliation');
});

// Financial Reports
Route::prefix('reports')->name('reports.')->group(function () {
    Route::get('/', [ReportController::class, 'index'])->name('index');
    Route::get('/balance-sheet', [ReportController::class, 'balanceSheet'])->name('balance-sheet');
    Route::get('/income-statement', [ReportController::class, 'incomeStatement'])->name('income-statement');
    Route::get('/cash-flow', [ReportController::class, 'cashFlow'])->name('cash-flow');
    Route::get('/trial-balance', [ReportController::class, 'trialBalance'])->name('trial-balance');
    Route::get('/general-ledger', [ReportController::class, 'generalLedger'])->name('general-ledger');
    Route::get('/accounts-receivable', [ReportController::class, 'accountsReceivable'])->name('accounts-receivable');
    Route::get('/accounts-payable', [ReportController::class, 'accountsPayable'])->name('accounts-payable');
    Route::get('/aged-receivables', [ReportController::class, 'agedReceivables'])->name('aged-receivables');
    Route::get('/aged-payables', [ReportController::class, 'agedPayables'])->name('aged-payables');
    Route::get('/profit-loss', [ReportController::class, 'profitLoss'])->name('profit-loss');
    Route::get('/budget-variance', [ReportController::class, 'budgetVariance'])->name('budget-variance');
    Route::get('/custom', [ReportController::class, 'custom'])->name('custom');
    Route::post('/generate', [ReportController::class, 'generate'])->name('generate');
    Route::post('/schedule', [ReportController::class, 'schedule'])->name('schedule');
    Route::get('/scheduled', [ReportController::class, 'scheduled'])->name('scheduled');
});

// Bank Reconciliation
Route::prefix('bank-reconciliation')->name('bank-reconciliation.')->group(function () {
    Route::get('/', [PaymentController::class, 'bankReconciliation'])->name('index');
    Route::get('/accounts', [PaymentController::class, 'bankAccounts'])->name('accounts');
    Route::get('/{account}/reconcile', [PaymentController::class, 'reconcile'])->name('reconcile');
    Route::post('/{account}/reconcile', [PaymentController::class, 'processReconciliation'])->name('process');
    Route::get('/{account}/transactions', [PaymentController::class, 'bankTransactions'])->name('transactions');
    Route::post('/{account}/import-transactions', [PaymentController::class, 'importTransactions'])->name('import-transactions');
});

// Budget Management
Route::prefix('budgets')->name('budgets.')->group(function () {
    Route::get('/', [ReportController::class, 'budgets'])->name('index');
    Route::get('/create', [ReportController::class, 'createBudget'])->name('create');
    Route::post('/', [ReportController::class, 'storeBudget'])->name('store');
    Route::get('/{budget}', [ReportController::class, 'showBudget'])->name('show');
    Route::get('/{budget}/edit', [ReportController::class, 'editBudget'])->name('edit');
    Route::put('/{budget}', [ReportController::class, 'updateBudget'])->name('update');
    Route::delete('/{budget}', [ReportController::class, 'destroyBudget'])->name('destroy');
    Route::post('/{budget}/approve', [ReportController::class, 'approveBudget'])->name('approve');
    Route::post('/{budget}/activate', [ReportController::class, 'activateBudget'])->name('activate');
    Route::get('/{budget}/variance', [ReportController::class, 'budgetVarianceDetail'])->name('variance');
    Route::get('/{budget}/performance', [ReportController::class, 'budgetPerformance'])->name('performance');
});

// Accounting Periods
Route::prefix('periods')->name('periods.')->group(function () {
    Route::get('/', [ReportController::class, 'periods'])->name('index');
    Route::get('/create', [ReportController::class, 'createPeriod'])->name('create');
    Route::post('/', [ReportController::class, 'storePeriod'])->name('store');
    Route::get('/{period}', [ReportController::class, 'showPeriod'])->name('show');
    Route::post('/{period}/close', [ReportController::class, 'closePeriod'])->name('close');
    Route::post('/{period}/reopen', [ReportController::class, 'reopenPeriod'])->name('reopen');
    Route::get('/{period}/closing-entries', [ReportController::class, 'closingEntries'])->name('closing-entries');
    Route::post('/{period}/year-end-close', [ReportController::class, 'yearEndClose'])->name('year-end-close');
});

// Settings & Configuration
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/', [DashboardController::class, 'settings'])->name('index');
    Route::get('/chart-of-accounts', [AccountController::class, 'chartSettings'])->name('chart-of-accounts');
    Route::post('/chart-of-accounts', [AccountController::class, 'updateChartSettings'])->name('update-chart-of-accounts');
    Route::get('/tax-rates', [DashboardController::class, 'taxRates'])->name('tax-rates');
    Route::post('/tax-rates', [DashboardController::class, 'updateTaxRates'])->name('update-tax-rates');
    Route::get('/currencies', [DashboardController::class, 'currencies'])->name('currencies');
    Route::post('/currencies', [DashboardController::class, 'updateCurrencies'])->name('update-currencies');
    Route::get('/backup', [DashboardController::class, 'backup'])->name('backup');
    Route::post('/backup', [DashboardController::class, 'createBackup'])->name('create-backup');
    Route::get('/audit-trail', [DashboardController::class, 'auditTrail'])->name('audit-trail');
});

// Analytics & Insights
Route::prefix('analytics')->name('analytics.')->group(function () {
    Route::get('/', [DashboardController::class, 'analytics'])->name('index');
    Route::get('/financial-health', [DashboardController::class, 'financialHealth'])->name('financial-health');
    Route::get('/cash-flow-forecast', [DashboardController::class, 'cashFlowForecast'])->name('cash-flow-forecast');
    Route::get('/expense-analysis', [DashboardController::class, 'expenseAnalysis'])->name('expense-analysis');
    Route::get('/revenue-analysis', [DashboardController::class, 'revenueAnalysis'])->name('revenue-analysis');
    Route::get('/profitability-analysis', [DashboardController::class, 'profitabilityAnalysis'])->name('profitability-analysis');
    Route::get('/trend-analysis', [DashboardController::class, 'trendAnalysis'])->name('trend-analysis');
    Route::get('/ratio-analysis', [DashboardController::class, 'ratioAnalysis'])->name('ratio-analysis');
    Route::get('/benchmarking', [DashboardController::class, 'benchmarking'])->name('benchmarking');
});

// Import/Export
Route::prefix('import-export')->name('import-export.')->group(function () {
    Route::get('/', [DashboardController::class, 'importExport'])->name('index');
    Route::get('/import', [DashboardController::class, 'import'])->name('import');
    Route::post('/import-accounts', [AccountController::class, 'importAccounts'])->name('import-accounts');
    Route::post('/import-transactions', [JournalEntryController::class, 'importTransactions'])->name('import-transactions');
    Route::post('/import-invoices', [InvoiceController::class, 'importInvoices'])->name('import-invoices');
    Route::get('/export', [DashboardController::class, 'export'])->name('export');
    Route::get('/export-accounts', [AccountController::class, 'exportAccounts'])->name('export-accounts');
    Route::get('/export-transactions', [JournalEntryController::class, 'exportTransactions'])->name('export-transactions');
    Route::get('/export-reports', [ReportController::class, 'exportReports'])->name('export-reports');
});
