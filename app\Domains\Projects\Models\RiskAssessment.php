<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تقييم المخاطر - Risk Assessment
 */
class RiskAssessment extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'risk_id',
        'probability',
        'impact',
        'risk_score',
        'notes',
        'assessed_by',
        'assessed_at',
    ];

    protected $casts = [
        'probability' => 'integer',
        'impact' => 'integer',
        'risk_score' => 'integer',
        'assessed_at' => 'datetime',
    ];

    public function risk(): BelongsTo
    {
        return $this->belongsTo(ProjectRisk::class, 'risk_id');
    }

    public function assessor(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assessed_by');
    }
}
