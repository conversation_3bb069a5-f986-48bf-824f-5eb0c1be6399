<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج اجتماع السبرنت - Sprint Meeting
 */
class SprintMeeting extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'sprint_id',
        'type',
        'title',
        'description',
        'scheduled_at',
        'duration_minutes',
        'meeting_url',
        'notes',
        'attendees',
        'created_by',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'duration_minutes' => 'integer',
        'attendees' => 'array',
    ];

    public function sprint(): BelongsTo
    {
        return $this->belongsTo(Sprint::class);
    }

    public function creator(): BelongsT<PERSON>
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }
}
