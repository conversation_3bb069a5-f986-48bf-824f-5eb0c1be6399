<?php

namespace App\Domains\Integration\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\Integration\Models\ApiGateway;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * API Gateway Policy
 * 
 * Defines authorization rules for API Gateway operations
 */
class ApiGatewayPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any gateways.
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_api_gateways') || 
               $user->hasRole(['admin', 'integration_manager', 'developer']);
    }

    /**
     * Determine whether the user can view the gateway.
     */
    public function view(Employee $user, ApiGateway $gateway): bool
    {
        // Admins and integration managers can view all gateways
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view gateways in their company
        if ($gateway->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_api_gateways');
        }

        // Developers can view gateways they have access to
        if ($user->hasRole('developer')) {
            return $this->hasGatewayAccess($user, $gateway);
        }

        return false;
    }

    /**
     * Determine whether the user can create gateways.
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_api_gateways') || 
               $user->hasRole(['admin', 'integration_manager']);
    }

    /**
     * Determine whether the user can update the gateway.
     */
    public function update(Employee $user, ApiGateway $gateway): bool
    {
        // Prevent updating system gateways
        if ($gateway->is_system_gateway && !$user->hasRole('admin')) {
            return false;
        }

        // Admins can update all gateways
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can update gateways in their company
        if ($user->hasRole('integration_manager') && $gateway->company_id === $user->company_id) {
            return true;
        }

        // Users with specific permission can update gateways they own
        if ($gateway->created_by === $user->id) {
            return $user->hasPermissionTo('update_api_gateways');
        }

        return false;
    }

    /**
     * Determine whether the user can delete the gateway.
     */
    public function delete(Employee $user, ApiGateway $gateway): bool
    {
        // Prevent deleting system gateways
        if ($gateway->is_system_gateway) {
            return false;
        }

        // Prevent deleting gateways with active endpoints
        if ($gateway->endpoints()->where('is_active', true)->exists()) {
            return false;
        }

        // Only admins and integration managers can delete gateways
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can activate/deactivate the gateway.
     */
    public function toggleStatus(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway configuration.
     */
    public function manageConfiguration(Employee $user, ApiGateway $gateway): bool
    {
        // Admins can manage all configurations
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage configurations in their company
        if ($user->hasRole('integration_manager') && $gateway->company_id === $user->company_id) {
            return true;
        }

        // Gateway owners can manage basic configurations
        if ($gateway->created_by === $user->id) {
            return $user->hasPermissionTo('manage_gateway_config');
        }

        return false;
    }

    /**
     * Determine whether the user can view gateway logs.
     */
    public function viewLogs(Employee $user, ApiGateway $gateway): bool
    {
        // Admins and integration managers can view all logs
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view logs for gateways in their company
        if ($gateway->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_gateway_logs');
        }

        return false;
    }

    /**
     * Determine whether the user can export gateway logs.
     */
    public function exportLogs(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway security.
     */
    public function manageSecurity(Employee $user, ApiGateway $gateway): bool
    {
        // Only admins and integration managers can manage security
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can restart the gateway.
     */
    public function restart(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can enable/disable maintenance mode.
     */
    public function manageMaintenance(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can clear gateway cache.
     */
    public function clearCache(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager', 'developer']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can view gateway statistics.
     */
    public function viewStatistics(Employee $user, ApiGateway $gateway): bool
    {
        // Admins and integration managers can view all statistics
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view statistics for gateways in their company
        if ($gateway->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_gateway_statistics');
        }

        return false;
    }

    /**
     * Determine whether the user can manage gateway API keys.
     */
    public function manageApiKeys(Employee $user, ApiGateway $gateway): bool
    {
        // Admins can manage all API keys
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage API keys in their company
        if ($user->hasRole('integration_manager') && $gateway->company_id === $user->company_id) {
            return true;
        }

        // Gateway owners can manage API keys
        if ($gateway->created_by === $user->id) {
            return $user->hasPermissionTo('manage_api_keys');
        }

        return false;
    }

    /**
     * Determine whether the user can manage gateway endpoints.
     */
    public function manageEndpoints(Employee $user, ApiGateway $gateway): bool
    {
        // Admins can manage all endpoints
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage endpoints in their company
        if ($user->hasRole('integration_manager') && $gateway->company_id === $user->company_id) {
            return true;
        }

        // Gateway owners can manage endpoints
        if ($gateway->created_by === $user->id) {
            return $user->hasPermissionTo('manage_endpoints');
        }

        // Developers with access can manage endpoints
        if ($user->hasRole('developer') && $this->hasGatewayAccess($user, $gateway)) {
            return $user->hasPermissionTo('manage_endpoints');
        }

        return false;
    }

    /**
     * Determine whether the user can perform health checks.
     */
    public function performHealthCheck(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager', 'developer']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway monitoring.
     */
    public function manageMonitoring(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Check if user has access to specific gateway
     */
    protected function hasGatewayAccess(Employee $user, ApiGateway $gateway): bool
    {
        // Check if user is assigned to gateway
        return $gateway->assignedUsers()->where('user_id', $user->id)->exists() ||
               $gateway->created_by === $user->id;
    }

    /**
     * Determine whether the user can manage gateway scaling.
     */
    public function manageScaling(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway load balancing.
     */
    public function manageLoadBalancing(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway rate limiting.
     */
    public function manageRateLimiting(Employee $user, ApiGateway $gateway): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $gateway->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage gateway transformations.
     */
    public function manageTransformations(Employee $user, ApiGateway $gateway): bool
    {
        // Admins can manage all transformations
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage transformations in their company
        if ($user->hasRole('integration_manager') && $gateway->company_id === $user->company_id) {
            return true;
        }

        // Developers with access can manage transformations
        if ($user->hasRole('developer') && $this->hasGatewayAccess($user, $gateway)) {
            return $user->hasPermissionTo('manage_transformations');
        }

        return false;
    }
}
