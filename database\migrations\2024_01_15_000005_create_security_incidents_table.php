<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('security_incidents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_gateway_id')->constrained()->onDelete('cascade');
            $table->foreignId('api_endpoint_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('api_key_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('incident_type'); // ddos, brute_force, sql_injection, xss, anomaly, rate_limit_exceeded
            $table->string('severity'); // low, medium, high, critical
            $table->string('status'); // open, investigating, resolved, false_positive
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('source_ip')->nullable();
            $table->text('user_agent')->nullable();
            $table->json('request_data')->nullable();
            $table->json('threat_indicators')->nullable();
            $table->string('detection_method'); // rule_based, ml_based, behavioral, signature
            $table->decimal('confidence_score', 5, 2)->default(0);
            $table->boolean('false_positive')->default(false);
            $table->json('mitigation_actions')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('resolution_notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['api_gateway_id', 'created_at']);
            $table->index(['incident_type', 'severity']);
            $table->index(['status', 'created_at']);
            $table->index(['source_ip', 'created_at']);
            $table->index(['confidence_score', 'severity']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('security_incidents');
    }
};
