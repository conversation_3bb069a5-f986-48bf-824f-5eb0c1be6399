<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Integration\Controllers\ApiGatewayController;
use App\Domains\Integration\Controllers\ApiEndpointController;
use App\Domains\Integration\Controllers\ApiKeyController;
use App\Domains\Integration\Controllers\ExternalIntegrationController;
use App\Domains\Integration\Controllers\McpController;
use App\Domains\Integration\Controllers\WebhookController;
use App\Domains\Integration\Controllers\IntegrationDashboardController;
use App\Domains\Integration\Controllers\IntegrationAnalyticsController;

/*
|--------------------------------------------------------------------------
| Integration API Routes - Hesabiai Connect
|--------------------------------------------------------------------------
|
| مسارات API لنظام التكامل المتقدم "Hesabiai Connect"
| يدعم جميع أنواع التكاملات والبروتوكولات
|
*/

Route::prefix('integration')->middleware(['auth:sanctum'])->group(function () {
    
    // ========== لوحات التحكم والتحليلات ==========
    Route::prefix('dashboard')->group(function () {
        Route::get('/', [IntegrationDashboardController::class, 'index']);
        Route::get('/overview', [IntegrationDashboardController::class, 'getOverview']);
        Route::get('/health-status', [IntegrationDashboardController::class, 'getHealthStatus']);
        Route::get('/real-time-metrics', [IntegrationDashboardController::class, 'getRealTimeMetrics']);
        Route::get('/alerts', [IntegrationDashboardController::class, 'getAlerts']);
        Route::post('/alerts/{alert}/acknowledge', [IntegrationDashboardController::class, 'acknowledgeAlert']);
    });

    Route::prefix('analytics')->group(function () {
        Route::get('/performance', [IntegrationAnalyticsController::class, 'getPerformanceAnalytics']);
        Route::get('/usage', [IntegrationAnalyticsController::class, 'getUsageAnalytics']);
        Route::get('/errors', [IntegrationAnalyticsController::class, 'getErrorAnalytics']);
        Route::get('/trends', [IntegrationAnalyticsController::class, 'getTrends']);
        Route::get('/reports', [IntegrationAnalyticsController::class, 'getReports']);
        Route::post('/reports/generate', [IntegrationAnalyticsController::class, 'generateReport']);
        Route::get('/export/{type}', [IntegrationAnalyticsController::class, 'exportData']);
    });

    // ========== API Gateway Management ==========
    Route::prefix('gateways')->group(function () {
        Route::get('/', [ApiGatewayController::class, 'index']);
        Route::post('/', [ApiGatewayController::class, 'store']);
        Route::get('/{gateway}', [ApiGatewayController::class, 'show']);
        Route::put('/{gateway}', [ApiGatewayController::class, 'update']);
        Route::delete('/{gateway}', [ApiGatewayController::class, 'destroy']);
        
        // عمليات البوابة
        Route::post('/{gateway}/activate', [ApiGatewayController::class, 'activate']);
        Route::post('/{gateway}/deactivate', [ApiGatewayController::class, 'deactivate']);
        Route::post('/{gateway}/maintenance/enable', [ApiGatewayController::class, 'enableMaintenance']);
        Route::post('/{gateway}/maintenance/disable', [ApiGatewayController::class, 'disableMaintenance']);
        Route::post('/{gateway}/restart', [ApiGatewayController::class, 'restart']);
        Route::post('/{gateway}/cache/clear', [ApiGatewayController::class, 'clearCache']);
        
        // فحص الصحة والمراقبة
        Route::get('/{gateway}/health', [ApiGatewayController::class, 'healthCheck']);
        Route::get('/{gateway}/statistics', [ApiGatewayController::class, 'getStatistics']);
        Route::get('/{gateway}/logs', [ApiGatewayController::class, 'getRequestLogs']);
        Route::post('/{gateway}/logs/export', [ApiGatewayController::class, 'exportLogs']);
        
        // التكوين
        Route::get('/{gateway}/configuration', [ApiGatewayController::class, 'getConfiguration']);
        Route::put('/{gateway}/configuration', [ApiGatewayController::class, 'updateConfiguration']);
    });

    // ========== API Endpoints Management ==========
    Route::prefix('endpoints')->group(function () {
        Route::get('/', [ApiEndpointController::class, 'index']);
        Route::post('/', [ApiEndpointController::class, 'store']);
        Route::get('/{endpoint}', [ApiEndpointController::class, 'show']);
        Route::put('/{endpoint}', [ApiEndpointController::class, 'update']);
        Route::delete('/{endpoint}', [ApiEndpointController::class, 'destroy']);
        
        // عمليات نقاط النهاية
        Route::post('/{endpoint}/activate', [ApiEndpointController::class, 'activate']);
        Route::post('/{endpoint}/deactivate', [ApiEndpointController::class, 'deactivate']);
        Route::post('/{endpoint}/test', [ApiEndpointController::class, 'test']);
        Route::get('/{endpoint}/health', [ApiEndpointController::class, 'healthCheck']);
        
        // التوثيق والأمثلة
        Route::get('/{endpoint}/documentation', [ApiEndpointController::class, 'getDocumentation']);
        Route::put('/{endpoint}/documentation', [ApiEndpointController::class, 'updateDocumentation']);
        Route::get('/{endpoint}/examples', [ApiEndpointController::class, 'getExamples']);
        Route::post('/{endpoint}/examples', [ApiEndpointController::class, 'addExample']);
        
        // الإحصائيات والسجلات
        Route::get('/{endpoint}/statistics', [ApiEndpointController::class, 'getStatistics']);
        Route::get('/{endpoint}/logs', [ApiEndpointController::class, 'getLogs']);
        
        // التحويلات والتعيينات
        Route::get('/{endpoint}/transformations', [ApiEndpointController::class, 'getTransformations']);
        Route::put('/{endpoint}/transformations', [ApiEndpointController::class, 'updateTransformations']);
        Route::get('/{endpoint}/mappings', [ApiEndpointController::class, 'getMappings']);
        Route::put('/{endpoint}/mappings', [ApiEndpointController::class, 'updateMappings']);
    });

    // ========== API Keys Management ==========
    Route::prefix('api-keys')->group(function () {
        Route::get('/', [ApiKeyController::class, 'index']);
        Route::post('/', [ApiKeyController::class, 'store']);
        Route::get('/{apiKey}', [ApiKeyController::class, 'show']);
        Route::put('/{apiKey}', [ApiKeyController::class, 'update']);
        Route::delete('/{apiKey}', [ApiKeyController::class, 'destroy']);
        
        // عمليات المفاتيح
        Route::post('/{apiKey}/regenerate', [ApiKeyController::class, 'regenerate']);
        Route::post('/{apiKey}/revoke', [ApiKeyController::class, 'revoke']);
        Route::post('/{apiKey}/activate', [ApiKeyController::class, 'activate']);
        Route::post('/{apiKey}/suspend', [ApiKeyController::class, 'suspend']);
        
        // الصلاحيات والنطاقات
        Route::get('/{apiKey}/permissions', [ApiKeyController::class, 'getPermissions']);
        Route::put('/{apiKey}/permissions', [ApiKeyController::class, 'updatePermissions']);
        Route::get('/{apiKey}/scopes', [ApiKeyController::class, 'getScopes']);
        Route::put('/{apiKey}/scopes', [ApiKeyController::class, 'updateScopes']);
        
        // الاستخدام والإحصائيات
        Route::get('/{apiKey}/usage', [ApiKeyController::class, 'getUsage']);
        Route::get('/{apiKey}/statistics', [ApiKeyController::class, 'getStatistics']);
        Route::get('/{apiKey}/logs', [ApiKeyController::class, 'getLogs']);
        
        // Rate Limiting
        Route::get('/{apiKey}/rate-limits', [ApiKeyController::class, 'getRateLimits']);
        Route::put('/{apiKey}/rate-limits', [ApiKeyController::class, 'updateRateLimits']);
        Route::post('/{apiKey}/rate-limits/reset', [ApiKeyController::class, 'resetRateLimits']);
    });

    // ========== External Integrations ==========
    Route::prefix('external')->group(function () {
        Route::get('/', [ExternalIntegrationController::class, 'index']);
        Route::post('/', [ExternalIntegrationController::class, 'store']);
        Route::get('/{integration}', [ExternalIntegrationController::class, 'show']);
        Route::put('/{integration}', [ExternalIntegrationController::class, 'update']);
        Route::delete('/{integration}', [ExternalIntegrationController::class, 'destroy']);
        
        // عمليات التكامل
        Route::post('/{integration}/activate', [ExternalIntegrationController::class, 'activate']);
        Route::post('/{integration}/deactivate', [ExternalIntegrationController::class, 'deactivate']);
        Route::post('/{integration}/test-connection', [ExternalIntegrationController::class, 'testConnection']);
        Route::post('/{integration}/restart', [ExternalIntegrationController::class, 'restart']);
        
        // المزامنة
        Route::post('/{integration}/sync', [ExternalIntegrationController::class, 'sync']);
        Route::post('/{integration}/sync/schedule', [ExternalIntegrationController::class, 'scheduleSync']);
        Route::post('/{integration}/sync/cancel', [ExternalIntegrationController::class, 'cancelSync']);
        Route::get('/{integration}/sync/status', [ExternalIntegrationController::class, 'getSyncStatus']);
        Route::get('/{integration}/sync/history', [ExternalIntegrationController::class, 'getSyncHistory']);
        
        // فحص الصحة
        Route::get('/{integration}/health', [ExternalIntegrationController::class, 'healthCheck']);
        Route::post('/{integration}/health/refresh', [ExternalIntegrationController::class, 'refreshHealth']);
        
        // التكوين والإعدادات
        Route::get('/{integration}/configuration', [ExternalIntegrationController::class, 'getConfiguration']);
        Route::put('/{integration}/configuration', [ExternalIntegrationController::class, 'updateConfiguration']);
        Route::get('/{integration}/credentials', [ExternalIntegrationController::class, 'getCredentials']);
        Route::put('/{integration}/credentials', [ExternalIntegrationController::class, 'updateCredentials']);
        
        // التحويلات والتعيينات
        Route::get('/{integration}/transformations', [ExternalIntegrationController::class, 'getTransformations']);
        Route::put('/{integration}/transformations', [ExternalIntegrationController::class, 'updateTransformations']);
        Route::get('/{integration}/field-mappings', [ExternalIntegrationController::class, 'getFieldMappings']);
        Route::put('/{integration}/field-mappings', [ExternalIntegrationController::class, 'updateFieldMappings']);
        
        // الإحصائيات والسجلات
        Route::get('/{integration}/statistics', [ExternalIntegrationController::class, 'getStatistics']);
        Route::get('/{integration}/logs', [ExternalIntegrationController::class, 'getLogs']);
        Route::get('/{integration}/errors', [ExternalIntegrationController::class, 'getErrors']);
        
        // Webhooks
        Route::get('/{integration}/webhooks', [ExternalIntegrationController::class, 'getWebhooks']);
        Route::put('/{integration}/webhooks', [ExternalIntegrationController::class, 'updateWebhooks']);
        Route::post('/{integration}/webhooks/test', [ExternalIntegrationController::class, 'testWebhook']);
        
        // البيانات والموارد
        Route::post('/{integration}/data/import', [ExternalIntegrationController::class, 'importData']);
        Route::post('/{integration}/data/export', [ExternalIntegrationController::class, 'exportData']);
        Route::get('/{integration}/resources', [ExternalIntegrationController::class, 'getResources']);
        Route::post('/{integration}/resources/sync', [ExternalIntegrationController::class, 'syncResources']);
    });

    // ========== MCP (Model Context Protocol) ==========
    Route::prefix('mcp')->group(function () {
        Route::post('/request', [McpController::class, 'processRequest']);
        Route::get('/tools', [McpController::class, 'listTools']);
        Route::post('/tools/{tool}/call', [McpController::class, 'callTool']);
        Route::get('/resources', [McpController::class, 'listResources']);
        Route::get('/resources/{resource}', [McpController::class, 'getResource']);
        Route::get('/prompts', [McpController::class, 'listPrompts']);
        Route::get('/prompts/{prompt}', [McpController::class, 'getPrompt']);
        Route::post('/completion', [McpController::class, 'complete']);
        Route::post('/messages', [McpController::class, 'createMessage']);
        
        // إدارة الوكلاء
        Route::get('/agents', [McpController::class, 'listAgents']);
        Route::post('/agents', [McpController::class, 'createAgent']);
        Route::get('/agents/{agent}', [McpController::class, 'getAgent']);
        Route::put('/agents/{agent}', [McpController::class, 'updateAgent']);
        Route::delete('/agents/{agent}', [McpController::class, 'deleteAgent']);
        Route::post('/agents/{agent}/permissions', [McpController::class, 'updateAgentPermissions']);
        
        // الإحصائيات والمراقبة
        Route::get('/statistics', [McpController::class, 'getStatistics']);
        Route::get('/usage', [McpController::class, 'getUsage']);
        Route::get('/logs', [McpController::class, 'getLogs']);
        Route::get('/security-events', [McpController::class, 'getSecurityEvents']);
    });

    // ========== Webhooks Management ==========
    Route::prefix('webhooks')->group(function () {
        Route::get('/', [WebhookController::class, 'index']);
        Route::post('/', [WebhookController::class, 'store']);
        Route::get('/{webhook}', [WebhookController::class, 'show']);
        Route::put('/{webhook}', [WebhookController::class, 'update']);
        Route::delete('/{webhook}', [WebhookController::class, 'destroy']);
        
        // عمليات Webhook
        Route::post('/{webhook}/test', [WebhookController::class, 'test']);
        Route::post('/{webhook}/activate', [WebhookController::class, 'activate']);
        Route::post('/{webhook}/deactivate', [WebhookController::class, 'deactivate']);
        Route::post('/{webhook}/retry', [WebhookController::class, 'retry']);
        
        // السجلات والإحصائيات
        Route::get('/{webhook}/deliveries', [WebhookController::class, 'getDeliveries']);
        Route::get('/{webhook}/statistics', [WebhookController::class, 'getStatistics']);
        Route::get('/{webhook}/failures', [WebhookController::class, 'getFailures']);
        
        // الأمان والتوقيع
        Route::get('/{webhook}/signature', [WebhookController::class, 'getSignature']);
        Route::post('/{webhook}/signature/regenerate', [WebhookController::class, 'regenerateSignature']);
        Route::post('/{webhook}/signature/verify', [WebhookController::class, 'verifySignature']);
    });

    // ========== Provider-Specific Routes ==========
    Route::prefix('providers')->group(function () {
        // Shopify
        Route::prefix('shopify')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectShopify']);
            Route::get('/products/sync', [ExternalIntegrationController::class, 'syncShopifyProducts']);
            Route::get('/orders/sync', [ExternalIntegrationController::class, 'syncShopifyOrders']);
            Route::post('/webhooks/products', [WebhookController::class, 'handleShopifyProductWebhook']);
            Route::post('/webhooks/orders', [WebhookController::class, 'handleShopifyOrderWebhook']);
        });
        
        // Salla
        Route::prefix('salla')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectSalla']);
            Route::get('/products/sync', [ExternalIntegrationController::class, 'syncSallaProducts']);
            Route::get('/orders/sync', [ExternalIntegrationController::class, 'syncSallaOrders']);
            Route::post('/webhooks/orders', [WebhookController::class, 'handleSallaOrderWebhook']);
        });
        
        // ZATCA (Saudi Arabia)
        Route::prefix('zatca')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectZatca']);
            Route::post('/invoices/submit', [ExternalIntegrationController::class, 'submitZatcaInvoice']);
            Route::get('/invoices/status/{uuid}', [ExternalIntegrationController::class, 'getZatcaInvoiceStatus']);
            Route::post('/certificates/renew', [ExternalIntegrationController::class, 'renewZatcaCertificate']);
        });
        
        // DGI (Morocco)
        Route::prefix('dgi')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectDgi']);
            Route::post('/declarations/submit', [ExternalIntegrationController::class, 'submitDgiDeclaration']);
            Route::get('/declarations/status/{id}', [ExternalIntegrationController::class, 'getDgiDeclarationStatus']);
        });
        
        // Facebook Ads
        Route::prefix('facebook-ads')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectFacebookAds']);
            Route::get('/campaigns/sync', [ExternalIntegrationController::class, 'syncFacebookCampaigns']);
            Route::get('/insights/sync', [ExternalIntegrationController::class, 'syncFacebookInsights']);
        });
        
        // Google Ads
        Route::prefix('google-ads')->group(function () {
            Route::post('/connect', [ExternalIntegrationController::class, 'connectGoogleAds']);
            Route::get('/campaigns/sync', [ExternalIntegrationController::class, 'syncGoogleCampaigns']);
            Route::get('/reports/sync', [ExternalIntegrationController::class, 'syncGoogleReports']);
        });
    });

    // ========== Utility Routes ==========
    Route::prefix('utils')->group(function () {
        Route::get('/providers', [IntegrationDashboardController::class, 'getAvailableProviders']);
        Route::get('/categories', [IntegrationDashboardController::class, 'getCategories']);
        Route::get('/protocols', [IntegrationDashboardController::class, 'getProtocols']);
        Route::get('/authentication-types', [IntegrationDashboardController::class, 'getAuthenticationTypes']);
        Route::get('/rate-limit-templates', [IntegrationDashboardController::class, 'getRateLimitTemplates']);
        Route::get('/security-templates', [IntegrationDashboardController::class, 'getSecurityTemplates']);
        
        // أدوات التحويل والتحقق
        Route::post('/validate-json', [IntegrationDashboardController::class, 'validateJson']);
        Route::post('/transform-data', [IntegrationDashboardController::class, 'transformData']);
        Route::post('/test-regex', [IntegrationDashboardController::class, 'testRegex']);
        Route::post('/generate-signature', [IntegrationDashboardController::class, 'generateSignature']);
        Route::post('/verify-signature', [IntegrationDashboardController::class, 'verifySignature']);
        
        // أدوات المراقبة والتشخيص
        Route::get('/system-health', [IntegrationDashboardController::class, 'getSystemHealth']);
        Route::get('/performance-metrics', [IntegrationDashboardController::class, 'getPerformanceMetrics']);
        Route::post('/run-diagnostics', [IntegrationDashboardController::class, 'runDiagnostics']);
        Route::post('/cleanup-logs', [IntegrationDashboardController::class, 'cleanupLogs']);
    });

    // ========== Batch Operations ==========
    Route::prefix('batch')->group(function () {
        Route::post('/integrations/sync', [ExternalIntegrationController::class, 'batchSync']);
        Route::post('/integrations/health-check', [ExternalIntegrationController::class, 'batchHealthCheck']);
        Route::post('/gateways/restart', [ApiGatewayController::class, 'batchRestart']);
        Route::post('/api-keys/revoke', [ApiKeyController::class, 'batchRevoke']);
        Route::post('/webhooks/retry', [WebhookController::class, 'batchRetry']);
    });
});

// ========== Public API Gateway Routes (No Auth Required) ==========
Route::prefix('api/v1')->group(function () {
    // معالجة طلبات API العامة
    Route::any('/{gateway}/{path?}', [ApiGatewayController::class, 'processRequest'])
        ->where('path', '.*')
        ->name('api.gateway.process');
});

// ========== Webhook Receivers (No Auth Required) ==========
Route::prefix('webhooks')->group(function () {
    Route::post('/shopify/{integration}', [WebhookController::class, 'receiveShopifyWebhook']);
    Route::post('/salla/{integration}', [WebhookController::class, 'receiveSallaWebhook']);
    Route::post('/facebook/{integration}', [WebhookController::class, 'receiveFacebookWebhook']);
    Route::post('/google/{integration}', [WebhookController::class, 'receiveGoogleWebhook']);
    Route::post('/stripe/{integration}', [WebhookController::class, 'receiveStripeWebhook']);
    Route::post('/paypal/{integration}', [WebhookController::class, 'receivePaypalWebhook']);
    Route::post('/generic/{provider}/{integration}', [WebhookController::class, 'receiveGenericWebhook']);
});

// ========== MCP Public Endpoint ==========
Route::post('/mcp', [McpController::class, 'processRequest'])
    ->middleware(['throttle:mcp'])
    ->name('mcp.process');
