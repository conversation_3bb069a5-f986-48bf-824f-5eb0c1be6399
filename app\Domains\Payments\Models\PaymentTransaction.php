<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use App\Domains\Accounting\Models\Invoice;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * نموذج معاملة الدفع
 * يمثل معاملة دفع واحدة في النظام
 */
class PaymentTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_id',
        'reference_number',
        'gateway_id',
        'payment_method_id',
        'user_id',
        'payable_type',
        'payable_id',
        'amount',
        'currency',
        'exchange_rate',
        'amount_in_base_currency',
        'fees',
        'net_amount',
        'status',
        'payment_method',
        'gateway_transaction_id',
        'gateway_reference',
        'gateway_response',
        'failure_reason',
        'risk_score',
        'country_code',
        'ip_address',
        'user_agent',
        'metadata',
        'processed_at',
        'settled_at',
        'expires_at',
        'webhook_verified_at',
        'refunded_amount',
        'description',
        'customer_email',
        'customer_phone',
        'billing_address',
        'shipping_address',
        'three_d_secure',
        'fraud_check_result',
        'settlement_currency',
        'settlement_amount',
        'settlement_rate',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'amount_in_base_currency' => 'decimal:2',
        'fees' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'gateway_response' => 'array',
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'settled_at' => 'datetime',
        'expires_at' => 'datetime',
        'webhook_verified_at' => 'datetime',
        'refunded_amount' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'three_d_secure' => 'array',
        'fraud_check_result' => 'array',
        'settlement_amount' => 'decimal:2',
        'settlement_rate' => 'decimal:6',
        'risk_score' => 'integer',
    ];

    /**
     * حالات المعاملة
     */
    const STATUSES = [
        'pending' => 'في الانتظار',
        'processing' => 'قيد المعالجة',
        'requires_action' => 'يتطلب إجراء',
        'requires_confirmation' => 'يتطلب تأكيد',
        'requires_payment_method' => 'يتطلب طريقة دفع',
        'succeeded' => 'نجح',
        'failed' => 'فشل',
        'canceled' => 'ملغي',
        'refunded' => 'مسترد',
        'partially_refunded' => 'مسترد جزئياً',
        'disputed' => 'متنازع عليه',
        'expired' => 'منتهي الصلاحية',
    ];

    /**
     * طرق الدفع
     */
    const PAYMENT_METHODS = [
        'card' => 'بطاقة ائتمانية',
        'bank_transfer' => 'تحويل بنكي',
        'digital_wallet' => 'محفظة رقمية',
        'mobile_payment' => 'دفع عبر الهاتف',
        'cash_on_delivery' => 'دفع عند الاستلام',
        'cryptocurrency' => 'عملة رقمية',
        'buy_now_pay_later' => 'اشتري الآن وادفع لاحقاً',
        'bank_debit' => 'خصم مباشر',
        'check' => 'شيك',
        'money_order' => 'حوالة مالية',
    ];

    /**
     * مستويات المخاطر
     */
    const RISK_LEVELS = [
        'low' => [0, 30],
        'medium' => [31, 70],
        'high' => [71, 100],
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = static::generateTransactionId();
            }
            
            if (empty($transaction->reference_number)) {
                $transaction->reference_number = static::generateReferenceNumber();
            }
        });
    }

    /**
     * العلاقة مع بوابة الدفع
     */
    public function gateway(): BelongsTo
    {
        return $this->belongsTo(PaymentGateway::class, 'gateway_id');
    }

    /**
     * العلاقة مع طريقة الدفع
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة المتعددة الأشكال مع الكيان القابل للدفع
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع عمليات الاسترداد
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(PaymentRefund::class, 'transaction_id');
    }

    /**
     * العلاقة مع سجلات المعاملة
     */
    public function logs(): HasMany
    {
        return $this->hasMany(PaymentTransactionLog::class, 'transaction_id');
    }

    /**
     * توليد معرف المعاملة
     */
    public static function generateTransactionId(): string
    {
        return 'TXN_' . strtoupper(Str::random(12)) . '_' . time();
    }

    /**
     * توليد رقم المرجع
     */
    public static function generateReferenceNumber(): string
    {
        return 'REF_' . date('Ymd') . '_' . strtoupper(Str::random(8));
    }

    /**
     * التحقق من نجاح المعاملة
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'succeeded';
    }

    /**
     * التحقق من فشل المعاملة
     */
    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'canceled', 'expired']);
    }

    /**
     * التحقق من إمكانية الاسترداد
     */
    public function isRefundable(): bool
    {
        return $this->isSuccessful() && 
               $this->refunded_amount < $this->amount &&
               $this->created_at->diffInDays(now()) <= 180; // 6 أشهر
    }

    /**
     * التحقق من الاسترداد الكامل
     */
    public function isFullyRefunded(): bool
    {
        return $this->refunded_amount >= $this->amount;
    }

    /**
     * التحقق من الاسترداد الجزئي
     */
    public function isPartiallyRefunded(): bool
    {
        return $this->refunded_amount > 0 && $this->refunded_amount < $this->amount;
    }

    /**
     * الحصول على المبلغ القابل للاسترداد
     */
    public function getRefundableAmount(): float
    {
        return max(0, $this->amount - $this->refunded_amount);
    }

    /**
     * الحصول على مستوى المخاطر
     */
    public function getRiskLevel(): string
    {
        foreach (self::RISK_LEVELS as $level => $range) {
            if ($this->risk_score >= $range[0] && $this->risk_score <= $range[1]) {
                return $level;
            }
        }
        return 'unknown';
    }

    /**
     * تحديث حالة المعاملة
     */
    public function updateStatus(string $status, array $data = []): void
    {
        $oldStatus = $this->status;
        
        $this->update(array_merge(['status' => $status], $data));
        
        // تسجيل تغيير الحالة
        $this->logs()->create([
            'action' => 'status_changed',
            'old_value' => $oldStatus,
            'new_value' => $status,
            'data' => $data,
            'created_at' => now(),
        ]);

        // إطلاق الأحداث
        event(new \App\Domains\Payments\Events\PaymentStatusChanged($this, $oldStatus, $status));
    }

    /**
     * معالجة الاسترداد
     */
    public function processRefund(float $amount, string $reason = null): PaymentRefund
    {
        if ($amount > $this->getRefundableAmount()) {
            throw new \InvalidArgumentException('مبلغ الاسترداد أكبر من المبلغ المتاح');
        }

        $refund = $this->refunds()->create([
            'refund_id' => 'REF_' . strtoupper(Str::random(12)),
            'amount' => $amount,
            'currency' => $this->currency,
            'reason' => $reason,
            'status' => 'pending',
            'requested_at' => now(),
        ]);

        // تحديث المبلغ المسترد
        $this->increment('refunded_amount', $amount);

        // تحديث حالة المعاملة
        if ($this->isFullyRefunded()) {
            $this->updateStatus('refunded');
        } else {
            $this->updateStatus('partially_refunded');
        }

        return $refund;
    }

    /**
     * تحويل العملة
     */
    public function convertCurrency(string $toCurrency, float $rate = null): array
    {
        if ($rate === null) {
            // الحصول على سعر الصرف من خدمة خارجية
            $rate = app(\App\Domains\Payments\Services\CurrencyExchangeService::class)
                ->getExchangeRate($this->currency, $toCurrency);
        }

        $convertedAmount = $this->amount * $rate;

        return [
            'original_amount' => $this->amount,
            'original_currency' => $this->currency,
            'converted_amount' => round($convertedAmount, 2),
            'converted_currency' => $toCurrency,
            'exchange_rate' => $rate,
            'conversion_date' => now(),
        ];
    }

    /**
     * الحصول على معلومات العميل
     */
    public function getCustomerInfo(): array
    {
        return [
            'email' => $this->customer_email,
            'phone' => $this->customer_phone,
            'billing_address' => $this->billing_address,
            'shipping_address' => $this->shipping_address,
            'user_id' => $this->user_id,
        ];
    }

    /**
     * الحصول على معلومات الجهاز
     */
    public function getDeviceInfo(): array
    {
        return [
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
            'country_code' => $this->country_code,
        ];
    }

    /**
     * تحديث معلومات المخاطر
     */
    public function updateRiskScore(int $score, array $details = []): void
    {
        $this->update([
            'risk_score' => $score,
            'fraud_check_result' => $details,
        ]);
    }

    /**
     * Scope للمعاملات الناجحة
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'succeeded');
    }

    /**
     * Scope للمعاملات الفاشلة
     */
    public function scopeFailed($query)
    {
        return $query->whereIn('status', ['failed', 'canceled', 'expired']);
    }

    /**
     * Scope للمعاملات في فترة زمنية
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope للمعاملات حسب العملة
     */
    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope للمعاملات حسب البوابة
     */
    public function scopeByGateway($query, int $gatewayId)
    {
        return $query->where('gateway_id', $gatewayId);
    }

    /**
     * Scope للمعاملات عالية المخاطر
     */
    public function scopeHighRisk($query)
    {
        return $query->where('risk_score', '>=', 71);
    }
}
