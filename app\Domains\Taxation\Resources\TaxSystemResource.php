<?php

namespace App\Domains\Taxation\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد النظام الضريبي
 * تنسيق شامل لبيانات النظام الضريبي مع جميع التفاصيل
 */
class TaxSystemResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            
            // Basic Information
            'basic_info' => [
                'name' => $this->name,
                'name_ar' => $this->name_ar,
                'name_en' => $this->name_en,
                'description' => $this->description,
                'country_code' => $this->country_code,
                'country_name' => $this->getCountryName(),
                'currency' => $this->currency,
                'timezone' => $this->timezone,
                'version' => $this->version,
            ],

            // Status and Settings
            'status' => [
                'is_active' => $this->is_active,
                'is_default' => $this->is_default,
                'effective_from' => $this->effective_from?->format('Y-m-d'),
                'effective_to' => $this->effective_to?->format('Y-m-d'),
                'is_current' => $this->isCurrent(),
                'days_until_effective' => $this->getDaysUntilEffective(),
                'days_until_expiry' => $this->getDaysUntilExpiry(),
            ],

            // VAT Configuration
            'vat_configuration' => [
                'enabled' => $this->vat_enabled,
                'rates' => $this->vat_rates ?? [],
                'default_rate' => $this->getDefaultVATRate(),
                'rate_count' => count($this->vat_rates ?? []),
                'supports_reverse_charge' => $this->features['auto_reverse_charge'] ?? false,
            ],

            // Corporate Tax
            'corporate_tax' => [
                'enabled' => $this->corporate_tax_enabled,
                'rate' => $this->corporate_tax_rate,
                'threshold' => $this->corporate_tax_threshold,
                'formatted_threshold' => $this->corporate_tax_threshold ? 
                    number_format($this->corporate_tax_threshold, 2) . ' ' . $this->currency : null,
            ],

            // Withholding Tax
            'withholding_tax' => [
                'enabled' => $this->withholding_tax_enabled,
                'rates' => $this->withholding_tax_rates ?? [],
                'categories_count' => count($this->withholding_tax_rates ?? []),
            ],

            // Excise Tax
            'excise_tax' => [
                'enabled' => $this->excise_tax_enabled,
                'categories' => $this->excise_tax_categories ?? [],
                'categories_count' => count($this->excise_tax_categories ?? []),
            ],

            // Customs Duty
            'customs_duty' => [
                'enabled' => $this->customs_duty_enabled,
                'rates' => $this->customs_duty_rates ?? [],
                'hs_codes_count' => count($this->customs_duty_rates ?? []),
            ],

            // Tax Periods
            'tax_periods' => [
                'periods' => $this->tax_periods ?? [],
                'monthly_taxes' => $this->getMonthlyTaxes(),
                'quarterly_taxes' => $this->getQuarterlyTaxes(),
                'annual_taxes' => $this->getAnnualTaxes(),
            ],

            // Authority Integration
            'authority_integration' => [
                'enabled' => $this->authority_integration['enabled'] ?? false,
                'api_endpoint' => $this->when(
                    $request->user()?->can('view-sensitive-data'),
                    $this->authority_integration['api_endpoint'] ?? null
                ),
                'test_mode' => $this->authority_integration['test_mode'] ?? true,
                'is_connected' => $this->is_connected,
                'connection_status' => $this->connection_status,
                'last_sync_at' => $this->last_sync_at?->format('Y-m-d H:i:s'),
                'api_version' => $this->api_version,
                'supported_features' => $this->supported_features ?? [],
            ],

            // Compliance Settings
            'compliance' => [
                'settings' => $this->compliance_settings ?? [],
                'is_compliant' => $this->is_compliant,
                'compliance_score' => $this->compliance_score,
                'last_compliance_check_at' => $this->last_compliance_check_at?->format('Y-m-d H:i:s'),
                'next_compliance_check_at' => $this->next_compliance_check_at?->format('Y-m-d H:i:s'),
                'compliance_issues' => $this->compliance_issues ?? [],
                'requires_digital_signature' => $this->compliance_settings['require_digital_signature'] ?? false,
                'requires_tax_number' => $this->compliance_settings['require_tax_number'] ?? false,
                'auto_calculate_penalties' => $this->compliance_settings['auto_calculate_penalties'] ?? false,
                'penalty_rate' => $this->compliance_settings['penalty_rate'] ?? 0,
            ],

            // Localization Settings
            'localization' => [
                'settings' => $this->localization ?? [],
                'date_format' => $this->localization['date_format'] ?? 'Y-m-d',
                'number_format' => $this->localization['number_format'] ?? 'en',
                'decimal_places' => $this->localization['decimal_places'] ?? 2,
                'thousand_separator' => $this->localization['thousand_separator'] ?? ',',
                'decimal_separator' => $this->localization['decimal_separator'] ?? '.',
                'formatted_sample' => $this->getFormattedSample(),
            ],

            // Reporting Settings
            'reporting' => [
                'settings' => $this->reporting_settings ?? [],
                'default_format' => $this->reporting_settings['default_format'] ?? 'PDF',
                'include_zero_amounts' => $this->reporting_settings['include_zero_amounts'] ?? false,
                'group_by_category' => $this->reporting_settings['group_by_category'] ?? true,
                'show_calculations' => $this->reporting_settings['show_calculations'] ?? true,
                'supported_formats' => ['PDF', 'XML', 'EXCEL', 'JSON'],
            ],

            // Notification Settings
            'notifications' => [
                'settings' => $this->notification_settings ?? [],
                'email_enabled' => $this->notification_settings['email_enabled'] ?? true,
                'sms_enabled' => $this->notification_settings['sms_enabled'] ?? false,
                'reminder_days' => $this->notification_settings['reminder_days'] ?? [30, 15, 7, 1],
            ],

            // Advanced Features
            'features' => [
                'list' => $this->features ?? [],
                'auto_reverse_charge' => $this->features['auto_reverse_charge'] ?? false,
                'multi_currency_support' => $this->features['multi_currency_support'] ?? false,
                'tax_exemption_handling' => $this->features['tax_exemption_handling'] ?? false,
                'installment_payments' => $this->features['installment_payments'] ?? false,
                'audit_trail' => $this->features['audit_trail'] ?? true,
                'real_time_validation' => $this->features['real_time_validation'] ?? false,
                'automated_filing' => $this->features['automated_filing'] ?? false,
            ],

            // Statistics
            'statistics' => [
                'total_tax_rules' => $this->whenLoaded('taxRules', function () {
                    return $this->taxRules->count();
                }),
                'active_tax_rules' => $this->whenLoaded('taxRules', function () {
                    return $this->taxRules->where('is_active', true)->count();
                }),
                'total_tax_returns' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->count();
                }),
                'pending_returns' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->where('status', 'PENDING')->count();
                }),
                'submitted_returns' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->where('status', 'SUBMITTED')->count();
                }),
                'approved_returns' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->where('status', 'APPROVED')->count();
                }),
                'total_tax_collected' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->where('status', 'APPROVED')->sum('total_tax_amount');
                }),
                'average_return_amount' => $this->whenLoaded('taxReturns', function () {
                    return $this->taxReturns->where('status', 'APPROVED')->avg('total_tax_amount');
                }),
            ],

            // Related Data
            'tax_rules' => $this->whenLoaded('taxRules', function () {
                return TaxRuleResource::collection($this->taxRules);
            }),

            'recent_returns' => $this->whenLoaded('taxReturns', function () {
                return TaxReturnResource::collection(
                    $this->taxReturns->sortByDesc('created_at')->take(5)
                );
            }),

            // Custom Fields and Metadata
            'custom_data' => [
                'custom_fields' => $this->custom_fields ?? [],
                'metadata' => $this->metadata ?? [],
                'notes' => $this->notes,
                'tags' => $this->tags ?? [],
            ],

            // System Information
            'system_info' => [
                'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
                'created_by' => $this->createdBy?->name,
                'updated_by' => $this->updatedBy?->name,
                'last_connection_test_at' => $this->last_connection_test_at?->format('Y-m-d H:i:s'),
                'last_calculation_at' => $this->last_calculation_at?->format('Y-m-d H:i:s'),
            ],

            // Health and Performance
            'health_metrics' => [
                'overall_health' => $this->getOverallHealth(),
                'connection_health' => $this->getConnectionHealth(),
                'compliance_health' => $this->getComplianceHealth(),
                'performance_score' => $this->getPerformanceScore(),
                'uptime_percentage' => $this->getUptimePercentage(),
                'average_response_time' => $this->getAverageResponseTime(),
            ],

            // Permissions (for current user)
            'permissions' => [
                'can_view' => $request->user()?->can('view', $this->resource),
                'can_update' => $request->user()?->can('update', $this->resource),
                'can_delete' => $request->user()?->can('delete', $this->resource),
                'can_set_default' => $request->user()?->can('setDefault', $this->resource),
                'can_test_connection' => $request->user()?->can('testConnection', $this->resource),
                'can_sync_data' => $request->user()?->can('syncData', $this->resource),
                'can_view_sensitive_data' => $request->user()?->can('view-sensitive-data'),
            ],
        ];
    }

    /**
     * الحصول على اسم الدولة
     */
    protected function getCountryName(): ?string
    {
        $countries = config('taxation.countries', []);
        return $countries[$this->country_code] ?? $this->country_code;
    }

    /**
     * التحقق من كون النظام ساري المفعول حالياً
     */
    protected function isCurrent(): bool
    {
        $now = now();
        return $now >= $this->effective_from && 
               ($this->effective_to === null || $now <= $this->effective_to);
    }

    /**
     * الحصول على عدد الأيام حتى السريان
     */
    protected function getDaysUntilEffective(): ?int
    {
        if ($this->effective_from && now() < $this->effective_from) {
            return now()->diffInDays($this->effective_from);
        }
        return null;
    }

    /**
     * الحصول على عدد الأيام حتى الانتهاء
     */
    protected function getDaysUntilExpiry(): ?int
    {
        if ($this->effective_to && now() < $this->effective_to) {
            return now()->diffInDays($this->effective_to);
        }
        return null;
    }

    /**
     * الحصول على معدل ضريبة القيمة المضافة الافتراضي
     */
    protected function getDefaultVATRate(): ?array
    {
        if (!$this->vat_rates) {
            return null;
        }

        return collect($this->vat_rates)->firstWhere('is_default', true);
    }

    /**
     * الحصول على الضرائب الشهرية
     */
    protected function getMonthlyTaxes(): array
    {
        if (!$this->tax_periods) {
            return [];
        }

        return collect($this->tax_periods)
            ->where('type', 'MONTHLY')
            ->pluck('tax_type')
            ->toArray();
    }

    /**
     * الحصول على الضرائب الربع سنوية
     */
    protected function getQuarterlyTaxes(): array
    {
        if (!$this->tax_periods) {
            return [];
        }

        return collect($this->tax_periods)
            ->where('type', 'QUARTERLY')
            ->pluck('tax_type')
            ->toArray();
    }

    /**
     * الحصول على الضرائب السنوية
     */
    protected function getAnnualTaxes(): array
    {
        if (!$this->tax_periods) {
            return [];
        }

        return collect($this->tax_periods)
            ->where('type', 'ANNUALLY')
            ->pluck('tax_type')
            ->toArray();
    }

    /**
     * الحصول على عينة التنسيق
     */
    protected function getFormattedSample(): string
    {
        $sample = 1234567.89;
        $decimalPlaces = $this->localization['decimal_places'] ?? 2;
        $thousandSep = $this->localization['thousand_separator'] ?? ',';
        $decimalSep = $this->localization['decimal_separator'] ?? '.';

        return number_format($sample, $decimalPlaces, $decimalSep, $thousandSep);
    }

    /**
     * الحصول على الصحة العامة
     */
    protected function getOverallHealth(): string
    {
        $connectionHealth = $this->getConnectionHealthScore();
        $complianceHealth = $this->getComplianceHealthScore();
        $performanceHealth = $this->getPerformanceHealthScore();

        $averageHealth = ($connectionHealth + $complianceHealth + $performanceHealth) / 3;

        if ($averageHealth >= 90) return 'EXCELLENT';
        if ($averageHealth >= 75) return 'GOOD';
        if ($averageHealth >= 60) return 'FAIR';
        return 'POOR';
    }

    /**
     * الحصول على صحة الاتصال
     */
    protected function getConnectionHealth(): string
    {
        if (!$this->authority_integration['enabled'] ?? false) {
            return 'NOT_APPLICABLE';
        }

        if ($this->is_connected && $this->connection_status === 'CONNECTED') {
            return 'HEALTHY';
        }

        return 'UNHEALTHY';
    }

    /**
     * الحصول على صحة الامتثال
     */
    protected function getComplianceHealth(): string
    {
        if ($this->is_compliant && $this->compliance_score >= 90) {
            return 'COMPLIANT';
        }

        if ($this->compliance_score >= 70) {
            return 'MOSTLY_COMPLIANT';
        }

        return 'NON_COMPLIANT';
    }

    /**
     * الحصول على درجة الأداء
     */
    protected function getPerformanceScore(): int
    {
        // حساب درجة الأداء بناءً على عوامل مختلفة
        $score = 100;

        // خصم نقاط للمشاكل
        if (!empty($this->compliance_issues)) {
            $score -= count($this->compliance_issues) * 5;
        }

        // خصم نقاط لعدم الاتصال
        if ($this->authority_integration['enabled'] ?? false && !$this->is_connected) {
            $score -= 20;
        }

        // خصم نقاط للقواعد غير النشطة
        if ($this->relationLoaded('taxRules')) {
            $totalRules = $this->taxRules->count();
            $activeRules = $this->taxRules->where('is_active', true)->count();
            if ($totalRules > 0) {
                $activePercentage = ($activeRules / $totalRules) * 100;
                if ($activePercentage < 80) {
                    $score -= (80 - $activePercentage);
                }
            }
        }

        return max(0, min(100, $score));
    }

    /**
     * الحصول على نسبة وقت التشغيل
     */
    protected function getUptimePercentage(): float
    {
        // حساب نسبة وقت التشغيل (يمكن تطويرها بناءً على سجلات الاتصال)
        return 99.5; // قيمة افتراضية
    }

    /**
     * الحصول على متوسط وقت الاستجابة
     */
    protected function getAverageResponseTime(): ?float
    {
        // حساب متوسط وقت الاستجابة (يمكن تطويرها بناءً على سجلات الأداء)
        return 250.5; // بالميلي ثانية
    }

    /**
     * الحصول على درجة صحة الاتصال
     */
    protected function getConnectionHealthScore(): int
    {
        if (!$this->authority_integration['enabled'] ?? false) {
            return 100; // غير قابل للتطبيق
        }

        if ($this->is_connected && $this->connection_status === 'CONNECTED') {
            return 100;
        }

        return 0;
    }

    /**
     * الحصول على درجة صحة الامتثال
     */
    protected function getComplianceHealthScore(): int
    {
        return $this->compliance_score ?? 0;
    }

    /**
     * الحصول على درجة صحة الأداء
     */
    protected function getPerformanceHealthScore(): int
    {
        return $this->getPerformanceScore();
    }
}
