<?php

namespace App\Domains\Compliance\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب إنشاء قاعدة امتثال
 */
class CreateComplianceRuleRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Domains\Compliance\Models\ComplianceRule::class);
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            'country_id' => ['required', 'exists:countries,id'],
            'rule_category' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceRule::RULE_CATEGORIES))],
            'rule_type' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceRule::RULE_TYPES))],
            'rule_name_ar' => ['required', 'string', 'max:255'],
            'rule_name_en' => ['nullable', 'string', 'max:255'],
            'rule_description_ar' => ['required', 'string'],
            'rule_description_en' => ['nullable', 'string'],
            'authority_name' => ['required', 'string', 'max:255'],
            'legal_reference' => ['nullable', 'string', 'max:500'],
            'regulation_number' => ['nullable', 'string', 'max:100'],
            'effective_date' => ['required', 'date'],
            'expiry_date' => ['nullable', 'date', 'after:effective_date'],
            'rule_conditions' => ['nullable', 'array'],
            'compliance_requirements' => ['nullable', 'array'],
            'validation_rules' => ['nullable', 'array'],
            'penalty_structure' => ['nullable', 'array'],
            'exemption_criteria' => ['nullable', 'array'],
            'applicable_entities' => ['nullable', 'array'],
            'frequency' => ['nullable', 'string', 'max:50'],
            'deadline_calculation' => ['nullable', 'array'],
            'notification_rules' => ['nullable', 'array'],
            'escalation_rules' => ['nullable', 'array'],
            'automation_config' => ['nullable', 'array'],
            'integration_endpoints' => ['nullable', 'array'],
            'documentation_requirements' => ['nullable', 'array'],
            'audit_requirements' => ['nullable', 'array'],
            'reporting_templates' => ['nullable', 'array'],
            'risk_level' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceRule::RISK_LEVELS))],
            'priority' => ['required', 'integer', 'min:1', 'max:10'],
            'compliance_score_impact' => ['nullable', 'integer', 'min:1', 'max:100'],
            'metadata' => ['nullable', 'array'],
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'country_id.required' => 'الدولة مطلوبة',
            'country_id.exists' => 'الدولة المحددة غير موجودة',
            'rule_category.required' => 'فئة القاعدة مطلوبة',
            'rule_category.in' => 'فئة القاعدة غير صحيحة',
            'rule_type.required' => 'نوع القاعدة مطلوب',
            'rule_type.in' => 'نوع القاعدة غير صحيح',
            'rule_name_ar.required' => 'اسم القاعدة بالعربية مطلوب',
            'rule_name_ar.max' => 'اسم القاعدة بالعربية يجب ألا يتجاوز 255 حرف',
            'rule_description_ar.required' => 'وصف القاعدة بالعربية مطلوب',
            'authority_name.required' => 'اسم السلطة مطلوب',
            'effective_date.required' => 'تاريخ السريان مطلوب',
            'effective_date.date' => 'تاريخ السريان يجب أن يكون تاريخ صحيح',
            'expiry_date.date' => 'تاريخ الانتهاء يجب أن يكون تاريخ صحيح',
            'expiry_date.after' => 'تاريخ الانتهاء يجب أن يكون بعد تاريخ السريان',
            'risk_level.required' => 'مستوى المخاطر مطلوب',
            'risk_level.in' => 'مستوى المخاطر غير صحيح',
            'priority.required' => 'الأولوية مطلوبة',
            'priority.integer' => 'الأولوية يجب أن تكون رقم صحيح',
            'priority.min' => 'الأولوية يجب أن تكون على الأقل 1',
            'priority.max' => 'الأولوية يجب ألا تتجاوز 10',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات قبل التحقق
        if ($this->has('rule_conditions') && is_string($this->rule_conditions)) {
            $this->merge([
                'rule_conditions' => json_decode($this->rule_conditions, true) ?: []
            ]);
        }

        if ($this->has('compliance_requirements') && is_string($this->compliance_requirements)) {
            $this->merge([
                'compliance_requirements' => json_decode($this->compliance_requirements, true) ?: []
            ]);
        }

        if ($this->has('validation_rules') && is_string($this->validation_rules)) {
            $this->merge([
                'validation_rules' => json_decode($this->validation_rules, true) ?: []
            ]);
        }
    }

    /**
     * الحصول على البيانات المحققة مع معالجة إضافية
     */
    public function getValidatedData(): array
    {
        $data = $this->validated();

        // توليد معرف فريد للقاعدة
        $data['rule_id'] = 'RULE_' . strtoupper(uniqid());

        // تعيين القيم الافتراضية
        $data['status'] = 'active';
        $data['last_updated_by_authority'] = now();
        $data['next_review_date'] = now()->addYear();

        return $data;
    }
}
