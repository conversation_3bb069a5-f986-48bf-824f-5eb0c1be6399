<?php

namespace App\Domains\Accounting\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\Accounting\Models\Invoice;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أمان الفواتير
 * Invoice Security Policy
 */
class InvoicePolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي فاتورة
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_invoices') || 
               $user->hasRole(['admin', 'accountant', 'financial_manager', 'sales_manager']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الفاتورة
     */
    public function view(Employee $user, Invoice $invoice): bool
    {
        // المدراء والمحاسبون يمكنهم رؤية جميع الفواتير
        if ($user->hasRole(['admin', 'financial_manager', 'accountant'])) {
            return true;
        }

        // مندوبو المبيعات يمكنهم رؤية فواتيرهم فقط
        if ($user->hasRole('sales_representative')) {
            return $invoice->created_by === $user->id;
        }

        // العملاء يمكنهم رؤية فواتيرهم فقط
        if ($user->hasRole('customer')) {
            return $invoice->customer_id === $user->customer_id;
        }

        return $user->hasPermissionTo('view_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء فاتورة
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_invoices') || 
               $user->hasRole(['admin', 'financial_manager', 'accountant', 'sales_manager', 'sales_representative']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الفاتورة
     */
    public function update(Employee $user, Invoice $invoice): bool
    {
        // منع تعديل الفواتير المعتمدة أو المدفوعة
        if (in_array($invoice->status, ['approved', 'paid', 'partially_paid'])) {
            return $user->hasRole(['admin', 'financial_manager']);
        }

        // منع تعديل الفواتير المرسلة إلا للمدراء
        if ($invoice->status === 'sent' && !$user->hasRole(['admin', 'financial_manager', 'accountant'])) {
            return false;
        }

        // المدراء والمحاسبون يمكنهم تعديل الفواتير
        if ($user->hasRole(['admin', 'financial_manager', 'accountant'])) {
            return true;
        }

        // منشئ الفاتورة يمكنه تعديلها إذا كانت مسودة
        if ($invoice->created_by === $user->id && $invoice->status === 'draft') {
            return true;
        }

        return $user->hasPermissionTo('update_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الفاتورة
     */
    public function delete(Employee $user, Invoice $invoice): bool
    {
        // منع حذف الفواتير المعتمدة أو المدفوعة
        if (in_array($invoice->status, ['approved', 'paid', 'partially_paid'])) {
            return false;
        }

        // منع حذف الفواتير المرسلة إلا للمدراء
        if ($invoice->status === 'sent' && !$user->hasRole(['admin', 'financial_manager'])) {
            return false;
        }

        // فقط المدراء يمكنهم حذف الفواتير
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('delete_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إرسال الفاتورة
     */
    public function send(Employee $user, Invoice $invoice): bool
    {
        // يجب أن تكون الفاتورة مسودة أو معتمدة
        if (!in_array($invoice->status, ['draft', 'approved'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'accountant', 'sales_manager']) || 
               $user->hasPermissionTo('send_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه اعتماد الفاتورة
     */
    public function approve(Employee $user, Invoice $invoice): bool
    {
        // يجب أن تكون الفاتورة مسودة أو مرسلة
        if (!in_array($invoice->status, ['draft', 'sent'])) {
            return false;
        }

        // منع اعتماد الفاتورة من قبل منشئها
        if ($invoice->created_by === $user->id) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('approve_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إلغاء الفاتورة
     */
    public function cancel(Employee $user, Invoice $invoice): bool
    {
        // منع إلغاء الفواتير المدفوعة
        if (in_array($invoice->status, ['paid', 'partially_paid'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('cancel_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تسجيل دفعة للفاتورة
     */
    public function recordPayment(Employee $user, Invoice $invoice): bool
    {
        // يجب أن تكون الفاتورة معتمدة أو مرسلة
        if (!in_array($invoice->status, ['approved', 'sent', 'viewed'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'accountant']) || 
               $user->hasPermissionTo('record_payments');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تنزيل PDF للفاتورة
     */
    public function downloadPdf(Employee $user, Invoice $invoice): bool
    {
        return $this->view($user, $invoice);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه نسخ الفاتورة
     */
    public function duplicate(Employee $user, Invoice $invoice): bool
    {
        return $this->create($user);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض المدفوعات
     */
    public function viewPayments(Employee $user, Invoice $invoice): bool
    {
        return $this->view($user, $invoice) && 
               ($user->hasRole(['admin', 'financial_manager', 'accountant']) || 
                $user->hasPermissionTo('view_payments'));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل عناصر الفاتورة
     */
    public function updateItems(Employee $user, Invoice $invoice): bool
    {
        // منع تعديل عناصر الفواتير المعتمدة أو المدفوعة
        if (in_array($invoice->status, ['approved', 'paid', 'partially_paid'])) {
            return false;
        }

        return $this->update($user, $invoice);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تطبيق خصم
     */
    public function applyDiscount(Employee $user, Invoice $invoice): bool
    {
        // منع تطبيق خصم على الفواتير المدفوعة
        if (in_array($invoice->status, ['paid', 'partially_paid'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'sales_manager']) || 
               $user->hasPermissionTo('apply_invoice_discounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل الضرائب
     */
    public function updateTaxes(Employee $user, Invoice $invoice): bool
    {
        // منع تعديل الضرائب للفواتير المدفوعة
        if (in_array($invoice->status, ['paid', 'partially_paid'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'accountant']) || 
               $user->hasPermissionTo('update_invoice_taxes');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إرسال تذكيرات
     */
    public function sendReminders(Employee $user, Invoice $invoice): bool
    {
        // يجب أن تكون الفاتورة مرسلة ومتأخرة
        if ($invoice->status !== 'sent' || !$invoice->is_overdue) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'accountant', 'sales_manager']) || 
               $user->hasPermissionTo('send_invoice_reminders');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تاريخ الفاتورة
     */
    public function viewHistory(Employee $user, Invoice $invoice): bool
    {
        return $this->view($user, $invoice) && 
               ($user->hasRole(['admin', 'financial_manager', 'accountant', 'auditor']) || 
                $user->hasPermissionTo('view_invoice_history'));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء استرداد
     */
    public function refund(Employee $user, Invoice $invoice): bool
    {
        // يجب أن تكون الفاتورة مدفوعة
        if (!in_array($invoice->status, ['paid', 'partially_paid'])) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('refund_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير الفواتير
     */
    public function export(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager', 'accountant', 'auditor']) || 
               $user->hasPermissionTo('export_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد الفواتير
     */
    public function import(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('import_invoices');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء مراجعة للفاتورة
     */
    public function audit(Employee $user, Invoice $invoice): bool
    {
        return $user->hasRole(['admin', 'auditor', 'financial_manager']) || 
               $user->hasPermissionTo('audit_invoices');
    }
}
