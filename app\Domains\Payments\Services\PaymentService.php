<?php

namespace App\Domains\Payments\Services;

use App\Domains\Payments\Models\PaymentGateway;
use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\PaymentMethod;
use App\Domains\Payments\Models\DigitalWallet;
use App\Domains\Payments\Factories\PaymentGatewayFactory;
use App\Domains\Payments\Services\PaymentGatewayService;
use App\Domains\Payments\Services\FraudDetectionService;
use App\Domains\Payments\Services\CurrencyExchangeService;
use App\Domains\Payments\Events\PaymentInitiated;
use App\Domains\Payments\Events\PaymentCompleted;
use App\Domains\Payments\Events\PaymentFailed;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة المدفوعات الرئيسية
 * تدير جميع عمليات الدفع في النظام
 */
class PaymentService
{
    protected PaymentGatewayService $gatewayService;
    protected FraudDetectionService $fraudService;
    protected CurrencyExchangeService $currencyService;

    public function __construct(
        PaymentGatewayService $gatewayService,
        FraudDetectionService $fraudService,
        CurrencyExchangeService $currencyService
    ) {
        $this->gatewayService = $gatewayService;
        $this->fraudService = $fraudService;
        $this->currencyService = $currencyService;
    }

    /**
     * معالجة دفعة جديدة
     */
    public function processPayment(array $paymentData): PaymentTransaction
    {
        return DB::transaction(function () use ($paymentData) {
            // التحقق من صحة البيانات
            $this->validatePaymentData($paymentData);

            // اختيار أفضل بوابة دفع
            $gateway = $this->selectOptimalGateway($paymentData);

            // إنشاء معاملة الدفع
            $transaction = $this->createPaymentTransaction($paymentData, $gateway);

            // فحص الاحتيال
            $fraudResult = $this->fraudService->analyzeTransaction($transaction);
            $transaction->updateRiskScore($fraudResult['risk_score'], $fraudResult);

            // معالجة الدفع حسب مستوى المخاطر
            if ($fraudResult['risk_level'] === 'high') {
                $transaction->updateStatus('requires_confirmation');
                return $transaction;
            }

            // معالجة الدفع عبر البوابة
            $result = $this->processPaymentThroughGateway($transaction, $gateway, $paymentData);

            // تحديث حالة المعاملة
            $this->updateTransactionFromGatewayResult($transaction, $result);

            // إطلاق الأحداث
            if ($transaction->isSuccessful()) {
                event(new PaymentCompleted($transaction));
            } else {
                event(new PaymentFailed($transaction));
            }

            return $transaction;
        });
    }

    /**
     * التحقق من صحة بيانات الدفع
     */
    protected function validatePaymentData(array $data): void
    {
        $required = ['amount', 'currency', 'payment_method', 'customer_email'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \InvalidArgumentException("الحقل {$field} مطلوب");
            }
        }

        if ($data['amount'] <= 0) {
            throw new \InvalidArgumentException('المبلغ يجب أن يكون أكبر من صفر');
        }

        if (!in_array($data['currency'], ['SAR', 'AED', 'EGP', 'MAD', 'USD', 'EUR'])) {
            throw new \InvalidArgumentException('العملة غير مدعومة');
        }
    }

    /**
     * اختيار أفضل بوابة دفع
     */
    protected function selectOptimalGateway(array $paymentData): PaymentGateway
    {
        $country = $paymentData['country'] ?? 'SA';
        $currency = $paymentData['currency'];
        $amount = $paymentData['amount'];
        $paymentMethod = $paymentData['payment_method'];

        // الحصول على البوابات المناسبة
        $gateways = PaymentGateway::active()
            ->forCountry($country)
            ->forCurrency($currency)
            ->forPaymentMethod($paymentMethod)
            ->orderByPriority()
            ->get();

        if ($gateways->isEmpty()) {
            throw new \Exception('لا توجد بوابة دفع متاحة لهذه المعاملة');
        }

        // اختيار أفضل بوابة بناءً على عوامل متعددة
        foreach ($gateways as $gateway) {
            // التحقق من الحدود
            $limitsCheck = $gateway->checkLimits($amount, $currency);
            if (!$limitsCheck['valid']) {
                continue;
            }

            // التحقق من معدل النجاح
            $successRate = $this->getGatewaySuccessRate($gateway, $country);
            if ($successRate < 0.85) { // 85% كحد أدنى
                continue;
            }

            return $gateway;
        }

        // إذا لم نجد بوابة مناسبة، نأخذ الأولى
        return $gateways->first();
    }

    /**
     * الحصول على معدل نجاح البوابة
     */
    protected function getGatewaySuccessRate(PaymentGateway $gateway, string $country): float
    {
        $total = PaymentTransaction::where('gateway_id', $gateway->id)
            ->where('country_code', $country)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        if ($total === 0) {
            return 1.0; // بوابة جديدة
        }

        $successful = PaymentTransaction::where('gateway_id', $gateway->id)
            ->where('country_code', $country)
            ->where('status', 'succeeded')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return $successful / $total;
    }

    /**
     * إنشاء معاملة الدفع
     */
    protected function createPaymentTransaction(array $data, PaymentGateway $gateway): PaymentTransaction
    {
        $exchangeRate = 1;
        $baseCurrency = config('app.base_currency', 'USD');
        
        if ($data['currency'] !== $baseCurrency) {
            $exchangeRate = $this->currencyService->getExchangeRate($data['currency'], $baseCurrency);
        }

        $fees = $gateway->calculateFees($data['amount'], $data['currency']);

        return PaymentTransaction::create([
            'gateway_id' => $gateway->id,
            'user_id' => $data['user_id'] ?? null,
            'payable_type' => $data['payable_type'] ?? null,
            'payable_id' => $data['payable_id'] ?? null,
            'amount' => $data['amount'],
            'currency' => $data['currency'],
            'exchange_rate' => $exchangeRate,
            'amount_in_base_currency' => $data['amount'] * $exchangeRate,
            'fees' => $fees['amount'],
            'net_amount' => $data['amount'] - $fees['amount'],
            'status' => 'pending',
            'payment_method' => $data['payment_method'],
            'country_code' => $data['country'] ?? 'SA',
            'ip_address' => $data['ip_address'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
            'customer_email' => $data['customer_email'],
            'customer_phone' => $data['customer_phone'] ?? null,
            'billing_address' => $data['billing_address'] ?? null,
            'shipping_address' => $data['shipping_address'] ?? null,
            'description' => $data['description'] ?? 'دفعة',
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * معالجة الدفع عبر البوابة
     */
    protected function processPaymentThroughGateway(
        PaymentTransaction $transaction,
        PaymentGateway $gateway,
        array $paymentData
    ): array {
        try {
            // الحصول على معالج البوابة
            $gatewayProcessor = PaymentGatewayFactory::create($gateway);

            // تحضير بيانات الدفع
            $processData = $this->prepareGatewayData($transaction, $paymentData);

            // معالجة الدفع
            $result = $gatewayProcessor->processPayment($processData);

            Log::info('تمت معالجة الدفع عبر البوابة', [
                'transaction_id' => $transaction->transaction_id,
                'gateway' => $gateway->name,
                'result' => $result,
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('فشل في معالجة الدفع عبر البوابة', [
                'transaction_id' => $transaction->transaction_id,
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'GATEWAY_ERROR',
            ];
        }
    }

    /**
     * تحضير بيانات البوابة
     */
    protected function prepareGatewayData(PaymentTransaction $transaction, array $paymentData): array
    {
        return [
            'transaction_id' => $transaction->transaction_id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency,
            'customer_email' => $transaction->customer_email,
            'customer_phone' => $transaction->customer_phone,
            'billing_address' => $transaction->billing_address,
            'payment_method' => $paymentData['payment_method_details'] ?? [],
            'return_url' => $paymentData['return_url'] ?? config('app.url'),
            'webhook_url' => route('payments.webhook', ['gateway' => $transaction->gateway->slug]),
            'metadata' => array_merge($transaction->metadata ?? [], [
                'internal_transaction_id' => $transaction->id,
            ]),
        ];
    }

    /**
     * تحديث المعاملة من نتيجة البوابة
     */
    protected function updateTransactionFromGatewayResult(PaymentTransaction $transaction, array $result): void
    {
        $updateData = [
            'gateway_response' => $result,
        ];

        if ($result['success'] ?? false) {
            $updateData['status'] = 'succeeded';
            $updateData['gateway_transaction_id'] = $result['transaction_id'] ?? null;
            $updateData['gateway_reference'] = $result['reference'] ?? null;
            $updateData['processed_at'] = now();
        } else {
            $updateData['status'] = 'failed';
            $updateData['failure_reason'] = $result['error'] ?? 'Unknown error';
            $updateData['failed_at'] = now();
        }

        $transaction->update($updateData);
    }

    /**
     * معالجة الدفع من المحفظة الرقمية
     */
    public function processWalletPayment(User $user, array $paymentData): PaymentTransaction
    {
        return DB::transaction(function () use ($user, $paymentData) {
            // الحصول على المحفظة
            $wallet = $user->digitalWallets()
                ->byCurrency($paymentData['currency'])
                ->usable()
                ->first();

            if (!$wallet) {
                throw new \Exception('المحفظة غير متاحة أو غير نشطة');
            }

            // التحقق من الرصيد
            if (!$wallet->hasSufficientBalance($paymentData['amount'])) {
                throw new \Exception('الرصيد غير كافي');
            }

            // التحقق من الحدود
            $limitsCheck = $wallet->checkTransactionLimits($paymentData['amount'], 'payment');
            if (!$limitsCheck['valid']) {
                throw new \Exception('تجاوز حدود المعاملة: ' . implode(', ', $limitsCheck['errors']));
            }

            // إنشاء معاملة الدفع
            $transaction = PaymentTransaction::create([
                'user_id' => $user->id,
                'payable_type' => $paymentData['payable_type'] ?? null,
                'payable_id' => $paymentData['payable_id'] ?? null,
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'],
                'fees' => 0, // لا رسوم على المحفظة الداخلية
                'net_amount' => $paymentData['amount'],
                'status' => 'succeeded',
                'payment_method' => 'digital_wallet',
                'customer_email' => $user->email,
                'description' => $paymentData['description'] ?? 'دفع من المحفظة الرقمية',
                'metadata' => array_merge($paymentData['metadata'] ?? [], [
                    'wallet_id' => $wallet->wallet_id,
                ]),
                'processed_at' => now(),
            ]);

            // خصم المبلغ من المحفظة
            $wallet->deductBalance(
                $paymentData['amount'],
                "دفع - {$transaction->transaction_id}",
                ['transaction_id' => $transaction->transaction_id]
            );

            event(new PaymentCompleted($transaction));

            return $transaction;
        });
    }

    /**
     * معالجة استرداد الأموال
     */
    public function processRefund(PaymentTransaction $transaction, float $amount = null, string $reason = null): array
    {
        if (!$transaction->isRefundable()) {
            throw new \Exception('هذه المعاملة غير قابلة للاسترداد');
        }

        $refundAmount = $amount ?? $transaction->getRefundableAmount();

        return DB::transaction(function () use ($transaction, $refundAmount, $reason) {
            // معالجة الاسترداد عبر البوابة
            if ($transaction->gateway_id) {
                $gatewayProcessor = PaymentGatewayFactory::create($transaction->gateway);
                $refundResult = $gatewayProcessor->processRefund($transaction, $refundAmount, $reason);
                
                if (!$refundResult['success']) {
                    throw new \Exception('فشل في معالجة الاسترداد: ' . $refundResult['error']);
                }
            }

            // إنشاء سجل الاسترداد
            $refund = $transaction->processRefund($refundAmount, $reason);

            // إذا كان الدفع من المحفظة، أعد المبلغ إليها
            if ($transaction->payment_method === 'digital_wallet' && $transaction->user) {
                $wallet = $transaction->user->digitalWallets()
                    ->byCurrency($transaction->currency)
                    ->first();

                if ($wallet) {
                    $wallet->addBalance(
                        $refundAmount,
                        "استرداد - {$transaction->transaction_id}",
                        ['refund_id' => $refund->refund_id]
                    );
                }
            }

            return [
                'success' => true,
                'refund' => $refund,
                'transaction' => $transaction->fresh(),
            ];
        });
    }

    /**
     * الحصول على طرق الدفع المتاحة
     */
    public function getAvailablePaymentMethods(string $country, string $currency, float $amount): array
    {
        $gateways = PaymentGateway::active()
            ->forCountry($country)
            ->forCurrency($currency)
            ->get();

        $methods = [];

        foreach ($gateways as $gateway) {
            $limitsCheck = $gateway->checkLimits($amount, $currency);
            if (!$limitsCheck['valid']) {
                continue;
            }

            foreach ($gateway->supported_payment_methods as $method) {
                if (!isset($methods[$method])) {
                    $methods[$method] = [
                        'type' => $method,
                        'name' => PaymentTransaction::PAYMENT_METHODS[$method] ?? $method,
                        'gateways' => [],
                        'fees' => [],
                    ];
                }

                $fees = $gateway->calculateFees($amount, $currency);
                $methods[$method]['gateways'][] = [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'provider' => $gateway->provider,
                    'fees' => $fees,
                ];
                $methods[$method]['fees'][] = $fees['amount'];
            }
        }

        // ترتيب حسب أقل رسوم
        foreach ($methods as &$method) {
            $method['min_fees'] = min($method['fees']);
            usort($method['gateways'], fn($a, $b) => $a['fees']['amount'] <=> $b['fees']['amount']);
        }

        return array_values($methods);
    }

    /**
     * الحصول على إحصائيات المدفوعات
     */
    public function getPaymentStatistics(array $filters = []): array
    {
        $query = PaymentTransaction::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['currency'])) {
            $query->where('currency', $filters['currency']);
        }

        if (isset($filters['gateway_id'])) {
            $query->where('gateway_id', $filters['gateway_id']);
        }

        $total = $query->count();
        $successful = $query->clone()->successful()->count();
        $failed = $query->clone()->failed()->count();
        $totalAmount = $query->clone()->successful()->sum('amount');
        $totalFees = $query->clone()->successful()->sum('fees');

        return [
            'total_transactions' => $total,
            'successful_transactions' => $successful,
            'failed_transactions' => $failed,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
            'total_amount' => $totalAmount,
            'total_fees' => $totalFees,
            'net_amount' => $totalAmount - $totalFees,
            'average_transaction_amount' => $successful > 0 ? round($totalAmount / $successful, 2) : 0,
        ];
    }
}
