<?php

namespace App\Domains\Ecommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * نموذج المنتج
 */
class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'sku',
        'price',
        'cost',
        'category_id',
        'brand_id',
        'status',
        'stock_quantity',
        'min_stock_level',
        'weight',
        'dimensions',
        'images',
        'attributes',
        'seo_title',
        'seo_description',
        'meta_keywords',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost' => 'decimal:2',
        'weight' => 'decimal:3',
        'dimensions' => 'array',
        'images' => 'array',
        'attributes' => 'array',
        'meta_keywords' => 'array',
    ];

    /**
     * حالات المنتج
     */
    public const STATUSES = [
        'ACTIVE' => 'نشط',
        'INACTIVE' => 'غير نشط',
        'DRAFT' => 'مسودة',
        'OUT_OF_STOCK' => 'نفد المخزون',
    ];

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * فحص إذا كان المنتج متوفر
     */
    public function isAvailable(): bool
    {
        return $this->status === 'ACTIVE' && $this->stock_quantity > 0;
    }

    /**
     * فحص إذا كان المخزون منخفض
     */
    public function isLowStock(): bool
    {
        return $this->stock_quantity <= $this->min_stock_level;
    }

    /**
     * حساب هامش الربح
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost <= 0) {
            return 0;
        }
        
        return (($this->price - $this->cost) / $this->cost) * 100;
    }
}
