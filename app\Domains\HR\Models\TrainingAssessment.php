<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج تقييم التدريب
 */
class TrainingAssessment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'training_program_id',
        'title',
        'description',
        'type',
        'total_marks',
        'passing_marks',
        'duration_minutes',
        'instructions',
        'is_active',
        'start_date',
        'end_date',
        'attempts_allowed',
        'show_results',
        'randomize_questions',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'total_marks' => 'decimal:2',
        'passing_marks' => 'decimal:2',
        'duration_minutes' => 'integer',
        'attempts_allowed' => 'integer',
        'show_results' => 'boolean',
        'randomize_questions' => 'boolean',
    ];

    /**
     * أنواع التقييم
     */
    public const TYPES = [
        'QUIZ' => 'اختبار قصير',
        'EXAM' => 'امتحان',
        'ASSIGNMENT' => 'مهمة',
        'PROJECT' => 'مشروع',
        'PRESENTATION' => 'عرض تقديمي',
        'PRACTICAL' => 'تطبيق عملي',
    ];

    /**
     * البرنامج التدريبي
     */
    public function trainingProgram(): BelongsTo
    {
        return $this->belongsTo(TrainingProgram::class);
    }

    /**
     * من أنشأ التقييم
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    /**
     * أسئلة التقييم
     */
    public function questions(): HasMany
    {
        return $this->hasMany(AssessmentQuestion::class);
    }

    /**
     * محاولات التقييم
     */
    public function attempts(): HasMany
    {
        return $this->hasMany(AssessmentAttempt::class);
    }

    /**
     * الحصول على اسم النوع
     */
    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * الحصول على نسبة النجاح
     */
    public function getPassingPercentageAttribute(): float
    {
        return $this->total_marks > 0 ? ($this->passing_marks / $this->total_marks) * 100 : 0;
    }

    /**
     * فحص إذا كان التقييم نشط
     */
    public function isActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        $now = now();
        
        if ($this->start_date && $now->lt($this->start_date)) {
            return false;
        }
        
        if ($this->end_date && $now->gt($this->end_date)) {
            return false;
        }
        
        return true;
    }

    /**
     * فحص إذا كان التقييم متاح للموظف
     */
    public function isAvailableFor(Employee $employee): bool
    {
        if (!$this->isActive()) {
            return false;
        }
        
        // التحقق من التسجيل في البرنامج
        $enrollment = $this->trainingProgram->enrollments()
            ->where('employee_id', $employee->id)
            ->first();
            
        if (!$enrollment || !$enrollment->isActive()) {
            return false;
        }
        
        // التحقق من عدد المحاولات
        if ($this->attempts_allowed > 0) {
            $attemptCount = $this->attempts()
                ->where('employee_id', $employee->id)
                ->count();
                
            if ($attemptCount >= $this->attempts_allowed) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * الحصول على أفضل نتيجة للموظف
     */
    public function getBestScoreFor(Employee $employee): ?float
    {
        return $this->attempts()
            ->where('employee_id', $employee->id)
            ->where('status', 'COMPLETED')
            ->max('score');
    }

    /**
     * الحصول على آخر محاولة للموظف
     */
    public function getLastAttemptFor(Employee $employee): ?AssessmentAttempt
    {
        return $this->attempts()
            ->where('employee_id', $employee->id)
            ->latest()
            ->first();
    }

    /**
     * الحصول على إحصائيات التقييم
     */
    public function getStatistics(): array
    {
        $attempts = $this->attempts()->where('status', 'COMPLETED');
        
        return [
            'total_attempts' => $attempts->count(),
            'average_score' => $attempts->avg('score') ?? 0,
            'highest_score' => $attempts->max('score') ?? 0,
            'lowest_score' => $attempts->min('score') ?? 0,
            'pass_rate' => $this->getPassRate(),
        ];
    }

    /**
     * الحصول على معدل النجاح
     */
    protected function getPassRate(): float
    {
        $totalAttempts = $this->attempts()->where('status', 'COMPLETED')->count();
        
        if ($totalAttempts === 0) {
            return 0;
        }
        
        $passedAttempts = $this->attempts()
            ->where('status', 'COMPLETED')
            ->where('score', '>=', $this->passing_marks)
            ->count();
            
        return ($passedAttempts / $totalAttempts) * 100;
    }

    /**
     * Scope للتقييمات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للتقييمات حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope للتقييمات المتاحة حالياً
     */
    public function scopeAvailable($query)
    {
        $now = now();
        
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('start_date')
                  ->orWhere('start_date', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('end_date')
                  ->orWhere('end_date', '>=', $now);
            });
    }
}
