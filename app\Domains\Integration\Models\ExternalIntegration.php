<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

/**
 * نموذج التكاملات الخارجية المتقدمة
 * يدير جميع التكاملات مع الأنظمة الخارجية والخدمات
 */
class ExternalIntegration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'integration_id',
        'name',
        'description',
        'provider',
        'category',
        'integration_type',
        'status',
        'environment',
        'base_url',
        'api_version',
        'authentication_type',
        'authentication_config',
        'connection_config',
        'sync_config',
        'mapping_config',
        'webhook_config',
        'rate_limit_config',
        'retry_config',
        'timeout_config',
        'security_config',
        'compliance_config',
        'monitoring_config',
        'data_transformation_rules',
        'field_mappings',
        'supported_operations',
        'required_permissions',
        'optional_permissions',
        'webhook_endpoints',
        'callback_urls',
        'supported_events',
        'event_subscriptions',
        'last_sync_at',
        'last_health_check',
        'health_status',
        'sync_status',
        'total_syncs',
        'successful_syncs',
        'failed_syncs',
        'last_error',
        'error_count',
        'uptime_percentage',
        'average_response_time',
        'data_volume_in',
        'data_volume_out',
        'cost_per_request',
        'monthly_cost',
        'usage_limits',
        'current_usage',
        'metadata',
        'tags',
        'is_active',
        'auto_sync_enabled',
        'sync_frequency',
        'next_sync_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'authentication_config' => 'encrypted:array',
        'connection_config' => 'array',
        'sync_config' => 'array',
        'mapping_config' => 'array',
        'webhook_config' => 'array',
        'rate_limit_config' => 'array',
        'retry_config' => 'array',
        'timeout_config' => 'array',
        'security_config' => 'array',
        'compliance_config' => 'array',
        'monitoring_config' => 'array',
        'data_transformation_rules' => 'array',
        'field_mappings' => 'array',
        'supported_operations' => 'array',
        'required_permissions' => 'array',
        'optional_permissions' => 'array',
        'webhook_endpoints' => 'array',
        'callback_urls' => 'array',
        'supported_events' => 'array',
        'event_subscriptions' => 'array',
        'usage_limits' => 'array',
        'current_usage' => 'array',
        'metadata' => 'array',
        'tags' => 'array',
        'last_sync_at' => 'datetime',
        'last_health_check' => 'datetime',
        'next_sync_at' => 'datetime',
        'is_active' => 'boolean',
        'auto_sync_enabled' => 'boolean',
    ];

    /**
     * مقدمو الخدمات المدعومون
     */
    const PROVIDERS = [
        // البنوك
        'attijariwafa_bank' => 'Attijariwafa Bank',
        'al_rajhi_bank' => 'Al Rajhi Bank',
        'emirates_nbd' => 'Emirates NBD',
        'cib_bank' => 'CIB Bank',
        'banque_populaire' => 'Banque Populaire',
        'bmce_bank' => 'BMCE Bank',
        'arab_bank' => 'Arab Bank',
        
        // الهيئات الحكومية
        'dgi_morocco' => 'DGI Morocco',
        'zatca_saudi' => 'ZATCA Saudi Arabia',
        'eta_egypt' => 'ETA Egypt',
        'fta_uae' => 'FTA UAE',
        'cnss_morocco' => 'CNSS Morocco',
        'gosi_saudi' => 'GOSI Saudi Arabia',
        'qiwa_saudi' => 'Qiwa Saudi Arabia',
        
        // التجارة الإلكترونية
        'shopify' => 'Shopify',
        'woocommerce' => 'WooCommerce',
        'salla' => 'Salla',
        'zid' => 'Zid',
        'jumia' => 'Jumia',
        'youcan' => 'YouCan',
        'magento' => 'Magento',
        'bigcommerce' => 'BigCommerce',
        'alibaba' => 'Alibaba',
        'aliexpress' => 'AliExpress',
        
        // أنظمة الدفع
        'cmi_payment' => 'CMI Payment',
        'fawry' => 'Fawry',
        'stc_pay' => 'STC Pay',
        'mada' => 'Mada',
        'visa' => 'Visa',
        'mastercard' => 'Mastercard',
        'paypal' => 'PayPal',
        'stripe' => 'Stripe',
        
        // التسويق
        'facebook_ads' => 'Facebook Ads',
        'google_ads' => 'Google Ads',
        'bing_ads' => 'Bing Ads',
        'twitter_ads' => 'Twitter Ads',
        'linkedin_ads' => 'LinkedIn Ads',
        'snapchat_ads' => 'Snapchat Ads',
        
        // أنظمة ERP/CRM
        'odoo' => 'Odoo',
        'salesforce' => 'Salesforce',
        'hubspot' => 'HubSpot',
        'zoho' => 'Zoho',
        'microsoft_dynamics' => 'Microsoft Dynamics',
        'sap' => 'SAP',
        'oracle' => 'Oracle',
        
        // خدمات سحابية
        'aws' => 'Amazon Web Services',
        'azure' => 'Microsoft Azure',
        'google_cloud' => 'Google Cloud',
        'dropbox' => 'Dropbox',
        'onedrive' => 'OneDrive',
        'google_drive' => 'Google Drive',
        
        // أدوات أخرى
        'slack' => 'Slack',
        'teams' => 'Microsoft Teams',
        'zoom' => 'Zoom',
        'whatsapp_business' => 'WhatsApp Business',
        'telegram' => 'Telegram',
    ];

    /**
     * فئات التكامل
     */
    const CATEGORIES = [
        'banking' => 'البنوك والمصارف',
        'government' => 'الهيئات الحكومية',
        'ecommerce' => 'التجارة الإلكترونية',
        'payment' => 'أنظمة الدفع',
        'marketing' => 'التسويق والإعلان',
        'erp' => 'أنظمة تخطيط الموارد',
        'crm' => 'إدارة علاقات العملاء',
        'cloud_storage' => 'التخزين السحابي',
        'communication' => 'الاتصالات',
        'analytics' => 'التحليلات',
        'social_media' => 'وسائل التواصل الاجتماعي',
        'logistics' => 'اللوجستيات والشحن',
        'hr' => 'الموارد البشرية',
        'accounting' => 'المحاسبة',
        'taxation' => 'الضرائب',
        'compliance' => 'الامتثال',
        'security' => 'الأمان',
        'monitoring' => 'المراقبة',
        'backup' => 'النسخ الاحتياطي',
        'other' => 'أخرى',
    ];

    /**
     * أنواع التكامل
     */
    const INTEGRATION_TYPES = [
        'api' => 'API Integration',
        'webhook' => 'Webhook Integration',
        'file_transfer' => 'File Transfer',
        'database_sync' => 'Database Sync',
        'message_queue' => 'Message Queue',
        'real_time' => 'Real-time Sync',
        'batch' => 'Batch Processing',
        'streaming' => 'Data Streaming',
        'rpc' => 'Remote Procedure Call',
        'graphql' => 'GraphQL',
        'soap' => 'SOAP',
        'rest' => 'REST API',
        'grpc' => 'gRPC',
        'websocket' => 'WebSocket',
        'mcp' => 'Model Context Protocol',
    ];

    /**
     * أنواع المصادقة
     */
    const AUTHENTICATION_TYPES = [
        'api_key' => 'API Key',
        'oauth2' => 'OAuth 2.0',
        'oauth1' => 'OAuth 1.0',
        'basic_auth' => 'Basic Authentication',
        'bearer_token' => 'Bearer Token',
        'jwt' => 'JSON Web Token',
        'hmac' => 'HMAC Signature',
        'certificate' => 'Client Certificate',
        'custom' => 'Custom Authentication',
        'none' => 'No Authentication',
    ];

    /**
     * حالات التكامل
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'testing' => 'قيد الاختبار',
        'error' => 'خطأ',
        'maintenance' => 'صيانة',
        'suspended' => 'معلق',
        'deprecated' => 'مهجور',
        'pending_approval' => 'قيد الموافقة',
    ];

    /**
     * حالات الصحة
     */
    const HEALTH_STATUSES = [
        'healthy' => 'سليم',
        'unhealthy' => 'غير سليم',
        'degraded' => 'متدهور',
        'unknown' => 'غير معروف',
        'maintenance' => 'صيانة',
    ];

    /**
     * حالات المزامنة
     */
    const SYNC_STATUSES = [
        'idle' => 'خامل',
        'syncing' => 'يتم المزامنة',
        'completed' => 'مكتمل',
        'failed' => 'فشل',
        'partial' => 'جزئي',
        'cancelled' => 'ملغي',
    ];

    /**
     * العلاقة مع سجلات المزامنة
     */
    public function syncLogs(): HasMany
    {
        return $this->hasMany(IntegrationSyncLog::class);
    }

    /**
     * العلاقة مع سجلات الأخطاء
     */
    public function errorLogs(): HasMany
    {
        return $this->hasMany(IntegrationErrorLog::class);
    }

    /**
     * العلاقة مع منشئ التكامل
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * العلاقة مع محدث التكامل
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * اختبار الاتصال
     */
    public function testConnection(): array
    {
        try {
            $startTime = microtime(true);
            
            // إعداد العميل HTTP
            $client = $this->createHttpClient();
            
            // تنفيذ طلب اختبار
            $response = $this->executeTestRequest($client);
            
            $responseTime = microtime(true) - $startTime;
            
            // تحديث حالة الصحة
            $this->updateHealthStatus('healthy', $responseTime);
            
            return [
                'success' => true,
                'response_time' => $responseTime,
                'status_code' => $response['status_code'] ?? 200,
                'message' => 'Connection test successful',
                'data' => $response['data'] ?? null,
            ];
            
        } catch (\Exception $e) {
            // تحديث حالة الصحة
            $this->updateHealthStatus('unhealthy');
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Connection test failed',
            ];
        }
    }

    /**
     * تنفيذ مزامنة البيانات
     */
    public function syncData(array $options = []): array
    {
        try {
            $this->update(['sync_status' => 'syncing']);
            
            $syncLog = IntegrationSyncLog::create([
                'external_integration_id' => $this->id,
                'sync_type' => $options['sync_type'] ?? 'full',
                'status' => 'running',
                'started_at' => now(),
            ]);
            
            // تنفيذ المزامنة حسب النوع
            $result = match ($this->integration_type) {
                'api' => $this->syncViaApi($options),
                'webhook' => $this->syncViaWebhook($options),
                'file_transfer' => $this->syncViaFileTransfer($options),
                'database_sync' => $this->syncViaDatabase($options),
                'real_time' => $this->syncRealTime($options),
                'batch' => $this->syncBatch($options),
                default => throw new \Exception("Unsupported integration type: {$this->integration_type}"),
            };
            
            // تحديث سجل المزامنة
            $syncLog->update([
                'status' => 'completed',
                'completed_at' => now(),
                'records_processed' => $result['records_processed'] ?? 0,
                'records_success' => $result['records_success'] ?? 0,
                'records_failed' => $result['records_failed'] ?? 0,
                'data_volume' => $result['data_volume'] ?? 0,
                'result_data' => $result,
            ]);
            
            // تحديث إحصائيات التكامل
            $this->updateSyncStatistics(true);
            
            return $result;
            
        } catch (\Exception $e) {
            // تسجيل الخطأ
            $this->logError($e, 'sync_data', $options);
            
            // تحديث إحصائيات التكامل
            $this->updateSyncStatistics(false);
            
            if (isset($syncLog)) {
                $syncLog->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_message' => $e->getMessage(),
                ]);
            }
            
            throw $e;
        } finally {
            $this->update([
                'sync_status' => 'idle',
                'last_sync_at' => now(),
            ]);
        }
    }

    /**
     * إرسال webhook
     */
    public function sendWebhook(string $event, array $data): array
    {
        $webhookConfig = $this->webhook_config ?? [];
        $webhookUrls = $this->webhook_endpoints ?? [];
        
        if (empty($webhookUrls)) {
            throw new \Exception('No webhook URLs configured');
        }
        
        $results = [];
        
        foreach ($webhookUrls as $url) {
            try {
                $payload = [
                    'event' => $event,
                    'data' => $data,
                    'timestamp' => now()->toISOString(),
                    'integration_id' => $this->integration_id,
                    'signature' => $this->generateWebhookSignature($data),
                ];
                
                $response = Http::timeout($this->timeout_config['webhook_timeout'] ?? 30)
                    ->withHeaders($webhookConfig['headers'] ?? [])
                    ->post($url, $payload);
                
                $results[] = [
                    'url' => $url,
                    'success' => $response->successful(),
                    'status_code' => $response->status(),
                    'response' => $response->json(),
                ];
                
            } catch (\Exception $e) {
                $results[] = [
                    'url' => $url,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        return $results;
    }

    /**
     * تحويل البيانات
     */
    public function transformData(array $data, string $direction = 'inbound'): array
    {
        $transformationRules = $this->data_transformation_rules[$direction] ?? [];
        
        if (empty($transformationRules)) {
            return $data;
        }
        
        $transformed = $data;
        
        foreach ($transformationRules as $rule) {
            $transformed = $this->applyTransformationRule($transformed, $rule);
        }
        
        return $transformed;
    }

    /**
     * تطبيق تعيين الحقول
     */
    public function mapFields(array $data, string $direction = 'inbound'): array
    {
        $fieldMappings = $this->field_mappings[$direction] ?? [];
        
        if (empty($fieldMappings)) {
            return $data;
        }
        
        $mapped = [];
        
        foreach ($fieldMappings as $sourceField => $targetField) {
            if (isset($data[$sourceField])) {
                $mapped[$targetField] = $data[$sourceField];
            }
        }
        
        return $mapped;
    }

    /**
     * فحص الصحة
     */
    public function performHealthCheck(): array
    {
        try {
            $checks = [];
            
            // فحص الاتصال
            $connectionCheck = $this->testConnection();
            $checks['connection'] = $connectionCheck;
            
            // فحص المصادقة
            $authCheck = $this->testAuthentication();
            $checks['authentication'] = $authCheck;
            
            // فحص الصلاحيات
            $permissionsCheck = $this->testPermissions();
            $checks['permissions'] = $permissionsCheck;
            
            // فحص حدود الاستخدام
            $usageLimitsCheck = $this->checkUsageLimits();
            $checks['usage_limits'] = $usageLimitsCheck;
            
            // تحديد الحالة العامة
            $overallHealth = $this->calculateOverallHealth($checks);
            
            $this->update([
                'health_status' => $overallHealth['status'],
                'last_health_check' => now(),
                'metadata' => array_merge($this->metadata ?? [], [
                    'last_health_check_results' => $checks,
                    'health_score' => $overallHealth['score'],
                ]),
            ]);
            
            return $checks;
            
        } catch (\Exception $e) {
            $this->update([
                'health_status' => 'unhealthy',
                'last_health_check' => now(),
            ]);
            
            throw $e;
        }
    }

    /**
     * جدولة المزامنة التلقائية
     */
    public function scheduleAutoSync(): void
    {
        if (!$this->auto_sync_enabled || !$this->sync_frequency) {
            return;
        }
        
        $nextSync = match ($this->sync_frequency) {
            'every_minute' => now()->addMinute(),
            'every_5_minutes' => now()->addMinutes(5),
            'every_15_minutes' => now()->addMinutes(15),
            'every_30_minutes' => now()->addMinutes(30),
            'hourly' => now()->addHour(),
            'every_2_hours' => now()->addHours(2),
            'every_6_hours' => now()->addHours(6),
            'every_12_hours' => now()->addHours(12),
            'daily' => now()->addDay(),
            'weekly' => now()->addWeek(),
            'monthly' => now()->addMonth(),
            default => now()->addHour(),
        };
        
        $this->update(['next_sync_at' => $nextSync]);
    }

    /**
     * الحصول على إحصائيات الاستخدام
     */
    public function getUsageStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $syncLogs = $this->syncLogs()
                        ->where('created_at', '>=', $startDate)
                        ->get();
        
        return [
            'total_syncs' => $syncLogs->count(),
            'successful_syncs' => $syncLogs->where('status', 'completed')->count(),
            'failed_syncs' => $syncLogs->where('status', 'failed')->count(),
            'average_sync_time' => $syncLogs->avg('duration'),
            'total_records_processed' => $syncLogs->sum('records_processed'),
            'total_data_volume' => $syncLogs->sum('data_volume'),
            'success_rate' => $syncLogs->count() > 0 ? 
                ($syncLogs->where('status', 'completed')->count() / $syncLogs->count()) * 100 : 0,
            'uptime_percentage' => $this->uptime_percentage ?? 0,
            'average_response_time' => $this->average_response_time ?? 0,
        ];
    }

    // طرق مساعدة محمية
    protected function createHttpClient()
    {
        $client = Http::timeout($this->timeout_config['request_timeout'] ?? 30);
        
        // إضافة المصادقة
        $client = $this->addAuthentication($client);
        
        // إضافة headers مخصصة
        if (isset($this->connection_config['headers'])) {
            $client = $client->withHeaders($this->connection_config['headers']);
        }
        
        return $client;
    }

    protected function addAuthentication($client)
    {
        $authConfig = $this->authentication_config ?? [];
        
        return match ($this->authentication_type) {
            'api_key' => $client->withHeaders([
                $authConfig['header_name'] ?? 'X-API-Key' => $authConfig['api_key']
            ]),
            'bearer_token' => $client->withToken($authConfig['token']),
            'basic_auth' => $client->withBasicAuth($authConfig['username'], $authConfig['password']),
            'oauth2' => $client->withHeaders([
                'Authorization' => 'Bearer ' . $authConfig['access_token']
            ]),
            default => $client,
        };
    }

    protected function executeTestRequest($client): array
    {
        $testEndpoint = $this->connection_config['test_endpoint'] ?? '/health';
        $fullUrl = rtrim($this->base_url, '/') . '/' . ltrim($testEndpoint, '/');
        
        $response = $client->get($fullUrl);
        
        return [
            'status_code' => $response->status(),
            'data' => $response->json(),
            'headers' => $response->headers(),
        ];
    }

    protected function updateHealthStatus(string $status, float $responseTime = null): void
    {
        $updateData = [
            'health_status' => $status,
            'last_health_check' => now(),
        ];
        
        if ($responseTime !== null) {
            $updateData['average_response_time'] = $responseTime;
        }
        
        $this->update($updateData);
    }

    protected function updateSyncStatistics(bool $success): void
    {
        $this->increment('total_syncs');
        
        if ($success) {
            $this->increment('successful_syncs');
        } else {
            $this->increment('failed_syncs');
        }
    }

    protected function logError(\Exception $e, string $operation, array $context = []): void
    {
        IntegrationErrorLog::create([
            'external_integration_id' => $this->id,
            'operation' => $operation,
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'stack_trace' => $e->getTraceAsString(),
            'context' => $context,
        ]);
        
        $this->increment('error_count');
        $this->update(['last_error' => $e->getMessage()]);
    }

    protected function generateWebhookSignature(array $data): string
    {
        $secret = $this->webhook_config['secret'] ?? '';
        return hash_hmac('sha256', json_encode($data), $secret);
    }

    protected function applyTransformationRule(array $data, array $rule): array
    {
        // تطبيق قواعد التحويل
        // هذا سيتم تطويره حسب نوع القاعدة
        return $data;
    }

    protected function calculateOverallHealth(array $checks): array
    {
        $totalScore = 0;
        $maxScore = 0;
        
        foreach ($checks as $check) {
            $maxScore += 100;
            if ($check['success'] ?? false) {
                $totalScore += 100;
            }
        }
        
        $overallScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 100;
        
        $status = match (true) {
            $overallScore >= 90 => 'healthy',
            $overallScore >= 70 => 'degraded',
            default => 'unhealthy',
        };
        
        return ['score' => $overallScore, 'status' => $status];
    }

    // طرق للتطوير المستقبلي
    protected function syncViaApi(array $options): array { return []; }
    protected function syncViaWebhook(array $options): array { return []; }
    protected function syncViaFileTransfer(array $options): array { return []; }
    protected function syncViaDatabase(array $options): array { return []; }
    protected function syncRealTime(array $options): array { return []; }
    protected function syncBatch(array $options): array { return []; }
    protected function testAuthentication(): array { return ['success' => true]; }
    protected function testPermissions(): array { return ['success' => true]; }
    protected function checkUsageLimits(): array { return ['success' => true]; }
}
