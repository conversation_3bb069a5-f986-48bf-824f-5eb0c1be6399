<?php

namespace App\Domains\Integration\Services\Analytics\Processors;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Metrics Processor for Analytics
 * 
 * Processes and transforms raw metrics data into meaningful insights
 */
class MetricsProcessor
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'aggregation_window' => 300, // 5 minutes
            'retention_period' => 86400 * 30, // 30 days
        ], $config);
    }

    /**
     * Process raw metrics data
     */
    public function processMetrics(array $rawMetrics): array
    {
        try {
            $processedMetrics = [
                'timestamp' => now()->toISOString(),
                'gateway_id' => $rawMetrics['gateway_id'] ?? null,
                'processed_data' => [],
            ];

            // Process performance metrics
            if (isset($rawMetrics['performance'])) {
                $processedMetrics['processed_data']['performance'] = $this->processPerformanceMetrics($rawMetrics['performance']);
            }

            // Process usage metrics
            if (isset($rawMetrics['usage'])) {
                $processedMetrics['processed_data']['usage'] = $this->processUsageMetrics($rawMetrics['usage']);
            }

            // Process error metrics
            if (isset($rawMetrics['errors'])) {
                $processedMetrics['processed_data']['errors'] = $this->processErrorMetrics($rawMetrics['errors']);
            }

            // Process security metrics
            if (isset($rawMetrics['security'])) {
                $processedMetrics['processed_data']['security'] = $this->processSecurityMetrics($rawMetrics['security']);
            }

            // Calculate derived metrics
            $processedMetrics['derived_metrics'] = $this->calculateDerivedMetrics($processedMetrics['processed_data']);

            // Generate alerts if needed
            $processedMetrics['alerts'] = $this->generateAlerts($processedMetrics);

            return $processedMetrics;

        } catch (\Exception $e) {
            Log::error('Metrics processing failed', [
                'error' => $e->getMessage(),
                'raw_metrics' => $rawMetrics,
            ]);

            return [
                'timestamp' => now()->toISOString(),
                'error' => 'Processing failed: ' . $e->getMessage(),
                'raw_metrics' => $rawMetrics,
            ];
        }
    }

    /**
     * Process performance metrics
     */
    protected function processPerformanceMetrics(array $performanceData): array
    {
        $processed = [
            'response_time' => [
                'current' => $performanceData['avg_response_time'] ?? 0,
                'trend' => $this->calculateTrend('response_time', $performanceData['avg_response_time'] ?? 0),
                'percentiles' => $this->calculatePercentiles($performanceData['response_times'] ?? []),
            ],
            'throughput' => [
                'current' => $performanceData['requests_per_second'] ?? 0,
                'trend' => $this->calculateTrend('throughput', $performanceData['requests_per_second'] ?? 0),
                'peak' => $this->calculatePeakThroughput($performanceData),
            ],
            'resource_utilization' => [
                'cpu' => $performanceData['cpu_usage'] ?? 0,
                'memory' => $performanceData['memory_usage'] ?? 0,
                'disk' => $performanceData['disk_usage'] ?? 0,
                'network' => $performanceData['network_usage'] ?? 0,
            ],
        ];

        // Calculate performance score
        $processed['performance_score'] = $this->calculatePerformanceScore($processed);

        return $processed;
    }

    /**
     * Process usage metrics
     */
    protected function processUsageMetrics(array $usageData): array
    {
        $processed = [
            'total_requests' => $usageData['total_requests'] ?? 0,
            'unique_users' => $usageData['unique_users'] ?? 0,
            'api_calls_by_endpoint' => $usageData['endpoint_usage'] ?? [],
            'geographic_distribution' => $usageData['geo_distribution'] ?? [],
            'time_distribution' => $this->processTimeDistribution($usageData),
            'user_behavior' => $this->analyzeUserBehavior($usageData),
        ];

        // Calculate usage patterns
        $processed['usage_patterns'] = $this->identifyUsagePatterns($processed);

        return $processed;
    }

    /**
     * Process error metrics
     */
    protected function processErrorMetrics(array $errorData): array
    {
        $processed = [
            'error_rate' => [
                'current' => $errorData['error_rate'] ?? 0,
                'trend' => $this->calculateTrend('error_rate', $errorData['error_rate'] ?? 0),
                'by_type' => $errorData['error_types'] ?? [],
            ],
            'error_distribution' => [
                'by_endpoint' => $errorData['errors_by_endpoint'] ?? [],
                'by_status_code' => $errorData['errors_by_status'] ?? [],
                'by_time' => $errorData['errors_by_time'] ?? [],
            ],
            'top_errors' => $this->rankErrors($errorData['top_errors'] ?? []),
            'error_impact' => $this->calculateErrorImpact($errorData),
        ];

        return $processed;
    }

    /**
     * Process security metrics
     */
    protected function processSecurityMetrics(array $securityData): array
    {
        $processed = [
            'threat_level' => $this->calculateThreatLevel($securityData),
            'incidents' => [
                'total' => $securityData['total_incidents'] ?? 0,
                'by_type' => $securityData['incidents_by_type'] ?? [],
                'by_severity' => $securityData['incidents_by_severity'] ?? [],
            ],
            'blocked_requests' => $securityData['blocked_requests'] ?? 0,
            'suspicious_activities' => $this->identifySuspiciousActivities($securityData),
            'security_score' => $this->calculateSecurityScore($securityData),
        ];

        return $processed;
    }

    /**
     * Calculate derived metrics
     */
    protected function calculateDerivedMetrics(array $processedData): array
    {
        $derived = [];

        // Calculate availability
        if (isset($processedData['errors']['error_rate']['current'])) {
            $errorRate = $processedData['errors']['error_rate']['current'];
            $derived['availability'] = max(0, 100 - $errorRate);
        }

        // Calculate efficiency score
        $derived['efficiency_score'] = $this->calculateEfficiencyScore($processedData);

        // Calculate user satisfaction index
        $derived['user_satisfaction_index'] = $this->calculateUserSatisfactionIndex($processedData);

        // Calculate cost efficiency
        $derived['cost_efficiency'] = $this->calculateCostEfficiency($processedData);

        // Calculate business impact score
        $derived['business_impact_score'] = $this->calculateBusinessImpactScore($processedData);

        return $derived;
    }

    /**
     * Generate alerts based on processed metrics
     */
    protected function generateAlerts(array $processedMetrics): array
    {
        $alerts = [];

        // Performance alerts
        if (isset($processedMetrics['processed_data']['performance'])) {
            $perfAlerts = $this->checkPerformanceAlerts($processedMetrics['processed_data']['performance']);
            $alerts = array_merge($alerts, $perfAlerts);
        }

        // Error rate alerts
        if (isset($processedMetrics['processed_data']['errors'])) {
            $errorAlerts = $this->checkErrorAlerts($processedMetrics['processed_data']['errors']);
            $alerts = array_merge($alerts, $errorAlerts);
        }

        // Security alerts
        if (isset($processedMetrics['processed_data']['security'])) {
            $securityAlerts = $this->checkSecurityAlerts($processedMetrics['processed_data']['security']);
            $alerts = array_merge($alerts, $securityAlerts);
        }

        return $alerts;
    }

    /**
     * Calculate trend for a metric
     */
    protected function calculateTrend(string $metric, float $currentValue): array
    {
        $historicalKey = "metric_history:{$metric}";
        $history = Cache::get($historicalKey, []);
        
        // Add current value to history
        $history[] = [
            'value' => $currentValue,
            'timestamp' => now()->timestamp,
        ];
        
        // Keep only last 24 hours
        $cutoff = now()->subDay()->timestamp;
        $history = array_filter($history, function ($item) use ($cutoff) {
            return $item['timestamp'] > $cutoff;
        });
        
        // Store updated history
        Cache::put($historicalKey, $history, 86400);
        
        // Calculate trend
        if (count($history) < 2) {
            return ['direction' => 'stable', 'change_percent' => 0];
        }
        
        $recent = array_slice($history, -5); // Last 5 values
        $older = array_slice($history, -10, 5); // Previous 5 values
        
        $recentAvg = array_sum(array_column($recent, 'value')) / count($recent);
        $olderAvg = count($older) > 0 ? array_sum(array_column($older, 'value')) / count($older) : $recentAvg;
        
        $changePercent = $olderAvg > 0 ? (($recentAvg - $olderAvg) / $olderAvg) * 100 : 0;
        
        $direction = 'stable';
        if ($changePercent > 5) {
            $direction = 'increasing';
        } elseif ($changePercent < -5) {
            $direction = 'decreasing';
        }
        
        return [
            'direction' => $direction,
            'change_percent' => round($changePercent, 2),
        ];
    }

    /**
     * Calculate percentiles for response times
     */
    protected function calculatePercentiles(array $values): array
    {
        if (empty($values)) {
            return ['p50' => 0, 'p90' => 0, 'p95' => 0, 'p99' => 0];
        }
        
        sort($values);
        $count = count($values);
        
        return [
            'p50' => $this->getPercentile($values, 50),
            'p90' => $this->getPercentile($values, 90),
            'p95' => $this->getPercentile($values, 95),
            'p99' => $this->getPercentile($values, 99),
        ];
    }

    /**
     * Get specific percentile value
     */
    protected function getPercentile(array $sortedValues, int $percentile): float
    {
        $count = count($sortedValues);
        $index = ($percentile / 100) * ($count - 1);
        
        if ($index == floor($index)) {
            return $sortedValues[(int)$index];
        } else {
            $lower = $sortedValues[floor($index)];
            $upper = $sortedValues[ceil($index)];
            return $lower + ($upper - $lower) * ($index - floor($index));
        }
    }

    /**
     * Calculate peak throughput
     */
    protected function calculatePeakThroughput(array $performanceData): array
    {
        $throughputHistory = $performanceData['throughput_history'] ?? [];
        
        if (empty($throughputHistory)) {
            return ['value' => 0, 'timestamp' => null];
        }
        
        $peak = max($throughputHistory);
        $peakIndex = array_search($peak, $throughputHistory);
        
        return [
            'value' => $peak,
            'timestamp' => $peakIndex !== false ? now()->subMinutes(count($throughputHistory) - $peakIndex)->toISOString() : null,
        ];
    }

    /**
     * Calculate performance score
     */
    protected function calculatePerformanceScore(array $performanceData): float
    {
        $responseTimeScore = $this->scoreResponseTime($performanceData['response_time']['current']);
        $throughputScore = $this->scoreThroughput($performanceData['throughput']['current']);
        $resourceScore = $this->scoreResourceUtilization($performanceData['resource_utilization']);
        
        // Weighted average
        $score = ($responseTimeScore * 0.4) + ($throughputScore * 0.3) + ($resourceScore * 0.3);
        
        return round($score, 1);
    }

    /**
     * Process time distribution
     */
    protected function processTimeDistribution(array $usageData): array
    {
        return [
            'peak_hours' => $this->identifyPeakHours($usageData['hourly_distribution'] ?? []),
            'weekly_pattern' => $this->analyzeWeeklyPattern($usageData['daily_distribution'] ?? []),
            'seasonal_trends' => $this->analyzeSeasonalTrends($usageData),
        ];
    }

    /**
     * Analyze user behavior
     */
    protected function analyzeUserBehavior(array $usageData): array
    {
        return [
            'session_duration' => $usageData['avg_session_duration'] ?? 0,
            'requests_per_session' => $usageData['avg_requests_per_session'] ?? 0,
            'user_retention' => $this->calculateUserRetention($usageData),
            'api_adoption_rate' => $this->calculateApiAdoptionRate($usageData),
        ];
    }

    // Placeholder methods for complex calculations
    protected function identifyUsagePatterns(array $processed): array { return []; }
    protected function rankErrors(array $errors): array { return $errors; }
    protected function calculateErrorImpact(array $errorData): array { return []; }
    protected function calculateThreatLevel(array $securityData): string { return 'low'; }
    protected function identifySuspiciousActivities(array $securityData): array { return []; }
    protected function calculateSecurityScore(array $securityData): float { return 90.0; }
    protected function calculateEfficiencyScore(array $processedData): float { return 85.0; }
    protected function calculateUserSatisfactionIndex(array $processedData): float { return 4.2; }
    protected function calculateCostEfficiency(array $processedData): float { return 78.5; }
    protected function calculateBusinessImpactScore(array $processedData): float { return 82.3; }
    protected function checkPerformanceAlerts(array $performanceData): array { return []; }
    protected function checkErrorAlerts(array $errorData): array { return []; }
    protected function checkSecurityAlerts(array $securityData): array { return []; }
    protected function scoreResponseTime(float $responseTime): float { return max(0, 100 - ($responseTime * 10)); }
    protected function scoreThroughput(float $throughput): float { return min(100, $throughput * 2); }
    protected function scoreResourceUtilization(array $resources): float { return 85.0; }
    protected function identifyPeakHours(array $hourlyData): array { return []; }
    protected function analyzeWeeklyPattern(array $dailyData): array { return []; }
    protected function analyzeSeasonalTrends(array $usageData): array { return []; }
    protected function calculateUserRetention(array $usageData): float { return 85.0; }
    protected function calculateApiAdoptionRate(array $usageData): float { return 72.0; }
}
