<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use App\Domains\Shared\Services\TwoFactorAuthService;
use Inertia\Inertia;

class AuthController extends Controller
{
    protected TwoFactorAuthService $twoFactorService;

    public function __construct(TwoFactorAuthService $twoFactorService)
    {
        $this->twoFactorService = $twoFactorService;
    }

    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * Show the registration form
     */
    public function showRegistrationForm(Request $request)
    {
        $plan = $request->get('plan', 'basic');
        return view('auth.register', compact('plan'));
    }

    /**
     * تسجيل الدخول
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        if (!Auth::attempt($credentials, $request->boolean('remember'))) {
            throw ValidationException::withMessages([
                'email' => ['بيانات الاعتماد المقدمة غير صحيحة.'],
            ]);
        }

        $user = Auth::user();

        // التحقق من حالة المستخدم
        if (!$user->isActive()) {
            Auth::logout();
            throw ValidationException::withMessages([
                'email' => ['تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة.'],
            ]);
        }

        // التحقق من المصادقة الثنائية
        if ($user->hasTwoFactorEnabled()) {
            Auth::logout();
            session(['2fa_user_id' => $user->id]);

            return redirect()->route('2fa.verify');
        }

        $request->session()->regenerate();

        return redirect()->intended(route('dashboard'));
    }

    /**
     * عرض صفحة المصادقة الثنائية
     */
    public function showTwoFactorVerification()
    {
        if (!session('2fa_user_id')) {
            return redirect()->route('login');
        }

        return Inertia::render('Auth/TwoFactorVerification');
    }

    /**
     * التحقق من المصادقة الثنائية
     */
    public function verifyTwoFactor(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $userId = session('2fa_user_id');
        if (!$userId) {
            return redirect()->route('login');
        }

        $user = User::find($userId);
        if (!$user) {
            return redirect()->route('login');
        }

        $isValid = $this->twoFactorService->verify($user, $request->code) ||
                   $this->twoFactorService->verifyRecoveryCode($user, $request->code);

        if (!$isValid) {
            throw ValidationException::withMessages([
                'code' => ['الرمز المدخل غير صحيح.'],
            ]);
        }

        session()->forget('2fa_user_id');
        Auth::login($user, true);
        $request->session()->regenerate();

        return redirect()->intended(route('dashboard'));
    }

    /**
     * تسجيل الخروج
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }

    /**
     * Create admin user if not exists
     */
    public function createAdminUser()
    {
        $adminEmail = '<EMAIL>';

        if (!User::where('email', $adminEmail)->exists()) {
            User::create([
                'name' => 'Said Admin',
                'email' => $adminEmail,
                'password' => Hash::make('Colorado2020@'),
                'email_verified_at' => now(),
            ]);

            return response()->json(['message' => 'Admin user created successfully']);
        }

        return response()->json(['message' => 'Admin user already exists']);
    }
}
