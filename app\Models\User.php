<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
// use App\Domains\Shared\Traits\HasUuid;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasApiTokens, HasRoles, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'phone',
        'avatar',
        'is_active',
        'last_login_at',
        'timezone',
        'locale',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'metadata',
        // New registration fields
        'account_type',
        'country',
        'city',
        'currency',
        'company_name',
        'commercial_register',
        'tax_id',
        'website',
        'employees_count',
        'industry',
        'is_admin',
        'subscription_plan',
        'subscription_expires_at',
        'preferences',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
            'two_factor_enabled' => 'boolean',
            'two_factor_recovery_codes' => 'array',
            'metadata' => 'array',
            // New fields casts
            'is_admin' => 'boolean',
            'subscription_expires_at' => 'datetime',
            'preferences' => 'array',
        ];
    }

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * التحقق من كون المستخدم نشط
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * التحقق من تفعيل المصادقة الثنائية
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->two_factor_enabled;
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * الحصول على الصورة الرمزية
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * نطاق للمستخدمين النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق للمستخدمين المحققين
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * نطاق حسب الدور
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->role($role);
    }
}
