<?php

namespace App\Domains\CRM\Repositories;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Contracts\CustomerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

/**
 * مستودع العملاء
 * Customer Repository
 */
class CustomerRepository implements CustomerRepositoryInterface
{
    /**
     * نموذج العميل
     */
    protected Customer $model;

    /**
     * Constructor
     */
    public function __construct(Customer $model)
    {
        $this->model = $model;
    }

    /**
     * الحصول على جميع العملاء
     */
    public function all(): Collection
    {
        return Cache::remember('customers.all', 3600, function () {
            return $this->model->with(['source', 'assignedTo', 'industry'])->get();
        });
    }

    /**
     * البحث عن عميل بالمعرف
     */
    public function find(int $id): ?Customer
    {
        return Cache::remember("customer.{$id}", 1800, function () use ($id) {
            return $this->model->with([
                'source',
                'assignedTo',
                'industry',
                'contacts',
                'interactions',
                'opportunities',
                'invoices',
                'projects',
                'supportTickets',
                'satisfactionSurveys',
                'segments'
            ])->find($id);
        });
    }

    /**
     * إنشاء عميل جديد
     */
    public function create(array $data): Customer
    {
        $customer = $this->model->create($data);
        
        // مسح الكاش
        $this->clearCache();
        
        return $customer->load(['source', 'assignedTo', 'industry']);
    }

    /**
     * تحديث عميل
     */
    public function update(Customer $customer, array $data): bool
    {
        $updated = $customer->update($data);
        
        if ($updated) {
            // مسح الكاش
            $this->clearCache();
            Cache::forget("customer.{$customer->id}");
        }
        
        return $updated;
    }

    /**
     * حذف عميل
     */
    public function delete(Customer $customer): bool
    {
        $deleted = $customer->delete();
        
        if ($deleted) {
            // مسح الكاش
            $this->clearCache();
            Cache::forget("customer.{$customer->id}");
        }
        
        return $deleted;
    }

    /**
     * البحث في العملاء
     */
    public function search(string $query): Collection
    {
        return $this->model->where(function ($q) use ($query) {
            $q->where('first_name', 'LIKE', "%{$query}%")
              ->orWhere('last_name', 'LIKE', "%{$query}%")
              ->orWhere('company_name', 'LIKE', "%{$query}%")
              ->orWhere('email', 'LIKE', "%{$query}%")
              ->orWhere('phone', 'LIKE', "%{$query}%")
              ->orWhere('customer_number', 'LIKE', "%{$query}%");
        })
        ->with(['source', 'assignedTo', 'industry'])
        ->limit(50)
        ->get();
    }

    /**
     * الحصول على العملاء مع الترقيم
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->with(['source', 'assignedTo', 'industry'])
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);
    }

    /**
     * الحصول على العملاء النشطين
     */
    public function getActive(): Collection
    {
        return Cache::remember('customers.active', 1800, function () {
            return $this->model->active()
                              ->with(['source', 'assignedTo', 'industry'])
                              ->get();
        });
    }

    /**
     * الحصول على العملاء حسب المصدر
     */
    public function getBySource(int $sourceId): Collection
    {
        return Cache::remember("customers.source.{$sourceId}", 1800, function () use ($sourceId) {
            return $this->model->where('source_id', $sourceId)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->get();
        });
    }

    /**
     * الحصول على العملاء حسب الشريحة
     */
    public function getBySegment(int $segmentId): Collection
    {
        return Cache::remember("customers.segment.{$segmentId}", 1800, function () use ($segmentId) {
            return $this->model->whereHas('segments', function ($query) use ($segmentId) {
                $query->where('customer_segment_id', $segmentId);
            })
            ->with(['source', 'assignedTo', 'industry', 'segments'])
            ->get();
        });
    }

    /**
     * الحصول على العملاء عالي القيمة
     */
    public function getHighValue(float $threshold = 50000): Collection
    {
        return Cache::remember("customers.high_value.{$threshold}", 1800, function () use ($threshold) {
            return $this->model->where('total_spent', '>=', $threshold)
                              ->orWhere('lifetime_value', '>=', $threshold * 2)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->orderBy('total_spent', 'desc')
                              ->get();
        });
    }

    /**
     * الحصول على العملاء المعرضين للخطر
     */
    public function getAtRisk(): Collection
    {
        return Cache::remember('customers.at_risk', 900, function () {
            return $this->model->where('risk_score', '>=', 70)
                              ->orWhere('last_contact_at', '<', now()->subMonths(3))
                              ->with(['source', 'assignedTo', 'industry'])
                              ->orderBy('risk_score', 'desc')
                              ->get();
        });
    }

    /**
     * الحصول على العملاء الجدد
     */
    public function getNew(int $days = 30): Collection
    {
        return Cache::remember("customers.new.{$days}", 1800, function () use ($days) {
            return $this->model->where('created_at', '>=', now()->subDays($days))
                              ->with(['source', 'assignedTo', 'industry'])
                              ->orderBy('created_at', 'desc')
                              ->get();
        });
    }

    /**
     * الحصول على العملاء حسب المندوب
     */
    public function getByRep(int $repId): Collection
    {
        return Cache::remember("customers.rep.{$repId}", 1800, function () use ($repId) {
            return $this->model->where('assigned_to', $repId)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->get();
        });
    }

    /**
     * الحصول على العملاء حسب الصناعة
     */
    public function getByIndustry(int $industryId): Collection
    {
        return Cache::remember("customers.industry.{$industryId}", 1800, function () use ($industryId) {
            return $this->model->where('industry_id', $industryId)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->get();
        });
    }

    /**
     * الحصول على العملاء حسب الدولة
     */
    public function getByCountry(string $countryCode): Collection
    {
        return Cache::remember("customers.country.{$countryCode}", 1800, function () use ($countryCode) {
            return $this->model->where('country_code', $countryCode)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->get();
        });
    }

    /**
     * الحصول على العملاء المخلصين
     */
    public function getLoyal(): Collection
    {
        return Cache::remember('customers.loyal', 1800, function () {
            return $this->model->where('total_orders', '>=', 5)
                              ->where('created_at', '<=', now()->subMonths(6))
                              ->where('last_purchase_at', '>=', now()->subMonths(3))
                              ->with(['source', 'assignedTo', 'industry'])
                              ->orderBy('total_spent', 'desc')
                              ->get();
        });
    }

    /**
     * الحصول على العملاء الخاملين
     */
    public function getDormant(int $months = 6): Collection
    {
        return Cache::remember("customers.dormant.{$months}", 1800, function () use ($months) {
            return $this->model->where('last_contact_at', '<', now()->subMonths($months))
                              ->where('total_spent', '>', 0)
                              ->with(['source', 'assignedTo', 'industry'])
                              ->orderBy('last_contact_at', 'asc')
                              ->get();
        });
    }

    /**
     * الحصول على إحصائيات العملاء
     */
    public function getStats(): array
    {
        return Cache::remember('customers.stats', 3600, function () {
            return [
                'total' => $this->model->count(),
                'active' => $this->model->active()->count(),
                'new_this_month' => $this->model->where('created_at', '>=', now()->startOfMonth())->count(),
                'high_value' => $this->model->where('total_spent', '>=', 50000)->count(),
                'at_risk' => $this->model->where('risk_score', '>=', 70)->count(),
                'total_value' => $this->model->sum('total_spent'),
                'average_value' => $this->model->avg('total_spent'),
                'average_satisfaction' => $this->model->avg('satisfaction_score'),
            ];
        });
    }

    /**
     * مسح الكاش
     */
    protected function clearCache(): void
    {
        Cache::forget('customers.all');
        Cache::forget('customers.active');
        Cache::forget('customers.stats');
        Cache::forget('customers.loyal');
        Cache::forget('customers.at_risk');
        
        // مسح كاش الشرائح والمصادر
        for ($i = 1; $i <= 100; $i++) {
            Cache::forget("customers.source.{$i}");
            Cache::forget("customers.segment.{$i}");
            Cache::forget("customers.rep.{$i}");
            Cache::forget("customers.industry.{$i}");
        }
    }
}
