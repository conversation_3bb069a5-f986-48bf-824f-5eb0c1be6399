<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج سجل SLA للتذكرة - Ticket SLA Log
 */
class TicketSlaLog extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'ticket_id',
        'event_type',
        'event_data',
        'created_at',
    ];

    protected $casts = [
        'event_data' => 'array',
        'created_at' => 'datetime',
    ];

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    public function getEventDescriptionAttribute(): string
    {
        return match ($this->event_type) {
            'sla_set' => 'تم تحديد SLA',
            'sla_recalculated' => 'تم إعادة حساب SLA',
            'sla_warning' => 'تحذير SLA',
            'sla_breach' => 'انتهاك SLA',
            'sla_paused' => 'تم إيقاف SLA مؤقتاً',
            'sla_resumed' => 'تم استئناف SLA',
            default => 'حدث SLA غير معروف',
        };
    }
}
