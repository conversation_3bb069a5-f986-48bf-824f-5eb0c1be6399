<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Domains\Projects\Models\Project;

/**
 * حدث اكتمال التكامل مع المحاسبة
 */
class ProjectAccountingIntegrationCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Project $project;
    public array $integrationResults;

    public function __construct(Project $project, array $integrationResults)
    {
        $this->project = $project;
        $this->integrationResults = $integrationResults;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('project.' . $this->project->id),
        ];
    }
}
