<?php

namespace App\Domains\Projects\Listeners;

use App\Domains\Projects\Events\ProjectCreated;
use App\Domains\Projects\Notifications\ProjectCreatedNotification;
use App\Domains\Projects\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Notification;

/**
 * مستمع إرسال إشعار إنشاء المشروع
 */
class SendProjectCreatedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * إنشاء مستمع الحدث
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * معالجة الحدث
     */
    public function handle(ProjectCreated $event): void
    {
        $project = $event->project;

        // إرسال إشعار لمدير المشروع
        if ($project->projectManager) {
            $project->projectManager->notify(
                new ProjectCreatedNotification($project, 'project_manager')
            );
        }

        // إرسال إشعار للعميل إذا كان موجوداً
        if ($project->client) {
            $project->client->notify(
                new ProjectCreatedNotification($project, 'client')
            );
        }

        // إرسال إشعار لأعضاء الفريق
        foreach ($project->team as $teamMember) {
            $teamMember->user->notify(
                new ProjectCreatedNotification($project, 'team_member')
            );
        }

        // إرسال إشعار للمدراء والمشرفين
        $this->notifyManagers($project);

        // إرسال إشعارات خارجية (Slack, Email, etc.)
        $this->sendExternalNotifications($project);

        // تسجيل النشاط
        $this->logActivity($project);
    }

    /**
     * إرسال إشعار للمدراء
     */
    protected function notifyManagers($project): void
    {
        $managers = \App\Models\User::role(['admin', 'project-supervisor'])
            ->where('id', '!=', $project->project_manager_id)
            ->get();

        foreach ($managers as $manager) {
            $manager->notify(
                new ProjectCreatedNotification($project, 'manager')
            );
        }
    }

    /**
     * إرسال إشعارات خارجية
     */
    protected function sendExternalNotifications($project): void
    {
        // إرسال إشعار Slack إذا كان مفعلاً
        if ($project->notification_settings['slack_notifications'] ?? false) {
            $this->notificationService->sendSlackNotification([
                'channel' => '#projects',
                'message' => "🚀 تم إنشاء مشروع جديد: {$project->name}",
                'project' => $project,
                'type' => 'project_created',
            ]);
        }

        // إرسال بريد إلكتروني للمهتمين
        if ($project->notification_settings['email_notifications'] ?? true) {
            $this->notificationService->sendEmailNotification([
                'template' => 'project-created',
                'recipients' => $this->getEmailRecipients($project),
                'data' => [
                    'project' => $project,
                    'project_manager' => $project->projectManager,
                    'client' => $project->client,
                ],
            ]);
        }

        // إرسال إشعار Teams إذا كان مفعلاً
        if ($project->notification_settings['teams_notifications'] ?? false) {
            $this->notificationService->sendTeamsNotification([
                'webhook_url' => config('services.teams.webhook_url'),
                'title' => 'مشروع جديد',
                'message' => "تم إنشاء مشروع جديد: {$project->name}",
                'project' => $project,
            ]);
        }
    }

    /**
     * الحصول على مستقبلي البريد الإلكتروني
     */
    protected function getEmailRecipients($project): array
    {
        $recipients = [];

        // إضافة مدير المشروع
        if ($project->projectManager && $project->projectManager->email) {
            $recipients[] = $project->projectManager->email;
        }

        // إضافة العميل
        if ($project->client && $project->client->email) {
            $recipients[] = $project->client->email;
        }

        // إضافة أعضاء الفريق
        foreach ($project->team as $teamMember) {
            if ($teamMember->user->email) {
                $recipients[] = $teamMember->user->email;
            }
        }

        // إضافة المدراء المهتمين
        $interestedManagers = \App\Models\User::role(['admin', 'project-supervisor'])
            ->whereHas('notificationPreferences', function ($query) {
                $query->where('type', 'project_created')
                      ->where('email_enabled', true);
            })
            ->pluck('email')
            ->toArray();

        $recipients = array_merge($recipients, $interestedManagers);

        return array_unique($recipients);
    }

    /**
     * تسجيل النشاط
     */
    protected function logActivity($project): void
    {
        activity()
            ->performedOn($project)
            ->causedBy(auth()->user())
            ->withProperties([
                'project_name' => $project->name,
                'project_code' => $project->code,
                'project_manager' => $project->projectManager?->name,
                'client' => $project->client?->name,
                'budget' => $project->budget,
                'start_date' => $project->start_date,
                'end_date' => $project->end_date,
            ])
            ->log('تم إنشاء مشروع جديد');
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(ProjectCreated $event, $exception): void
    {
        \Log::error('فشل في إرسال إشعار إنشاء المشروع', [
            'project_id' => $event->project->id,
            'project_name' => $event->project->name,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // إرسال إشعار للمدراء بالفشل
        $admins = \App\Models\User::role('admin')->get();
        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\SystemErrorNotification([
                'type' => 'notification_failure',
                'message' => 'فشل في إرسال إشعار إنشاء المشروع',
                'project_id' => $event->project->id,
                'error' => $exception->getMessage(),
            ]));
        }
    }
}
