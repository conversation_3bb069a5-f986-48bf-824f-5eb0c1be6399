<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج دليل الحسابات
 * يدعم معايير محاسبية متعددة مع إمكانية التحويل التلقائي
 */
class ChartOfAccounts extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'standard',
        'version',
        'country_code',
        'currency',
        'is_active',
        'is_default',
        'description',
        'effective_date',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'effective_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * المعايير المحاسبية المدعومة
     */
    public const STANDARDS = [
        'PCGM' => 'الدليل المحاسبي العام المغربي',
        'IFRS' => 'المعايير الدولية للتقارير المالية',
        'GAAP_US' => 'مبادئ المحاسبة المقبولة عموماً الأمريكية',
        'GAAP_UK' => 'مبادئ المحاسبة المقبولة عموماً البريطانية',
        'CUSTOM' => 'دليل مخصص',
    ];

    /**
     * الحسابات المرتبطة بهذا الدليل
     */
    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class, 'chart_id');
    }

    /**
     * خرائط الحسابات للتحويل بين المعايير
     */
    public function accountMappings(): HasMany
    {
        return $this->hasMany(AccountMapping::class, 'source_chart_id');
    }

    /**
     * الحصول على الدليل الافتراضي
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * الحصول على دليل حسب المعيار
     */
    public static function getByStandard(string $standard): ?self
    {
        return static::where('standard', $standard)->where('is_active', true)->first();
    }

    /**
     * تعيين كدليل افتراضي
     */
    public function setAsDefault(): bool
    {
        // إلغاء تعيين الأدلة الأخرى كافتراضية
        static::where('is_default', true)->update(['is_default' => false]);

        $this->is_default = true;
        return $this->save();
    }

    /**
     * استيراد حسابات من معيار آخر
     */
    public function importAccountsFromStandard(string $sourceStandard): int
    {
        $sourceChart = static::getByStandard($sourceStandard);
        if (!$sourceChart) {
            throw new \Exception("لم يتم العثور على دليل للمعيار: {$sourceStandard}");
        }

        $importedCount = 0;
        foreach ($sourceChart->accounts as $sourceAccount) {
            // التحقق من عدم وجود الحساب مسبقاً
            $existingAccount = $this->accounts()
                ->where('code', $sourceAccount->code)
                ->first();

            if (!$existingAccount) {
                $newAccount = $sourceAccount->replicate();
                $newAccount->chart_id = $this->id;
                $newAccount->save();
                $importedCount++;
            }
        }

        return $importedCount;
    }

    /**
     * إنشاء خريطة تحويل إلى معيار آخر
     */
    public function createMappingTo(ChartOfAccounts $targetChart): void
    {
        foreach ($this->accounts as $sourceAccount) {
            // البحث عن حساب مطابق في الدليل المستهدف
            $targetAccount = $targetChart->accounts()
                ->where('code', $sourceAccount->code)
                ->orWhere('name', $sourceAccount->name)
                ->first();

            if ($targetAccount) {
                AccountMapping::firstOrCreate([
                    'source_chart_id' => $this->id,
                    'target_chart_id' => $targetChart->id,
                    'source_account_id' => $sourceAccount->id,
                    'target_account_id' => $targetAccount->id,
                ]);
            }
        }
    }

    /**
     * التحقق من صحة الدليل
     */
    public function validate(): array
    {
        $errors = [];

        // التحقق من وجود الحسابات الأساسية
        $requiredAccountTypes = ['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE'];
        foreach ($requiredAccountTypes as $type) {
            if (!$this->accounts()->where('account_type', $type)->exists()) {
                $errors[] = "لا توجد حسابات من نوع: {$type}";
            }
        }

        // التحقق من توازن الحسابات
        $totalDebits = $this->accounts()->sum('debit_balance');
        $totalCredits = $this->accounts()->sum('credit_balance');

        if (abs($totalDebits - $totalCredits) > 0.01) {
            $errors[] = "عدم توازن في الحسابات: المدين = {$totalDebits}, الدائن = {$totalCredits}";
        }

        return $errors;
    }

    /**
     * نطاق للأدلة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق حسب المعيار
     */
    public function scopeByStandard($query, string $standard)
    {
        return $query->where('standard', $standard);
    }

    /**
     * نطاق حسب الدولة
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->where('country_code', $countryCode);
    }
}
