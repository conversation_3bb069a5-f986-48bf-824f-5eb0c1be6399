<?php

namespace App\Domains\ECommerce\Policies;

use App\Models\User;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة صلاحيات تكاملات التجارة الإلكترونية
 */
class ECommerceIntegrationPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي تكاملات
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('view_ecommerce_integrations') ||
               $user->hasRole(['admin', 'store_manager']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التكامل
     */
    public function view(User $user, ECommerceIntegration $integration): bool
    {
        // المدير يمكنه عرض جميع التكاملات
        if ($user->hasRole('admin')) {
            return true;
        }

        // مدير المتجر يمكنه عرض تكاملات متجره فقط
        if ($user->hasRole('store_manager')) {
            return $user->stores()->where('stores.id', $integration->store_id)->exists();
        }

        // المستخدم العادي يحتاج صلاحية محددة
        return $user->hasPermissionTo('view_ecommerce_integrations') &&
               $this->userCanAccessStore($user, $integration->store_id);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء تكاملات
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('create_ecommerce_integrations') ||
               $user->hasRole(['admin', 'store_manager']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث التكامل
     */
    public function update(User $user, ECommerceIntegration $integration): bool
    {
        // المدير يمكنه تحديث جميع التكاملات
        if ($user->hasRole('admin')) {
            return true;
        }

        // مدير المتجر يمكنه تحديث تكاملات متجره فقط
        if ($user->hasRole('store_manager')) {
            return $user->stores()->where('stores.id', $integration->store_id)->exists();
        }

        // المستخدم العادي يحتاج صلاحية محددة
        return $user->hasPermissionTo('update_ecommerce_integrations') &&
               $this->userCanAccessStore($user, $integration->store_id);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف التكامل
     */
    public function delete(User $user, ECommerceIntegration $integration): bool
    {
        // المدير يمكنه حذف جميع التكاملات
        if ($user->hasRole('admin')) {
            return true;
        }

        // مدير المتجر يمكنه حذف تكاملات متجره فقط
        if ($user->hasRole('store_manager')) {
            return $user->stores()->where('stores.id', $integration->store_id)->exists();
        }

        // المستخدم العادي يحتاج صلاحية محددة
        return $user->hasPermissionTo('delete_ecommerce_integrations') &&
               $this->userCanAccessStore($user, $integration->store_id);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استعادة التكامل
     */
    public function restore(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasRole('admin') ||
               ($user->hasPermissionTo('restore_ecommerce_integrations') &&
                $this->userCanAccessStore($user, $integration->store_id));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف التكامل نهائياً
     */
    public function forceDelete(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه بدء المزامنة
     */
    public function sync(User $user, ECommerceIntegration $integration): bool
    {
        // المدير يمكنه مزامنة جميع التكاملات
        if ($user->hasRole('admin')) {
            return true;
        }

        // مدير المتجر يمكنه مزامنة تكاملات متجره فقط
        if ($user->hasRole('store_manager')) {
            return $user->stores()->where('stores.id', $integration->store_id)->exists();
        }

        // المستخدم العادي يحتاج صلاحية محددة
        return $user->hasPermissionTo('sync_ecommerce_integrations') &&
               $this->userCanAccessStore($user, $integration->store_id);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إيقاف المزامنة
     */
    public function stopSync(User $user, ECommerceIntegration $integration): bool
    {
        return $this->sync($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض سجلات المزامنة
     */
    public function viewSyncLogs(User $user, ECommerceIntegration $integration): bool
    {
        return $this->view($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه اختبار الاتصال
     */
    public function testConnection(User $user, ECommerceIntegration $integration): bool
    {
        return $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تفعيل التكامل
     */
    public function activate(User $user, ECommerceIntegration $integration): bool
    {
        return $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إلغاء تفعيل التكامل
     */
    public function deactivate(User $user, ECommerceIntegration $integration): bool
    {
        return $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض إحصائيات التكامل
     */
    public function viewStatistics(User $user, ECommerceIntegration $integration): bool
    {
        return $this->view($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير البيانات
     */
    public function export(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasPermissionTo('export_ecommerce_data') &&
               $this->view($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد البيانات
     */
    public function import(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasPermissionTo('import_ecommerce_data') &&
               $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة Webhooks
     */
    public function manageWebhooks(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasPermissionTo('manage_ecommerce_webhooks') &&
               $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات الحساسة
     */
    public function viewSensitiveData(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasRole('admin') ||
               ($user->hasPermissionTo('view_sensitive_ecommerce_data') &&
                $this->view($user, $integration));
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه الوصول إلى المتجر
     */
    protected function userCanAccessStore(User $user, int $storeId): bool
    {
        // التحقق من أن المستخدم له صلاحية الوصول للمتجر
        return $user->stores()->where('stores.id', $storeId)->exists() ||
               $user->hasPermissionTo('access_all_stores');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة إعدادات المزامنة
     */
    public function manageSyncSettings(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasPermissionTo('manage_sync_settings') &&
               $this->update($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تقارير التكامل
     */
    public function viewReports(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasPermissionTo('view_ecommerce_reports') &&
               $this->view($user, $integration);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة إعدادات الأمان
     */
    public function manageSecuritySettings(User $user, ECommerceIntegration $integration): bool
    {
        return $user->hasRole('admin') ||
               ($user->hasPermissionTo('manage_ecommerce_security') &&
                $this->update($user, $integration));
    }
}
