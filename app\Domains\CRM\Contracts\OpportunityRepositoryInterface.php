<?php

namespace App\Domains\CRM\Contracts;

use App\Domains\CRM\Models\Opportunity;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

/**
 * واجهة مستودع الفرص التجارية
 * Opportunity Repository Interface
 */
interface OpportunityRepositoryInterface
{
    /**
     * الحصول على جميع الفرص
     */
    public function all(): Collection;

    /**
     * البحث عن فرصة بالمعرف
     */
    public function find(int $id): ?Opportunity;

    /**
     * إنشاء فرصة جديدة
     */
    public function create(array $data): Opportunity;

    /**
     * تحديث فرصة
     */
    public function update(Opportunity $opportunity, array $data): bool;

    /**
     * حذف فرصة
     */
    public function delete(Opportunity $opportunity): bool;

    /**
     * الحصول على الفرص حسب المرحلة
     */
    public function getByStage(string $stage): Collection;

    /**
     * الحصول على الفرص المفتوحة
     */
    public function getOpen(): Collection;

    /**
     * الحصول على الفرص المكسوبة
     */
    public function getWon(Carbon $dateFrom = null, Carbon $dateTo = null): Collection;

    /**
     * الحصول على الفرص المفقودة
     */
    public function getLost(Carbon $dateFrom = null, Carbon $dateTo = null): Collection;

    /**
     * الحصول على الفرص المتأخرة
     */
    public function getOverdue(): Collection;

    /**
     * الحصول على الفرص حسب المندوب
     */
    public function getByRep(int $repId): Collection;

    /**
     * الحصول على قيمة الأنبوب
     */
    public function getPipelineValue(): float;

    /**
     * الحصول على القيمة المرجحة للأنبوب
     */
    public function getWeightedPipelineValue(): float;

    /**
     * الحصول على معدل التحويل
     */
    public function getConversionRate(Carbon $dateFrom = null, Carbon $dateTo = null): float;

    /**
     * الحصول على متوسط دورة المبيعات
     */
    public function getAverageSalesCycle(): float;
}
