<?php

namespace App\Domains\Taxation\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج النظام الضريبي
 * يدعم الأنظمة الضريبية المختلفة (المغرب، دول أخرى)
 */
class TaxSystem extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'country_code',
        'currency',
        'is_active',
        'is_default',
        'tax_year_start_month',
        'tax_year_start_day',
        'vat_registration_threshold',
        'corporate_tax_rate',
        'personal_tax_brackets',
        'vat_rates',
        'withholding_tax_rates',
        'social_security_rates',
        'api_endpoints',
        'digital_signature_required',
        'e_filing_mandatory',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'vat_registration_threshold' => 'decimal:2',
        'corporate_tax_rate' => 'decimal:2',
        'personal_tax_brackets' => 'array',
        'vat_rates' => 'array',
        'withholding_tax_rates' => 'array',
        'social_security_rates' => 'array',
        'api_endpoints' => 'array',
        'digital_signature_required' => 'boolean',
        'e_filing_mandatory' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * الأنظمة الضريبية المدعومة
     */
    public const SUPPORTED_SYSTEMS = [
        'MA' => 'المغرب',
        'FR' => 'فرنسا',
        'US' => 'الولايات المتحدة',
        'UK' => 'المملكة المتحدة',
        'DE' => 'ألمانيا',
        'AE' => 'الإمارات العربية المتحدة',
        'SA' => 'المملكة العربية السعودية',
    ];

    /**
     * أنواع الضرائب
     */
    public const TAX_TYPES = [
        'VAT' => 'ضريبة القيمة المضافة',
        'CORPORATE' => 'ضريبة الشركات',
        'PERSONAL' => 'ضريبة الدخل الشخصي',
        'WITHHOLDING' => 'ضريبة الاستقطاع',
        'SOCIAL_SECURITY' => 'الضمان الاجتماعي',
        'CUSTOMS' => 'الرسوم الجمركية',
        'EXCISE' => 'ضريبة الاستهلاك',
        'PROPERTY' => 'ضريبة العقارات',
    ];

    /**
     * قواعد الضرائب
     */
    public function taxRules(): HasMany
    {
        return $this->hasMany(TaxRule::class);
    }

    /**
     * الإقرارات الضريبية
     */
    public function taxReturns(): HasMany
    {
        return $this->hasMany(TaxReturn::class);
    }

    /**
     * الحصول على النظام الافتراضي
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * الحصول على نظام حسب الدولة
     */
    public static function getByCountry(string $countryCode): ?self
    {
        return static::where('country_code', $countryCode)->where('is_active', true)->first();
    }

    /**
     * تعيين كنظام افتراضي
     */
    public function setAsDefault(): bool
    {
        static::where('is_default', true)->update(['is_default' => false]);
        $this->is_default = true;
        return $this->save();
    }

    /**
     * حساب ضريبة القيمة المضافة
     */
    public function calculateVAT(float $amount, string $vatCategory = 'standard'): array
    {
        $vatRates = $this->vat_rates ?? [];
        $rate = $vatRates[$vatCategory] ?? $vatRates['standard'] ?? 20;

        $vatAmount = $amount * ($rate / 100);
        $totalAmount = $amount + $vatAmount;

        return [
            'base_amount' => $amount,
            'vat_rate' => $rate,
            'vat_amount' => round($vatAmount, 2),
            'total_amount' => round($totalAmount, 2),
            'category' => $vatCategory,
        ];
    }

    /**
     * حساب ضريبة الشركات
     */
    public function calculateCorporateTax(float $taxableIncome): array
    {
        $rate = $this->corporate_tax_rate ?? 31; // المعدل الافتراضي للمغرب
        $taxAmount = $taxableIncome * ($rate / 100);

        return [
            'taxable_income' => $taxableIncome,
            'tax_rate' => $rate,
            'tax_amount' => round($taxAmount, 2),
            'net_income' => round($taxableIncome - $taxAmount, 2),
        ];
    }

    /**
     * حساب ضريبة الدخل الشخصي (بالشرائح)
     */
    public function calculatePersonalTax(float $income): array
    {
        $brackets = $this->personal_tax_brackets ?? $this->getDefaultPersonalTaxBrackets();
        $totalTax = 0;
        $taxBreakdown = [];

        $remainingIncome = $income;

        foreach ($brackets as $bracket) {
            $min = $bracket['min'] ?? 0;
            $max = $bracket['max'] ?? PHP_FLOAT_MAX;
            $rate = $bracket['rate'] ?? 0;

            if ($remainingIncome <= 0) break;

            $taxableInBracket = min($remainingIncome, $max - $min);
            $taxInBracket = $taxableInBracket * ($rate / 100);

            if ($taxableInBracket > 0) {
                $taxBreakdown[] = [
                    'bracket' => "{$min} - " . ($max == PHP_FLOAT_MAX ? '∞' : $max),
                    'rate' => $rate,
                    'taxable_amount' => $taxableInBracket,
                    'tax_amount' => round($taxInBracket, 2),
                ];

                $totalTax += $taxInBracket;
                $remainingIncome -= $taxableInBracket;
            }
        }

        return [
            'gross_income' => $income,
            'total_tax' => round($totalTax, 2),
            'net_income' => round($income - $totalTax, 2),
            'effective_rate' => $income > 0 ? round(($totalTax / $income) * 100, 2) : 0,
            'breakdown' => $taxBreakdown,
        ];
    }

    /**
     * الشرائح الضريبية الافتراضية للمغرب
     */
    protected function getDefaultPersonalTaxBrackets(): array
    {
        return [
            ['min' => 0, 'max' => 30000, 'rate' => 0],
            ['min' => 30000, 'max' => 50000, 'rate' => 10],
            ['min' => 50000, 'max' => 60000, 'rate' => 20],
            ['min' => 60000, 'max' => 80000, 'rate' => 30],
            ['min' => 80000, 'max' => 180000, 'rate' => 34],
            ['min' => 180000, 'max' => PHP_FLOAT_MAX, 'rate' => 38],
        ];
    }

    /**
     * حساب الضمان الاجتماعي
     */
    public function calculateSocialSecurity(float $salary): array
    {
        $rates = $this->social_security_rates ?? $this->getDefaultSocialSecurityRates();

        $employeeContribution = 0;
        $employerContribution = 0;
        $breakdown = [];

        foreach ($rates as $type => $config) {
            $maxSalary = $config['max_salary'] ?? PHP_FLOAT_MAX;
            $applicableSalary = min($salary, $maxSalary);

            $employeeRate = $config['employee_rate'] ?? 0;
            $employerRate = $config['employer_rate'] ?? 0;

            $employeeAmount = $applicableSalary * ($employeeRate / 100);
            $employerAmount = $applicableSalary * ($employerRate / 100);

            $breakdown[$type] = [
                'applicable_salary' => $applicableSalary,
                'employee_rate' => $employeeRate,
                'employer_rate' => $employerRate,
                'employee_amount' => round($employeeAmount, 2),
                'employer_amount' => round($employerAmount, 2),
            ];

            $employeeContribution += $employeeAmount;
            $employerContribution += $employerAmount;
        }

        return [
            'gross_salary' => $salary,
            'employee_contribution' => round($employeeContribution, 2),
            'employer_contribution' => round($employerContribution, 2),
            'total_contribution' => round($employeeContribution + $employerContribution, 2),
            'net_salary' => round($salary - $employeeContribution, 2),
            'breakdown' => $breakdown,
        ];
    }

    /**
     * معدلات الضمان الاجتماعي الافتراضية للمغرب
     */
    protected function getDefaultSocialSecurityRates(): array
    {
        return [
            'cnss' => [
                'employee_rate' => 4.48,
                'employer_rate' => 16.46,
                'max_salary' => 6000, // الحد الأقصى للراتب الخاضع للضمان
            ],
            'amo' => [
                'employee_rate' => 2.26,
                'employer_rate' => 3.39,
                'max_salary' => null,
            ],
        ];
    }

    /**
     * التحقق من وجوب التسجيل في ضريبة القيمة المضافة
     */
    public function isVATRegistrationRequired(float $annualTurnover): bool
    {
        return $annualTurnover >= ($this->vat_registration_threshold ?? 1000000);
    }

    /**
     * الحصول على معدلات ضريبة القيمة المضافة
     */
    public function getVATRates(): array
    {
        return $this->vat_rates ?? [
            'standard' => 20,
            'reduced' => 14,
            'super_reduced' => 7,
            'zero' => 0,
            'exempt' => 0,
        ];
    }

    /**
     * الحصول على نقاط النهاية للواجهات البرمجية
     */
    public function getAPIEndpoints(): array
    {
        return $this->api_endpoints ?? [];
    }

    /**
     * التحقق من إلزامية الإيداع الإلكتروني
     */
    public function isEFilingMandatory(): bool
    {
        return $this->e_filing_mandatory ?? false;
    }

    /**
     * التحقق من إلزامية التوقيع الرقمي
     */
    public function isDigitalSignatureRequired(): bool
    {
        return $this->digital_signature_required ?? false;
    }

    /**
     * نطاق للأنظمة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق حسب الدولة
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->where('country_code', $countryCode);
    }
}
