<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج طلب الإجازة - نظام إدارة الإجازات المتقدم
 */
class LeaveRequest extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'start_date',
        'end_date',
        'total_days',
        'working_days',
        'status',
        'reason',
        'emergency_contact',
        'emergency_phone',
        'replacement_employee_id',
        'handover_notes',
        'medical_certificate',
        'supporting_documents',
        'requested_at',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'rejection_reason',
        'cancelled_at',
        'cancellation_reason',
        'return_date',
        'actual_return_date',
        'extension_requested',
        'extension_days',
        'extension_reason',
        'extension_approved_by',
        'extension_approved_at',
        'is_paid',
        'salary_deduction',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'return_date' => 'date',
        'actual_return_date' => 'date',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'extension_approved_at' => 'datetime',
        'is_paid' => 'boolean',
        'extension_requested' => 'boolean',
        'salary_deduction' => 'decimal:2',
        'supporting_documents' => 'array',
        'metadata' => 'array',
    ];

    /**
     * حالات طلب الإجازة
     */
    const STATUSES = [
        'PENDING' => 'في الانتظار',
        'APPROVED' => 'موافق عليه',
        'REJECTED' => 'مرفوض',
        'CANCELLED' => 'ملغي',
        'IN_PROGRESS' => 'جاري',
        'COMPLETED' => 'مكتمل',
        'EXTENDED' => 'ممدد',
        'OVERDUE' => 'متأخر في العودة',
    ];

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * نوع الإجازة
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * الموظف البديل
     */
    public function replacementEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'replacement_employee_id');
    }

    /**
     * الشخص الذي وافق
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * الشخص الذي رفض
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'rejected_by');
    }

    /**
     * الشخص الذي وافق على التمديد
     */
    public function extensionApprover(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'extension_approved_by');
    }

    /**
     * المستندات
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(\App\Domains\Shared\Models\Document::class, 'documentable');
    }

    /**
     * حساب عدد الأيام
     */
    public function calculateDays(): void
    {
        if (!$this->start_date || !$this->end_date) {
            return;
        }

        // حساب إجمالي الأيام
        $this->total_days = $this->start_date->diffInDays($this->end_date) + 1;

        // حساب أيام العمل (استثناء العطل الأسبوعية والرسمية)
        $this->working_days = $this->calculateWorkingDays();
    }

    /**
     * حساب أيام العمل
     */
    protected function calculateWorkingDays(): int
    {
        $workingDays = 0;
        $current = $this->start_date->copy();
        
        while ($current <= $this->end_date) {
            // تحقق من كونه يوم عمل (ليس عطلة أسبوعية)
            if (!$this->isWeekend($current)) {
                // تحقق من كونه ليس عطلة رسمية
                if (!$this->isPublicHoliday($current)) {
                    $workingDays++;
                }
            }
            $current->addDay();
        }

        return $workingDays;
    }

    /**
     * التحقق من كون اليوم عطلة أسبوعية
     */
    protected function isWeekend(\Carbon\Carbon $date): bool
    {
        // في المنطقة العربية، العطلة الأسبوعية عادة الجمعة والسبت
        return in_array($date->dayOfWeek, [5, 6]); // Friday = 5, Saturday = 6
    }

    /**
     * التحقق من كون اليوم عطلة رسمية
     */
    protected function isPublicHoliday(\Carbon\Carbon $date): bool
    {
        // يمكن تطوير هذا للتحقق من قاعدة بيانات العطل الرسمية
        $holidays = config('hr.public_holidays', []);
        return in_array($date->format('m-d'), $holidays);
    }

    /**
     * الموافقة على الطلب
     */
    public function approve(Employee $approver, string $notes = null): bool
    {
        if ($this->status !== 'PENDING') {
            return false;
        }

        // التحقق من رصيد الإجازات
        if (!$this->checkLeaveBalance()) {
            return false;
        }

        $this->update([
            'status' => 'APPROVED',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'notes' => $notes,
        ]);

        // خصم من رصيد الإجازات
        $this->deductFromLeaveBalance();

        // إرسال إشعار للموظف
        $this->employee->notify(new \App\Notifications\LeaveRequestApproved($this));

        return true;
    }

    /**
     * رفض الطلب
     */
    public function reject(Employee $rejector, string $reason): bool
    {
        if ($this->status !== 'PENDING') {
            return false;
        }

        $this->update([
            'status' => 'REJECTED',
            'rejected_by' => $rejector->id,
            'rejected_at' => now(),
            'rejection_reason' => $reason,
        ]);

        // إرسال إشعار للموظف
        $this->employee->notify(new \App\Notifications\LeaveRequestRejected($this));

        return true;
    }

    /**
     * إلغاء الطلب
     */
    public function cancel(string $reason = null): bool
    {
        if (!in_array($this->status, ['PENDING', 'APPROVED'])) {
            return false;
        }

        $oldStatus = $this->status;
        
        $this->update([
            'status' => 'CANCELLED',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);

        // إذا كان معتمد، أعد الرصيد
        if ($oldStatus === 'APPROVED') {
            $this->restoreLeaveBalance();
        }

        return true;
    }

    /**
     * طلب تمديد الإجازة
     */
    public function requestExtension(int $days, string $reason): bool
    {
        if ($this->status !== 'IN_PROGRESS') {
            return false;
        }

        $this->update([
            'extension_requested' => true,
            'extension_days' => $days,
            'extension_reason' => $reason,
        ]);

        // إرسال إشعار للمدير
        $this->employee->manager?->notify(new \App\Notifications\LeaveExtensionRequested($this));

        return true;
    }

    /**
     * الموافقة على التمديد
     */
    public function approveExtension(Employee $approver): bool
    {
        if (!$this->extension_requested) {
            return false;
        }

        $this->update([
            'status' => 'EXTENDED',
            'end_date' => $this->end_date->addDays($this->extension_days),
            'extension_approved_by' => $approver->id,
            'extension_approved_at' => now(),
        ]);

        // إعادة حساب الأيام
        $this->calculateDays();
        $this->save();

        // خصم أيام إضافية من الرصيد
        $this->deductFromLeaveBalance($this->extension_days);

        return true;
    }

    /**
     * بدء الإجازة
     */
    public function start(): bool
    {
        if ($this->status !== 'APPROVED' || $this->start_date > now()) {
            return false;
        }

        $this->update(['status' => 'IN_PROGRESS']);
        return true;
    }

    /**
     * إنهاء الإجازة
     */
    public function complete(\Carbon\Carbon $actualReturnDate = null): bool
    {
        if ($this->status !== 'IN_PROGRESS') {
            return false;
        }

        $this->update([
            'status' => 'COMPLETED',
            'actual_return_date' => $actualReturnDate ?? now(),
        ]);

        return true;
    }

    /**
     * التحقق من رصيد الإجازات
     */
    protected function checkLeaveBalance(): bool
    {
        $leaveService = app(\App\Domains\HR\Services\LeaveManagementService::class);
        $balance = $leaveService->getEmployeeLeaveBalance($this->employee, $this->leaveType->code);
        
        return $balance['remaining'] >= $this->working_days;
    }

    /**
     * خصم من رصيد الإجازات
     */
    protected function deductFromLeaveBalance(int $days = null): void
    {
        $leaveService = app(\App\Domains\HR\Services\LeaveManagementService::class);
        $leaveService->deductLeaveBalance(
            $this->employee,
            $this->leaveType->code,
            $days ?? $this->working_days
        );
    }

    /**
     * استرداد رصيد الإجازات
     */
    protected function restoreLeaveBalance(): void
    {
        $leaveService = app(\App\Domains\HR\Services\LeaveManagementService::class);
        $leaveService->restoreLeaveBalance(
            $this->employee,
            $this->leaveType->code,
            $this->working_days
        );
    }

    /**
     * التحقق من التداخل مع إجازات أخرى
     */
    public function hasConflict(): bool
    {
        return self::where('employee_id', $this->employee_id)
            ->where('id', '!=', $this->id)
            ->where('status', 'APPROVED')
            ->where(function ($query) {
                $query->whereBetween('start_date', [$this->start_date, $this->end_date])
                      ->orWhereBetween('end_date', [$this->start_date, $this->end_date])
                      ->orWhere(function ($q) {
                          $q->where('start_date', '<=', $this->start_date)
                            ->where('end_date', '>=', $this->end_date);
                      });
            })
            ->exists();
    }

    /**
     * التحقق من كون الإجازة متأخرة في العودة
     */
    public function isOverdue(): bool
    {
        return $this->status === 'IN_PROGRESS' && 
               $this->return_date < now() && 
               !$this->actual_return_date;
    }

    /**
     * الحصول على ملخص الإجازة
     */
    public function getSummary(): array
    {
        return [
            'employee' => $this->employee->full_name,
            'leave_type' => $this->leaveType->name,
            'start_date' => $this->start_date->format('Y-m-d'),
            'end_date' => $this->end_date->format('Y-m-d'),
            'total_days' => $this->total_days,
            'working_days' => $this->working_days,
            'status' => $this->status,
            'is_paid' => $this->is_paid,
            'replacement' => $this->replacementEmployee?->full_name,
        ];
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق للطلبات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'PENDING');
    }

    /**
     * نطاق للطلبات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * نطاق للإجازات الجارية
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'IN_PROGRESS');
    }

    /**
     * نطاق للإجازات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'IN_PROGRESS')
            ->where('return_date', '<', now())
            ->whereNull('actual_return_date');
    }

    /**
     * نطاق حسب نوع الإجازة
     */
    public function scopeByLeaveType($query, $leaveTypeId)
    {
        return $query->where('leave_type_id', $leaveTypeId);
    }

    /**
     * نطاق للفترة
     */
    public function scopeForPeriod($query, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_date', [$startDate, $endDate])
              ->orWhereBetween('end_date', [$startDate, $endDate])
              ->orWhere(function ($subQ) use ($startDate, $endDate) {
                  $subQ->where('start_date', '<=', $startDate)
                       ->where('end_date', '>=', $endDate);
              });
        });
    }

    /**
     * نطاق للموظف
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }
}
