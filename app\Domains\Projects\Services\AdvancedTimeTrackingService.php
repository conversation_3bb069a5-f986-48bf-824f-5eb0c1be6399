<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\TimeEntry;
use App\Domains\HR\Models\Employee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة إدارة الوقت والحضور المتقدمة - Enterprise Time Tracking
 * تدعم تتبع الوقت المتطور مع التحليلات والفوترة
 */
class AdvancedTimeTrackingService
{
    protected array $trackingMethods = ['MANUAL', 'TIMER', 'AUTOMATIC', 'MOBILE'];
    protected array $billableTypes = ['BILLABLE', 'NON_BILLABLE', 'INTERNAL'];

    /**
     * بدء تتبع الوقت المتقدم
     */
    public function startAdvancedTracking(
        int $employeeId,
        int $projectId,
        int $taskId = null,
        array $options = []
    ): TimeEntry {
        try {
            DB::beginTransaction();

            // إيقاف أي تتبع نشط للموظف
            $this->stopAllActiveTracking($employeeId);

            // إنشاء تسجيل وقت جديد
            $timeEntry = TimeEntry::startTimer(
                $projectId,
                $employeeId,
                $taskId,
                $options['description'] ?? null
            );

            // إعداد التتبع المتقدم
            $timeEntry->update([
                'tracking_method' => $options['method'] ?? 'TIMER',
                'is_billable' => $options['is_billable'] ?? $this->determineBillability($projectId, $taskId),
                'hourly_rate' => $options['hourly_rate'] ?? $this->getHourlyRate($employeeId, $projectId),
                'location' => $options['location'] ?? null,
                'device_info' => $this->captureDeviceInfo(),
                'metadata' => array_merge($options['metadata'] ?? [], [
                    'auto_screenshots' => $options['auto_screenshots'] ?? false,
                    'activity_monitoring' => $options['activity_monitoring'] ?? false,
                    'break_detection' => $options['break_detection'] ?? true,
                ]),
            ]);

            // بدء المراقبة التلقائية إذا كانت مفعلة
            if ($options['activity_monitoring'] ?? false) {
                $this->startActivityMonitoring($timeEntry);
            }

            // جدولة لقطات الشاشة التلقائية
            if ($options['auto_screenshots'] ?? false) {
                $this->scheduleAutoScreenshots($timeEntry);
            }

            DB::commit();

            // إشعار المدير
            $this->notifyManagerOfTimeStart($timeEntry);

            return $timeEntry;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في بدء تتبع الوقت المتقدم', [
                'employee_id' => $employeeId,
                'project_id' => $projectId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * إيقاف تتبع الوقت مع التحليل
     */
    public function stopAdvancedTracking(int $timeEntryId, array $options = []): TimeEntry
    {
        $timeEntry = TimeEntry::findOrFail($timeEntryId);

        if (!$timeEntry->is_active) {
            throw new \InvalidArgumentException('تسجيل الوقت غير نشط');
        }

        // إيقاف المؤقت
        $timeEntry->stopTimer();

        // تحليل النشاط
        $activityAnalysis = $this->analyzeActivity($timeEntry);

        // تحديث البيانات المتقدمة
        $timeEntry->update([
            'activity_level' => $activityAnalysis['activity_level'],
            'break_time' => $activityAnalysis['break_time'],
            'productive_time' => $activityAnalysis['productive_time'],
            'idle_time' => $activityAnalysis['idle_time'],
            'quality_score' => $this->calculateQualityScore($timeEntry, $activityAnalysis),
            'notes' => $options['notes'] ?? $timeEntry->notes,
            'metadata' => array_merge($timeEntry->metadata ?? [], [
                'stop_reason' => $options['stop_reason'] ?? 'MANUAL',
                'activity_analysis' => $activityAnalysis,
            ]),
        ]);

        // إيقاف المراقبة
        $this->stopActivityMonitoring($timeEntry);

        // تحديث إحصائيات المهمة والمشروع
        $this->updateProjectStatistics($timeEntry);

        // إشعار المدير
        $this->notifyManagerOfTimeStop($timeEntry);

        return $timeEntry;
    }

    /**
     * تسجيل وقت يدوي
     */
    public function logManualTime(
        int $employeeId,
        int $projectId,
        array $timeData
    ): TimeEntry {
        try {
            DB::beginTransaction();

            $timeEntry = TimeEntry::create([
                'project_id' => $projectId,
                'task_id' => $timeData['task_id'] ?? null,
                'employee_id' => $employeeId,
                'start_time' => $timeData['start_time'],
                'end_time' => $timeData['end_time'],
                'hours' => $timeData['hours'],
                'description' => $timeData['description'],
                'is_billable' => $timeData['is_billable'] ?? true,
                'hourly_rate' => $timeData['hourly_rate'] ?? $this->getHourlyRate($employeeId, $projectId),
                'status' => 'PENDING_APPROVAL',
                'tracking_method' => 'MANUAL',
                'tags' => $timeData['tags'] ?? [],
                'metadata' => [
                    'manual_entry' => true,
                    'entry_date' => now(),
                    'requires_approval' => true,
                ],
            ]);

            // حساب التكلفة
            $timeEntry->calculateTotalCost();

            // إرسال للموافقة
            $this->submitForApproval($timeEntry);

            DB::commit();

            return $timeEntry;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * تحليل الإنتاجية
     */
    public function analyzeProductivity(int $employeeId, array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth();
        $endDate = $filters['end_date'] ?? now()->endOfMonth();

        $timeEntries = TimeEntry::where('employee_id', $employeeId)
                               ->whereBetween('start_time', [$startDate, $endDate])
                               ->with(['project', 'task'])
                               ->get();

        return [
            'summary' => $this->getProductivitySummary($timeEntries),
            'daily_breakdown' => $this->getDailyProductivityBreakdown($timeEntries),
            'project_breakdown' => $this->getProjectProductivityBreakdown($timeEntries),
            'efficiency_metrics' => $this->calculateEfficiencyMetrics($timeEntries),
            'time_patterns' => $this->analyzeTimePatterns($timeEntries),
            'recommendations' => $this->generateProductivityRecommendations($timeEntries),
        ];
    }

    /**
     * تقارير الوقت المتقدمة
     */
    public function generateTimeReport(string $reportType, array $parameters): array
    {
        return match ($reportType) {
            'employee_timesheet' => $this->generateEmployeeTimesheet($parameters),
            'project_time_summary' => $this->generateProjectTimeSummary($parameters),
            'billable_hours' => $this->generateBillableHoursReport($parameters),
            'productivity_analysis' => $this->generateProductivityAnalysisReport($parameters),
            'time_utilization' => $this->generateTimeUtilizationReport($parameters),
            'overtime_analysis' => $this->generateOvertimeAnalysisReport($parameters),
            'client_billing' => $this->generateClientBillingReport($parameters),
            default => throw new \InvalidArgumentException("نوع التقرير غير مدعوم: {$reportType}"),
        };
    }

    /**
     * إدارة الموافقات
     */
    public function manageApprovals(int $managerId, array $filters = []): array
    {
        $pendingEntries = TimeEntry::where('status', 'PENDING_APPROVAL')
                                  ->whereHas('project', function ($query) use ($managerId) {
                                      $query->where('project_manager_id', $managerId);
                                  })
                                  ->with(['employee', 'project', 'task'])
                                  ->when($filters['project_id'] ?? null, function ($query, $projectId) {
                                      $query->where('project_id', $projectId);
                                  })
                                  ->when($filters['employee_id'] ?? null, function ($query, $employeeId) {
                                      $query->where('employee_id', $employeeId);
                                  })
                                  ->orderBy('created_at', 'desc')
                                  ->get();

        return [
            'pending_entries' => $pendingEntries,
            'approval_statistics' => $this->getApprovalStatistics($managerId),
            'bulk_actions' => $this->getAvailableBulkActions(),
            'approval_rules' => $this->getApprovalRules($managerId),
        ];
    }

    /**
     * موافقة مجمعة
     */
    public function bulkApprove(array $timeEntryIds, int $approvedBy, string $notes = null): array
    {
        $results = ['approved' => 0, 'failed' => 0, 'errors' => []];

        foreach ($timeEntryIds as $entryId) {
            try {
                $timeEntry = TimeEntry::findOrFail($entryId);

                if ($timeEntry->approve($approvedBy)) {
                    $results['approved']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "فشل في موافقة التسجيل {$entryId}";
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "خطأ في التسجيل {$entryId}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * تصدير البيانات للفوترة
     */
    public function exportForBilling(int $projectId, array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth();
        $endDate = $filters['end_date'] ?? now()->endOfMonth();

        $timeEntries = TimeEntry::where('project_id', $projectId)
                               ->where('is_billable', true)
                               ->where('status', 'APPROVED')
                               ->whereNull('invoice_id')
                               ->whereBetween('start_time', [$startDate, $endDate])
                               ->with(['employee', 'task'])
                               ->get();

        $groupedEntries = $timeEntries->groupBy('employee_id');

        $billingData = [];
        foreach ($groupedEntries as $employeeId => $entries) {
            $employee = $entries->first()->employee;

            $billingData[] = [
                'employee' => $employee,
                'total_hours' => $entries->sum('hours'),
                'total_cost' => $entries->sum('total_cost'),
                'entries' => $entries->map(function ($entry) {
                    return [
                        'date' => $entry->start_time->format('Y-m-d'),
                        'task' => $entry->task?->title,
                        'description' => $entry->description,
                        'hours' => $entry->hours,
                        'rate' => $entry->hourly_rate,
                        'cost' => $entry->total_cost,
                    ];
                }),
            ];
        }

        return [
            'project' => Project::find($projectId),
            'period' => ['start' => $startDate, 'end' => $endDate],
            'billing_data' => $billingData,
            'summary' => [
                'total_hours' => $timeEntries->sum('hours'),
                'total_cost' => $timeEntries->sum('total_cost'),
                'entries_count' => $timeEntries->count(),
                'employees_count' => $groupedEntries->count(),
            ],
        ];
    }

    /**
     * تحليل أنماط العمل
     */
    public function analyzeWorkPatterns(int $employeeId, array $filters = []): array
    {
        $timeEntries = TimeEntry::where('employee_id', $employeeId)
                               ->when($filters['start_date'] ?? null, function ($query, $date) {
                                   $query->where('start_time', '>=', $date);
                               })
                               ->when($filters['end_date'] ?? null, function ($query, $date) {
                                   $query->where('start_time', '<=', $date);
                               })
                               ->get();

        return [
            'peak_hours' => $this->identifyPeakWorkingHours($timeEntries),
            'work_rhythm' => $this->analyzeWorkRhythm($timeEntries),
            'break_patterns' => $this->analyzeBreakPatterns($timeEntries),
            'productivity_trends' => $this->analyzeProductivityTrends($timeEntries),
            'focus_periods' => $this->identifyFocusPeriods($timeEntries),
            'distraction_analysis' => $this->analyzeDistractions($timeEntries),
        ];
    }

    // دوال مساعدة
    protected function stopAllActiveTracking(int $employeeId): void
    {
        TimeEntry::where('employee_id', $employeeId)
                 ->where('status', 'RUNNING')
                 ->each(function ($entry) {
                     $entry->stopTimer();
                 });
    }

    protected function determineBillability(int $projectId, int $taskId = null): bool
    {
        $project = Project::find($projectId);

        if ($taskId) {
            $task = Task::find($taskId);
            return $task->is_billable ?? $project->is_billable;
        }

        return $project->is_billable;
    }

    protected function getHourlyRate(int $employeeId, int $projectId): float
    {
        $employee = Employee::find($employeeId);
        $project = Project::find($projectId);

        // أولوية: معدل الموظف في المشروع > معدل المشروع > معدل الموظف العام
        $teamMember = $project->teamMembers()->where('employee_id', $employeeId)->first();

        return $teamMember?->pivot->hourly_rate ??
               $project->hourly_rate ??
               $employee->hourly_rate ??
               config('projects.default_hourly_rate', 50);
    }

    protected function captureDeviceInfo(): array
    {
        return [
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'platform' => $this->detectPlatform(),
            'browser' => $this->detectBrowser(),
            'screen_resolution' => request()->header('X-Screen-Resolution'),
            'timezone' => request()->header('X-Timezone') ?? config('app.timezone'),
        ];
    }

    protected function analyzeActivity(TimeEntry $timeEntry): array
    {
        // تحليل النشاط بناءً على البيانات المجمعة
        return [
            'activity_level' => 85.5, // نسبة النشاط
            'break_time' => 0.5, // ساعات الاستراحة
            'productive_time' => $timeEntry->hours * 0.85, // الوقت المنتج
            'idle_time' => $timeEntry->hours * 0.15, // وقت الخمول
            'keyboard_activity' => 75, // نشاط لوحة المفاتيح
            'mouse_activity' => 80, // نشاط الماوس
            'application_usage' => [], // استخدام التطبيقات
        ];
    }

    protected function calculateQualityScore(TimeEntry $timeEntry, array $activityAnalysis): float
    {
        $activityScore = $activityAnalysis['activity_level'];
        $productivityScore = ($activityAnalysis['productive_time'] / $timeEntry->hours) * 100;
        $consistencyScore = 100 - ($activityAnalysis['idle_time'] / $timeEntry->hours) * 100;

        return ($activityScore + $productivityScore + $consistencyScore) / 3;
    }

    protected function updateProjectStatistics(TimeEntry $timeEntry): void
    {
        $project = $timeEntry->project;
        $project->increment('total_logged_hours', $timeEntry->hours);
        $project->increment('actual_cost', $timeEntry->total_cost);

        if ($timeEntry->task) {
            $timeEntry->task->increment('actual_hours', $timeEntry->hours);
        }
    }

    protected function submitForApproval(TimeEntry $timeEntry): void
    {
        $project = $timeEntry->project;

        if ($project->projectManager) {
            $project->projectManager->notify(
                new \App\Notifications\TimeEntryPendingApprovalNotification($timeEntry)
            );
        }
    }

    // دوال إضافية للتحليل والتقارير
    protected function startActivityMonitoring(TimeEntry $timeEntry): void { /* تنفيذ مراقبة النشاط */ }
    protected function scheduleAutoScreenshots(TimeEntry $timeEntry): void { /* تنفيذ جدولة لقطات الشاشة */ }
    protected function stopActivityMonitoring(TimeEntry $timeEntry): void { /* إيقاف مراقبة النشاط */ }
    protected function notifyManagerOfTimeStart(TimeEntry $timeEntry): void { /* إشعار المدير */ }
    protected function notifyManagerOfTimeStop(TimeEntry $timeEntry): void { /* إشعار المدير */ }
    protected function getProductivitySummary($timeEntries): array { return []; }
    protected function getDailyProductivityBreakdown($timeEntries): array { return []; }
    protected function getProjectProductivityBreakdown($timeEntries): array { return []; }
    protected function calculateEfficiencyMetrics($timeEntries): array { return []; }
    protected function analyzeTimePatterns($timeEntries): array { return []; }
    protected function generateProductivityRecommendations($timeEntries): array { return []; }
    protected function detectPlatform(): string { return 'Web'; }
    protected function detectBrowser(): string { return 'Unknown'; }
    protected function generateEmployeeTimesheet(array $parameters): array { return []; }
    protected function generateProjectTimeSummary(array $parameters): array { return []; }
    protected function generateBillableHoursReport(array $parameters): array { return []; }
    protected function generateProductivityAnalysisReport(array $parameters): array { return []; }
    protected function generateTimeUtilizationReport(array $parameters): array { return []; }
    protected function generateOvertimeAnalysisReport(array $parameters): array { return []; }
    protected function generateClientBillingReport(array $parameters): array { return []; }
    protected function getApprovalStatistics(int $managerId): array { return []; }
    protected function getAvailableBulkActions(): array { return []; }
    protected function getApprovalRules(int $managerId): array { return []; }
    protected function identifyPeakWorkingHours($timeEntries): array { return []; }
    protected function analyzeWorkRhythm($timeEntries): array { return []; }
    protected function analyzeBreakPatterns($timeEntries): array { return []; }
    protected function analyzeProductivityTrends($timeEntries): array { return []; }
    protected function identifyFocusPeriods($timeEntries): array { return []; }
    protected function analyzeDistractions($timeEntries): array { return []; }
}
