<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * نموذج المحفظة الرقمية
 * يمثل المحفظة الرقمية الداخلية للمستخدمين
 */
class DigitalWallet extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'wallet_id',
        'currency',
        'balance',
        'available_balance',
        'pending_balance',
        'reserved_balance',
        'status',
        'type',
        'limits',
        'verification_level',
        'is_primary',
        'metadata',
        'last_activity_at',
        'frozen_until',
        'freeze_reason',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'reserved_balance' => 'decimal:2',
        'limits' => 'array',
        'metadata' => 'array',
        'is_primary' => 'boolean',
        'last_activity_at' => 'datetime',
        'frozen_until' => 'datetime',
        'verification_level' => 'integer',
    ];

    /**
     * حالات المحفظة
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'frozen' => 'مجمد',
        'suspended' => 'معلق',
        'closed' => 'مغلق',
        'pending_verification' => 'في انتظار التحقق',
    ];

    /**
     * أنواع المحفظة
     */
    const TYPES = [
        'personal' => 'شخصية',
        'business' => 'تجارية',
        'merchant' => 'تاجر',
        'escrow' => 'ضمان',
        'savings' => 'توفير',
    ];

    /**
     * مستويات التحقق
     */
    const VERIFICATION_LEVELS = [
        0 => 'غير متحقق',
        1 => 'تحقق أساسي',
        2 => 'تحقق متوسط',
        3 => 'تحقق كامل',
    ];

    /**
     * العملات المدعومة
     */
    const SUPPORTED_CURRENCIES = [
        'SAR', 'AED', 'EGP', 'MAD', 'USD', 'EUR', 'GBP',
        'KWD', 'QAR', 'BHD', 'OMR', 'JOD', 'TND', 'DZD'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($wallet) {
            if (empty($wallet->wallet_id)) {
                $wallet->wallet_id = static::generateWalletId();
            }
        });
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع معاملات المحفظة
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'wallet_id');
    }

    /**
     * العلاقة مع عمليات التحويل الصادرة
     */
    public function outgoingTransfers(): HasMany
    {
        return $this->hasMany(WalletTransfer::class, 'from_wallet_id');
    }

    /**
     * العلاقة مع عمليات التحويل الواردة
     */
    public function incomingTransfers(): HasMany
    {
        return $this->hasMany(WalletTransfer::class, 'to_wallet_id');
    }

    /**
     * توليد معرف المحفظة
     */
    public static function generateWalletId(): string
    {
        return 'WALLET_' . strtoupper(Str::random(12));
    }

    /**
     * التحقق من إمكانية الاستخدام
     */
    public function isUsable(): bool
    {
        return $this->status === 'active' && 
               !$this->isFrozen() &&
               $this->verification_level >= 1;
    }

    /**
     * التحقق من تجميد المحفظة
     */
    public function isFrozen(): bool
    {
        return $this->status === 'frozen' || 
               ($this->frozen_until && $this->frozen_until->isFuture());
    }

    /**
     * التحقق من توفر الرصيد
     */
    public function hasSufficientBalance(float $amount): bool
    {
        return $this->available_balance >= $amount;
    }

    /**
     * التحقق من حدود المعاملة
     */
    public function checkTransactionLimits(float $amount, string $type = 'transfer'): array
    {
        $limits = $this->limits ?? [];
        $verificationLimits = $this->getVerificationLimits();
        
        $dailyLimit = min(
            $limits['daily'][$type] ?? PHP_FLOAT_MAX,
            $verificationLimits['daily'][$type] ?? PHP_FLOAT_MAX
        );
        
        $monthlyLimit = min(
            $limits['monthly'][$type] ?? PHP_FLOAT_MAX,
            $verificationLimits['monthly'][$type] ?? PHP_FLOAT_MAX
        );
        
        $singleLimit = min(
            $limits['single'][$type] ?? PHP_FLOAT_MAX,
            $verificationLimits['single'][$type] ?? PHP_FLOAT_MAX
        );

        // حساب الاستخدام اليومي والشهري
        $dailyUsage = $this->getDailyUsage($type);
        $monthlyUsage = $this->getMonthlyUsage($type);

        $errors = [];

        if ($amount > $singleLimit) {
            $errors[] = "المبلغ يتجاوز الحد الأقصى للمعاملة الواحدة ({$singleLimit} {$this->currency})";
        }

        if (($dailyUsage + $amount) > $dailyLimit) {
            $errors[] = "المبلغ يتجاوز الحد اليومي المتاح (" . ($dailyLimit - $dailyUsage) . " {$this->currency})";
        }

        if (($monthlyUsage + $amount) > $monthlyLimit) {
            $errors[] = "المبلغ يتجاوز الحد الشهري المتاح (" . ($monthlyLimit - $monthlyUsage) . " {$this->currency})";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'limits' => [
                'single' => $singleLimit,
                'daily' => $dailyLimit,
                'monthly' => $monthlyLimit,
                'daily_remaining' => max(0, $dailyLimit - $dailyUsage),
                'monthly_remaining' => max(0, $monthlyLimit - $monthlyUsage),
            ],
        ];
    }

    /**
     * الحصول على حدود التحقق
     */
    protected function getVerificationLimits(): array
    {
        return match ($this->verification_level) {
            0 => [
                'daily' => ['transfer' => 100, 'withdrawal' => 50],
                'monthly' => ['transfer' => 1000, 'withdrawal' => 500],
                'single' => ['transfer' => 50, 'withdrawal' => 25],
            ],
            1 => [
                'daily' => ['transfer' => 1000, 'withdrawal' => 500],
                'monthly' => ['transfer' => 10000, 'withdrawal' => 5000],
                'single' => ['transfer' => 500, 'withdrawal' => 250],
            ],
            2 => [
                'daily' => ['transfer' => 5000, 'withdrawal' => 2500],
                'monthly' => ['transfer' => 50000, 'withdrawal' => 25000],
                'single' => ['transfer' => 2500, 'withdrawal' => 1000],
            ],
            3 => [
                'daily' => ['transfer' => PHP_FLOAT_MAX, 'withdrawal' => PHP_FLOAT_MAX],
                'monthly' => ['transfer' => PHP_FLOAT_MAX, 'withdrawal' => PHP_FLOAT_MAX],
                'single' => ['transfer' => PHP_FLOAT_MAX, 'withdrawal' => PHP_FLOAT_MAX],
            ],
            default => [
                'daily' => ['transfer' => 0, 'withdrawal' => 0],
                'monthly' => ['transfer' => 0, 'withdrawal' => 0],
                'single' => ['transfer' => 0, 'withdrawal' => 0],
            ],
        };
    }

    /**
     * الحصول على الاستخدام اليومي
     */
    protected function getDailyUsage(string $type): float
    {
        return $this->transactions()
            ->where('type', $type)
            ->where('status', 'completed')
            ->whereDate('created_at', today())
            ->sum('amount');
    }

    /**
     * الحصول على الاستخدام الشهري
     */
    protected function getMonthlyUsage(string $type): float
    {
        return $this->transactions()
            ->where('type', $type)
            ->where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');
    }

    /**
     * إضافة رصيد
     */
    public function addBalance(float $amount, string $description = null, array $metadata = []): WalletTransaction
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('المبلغ يجب أن يكون أكبر من صفر');
        }

        return \DB::transaction(function () use ($amount, $description, $metadata) {
            // تحديث الرصيد
            $this->increment('balance', $amount);
            $this->increment('available_balance', $amount);
            $this->update(['last_activity_at' => now()]);

            // إنشاء معاملة
            return $this->transactions()->create([
                'transaction_id' => 'WT_' . strtoupper(Str::random(12)),
                'type' => 'credit',
                'amount' => $amount,
                'currency' => $this->currency,
                'description' => $description ?? 'إضافة رصيد',
                'status' => 'completed',
                'metadata' => $metadata,
                'balance_before' => $this->balance - $amount,
                'balance_after' => $this->balance,
            ]);
        });
    }

    /**
     * خصم رصيد
     */
    public function deductBalance(float $amount, string $description = null, array $metadata = []): WalletTransaction
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('المبلغ يجب أن يكون أكبر من صفر');
        }

        if (!$this->hasSufficientBalance($amount)) {
            throw new \Exception('الرصيد غير كافي');
        }

        return \DB::transaction(function () use ($amount, $description, $metadata) {
            // تحديث الرصيد
            $this->decrement('balance', $amount);
            $this->decrement('available_balance', $amount);
            $this->update(['last_activity_at' => now()]);

            // إنشاء معاملة
            return $this->transactions()->create([
                'transaction_id' => 'WT_' . strtoupper(Str::random(12)),
                'type' => 'debit',
                'amount' => $amount,
                'currency' => $this->currency,
                'description' => $description ?? 'خصم رصيد',
                'status' => 'completed',
                'metadata' => $metadata,
                'balance_before' => $this->balance + $amount,
                'balance_after' => $this->balance,
            ]);
        });
    }

    /**
     * حجز رصيد
     */
    public function reserveBalance(float $amount, string $description = null): WalletTransaction
    {
        if (!$this->hasSufficientBalance($amount)) {
            throw new \Exception('الرصيد غير كافي للحجز');
        }

        return \DB::transaction(function () use ($amount, $description) {
            // تحديث الأرصدة
            $this->decrement('available_balance', $amount);
            $this->increment('reserved_balance', $amount);
            $this->update(['last_activity_at' => now()]);

            // إنشاء معاملة
            return $this->transactions()->create([
                'transaction_id' => 'WT_' . strtoupper(Str::random(12)),
                'type' => 'reserve',
                'amount' => $amount,
                'currency' => $this->currency,
                'description' => $description ?? 'حجز رصيد',
                'status' => 'pending',
                'balance_before' => $this->available_balance + $amount,
                'balance_after' => $this->available_balance,
            ]);
        });
    }

    /**
     * إلغاء حجز الرصيد
     */
    public function releaseReservedBalance(float $amount, string $description = null): WalletTransaction
    {
        if ($amount > $this->reserved_balance) {
            throw new \Exception('المبلغ أكبر من الرصيد المحجوز');
        }

        return \DB::transaction(function () use ($amount, $description) {
            // تحديث الأرصدة
            $this->increment('available_balance', $amount);
            $this->decrement('reserved_balance', $amount);
            $this->update(['last_activity_at' => now()]);

            // إنشاء معاملة
            return $this->transactions()->create([
                'transaction_id' => 'WT_' . strtoupper(Str::random(12)),
                'type' => 'release',
                'amount' => $amount,
                'currency' => $this->currency,
                'description' => $description ?? 'إلغاء حجز رصيد',
                'status' => 'completed',
                'balance_before' => $this->available_balance - $amount,
                'balance_after' => $this->available_balance,
            ]);
        });
    }

    /**
     * تجميد المحفظة
     */
    public function freeze(string $reason = null, \DateTime $until = null): void
    {
        $this->update([
            'status' => 'frozen',
            'freeze_reason' => $reason,
            'frozen_until' => $until,
        ]);
    }

    /**
     * إلغاء تجميد المحفظة
     */
    public function unfreeze(): void
    {
        $this->update([
            'status' => 'active',
            'freeze_reason' => null,
            'frozen_until' => null,
        ]);
    }

    /**
     * تعيين كمحفظة أساسية
     */
    public function setAsPrimary(): void
    {
        // إلغاء تعيين المحافظ الأخرى كأساسية
        static::where('user_id', $this->user_id)
            ->where('currency', $this->currency)
            ->where('id', '!=', $this->id)
            ->update(['is_primary' => false]);

        // تعيين هذه المحفظة كأساسية
        $this->update(['is_primary' => true]);
    }

    /**
     * Scope للمحافظ النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope للمحافظ القابلة للاستخدام
     */
    public function scopeUsable($query)
    {
        return $query->where('status', 'active')
            ->where(function ($q) {
                $q->whereNull('frozen_until')
                  ->orWhere('frozen_until', '<=', now());
            });
    }

    /**
     * Scope للمحافظ الأساسية
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope للمحافظ حسب العملة
     */
    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }
}
