<?php

namespace App\Domains\Integration\Jobs;

use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\ApiEndpoint;
use App\Domains\Integration\Models\ApiKey;
use App\Domains\Integration\Services\ApiGatewayService;
use App\Domains\Integration\Events\RequestProcessed;
use App\Domains\Integration\Events\SecurityThreatDetected;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Process API Request Job
 * 
 * Handles asynchronous processing of API requests through the gateway
 * with comprehensive error handling, retry logic, and monitoring
 */
class ProcessApiRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $requestId;
    public ApiGateway $gateway;
    public ApiEndpoint $endpoint;
    public ?ApiKey $apiKey;
    public array $requestData;
    public array $headers;
    public string $method;
    public array $context;
    public int $tries = 3;
    public int $maxExceptions = 3;
    public int $timeout = 300; // 5 minutes
    public int $retryAfter = 60; // 1 minute

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $requestId,
        ApiGateway $gateway,
        ApiEndpoint $endpoint,
        ?ApiKey $apiKey,
        array $requestData,
        array $headers = [],
        string $method = 'POST',
        array $context = []
    ) {
        $this->requestId = $requestId;
        $this->gateway = $gateway;
        $this->endpoint = $endpoint;
        $this->apiKey = $apiKey;
        $this->requestData = $requestData;
        $this->headers = $headers;
        $this->method = $method;
        $this->context = $context;

        // Set queue based on priority
        $this->onQueue($this->determineQueue());
        
        // Set delay if specified
        if (isset($context['delay_seconds'])) {
            $this->delay(now()->addSeconds($context['delay_seconds']));
        }
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping($this->requestId),
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(ApiGatewayService $gatewayService): void
    {
        $startTime = microtime(true);
        
        try {
            Log::info('Processing API request', [
                'request_id' => $this->requestId,
                'gateway_id' => $this->gateway->gateway_id,
                'endpoint_id' => $this->endpoint->endpoint_id,
                'method' => $this->method,
                'attempt' => $this->attempts(),
            ]);

            // Update request status
            $this->updateRequestStatus('processing');

            // Validate request
            $this->validateRequest();

            // Apply security checks
            $this->performSecurityChecks();

            // Process the request
            $response = $gatewayService->processRequest(
                $this->gateway,
                $this->endpoint,
                $this->requestData,
                $this->headers,
                $this->method,
                $this->context
            );

            $processingTime = microtime(true) - $startTime;

            // Update request status
            $this->updateRequestStatus('completed', $response);

            // Fire success event
            event(new RequestProcessed(
                $this->requestId,
                $this->gateway,
                $this->endpoint,
                $this->apiKey,
                $this->requestData,
                $response,
                $processingTime,
                'success',
                null,
                $this->collectMetrics($processingTime)
            ));

            Log::info('API request processed successfully', [
                'request_id' => $this->requestId,
                'processing_time' => $processingTime,
                'response_size' => strlen(json_encode($response)),
            ]);

        } catch (\Exception $e) {
            $this->handleFailure($e, microtime(true) - $startTime);
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('API request processing failed permanently', [
            'request_id' => $this->requestId,
            'gateway_id' => $this->gateway->gateway_id,
            'endpoint_id' => $this->endpoint->endpoint_id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update request status
        $this->updateRequestStatus('failed', null, $exception->getMessage());

        // Fire failure event
        event(new RequestProcessed(
            $this->requestId,
            $this->gateway,
            $this->endpoint,
            $this->apiKey,
            $this->requestData,
            [],
            0,
            'failed',
            $exception->getMessage(),
            []
        ));

        // Clean up any resources
        $this->cleanup();
    }

    /**
     * Validate the request
     */
    protected function validateRequest(): void
    {
        // Check if gateway is active
        if (!$this->gateway->is_active) {
            throw new \Exception("Gateway {$this->gateway->gateway_id} is not active");
        }

        // Check if endpoint is active
        if (!$this->endpoint->is_active) {
            throw new \Exception("Endpoint {$this->endpoint->endpoint_id} is not active");
        }

        // Validate API key if required
        if ($this->endpoint->authentication_required && !$this->apiKey) {
            throw new \Exception("API key required for endpoint {$this->endpoint->endpoint_id}");
        }

        // Check API key validity
        if ($this->apiKey && !$this->apiKey->is_active) {
            throw new \Exception("API key is not active");
        }

        // Validate request data structure
        if (empty($this->requestData) && in_array($this->method, ['POST', 'PUT', 'PATCH'])) {
            throw new \Exception("Request data is required for {$this->method} requests");
        }
    }

    /**
     * Perform security checks
     */
    protected function performSecurityChecks(): void
    {
        $sourceIp = $this->context['source_ip'] ?? 'unknown';
        $userAgent = $this->headers['User-Agent'] ?? 'unknown';

        // Check for suspicious patterns
        if ($this->detectSuspiciousActivity($sourceIp, $userAgent)) {
            event(new SecurityThreatDetected(
                $this->requestId,
                'suspicious_behavior',
                'medium',
                [
                    'source_ip' => $sourceIp,
                    'user_agent' => $userAgent,
                    'detection_method' => 'pattern_analysis',
                ],
                $this->gateway,
                null,
                $sourceIp,
                $userAgent
            ));
        }

        // Check rate limits
        $this->checkRateLimits($sourceIp);

        // Validate payload for malicious content
        $this->validatePayloadSecurity();
    }

    /**
     * Determine queue based on priority
     */
    protected function determineQueue(): string
    {
        $priority = $this->context['priority'] ?? 'normal';
        
        return match ($priority) {
            'critical' => 'integration-critical',
            'high' => 'integration-high',
            'low' => 'integration-low',
            default => 'integration-normal',
        };
    }

    /**
     * Update request status in cache
     */
    protected function updateRequestStatus(string $status, ?array $response = null, ?string $error = null): void
    {
        $statusData = [
            'status' => $status,
            'updated_at' => now()->toISOString(),
            'attempt' => $this->attempts(),
        ];

        if ($response) {
            $statusData['response'] = $response;
        }

        if ($error) {
            $statusData['error'] = $error;
        }

        Cache::put("request_status:{$this->requestId}", $statusData, 3600);
    }

    /**
     * Collect performance metrics
     */
    protected function collectMetrics(float $processingTime): array
    {
        return [
            'processing_time' => $processingTime,
            'memory_usage' => memory_get_peak_usage(true),
            'request_size' => strlen(json_encode($this->requestData)),
            'queue_wait_time' => $this->context['queue_wait_time'] ?? 0,
            'attempt_number' => $this->attempts(),
        ];
    }

    /**
     * Handle processing failure
     */
    protected function handleFailure(\Exception $e, float $processingTime): void
    {
        Log::warning('API request processing failed', [
            'request_id' => $this->requestId,
            'error' => $e->getMessage(),
            'attempt' => $this->attempts(),
            'processing_time' => $processingTime,
        ]);

        // Update request status
        $this->updateRequestStatus('failed', null, $e->getMessage());

        // Fire failure event
        event(new RequestProcessed(
            $this->requestId,
            $this->gateway,
            $this->endpoint,
            $this->apiKey,
            $this->requestData,
            [],
            $processingTime,
            'failed',
            $e->getMessage(),
            $this->collectMetrics($processingTime)
        ));

        // Re-throw to trigger retry mechanism
        throw $e;
    }

    /**
     * Cleanup resources
     */
    protected function cleanup(): void
    {
        // Remove temporary cache entries
        Cache::forget("request_lock:{$this->requestId}");
        
        // Clean up any temporary files
        // Implementation depends on specific requirements
    }

    // Placeholder methods for security checks
    protected function detectSuspiciousActivity(string $ip, string $userAgent): bool { return false; }
    protected function checkRateLimits(string $ip): void { }
    protected function validatePayloadSecurity(): void { }
}
