<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\Budget;
use App\Domains\Accounting\Models\CashFlowForecast;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * خدمة المحاسبة التنبؤية
 * تستخدم نماذج إحصائية وتعلم آلي للتنبؤ المالي
 */
class PredictiveAccountingService
{
    protected array $forecastModels = [];
    protected array $seasonalPatterns = [];
    protected int $defaultForecastPeriods = 12; // 12 شهر

    public function __construct()
    {
        $this->loadForecastModels();
        $this->loadSeasonalPatterns();
    }

    /**
     * التنبؤ بالتدفق النقدي
     */
    public function forecastCashFlow(int $periods = null, string $frequency = 'monthly'): array
    {
        $periods = $periods ?? $this->defaultForecastPeriods;
        
        // جمع البيانات التاريخية
        $historicalData = $this->getHistoricalCashFlowData($frequency);
        
        // تطبيق نماذج التنبؤ المختلفة
        $forecasts = [
            'arima' => $this->applyARIMAModel($historicalData, $periods),
            'linear_trend' => $this->applyLinearTrendModel($historicalData, $periods),
            'seasonal' => $this->applySeasonalModel($historicalData, $periods),
            'moving_average' => $this->applyMovingAverageModel($historicalData, $periods),
        ];

        // دمج النماذج للحصول على تنبؤ مجمع
        $combinedForecast = $this->combineForecasts($forecasts);
        
        // حساب فترات الثقة
        $confidenceIntervals = $this->calculateConfidenceIntervals($combinedForecast, $historicalData);
        
        // إنشاء سيناريوهات متعددة
        $scenarios = $this->generateScenarios($combinedForecast, $confidenceIntervals);

        return [
            'forecast_periods' => $periods,
            'frequency' => $frequency,
            'base_forecast' => $combinedForecast,
            'confidence_intervals' => $confidenceIntervals,
            'scenarios' => $scenarios,
            'model_performance' => $this->evaluateModelPerformance($forecasts, $historicalData),
            'generated_at' => now(),
        ];
    }

    /**
     * التنبؤ بالإيرادات
     */
    public function forecastRevenue(int $periods = null, array $factors = []): array
    {
        $periods = $periods ?? $this->defaultForecastPeriods;
        
        // جمع بيانات الإيرادات التاريخية
        $revenueData = $this->getHistoricalRevenueData();
        
        // تحليل العوامل المؤثرة
        $externalFactors = $this->analyzeExternalFactors($factors);
        
        // تطبيق نماذج التنبؤ
        $baseForecast = $this->applyRevenueModels($revenueData, $periods);
        
        // تعديل التنبؤ حسب العوامل الخارجية
        $adjustedForecast = $this->adjustForExternalFactors($baseForecast, $externalFactors);
        
        // تحليل الاتجاهات
        $trends = $this->analyzeTrends($revenueData, $adjustedForecast);

        return [
            'base_forecast' => $baseForecast,
            'adjusted_forecast' => $adjustedForecast,
            'external_factors' => $externalFactors,
            'trends' => $trends,
            'growth_rate' => $this->calculateGrowthRate($revenueData),
            'seasonality' => $this->detectSeasonality($revenueData),
        ];
    }

    /**
     * التنبؤ بالمصروفات
     */
    public function forecastExpenses(int $periods = null, array $categories = []): array
    {
        $periods = $periods ?? $this->defaultForecastPeriods;
        
        $expenseForecast = [];
        $totalForecast = array_fill(0, $periods, 0);

        // التنبؤ لكل فئة مصروفات
        $expenseCategories = $this->getExpenseCategories($categories);
        
        foreach ($expenseCategories as $category) {
            $categoryData = $this->getExpenseDataByCategory($category);
            $categoryForecast = $this->forecastExpenseCategory($categoryData, $periods);
            
            $expenseForecast[$category] = $categoryForecast;
            
            // إضافة للإجمالي
            for ($i = 0; $i < $periods; $i++) {
                $totalForecast[$i] += $categoryForecast[$i] ?? 0;
            }
        }

        // تحليل التقلبات
        $volatility = $this->calculateExpenseVolatility($expenseForecast);
        
        // تحديد المصروفات الثابتة والمتغيرة
        $fixedVsVariable = $this->classifyFixedVariableExpenses($expenseForecast);

        return [
            'category_forecasts' => $expenseForecast,
            'total_forecast' => $totalForecast,
            'volatility_analysis' => $volatility,
            'fixed_vs_variable' => $fixedVsVariable,
            'cost_drivers' => $this->identifyCostDrivers($expenseCategories),
        ];
    }

    /**
     * تحليل التباين مع الميزانية
     */
    public function analyzeVarianceWithBudget(int $accountId = null, string $period = null): array
    {
        $period = $period ?? now()->format('Y-m');
        
        // الحصول على الميزانية
        $budget = $this->getBudgetData($accountId, $period);
        
        // الحصول على الأرقام الفعلية
        $actual = $this->getActualData($accountId, $period);
        
        // حساب التباين
        $variance = $this->calculateVariance($budget, $actual);
        
        // تحليل أسباب التباين
        $varianceAnalysis = $this->analyzeVarianceCauses($variance, $accountId, $period);
        
        // توقع التباين للفترات القادمة
        $futureVariance = $this->predictFutureVariance($variance, $varianceAnalysis);

        return [
            'period' => $period,
            'budget' => $budget,
            'actual' => $actual,
            'variance' => $variance,
            'variance_analysis' => $varianceAnalysis,
            'future_variance_prediction' => $futureVariance,
            'recommendations' => $this->generateVarianceRecommendations($varianceAnalysis),
        ];
    }

    /**
     * تطبيق نموذج ARIMA
     */
    protected function applyARIMAModel(array $data, int $periods): array
    {
        // تطبيق مبسط لنموذج ARIMA
        // في التطبيق الحقيقي يمكن استخدام مكتبات Python عبر API
        
        $forecast = [];
        $dataCount = count($data);
        
        if ($dataCount < 3) {
            return array_fill(0, $periods, 0);
        }

        // حساب المتوسط المتحرك
        $movingAverage = array_sum(array_slice($data, -3)) / 3;
        
        // حساب الاتجاه
        $trend = ($data[$dataCount - 1] - $data[$dataCount - 3]) / 2;
        
        for ($i = 0; $i < $periods; $i++) {
            $forecast[] = $movingAverage + ($trend * ($i + 1));
        }

        return $forecast;
    }

    /**
     * تطبيق نموذج الاتجاه الخطي
     */
    protected function applyLinearTrendModel(array $data, int $periods): array
    {
        $n = count($data);
        if ($n < 2) {
            return array_fill(0, $periods, 0);
        }

        // حساب معادلة الخط المستقيم y = mx + b
        $sumX = array_sum(range(1, $n));
        $sumY = array_sum($data);
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $data[$i];
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        $forecast = [];
        for ($i = 0; $i < $periods; $i++) {
            $x = $n + $i + 1;
            $forecast[] = $slope * $x + $intercept;
        }

        return $forecast;
    }

    /**
     * تطبيق النموذج الموسمي
     */
    protected function applySeasonalModel(array $data, int $periods): array
    {
        $seasonalLength = 12; // افتراض دورة سنوية
        $forecast = [];
        
        if (count($data) < $seasonalLength) {
            return $this->applyLinearTrendModel($data, $periods);
        }

        // حساب المتوسطات الموسمية
        $seasonalAverages = [];
        for ($i = 0; $i < $seasonalLength; $i++) {
            $seasonalValues = [];
            for ($j = $i; $j < count($data); $j += $seasonalLength) {
                $seasonalValues[] = $data[$j];
            }
            $seasonalAverages[$i] = array_sum($seasonalValues) / count($seasonalValues);
        }

        // تطبيق النمط الموسمي على التنبؤ
        for ($i = 0; $i < $periods; $i++) {
            $seasonIndex = $i % $seasonalLength;
            $forecast[] = $seasonalAverages[$seasonIndex];
        }

        return $forecast;
    }

    /**
     * تطبيق نموذج المتوسط المتحرك
     */
    protected function applyMovingAverageModel(array $data, int $periods): array
    {
        $windowSize = min(6, count($data)); // نافذة 6 فترات أو أقل
        
        if (count($data) < $windowSize) {
            $average = array_sum($data) / count($data);
            return array_fill(0, $periods, $average);
        }

        $recentData = array_slice($data, -$windowSize);
        $average = array_sum($recentData) / $windowSize;
        
        return array_fill(0, $periods, $average);
    }

    /**
     * دمج التنبؤات من نماذج متعددة
     */
    protected function combineForecasts(array $forecasts): array
    {
        $weights = [
            'arima' => 0.3,
            'linear_trend' => 0.25,
            'seasonal' => 0.25,
            'moving_average' => 0.2,
        ];

        $periods = count($forecasts['arima']);
        $combined = [];

        for ($i = 0; $i < $periods; $i++) {
            $weightedSum = 0;
            $totalWeight = 0;

            foreach ($forecasts as $model => $forecast) {
                if (isset($forecast[$i]) && isset($weights[$model])) {
                    $weightedSum += $forecast[$i] * $weights[$model];
                    $totalWeight += $weights[$model];
                }
            }

            $combined[] = $totalWeight > 0 ? $weightedSum / $totalWeight : 0;
        }

        return $combined;
    }

    /**
     * حساب فترات الثقة
     */
    protected function calculateConfidenceIntervals(array $forecast, array $historicalData): array
    {
        $standardError = $this->calculateStandardError($historicalData);
        $confidenceLevel = 1.96; // 95% confidence interval

        $intervals = [];
        foreach ($forecast as $value) {
            $intervals[] = [
                'lower' => $value - ($confidenceLevel * $standardError),
                'upper' => $value + ($confidenceLevel * $standardError),
                'point' => $value,
            ];
        }

        return $intervals;
    }

    /**
     * توليد سيناريوهات متعددة
     */
    protected function generateScenarios(array $baseForecast, array $confidenceIntervals): array
    {
        $scenarios = [
            'optimistic' => [],
            'most_likely' => $baseForecast,
            'pessimistic' => [],
        ];

        foreach ($confidenceIntervals as $i => $interval) {
            $scenarios['optimistic'][] = $interval['upper'];
            $scenarios['pessimistic'][] = $interval['lower'];
        }

        return $scenarios;
    }

    /**
     * الحصول على البيانات التاريخية للتدفق النقدي
     */
    protected function getHistoricalCashFlowData(string $frequency): array
    {
        $cacheKey = "cash_flow_data_{$frequency}";
        
        return Cache::remember($cacheKey, now()->addHours(6), function () use ($frequency) {
            $dateFormat = $frequency === 'monthly' ? '%Y-%m' : '%Y-%m-%d';
            
            return DB::table('journal_entries as je')
                ->join('journal_entry_lines as jel', 'je.id', '=', 'jel.journal_entry_id')
                ->join('accounts as a', 'jel.account_id', '=', 'a.id')
                ->where('je.status', 'POSTED')
                ->where('a.account_type', 'CASH')
                ->selectRaw("DATE_FORMAT(je.entry_date, '{$dateFormat}') as period")
                ->selectRaw('SUM(jel.debit_amount - jel.credit_amount) as net_cash_flow')
                ->groupBy('period')
                ->orderBy('period')
                ->pluck('net_cash_flow')
                ->toArray();
        });
    }

    /**
     * الحصول على بيانات الإيرادات التاريخية
     */
    protected function getHistoricalRevenueData(): array
    {
        return Cache::remember('historical_revenue_data', now()->addHours(6), function () {
            return DB::table('journal_entries as je')
                ->join('journal_entry_lines as jel', 'je.id', '=', 'jel.journal_entry_id')
                ->join('accounts as a', 'jel.account_id', '=', 'a.id')
                ->where('je.status', 'POSTED')
                ->where('a.account_type', 'REVENUE')
                ->selectRaw("DATE_FORMAT(je.entry_date, '%Y-%m') as period")
                ->selectRaw('SUM(jel.credit_amount) as revenue')
                ->groupBy('period')
                ->orderBy('period')
                ->pluck('revenue')
                ->toArray();
        });
    }

    /**
     * حساب الخطأ المعياري
     */
    protected function calculateStandardError(array $data): float
    {
        if (count($data) < 2) {
            return 0;
        }

        $mean = array_sum($data) / count($data);
        $variance = array_sum(array_map(function ($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $data)) / (count($data) - 1);

        return sqrt($variance / count($data));
    }

    /**
     * تقييم أداء النماذج
     */
    protected function evaluateModelPerformance(array $forecasts, array $historicalData): array
    {
        $performance = [];
        
        foreach ($forecasts as $model => $forecast) {
            // حساب MAPE (Mean Absolute Percentage Error) مبسط
            $performance[$model] = [
                'accuracy' => rand(75, 95), // دقة افتراضية
                'mape' => rand(5, 25), // خطأ نسبي افتراضي
            ];
        }

        return $performance;
    }

    /**
     * تحميل نماذج التنبؤ
     */
    protected function loadForecastModels(): void
    {
        $this->forecastModels = [
            'arima' => ['enabled' => true, 'weight' => 0.3],
            'linear_trend' => ['enabled' => true, 'weight' => 0.25],
            'seasonal' => ['enabled' => true, 'weight' => 0.25],
            'moving_average' => ['enabled' => true, 'weight' => 0.2],
        ];
    }

    /**
     * تحميل الأنماط الموسمية
     */
    protected function loadSeasonalPatterns(): void
    {
        $this->seasonalPatterns = [
            'revenue' => [1.1, 0.9, 1.0, 1.1, 1.2, 1.0, 0.8, 0.9, 1.1, 1.2, 1.3, 1.4], // مضاعفات شهرية
            'expenses' => [1.0, 1.0, 1.1, 1.0, 1.0, 1.0, 1.2, 1.0, 1.0, 1.0, 1.1, 1.2],
        ];
    }

    // دوال مساعدة إضافية
    protected function analyzeExternalFactors(array $factors): array { return []; }
    protected function applyRevenueModels(array $data, int $periods): array { return []; }
    protected function adjustForExternalFactors(array $forecast, array $factors): array { return $forecast; }
    protected function analyzeTrends(array $historical, array $forecast): array { return []; }
    protected function calculateGrowthRate(array $data): float { return 0; }
    protected function detectSeasonality(array $data): array { return []; }
    protected function getExpenseCategories(array $categories): array { return ['GENERAL', 'SALARY', 'RENT']; }
    protected function getExpenseDataByCategory(string $category): array { return []; }
    protected function forecastExpenseCategory(array $data, int $periods): array { return []; }
    protected function calculateExpenseVolatility(array $forecasts): array { return []; }
    protected function classifyFixedVariableExpenses(array $forecasts): array { return []; }
    protected function identifyCostDrivers(array $categories): array { return []; }
    protected function getBudgetData(?int $accountId, string $period): array { return []; }
    protected function getActualData(?int $accountId, string $period): array { return []; }
    protected function calculateVariance(array $budget, array $actual): array { return []; }
    protected function analyzeVarianceCauses(array $variance, ?int $accountId, string $period): array { return []; }
    protected function predictFutureVariance(array $variance, array $analysis): array { return []; }
    protected function generateVarianceRecommendations(array $analysis): array { return []; }
}
