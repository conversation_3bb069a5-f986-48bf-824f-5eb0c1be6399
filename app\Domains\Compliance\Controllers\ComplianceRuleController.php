<?php

namespace App\Domains\Compliance\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Compliance\Models\ComplianceRule;
use App\Domains\Compliance\Requests\CreateComplianceRuleRequest;
use App\Domains\Compliance\Resources\ComplianceRuleResource;
use App\Domains\Compliance\Repositories\ComplianceRuleRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * تحكم قواعد الامتثال
 */
class ComplianceRuleController extends Controller
{
    public function __construct(
        protected ComplianceRuleRepository $ruleRepository
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * عرض قائمة القواعد
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', ComplianceRule::class);

        $rules = $this->ruleRepository->getAll($request->all());

        return response()->json([
            'success' => true,
            'data' => ComplianceRuleResource::collection($rules),
            'meta' => [
                'total' => $rules->total(),
                'per_page' => $rules->perPage(),
                'current_page' => $rules->currentPage(),
                'last_page' => $rules->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء قاعدة جديدة
     */
    public function store(CreateComplianceRuleRequest $request): JsonResponse
    {
        $rule = $this->ruleRepository->create($request->getValidatedData());

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء قاعدة الامتثال بنجاح',
            'data' => new ComplianceRuleResource($rule),
        ], 201);
    }

    /**
     * عرض قاعدة محددة
     */
    public function show(ComplianceRule $rule, Request $request): JsonResponse
    {
        $this->authorize('view', $rule);

        $rule->load(['country']);

        return response()->json([
            'success' => true,
            'data' => new ComplianceRuleResource($rule),
        ]);
    }

    /**
     * تحديث قاعدة
     */
    public function update(ComplianceRule $rule, CreateComplianceRuleRequest $request): JsonResponse
    {
        $this->authorize('update', $rule);

        $this->ruleRepository->update($rule, $request->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث قاعدة الامتثال بنجاح',
            'data' => new ComplianceRuleResource($rule->fresh()),
        ]);
    }

    /**
     * حذف قاعدة
     */
    public function destroy(ComplianceRule $rule): JsonResponse
    {
        $this->authorize('delete', $rule);

        $this->ruleRepository->delete($rule);

        return response()->json([
            'success' => true,
            'message' => 'تم حذف قاعدة الامتثال بنجاح',
        ]);
    }

    /**
     * تفعيل قاعدة
     */
    public function activate(ComplianceRule $rule): JsonResponse
    {
        $this->authorize('toggleStatus', $rule);

        $this->ruleRepository->activate($rule);

        return response()->json([
            'success' => true,
            'message' => 'تم تفعيل قاعدة الامتثال بنجاح',
            'data' => new ComplianceRuleResource($rule->fresh()),
        ]);
    }

    /**
     * إلغاء تفعيل قاعدة
     */
    public function deactivate(ComplianceRule $rule): JsonResponse
    {
        $this->authorize('toggleStatus', $rule);

        $this->ruleRepository->deactivate($rule);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء تفعيل قاعدة الامتثال بنجاح',
            'data' => new ComplianceRuleResource($rule->fresh()),
        ]);
    }

    /**
     * فحص الامتثال لقاعدة معينة
     */
    public function checkCompliance(ComplianceRule $rule, Request $request): JsonResponse
    {
        $this->authorize('view', $rule);

        $request->validate([
            'entity_data' => 'required|array',
        ]);

        $complianceResult = $rule->checkCompliance($request->entity_data);

        return response()->json([
            'success' => true,
            'data' => $complianceResult,
        ]);
    }

    /**
     * الحصول على الكيانات المطبقة عليها القاعدة
     */
    public function getApplicableEntities(ComplianceRule $rule, Request $request): JsonResponse
    {
        $this->authorize('view', $rule);

        $entities = $this->ruleRepository->getApplicableRules(
            $request->get('entity_data', []),
            $rule->country->code
        );

        return response()->json([
            'success' => true,
            'data' => $entities,
        ]);
    }

    /**
     * الحصول على القواعد حسب الفئة
     */
    public function getByCategory(string $category, Request $request): JsonResponse
    {
        $this->authorize('viewAny', ComplianceRule::class);

        $rules = $this->ruleRepository->getRulesByCategory(
            $category,
            $request->get('country_code')
        );

        return response()->json([
            'success' => true,
            'data' => ComplianceRuleResource::collection($rules),
        ]);
    }

    /**
     * الحصول على القواعد حسب الدولة
     */
    public function getByCountry(string $country): JsonResponse
    {
        $this->authorize('viewAny', ComplianceRule::class);

        $rules = $this->ruleRepository->getActiveRulesForCountry($country);

        return response()->json([
            'success' => true,
            'data' => ComplianceRuleResource::collection($rules),
        ]);
    }

    /**
     * البحث في القواعد
     */
    public function search(string $term, Request $request): JsonResponse
    {
        $this->authorize('viewAny', ComplianceRule::class);

        $rules = $this->ruleRepository->search($term, $request->all());

        return response()->json([
            'success' => true,
            'data' => ComplianceRuleResource::collection($rules),
        ]);
    }

    /**
     * الحصول على إحصائيات القواعد
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $this->authorize('viewAny', ComplianceRule::class);

        $statistics = $this->ruleRepository->getStatistics(
            $request->get('country_code')
        );

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * الحصول على فئات القواعد
     */
    public function getCategories(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => ComplianceRule::RULE_CATEGORIES,
        ]);
    }
}
