<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Accounting Configuration
    |--------------------------------------------------------------------------
    |
    | تكوين نظام المحاسبة المتقدم
    |
    */

    // الإعدادات العامة
    'enabled' => true,
    'default_currency' => env('ACCOUNTING_DEFAULT_CURRENCY', 'SAR'),
    'default_accounting_standard' => env('ACCOUNTING_DEFAULT_STANDARD', 'IFRS'),
    'fiscal_year_start' => env('ACCOUNTING_FISCAL_YEAR_START', '01-01'),
    'decimal_places' => env('ACCOUNTING_DECIMAL_PLACES', 2),
    'rounding_method' => env('ACCOUNTING_ROUNDING_METHOD', 'round'), // round, ceil, floor

    // العملات المدعومة
    'supported_currencies' => [
        'SAR' => [
            'name' => 'Saudi Riyal',
            'name_ar' => 'ريال سعودي',
            'symbol' => 'ر.س',
            'decimal_places' => 2,
            'exchange_rate_source' => 'sama',
        ],
        'USD' => [
            'name' => 'US Dollar',
            'name_ar' => 'دولار أمريكي',
            'symbol' => '$',
            'decimal_places' => 2,
            'exchange_rate_source' => 'xe',
        ],
        'EUR' => [
            'name' => 'Euro',
            'name_ar' => 'يورو',
            'symbol' => '€',
            'decimal_places' => 2,
            'exchange_rate_source' => 'ecb',
        ],
        'GBP' => [
            'name' => 'British Pound',
            'name_ar' => 'جنيه إسترليني',
            'symbol' => '£',
            'decimal_places' => 2,
            'exchange_rate_source' => 'boe',
        ],
        'AED' => [
            'name' => 'UAE Dirham',
            'name_ar' => 'درهم إماراتي',
            'symbol' => 'د.إ',
            'decimal_places' => 2,
            'exchange_rate_source' => 'cbuae',
        ],
        'EGP' => [
            'name' => 'Egyptian Pound',
            'name_ar' => 'جنيه مصري',
            'symbol' => 'ج.م',
            'decimal_places' => 2,
            'exchange_rate_source' => 'cbe',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Chart of Accounts Configuration
    |--------------------------------------------------------------------------
    */
    'chart_of_accounts' => [
        'auto_generate' => true,
        'template' => 'saudi_gaap', // saudi_gaap, ifrs, us_gaap
        'max_levels' => 5,
        'code_format' => 'hierarchical', // hierarchical, sequential
        'allow_custom_codes' => true,
        'require_approval_for_changes' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Journal Entry Configuration
    |--------------------------------------------------------------------------
    */
    'journal_entries' => [
        'require_approval' => true,
        'auto_numbering' => true,
        'number_format' => 'JE-{year}-{month}-{sequence}',
        'allow_backdating' => false,
        'require_supporting_documents' => true,
        'auto_reverse_entries' => true,
        'batch_processing' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Invoice Configuration
    |--------------------------------------------------------------------------
    */
    'invoices' => [
        'auto_numbering' => true,
        'number_format' => 'INV-{year}-{sequence}',
        'require_approval' => true,
        'auto_send' => false,
        'payment_terms_days' => 30,
        'late_fee_percentage' => 2.5,
        'auto_reminders' => true,
        'reminder_schedule' => [7, 14, 30], // days before due date
        'multi_currency_support' => true,
        'tax_calculation' => 'automatic',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Configuration
    |--------------------------------------------------------------------------
    */
    'payments' => [
        'require_approval' => true,
        'approval_threshold' => 10000,
        'auto_matching' => true,
        'bank_reconciliation' => true,
        'duplicate_detection' => true,
        'payment_methods' => [
            'cash' => 'نقداً',
            'bank_transfer' => 'تحويل بنكي',
            'credit_card' => 'بطاقة ائتمان',
            'check' => 'شيك',
            'online_payment' => 'دفع إلكتروني',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Financial Reporting Configuration
    |--------------------------------------------------------------------------
    */
    'reporting' => [
        'real_time_reports' => true,
        'scheduled_reports' => true,
        'comparative_analysis' => true,
        'drill_down_capability' => true,
        'export_formats' => ['pdf', 'excel', 'csv'],
        'email_reports' => true,
        'dashboard_widgets' => true,
        'custom_reports' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Budget Management
    |--------------------------------------------------------------------------
    */
    'budgets' => [
        'enabled' => true,
        'budget_periods' => ['monthly', 'quarterly', 'yearly'],
        'variance_analysis' => true,
        'budget_alerts' => true,
        'alert_thresholds' => [
            'warning' => 80, // percentage
            'critical' => 95,
        ],
        'revision_control' => true,
        'approval_workflow' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Accounting Periods
    |--------------------------------------------------------------------------
    */
    'periods' => [
        'auto_create' => true,
        'period_type' => 'monthly', // monthly, quarterly
        'closing_process' => [
            'require_approval' => true,
            'auto_adjusting_entries' => true,
            'year_end_closing' => true,
            'backup_before_closing' => true,
        ],
        'reopen_restrictions' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Bank Integration
    |--------------------------------------------------------------------------
    */
    'bank_integration' => [
        'enabled' => true,
        'auto_import' => true,
        'supported_formats' => ['csv', 'excel', 'ofx', 'qif'],
        'auto_categorization' => true,
        'duplicate_detection' => true,
        'reconciliation_rules' => true,
        'api_connections' => [
            'saudi_banks' => true,
            'international_banks' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Integration
    |--------------------------------------------------------------------------
    */
    'tax_integration' => [
        'enabled' => true,
        'auto_calculation' => true,
        'multi_tax_support' => true,
        'tax_reporting' => true,
        'compliance_monitoring' => true,
        'e_invoicing_integration' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Trail
    |--------------------------------------------------------------------------
    */
    'audit_trail' => [
        'enabled' => true,
        'track_all_changes' => true,
        'user_tracking' => true,
        'ip_tracking' => true,
        'retention_period_years' => 7,
        'immutable_records' => true,
        'digital_signatures' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Validation
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'accounting_equation' => true,
        'balance_validation' => true,
        'date_validation' => true,
        'amount_validation' => true,
        'reference_validation' => true,
        'duplicate_prevention' => true,
        'data_integrity_checks' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'caching' => [
            'enabled' => true,
            'cache_duration' => 3600, // seconds
            'cache_reports' => true,
            'cache_balances' => true,
        ],
        'indexing' => [
            'auto_indexing' => true,
            'custom_indexes' => true,
        ],
        'archiving' => [
            'enabled' => true,
            'archive_after_years' => 3,
            'compress_archived_data' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encryption' => [
            'sensitive_data' => true,
            'bank_details' => true,
            'financial_amounts' => false,
        ],
        'access_control' => [
            'role_based' => true,
            'field_level_security' => true,
            'ip_restrictions' => false,
        ],
        'backup' => [
            'auto_backup' => true,
            'backup_frequency' => 'daily',
            'retention_days' => 90,
            'encrypted_backups' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'crm' => [
            'enabled' => true,
            'sync_customers' => true,
            'sync_invoices' => true,
            'sync_payments' => true,
        ],
        'hr' => [
            'enabled' => true,
            'sync_employees' => true,
            'sync_payroll' => true,
            'sync_expenses' => true,
        ],
        'projects' => [
            'enabled' => true,
            'project_accounting' => true,
            'time_billing' => true,
            'expense_tracking' => true,
        ],
        'taxation' => [
            'enabled' => true,
            'auto_tax_calculation' => true,
            'tax_reporting' => true,
            'compliance_monitoring' => true,
        ],
        'ecommerce' => [
            'enabled' => true,
            'auto_invoice_generation' => true,
            'payment_sync' => true,
            'inventory_integration' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | AI and Automation
    |--------------------------------------------------------------------------
    */
    'ai_features' => [
        'enabled' => true,
        'auto_categorization' => true,
        'anomaly_detection' => true,
        'predictive_analytics' => true,
        'smart_reconciliation' => true,
        'expense_recognition' => true,
        'fraud_detection' => true,
        'cash_flow_prediction' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance Settings
    |--------------------------------------------------------------------------
    */
    'compliance' => [
        'saudi_gaap' => true,
        'ifrs' => false,
        'zakat_compliance' => true,
        'vat_compliance' => true,
        'audit_requirements' => true,
        'regulatory_reporting' => true,
        'data_retention_compliance' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization
    |--------------------------------------------------------------------------
    */
    'localization' => [
        'default_language' => 'ar',
        'supported_languages' => ['ar', 'en'],
        'rtl_support' => true,
        'date_format' => 'd/m/Y',
        'number_format' => [
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'decimal_places' => 2,
        ],
        'currency_display' => [
            'symbol_position' => 'after', // before, after
            'space_between' => true,
        ],
    ],
];
