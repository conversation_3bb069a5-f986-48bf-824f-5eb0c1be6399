<?php

namespace App\Domains\Integration\Events;

use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\SecurityIncident;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

/**
 * Security Threat Detected Event
 * 
 * Fired when a security threat is detected by the system
 */
class SecurityThreatDetected implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $requestId;
    public string $threatType;
    public string $severity;
    public array $threatData;
    public ?ApiGateway $gateway;
    public ?SecurityIncident $incident;
    public string $sourceIp;
    public string $userAgent;
    public array $mitigationActions;
    public Carbon $detectedAt;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $requestId,
        string $threatType,
        string $severity = 'medium',
        array $threatData = [],
        ?ApiGateway $gateway = null,
        ?SecurityIncident $incident = null,
        string $sourceIp = 'unknown',
        string $userAgent = 'unknown',
        array $mitigationActions = []
    ) {
        $this->requestId = $requestId;
        $this->threatType = $threatType;
        $this->severity = $severity;
        $this->threatData = $threatData;
        $this->gateway = $gateway;
        $this->incident = $incident;
        $this->sourceIp = $sourceIp;
        $this->userAgent = $userAgent;
        $this->mitigationActions = $mitigationActions;
        $this->detectedAt = now();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('security.threats'),
            new PrivateChannel('admin.security'),
        ];

        if ($this->gateway) {
            $channels[] = new PrivateChannel("gateway.{$this->gateway->gateway_id}.security");
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'request_id' => $this->requestId,
            'threat_type' => $this->threatType,
            'severity' => $this->severity,
            'gateway_id' => $this->gateway?->gateway_id,
            'incident_id' => $this->incident?->id,
            'source_ip' => $this->sourceIp,
            'user_agent' => $this->userAgent,
            'mitigation_actions' => $this->mitigationActions,
            'detected_at' => $this->detectedAt->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'security.threat.detected';
    }

    /**
     * Get threat description
     */
    public function getThreatDescription(): string
    {
        $descriptions = [
            'ddos' => 'Distributed Denial of Service attack detected',
            'brute_force' => 'Brute force attack attempt detected',
            'sql_injection' => 'SQL injection attempt detected',
            'xss' => 'Cross-site scripting attempt detected',
            'malicious_payload' => 'Malicious payload detected in request',
            'suspicious_behavior' => 'Suspicious user behavior detected',
            'rate_limit_abuse' => 'Rate limit abuse detected',
            'unauthorized_access' => 'Unauthorized access attempt detected',
            'data_exfiltration' => 'Potential data exfiltration detected',
            'anomaly' => 'Security anomaly detected',
        ];

        return $descriptions[$this->threatType] ?? "Security threat detected: {$this->threatType}";
    }

    /**
     * Get threat severity level
     */
    public function getSeverityLevel(): int
    {
        return match ($this->severity) {
            'critical' => 5,
            'high' => 4,
            'medium' => 3,
            'low' => 2,
            'info' => 1,
            default => 3,
        };
    }

    /**
     * Check if threat requires immediate response
     */
    public function requiresImmediateResponse(): bool
    {
        return in_array($this->severity, ['critical', 'high']) ||
               in_array($this->threatType, ['ddos', 'data_exfiltration', 'unauthorized_access']);
    }

    /**
     * Get recommended mitigation actions
     */
    public function getRecommendedMitigations(): array
    {
        $mitigations = [
            'ddos' => [
                'Enable DDoS protection',
                'Block source IP ranges',
                'Implement rate limiting',
                'Scale up infrastructure',
            ],
            'brute_force' => [
                'Block source IP',
                'Implement account lockout',
                'Enable CAPTCHA',
                'Require stronger authentication',
            ],
            'sql_injection' => [
                'Block malicious requests',
                'Review input validation',
                'Enable WAF rules',
                'Audit database queries',
            ],
            'xss' => [
                'Sanitize user inputs',
                'Enable content security policy',
                'Block malicious scripts',
                'Review output encoding',
            ],
            'suspicious_behavior' => [
                'Monitor user activity',
                'Require additional verification',
                'Review access patterns',
                'Enable behavioral analytics',
            ],
        ];

        return $mitigations[$this->threatType] ?? [
            'Monitor threat closely',
            'Review security logs',
            'Implement additional controls',
            'Contact security team',
        ];
    }

    /**
     * Get threat intelligence data
     */
    public function getThreatIntelligence(): array
    {
        return [
            'threat_type' => $this->threatType,
            'severity' => $this->severity,
            'source_ip' => $this->sourceIp,
            'user_agent' => $this->userAgent,
            'detection_method' => $this->threatData['detection_method'] ?? 'automated',
            'confidence_score' => $this->threatData['confidence_score'] ?? 0.8,
            'threat_indicators' => $this->threatData['indicators'] ?? [],
            'attack_vector' => $this->threatData['attack_vector'] ?? 'unknown',
            'payload_analysis' => $this->threatData['payload_analysis'] ?? [],
            'geolocation' => $this->threatData['geolocation'] ?? [],
            'reputation_data' => $this->threatData['reputation_data'] ?? [],
        ];
    }

    /**
     * Check if threat is from known malicious source
     */
    public function isFromKnownMaliciousSource(): bool
    {
        $reputationData = $this->threatData['reputation_data'] ?? [];
        return ($reputationData['is_malicious'] ?? false) ||
               ($reputationData['reputation_score'] ?? 100) < 30;
    }

    /**
     * Get incident priority
     */
    public function getIncidentPriority(): string
    {
        if ($this->severity === 'critical') {
            return 'P1';
        } elseif ($this->severity === 'high') {
            return 'P2';
        } elseif ($this->severity === 'medium') {
            return 'P3';
        } else {
            return 'P4';
        }
    }
}
