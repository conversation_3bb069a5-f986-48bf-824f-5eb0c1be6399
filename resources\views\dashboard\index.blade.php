<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - حسابي AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="{{ asset('css/custom.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .dashboard-grid { display: grid; grid-template-columns: repeat(12, 1fr); gap: 1rem; }
        .widget { transition: all 0.3s ease; }
        .widget:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .widget-dragging { opacity: 0.5; transform: rotate(5deg); }
        .sidebar-collapsed { width: 80px; }
        .sidebar-expanded { width: 280px; }
        .command-mode { position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999; background: #000; }
        .notification-badge { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed right-0 top-0 h-full bg-white shadow-xl sidebar-expanded transition-all duration-300 z-40">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="/images/logo.png" alt="حسابي AI" class="h-10 w-10">
                    <div class="sidebar-text">
                        <h2 class="text-xl font-bold text-purple-600">حسابي AI</h2>
                        <p class="text-sm text-gray-500">{{ $user->name }}</p>
                    </div>
                </div>
                <button id="sidebar-toggle" class="text-gray-500 hover:text-purple-600">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="mt-6">
            <div class="px-6 mb-4">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider sidebar-text">القائمة الرئيسية</h3>
            </div>
            
            <a href="#" class="flex items-center px-6 py-3 text-purple-600 bg-purple-50 border-l-4 border-purple-600">
                <i class="fas fa-tachometer-alt w-5"></i>
                <span class="mr-3 sidebar-text">لوحة التحكم</span>
            </a>
            
            @if($userRole === 'general_manager')
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-chart-line w-5"></i>
                <span class="mr-3 sidebar-text">التحليلات الاستراتيجية</span>
            </a>
            @endif
            
            @if(in_array($userRole, ['general_manager', 'accountant']))
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-calculator w-5"></i>
                <span class="mr-3 sidebar-text">المحاسبة</span>
            </a>
            @endif
            
            @if(in_array($userRole, ['general_manager', 'project_manager']))
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-project-diagram w-5"></i>
                <span class="mr-3 sidebar-text">إدارة المشاريع</span>
            </a>
            @endif
            
            @if(in_array($userRole, ['general_manager', 'support_supervisor']))
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-headset w-5"></i>
                <span class="mr-3 sidebar-text">الدعم الفني</span>
            </a>
            @endif
            
            @if(in_array($userRole, ['general_manager', 'hr_manager']))
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-users w-5"></i>
                <span class="mr-3 sidebar-text">الموارد البشرية</span>
            </a>
            @endif

            <div class="px-6 mt-8 mb-4">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider sidebar-text">الأدوات</h3>
            </div>
            
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-cog w-5"></i>
                <span class="mr-3 sidebar-text">الإعدادات</span>
            </a>
            
            <a href="#" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-purple-600">
                <i class="fas fa-question-circle w-5"></i>
                <span class="mr-3 sidebar-text">المساعدة</span>
            </a>
        </nav>

        <!-- User Profile -->
        <div class="absolute bottom-0 w-full p-6 border-t border-gray-200">
            <div class="flex items-center">
                <img src="/images/avatars/default.png" alt="{{ $user->name }}" class="h-10 w-10 rounded-full">
                <div class="mr-3 sidebar-text">
                    <p class="text-sm font-medium text-gray-700">{{ $user->name }}</p>
                    <p class="text-xs text-gray-500">{{ $userRole }}</p>
                </div>
                <button class="mr-auto text-gray-400 hover:text-gray-600">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="mr-280 transition-all duration-300">
        <!-- Top Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Search Bar -->
                <div class="flex-1 max-w-2xl">
                    <div class="relative">
                        <input type="text" 
                               id="global-search" 
                               placeholder="البحث الشامل... (جرب: فواتير غير مدفوعة، مشروع ABC، العميل XYZ)"
                               class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <i class="fas fa-search absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <button id="ai-assistant-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-500 hover:text-purple-700">
                            <i class="fas fa-robot"></i>
                        </button>
                    </div>
                    
                    <!-- Search Results Dropdown -->
                    <div id="search-results" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 hidden z-50">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>

                <!-- Header Actions -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Language Switcher -->
                    <div class="relative">
                        <button id="language-btn" class="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-purple-600">
                            <i class="fas fa-globe"></i>
                            <span>العربية</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                    </div>

                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notifications-btn" class="relative text-gray-700 hover:text-purple-600">
                            <i class="fas fa-bell text-xl"></i>
                            @if($notifications['unread_count'] > 0)
                            <span class="absolute -top-2 -left-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center notification-badge">
                                {{ $notifications['unread_count'] }}
                            </span>
                            @endif
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="relative">
                        <button id="quick-actions-btn" class="text-gray-700 hover:text-purple-600">
                            <i class="fas fa-plus-circle text-xl"></i>
                        </button>
                    </div>

                    <!-- Command Mode -->
                    <button id="command-mode-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition duration-300">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>
                        <span>وضع القيادة</span>
                    </button>

                    <!-- Dashboard Customization -->
                    <button id="customize-dashboard-btn" class="text-gray-700 hover:text-purple-600">
                        <i class="fas fa-edit text-xl"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Today's Briefing -->
            <div id="daily-briefing" class="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold mb-2">مرحباً {{ $user->name }}، إليك موجز اليوم</h2>
                        <p class="opacity-90">{{ now()->format('l، j F Y') }}</p>
                    </div>
                    <button id="close-briefing" class="text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-tasks text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm opacity-90">المهام اليوم</p>
                                <p class="text-xl font-bold">{{ $tasks['pending_count'] }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-calendar text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm opacity-90">الاجتماعات</p>
                                <p class="text-xl font-bold">{{ count($upcomingEvents) }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm opacity-90">المهام المتأخرة</p>
                                <p class="text-xl font-bold">{{ $tasks['overdue_count'] }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-bell text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm opacity-90">إشعارات جديدة</p>
                                <p class="text-xl font-bold">{{ $notifications['unread_count'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Widgets Grid -->
            <div id="dashboard-grid" class="dashboard-grid">
                @foreach($layout->layout_data as $widget)
                    @include('dashboard.widgets.' . $widget['type'], [
                        'widget' => $widget,
                        'data' => $dashboardData
                    ])
                @endforeach
            </div>

            <!-- Add Widget Button -->
            <div class="fixed bottom-6 left-6 z-30">
                <button id="add-widget-btn" class="bg-purple-600 text-white p-4 rounded-full shadow-lg hover:bg-purple-700 transition duration-300">
                    <i class="fas fa-plus text-xl"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- Modals and Overlays -->
    @include('dashboard.modals.notifications')
    @include('dashboard.modals.quick-actions')
    @include('dashboard.modals.widget-selector')
    @include('dashboard.modals.ai-assistant')
    @include('dashboard.modals.command-mode')

    <!-- Scripts -->
    <script src="{{ asset('js/custom.js') }}"></script>
    <script src="{{ asset('js/dashboard.js') }}"></script>
    <script>
        // تمرير البيانات إلى JavaScript
        window.dashboardData = @json($dashboardData);
        window.userRole = @json($userRole);
        window.notifications = @json($notifications);
        window.tasks = @json($tasks);
        window.upcomingEvents = @json($upcomingEvents);
    </script>
</body>
</html>
