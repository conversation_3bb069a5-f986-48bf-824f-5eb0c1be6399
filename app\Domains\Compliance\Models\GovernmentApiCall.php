<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * نموذج سجلات استدعاءات API الحكومية
 * يتتبع جميع الاستدعاءات للأنظمة الحكومية
 */
class GovernmentApiCall extends Model
{
    use HasFactory;

    protected $fillable = [
        'government_integration_id',
        'operation',
        'request_data',
        'response_data',
        'status',
        'status_code',
        'error_message',
        'response_time',
        'started_at',
        'completed_at',
        'retry_count',
        'user_id',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * حالات الاستدعاء
     */
    const STATUSES = [
        'pending' => 'قيد الانتظار',
        'success' => 'نجح',
        'failed' => 'فشل',
        'timeout' => 'انتهت المهلة',
        'cancelled' => 'ملغي',
        'retrying' => 'إعادة محاولة',
    ];

    /**
     * العلاقة مع التكامل الحكومي
     */
    public function governmentIntegration(): BelongsTo
    {
        return $this->belongsTo(GovernmentIntegration::class);
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeByOperation($query, string $operation)
    {
        return $query->where('operation', $operation);
    }

    public function scopeSlowResponses($query, int $threshold = 5000)
    {
        return $query->where('response_time', '>', $threshold);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeLastWeek($query)
    {
        return $query->whereBetween('created_at', [now()->subWeek(), now()]);
    }

    /**
     * الحصول على معدل النجاح
     */
    public static function getSuccessRate(int $integrationId = null, int $days = 7): float
    {
        $query = self::where('created_at', '>=', now()->subDays($days));
        
        if ($integrationId) {
            $query->where('government_integration_id', $integrationId);
        }

        $total = $query->count();
        
        if ($total === 0) {
            return 100.0;
        }

        $successful = $query->where('status', 'success')->count();
        
        return round(($successful / $total) * 100, 2);
    }

    /**
     * الحصول على متوسط وقت الاستجابة
     */
    public static function getAverageResponseTime(int $integrationId = null, int $days = 7): float
    {
        $query = self::where('created_at', '>=', now()->subDays($days))
                    ->where('status', 'success')
                    ->whereNotNull('response_time');
        
        if ($integrationId) {
            $query->where('government_integration_id', $integrationId);
        }

        return round($query->avg('response_time') ?? 0, 2);
    }
}
