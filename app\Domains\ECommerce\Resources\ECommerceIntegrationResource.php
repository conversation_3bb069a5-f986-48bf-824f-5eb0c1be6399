<?php

namespace App\Domains\ECommerce\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد تكامل التجارة الإلكترونية
 */
class ECommerceIntegrationResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'last_sync_at' => $this->last_sync_at?->format('Y-m-d H:i:s'),
            'last_sync_status' => $this->last_sync_status,
            'sync_frequency' => $this->sync_config['sync_interval'] ?? null,
            'auto_sync_enabled' => $this->sync_config['auto_sync'] ?? false,
            
            // معلومات المنصة
            'platform' => $this->whenLoaded('platform', function () {
                return [
                    'id' => $this->platform->id,
                    'name' => $this->platform->name,
                    'slug' => $this->platform->slug,
                    'logo' => $this->platform->logo,
                    'type' => $this->platform->type,
                    'is_marketplace' => $this->platform->is_marketplace,
                ];
            }),
            
            // معلومات المتجر
            'store' => $this->whenLoaded('store', function () {
                return [
                    'id' => $this->store->id,
                    'name' => $this->store->name,
                    'domain' => $this->store->domain,
                    'currency' => $this->store->currency,
                    'timezone' => $this->store->timezone,
                ];
            }),
            
            // إعدادات المزامنة
            'sync_config' => [
                'auto_sync' => $this->sync_config['auto_sync'] ?? false,
                'sync_interval' => $this->sync_config['sync_interval'] ?? 60,
                'sync_products' => $this->sync_config['sync_products'] ?? true,
                'sync_orders' => $this->sync_config['sync_orders'] ?? true,
                'sync_customers' => $this->sync_config['sync_customers'] ?? true,
                'sync_categories' => $this->sync_config['sync_categories'] ?? true,
                'sync_inventory' => $this->sync_config['sync_inventory'] ?? true,
                'batch_size' => $this->sync_config['batch_size'] ?? 50,
            ],
            
            // إعدادات Webhooks
            'webhook_config' => [
                'enabled' => $this->webhook_config['enabled'] ?? false,
                'url' => $this->webhook_config['url'] ?? null,
                'events' => $this->webhook_config['events'] ?? [],
                'has_secret' => !empty($this->webhook_config['secret']),
            ],
            
            // إحصائيات المزامنة
            'sync_statistics' => $this->when($request->include_stats, function () {
                return [
                    'total_syncs' => $this->syncLogs()->count(),
                    'successful_syncs' => $this->syncLogs()->where('is_successful', true)->count(),
                    'failed_syncs' => $this->syncLogs()->where('is_successful', false)->count(),
                    'last_successful_sync' => $this->syncLogs()
                        ->where('is_successful', true)
                        ->latest()
                        ->first()?->created_at?->format('Y-m-d H:i:s'),
                    'products_synced' => $this->products()->count(),
                    'orders_synced' => $this->orders()->count(),
                    'customers_synced' => $this->customers()->count(),
                ];
            }),
            
            // سجلات المزامنة الأخيرة
            'recent_sync_logs' => $this->whenLoaded('syncLogs', function () {
                return $this->syncLogs->take(5)->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'entity_type' => $log->entity_type,
                        'operation' => $log->operation,
                        'is_successful' => $log->is_successful,
                        'records_processed' => $log->records_processed,
                        'records_successful' => $log->records_successful,
                        'records_failed' => $log->records_failed,
                        'duration' => $log->duration,
                        'error_message' => $log->error_message,
                        'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    ];
                });
            }),
            
            // معلومات الاتصال
            'connection_status' => $this->getConnectionStatus(),
            'api_limits' => $this->when($request->include_limits, function () {
                return $this->getApiLimits();
            }),
            
            // التواريخ
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // معلومات إضافية للمطورين
            'debug_info' => $this->when($request->debug && app()->environment('local'), function () {
                return [
                    'authentication_config_keys' => array_keys($this->authentication_config ?? []),
                    'platform_driver' => $this->platform->slug . '_driver',
                    'supported_operations' => $this->getSupportedOperations(),
                ];
            }),
        ];
    }

    /**
     * الحصول على تسمية الحالة
     */
    protected function getStatusLabel(): string
    {
        return match ($this->status) {
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'pending' => 'في الانتظار',
            'error' => 'خطأ',
            'syncing' => 'جاري المزامنة',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على حالة الاتصال
     */
    protected function getConnectionStatus(): array
    {
        // يمكن تحسين هذا ليتحقق فعلياً من الاتصال
        return [
            'is_connected' => $this->status === 'active',
            'last_check' => $this->updated_at->format('Y-m-d H:i:s'),
            'response_time' => null, // يمكن إضافة قياس زمن الاستجابة
        ];
    }

    /**
     * الحصول على حدود API
     */
    protected function getApiLimits(): array
    {
        // يمكن تحسين هذا ليجلب الحدود الفعلية من المنصة
        return [
            'requests_per_minute' => null,
            'requests_remaining' => null,
            'reset_time' => null,
        ];
    }

    /**
     * الحصول على العمليات المدعومة
     */
    protected function getSupportedOperations(): array
    {
        try {
            $driver = \App\Domains\ECommerce\Factories\ECommercePlatformDriverFactory::create($this->platform);
            return $driver->getSupportedOperations();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * معلومات إضافية مع المورد
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'generated_at' => now()->toISOString(),
            ],
        ];
    }
}
