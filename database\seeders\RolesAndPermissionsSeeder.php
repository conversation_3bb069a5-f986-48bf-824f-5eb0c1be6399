<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إعادة تعيين الأدوار والصلاحيات المخزنة مؤقتاً
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المحاسبة
            'accounting.view',
            'accounting.create',
            'accounting.edit',
            'accounting.delete',
            'accounting.approve',
            'accounting.reports',

            // صلاحيات الضرائب
            'taxation.view',
            'taxation.create',
            'taxation.edit',
            'taxation.delete',
            'taxation.calculate',
            'taxation.submit',

            // صلاحيات الموارد البشرية
            'hr.view',
            'hr.create',
            'hr.edit',
            'hr.delete',
            'hr.payroll',
            'hr.reports',

            // صلاحيات المشاريع
            'projects.view',
            'projects.create',
            'projects.edit',
            'projects.delete',
            'projects.assign',
            'projects.reports',

            // صلاحيات الدعم الفني
            'support.view',
            'support.create',
            'support.edit',
            'support.delete',
            'support.assign',
            'support.close',

            // صلاحيات التجارة الإلكترونية
            'ecommerce.view',
            'ecommerce.create',
            'ecommerce.edit',
            'ecommerce.delete',
            'ecommerce.sync',
            'ecommerce.reports',

            // صلاحيات البريد الإلكتروني
            'email.view',
            'email.send',
            'email.templates',
            'email.settings',

            // صلاحيات المدفوعات
            'payments.view',
            'payments.create',
            'payments.edit',
            'payments.delete',
            'payments.process',
            'payments.refund',

            // صلاحيات إدارة النظام
            'admin.users',
            'admin.roles',
            'admin.permissions',
            'admin.settings',
            'admin.backup',
            'admin.logs',

            // صلاحيات التقارير
            'reports.financial',
            'reports.tax',
            'reports.hr',
            'reports.projects',
            'reports.custom',
            'reports.export',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // إنشاء الأدوار
        $superAdminRole = Role::firstOrCreate(['name' => 'super-admin']);
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $accountantRole = Role::firstOrCreate(['name' => 'accountant']);
        $hrRole = Role::firstOrCreate(['name' => 'hr']);
        $projectManagerRole = Role::firstOrCreate(['name' => 'project-manager']);
        $supportRole = Role::firstOrCreate(['name' => 'support']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // تعيين الصلاحيات للأدوار
        $superAdminRole->givePermissionTo(Permission::all());

        $adminRole->givePermissionTo([
            'accounting.view', 'accounting.create', 'accounting.edit', 'accounting.reports',
            'taxation.view', 'taxation.create', 'taxation.edit', 'taxation.calculate',
            'hr.view', 'hr.create', 'hr.edit', 'hr.reports',
            'projects.view', 'projects.create', 'projects.edit', 'projects.assign',
            'support.view', 'support.create', 'support.edit', 'support.assign',
            'ecommerce.view', 'ecommerce.create', 'ecommerce.edit', 'ecommerce.sync',
            'email.view', 'email.send', 'email.templates',
            'payments.view', 'payments.create', 'payments.edit', 'payments.process',
            'reports.financial', 'reports.tax', 'reports.hr', 'reports.projects',
        ]);

        $managerRole->givePermissionTo([
            'accounting.view', 'accounting.reports',
            'taxation.view', 'taxation.calculate',
            'hr.view', 'hr.reports',
            'projects.view', 'projects.create', 'projects.edit', 'projects.assign',
            'support.view', 'support.assign',
            'ecommerce.view', 'ecommerce.reports',
            'payments.view',
            'reports.financial', 'reports.projects',
        ]);

        $accountantRole->givePermissionTo([
            'accounting.view', 'accounting.create', 'accounting.edit', 'accounting.reports',
            'taxation.view', 'taxation.create', 'taxation.edit', 'taxation.calculate', 'taxation.submit',
            'payments.view', 'payments.create', 'payments.edit',
            'reports.financial', 'reports.tax',
        ]);

        $hrRole->givePermissionTo([
            'hr.view', 'hr.create', 'hr.edit', 'hr.payroll', 'hr.reports',
            'reports.hr',
        ]);

        $projectManagerRole->givePermissionTo([
            'projects.view', 'projects.create', 'projects.edit', 'projects.assign', 'projects.reports',
            'support.view', 'support.create', 'support.edit',
            'reports.projects',
        ]);

        $supportRole->givePermissionTo([
            'support.view', 'support.create', 'support.edit', 'support.close',
            'email.view', 'email.send',
        ]);

        $userRole->givePermissionTo([
            'accounting.view',
            'projects.view',
            'support.view', 'support.create',
            'email.view',
        ]);

        // إنشاء مستخدم Super Admin
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
                'locale' => 'ar',
                'timezone' => 'Africa/Casablanca',
            ]
        );

        if (!$superAdmin->hasRole($superAdminRole)) {
            $superAdmin->assignRole($superAdminRole);
        }
    }
}
