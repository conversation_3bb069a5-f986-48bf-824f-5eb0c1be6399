<?php

namespace App\Domains\Accounting\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Update Payment Request
 */
class UpdatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $payment = $this->route('payment');
        return $this->user()->can('update', $payment);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $payment = $this->route('payment');
        
        // If payment is completed or cancelled, only allow limited updates
        if (in_array($payment->status, ['completed', 'cancelled', 'voided'])) {
            return [
                'notes' => 'nullable|string|max:1000',
                'description' => 'nullable|string|max:500',
            ];
        }

        return [
            'amount' => 'sometimes|required|numeric|min:0.01|max:*********.99',
            'currency' => 'sometimes|required|string|size:3|in:SAR,USD,EUR,GBP,AED',
            'payment_method' => [
                'sometimes',
                'required',
                'string',
                Rule::in(['cash', 'bank_transfer', 'check', 'credit_card', 'debit_card', 'online'])
            ],
            'payment_date' => 'sometimes|required|date|before_or_equal:today',
            'bank_account_id' => 'nullable|exists:bank_accounts,id',
            'reference' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'exchange_rate' => 'nullable|numeric|min:0.0001|max:9999.9999',
            'fees' => 'nullable|numeric|min:0|max:999999.99',
            
            // Check-specific fields
            'check_number' => 'required_if:payment_method,check|nullable|string|max:50',
            'check_date' => 'required_if:payment_method,check|nullable|date',
            'check_bank' => 'required_if:payment_method,check|nullable|string|max:100',
            
            // Card-specific fields
            'card_last_four' => 'required_if:payment_method,credit_card,debit_card|nullable|string|size:4',
            'card_type' => 'required_if:payment_method,credit_card,debit_card|nullable|string|max:20',
            
            // Online payment fields
            'transaction_id' => 'required_if:payment_method,online|nullable|string|max:100',
            'gateway' => 'required_if:payment_method,online|nullable|string|max:50',
            
            // Attachments
            'attachments' => 'nullable|array|max:5',
            'attachments.*' => 'file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'amount' => 'المبلغ',
            'currency' => 'العملة',
            'payment_method' => 'طريقة الدفع',
            'payment_date' => 'تاريخ الدفع',
            'bank_account_id' => 'الحساب البنكي',
            'reference' => 'المرجع',
            'description' => 'الوصف',
            'notes' => 'الملاحظات',
            'exchange_rate' => 'سعر الصرف',
            'fees' => 'الرسوم',
            'check_number' => 'رقم الشيك',
            'check_date' => 'تاريخ الشيك',
            'check_bank' => 'البنك المسحوب عليه',
            'card_last_four' => 'آخر أربعة أرقام من البطاقة',
            'card_type' => 'نوع البطاقة',
            'transaction_id' => 'رقم المعاملة',
            'gateway' => 'بوابة الدفع',
            'attachments' => 'المرفقات',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'المبلغ مطلوب',
            'amount.numeric' => 'المبلغ يجب أن يكون رقماً',
            'amount.min' => 'المبلغ يجب أن يكون أكبر من صفر',
            'amount.max' => 'المبلغ كبير جداً',
            'currency.required' => 'العملة مطلوبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'currency.in' => 'العملة غير مدعومة',
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'payment_method.in' => 'طريقة الدفع غير صحيحة',
            'payment_date.required' => 'تاريخ الدفع مطلوب',
            'payment_date.date' => 'تاريخ الدفع غير صحيح',
            'payment_date.before_or_equal' => 'تاريخ الدفع لا يمكن أن يكون في المستقبل',
            'bank_account_id.exists' => 'الحساب البنكي المحدد غير موجود',
            'exchange_rate.numeric' => 'سعر الصرف يجب أن يكون رقماً',
            'exchange_rate.min' => 'سعر الصرف يجب أن يكون أكبر من صفر',
            'fees.numeric' => 'الرسوم يجب أن تكون رقماً',
            'fees.min' => 'الرسوم لا يمكن أن تكون سالبة',
            'check_number.required_if' => 'رقم الشيك مطلوب عند اختيار الدفع بالشيك',
            'check_date.required_if' => 'تاريخ الشيك مطلوب عند اختيار الدفع بالشيك',
            'check_bank.required_if' => 'البنك المسحوب عليه مطلوب عند اختيار الدفع بالشيك',
            'card_last_four.required_if' => 'آخر أربعة أرقام من البطاقة مطلوبة',
            'card_last_four.size' => 'آخر أربعة أرقام من البطاقة يجب أن تكون 4 أرقام',
            'card_type.required_if' => 'نوع البطاقة مطلوب',
            'transaction_id.required_if' => 'رقم المعاملة مطلوب للدفع الإلكتروني',
            'gateway.required_if' => 'بوابة الدفع مطلوبة للدفع الإلكتروني',
            'attachments.array' => 'المرفقات يجب أن تكون مصفوفة',
            'attachments.max' => 'لا يمكن إرفاق أكثر من 5 ملفات',
            'attachments.*.file' => 'المرفق يجب أن يكون ملفاً',
            'attachments.*.mimes' => 'نوع الملف غير مدعوم (PDF, JPG, JPEG, PNG فقط)',
            'attachments.*.max' => 'حجم الملف يجب أن يكون أقل من 5 ميجابايت',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Calculate net amount if fees are provided
        if ($this->has('amount') && $this->has('fees')) {
            $this->merge([
                'net_amount' => $this->amount - ($this->fees ?? 0)
            ]);
        }

        // Set default exchange rate for base currency
        if ($this->has('currency') && !$this->has('exchange_rate') && 
            $this->currency === config('app.base_currency', 'SAR')) {
            $this->merge([
                'exchange_rate' => 1.0000
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $payment = $this->route('payment');
            
            // Prevent updates to completed/cancelled payments except notes and description
            if (in_array($payment->status, ['completed', 'cancelled', 'voided'])) {
                $restrictedFields = ['amount', 'currency', 'payment_method', 'payment_date', 'bank_account_id'];
                foreach ($restrictedFields as $field) {
                    if ($this->has($field)) {
                        $validator->errors()->add($field, 'لا يمكن تعديل هذا الحقل للدفعات المكتملة أو الملغاة');
                    }
                }
            }

            // Validate bank account is required for bank transfers
            if ($this->payment_method === 'bank_transfer' && !$this->bank_account_id) {
                $validator->errors()->add('bank_account_id', 'الحساب البنكي مطلوب للتحويل البنكي');
            }

            // Validate amount doesn't exceed payable amount
            if ($this->has('amount') && $payment->payable) {
                $currentPaidAmount = $payment->payable->payments()
                    ->where('id', '!=', $payment->id)
                    ->where('status', 'completed')
                    ->sum('amount');
                
                $totalAmount = $payment->payable->total_amount ?? $payment->payable->amount ?? 0;
                $remainingAmount = $totalAmount - $currentPaidAmount;
                
                if ($this->amount > $remainingAmount) {
                    $validator->errors()->add('amount', 'المبلغ يتجاوز المبلغ المستحق المتبقي');
                }
            }
        });
    }
}
