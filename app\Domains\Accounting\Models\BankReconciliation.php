<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج تسوية البنك
 */
class BankReconciliation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'bank_account_id',
        'reconciliation_date',
        'statement_date',
        'opening_balance',
        'closing_balance',
        'book_balance',
        'adjusted_balance',
        'total_deposits',
        'total_withdrawals',
        'outstanding_deposits',
        'outstanding_checks',
        'bank_charges',
        'interest_earned',
        'status',
        'notes',
        'prepared_by',
        'reviewed_by',
        'approved_by',
        'prepared_at',
        'reviewed_at',
        'approved_at',
    ];

    protected $casts = [
        'reconciliation_date' => 'date',
        'statement_date' => 'date',
        'opening_balance' => 'decimal:2',
        'closing_balance' => 'decimal:2',
        'book_balance' => 'decimal:2',
        'adjusted_balance' => 'decimal:2',
        'total_deposits' => 'decimal:2',
        'total_withdrawals' => 'decimal:2',
        'outstanding_deposits' => 'decimal:2',
        'outstanding_checks' => 'decimal:2',
        'bank_charges' => 'decimal:2',
        'interest_earned' => 'decimal:2',
        'prepared_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * حالات التسوية
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'IN_PROGRESS' => 'قيد التنفيذ',
        'PENDING_REVIEW' => 'في انتظار المراجعة',
        'REVIEWED' => 'تمت المراجعة',
        'APPROVED' => 'معتمدة',
        'REJECTED' => 'مرفوضة',
        'COMPLETED' => 'مكتملة',
    ];

    /**
     * الحساب البنكي
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * من أعد التسوية
     */
    public function preparedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'prepared_by');
    }

    /**
     * من راجع التسوية
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'reviewed_by');
    }

    /**
     * من اعتمد التسوية
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * بنود التسوية
     */
    public function reconciliationItems(): HasMany
    {
        return $this->hasMany(BankReconciliationItem::class);
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * حساب الفرق
     */
    public function getDifferenceAttribute(): float
    {
        return $this->closing_balance - $this->adjusted_balance;
    }

    /**
     * فحص إذا كانت التسوية متوازنة
     */
    public function isBalanced(): bool
    {
        return abs($this->getDifferenceAttribute()) < 0.01;
    }

    /**
     * فحص إذا كانت التسوية مكتملة
     */
    public function isCompleted(): bool
    {
        return $this->status === 'COMPLETED';
    }

    /**
     * فحص إذا كانت التسوية معتمدة
     */
    public function isApproved(): bool
    {
        return $this->status === 'APPROVED';
    }

    /**
     * حساب الرصيد المعدل
     */
    public function calculateAdjustedBalance(): float
    {
        $adjustedBalance = $this->book_balance;

        // إضافة الودائع المعلقة
        $adjustedBalance += $this->outstanding_deposits;

        // طرح الشيكات المعلقة
        $adjustedBalance -= $this->outstanding_checks;

        // طرح رسوم البنك
        $adjustedBalance -= $this->bank_charges;

        // إضافة الفوائد المكتسبة
        $adjustedBalance += $this->interest_earned;

        return $adjustedBalance;
    }

    /**
     * تحديث الرصيد المعدل
     */
    public function updateAdjustedBalance(): void
    {
        $this->update([
            'adjusted_balance' => $this->calculateAdjustedBalance()
        ]);
    }

    /**
     * بدء التسوية
     */
    public function start(int $preparedBy): void
    {
        $this->update([
            'status' => 'IN_PROGRESS',
            'prepared_by' => $preparedBy,
            'prepared_at' => now(),
        ]);
    }

    /**
     * إرسال للمراجعة
     */
    public function submitForReview(): void
    {
        if (!$this->isBalanced()) {
            throw new \Exception('لا يمكن إرسال تسوية غير متوازنة للمراجعة');
        }

        $this->update([
            'status' => 'PENDING_REVIEW',
        ]);
    }

    /**
     * مراجعة التسوية
     */
    public function review(int $reviewedBy, bool $approved = true): void
    {
        $this->update([
            'reviewed_by' => $reviewedBy,
            'reviewed_at' => now(),
            'status' => $approved ? 'REVIEWED' : 'REJECTED',
        ]);
    }

    /**
     * اعتماد التسوية
     */
    public function approve(int $approvedBy): void
    {
        if ($this->status !== 'REVIEWED') {
            throw new \Exception('يجب مراجعة التسوية قبل الاعتماد');
        }

        $this->update([
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'status' => 'APPROVED',
        ]);
    }

    /**
     * إكمال التسوية
     */
    public function complete(): void
    {
        if ($this->status !== 'APPROVED') {
            throw new \Exception('يجب اعتماد التسوية قبل الإكمال');
        }

        if (!$this->isBalanced()) {
            throw new \Exception('لا يمكن إكمال تسوية غير متوازنة');
        }

        $this->update([
            'status' => 'COMPLETED',
        ]);
    }

    /**
     * رفض التسوية
     */
    public function reject(string $reason): void
    {
        $this->update([
            'status' => 'REJECTED',
            'notes' => "{$this->notes}\nسبب الرفض: {$reason}",
        ]);
    }

    /**
     * Scope للتسويات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'COMPLETED');
    }

    /**
     * Scope للتسويات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * Scope للتسويات في فترة معينة
     */
    public function scopeInPeriod($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('reconciliation_date', [$startDate, $endDate]);
    }

    /**
     * Scope للتسويات غير المتوازنة
     */
    public function scopeUnbalanced($query)
    {
        return $query->whereRaw('ABS(closing_balance - adjusted_balance) >= 0.01');
    }
}
