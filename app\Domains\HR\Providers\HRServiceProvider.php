<?php

namespace App\Domains\HR\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Domains\HR\Services\AdvancedHRManagementService;
use App\Domains\HR\Services\SmartPayrollService;
use App\Domains\HR\Services\AttendanceTrackingService;
use App\Domains\HR\Services\PerformanceManagementService;
use App\Domains\HR\Services\RecruitmentService;
use App\Domains\HR\Services\TrainingDevelopmentService;
use App\Domains\HR\Services\EmployeeAnalyticsService;
use App\Domains\HR\Services\HRComplianceService;

/**
 * HR Domain Service Provider
 * مزود خدمات مجال الموارد البشرية
 */
class HRServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل الخدمات الأساسية
        $this->app->singleton(AdvancedHRManagementService::class);
        $this->app->singleton(SmartPayrollService::class);
        $this->app->singleton(AttendanceTrackingService::class);
        $this->app->singleton(PerformanceManagementService::class);
        $this->app->singleton(RecruitmentService::class);
        $this->app->singleton(TrainingDevelopmentService::class);
        $this->app->singleton(EmployeeAnalyticsService::class);
        $this->app->singleton(HRComplianceService::class);

        // تسجيل التكوينات
        $this->mergeConfigFrom(
            config_path('hr.php'),
            'hr'
        );

        // تسجيل الواجهات والتنفيذات
        $this->registerContracts();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل المسارات
        $this->loadRoutes();

        // تحميل الـ Views
        $this->loadViews();

        // تحميل الـ Migrations
        $this->loadMigrations();

        // تحميل الترجمات
        $this->loadTranslations();

        // تسجيل السياسات
        $this->registerPolicies();

        // تسجيل نظام إدارة الموارد البشرية الذكي
        $this->registerIntelligentHRSystem();

        // تسجيل نظام الذكاء الاصطناعي للموارد البشرية
        $this->registerHRAIServices();

        // تسجيل نظام إدارة الأداء المتقدم
        $this->registerAdvancedPerformanceManagement();

        // تسجيل خدمات التحليل التنبؤي للموارد البشرية
        $this->registerPredictiveHRAnalytics();

        // نشر الموارد المتقدمة للموارد البشرية
        $this->publishAdvancedHRAssets();
    }

    /**
     * تسجيل الواجهات والتنفيذات
     */
    protected function registerContracts(): void
    {
        $this->app->bind(
            \App\Domains\HR\Contracts\EmployeeRepositoryInterface::class,
            \App\Domains\HR\Repositories\EmployeeRepository::class
        );

        $this->app->bind(
            \App\Domains\HR\Contracts\PayrollRepositoryInterface::class,
            \App\Domains\HR\Repositories\PayrollRepository::class
        );

        $this->app->bind(
            \App\Domains\HR\Contracts\AttendanceRepositoryInterface::class,
            \App\Domains\HR\Repositories\AttendanceRepository::class
        );

        $this->app->bind(
            \App\Domains\HR\Contracts\HRServiceInterface::class,
            AdvancedHRManagementService::class
        );
    }

    /**
     * تحميل المسارات
     */
    protected function loadRoutes(): void
    {
        if ($this->app->routesAreCached()) {
            return;
        }

        // API Routes
        Route::prefix('api/hr')
            ->middleware(['api', 'auth:sanctum'])
            ->namespace('App\Domains\HR\Controllers')
            ->group(__DIR__ . '/../Routes/api.php');

        // Web Routes
        Route::prefix('hr')
            ->middleware(['web', 'auth'])
            ->namespace('App\Domains\HR\Controllers')
            ->group(__DIR__ . '/../Routes/web.php');
    }

    /**
     * تحميل الـ Views
     */
    protected function loadViews(): void
    {
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'hr');
    }

    /**
     * تحميل الـ Migrations
     */
    protected function loadMigrations(): void
    {
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations/hr');
    }

    /**
     * تحميل الترجمات
     */
    protected function loadTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'hr');
    }

    /**
     * تسجيل السياسات
     */
    protected function registerPolicies(): void
    {
        $policies = [
            \App\Domains\HR\Models\Employee::class => \App\Domains\HR\Policies\EmployeePolicy::class,
            \App\Domains\HR\Models\Payroll::class => \App\Domains\HR\Policies\PayrollPolicy::class,
            \App\Domains\HR\Models\Attendance::class => \App\Domains\HR\Policies\AttendancePolicy::class,
            \App\Domains\HR\Models\Leave::class => \App\Domains\HR\Policies\LeavePolicy::class,
        ];

        foreach ($policies as $model => $policy) {
            \Illuminate\Support\Facades\Gate::policy($model, $policy);
        }
    }

    /**
     * تسجيل الأحداث والمستمعين
     */
    protected function registerEvents(): void
    {
        $events = [
            \App\Domains\HR\Events\EmployeeHired::class => [
                \App\Domains\HR\Listeners\CreateEmployeeProfile::class,
                \App\Domains\HR\Listeners\SetupPayrollRecord::class,
                \App\Domains\HR\Listeners\SendWelcomeEmail::class,
            ],
            \App\Domains\HR\Events\PayrollProcessed::class => [
                \App\Domains\HR\Listeners\GeneratePayslip::class,
                \App\Domains\HR\Listeners\UpdateAccountingRecords::class,
            ],
            \App\Domains\HR\Events\AttendanceMarked::class => [
                \App\Domains\HR\Listeners\CalculateWorkingHours::class,
                \App\Domains\HR\Listeners\CheckOvertimeRules::class,
            ],
        ];

        foreach ($events as $event => $listeners) {
            foreach ($listeners as $listener) {
                \Illuminate\Support\Facades\Event::listen($event, $listener);
            }
        }
    }



    /**
     * تسجيل نظام إدارة الموارد البشرية الذكي
     */
    protected function registerIntelligentHRSystem(): void
    {
        // تسجيل محرك إدارة الموارد البشرية الذكي
        $this->app->singleton('hr.intelligent_engine', function ($app) {
            return new \App\Domains\HR\Services\Intelligence\IntelligentHREngine(
                $app['db'],
                $app['cache.store'],
                $app['hr.ai_processor']
            );
        });

        // تسجيل خدمة إدارة المواهب المتقدمة
        $this->app->singleton('hr.talent_management', function ($app) {
            return new \App\Domains\HR\Services\TalentManagement\AdvancedTalentManagementService(
                $app['hr.intelligent_engine'],
                $app['hr.performance_analyzer'],
                $app['hr.skill_matcher']
            );
        });

        // تسجيل خدمة مطابقة المهارات الذكية
        $this->app->singleton('hr.skill_matcher', function ($app) {
            return new \App\Domains\HR\Services\Skills\IntelligentSkillMatcher(
                $app['hr.ml_model'],
                $app['cache.store']
            );
        });
    }

    /**
     * تسجيل نظام الذكاء الاصطناعي للموارد البشرية
     */
    protected function registerHRAIServices(): void
    {
        // تسجيل معالج الذكاء الاصطناعي للموارد البشرية
        $this->app->singleton('hr.ai_processor', function ($app) {
            return new \App\Domains\HR\Services\AI\HRAIProcessor(
                config('hr.ai_models'),
                $app['log']
            );
        });

        // تسجيل نموذج التعلم الآلي للموارد البشرية
        $this->app->singleton('hr.ml_model', function ($app) {
            return new \App\Domains\HR\Services\MachineLearning\HRPredictionModel(
                config('hr.ml_model_path'),
                $app['hr.ai_processor']
            );
        });

        // تسجيل خدمة التوصيات الذكية
        $this->app->singleton('hr.recommendation_engine', function ($app) {
            return new \App\Domains\HR\Services\Recommendations\IntelligentRecommendationEngine(
                $app['hr.ml_model'],
                $app['hr.behavior_analyzer'],
                $app['cache.store']
            );
        });

        // تسجيل محلل السلوك الوظيفي
        $this->app->singleton('hr.behavior_analyzer', function ($app) {
            return new \App\Domains\HR\Services\Analytics\EmployeeBehaviorAnalyzer(
                $app['db'],
                $app['hr.ai_processor']
            );
        });
    }

    /**
     * تسجيل نظام إدارة الأداء المتقدم
     */
    protected function registerAdvancedPerformanceManagement(): void
    {
        // تسجيل محلل الأداء المتقدم
        $this->app->singleton('hr.performance_analyzer', function ($app) {
            return new \App\Domains\HR\Services\Performance\AdvancedPerformanceAnalyzer(
                $app['db'],
                $app['hr.ai_processor'],
                $app['hr.goal_tracker']
            );
        });

        // تسجيل متتبع الأهداف الذكي
        $this->app->singleton('hr.goal_tracker', function ($app) {
            return new \App\Domains\HR\Services\Goals\IntelligentGoalTracker(
                $app['db'],
                $app['hr.ml_model'],
                $app['events']
            );
        });

        // تسجيل خدمة التقييم 360 درجة
        $this->app->singleton('hr.360_evaluation', function ($app) {
            return new \App\Domains\HR\Services\Evaluation\Advanced360EvaluationService(
                $app['hr.performance_analyzer'],
                $app['hr.feedback_processor'],
                $app['queue']
            );
        });
    }

    /**
     * تسجيل خدمات التحليل التنبؤي للموارد البشرية
     */
    protected function registerPredictiveHRAnalytics(): void
    {
        // تسجيل محرك التحليل التنبؤي
        $this->app->singleton('hr.predictive_engine', function ($app) {
            return new \App\Domains\HR\Services\Predictive\PredictiveHREngine(
                $app['hr.ml_model'],
                $app['hr.data_processor'],
                $app['cache.store']
            );
        });

        // تسجيل معالج البيانات المتقدم
        $this->app->singleton('hr.data_processor', function ($app) {
            return new \App\Domains\HR\Services\DataProcessing\AdvancedHRDataProcessor(
                $app['db'],
                $app['hr.ai_processor']
            );
        });

        // تسجيل خدمة التنبؤ بدوران الموظفين
        $this->app->singleton('hr.turnover_predictor', function ($app) {
            return new \App\Domains\HR\Services\Predictive\EmployeeTurnoverPredictor(
                $app['hr.predictive_engine'],
                $app['hr.behavior_analyzer']
            );
        });
    }

    /**
     * نشر الموارد المتقدمة للموارد البشرية
     */
    protected function publishAdvancedHRAssets(): void
    {
        if ($this->app->runningInConsole()) {
            // نشر ملف التكوين
            $this->publishes([
                __DIR__ . '/../../../config/hr.php' => config_path('hr.php'),
            ], 'hr-config');

            // نشر الـ Views
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/hr'),
            ], 'hr-views');

            // نشر الترجمات
            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/hr'),
            ], 'hr-lang');

            // نشر الأصول
            $this->publishes([
                __DIR__ . '/../Resources/assets' => public_path('vendor/hr'),
            ], 'hr-assets');
        }
    }

    /**
     * الحصول على الخدمات المتقدمة للموارد البشرية
     */
    public function provides(): array
    {
        return [
            // Intelligent HR Core Services
            'hr.intelligent_engine',
            'hr.talent_management',
            'hr.skill_matcher',

            // AI & Machine Learning Services
            'hr.ai_processor',
            'hr.ml_model',
            'hr.recommendation_engine',
            'hr.behavior_analyzer',

            // Performance Management
            'hr.performance_analyzer',
            'hr.goal_tracker',
            'hr.360_evaluation',

            // Predictive Analytics
            'hr.predictive_engine',
            'hr.data_processor',
            'hr.turnover_predictor',
        ];
    }
}
