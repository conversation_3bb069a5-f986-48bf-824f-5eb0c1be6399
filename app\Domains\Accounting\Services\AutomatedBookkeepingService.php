<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\Account;

use App\Domains\Accounting\Models\BankTransaction;
use App\Domains\Accounting\Models\Invoice;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة المحاسبة التلقائية
 * تدعم الربط المباشر مع البنوك والمطابقة التلقائية
 */
class AutomatedBookkeepingService
{
    protected DynamicAccountingEngine $accountingEngine;
    protected array $matchingRules;

    public function __construct(DynamicAccountingEngine $accountingEngine)
    {
        $this->accountingEngine = $accountingEngine;
        $this->loadMatchingRules();
    }

    /**
     * تحميل قواعد المطابقة
     */
    protected function loadMatchingRules(): void
    {
        $this->matchingRules = [
            'invoice_payment' => [
                'pattern' => '/INV[-_](\d+)|فاتورة\s*(\d+)/i',
                'account_mapping' => [
                    'debit' => 'cash_bank',
                    'credit' => 'accounts_receivable',
                ],
            ],
            'salary_payment' => [
                'pattern' => '/راتب|salary|payroll/i',
                'account_mapping' => [
                    'debit' => 'salaries_expense',
                    'credit' => 'cash_bank',
                ],
            ],
            'utility_payment' => [
                'pattern' => '/كهرباء|ماء|هاتف|electricity|water|phone/i',
                'account_mapping' => [
                    'debit' => 'utilities_expense',
                    'credit' => 'cash_bank',
                ],
            ],
            'rent_payment' => [
                'pattern' => '/إيجار|rent/i',
                'account_mapping' => [
                    'debit' => 'rent_expense',
                    'credit' => 'cash_bank',
                ],
            ],
        ];
    }

    /**
     * معالجة المعاملات البنكية
     */
    public function processBankTransactions(array $transactions): array
    {
        $results = [
            'processed' => 0,
            'matched' => 0,
            'unmatched' => 0,
            'errors' => [],
        ];

        DB::beginTransaction();
        try {
            foreach ($transactions as $transactionData) {
                $result = $this->processSingleTransaction($transactionData);

                if ($result['status'] === 'processed') {
                    $results['processed']++;
                    if ($result['matched']) {
                        $results['matched']++;
                    } else {
                        $results['unmatched']++;
                    }
                } else {
                    $results['errors'][] = $result['error'];
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * معالجة معاملة واحدة
     */
    protected function processSingleTransaction(array $transactionData): array
    {
        try {
            // إنشاء سجل المعاملة البنكية
            $bankTransaction = BankTransaction::create([
                'bank_account_id' => $transactionData['bank_account_id'],
                'transaction_date' => $transactionData['date'],
                'description' => $transactionData['description'],
                'reference' => $transactionData['reference'] ?? null,
                'amount' => $transactionData['amount'],
                'type' => $transactionData['amount'] > 0 ? 'CREDIT' : 'DEBIT',
                'balance' => $transactionData['balance'] ?? null,
                'status' => 'PENDING',
                'metadata' => $transactionData['metadata'] ?? [],
            ]);

            // محاولة المطابقة التلقائية
            $matchResult = $this->attemptAutoMatch($bankTransaction);

            if ($matchResult['matched']) {
                $bankTransaction->update([
                    'status' => 'MATCHED',
                    'matched_type' => $matchResult['type'],
                    'matched_id' => $matchResult['id'],
                ]);

                // إنشاء القيد المحاسبي
                $this->createJournalEntryFromMatch($bankTransaction, $matchResult);
            } else {
                // تصنيف ذكي للمعاملة
                $classification = $this->classifyTransaction($bankTransaction);

                $bankTransaction->update([
                    'status' => 'CLASSIFIED',
                    'suggested_account_id' => $classification['account_id'],
                    'confidence_score' => $classification['confidence'],
                ]);

                // إنشاء قيد مقترح
                $this->createSuggestedJournalEntry($bankTransaction, $classification);
            }

            return [
                'status' => 'processed',
                'matched' => $matchResult['matched'],
                'transaction_id' => $bankTransaction->id,
            ];

        } catch (\Exception $e) {
            Log::error('خطأ في معالجة المعاملة البنكية', [
                'transaction' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * محاولة المطابقة التلقائية
     */
    protected function attemptAutoMatch(BankTransaction $transaction): array
    {
        // مطابقة مع الفواتير
        $invoiceMatch = $this->matchWithInvoices($transaction);
        if ($invoiceMatch) {
            return [
                'matched' => true,
                'type' => 'invoice',
                'id' => $invoiceMatch->id,
                'data' => $invoiceMatch,
            ];
        }

        // مطابقة مع المدفوعات المتوقعة
        $paymentMatch = $this->matchWithExpectedPayments($transaction);
        if ($paymentMatch) {
            return [
                'matched' => true,
                'type' => 'payment',
                'id' => $paymentMatch->id,
                'data' => $paymentMatch,
            ];
        }

        return ['matched' => false];
    }

    /**
     * مطابقة مع الفواتير
     */
    protected function matchWithInvoices(BankTransaction $transaction): ?Invoice
    {
        $description = $transaction->description;
        $amount = abs($transaction->amount);

        // البحث عن رقم الفاتورة في الوصف
        if (preg_match('/INV[-_](\d+)|فاتورة\s*(\d+)/i', $description, $matches)) {
            $invoiceNumber = $matches[1] ?? $matches[2];

            $invoice = Invoice::where('invoice_number', 'LIKE', "%{$invoiceNumber}%")
                ->where('payment_status', '!=', 'PAID')
                ->first();

            if ($invoice && abs($invoice->balance_amount - $amount) < 0.01) {
                return $invoice;
            }
        }

        // البحث بالمبلغ والتاريخ
        $invoices = Invoice::where('payment_status', '!=', 'PAID')
            ->whereBetween('due_date', [
                $transaction->transaction_date->subDays(30),
                $transaction->transaction_date->addDays(30)
            ])
            ->get();

        foreach ($invoices as $invoice) {
            if (abs($invoice->balance_amount - $amount) < 0.01) {
                return $invoice;
            }
        }

        return null;
    }

    /**
     * مطابقة مع المدفوعات المتوقعة
     */
    protected function matchWithExpectedPayments(BankTransaction $transaction): ?object
    {
        // يمكن تطوير هذه الدالة لمطابقة المدفوعات المتوقعة
        // مثل الرواتب، الإيجارات، المرافق، إلخ
        return null;
    }

    /**
     * تصنيف ذكي للمعاملة
     */
    protected function classifyTransaction(BankTransaction $transaction): array
    {
        $description = strtolower($transaction->description);
        $amount = $transaction->amount;
        $bestMatch = null;
        $highestConfidence = 0;

        foreach ($this->matchingRules as $ruleType => $rule) {
            if (preg_match($rule['pattern'], $description)) {
                $confidence = $this->calculateConfidence($transaction, $rule);

                if ($confidence > $highestConfidence) {
                    $highestConfidence = $confidence;
                    $bestMatch = [
                        'type' => $ruleType,
                        'rule' => $rule,
                        'confidence' => $confidence,
                    ];
                }
            }
        }

        if ($bestMatch) {
            $accountType = $amount > 0 ? $bestMatch['rule']['account_mapping']['debit'] : $bestMatch['rule']['account_mapping']['credit'];
            $account = $this->getAccountByType($accountType);

            return [
                'account_id' => $account?->id,
                'confidence' => $bestMatch['confidence'],
                'rule_type' => $bestMatch['type'],
            ];
        }

        // تصنيف افتراضي
        $defaultAccount = $this->getDefaultAccount($amount > 0 ? 'income' : 'expense');

        return [
            'account_id' => $defaultAccount?->id,
            'confidence' => 0.3,
            'rule_type' => 'default',
        ];
    }

    /**
     * حساب مستوى الثقة
     */
    protected function calculateConfidence(BankTransaction $transaction, array $rule): float
    {
        $confidence = 0.5; // ثقة أساسية

        // زيادة الثقة بناءً على عوامل مختلفة
        $description = strtolower($transaction->description);

        // وجود كلمات مفتاحية متعددة
        $keywords = explode('|', str_replace(['/', '(', ')'], '', $rule['pattern']));
        $matchedKeywords = 0;

        foreach ($keywords as $keyword) {
            if (strpos($description, strtolower($keyword)) !== false) {
                $matchedKeywords++;
            }
        }

        $confidence += ($matchedKeywords / count($keywords)) * 0.3;

        // التكرار التاريخي
        $historicalMatches = BankTransaction::where('description', 'LIKE', "%{$transaction->description}%")
            ->where('status', 'MATCHED')
            ->count();

        if ($historicalMatches > 0) {
            $confidence += min($historicalMatches * 0.1, 0.2);
        }

        return min($confidence, 1.0);
    }

    /**
     * الحصول على حساب حسب النوع
     */
    protected function getAccountByType(string $type): ?Account
    {
        $accountMappings = [
            'cash_bank' => ['1110', '1120'], // حسابات البنك والنقدية
            'accounts_receivable' => ['1210'], // حسابات مدينة
            'salaries_expense' => ['6110'], // مصروف رواتب
            'utilities_expense' => ['6210'], // مصروف مرافق
            'rent_expense' => ['6220'], // مصروف إيجار
        ];

        $codes = $accountMappings[$type] ?? [];

        foreach ($codes as $code) {
            $account = Account::where('code', $code)->first();
            if ($account) {
                return $account;
            }
        }

        return null;
    }

    /**
     * الحصول على حساب افتراضي
     */
    protected function getDefaultAccount(string $type): ?Account
    {
        return match ($type) {
            'income' => Account::where('account_type', 'REVENUE')->first(),
            'expense' => Account::where('account_type', 'EXPENSE')->first(),
            default => null,
        };
    }

    /**
     * إنشاء قيد محاسبي من المطابقة
     */
    protected function createJournalEntryFromMatch(BankTransaction $transaction, array $matchResult): void
    {
        if ($matchResult['type'] === 'invoice') {
            $this->createInvoicePaymentEntry($transaction, $matchResult['data']);
        }
    }

    /**
     * إنشاء قيد دفع فاتورة
     */
    protected function createInvoicePaymentEntry(BankTransaction $transaction, Invoice $invoice): void
    {
        $amount = abs($transaction->amount);

        $entryData = [
            'description' => "دفع فاتورة رقم {$invoice->invoice_number}",
            'entry_date' => $transaction->transaction_date,
            'currency' => 'MAD',
            'source_type' => 'BANK_TRANSACTION',
            'source_id' => $transaction->id,
            'lines' => [
                [
                    'account_code' => '1110', // البنك
                    'debit_amount' => $amount,
                    'credit_amount' => 0,
                ],
                [
                    'account_code' => '1210', // حسابات مدينة
                    'debit_amount' => 0,
                    'credit_amount' => $amount,
                ],
            ],
        ];

        $this->accountingEngine->createJournalEntry($entryData);

        // تحديث حالة الفاتورة
        $invoice->updatePaymentStatus();
    }

    /**
     * إنشاء قيد مقترح
     */
    protected function createSuggestedJournalEntry(BankTransaction $transaction, array $classification): void
    {
        if (!$classification['account_id']) {
            return;
        }

        $account = Account::find($classification['account_id']);
        $amount = abs($transaction->amount);
        $isCredit = $transaction->amount > 0;

        $entryData = [
            'description' => "معاملة بنكية: {$transaction->description}",
            'entry_date' => $transaction->transaction_date,
            'currency' => 'MAD',
            'source_type' => 'BANK_TRANSACTION',
            'source_id' => $transaction->id,
            'lines' => [
                [
                    'account_code' => '1110', // البنك
                    'debit_amount' => $isCredit ? $amount : 0,
                    'credit_amount' => $isCredit ? 0 : $amount,
                ],
                [
                    'account_code' => $account->code,
                    'debit_amount' => $isCredit ? 0 : $amount,
                    'credit_amount' => $isCredit ? $amount : 0,
                ],
            ],
            'metadata' => [
                'suggested' => true,
                'confidence' => $classification['confidence'],
                'rule_type' => $classification['rule_type'],
            ],
        ];

        $this->accountingEngine->createJournalEntry($entryData);
    }

    /**
     * استيراد معاملات من ملف CSV
     */
    public function importFromCSV(string $filePath, array $mapping): array
    {
        $transactions = [];
        $handle = fopen($filePath, 'r');

        if (!$handle) {
            throw new \Exception('لا يمكن فتح الملف');
        }

        $header = fgetcsv($handle);

        while (($row = fgetcsv($handle)) !== false) {
            $transaction = [];

            foreach ($mapping as $field => $columnIndex) {
                $transaction[$field] = $row[$columnIndex] ?? null;
            }

            // تحويل التاريخ
            if (isset($transaction['date'])) {
                $transaction['date'] = \Carbon\Carbon::parse($transaction['date']);
            }

            // تحويل المبلغ
            if (isset($transaction['amount'])) {
                $transaction['amount'] = (float) str_replace([',', ' '], '', $transaction['amount']);
            }

            $transactions[] = $transaction;
        }

        fclose($handle);

        return $this->processBankTransactions($transactions);
    }
}
