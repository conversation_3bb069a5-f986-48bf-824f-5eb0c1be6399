<?php

namespace App\Domains\Taxation\Listeners;

use App\Domains\Taxation\Events\TaxReturnCreated;
use App\Domains\Taxation\Notifications\TaxReturnCreatedNotification;
use App\Domains\Shared\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

/**
 * مستمع إرسال إشعار إنشاء الإقرار الضريبي
 * يرسل إشعارات متعددة القنوات عند إنشاء إقرار ضريبي جديد
 */
class SendTaxReturnCreatedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * إنشاء مثيل جديد من المستمع
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * معالجة الحدث
     */
    public function handle(TaxReturnCreated $event): void
    {
        try {
            $taxReturn = $event->taxReturn;
            $metadata = $event->metadata;

            // تحديد المستلمين
            $recipients = $this->getRecipients($taxReturn);

            // إرسال الإشعارات
            $this->sendNotifications($taxReturn, $recipients, $metadata);

            // تسجيل النشاط
            $this->logActivity($taxReturn, $recipients, $metadata);

            // إرسال إشعارات خاصة حسب الحالة
            $this->sendConditionalNotifications($taxReturn, $metadata);

        } catch (\Exception $e) {
            Log::error('فشل في إرسال إشعار إنشاء الإقرار الضريبي', [
                'tax_return_id' => $event->taxReturn->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // إعادة المحاولة في حالة الفشل
            $this->release(60); // إعادة المحاولة بعد دقيقة
        }
    }

    /**
     * تحديد المستلمين للإشعارات
     */
    protected function getRecipients($taxReturn): array
    {
        $recipients = [];

        // منشئ الإقرار
        if ($taxReturn->createdBy) {
            $recipients['creator'] = $taxReturn->createdBy;
        }

        // مدير الشركة
        if ($taxReturn->company && $taxReturn->company->manager) {
            $recipients['company_manager'] = $taxReturn->company->manager;
        }

        // المحاسب المسؤول
        if ($taxReturn->company && $taxReturn->company->accountant) {
            $recipients['accountant'] = $taxReturn->company->accountant;
        }

        // فريق الضرائب
        $taxTeam = $this->getTaxTeamMembers($taxReturn->company_id);
        if (!empty($taxTeam)) {
            $recipients['tax_team'] = $taxTeam;
        }

        // المدراء التنفيذيين (للمبالغ الكبيرة)
        if ($taxReturn->total_tax_amount > 50000) {
            $executives = $this->getExecutiveTeam($taxReturn->company_id);
            if (!empty($executives)) {
                $recipients['executives'] = $executives;
            }
        }

        // مراجع الحسابات الخارجي
        if ($taxReturn->company && $taxReturn->company->external_auditor) {
            $recipients['external_auditor'] = $taxReturn->company->external_auditor;
        }

        return $recipients;
    }

    /**
     * إرسال الإشعارات
     */
    protected function sendNotifications($taxReturn, array $recipients, array $metadata): void
    {
        foreach ($recipients as $role => $recipient) {
            if (is_array($recipient)) {
                // إرسال للمجموعة
                foreach ($recipient as $user) {
                    $this->sendIndividualNotification($taxReturn, $user, $role, $metadata);
                }
            } else {
                // إرسال للمستخدم الواحد
                $this->sendIndividualNotification($taxReturn, $recipient, $role, $metadata);
            }
        }
    }

    /**
     * إرسال إشعار فردي
     */
    protected function sendIndividualNotification($taxReturn, $user, string $role, array $metadata): void
    {
        try {
            // تخصيص الإشعار حسب الدور
            $notificationData = $this->customizeNotificationForRole($taxReturn, $role, $metadata);

            // إنشاء الإشعار
            $notification = new TaxReturnCreatedNotification($taxReturn, $notificationData);

            // تحديد قنوات الإرسال حسب تفضيلات المستخدم
            $channels = $this->getNotificationChannels($user, $role, $taxReturn);

            // إرسال الإشعار
            $user->notify($notification->via($channels));

            // إرسال إشعارات إضافية حسب الحاجة
            $this->sendAdditionalNotifications($taxReturn, $user, $role, $metadata);

        } catch (\Exception $e) {
            Log::warning('فشل في إرسال إشعار فردي', [
                'user_id' => $user->id,
                'role' => $role,
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * تخصيص الإشعار حسب الدور
     */
    protected function customizeNotificationForRole($taxReturn, string $role, array $metadata): array
    {
        $baseData = [
            'tax_return' => $taxReturn,
            'metadata' => $metadata,
            'role' => $role,
        ];

        switch ($role) {
            case 'creator':
                return array_merge($baseData, [
                    'title' => 'تم إنشاء الإقرار الضريبي بنجاح',
                    'message' => "تم إنشاء الإقرار الضريبي رقم {$taxReturn->return_number} بنجاح",
                    'action_text' => 'مراجعة الإقرار',
                    'action_url' => route('tax-returns.show', $taxReturn->id),
                    'priority' => 'medium',
                    'include_next_steps' => true,
                ]);

            case 'company_manager':
                return array_merge($baseData, [
                    'title' => 'إقرار ضريبي جديد يتطلب المراجعة',
                    'message' => "تم إنشاء إقرار ضريبي جديد للشركة بمبلغ " . number_format($taxReturn->total_tax_amount, 2),
                    'action_text' => 'مراجعة والموافقة',
                    'action_url' => route('tax-returns.review', $taxReturn->id),
                    'priority' => 'high',
                    'include_financial_summary' => true,
                ]);

            case 'accountant':
                return array_merge($baseData, [
                    'title' => 'إقرار ضريبي جديد للمراجعة المحاسبية',
                    'message' => "يتطلب الإقرار الضريبي رقم {$taxReturn->return_number} مراجعة محاسبية",
                    'action_text' => 'مراجعة الحسابات',
                    'action_url' => route('tax-returns.accounting-review', $taxReturn->id),
                    'priority' => 'high',
                    'include_calculation_details' => true,
                ]);

            case 'tax_team':
                return array_merge($baseData, [
                    'title' => 'إقرار ضريبي جديد للمراجعة الفنية',
                    'message' => "يتطلب الإقرار الضريبي مراجعة فنية من فريق الضرائب",
                    'action_text' => 'المراجعة الفنية',
                    'action_url' => route('tax-returns.technical-review', $taxReturn->id),
                    'priority' => 'medium',
                    'include_compliance_checklist' => true,
                ]);

            case 'executives':
                return array_merge($baseData, [
                    'title' => 'إقرار ضريبي بمبلغ كبير يتطلب الموافقة',
                    'message' => "إقرار ضريبي بمبلغ " . number_format($taxReturn->total_tax_amount, 2) . " يتطلب موافقة تنفيذية",
                    'action_text' => 'مراجعة تنفيذية',
                    'action_url' => route('tax-returns.executive-review', $taxReturn->id),
                    'priority' => 'high',
                    'include_executive_summary' => true,
                ]);

            case 'external_auditor':
                return array_merge($baseData, [
                    'title' => 'إقرار ضريبي جديد للمراجعة الخارجية',
                    'message' => "إقرار ضريبي جديد متاح للمراجعة الخارجية",
                    'action_text' => 'المراجعة الخارجية',
                    'action_url' => route('tax-returns.external-audit', $taxReturn->id),
                    'priority' => 'low',
                    'include_audit_trail' => true,
                ]);

            default:
                return $baseData;
        }
    }

    /**
     * تحديد قنوات الإشعار
     */
    protected function getNotificationChannels($user, string $role, $taxReturn): array
    {
        $channels = ['database']; // قناة افتراضية

        // إضافة البريد الإلكتروني للأدوار المهمة
        if (in_array($role, ['creator', 'company_manager', 'accountant', 'executives'])) {
            $channels[] = 'mail';
        }

        // إضافة SMS للمبالغ الكبيرة أو المواعيد العاجلة
        if ($taxReturn->total_tax_amount > 100000 || 
            ($taxReturn->due_date && $taxReturn->due_date->diffInDays(now()) <= 3)) {
            if ($user->phone && $user->sms_notifications_enabled) {
                $channels[] = 'sms';
            }
        }

        // إضافة Slack للفرق
        if ($role === 'tax_team' && config('services.slack.enabled')) {
            $channels[] = 'slack';
        }

        // إضافة إشعارات المتصفح للمستخدمين النشطين
        if ($user->browser_notifications_enabled) {
            $channels[] = 'broadcast';
        }

        return $channels;
    }

    /**
     * إرسال إشعارات إضافية
     */
    protected function sendAdditionalNotifications($taxReturn, $user, string $role, array $metadata): void
    {
        // إشعار تذكير للمواعيد القريبة
        if ($taxReturn->due_date && $taxReturn->due_date->diffInDays(now()) <= 7) {
            $this->scheduleReminderNotification($taxReturn, $user, $role);
        }

        // إشعار للمتطلبات الناقصة
        $missingRequirements = $this->checkMissingRequirements($taxReturn);
        if (!empty($missingRequirements)) {
            $this->sendMissingRequirementsNotification($taxReturn, $user, $missingRequirements);
        }

        // إشعار للمخاطر المحتملة
        $risks = $this->assessRisks($taxReturn);
        if (!empty($risks)) {
            $this->sendRiskAssessmentNotification($taxReturn, $user, $risks);
        }
    }

    /**
     * إرسال إشعارات مشروطة
     */
    protected function sendConditionalNotifications($taxReturn, array $metadata): void
    {
        // إشعار للهيئة الضريبية (إذا كان مطلوباً)
        if ($this->shouldNotifyTaxAuthority($taxReturn)) {
            $this->notifyTaxAuthority($taxReturn);
        }

        // إشعار للمراجع الداخلي
        if ($this->requiresInternalAudit($taxReturn)) {
            $this->notifyInternalAuditor($taxReturn);
        }

        // إشعار للإدارة العليا
        if ($this->requiresExecutiveNotification($taxReturn)) {
            $this->notifyExecutiveTeam($taxReturn);
        }

        // إشعار للمستشار القانوني
        if ($this->requiresLegalReview($taxReturn)) {
            $this->notifyLegalCounsel($taxReturn);
        }
    }

    /**
     * تسجيل النشاط
     */
    protected function logActivity($taxReturn, array $recipients, array $metadata): void
    {
        activity()
            ->performedOn($taxReturn)
            ->causedBy(auth()->user())
            ->withProperties([
                'action' => 'tax_return_created_notification_sent',
                'recipients_count' => count($recipients),
                'recipients_roles' => array_keys($recipients),
                'metadata' => $metadata,
                'notification_channels' => $this->getUsedChannels($recipients),
            ])
            ->log('تم إرسال إشعارات إنشاء الإقرار الضريبي');
    }

    /**
     * الحصول على أعضاء فريق الضرائب
     */
    protected function getTaxTeamMembers(int $companyId): array
    {
        // تنفيذ منطق الحصول على أعضاء فريق الضرائب
        return [];
    }

    /**
     * الحصول على الفريق التنفيذي
     */
    protected function getExecutiveTeam(int $companyId): array
    {
        // تنفيذ منطق الحصول على الفريق التنفيذي
        return [];
    }

    /**
     * جدولة إشعار تذكير
     */
    protected function scheduleReminderNotification($taxReturn, $user, string $role): void
    {
        // تنفيذ منطق جدولة التذكير
    }

    /**
     * فحص المتطلبات الناقصة
     */
    protected function checkMissingRequirements($taxReturn): array
    {
        $missing = [];

        if (!$taxReturn->calculated_at) {
            $missing[] = 'حساب الضرائب';
        }

        if ($taxReturn->vat_applicable && !$taxReturn->vat_sales) {
            $missing[] = 'بيانات ضريبة القيمة المضافة';
        }

        return $missing;
    }

    /**
     * تقييم المخاطر
     */
    protected function assessRisks($taxReturn): array
    {
        $risks = [];

        if ($taxReturn->due_date && $taxReturn->due_date->diffInDays(now()) <= 3) {
            $risks[] = [
                'type' => 'deadline_risk',
                'level' => 'high',
                'description' => 'الموعد النهائي قريب جداً',
            ];
        }

        return $risks;
    }

    /**
     * التحقق من ضرورة إشعار الهيئة الضريبية
     */
    protected function shouldNotifyTaxAuthority($taxReturn): bool
    {
        return $taxReturn->total_tax_amount > 500000;
    }

    /**
     * التحقق من ضرورة المراجعة الداخلية
     */
    protected function requiresInternalAudit($taxReturn): bool
    {
        return $taxReturn->total_tax_amount > 100000;
    }

    /**
     * التحقق من ضرورة إشعار الإدارة التنفيذية
     */
    protected function requiresExecutiveNotification($taxReturn): bool
    {
        return $taxReturn->total_tax_amount > 200000;
    }

    /**
     * التحقق من ضرورة المراجعة القانونية
     */
    protected function requiresLegalReview($taxReturn): bool
    {
        return $taxReturn->type === 'ANNUAL_CORPORATE_TAX' && $taxReturn->total_tax_amount > 1000000;
    }

    /**
     * الحصول على القنوات المستخدمة
     */
    protected function getUsedChannels(array $recipients): array
    {
        // تنفيذ منطق جمع القنوات المستخدمة
        return ['database', 'mail', 'sms', 'slack'];
    }

    /**
     * إشعار الهيئة الضريبية
     */
    protected function notifyTaxAuthority($taxReturn): void
    {
        // تنفيذ منطق إشعار الهيئة الضريبية
    }

    /**
     * إشعار المراجع الداخلي
     */
    protected function notifyInternalAuditor($taxReturn): void
    {
        // تنفيذ منطق إشعار المراجع الداخلي
    }

    /**
     * إشعار الفريق التنفيذي
     */
    protected function notifyExecutiveTeam($taxReturn): void
    {
        // تنفيذ منطق إشعار الفريق التنفيذي
    }

    /**
     * إشعار المستشار القانوني
     */
    protected function notifyLegalCounsel($taxReturn): void
    {
        // تنفيذ منطق إشعار المستشار القانوني
    }

    /**
     * إرسال إشعار المتطلبات الناقصة
     */
    protected function sendMissingRequirementsNotification($taxReturn, $user, array $missing): void
    {
        // تنفيذ منطق إرسال إشعار المتطلبات الناقصة
    }

    /**
     * إرسال إشعار تقييم المخاطر
     */
    protected function sendRiskAssessmentNotification($taxReturn, $user, array $risks): void
    {
        // تنفيذ منطق إرسال إشعار تقييم المخاطر
    }
}
