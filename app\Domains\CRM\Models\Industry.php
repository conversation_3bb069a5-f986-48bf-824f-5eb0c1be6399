<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج الصناعة/القطاع - Industry
 */
class Industry extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'name_ar',
        'name_en',
        'name_fr',
        'description',
        'code',
        'parent_id',
        'is_active',
        'sort_order',
        'icon',
        'color',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'metadata' => 'array',
    ];

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function parent(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            default => $this->name,
        };
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }
}
