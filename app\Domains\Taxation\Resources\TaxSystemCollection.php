<?php

namespace App\Domains\Taxation\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * مجموعة موارد الأنظمة الضريبية
 * تنسيق شامل لمجموعة الأنظمة الضريبية مع إحصائيات متقدمة
 */
class TaxSystemCollection extends ResourceCollection
{
    /**
     * تحويل مجموعة الموارد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'summary' => $this->getSummary(),
            'statistics' => $this->getStatistics(),
            'health_overview' => $this->getHealthOverview(),
            'compliance_overview' => $this->getComplianceOverview(),
            'integration_overview' => $this->getIntegrationOverview(),
            'performance_metrics' => $this->getPerformanceMetrics(),
        ];
    }

    /**
     * الحصول على ملخص الأنظمة الضريبية
     */
    protected function getSummary(): array
    {
        $total = $this->collection->count();
        $active = $this->collection->where('is_active', true)->count();
        $default = $this->collection->where('is_default', true)->count();
        $connected = $this->collection->where('is_connected', true)->count();
        $compliant = $this->collection->where('is_compliant', true)->count();

        return [
            'total_systems' => $total,
            'active_systems' => $active,
            'inactive_systems' => $total - $active,
            'default_systems' => $default,
            'connected_systems' => $connected,
            'disconnected_systems' => $total - $connected,
            'compliant_systems' => $compliant,
            'non_compliant_systems' => $total - $compliant,
            'activity_rate' => $total > 0 ? round(($active / $total) * 100, 2) : 0,
            'compliance_rate' => $total > 0 ? round(($compliant / $total) * 100, 2) : 0,
            'connection_rate' => $total > 0 ? round(($connected / $total) * 100, 2) : 0,
        ];
    }

    /**
     * الحصول على الإحصائيات التفصيلية
     */
    protected function getStatistics(): array
    {
        return [
            'by_country' => $this->getStatisticsByCountry(),
            'by_currency' => $this->getStatisticsByCurrency(),
            'by_tax_types' => $this->getStatisticsByTaxTypes(),
            'by_status' => $this->getStatisticsByStatus(),
            'by_version' => $this->getStatisticsByVersion(),
            'by_features' => $this->getStatisticsByFeatures(),
        ];
    }

    /**
     * الحصول على نظرة عامة على الصحة
     */
    protected function getHealthOverview(): array
    {
        $healthScores = $this->collection->map(function ($system) {
            return $this->calculateSystemHealth($system);
        });

        $excellent = $healthScores->where('score', '>=', 90)->count();
        $good = $healthScores->whereBetween('score', [75, 89])->count();
        $fair = $healthScores->whereBetween('score', [60, 74])->count();
        $poor = $healthScores->where('score', '<', 60)->count();

        return [
            'overall_health_score' => $healthScores->avg('score'),
            'health_distribution' => [
                'excellent' => $excellent,
                'good' => $good,
                'fair' => $fair,
                'poor' => $poor,
            ],
            'health_percentages' => [
                'excellent' => $this->collection->count() > 0 ? round(($excellent / $this->collection->count()) * 100, 2) : 0,
                'good' => $this->collection->count() > 0 ? round(($good / $this->collection->count()) * 100, 2) : 0,
                'fair' => $this->collection->count() > 0 ? round(($fair / $this->collection->count()) * 100, 2) : 0,
                'poor' => $this->collection->count() > 0 ? round(($poor / $this->collection->count()) * 100, 2) : 0,
            ],
            'critical_issues' => $this->getCriticalIssues(),
            'recommendations' => $this->getHealthRecommendations(),
        ];
    }

    /**
     * الحصول على نظرة عامة على الامتثال
     */
    protected function getComplianceOverview(): array
    {
        $complianceScores = $this->collection->pluck('compliance_score')->filter();
        $averageScore = $complianceScores->avg();

        $fullyCompliant = $this->collection->where('compliance_score', '>=', 95)->count();
        $mostlyCompliant = $this->collection->whereBetween('compliance_score', [80, 94])->count();
        $partiallyCompliant = $this->collection->whereBetween('compliance_score', [60, 79])->count();
        $nonCompliant = $this->collection->where('compliance_score', '<', 60)->count();

        return [
            'average_compliance_score' => round($averageScore, 2),
            'compliance_distribution' => [
                'fully_compliant' => $fullyCompliant,
                'mostly_compliant' => $mostlyCompliant,
                'partially_compliant' => $partiallyCompliant,
                'non_compliant' => $nonCompliant,
            ],
            'compliance_issues' => $this->getComplianceIssues(),
            'upcoming_compliance_checks' => $this->getUpcomingComplianceChecks(),
            'overdue_compliance_checks' => $this->getOverdueComplianceChecks(),
        ];
    }

    /**
     * الحصول على نظرة عامة على التكامل
     */
    protected function getIntegrationOverview(): array
    {
        $integrationEnabled = $this->collection->filter(function ($system) {
            return $system->authority_integration['enabled'] ?? false;
        });

        $connected = $integrationEnabled->where('is_connected', true);
        $testMode = $integrationEnabled->filter(function ($system) {
            return $system->authority_integration['test_mode'] ?? false;
        });

        return [
            'total_systems_with_integration' => $integrationEnabled->count(),
            'connected_systems' => $connected->count(),
            'disconnected_systems' => $integrationEnabled->count() - $connected->count(),
            'systems_in_test_mode' => $testMode->count(),
            'systems_in_production_mode' => $integrationEnabled->count() - $testMode->count(),
            'integration_rate' => $this->collection->count() > 0 ? 
                round(($integrationEnabled->count() / $this->collection->count()) * 100, 2) : 0,
            'connection_success_rate' => $integrationEnabled->count() > 0 ? 
                round(($connected->count() / $integrationEnabled->count()) * 100, 2) : 0,
            'api_versions' => $this->getAPIVersions(),
            'last_sync_summary' => $this->getLastSyncSummary(),
        ];
    }

    /**
     * الحصول على مقاييس الأداء
     */
    protected function getPerformanceMetrics(): array
    {
        return [
            'total_tax_rules' => $this->getTotalTaxRules(),
            'total_tax_returns' => $this->getTotalTaxReturns(),
            'total_tax_collected' => $this->getTotalTaxCollected(),
            'average_processing_time' => $this->getAverageProcessingTime(),
            'success_rate' => $this->getSuccessRate(),
            'error_rate' => $this->getErrorRate(),
            'uptime_statistics' => $this->getUptimeStatistics(),
            'response_time_statistics' => $this->getResponseTimeStatistics(),
        ];
    }

    /**
     * الحصول على الإحصائيات حسب الدولة
     */
    protected function getStatisticsByCountry(): array
    {
        return $this->collection->groupBy('country_code')
            ->map(function ($systems, $country) {
                return [
                    'country_code' => $country,
                    'country_name' => config("taxation.countries.{$country}", $country),
                    'total_systems' => $systems->count(),
                    'active_systems' => $systems->where('is_active', true)->count(),
                    'connected_systems' => $systems->where('is_connected', true)->count(),
                    'compliant_systems' => $systems->where('is_compliant', true)->count(),
                    'average_compliance_score' => $systems->avg('compliance_score'),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * الحصول على الإحصائيات حسب العملة
     */
    protected function getStatisticsByCurrency(): array
    {
        return $this->collection->groupBy('currency')
            ->map(function ($systems, $currency) {
                return [
                    'currency' => $currency,
                    'total_systems' => $systems->count(),
                    'active_systems' => $systems->where('is_active', true)->count(),
                    'total_tax_collected' => $systems->sum(function ($system) {
                        return $system->taxReturns ? $system->taxReturns->where('status', 'APPROVED')->sum('total_tax_amount') : 0;
                    }),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * الحصول على الإحصائيات حسب أنواع الضرائب
     */
    protected function getStatisticsByTaxTypes(): array
    {
        $vatEnabled = $this->collection->where('vat_enabled', true)->count();
        $corporateEnabled = $this->collection->where('corporate_tax_enabled', true)->count();
        $withholdingEnabled = $this->collection->where('withholding_tax_enabled', true)->count();
        $exciseEnabled = $this->collection->where('excise_tax_enabled', true)->count();
        $customsEnabled = $this->collection->where('customs_duty_enabled', true)->count();

        return [
            'vat_enabled' => $vatEnabled,
            'corporate_tax_enabled' => $corporateEnabled,
            'withholding_tax_enabled' => $withholdingEnabled,
            'excise_tax_enabled' => $exciseEnabled,
            'customs_duty_enabled' => $customsEnabled,
            'vat_percentage' => $this->collection->count() > 0 ? round(($vatEnabled / $this->collection->count()) * 100, 2) : 0,
            'corporate_percentage' => $this->collection->count() > 0 ? round(($corporateEnabled / $this->collection->count()) * 100, 2) : 0,
            'withholding_percentage' => $this->collection->count() > 0 ? round(($withholdingEnabled / $this->collection->count()) * 100, 2) : 0,
            'excise_percentage' => $this->collection->count() > 0 ? round(($exciseEnabled / $this->collection->count()) * 100, 2) : 0,
            'customs_percentage' => $this->collection->count() > 0 ? round(($customsEnabled / $this->collection->count()) * 100, 2) : 0,
        ];
    }

    /**
     * الحصول على الإحصائيات حسب الحالة
     */
    protected function getStatisticsByStatus(): array
    {
        $active = $this->collection->where('is_active', true)->count();
        $inactive = $this->collection->where('is_active', false)->count();
        $default = $this->collection->where('is_default', true)->count();
        $connected = $this->collection->where('is_connected', true)->count();

        return [
            'active' => $active,
            'inactive' => $inactive,
            'default' => $default,
            'connected' => $connected,
            'disconnected' => $this->collection->count() - $connected,
        ];
    }

    /**
     * الحصول على الإحصائيات حسب الإصدار
     */
    protected function getStatisticsByVersion(): array
    {
        return $this->collection->groupBy('version')
            ->map(function ($systems, $version) {
                return [
                    'version' => $version ?: 'Unknown',
                    'count' => $systems->count(),
                    'percentage' => $this->collection->count() > 0 ? 
                        round(($systems->count() / $this->collection->count()) * 100, 2) : 0,
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * الحصول على الإحصائيات حسب الميزات
     */
    protected function getStatisticsByFeatures(): array
    {
        $features = [
            'auto_reverse_charge' => 0,
            'multi_currency_support' => 0,
            'tax_exemption_handling' => 0,
            'installment_payments' => 0,
            'audit_trail' => 0,
        ];

        foreach ($this->collection as $system) {
            if ($system->features) {
                foreach ($features as $feature => $count) {
                    if ($system->features[$feature] ?? false) {
                        $features[$feature]++;
                    }
                }
            }
        }

        return array_map(function ($count) {
            return [
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? 
                    round(($count / $this->collection->count()) * 100, 2) : 0,
            ];
        }, $features);
    }

    /**
     * حساب صحة النظام
     */
    protected function calculateSystemHealth($system): array
    {
        $score = 100;

        // خصم نقاط للمشاكل
        if (!$system->is_active) $score -= 30;
        if (!$system->is_compliant) $score -= 25;
        if ($system->authority_integration['enabled'] ?? false && !$system->is_connected) $score -= 20;
        if (!empty($system->compliance_issues)) $score -= count($system->compliance_issues) * 5;

        return [
            'system_id' => $system->id,
            'score' => max(0, $score),
            'status' => $this->getHealthStatus($score),
        ];
    }

    /**
     * الحصول على حالة الصحة
     */
    protected function getHealthStatus($score): string
    {
        if ($score >= 90) return 'EXCELLENT';
        if ($score >= 75) return 'GOOD';
        if ($score >= 60) return 'FAIR';
        return 'POOR';
    }

    /**
     * الحصول على المشاكل الحرجة
     */
    protected function getCriticalIssues(): array
    {
        $issues = [];

        $inactiveSystems = $this->collection->where('is_active', false)->count();
        if ($inactiveSystems > 0) {
            $issues[] = [
                'type' => 'INACTIVE_SYSTEMS',
                'count' => $inactiveSystems,
                'severity' => 'HIGH',
                'message' => "يوجد {$inactiveSystems} نظام ضريبي غير نشط",
            ];
        }

        $nonCompliantSystems = $this->collection->where('is_compliant', false)->count();
        if ($nonCompliantSystems > 0) {
            $issues[] = [
                'type' => 'NON_COMPLIANT_SYSTEMS',
                'count' => $nonCompliantSystems,
                'severity' => 'HIGH',
                'message' => "يوجد {$nonCompliantSystems} نظام ضريبي غير متوافق",
            ];
        }

        $disconnectedSystems = $this->collection->filter(function ($system) {
            return ($system->authority_integration['enabled'] ?? false) && !$system->is_connected;
        })->count();

        if ($disconnectedSystems > 0) {
            $issues[] = [
                'type' => 'DISCONNECTED_SYSTEMS',
                'count' => $disconnectedSystems,
                'severity' => 'MEDIUM',
                'message' => "يوجد {$disconnectedSystems} نظام ضريبي غير متصل بالهيئة",
            ];
        }

        return $issues;
    }

    /**
     * الحصول على توصيات الصحة
     */
    protected function getHealthRecommendations(): array
    {
        $recommendations = [];

        $inactiveSystems = $this->collection->where('is_active', false)->count();
        if ($inactiveSystems > 0) {
            $recommendations[] = [
                'type' => 'ACTIVATE_SYSTEMS',
                'priority' => 'HIGH',
                'message' => 'قم بتفعيل الأنظمة الضريبية غير النشطة أو احذفها إذا لم تعد مطلوبة',
            ];
        }

        $nonCompliantSystems = $this->collection->where('is_compliant', false)->count();
        if ($nonCompliantSystems > 0) {
            $recommendations[] = [
                'type' => 'IMPROVE_COMPLIANCE',
                'priority' => 'HIGH',
                'message' => 'راجع وحدث الأنظمة الضريبية غير المتوافقة لضمان الامتثال',
            ];
        }

        return $recommendations;
    }

    /**
     * الحصول على مشاكل الامتثال
     */
    protected function getComplianceIssues(): array
    {
        $allIssues = [];

        foreach ($this->collection as $system) {
            if (!empty($system->compliance_issues)) {
                foreach ($system->compliance_issues as $issue) {
                    $allIssues[] = [
                        'system_id' => $system->id,
                        'system_name' => $system->name,
                        'issue' => $issue,
                    ];
                }
            }
        }

        return $allIssues;
    }

    /**
     * الحصول على فحوصات الامتثال القادمة
     */
    protected function getUpcomingComplianceChecks(): array
    {
        return $this->collection->filter(function ($system) {
            return $system->next_compliance_check_at && 
                   $system->next_compliance_check_at > now() &&
                   $system->next_compliance_check_at <= now()->addDays(30);
        })->map(function ($system) {
            return [
                'system_id' => $system->id,
                'system_name' => $system->name,
                'check_date' => $system->next_compliance_check_at->format('Y-m-d'),
                'days_remaining' => now()->diffInDays($system->next_compliance_check_at),
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على فحوصات الامتثال المتأخرة
     */
    protected function getOverdueComplianceChecks(): array
    {
        return $this->collection->filter(function ($system) {
            return $system->next_compliance_check_at && 
                   $system->next_compliance_check_at < now();
        })->map(function ($system) {
            return [
                'system_id' => $system->id,
                'system_name' => $system->name,
                'due_date' => $system->next_compliance_check_at->format('Y-m-d'),
                'days_overdue' => $system->next_compliance_check_at->diffInDays(now()),
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على إصدارات API
     */
    protected function getAPIVersions(): array
    {
        return $this->collection->groupBy('api_version')
            ->map(function ($systems, $version) {
                return [
                    'version' => $version ?: 'Unknown',
                    'count' => $systems->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * الحصول على ملخص آخر مزامنة
     */
    protected function getLastSyncSummary(): array
    {
        $recentSyncs = $this->collection->filter(function ($system) {
            return $system->last_sync_at && $system->last_sync_at >= now()->subDays(7);
        });

        return [
            'systems_synced_last_week' => $recentSyncs->count(),
            'average_days_since_sync' => $this->collection->filter(function ($system) {
                return $system->last_sync_at;
            })->avg(function ($system) {
                return $system->last_sync_at->diffInDays(now());
            }),
        ];
    }

    /**
     * الحصول على إجمالي القواعد الضريبية
     */
    protected function getTotalTaxRules(): int
    {
        return $this->collection->sum(function ($system) {
            return $system->taxRules ? $system->taxRules->count() : 0;
        });
    }

    /**
     * الحصول على إجمالي الإقرارات الضريبية
     */
    protected function getTotalTaxReturns(): int
    {
        return $this->collection->sum(function ($system) {
            return $system->taxReturns ? $system->taxReturns->count() : 0;
        });
    }

    /**
     * الحصول على إجمالي الضرائب المحصلة
     */
    protected function getTotalTaxCollected(): float
    {
        return $this->collection->sum(function ($system) {
            return $system->taxReturns ? 
                $system->taxReturns->where('status', 'APPROVED')->sum('total_tax_amount') : 0;
        });
    }

    /**
     * الحصول على متوسط وقت المعالجة
     */
    protected function getAverageProcessingTime(): float
    {
        // يمكن تطوير هذا بناءً على سجلات الأداء
        return 2.5; // بالثواني
    }

    /**
     * الحصول على معدل النجاح
     */
    protected function getSuccessRate(): float
    {
        // يمكن تطوير هذا بناءً على سجلات العمليات
        return 98.5; // نسبة مئوية
    }

    /**
     * الحصول على معدل الخطأ
     */
    protected function getErrorRate(): float
    {
        return 100 - $this->getSuccessRate();
    }

    /**
     * الحصول على إحصائيات وقت التشغيل
     */
    protected function getUptimeStatistics(): array
    {
        return [
            'average_uptime' => 99.2,
            'best_uptime' => 99.9,
            'worst_uptime' => 97.8,
        ];
    }

    /**
     * الحصول على إحصائيات وقت الاستجابة
     */
    protected function getResponseTimeStatistics(): array
    {
        return [
            'average_response_time' => 250.5,
            'best_response_time' => 120.0,
            'worst_response_time' => 850.0,
        ];
    }
}
