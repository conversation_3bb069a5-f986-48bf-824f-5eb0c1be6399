<?php

namespace App\Domains\Projects\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد المشروع
 * تنسيق شامل لبيانات المشروع مع دعم المستويات المختلفة
 */
class ProjectResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'description' => $this->description,
            'code' => $this->code,
            
            // Basic Information
            'basic_info' => [
                'status' => $this->status,
                'status_label' => $this->status ? \App\Domains\Projects\Models\Project::STATUSES[$this->status] ?? $this->status : null,
                'status_color' => $this->getStatusColor(),
                'priority' => $this->priority,
                'priority_label' => $this->priority ? \App\Domains\Projects\Models\Project::PRIORITIES[$this->priority] ?? $this->priority : null,
                'priority_color' => $this->getPriorityColor(),
                'category' => $this->category,
                'category_label' => $this->category ? \App\Domains\Projects\Models\Project::CATEGORIES[$this->category] ?? $this->category : null,
                'methodology' => $this->methodology,
                'methodology_label' => $this->methodology ? \App\Domains\Projects\Models\Project::METHODOLOGIES[$this->methodology] ?? $this->methodology : null,
                'visibility' => $this->visibility,
                'risk_level' => $this->risk_level,
                'security_level' => $this->security_level,
            ],

            // Dates and Timeline
            'timeline' => [
                'start_date' => $this->start_date?->format('Y-m-d'),
                'end_date' => $this->end_date?->format('Y-m-d'),
                'planned_start_date' => $this->planned_start_date?->format('Y-m-d'),
                'planned_end_date' => $this->planned_end_date?->format('Y-m-d'),
                'actual_start_date' => $this->actual_start_date?->format('Y-m-d'),
                'actual_end_date' => $this->actual_end_date?->format('Y-m-d'),
                'duration_days' => $this->getDurationInDays(),
                'remaining_days' => $this->getRemainingDays(),
                'is_overdue' => $this->isOverdue(),
                'days_overdue' => $this->getDaysOverdue(),
                'progress_percentage' => $this->progress_percentage,
                'estimated_completion_date' => $this->getEstimatedCompletionDate(),
            ],

            // Financial Information
            'financial' => [
                'budget' => $this->budget,
                'currency' => $this->currency,
                'formatted_budget' => $this->currency . ' ' . number_format($this->budget, 2),
                'is_billable' => $this->is_billable,
                'billing_type' => $this->billing_type,
                'hourly_rate' => $this->hourly_rate,
                'fixed_price' => $this->fixed_price,
                'total_cost' => $this->when(
                    method_exists($this, 'calculateTotalCost'),
                    fn() => $this->calculateTotalCost()
                ),
                'budget_utilization' => $this->when(
                    method_exists($this, 'getBudgetUtilization'),
                    fn() => $this->getBudgetUtilization()
                ),
                'profit_margin' => $this->when(
                    method_exists($this, 'getProfitMargin'),
                    fn() => $this->getProfitMargin()
                ),
            ],

            // Team and Stakeholders
            'team' => [
                'client' => $this->whenLoaded('client', function () {
                    return $this->client ? [
                        'id' => $this->client->id,
                        'name' => $this->client->name,
                        'email' => $this->client->email,
                        'company' => $this->client->company,
                        'avatar' => $this->client->avatar,
                    ] : null;
                }),
                'project_manager' => $this->whenLoaded('projectManager', function () {
                    return $this->projectManager ? [
                        'id' => $this->projectManager->id,
                        'name' => $this->projectManager->name,
                        'email' => $this->projectManager->email,
                        'avatar' => $this->projectManager->avatar,
                        'title' => $this->projectManager->title,
                    ] : null;
                }),
                'team_members' => $this->whenLoaded('team', function () {
                    return $this->team->map(function ($member) {
                        return [
                            'id' => $member->user->id,
                            'name' => $member->user->name,
                            'email' => $member->user->email,
                            'avatar' => $member->user->avatar,
                            'role' => $member->role,
                            'permissions' => $member->permissions,
                            'hourly_rate' => $member->hourly_rate,
                            'joined_at' => $member->created_at?->format('Y-m-d'),
                        ];
                    });
                }),
                'team_size' => $this->whenLoaded('team', function () {
                    return $this->team->count();
                }),
            ],

            // Project Structure
            'structure' => [
                'parent' => $this->whenLoaded('parent', function () {
                    return $this->parent ? [
                        'id' => $this->parent->id,
                        'name' => $this->parent->name,
                        'code' => $this->parent->code,
                    ] : null;
                }),
                'children' => $this->whenLoaded('children', function () {
                    return $this->children->map(function ($child) {
                        return [
                            'id' => $child->id,
                            'name' => $child->name,
                            'code' => $child->code,
                            'status' => $child->status,
                            'progress_percentage' => $child->progress_percentage,
                        ];
                    });
                }),
                'children_count' => $this->whenLoaded('children', function () {
                    return $this->children->count();
                }),
            ],

            // Tasks and Milestones
            'work_breakdown' => [
                'tasks_count' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->count();
                }),
                'completed_tasks' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->where('status', 'DONE')->count();
                }),
                'in_progress_tasks' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->where('status', 'IN_PROGRESS')->count();
                }),
                'overdue_tasks' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->filter(function ($task) {
                        return $task->due_date && $task->due_date < now() && $task->status !== 'DONE';
                    })->count();
                }),
                'milestones' => $this->whenLoaded('milestones', function () {
                    return MilestoneResource::collection($this->milestones);
                }),
                'milestones_count' => $this->whenLoaded('milestones', function () {
                    return $this->milestones->count();
                }),
                'completed_milestones' => $this->whenLoaded('milestones', function () {
                    return $this->milestones->where('is_completed', true)->count();
                }),
            ],

            // Time Tracking
            'time_tracking' => [
                'total_logged_hours' => $this->whenLoaded('timeEntries', function () {
                    return $this->timeEntries->sum('hours');
                }),
                'billable_hours' => $this->whenLoaded('timeEntries', function () {
                    return $this->timeEntries->where('is_billable', true)->sum('hours');
                }),
                'estimated_hours' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->sum('estimated_hours');
                }),
                'remaining_hours' => $this->whenLoaded('tasks', function () {
                    return $this->tasks->sum('remaining_hours');
                }),
                'time_utilization' => $this->getTimeUtilization(),
            ],

            // Settings and Features
            'settings' => [
                'auto_calculate_progress' => $this->auto_calculate_progress,
                'enable_time_tracking' => $this->enable_time_tracking,
                'enable_expenses' => $this->enable_expenses,
                'enable_documents' => $this->enable_documents,
                'enable_chat' => $this->enable_chat,
                'enable_gantt' => $this->enable_gantt,
                'enable_kanban' => $this->enable_kanban,
                'enable_calendar' => $this->enable_calendar,
            ],

            // Integration and URLs
            'integration' => [
                'repository_url' => $this->repository_url,
                'deployment_url' => $this->deployment_url,
                'staging_url' => $this->staging_url,
                'documentation_url' => $this->documentation_url,
            ],

            // Quality and Compliance
            'quality' => [
                'quality_standards' => $this->quality_standards,
                'testing_requirements' => $this->testing_requirements,
                'acceptance_criteria' => $this->acceptance_criteria,
                'compliance_requirements' => $this->compliance_requirements,
                'industry_standards' => $this->industry_standards,
            ],

            // Risk Management
            'risk_management' => [
                'risk_factors' => $this->risk_factors,
                'risks' => $this->whenLoaded('risks', function () {
                    return RiskResource::collection($this->risks);
                }),
                'high_risks_count' => $this->whenLoaded('risks', function () {
                    return $this->risks->where('severity', 'HIGH')->count();
                }),
            ],

            // Documents and Communication
            'collaboration' => [
                'documents_count' => $this->whenLoaded('documents', function () {
                    return $this->documents->count();
                }),
                'comments_count' => $this->whenLoaded('comments', function () {
                    return $this->comments->count();
                }),
                'activities_count' => $this->whenLoaded('activities', function () {
                    return $this->activities->count();
                }),
                'last_activity' => $this->whenLoaded('activities', function () {
                    $lastActivity = $this->activities->sortByDesc('created_at')->first();
                    return $lastActivity ? [
                        'description' => $lastActivity->description,
                        'user' => $lastActivity->user->name,
                        'created_at' => $lastActivity->created_at->diffForHumans(),
                    ] : null;
                }),
            ],

            // Agile/Scrum Information
            'agile' => [
                'sprints' => $this->whenLoaded('sprints', function () {
                    return SprintResource::collection($this->sprints);
                }),
                'active_sprint' => $this->whenLoaded('sprints', function () {
                    $activeSprint = $this->sprints->where('status', 'ACTIVE')->first();
                    return $activeSprint ? new SprintResource($activeSprint) : null;
                }),
                'sprints_count' => $this->whenLoaded('sprints', function () {
                    return $this->sprints->count();
                }),
                'completed_sprints' => $this->whenLoaded('sprints', function () {
                    return $this->sprints->where('status', 'COMPLETED')->count();
                }),
            ],

            // Custom Fields and Metadata
            'custom_data' => [
                'custom_fields' => $this->custom_fields,
                'tags' => $this->tags,
                'metadata' => $this->metadata,
                'notes' => $this->notes,
            ],

            // Notifications
            'notification_settings' => $this->notification_settings,

            // System Information
            'system_info' => [
                'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
                'created_by' => $this->created_by,
                'updated_by' => $this->updated_by,
            ],

            // Health Metrics
            'health_metrics' => [
                'overall_health' => $this->getOverallHealth(),
                'schedule_health' => $this->getScheduleHealth(),
                'budget_health' => $this->getBudgetHealth(),
                'quality_health' => $this->getQualityHealth(),
                'team_health' => $this->getTeamHealth(),
            ],

            // Permissions (for current user)
            'permissions' => [
                'can_view' => $request->user()?->can('view', $this->resource),
                'can_update' => $request->user()?->can('update', $this->resource),
                'can_delete' => $request->user()?->can('delete', $this->resource),
                'can_manage_team' => $request->user()?->can('manageTeam', $this->resource),
                'can_manage_tasks' => $request->user()?->can('manageTasks', $this->resource),
                'can_view_financials' => $request->user()?->can('viewFinancials', $this->resource),
            ],
        ];
    }

    /**
     * الحصول على لون الحالة
     */
    protected function getStatusColor(): string
    {
        return match ($this->status) {
            'PLANNING' => '#3B82F6',
            'IN_PROGRESS' => '#10B981',
            'ON_HOLD' => '#F59E0B',
            'COMPLETED' => '#059669',
            'CANCELLED' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    protected function getPriorityColor(): string
    {
        return match ($this->priority) {
            'LOW' => '#10B981',
            'MEDIUM' => '#F59E0B',
            'HIGH' => '#EF4444',
            'CRITICAL' => '#DC2626',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على مدة المشروع بالأيام
     */
    protected function getDurationInDays(): ?int
    {
        if (!$this->start_date || !$this->end_date) {
            return null;
        }

        return $this->start_date->diffInDays($this->end_date);
    }

    /**
     * الحصول على الأيام المتبقية
     */
    protected function getRemainingDays(): ?int
    {
        if (!$this->end_date) {
            return null;
        }

        $remaining = now()->diffInDays($this->end_date, false);
        return $remaining > 0 ? $remaining : 0;
    }

    /**
     * التحقق من التأخير
     */
    protected function isOverdue(): bool
    {
        return $this->end_date && now() > $this->end_date && $this->status !== 'COMPLETED';
    }

    /**
     * الحصول على أيام التأخير
     */
    protected function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }

    /**
     * الحصول على تاريخ الإنجاز المتوقع
     */
    protected function getEstimatedCompletionDate(): ?string
    {
        if ($this->status === 'COMPLETED') {
            return $this->actual_end_date?->format('Y-m-d');
        }

        if ($this->progress_percentage > 0) {
            $totalDays = $this->getDurationInDays();
            if ($totalDays) {
                $remainingProgress = 100 - $this->progress_percentage;
                $estimatedRemainingDays = ($totalDays * $remainingProgress) / 100;
                return now()->addDays($estimatedRemainingDays)->format('Y-m-d');
            }
        }

        return $this->end_date?->format('Y-m-d');
    }

    /**
     * الحصول على استخدام الوقت
     */
    protected function getTimeUtilization(): ?float
    {
        if (!$this->relationLoaded('timeEntries') || !$this->relationLoaded('tasks')) {
            return null;
        }

        $totalLogged = $this->timeEntries->sum('hours');
        $totalEstimated = $this->tasks->sum('estimated_hours');

        if ($totalEstimated == 0) {
            return null;
        }

        return round(($totalLogged / $totalEstimated) * 100, 2);
    }

    /**
     * الحصول على الصحة العامة للمشروع
     */
    protected function getOverallHealth(): string
    {
        $scheduleHealth = $this->getScheduleHealthScore();
        $budgetHealth = $this->getBudgetHealthScore();
        $qualityHealth = $this->getQualityHealthScore();

        $averageHealth = ($scheduleHealth + $budgetHealth + $qualityHealth) / 3;

        if ($averageHealth >= 80) return 'EXCELLENT';
        if ($averageHealth >= 60) return 'GOOD';
        if ($averageHealth >= 40) return 'FAIR';
        return 'POOR';
    }

    /**
     * الحصول على صحة الجدولة
     */
    protected function getScheduleHealth(): string
    {
        $score = $this->getScheduleHealthScore();
        
        if ($score >= 80) return 'ON_TRACK';
        if ($score >= 60) return 'MINOR_DELAYS';
        if ($score >= 40) return 'MODERATE_DELAYS';
        return 'MAJOR_DELAYS';
    }

    /**
     * الحصول على درجة صحة الجدولة
     */
    protected function getScheduleHealthScore(): int
    {
        if ($this->isOverdue()) {
            return 20;
        }

        $remainingDays = $this->getRemainingDays();
        $totalDays = $this->getDurationInDays();

        if (!$remainingDays || !$totalDays) {
            return 50;
        }

        $timeProgress = (($totalDays - $remainingDays) / $totalDays) * 100;
        $workProgress = $this->progress_percentage;

        if ($workProgress >= $timeProgress) {
            return 90;
        }

        $variance = $timeProgress - $workProgress;
        return max(20, 90 - ($variance * 2));
    }

    /**
     * الحصول على صحة الميزانية
     */
    protected function getBudgetHealth(): string
    {
        $score = $this->getBudgetHealthScore();
        
        if ($score >= 80) return 'UNDER_BUDGET';
        if ($score >= 60) return 'ON_BUDGET';
        if ($score >= 40) return 'OVER_BUDGET';
        return 'SIGNIFICANTLY_OVER_BUDGET';
    }

    /**
     * الحصول على درجة صحة الميزانية
     */
    protected function getBudgetHealthScore(): int
    {
        // يمكن تطوير هذا بناءً على التكاليف الفعلية
        return 75; // قيمة افتراضية
    }

    /**
     * الحصول على صحة الجودة
     */
    protected function getQualityHealth(): string
    {
        $score = $this->getQualityHealthScore();
        
        if ($score >= 80) return 'HIGH_QUALITY';
        if ($score >= 60) return 'GOOD_QUALITY';
        if ($score >= 40) return 'ACCEPTABLE_QUALITY';
        return 'POOR_QUALITY';
    }

    /**
     * الحصول على درجة صحة الجودة
     */
    protected function getQualityHealthScore(): int
    {
        // يمكن تطوير هذا بناءً على معايير الجودة
        return 80; // قيمة افتراضية
    }

    /**
     * الحصول على صحة الفريق
     */
    protected function getTeamHealth(): string
    {
        // يمكن تطوير هذا بناءً على أداء الفريق
        return 'GOOD';
    }
}
