<?php

namespace App\Domains\Accounting\Repositories;

use App\Domains\Accounting\Models\Account;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Account Repository
 * مستودع الحسابات المحاسبية
 */
class AccountRepository
{
    protected Account $model;

    public function __construct(Account $model)
    {
        $this->model = $model;
    }

    /**
     * الحصول على جميع الحسابات
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->newQuery();

        return $this->applyFilters($query, $filters)->get();
    }

    /**
     * الحصول على الحسابات مع التصفح
     */
    public function paginate(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        return $this->applyFilters($query, $filters)->paginate($perPage);
    }

    /**
     * البحث عن حساب بالمعرف
     */
    public function findById(int $id): ?Account
    {
        return $this->model->find($id);
    }

    /**
     * البحث عن حساب بالرمز
     */
    public function findByCode(string $code): ?Account
    {
        return $this->model->where('account_code', $code)->first();
    }

    /**
     * البحث عن حساب بالاسم
     */
    public function findByName(string $name): ?Account
    {
        return $this->model->where('account_name', $name)->first();
    }

    /**
     * إنشاء حساب جديد
     */
    public function create(array $data): Account
    {
        return $this->model->create($data);
    }

    /**
     * تحديث حساب
     */
    public function update(Account $account, array $data): bool
    {
        return $account->update($data);
    }

    /**
     * حذف حساب
     */
    public function delete(Account $account): bool
    {
        return $account->delete();
    }

    /**
     * الحصول على الحسابات الرئيسية
     */
    public function getParentAccounts(): Collection
    {
        return Cache::remember('parent_accounts', 3600, function () {
            return $this->model->whereNull('parent_account_id')
                              ->where('is_active', true)
                              ->orderBy('account_code')
                              ->get();
        });
    }

    /**
     * الحصول على الحسابات الفرعية
     */
    public function getSubAccounts(int $parentId): Collection
    {
        return $this->model->where('parent_account_id', $parentId)
                          ->where('is_active', true)
                          ->orderBy('account_code')
                          ->get();
    }

    /**
     * الحصول على الحسابات حسب النوع
     */
    public function getByType(string $type): Collection
    {
        return Cache::remember("accounts_by_type_{$type}", 1800, function () use ($type) {
            return $this->model->where('account_type', $type)
                              ->where('is_active', true)
                              ->orderBy('account_code')
                              ->get();
        });
    }

    /**
     * الحصول على الحسابات حسب الفئة
     */
    public function getByCategory(string $category): Collection
    {
        return Cache::remember("accounts_by_category_{$category}", 1800, function () use ($category) {
            return $this->model->where('account_category', $category)
                              ->where('is_active', true)
                              ->orderBy('account_code')
                              ->get();
        });
    }

    /**
     * الحصول على الحسابات البنكية
     */
    public function getBankAccounts(): Collection
    {
        return Cache::remember('bank_accounts', 1800, function () {
            return $this->model->where('account_category', 'current_assets')
                              ->where(function ($query) {
                                  $query->whereNotNull('bank_account_number')
                                        ->orWhereNotNull('iban');
                              })
                              ->where('is_active', true)
                              ->orderBy('account_code')
                              ->get();
        });
    }

    /**
     * الحصول على الحسابات التي تسمح بالإدخال اليدوي
     */
    public function getManualEntryAccounts(): Collection
    {
        return Cache::remember('manual_entry_accounts', 1800, function () {
            return $this->model->where('allow_manual_entry', true)
                              ->where('is_active', true)
                              ->orderBy('account_code')
                              ->get();
        });
    }

    /**
     * الحصول على دليل الحسابات
     */
    public function getChartOfAccounts(): Collection
    {
        return Cache::remember('chart_of_accounts', 3600, function () {
            return $this->model->with(['subAccounts' => function ($query) {
                $query->where('is_active', true)->orderBy('account_code');
            }])
            ->whereNull('parent_account_id')
            ->where('is_active', true)
            ->orderBy('account_code')
            ->get();
        });
    }

    /**
     * البحث في الحسابات
     */
    public function search(string $term): Collection
    {
        return $this->model->where(function ($query) use ($term) {
            $query->where('account_code', 'like', "%{$term}%")
                  ->orWhere('account_name', 'like', "%{$term}%")
                  ->orWhere('account_name_en', 'like', "%{$term}%");
        })
        ->where('is_active', true)
        ->orderBy('account_code')
        ->limit(50)
        ->get();
    }

    /**
     * الحصول على الحسابات مع الأرصدة
     */
    public function getAccountsWithBalances(array $accountIds = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($accountIds)) {
            $query->whereIn('id', $accountIds);
        }

        return $query->where('is_active', true)
                    ->orderBy('account_code')
                    ->get()
                    ->each(function ($account) {
                        $account->updateCurrentBalance();
                    });
    }

    /**
     * الحصول على ميزان المراجعة
     */
    public function getTrialBalance(string $dateFrom, string $dateTo): array
    {
        $cacheKey = "trial_balance_{$dateFrom}_{$dateTo}";
        
        return Cache::remember($cacheKey, 1800, function () use ($dateFrom, $dateTo) {
            $accounts = $this->model->with(['journalEntryDetails' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereHas('journalEntry', function ($q) use ($dateFrom, $dateTo) {
                    $q->whereBetween('entry_date', [$dateFrom, $dateTo])
                      ->where('status', 'POSTED');
                });
            }])
            ->where('allow_manual_entry', true)
            ->where('is_active', true)
            ->orderBy('account_code')
            ->get();

            $trialBalance = [];
            $totalDebits = 0;
            $totalCredits = 0;

            foreach ($accounts as $account) {
                $debits = $account->journalEntryDetails->where('entry_type', 'debit')->sum('amount');
                $credits = $account->journalEntryDetails->where('entry_type', 'credit')->sum('amount');
                
                if ($debits > 0 || $credits > 0) {
                    $trialBalance[] = [
                        'account_id' => $account->id,
                        'account_code' => $account->account_code,
                        'account_name' => $account->account_name,
                        'account_type' => $account->account_type,
                        'debit_amount' => $debits,
                        'credit_amount' => $credits,
                        'balance' => $account->is_debit_nature ? ($debits - $credits) : ($credits - $debits),
                    ];
                    
                    $totalDebits += $debits;
                    $totalCredits += $credits;
                }
            }

            return [
                'accounts' => $trialBalance,
                'totals' => [
                    'total_debits' => $totalDebits,
                    'total_credits' => $totalCredits,
                    'difference' => $totalDebits - $totalCredits,
                    'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
                ],
            ];
        });
    }

    /**
     * الحصول على إحصائيات الحسابات
     */
    public function getStatistics(): array
    {
        return Cache::remember('accounts_statistics', 1800, function () {
            return [
                'total_accounts' => $this->model->count(),
                'active_accounts' => $this->model->where('is_active', true)->count(),
                'inactive_accounts' => $this->model->where('is_active', false)->count(),
                'by_type' => [
                    'assets' => $this->model->where('account_type', 'asset')->count(),
                    'liabilities' => $this->model->where('account_type', 'liability')->count(),
                    'equity' => $this->model->where('account_type', 'equity')->count(),
                    'revenue' => $this->model->where('account_type', 'revenue')->count(),
                    'expenses' => $this->model->where('account_type', 'expense')->count(),
                ],
                'by_level' => [
                    'level_1' => $this->model->where('level', 1)->count(),
                    'level_2' => $this->model->where('level', 2)->count(),
                    'level_3' => $this->model->where('level', 3)->count(),
                    'level_4_plus' => $this->model->where('level', '>=', 4)->count(),
                ],
                'bank_accounts' => $this->model->whereNotNull('iban')->count(),
                'manual_entry_accounts' => $this->model->where('allow_manual_entry', true)->count(),
            ];
        });
    }

    /**
     * تطبيق المرشحات على الاستعلام
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('account_code', 'like', "%{$filters['search']}%")
                  ->orWhere('account_name', 'like', "%{$filters['search']}%")
                  ->orWhere('account_name_en', 'like', "%{$filters['search']}%");
            });
        }

        if (isset($filters['type'])) {
            $query->where('account_type', $filters['type']);
        }

        if (isset($filters['category'])) {
            $query->where('account_category', $filters['category']);
        }

        if (isset($filters['parent_id'])) {
            if ($filters['parent_id'] === 'null') {
                $query->whereNull('parent_account_id');
            } else {
                $query->where('parent_account_id', $filters['parent_id']);
            }
        }

        if (isset($filters['active'])) {
            $query->where('is_active', $filters['active']);
        }

        if (isset($filters['level'])) {
            $query->where('level', $filters['level']);
        }

        if (isset($filters['currency'])) {
            $query->where('currency_code', $filters['currency']);
        }

        if (isset($filters['manual_entry'])) {
            $query->where('allow_manual_entry', $filters['manual_entry']);
        }

        if (isset($filters['bank_account'])) {
            if ($filters['bank_account']) {
                $query->where(function ($q) {
                    $q->whereNotNull('bank_account_number')
                      ->orWhereNotNull('iban');
                });
            }
        }

        return $query->orderBy('account_code');
    }

    /**
     * مسح الكاش
     */
    public function clearCache(): void
    {
        Cache::tags(['accounts'])->flush();
    }

    /**
     * الحصول على الحسابات المحذوفة
     */
    public function getTrashedAccounts(): Collection
    {
        return $this->model->onlyTrashed()->get();
    }

    /**
     * استعادة حساب محذوف
     */
    public function restore(int $id): bool
    {
        $account = $this->model->onlyTrashed()->find($id);
        return $account ? $account->restore() : false;
    }

    /**
     * حذف نهائي للحساب
     */
    public function forceDelete(int $id): bool
    {
        $account = $this->model->onlyTrashed()->find($id);
        return $account ? $account->forceDelete() : false;
    }
}
