<?php

namespace App\Domains\Integration\Services\Transformation\Processors;

use App\Domains\Integration\Contracts\ProcessorInterface;
use App\Domains\Integration\Exceptions\TransformationException;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use SimpleXMLElement;

/**
 * XML Data Processor
 * Handles XML data transformation, validation, and processing
 */
class XmlProcessor implements ProcessorInterface
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'encoding' => 'UTF-8',
            'version' => '1.0',
            'format_output' => true,
            'preserve_whitespace' => false,
            'validate_on_parse' => true,
        ], $config);
    }

    /**
     * Process XML data
     */
    public function process(mixed $data, array $options = []): array
    {
        try {
            if (is_array($data)) {
                return $data;
            }

            if (is_string($data)) {
                return $this->xmlToArray($data, $options);
            }

            if ($data instanceof SimpleXMLElement) {
                return $this->simpleXmlToArray($data);
            }

            if ($data instanceof DOMDocument) {
                return $this->domToArray($data);
            }

            throw new TransformationException('Invalid data type for XML processing');
        } catch (\Exception $e) {
            Log::error('XML processing failed', [
                'error' => $e->getMessage(),
                'data_type' => gettype($data),
            ]);
            throw new TransformationException('XML processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Convert XML string to array
     */
    public function xmlToArray(string $xml, array $options = []): array
    {
        unset($options);
        try {
            // Remove BOM if present
            $xml = $this->removeBom($xml);

            // Load XML
            $simpleXml = new SimpleXMLElement($xml, LIBXML_NOCDATA);

            return $this->simpleXmlToArray($simpleXml);
        } catch (\Exception $e) {
            throw new TransformationException('XML parsing failed: ' . $e->getMessage());
        }
    }

    /**
     * Convert SimpleXMLElement to array
     */
    public function simpleXmlToArray(SimpleXMLElement $xml): array
    {
        $array = [];

        // Get attributes
        $attributes = $xml->attributes();
        if ($attributes) {
            foreach ($attributes as $key => $value) {
                $array['@attributes'][$key] = (string) $value;
            }
        }

        // Get children
        $children = $xml->children();
        if ($children->count() > 0) {
            foreach ($children as $child) {
                $name = $child->getName();
                $value = $this->simpleXmlToArray($child);

                if (isset($array[$name])) {
                    if (!is_array($array[$name]) || !isset($array[$name][0])) {
                        $array[$name] = [$array[$name]];
                    }
                    $array[$name][] = $value;
                } else {
                    $array[$name] = $value;
                }
            }
        } else {
            // Leaf node
            $value = (string) $xml;
            if (!empty($value)) {
                if (isset($array['@attributes'])) {
                    $array['@value'] = $value;
                } else {
                    $array = ['value' => $value];
                }
            }
        }

        return $array;
    }

    /**
     * Convert DOMDocument to array
     */
    public function domToArray(DOMDocument $dom): array
    {
        $xml = $dom->saveXML();
        return $this->xmlToArray($xml);
    }

    /**
     * Convert array to XML string
     */
    public function arrayToXml(array $data, array $options = []): string
    {
        $rootElement = $options['root_element'] ?? 'root';
        $encoding = $options['encoding'] ?? $this->config['encoding'];
        $version = $options['version'] ?? $this->config['version'];

        $dom = new DOMDocument($version, $encoding);
        $dom->formatOutput = $options['format_output'] ?? $this->config['format_output'];
        $dom->preserveWhiteSpace = $options['preserve_whitespace'] ?? $this->config['preserve_whitespace'];

        $root = $dom->createElement($rootElement);
        $dom->appendChild($root);

        $this->arrayToXmlRecursive($data, $root, $dom);

        return $dom->saveXML();
    }

    /**
     * Recursive helper for array to XML conversion
     */
    protected function arrayToXmlRecursive(array $data, \DOMElement $parent, DOMDocument $dom): void
    {
        foreach ($data as $key => $value) {
            if ($key === '@attributes') {
                // Handle attributes
                foreach ($value as $attrName => $attrValue) {
                    $parent->setAttribute($attrName, $attrValue);
                }
            } elseif ($key === '@value') {
                // Handle text content
                $parent->appendChild($dom->createTextNode($value));
            } elseif (is_array($value)) {
                if (isset($value[0])) {
                    // Multiple elements with same name
                    foreach ($value as $item) {
                        $element = $dom->createElement($key);
                        $parent->appendChild($element);

                        if (is_array($item)) {
                            $this->arrayToXmlRecursive($item, $element, $dom);
                        } else {
                            $element->appendChild($dom->createTextNode($item));
                        }
                    }
                } else {
                    // Single element
                    $element = $dom->createElement($key);
                    $parent->appendChild($element);
                    $this->arrayToXmlRecursive($value, $element, $dom);
                }
            } else {
                // Simple value
                $element = $dom->createElement($key);
                $element->appendChild($dom->createTextNode($value));
                $parent->appendChild($element);
            }
        }
    }

    /**
     * Validate XML against XSD schema
     */
    public function validate(mixed $data, array $schema = []): bool
    {
        try {
            if (is_string($data)) {
                $dom = new DOMDocument();
                $dom->loadXML($data);

                if (!empty($schema['xsd'])) {
                    return $dom->schemaValidate($schema['xsd']);
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('XML validation failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Transform XML data using XSLT
     */
    public function transform(array $data, array $mapping): array
    {
        // For now, implement basic key mapping
        $result = [];

        foreach ($mapping as $targetKey => $sourceKey) {
            $result[$targetKey] = $this->getNestedValue($data, $sourceKey);
        }

        return $result;
    }

    /**
     * Get nested value from array using dot notation
     */
    protected function getNestedValue(array $data, string $key): mixed
    {
        $keys = explode('.', $key);
        $value = $data;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return null;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Remove BOM from XML string
     */
    protected function removeBom(string $xml): string
    {
        $bom = pack('H*','EFBBBF');
        $xml = preg_replace("/^$bom/", '', $xml);
        return $xml;
    }

    /**
     * Get supported formats
     */
    public function getSupportedFormats(): array
    {
        return ['xml', 'application/xml', 'text/xml'];
    }

    /**
     * Check if format is supported
     */
    public function supports(string $format): bool
    {
        return in_array(strtolower($format), $this->getSupportedFormats());
    }

    /**
     * Pretty print XML
     */
    public function prettyPrint(string $xml): string
    {
        $dom = new DOMDocument();
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($xml);

        return $dom->saveXML();
    }
}
