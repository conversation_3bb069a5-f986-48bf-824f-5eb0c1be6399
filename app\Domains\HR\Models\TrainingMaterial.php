<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج مادة التدريب
 */
class TrainingMaterial extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'training_program_id',
        'title',
        'description',
        'type',
        'file_path',
        'file_size',
        'file_type',
        'url',
        'content',
        'is_required',
        'sort_order',
        'access_level',
        'uploaded_by',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'file_size' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * أنواع المواد التدريبية
     */
    public const TYPES = [
        'DOCUMENT' => 'مستند',
        'VIDEO' => 'فيديو',
        'AUDIO' => 'صوت',
        'PRESENTATION' => 'عرض تقديمي',
        'LINK' => 'رابط',
        'TEXT' => 'نص',
        'QUIZ' => 'اختبار',
        'ASSIGNMENT' => 'مهمة',
    ];

    /**
     * مستويات الوصول
     */
    public const ACCESS_LEVELS = [
        'PUBLIC' => 'عام',
        'ENROLLED_ONLY' => 'للمسجلين فقط',
        'COMPLETED_ONLY' => 'للمكملين فقط',
        'INSTRUCTOR_ONLY' => 'للمدربين فقط',
    ];

    /**
     * البرنامج التدريبي
     */
    public function trainingProgram(): BelongsTo
    {
        return $this->belongsTo(TrainingProgram::class);
    }

    /**
     * من قام بالرفع
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'uploaded_by');
    }

    /**
     * الحصول على اسم النوع
     */
    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * الحصول على اسم مستوى الوصول
     */
    public function getAccessLevelNameAttribute(): string
    {
        return self::ACCESS_LEVELS[$this->access_level] ?? $this->access_level;
    }

    /**
     * الحصول على حجم الملف المنسق
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'غير محدد';
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * فحص إذا كانت المادة ملف
     */
    public function isFile(): bool
    {
        return !empty($this->file_path);
    }

    /**
     * فحص إذا كانت المادة رابط
     */
    public function isLink(): bool
    {
        return $this->type === 'LINK' && !empty($this->url);
    }

    /**
     * فحص إذا كانت المادة نص
     */
    public function isText(): bool
    {
        return $this->type === 'TEXT' && !empty($this->content);
    }

    /**
     * الحصول على رابط التحميل
     */
    public function getDownloadUrlAttribute(): ?string
    {
        if (!$this->isFile()) {
            return null;
        }
        
        return route('training.materials.download', $this->id);
    }

    /**
     * فحص إمكانية الوصول للمادة
     */
    public function canAccess(Employee $employee): bool
    {
        switch ($this->access_level) {
            case 'PUBLIC':
                return true;
                
            case 'ENROLLED_ONLY':
                return $this->trainingProgram->enrollments()
                    ->where('employee_id', $employee->id)
                    ->exists();
                    
            case 'COMPLETED_ONLY':
                return $this->trainingProgram->enrollments()
                    ->where('employee_id', $employee->id)
                    ->where('status', 'COMPLETED')
                    ->exists();
                    
            case 'INSTRUCTOR_ONLY':
                return $this->trainingProgram->instructor_id === $employee->id;
                
            default:
                return false;
        }
    }

    /**
     * Scope للمواد المطلوبة
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope للمواد حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope للترتيب
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('title');
    }
}
