<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج قاعدة مطابقة البنك
 * يحدد قواعد المطابقة التلقائية للمعاملات البنكية
 */
class BankMatchingRule extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'bank_account_id',
        'rule_type',
        'conditions',
        'actions',
        'priority',
        'is_active',
        'match_count',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'conditions' => 'array',
        'actions' => 'array',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الحساب البنكي
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * التحقق من تطابق القاعدة مع المعاملة
     */
    public function matches(BankTransaction $transaction): bool
    {
        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $transaction)) {
                return false;
            }
        }
        return true;
    }

    /**
     * تقييم شرط واحد
     */
    protected function evaluateCondition(array $condition, BankTransaction $transaction): bool
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];

        $transactionValue = $transaction->{$field};

        switch ($operator) {
            case 'equals':
                return $transactionValue == $value;
            case 'contains':
                return strpos(strtolower($transactionValue), strtolower($value)) !== false;
            case 'starts_with':
                return strpos(strtolower($transactionValue), strtolower($value)) === 0;
            case 'greater_than':
                return $transactionValue > $value;
            case 'less_than':
                return $transactionValue < $value;
            case 'regex':
                return preg_match($value, $transactionValue);
            default:
                return false;
        }
    }

    /**
     * تطبيق الإجراءات على المعاملة
     */
    public function applyActions(BankTransaction $transaction): void
    {
        foreach ($this->actions as $action) {
            $this->executeAction($action, $transaction);
        }

        $this->increment('match_count');
    }

    /**
     * تنفيذ إجراء واحد
     */
    protected function executeAction(array $action, BankTransaction $transaction): void
    {
        switch ($action['type']) {
            case 'set_category':
                $transaction->update(['category' => $action['value']]);
                break;
            case 'create_journal_entry':
                $transaction->createJournalEntry($action['account_id'], $action['description'] ?? null);
                break;
            case 'set_status':
                $transaction->update(['status' => $action['value']]);
                break;
        }
    }
}
