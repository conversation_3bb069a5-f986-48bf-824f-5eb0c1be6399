<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceProduct;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث مزامنة المنتج
 * يتم إطلاقه عند مزامنة منتج مع المنصة
 */
class ProductSynced
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceProduct $product;
    public string $action;
    public ECommerceIntegration $integration;
    public array $syncData;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceProduct $product,
        string $action,
        ECommerceIntegration $integration,
        array $syncData = []
    ) {
        $this->product = $product;
        $this->action = $action;
        $this->integration = $integration;
        $this->syncData = $syncData;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'product_id' => $this->product->id,
            'external_id' => $this->product->external_id,
            'product_name' => $this->product->name,
            'product_sku' => $this->product->sku,
            'action' => $this->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'synced_at' => $this->product->last_synced_at,
            'sync_data' => $this->syncData,
        ];
    }

    /**
     * تحديد ما إذا كان المنتج جديد
     */
    public function isNewProduct(): bool
    {
        return $this->action === 'created';
    }

    /**
     * تحديد ما إذا كان المنتج محدث
     */
    public function isUpdatedProduct(): bool
    {
        return $this->action === 'updated';
    }

    /**
     * تحديد ما إذا كان المنتج محذوف
     */
    public function isDeletedProduct(): bool
    {
        return $this->action === 'deleted';
    }
}
