<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج سجل الحضور والانصراف
 * يدعم طرق متعددة للتسجيل (بصمة، GPS، Wi-Fi، تطبيق جوال)
 */
class AttendanceRecord extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'employee_id',
        'date',
        'check_in_time',
        'check_out_time',
        'break_start_time',
        'break_end_time',
        'total_hours',
        'regular_hours',
        'overtime_hours',
        'break_duration',
        'status',
        'check_in_method',
        'check_out_method',
        'check_in_location',
        'check_out_location',
        'check_in_ip',
        'check_out_ip',
        'check_in_device',
        'check_out_device',
        'biometric_id',
        'is_late',
        'is_early_departure',
        'late_minutes',
        'early_departure_minutes',
        'notes',
        'approved_by',
        'approved_at',
        'metadata',
    ];

    protected $casts = [
        'date' => 'date',
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
        'break_start_time' => 'datetime',
        'break_end_time' => 'datetime',
        'total_hours' => 'decimal:2',
        'regular_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'break_duration' => 'decimal:2',
        'is_late' => 'boolean',
        'is_early_departure' => 'boolean',
        'approved_at' => 'datetime',
        'check_in_location' => 'array',
        'check_out_location' => 'array',
        'metadata' => 'array',
    ];

    /**
     * حالات الحضور
     */
    const STATUSES = [
        'PRESENT' => 'حاضر',
        'ABSENT' => 'غائب',
        'LATE' => 'متأخر',
        'EARLY_DEPARTURE' => 'انصراف مبكر',
        'HALF_DAY' => 'نصف يوم',
        'ON_LEAVE' => 'في إجازة',
        'SICK_LEAVE' => 'إجازة مرضية',
        'BUSINESS_TRIP' => 'مهمة عمل',
        'REMOTE_WORK' => 'عمل عن بُعد',
        'HOLIDAY' => 'عطلة رسمية',
    ];

    /**
     * طرق التسجيل
     */
    const CHECK_METHODS = [
        'BIOMETRIC' => 'بصمة',
        'RFID_CARD' => 'بطاقة RFID',
        'MOBILE_APP' => 'تطبيق جوال',
        'WEB_PORTAL' => 'البوابة الإلكترونية',
        'GPS' => 'GPS',
        'WIFI' => 'Wi-Fi',
        'QR_CODE' => 'رمز QR',
        'MANUAL' => 'يدوي',
        'FACIAL_RECOGNITION' => 'التعرف على الوجه',
    ];

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * الشخص الذي وافق على السجل
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * حساب إجمالي ساعات العمل
     */
    public function calculateTotalHours(): void
    {
        if (!$this->check_in_time || !$this->check_out_time) {
            return;
        }

        $totalMinutes = $this->check_in_time->diffInMinutes($this->check_out_time);
        
        // طرح وقت الاستراحة
        if ($this->break_start_time && $this->break_end_time) {
            $breakMinutes = $this->break_start_time->diffInMinutes($this->break_end_time);
            $this->break_duration = $breakMinutes / 60;
            $totalMinutes -= $breakMinutes;
        }

        $this->total_hours = $totalMinutes / 60;

        // حساب الساعات العادية والإضافية
        $this->calculateRegularAndOvertimeHours();
    }

    /**
     * حساب الساعات العادية والإضافية
     */
    protected function calculateRegularAndOvertimeHours(): void
    {
        $workSchedule = $this->employee->getWorkSchedule($this->date);
        $standardHours = $workSchedule['daily_hours'] ?? 8;

        if ($this->total_hours <= $standardHours) {
            $this->regular_hours = $this->total_hours;
            $this->overtime_hours = 0;
        } else {
            $this->regular_hours = $standardHours;
            $this->overtime_hours = $this->total_hours - $standardHours;
        }
    }

    /**
     * التحقق من التأخير
     */
    public function checkLateness(): void
    {
        if (!$this->check_in_time) {
            return;
        }

        $workSchedule = $this->employee->getWorkSchedule($this->date);
        $expectedStartTime = $workSchedule['start_time'] ?? '09:00';
        
        $expectedStart = \Carbon\Carbon::parse($this->date->format('Y-m-d') . ' ' . $expectedStartTime);
        $graceMinutes = $workSchedule['grace_minutes'] ?? 15;
        
        if ($this->check_in_time > $expectedStart->addMinutes($graceMinutes)) {
            $this->is_late = true;
            $this->late_minutes = $expectedStart->diffInMinutes($this->check_in_time);
            
            if ($this->status === 'PRESENT') {
                $this->status = 'LATE';
            }
        }
    }

    /**
     * التحقق من الانصراف المبكر
     */
    public function checkEarlyDeparture(): void
    {
        if (!$this->check_out_time) {
            return;
        }

        $workSchedule = $this->employee->getWorkSchedule($this->date);
        $expectedEndTime = $workSchedule['end_time'] ?? '17:00';
        
        $expectedEnd = \Carbon\Carbon::parse($this->date->format('Y-m-d') . ' ' . $expectedEndTime);
        
        if ($this->check_out_time < $expectedEnd) {
            $this->is_early_departure = true;
            $this->early_departure_minutes = $this->check_out_time->diffInMinutes($expectedEnd);
            
            if ($this->status === 'PRESENT') {
                $this->status = 'EARLY_DEPARTURE';
            }
        }
    }

    /**
     * تسجيل الدخول
     */
    public function checkIn(array $data = []): bool
    {
        if ($this->check_in_time) {
            return false; // تم التسجيل مسبقاً
        }

        $this->update([
            'check_in_time' => now(),
            'check_in_method' => $data['method'] ?? 'MANUAL',
            'check_in_location' => $data['location'] ?? null,
            'check_in_ip' => $data['ip'] ?? request()->ip(),
            'check_in_device' => $data['device'] ?? request()->userAgent(),
            'biometric_id' => $data['biometric_id'] ?? null,
            'status' => 'PRESENT',
        ]);

        $this->checkLateness();
        $this->save();

        return true;
    }

    /**
     * تسجيل الخروج
     */
    public function checkOut(array $data = []): bool
    {
        if (!$this->check_in_time || $this->check_out_time) {
            return false;
        }

        $this->update([
            'check_out_time' => now(),
            'check_out_method' => $data['method'] ?? 'MANUAL',
            'check_out_location' => $data['location'] ?? null,
            'check_out_ip' => $data['ip'] ?? request()->ip(),
            'check_out_device' => $data['device'] ?? request()->userAgent(),
        ]);

        $this->calculateTotalHours();
        $this->checkEarlyDeparture();
        $this->save();

        return true;
    }

    /**
     * بدء الاستراحة
     */
    public function startBreak(): bool
    {
        if (!$this->check_in_time || $this->break_start_time) {
            return false;
        }

        $this->update(['break_start_time' => now()]);
        return true;
    }

    /**
     * انتهاء الاستراحة
     */
    public function endBreak(): bool
    {
        if (!$this->break_start_time || $this->break_end_time) {
            return false;
        }

        $this->update(['break_end_time' => now()]);
        $this->calculateTotalHours();
        $this->save();

        return true;
    }

    /**
     * الموافقة على السجل
     */
    public function approve(Employee $approver): bool
    {
        $this->update([
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * التحقق من صحة الموقع
     */
    public function validateLocation(array $location, array $allowedLocations): bool
    {
        foreach ($allowedLocations as $allowedLocation) {
            $distance = $this->calculateDistance(
                $location['latitude'],
                $location['longitude'],
                $allowedLocation['latitude'],
                $allowedLocation['longitude']
            );

            if ($distance <= ($allowedLocation['radius'] ?? 100)) { // 100 متر افتراضياً
                return true;
            }
        }

        return false;
    }

    /**
     * حساب المسافة بين نقطتين
     */
    protected function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371000; // نصف قطر الأرض بالمتر

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * الحصول على ملخص اليوم
     */
    public function getDailySummary(): array
    {
        return [
            'date' => $this->date->format('Y-m-d'),
            'status' => $this->status,
            'check_in' => $this->check_in_time?->format('H:i'),
            'check_out' => $this->check_out_time?->format('H:i'),
            'total_hours' => $this->total_hours,
            'regular_hours' => $this->regular_hours,
            'overtime_hours' => $this->overtime_hours,
            'break_duration' => $this->break_duration,
            'is_late' => $this->is_late,
            'late_minutes' => $this->late_minutes,
            'is_early_departure' => $this->is_early_departure,
            'early_departure_minutes' => $this->early_departure_minutes,
        ];
    }

    /**
     * نطاق للتاريخ
     */
    public function scopeForDate($query, \Carbon\Carbon $date)
    {
        return $query->whereDate('date', $date);
    }

    /**
     * نطاق للفترة
     */
    public function scopeForPeriod($query, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * نطاق للموظف
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق للمتأخرين
     */
    public function scopeLate($query)
    {
        return $query->where('is_late', true);
    }

    /**
     * نطاق للانصراف المبكر
     */
    public function scopeEarlyDeparture($query)
    {
        return $query->where('is_early_departure', true);
    }

    /**
     * نطاق للساعات الإضافية
     */
    public function scopeWithOvertime($query)
    {
        return $query->where('overtime_hours', '>', 0);
    }

    /**
     * نطاق للسجلات غير المكتملة
     */
    public function scopeIncomplete($query)
    {
        return $query->where(function ($q) {
            $q->whereNotNull('check_in_time')
              ->whereNull('check_out_time');
        });
    }

    /**
     * نطاق للسجلات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    /**
     * نطاق للسجلات غير المعتمدة
     */
    public function scopePendingApproval($query)
    {
        return $query->whereNull('approved_at');
    }
}
