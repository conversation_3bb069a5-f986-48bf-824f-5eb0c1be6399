<?php

namespace App\Domains\Compliance\Repositories;

use App\Domains\Compliance\Models\ComplianceActivity;
use App\Domains\Compliance\Models\Company;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

/**
 * مستودع أنشطة الامتثال
 */
class ComplianceActivityRepository
{
    /**
     * الحصول على جميع الأنشطة مع التصفية
     */
    public function getAll(array $filters = []): LengthAwarePaginator
    {
        $query = ComplianceActivity::with(['company', 'country', 'complianceRule', 'assignedUser'])
            ->when(isset($filters['company_id']), fn($q) => $q->where('company_id', $filters['company_id']))
            ->when(isset($filters['country_id']), fn($q) => $q->where('country_id', $filters['country_id']))
            ->when(isset($filters['status']), fn($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['priority']), fn($q) => $q->where('priority', $filters['priority']))
            ->when(isset($filters['activity_type']), fn($q) => $q->where('activity_type', $filters['activity_type']))
            ->when(isset($filters['assigned_to']), fn($q) => $q->where('assigned_to', $filters['assigned_to']))
            ->when(isset($filters['due_date_from']), fn($q) => $q->where('due_date', '>=', $filters['due_date_from']))
            ->when(isset($filters['due_date_to']), fn($q) => $q->where('due_date', '<=', $filters['due_date_to']))
            ->when(isset($filters['search']), function($q) use ($filters) {
                $q->where(function($query) use ($filters) {
                    $query->where('title', 'like', "%{$filters['search']}%")
                          ->orWhere('description', 'like', "%{$filters['search']}%");
                });
            })
            ->orderBy($filters['sort_by'] ?? 'created_at', $filters['sort_direction'] ?? 'desc');

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * الحصول على الأنشطة المتأخرة
     */
    public function getOverdueActivities(): Collection
    {
        return ComplianceActivity::overdue()
            ->with(['company', 'country', 'assignedUser'])
            ->orderBy('due_date', 'asc')
            ->get();
    }

    /**
     * الحصول على الأنشطة المستحقة اليوم
     */
    public function getActivitiesDueToday(): Collection
    {
        return ComplianceActivity::dueToday()
            ->with(['company', 'country', 'assignedUser'])
            ->orderBy('priority', 'desc')
            ->get();
    }

    /**
     * الحصول على الأنشطة المستحقة هذا الأسبوع
     */
    public function getActivitiesDueThisWeek(): Collection
    {
        return ComplianceActivity::dueThisWeek()
            ->with(['company', 'country', 'assignedUser'])
            ->orderBy('due_date', 'asc')
            ->get();
    }

    /**
     * الحصول على الأنشطة عالية الأولوية
     */
    public function getHighPriorityActivities(): Collection
    {
        return ComplianceActivity::highPriority()
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['company', 'country', 'assignedUser'])
            ->orderBy('priority', 'desc')
            ->orderBy('due_date', 'asc')
            ->get();
    }

    /**
     * الحصول على أنشطة شركة معينة
     */
    public function getCompanyActivities(int $companyId, array $filters = []): Collection
    {
        $query = ComplianceActivity::where('company_id', $companyId)
            ->with(['country', 'complianceRule', 'assignedUser'])
            ->when(isset($filters['status']), fn($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['country_id']), fn($q) => $q->where('country_id', $filters['country_id']));

        return $query->orderBy('due_date', 'asc')->get();
    }

    /**
     * الحصول على أنشطة مستخدم معين
     */
    public function getUserActivities(int $userId, array $filters = []): Collection
    {
        $query = ComplianceActivity::where('assigned_to', $userId)
            ->with(['company', 'country', 'complianceRule'])
            ->when(isset($filters['status']), fn($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['priority']), fn($q) => $q->where('priority', $filters['priority']));

        return $query->orderBy('due_date', 'asc')->get();
    }

    /**
     * الحصول على أنشطة دولة معينة
     */
    public function getCountryActivities(string $countryCode, array $filters = []): Collection
    {
        $query = ComplianceActivity::whereHas('country', fn($q) => $q->where('code', $countryCode))
            ->with(['company', 'complianceRule', 'assignedUser'])
            ->when(isset($filters['status']), fn($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['company_id']), fn($q) => $q->where('company_id', $filters['company_id']));

        return $query->orderBy('due_date', 'asc')->get();
    }

    /**
     * إنشاء نشاط جديد
     */
    public function create(array $data): ComplianceActivity
    {
        return ComplianceActivity::createActivity($data);
    }

    /**
     * تحديث نشاط
     */
    public function update(ComplianceActivity $activity, array $data): bool
    {
        return $activity->update($data);
    }

    /**
     * تحديث حالة النشاط
     */
    public function updateStatus(ComplianceActivity $activity, string $status, array $data = []): void
    {
        $activity->updateStatus($status, $data);
    }

    /**
     * تعيين نشاط لمستخدم
     */
    public function assignToUser(ComplianceActivity $activity, int $userId): bool
    {
        return $activity->update(['assigned_to' => $userId]);
    }

    /**
     * تصعيد نشاط
     */
    public function escalate(ComplianceActivity $activity, string $reason = null): void
    {
        $activity->escalate($reason);
    }

    /**
     * حذف نشاط
     */
    public function delete(ComplianceActivity $activity): bool
    {
        return $activity->delete();
    }

    /**
     * الحصول على إحصائيات الأنشطة
     */
    public function getStatistics(array $filters = []): array
    {
        $query = ComplianceActivity::query();

        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['country_id'])) {
            $query->where('country_id', $filters['country_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return [
            'total' => $query->count(),
            'pending' => $query->where('status', 'pending')->count(),
            'in_progress' => $query->where('status', 'in_progress')->count(),
            'completed' => $query->where('status', 'completed')->count(),
            'overdue' => $query->where('due_date', '<', now())
                              ->whereNotIn('status', ['completed', 'cancelled'])
                              ->count(),
            'due_today' => $query->whereDate('due_date', today())
                                ->whereNotIn('status', ['completed', 'cancelled'])
                                ->count(),
            'due_this_week' => $query->whereBetween('due_date', [now()->startOfWeek(), now()->endOfWeek()])
                                    ->whereNotIn('status', ['completed', 'cancelled'])
                                    ->count(),
            'high_priority' => $query->whereIn('priority', ['high', 'urgent', 'critical'])
                                    ->whereNotIn('status', ['completed', 'cancelled'])
                                    ->count(),
            'by_type' => $query->groupBy('activity_type')
                              ->selectRaw('activity_type, count(*) as count')
                              ->pluck('count', 'activity_type')
                              ->toArray(),
            'by_status' => $query->groupBy('status')
                                ->selectRaw('status, count(*) as count')
                                ->pluck('count', 'status')
                                ->toArray(),
        ];
    }

    /**
     * الحصول على الأنشطة المكتملة في فترة معينة
     */
    public function getCompletedActivitiesInPeriod(Carbon $startDate, Carbon $endDate): Collection
    {
        return ComplianceActivity::where('status', 'completed')
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->with(['company', 'country', 'complianceRule'])
            ->orderBy('completed_at', 'desc')
            ->get();
    }

    /**
     * الحصول على متوسط وقت إكمال الأنشطة
     */
    public function getAverageCompletionTime(array $filters = []): float
    {
        $query = ComplianceActivity::where('status', 'completed')
            ->whereNotNull('completed_at');

        if (isset($filters['activity_type'])) {
            $query->where('activity_type', $filters['activity_type']);
        }

        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        $activities = $query->get();

        if ($activities->isEmpty()) {
            return 0;
        }

        $totalHours = $activities->sum(function($activity) {
            return $activity->created_at->diffInHours($activity->completed_at);
        });

        return round($totalHours / $activities->count(), 2);
    }

    /**
     * البحث في الأنشطة
     */
    public function search(string $term, array $filters = []): Collection
    {
        $query = ComplianceActivity::where(function($q) use ($term) {
            $q->where('title', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('activity_id', 'like', "%{$term}%");
        });

        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->with(['company', 'country', 'assignedUser'])->get();
    }

    /**
     * تحديث الأنشطة المتأخرة
     */
    public function markOverdueActivities(): int
    {
        return ComplianceActivity::where('due_date', '<', now())
            ->whereIn('status', ['pending', 'in_progress'])
            ->update(['status' => 'overdue']);
    }

    /**
     * الحصول على الأنشطة المؤتمتة
     */
    public function getAutomatedActivities(): Collection
    {
        return ComplianceActivity::automated()
            ->with(['company', 'country'])
            ->get();
    }

    /**
     * الحصول على الأنشطة اليدوية
     */
    public function getManualActivities(): Collection
    {
        return ComplianceActivity::manual()
            ->with(['company', 'country', 'assignedUser'])
            ->get();
    }
}
