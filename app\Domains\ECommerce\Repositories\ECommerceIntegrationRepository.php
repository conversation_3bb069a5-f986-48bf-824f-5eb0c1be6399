<?php

namespace App\Domains\ECommerce\Repositories;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommercePlatform;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * مستودع تكاملات التجارة الإلكترونية
 */
class ECommerceIntegrationRepository
{
    /**
     * الحصول على جميع التكاملات مع التصفية
     */
    public function getAll(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = ECommerceIntegration::with(['platform', 'store'])
            ->when(isset($filters['platform_id']), function ($q) use ($filters) {
                return $q->where('platform_id', $filters['platform_id']);
            })
            ->when(isset($filters['store_id']), function ($q) use ($filters) {
                return $q->where('store_id', $filters['store_id']);
            })
            ->when(isset($filters['status']), function ($q) use ($filters) {
                return $q->where('status', $filters['status']);
            })
            ->when(isset($filters['search']), function ($q) use ($filters) {
                return $q->where(function ($query) use ($filters) {
                    $query->where('name', 'like', "%{$filters['search']}%")
                          ->orWhere('description', 'like', "%{$filters['search']}%");
                });
            })
            ->when(isset($filters['created_from']), function ($q) use ($filters) {
                return $q->where('created_at', '>=', $filters['created_from']);
            })
            ->when(isset($filters['created_to']), function ($q) use ($filters) {
                return $q->where('created_at', '<=', $filters['created_to']);
            })
            ->when(isset($filters['last_sync_from']), function ($q) use ($filters) {
                return $q->where('last_sync_at', '>=', $filters['last_sync_from']);
            })
            ->when(isset($filters['last_sync_to']), function ($q) use ($filters) {
                return $q->where('last_sync_at', '<=', $filters['last_sync_to']);
            });

        // ترتيب النتائج
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * الحصول على تكامل بالمعرف
     */
    public function findById(int $id): ?ECommerceIntegration
    {
        return ECommerceIntegration::with(['platform', 'store', 'syncLogs'])
            ->find($id);
    }

    /**
     * الحصول على تكامل بالمنصة والمتجر
     */
    public function findByPlatformAndStore(int $platformId, int $storeId): ?ECommerceIntegration
    {
        return ECommerceIntegration::where('platform_id', $platformId)
            ->where('store_id', $storeId)
            ->first();
    }

    /**
     * إنشاء تكامل جديد
     */
    public function create(array $data): ECommerceIntegration
    {
        return DB::transaction(function () use ($data) {
            $integration = ECommerceIntegration::create($data);
            
            // تسجيل إنشاء التكامل
            activity()
                ->performedOn($integration)
                ->withProperties(['action' => 'created'])
                ->log('تم إنشاء تكامل جديد');

            return $integration->load(['platform', 'store']);
        });
    }

    /**
     * تحديث تكامل موجود
     */
    public function update(ECommerceIntegration $integration, array $data): ECommerceIntegration
    {
        return DB::transaction(function () use ($integration, $data) {
            $oldData = $integration->toArray();
            $integration->update($data);
            
            // تسجيل تحديث التكامل
            activity()
                ->performedOn($integration)
                ->withProperties([
                    'action' => 'updated',
                    'old' => $oldData,
                    'new' => $integration->fresh()->toArray(),
                ])
                ->log('تم تحديث التكامل');

            return $integration->fresh(['platform', 'store']);
        });
    }

    /**
     * حذف تكامل
     */
    public function delete(ECommerceIntegration $integration): bool
    {
        return DB::transaction(function () use ($integration) {
            // تسجيل حذف التكامل
            activity()
                ->performedOn($integration)
                ->withProperties(['action' => 'deleted'])
                ->log('تم حذف التكامل');

            return $integration->delete();
        });
    }

    /**
     * الحصول على التكاملات النشطة
     */
    public function getActive(): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where('status', 'active')
            ->get();
    }

    /**
     * الحصول على التكاملات التي تحتاج مزامنة
     */
    public function getNeedingSync(): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereJsonPath('sync_config->auto_sync', true)
                      ->where(function ($q) {
                          $q->whereNull('last_sync_at')
                            ->orWhere('last_sync_at', '<', function ($subQuery) {
                                $subQuery->selectRaw('DATE_SUB(NOW(), INTERVAL JSON_EXTRACT(sync_config, "$.sync_interval") MINUTE)');
                            });
                      });
            })
            ->get();
    }

    /**
     * الحصول على التكاملات حسب المنصة
     */
    public function getByPlatform(string $platformSlug): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->whereHas('platform', function ($query) use ($platformSlug) {
                $query->where('slug', $platformSlug);
            })
            ->get();
    }

    /**
     * الحصول على التكاملات حسب المتجر
     */
    public function getByStore(int $storeId): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where('store_id', $storeId)
            ->get();
    }

    /**
     * الحصول على إحصائيات التكاملات
     */
    public function getStatistics(): array
    {
        $stats = ECommerceIntegration::selectRaw('
            COUNT(*) as total,
            SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = "inactive" THEN 1 ELSE 0 END) as inactive,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = "error" THEN 1 ELSE 0 END) as error,
            SUM(CASE WHEN status = "syncing" THEN 1 ELSE 0 END) as syncing
        ')->first();

        $platformStats = ECommerceIntegration::join('e_commerce_platforms', 'e_commerce_integrations.platform_id', '=', 'e_commerce_platforms.id')
            ->selectRaw('e_commerce_platforms.name as platform_name, COUNT(*) as count')
            ->groupBy('e_commerce_platforms.id', 'e_commerce_platforms.name')
            ->orderByDesc('count')
            ->get();

        $recentSyncs = ECommerceIntegration::whereNotNull('last_sync_at')
            ->where('last_sync_at', '>=', now()->subDays(7))
            ->count();

        return [
            'total_integrations' => $stats->total,
            'active_integrations' => $stats->active,
            'inactive_integrations' => $stats->inactive,
            'pending_integrations' => $stats->pending,
            'error_integrations' => $stats->error,
            'syncing_integrations' => $stats->syncing,
            'platform_distribution' => $platformStats->toArray(),
            'recent_syncs_count' => $recentSyncs,
            'sync_success_rate' => $this->getSyncSuccessRate(),
        ];
    }

    /**
     * الحصول على معدل نجاح المزامنة
     */
    protected function getSyncSuccessRate(): float
    {
        $totalSyncs = DB::table('e_commerce_sync_logs')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        if ($totalSyncs === 0) {
            return 0;
        }

        $successfulSyncs = DB::table('e_commerce_sync_logs')
            ->where('created_at', '>=', now()->subDays(30))
            ->where('is_successful', true)
            ->count();

        return round(($successfulSyncs / $totalSyncs) * 100, 2);
    }

    /**
     * الحصول على التكاملات مع آخر سجلات المزامنة
     */
    public function getWithRecentSyncLogs(int $limit = 10): Collection
    {
        return ECommerceIntegration::with([
            'platform',
            'store',
            'syncLogs' => function ($query) use ($limit) {
                $query->latest()->take($limit);
            }
        ])->get();
    }

    /**
     * البحث في التكاملات
     */
    public function search(string $term, int $limit = 10): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where(function ($query) use ($term) {
                $query->where('name', 'like', "%{$term}%")
                      ->orWhere('description', 'like', "%{$term}%")
                      ->orWhereHas('platform', function ($q) use ($term) {
                          $q->where('name', 'like', "%{$term}%");
                      })
                      ->orWhereHas('store', function ($q) use ($term) {
                          $q->where('name', 'like', "%{$term}%");
                      });
            })
            ->limit($limit)
            ->get();
    }

    /**
     * تحديث حالة المزامنة
     */
    public function updateSyncStatus(ECommerceIntegration $integration, string $status, ?Carbon $syncTime = null): void
    {
        $integration->update([
            'last_sync_status' => $status,
            'last_sync_at' => $syncTime ?? now(),
        ]);
    }

    /**
     * الحصول على التكاملات المتأخرة في المزامنة
     */
    public function getOverdueSyncs(int $hours = 24): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where('status', 'active')
            ->whereJsonPath('sync_config->auto_sync', true)
            ->where(function ($query) use ($hours) {
                $query->whereNull('last_sync_at')
                      ->orWhere('last_sync_at', '<', now()->subHours($hours));
            })
            ->get();
    }

    /**
     * تفعيل التكامل
     */
    public function activate(ECommerceIntegration $integration): ECommerceIntegration
    {
        return $this->update($integration, ['status' => 'active']);
    }

    /**
     * إلغاء تفعيل التكامل
     */
    public function deactivate(ECommerceIntegration $integration): ECommerceIntegration
    {
        return $this->update($integration, ['status' => 'inactive']);
    }

    /**
     * الحصول على التكاملات حسب الحالة
     */
    public function getByStatus(string $status): Collection
    {
        return ECommerceIntegration::with(['platform', 'store'])
            ->where('status', $status)
            ->get();
    }

    /**
     * تحديث إعدادات المزامنة
     */
    public function updateSyncConfig(ECommerceIntegration $integration, array $syncConfig): ECommerceIntegration
    {
        $currentConfig = $integration->sync_config ?? [];
        $newConfig = array_merge($currentConfig, $syncConfig);
        
        return $this->update($integration, ['sync_config' => $newConfig]);
    }

    /**
     * تحديث إعدادات Webhooks
     */
    public function updateWebhookConfig(ECommerceIntegration $integration, array $webhookConfig): ECommerceIntegration
    {
        $currentConfig = $integration->webhook_config ?? [];
        $newConfig = array_merge($currentConfig, $webhookConfig);
        
        return $this->update($integration, ['webhook_config' => $newConfig]);
    }
}
