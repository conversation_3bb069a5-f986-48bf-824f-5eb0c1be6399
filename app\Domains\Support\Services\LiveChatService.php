<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\LiveChat;
use App\Domains\Support\Models\LiveChatMessage;
use App\Domains\Support\Models\KnowledgeBaseArticle;
use App\Domains\Support\Models\Ticket;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;

/**
 * خدمة الدردشة الحية - Live Chat Service
 * تدير الدردشة الحية مع البوت الذكي والتحويل للوكلاء
 */
class LiveChatService
{
    protected IntelligentChatBotService $chatBotService;
    protected AITicketClassificationService $aiClassificationService;

    public function __construct(
        IntelligentChatBotService $chatBotService,
        AITicketClassificationService $aiClassificationService
    ) {
        $this->chatBotService = $chatBotService;
        $this->aiClassificationService = $aiClassificationService;
    }

    /**
     * بدء جلسة دردشة جديدة
     */
    public function startChatSession(array $sessionData): LiveChat
    {
        $sessionData = array_merge([
            'session_id' => $this->generateSessionId(),
            'status' => 'waiting',
            'channel' => 'web',
            'platform' => 'website',
            'language' => $this->detectLanguage($sessionData['initial_message'] ?? ''),
            'started_at' => now(),
            'is_bot_handled' => true,
            'bot_confidence' => 0.8,
        ], $sessionData);

        $chat = LiveChat::create($sessionData);

        // إرسال رسالة ترحيب من البوت
        $this->sendBotWelcomeMessage($chat);

        // معالجة الرسالة الأولى إذا وجدت
        if (!empty($sessionData['initial_message'])) {
            $this->processCustomerMessage($chat->id, $sessionData['initial_message']);
        }

        return $chat;
    }

    /**
     * معالجة رسالة العميل
     */
    public function processCustomerMessage(int $chatId, string $message, array $metadata = []): array
    {
        $chat = LiveChat::findOrFail($chatId);

        // إضافة رسالة العميل
        $customerMessage = $this->addMessage($chat, [
            'sender_type' => 'customer',
            'sender_id' => $chat->customer_id,
            'content' => $message,
            'message_type' => 'text',
            'metadata' => $metadata,
        ]);

        // تحليل الرسالة بالذكاء الاصطناعي
        $analysis = $this->aiClassificationService->analyzeReply($message);

        // تحديث تحليل الدردشة
        $this->updateChatAnalysis($chat, $analysis);

        // تحديد نوع الاستجابة
        $responseType = $this->determineResponseType($chat, $message, $analysis);

        switch ($responseType) {
            case 'bot_response':
                return $this->handleBotResponse($chat, $message, $analysis);
            
            case 'agent_transfer':
                return $this->transferToAgent($chat, $analysis['escalation_reason'] ?? 'طلب العميل');
            
            case 'ticket_creation':
                return $this->convertToTicket($chat, $message);
            
            default:
                return $this->handleBotResponse($chat, $message, $analysis);
        }
    }

    /**
     * معالجة رد البوت الذكي
     */
    protected function handleBotResponse(LiveChat $chat, string $message, array $analysis): array
    {
        // الحصول على رد البوت
        $botResponse = $this->chatBotService->generateResponse($message, [
            'chat_context' => $this->getChatContext($chat),
            'customer_info' => $this->getCustomerInfo($chat),
            'language' => $chat->language,
            'sentiment' => $analysis['sentiment'] ?? 'neutral',
        ]);

        // إضافة رد البوت
        $botMessage = $this->addMessage($chat, [
            'sender_type' => 'bot',
            'sender_id' => null,
            'content' => $botResponse['message'],
            'message_type' => 'text',
            'ai_generated' => true,
            'ai_confidence' => $botResponse['confidence'],
            'metadata' => [
                'suggested_actions' => $botResponse['suggested_actions'] ?? [],
                'knowledge_base_articles' => $botResponse['related_articles'] ?? [],
            ],
        ]);

        // تحديث ثقة البوت
        $chat->update([
            'bot_confidence' => $botResponse['confidence'],
            'ai_insights' => array_merge($chat->ai_insights ?? [], [
                'last_interaction' => [
                    'customer_message' => $message,
                    'bot_response' => $botResponse['message'],
                    'confidence' => $botResponse['confidence'],
                    'timestamp' => now()->toISOString(),
                ]
            ]),
        ]);

        return [
            'type' => 'bot_response',
            'message' => $botMessage,
            'suggested_actions' => $botResponse['suggested_actions'] ?? [],
            'confidence' => $botResponse['confidence'],
            'should_transfer' => $botResponse['confidence'] < 0.6,
        ];
    }

    /**
     * تحويل الدردشة لوكيل
     */
    public function transferToAgent(LiveChat $chat, string $reason = null): array
    {
        // العثور على أفضل وكيل متاح
        $availableAgent = $this->findBestAvailableAgent($chat);

        if (!$availableAgent) {
            // إضافة للطابور
            $this->addToQueue($chat);
            
            $this->addMessage($chat, [
                'sender_type' => 'system',
                'content' => 'جميع الوكلاء مشغولون حالياً. سيتم توصيلك بأول وكيل متاح.',
                'message_type' => 'system',
            ]);

            return [
                'type' => 'queued',
                'message' => 'تم إضافتك للطابور',
                'estimated_wait_time' => $this->getEstimatedWaitTime(),
            ];
        }

        // تعيين الوكيل
        $chat->joinAgent($availableAgent->id);

        // إرسال رسالة تحويل
        $this->addMessage($chat, [
            'sender_type' => 'system',
            'content' => "تم توصيلك بالوكيل {$availableAgent->name}. كيف يمكنني مساعدتك؟",
            'message_type' => 'system',
        ]);

        // إشعار الوكيل
        $this->notifyAgent($availableAgent, $chat);

        return [
            'type' => 'agent_assigned',
            'agent' => $availableAgent,
            'message' => 'تم توصيلك بوكيل دعم',
        ];
    }

    /**
     * إضافة رسالة للدردشة
     */
    public function addMessage(LiveChat $chat, array $messageData): LiveChatMessage
    {
        $messageData['chat_id'] = $chat->id;
        
        // حساب وقت الاستجابة
        if ($messageData['sender_type'] === 'agent') {
            $lastCustomerMessage = $chat->messages()
                ->where('sender_type', 'customer')
                ->latest()
                ->first();

            if ($lastCustomerMessage) {
                $messageData['response_time_seconds'] = now()->diffInSeconds($lastCustomerMessage->created_at);
            }
        }

        $message = LiveChatMessage::create($messageData);

        // بث الرسالة للمشتركين
        $this->broadcastMessage($message);

        return $message;
    }

    /**
     * إنهاء الدردشة
     */
    public function endChat(int $chatId, string $reason = null, int $endedBy = null): array
    {
        $chat = LiveChat::findOrFail($chatId);
        
        $chat->end($reason, $endedBy);

        // إرسال استبيان الرضا
        $this->sendSatisfactionSurvey($chat);

        // تحليل الدردشة
        $analysis = $this->analyzeChatSession($chat);

        return [
            'status' => 'ended',
            'duration' => $chat->formatted_duration,
            'analysis' => $analysis,
            'satisfaction_survey_sent' => true,
        ];
    }

    /**
     * تحويل الدردشة إلى تذكرة
     */
    public function convertToTicket(LiveChat $chat, string $reason = null): array
    {
        $ticketData = [
            'subject' => 'دردشة حية - ' . $chat->session_id,
            'description' => $this->generateTicketDescription($chat),
            'customer_id' => $chat->customer_id,
            'assigned_to' => $chat->agent_id,
            'channel' => 'live_chat',
            'source_reference' => $chat->id,
            'priority' => $this->determinePriorityFromChat($chat),
            'language' => $chat->language,
            'metadata' => [
                'chat_duration' => $chat->duration_seconds,
                'messages_count' => $chat->messages_count,
                'conversion_reason' => $reason,
            ],
        ];

        $ticket = Ticket::create($ticketData);
        
        $chat->update(['related_ticket_id' => $ticket->id]);

        // إضافة رسالة إعلام
        $this->addMessage($chat, [
            'sender_type' => 'system',
            'content' => "تم إنشاء تذكرة رقم {$ticket->formatted_ticket_number} لمتابعة استفسارك.",
            'message_type' => 'system',
            'metadata' => ['ticket_id' => $ticket->id],
        ]);

        return [
            'type' => 'ticket_created',
            'ticket' => $ticket,
            'message' => 'تم إنشاء تذكرة لمتابعة استفسارك',
        ];
    }

    /**
     * الحصول على إحصائيات الدردشة الحية
     */
    public function getChatAnalytics(array $filters = []): array
    {
        $dateFrom = $filters['date_from'] ?? now()->subDays(30);
        $dateTo = $filters['date_to'] ?? now();

        return [
            'overview' => $this->getChatOverviewMetrics($dateFrom, $dateTo),
            'response_times' => $this->getResponseTimeMetrics($dateFrom, $dateTo),
            'bot_performance' => $this->getBotPerformanceMetrics($dateFrom, $dateTo),
            'agent_performance' => $this->getAgentChatMetrics($dateFrom, $dateTo),
            'satisfaction_metrics' => $this->getChatSatisfactionMetrics($dateFrom, $dateTo),
            'conversion_rates' => $this->getConversionRates($dateFrom, $dateTo),
        ];
    }

    /**
     * إرسال رسالة ترحيب من البوت
     */
    protected function sendBotWelcomeMessage(LiveChat $chat): void
    {
        $welcomeMessage = $this->chatBotService->getWelcomeMessage($chat->language);
        
        $this->addMessage($chat, [
            'sender_type' => 'bot',
            'content' => $welcomeMessage,
            'message_type' => 'text',
            'ai_generated' => true,
        ]);
    }

    /**
     * تحديد نوع الاستجابة المطلوبة
     */
    protected function determineResponseType(LiveChat $chat, string $message, array $analysis): string
    {
        // إذا طلب العميل التحدث مع وكيل
        if ($this->customerRequestsAgent($message)) {
            return 'agent_transfer';
        }

        // إذا كان التحليل يشير لحاجة تصعيد
        if (!empty($analysis['escalation_triggers'])) {
            return 'agent_transfer';
        }

        // إذا كانت ثقة البوت منخفضة
        if (($analysis['confidence'] ?? 0) < 0.4) {
            return 'agent_transfer';
        }

        // إذا كانت المشكلة معقدة
        if ($this->isComplexIssue($message, $analysis)) {
            return 'ticket_creation';
        }

        return 'bot_response';
    }

    /**
     * تحليل جلسة الدردشة
     */
    protected function analyzeChatSession(LiveChat $chat): array
    {
        $messages = $chat->messages()->get();
        
        return [
            'total_messages' => $messages->count(),
            'customer_messages' => $messages->where('sender_type', 'customer')->count(),
            'bot_messages' => $messages->where('sender_type', 'bot')->count(),
            'agent_messages' => $messages->where('sender_type', 'agent')->count(),
            'average_response_time' => $chat->average_response_time,
            'resolution_status' => $this->determineResolutionStatus($chat),
            'topics_discussed' => $this->extractTopics($messages),
            'sentiment_analysis' => $this->analyzeChatSentiment($messages),
        ];
    }

    // دوال مساعدة
    protected function generateSessionId(): string
    {
        return 'chat_' . time() . '_' . rand(1000, 9999);
    }

    protected function detectLanguage(string $text): string
    {
        // كشف اللغة - مبسط
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
            return 'ar';
        }
        return 'en';
    }

    protected function getChatContext(LiveChat $chat): array
    {
        return [
            'previous_messages' => $chat->messages()->latest()->limit(10)->get()->toArray(),
            'chat_duration' => $chat->duration_seconds,
            'customer_info' => $chat->customer ? $chat->customer->toArray() : [],
        ];
    }

    protected function getCustomerInfo(LiveChat $chat): array
    {
        return $chat->customer ? [
            'name' => $chat->customer->name,
            'email' => $chat->customer->email,
            'previous_tickets' => $chat->customer->tickets()->count(),
            'satisfaction_history' => $chat->customer->averageSatisfaction(),
        ] : [];
    }

    protected function findBestAvailableAgent(LiveChat $chat): ?\App\Domains\HR\Models\Employee
    {
        // منطق العثور على أفضل وكيل متاح
        return \App\Domains\HR\Models\Employee::where('department_id', $chat->department_id)
                                            ->where('is_available_for_chat', true)
                                            ->orderBy('current_chat_load')
                                            ->first();
    }

    protected function addToQueue(LiveChat $chat): void
    {
        $chat->update(['status' => 'waiting']);
        // إضافة منطق الطابور
    }

    protected function getEstimatedWaitTime(): int
    {
        // حساب الوقت المتوقع للانتظار
        return 5; // دقائق
    }

    protected function notifyAgent($agent, LiveChat $chat): void
    {
        // إرسال إشعار للوكيل
    }

    protected function broadcastMessage(LiveChatMessage $message): void
    {
        // بث الرسالة عبر WebSocket
        Event::dispatch(new \App\Events\LiveChatMessageSent($message));
    }

    protected function sendSatisfactionSurvey(LiveChat $chat): void
    {
        // إرسال استبيان الرضا
    }

    protected function generateTicketDescription(LiveChat $chat): string
    {
        $messages = $chat->messages()
                        ->whereIn('sender_type', ['customer', 'agent'])
                        ->get();

        $description = "تم تحويل هذه الدردشة إلى تذكرة.\n\n";
        $description .= "ملخص المحادثة:\n";

        foreach ($messages as $message) {
            $sender = $message->sender_type === 'customer' ? 'العميل' : 'الوكيل';
            $description .= "{$sender}: {$message->content}\n";
        }

        return $description;
    }

    protected function determinePriorityFromChat(LiveChat $chat): string
    {
        // تحديد الأولوية بناءً على تحليل الدردشة
        return 'medium';
    }

    protected function customerRequestsAgent(string $message): bool
    {
        $agentKeywords = ['وكيل', 'موظف', 'شخص', 'agent', 'human', 'person'];
        foreach ($agentKeywords as $keyword) {
            if (str_contains(strtolower($message), strtolower($keyword))) {
                return true;
            }
        }
        return false;
    }

    protected function isComplexIssue(string $message, array $analysis): bool
    {
        return ($analysis['complexity_level'] ?? 'low') === 'high';
    }

    protected function determineResolutionStatus(LiveChat $chat): string
    {
        // تحديد حالة الحل
        return 'resolved';
    }

    protected function extractTopics(array $messages): array
    {
        // استخراج المواضيع المناقشة
        return [];
    }

    protected function analyzeChatSentiment(array $messages): array
    {
        // تحليل مشاعر الدردشة
        return ['overall' => 'neutral'];
    }

    // دوال الإحصائيات
    protected function getChatOverviewMetrics($dateFrom, $dateTo): array
    {
        return [
            'total_chats' => LiveChat::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'active_chats' => LiveChat::active()->count(),
            'bot_handled_chats' => LiveChat::botHandled()->whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'agent_chats' => LiveChat::whereNotNull('agent_id')->whereBetween('created_at', [$dateFrom, $dateTo])->count(),
        ];
    }

    protected function getResponseTimeMetrics($dateFrom, $dateTo): array { return []; }
    protected function getBotPerformanceMetrics($dateFrom, $dateTo): array { return []; }
    protected function getAgentChatMetrics($dateFrom, $dateTo): array { return []; }
    protected function getChatSatisfactionMetrics($dateFrom, $dateTo): array { return []; }
    protected function getConversionRates($dateFrom, $dateTo): array { return []; }
}
