<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\ProjectFile;
use App\Domains\Projects\Models\FileVersion;
use App\Domains\Projects\Models\FileComment;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التعاون على الملفات - Advanced File Collaboration
 * تدعم إدارة الملفات المتقدمة مع التحكم في الإصدارات والتعاون
 */
class FileCollaborationService
{
    protected array $allowedFileTypes = [
        'documents' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
        'spreadsheets' => ['xls', 'xlsx', 'csv', 'ods'],
        'presentations' => ['ppt', 'pptx', 'odp'],
        'images' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
        'videos' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac'],
        'archives' => ['zip', 'rar', '7z', 'tar', 'gz'],
        'code' => ['php', 'js', 'html', 'css', 'json', 'xml', 'sql'],
    ];

    protected array $previewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'css', 'js', 'json'];

    /**
     * رفع ملف جديد
     */
    public function uploadFile(
        int $projectId,
        UploadedFile $file,
        int $uploadedBy,
        array $metadata = []
    ): ProjectFile {
        try {
            DB::beginTransaction();

            // التحقق من نوع الملف
            $this->validateFileType($file);

            // التحقق من حجم الملف
            $this->validateFileSize($file);

            // إنشاء مسار فريد للملف
            $filePath = $this->generateFilePath($projectId, $file);

            // رفع الملف
            $storedPath = Storage::disk('projects')->putFileAs(
                "project_{$projectId}/files",
                $file,
                $filePath
            );

            // إنشاء سجل الملف
            $projectFile = ProjectFile::create([
                'project_id' => $projectId,
                'name' => $file->getClientOriginalName(),
                'original_name' => $file->getClientOriginalName(),
                'file_path' => $storedPath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension(),
                'uploaded_by' => $uploadedBy,
                'version' => 1,
                'is_active' => true,
                'permissions' => $metadata['permissions'] ?? ['VIEW' => 'ALL', 'EDIT' => 'TEAM'],
                'tags' => $metadata['tags'] ?? [],
                'description' => $metadata['description'] ?? null,
                'category' => $this->categorizeFile($file->getClientOriginalExtension()),
                'metadata' => array_merge($metadata, [
                    'upload_ip' => request()->ip(),
                    'upload_user_agent' => request()->userAgent(),
                ]),
            ]);

            // إنشاء الإصدار الأول
            $this->createFileVersion($projectFile, $uploadedBy, 'الإصدار الأولي');

            // معالجة الملف (استخراج النص، إنشاء المعاينة، إلخ)
            $this->processFile($projectFile);

            DB::commit();

            // إشعار أعضاء الفريق
            $this->notifyTeamMembers($projectFile, 'file_uploaded');

            return $projectFile;

        } catch (\Exception $e) {
            DB::rollBack();
            
            // حذف الملف إذا تم رفعه
            if (isset($storedPath)) {
                Storage::disk('projects')->delete($storedPath);
            }

            Log::error('خطأ في رفع الملف', [
                'project_id' => $projectId,
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * تحديث ملف موجود (إصدار جديد)
     */
    public function updateFile(
        int $fileId,
        UploadedFile $newFile,
        int $updatedBy,
        string $versionNotes = null
    ): ProjectFile {
        try {
            DB::beginTransaction();

            $projectFile = ProjectFile::findOrFail($fileId);

            // التحقق من الصلاحيات
            $this->checkEditPermission($projectFile, $updatedBy);

            // التحقق من نوع الملف
            $this->validateFileType($newFile);

            // أرشفة الإصدار الحالي
            $this->archiveCurrentVersion($projectFile);

            // رفع الإصدار الجديد
            $newFilePath = $this->generateFilePath($projectFile->project_id, $newFile);
            $storedPath = Storage::disk('projects')->putFileAs(
                "project_{$projectFile->project_id}/files",
                $newFile,
                $newFilePath
            );

            // تحديث معلومات الملف
            $projectFile->update([
                'file_path' => $storedPath,
                'file_size' => $newFile->getSize(),
                'mime_type' => $newFile->getMimeType(),
                'version' => $projectFile->version + 1,
                'updated_by' => $updatedBy,
                'metadata' => array_merge($projectFile->metadata ?? [], [
                    'last_update_ip' => request()->ip(),
                    'last_update_user_agent' => request()->userAgent(),
                ]),
            ]);

            // إنشاء إصدار جديد
            $this->createFileVersion($projectFile, $updatedBy, $versionNotes ?? 'تحديث الملف');

            // معالجة الملف الجديد
            $this->processFile($projectFile);

            DB::commit();

            // إشعار أعضاء الفريق
            $this->notifyTeamMembers($projectFile, 'file_updated');

            return $projectFile;

        } catch (\Exception $e) {
            DB::rollBack();
            
            if (isset($storedPath)) {
                Storage::disk('projects')->delete($storedPath);
            }

            throw $e;
        }
    }

    /**
     * إضافة تعليق على الملف
     */
    public function addFileComment(
        int $fileId,
        int $userId,
        string $comment,
        array $coordinates = null
    ): FileComment {
        $projectFile = ProjectFile::findOrFail($fileId);

        // التحقق من صلاحية العرض
        $this->checkViewPermission($projectFile, $userId);

        $fileComment = FileComment::create([
            'file_id' => $fileId,
            'user_id' => $userId,
            'comment' => $comment,
            'coordinates' => $coordinates, // للتعليقات على نقاط محددة في الملف
            'is_resolved' => false,
        ]);

        // إشعار المهتمين
        $this->notifyFileStakeholders($projectFile, $fileComment, 'comment_added');

        return $fileComment;
    }

    /**
     * مشاركة الملف مع مستخدمين محددين
     */
    public function shareFile(
        int $fileId,
        array $userIds,
        array $permissions,
        int $sharedBy,
        string $message = null
    ): void {
        $projectFile = ProjectFile::findOrFail($fileId);

        foreach ($userIds as $userId) {
            $projectFile->sharedWith()->syncWithoutDetaching([
                $userId => [
                    'permissions' => $permissions,
                    'shared_by' => $sharedBy,
                    'shared_at' => now(),
                    'message' => $message,
                ]
            ]);

            // إشعار المستخدم
            $user = \App\Models\User::find($userId);
            $user?->notify(new \App\Notifications\FileSharedNotification($projectFile, $message));
        }
    }

    /**
     * إنشاء رابط مشاركة عام
     */
    public function createPublicLink(
        int $fileId,
        int $createdBy,
        array $options = []
    ): string {
        $projectFile = ProjectFile::findOrFail($fileId);

        // التحقق من الصلاحيات
        $this->checkEditPermission($projectFile, $createdBy);

        $token = \Str::random(32);
        $expiresAt = $options['expires_at'] ?? now()->addDays(7);

        $projectFile->update([
            'public_link_token' => $token,
            'public_link_expires_at' => $expiresAt,
            'public_link_permissions' => $options['permissions'] ?? ['VIEW'],
            'public_link_password' => $options['password'] ?? null,
        ]);

        return route('files.public', ['token' => $token]);
    }

    /**
     * معاينة الملف
     */
    public function previewFile(int $fileId, int $userId): array
    {
        $projectFile = ProjectFile::findOrFail($fileId);

        // التحقق من صلاحية العرض
        $this->checkViewPermission($projectFile, $userId);

        // تسجيل المشاهدة
        $this->logFileView($projectFile, $userId);

        $previewData = [
            'file' => $projectFile,
            'can_preview' => $this->canPreview($projectFile),
            'preview_url' => null,
            'download_url' => route('files.download', $projectFile->id),
        ];

        if ($this->canPreview($projectFile)) {
            $previewData['preview_url'] = $this->generatePreviewUrl($projectFile);
        }

        return $previewData;
    }

    /**
     * البحث في الملفات
     */
    public function searchFiles(int $projectId, string $query, array $filters = []): array
    {
        $queryBuilder = ProjectFile::where('project_id', $projectId)
                                  ->where('is_active', true);

        // البحث في النص
        $queryBuilder->where(function ($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%")
              ->orWhere('extracted_text', 'LIKE', "%{$query}%")
              ->orWhereJsonContains('tags', $query);
        });

        // تطبيق المرشحات
        if ($filters['category'] ?? null) {
            $queryBuilder->where('category', $filters['category']);
        }

        if ($filters['file_type'] ?? null) {
            $queryBuilder->where('extension', $filters['file_type']);
        }

        if ($filters['uploaded_by'] ?? null) {
            $queryBuilder->where('uploaded_by', $filters['uploaded_by']);
        }

        if ($filters['date_from'] ?? null) {
            $queryBuilder->where('created_at', '>=', $filters['date_from']);
        }

        if ($filters['date_to'] ?? null) {
            $queryBuilder->where('created_at', '<=', $filters['date_to']);
        }

        $files = $queryBuilder->with(['uploader', 'versions'])
                             ->orderBy('updated_at', 'desc')
                             ->paginate($filters['per_page'] ?? 20);

        return [
            'files' => $files,
            'query' => $query,
            'filters' => $filters,
            'statistics' => $this->getSearchStatistics($projectId, $query),
        ];
    }

    /**
     * إحصائيات الملفات
     */
    public function getFileStatistics(int $projectId): array
    {
        $project = Project::findOrFail($projectId);

        return [
            'total_files' => $project->files()->count(),
            'total_size' => $project->files()->sum('file_size'),
            'files_by_category' => $project->files()
                                         ->selectRaw('category, COUNT(*) as count, SUM(file_size) as total_size')
                                         ->groupBy('category')
                                         ->get(),
            'recent_uploads' => $project->files()
                                      ->with('uploader')
                                      ->orderBy('created_at', 'desc')
                                      ->limit(10)
                                      ->get(),
            'most_viewed' => $project->files()
                                   ->orderBy('view_count', 'desc')
                                   ->limit(10)
                                   ->get(),
            'storage_usage' => [
                'used' => $project->files()->sum('file_size'),
                'limit' => config('projects.storage_limit', 5 * 1024 * 1024 * 1024), // 5GB
            ],
        ];
    }

    /**
     * التكامل مع التخزين السحابي
     */
    public function syncWithCloudStorage(int $projectId, string $provider, array $config): array
    {
        $project = Project::findOrFail($projectId);

        return match ($provider) {
            'google_drive' => $this->syncWithGoogleDrive($project, $config),
            'dropbox' => $this->syncWithDropbox($project, $config),
            'onedrive' => $this->syncWithOneDrive($project, $config),
            default => throw new \InvalidArgumentException("مزود التخزين غير مدعوم: {$provider}"),
        };
    }

    // دوال مساعدة
    protected function validateFileType(UploadedFile $file): void
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $allowedExtensions = collect($this->allowedFileTypes)->flatten()->toArray();

        if (!in_array($extension, $allowedExtensions)) {
            throw new \InvalidArgumentException("نوع الملف غير مدعوم: {$extension}");
        }
    }

    protected function validateFileSize(UploadedFile $file): void
    {
        $maxSize = config('projects.max_file_size', 50 * 1024 * 1024); // 50MB

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('حجم الملف كبير جداً');
        }
    }

    protected function generateFilePath(int $projectId, UploadedFile $file): string
    {
        $timestamp = now()->format('Y/m/d');
        $filename = \Str::uuid() . '.' . $file->getClientOriginalExtension();
        
        return "{$timestamp}/{$filename}";
    }

    protected function categorizeFile(string $extension): string
    {
        foreach ($this->allowedFileTypes as $category => $extensions) {
            if (in_array(strtolower($extension), $extensions)) {
                return $category;
            }
        }
        
        return 'other';
    }

    protected function createFileVersion(ProjectFile $file, int $userId, string $notes): FileVersion
    {
        return $file->versions()->create([
            'version_number' => $file->version,
            'file_path' => $file->file_path,
            'file_size' => $file->file_size,
            'created_by' => $userId,
            'notes' => $notes,
            'is_current' => true,
        ]);
    }

    protected function processFile(ProjectFile $file): void
    {
        // استخراج النص للبحث
        if (in_array($file->extension, ['pdf', 'doc', 'docx', 'txt'])) {
            $this->extractText($file);
        }

        // إنشاء صورة مصغرة
        if (in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $this->generateThumbnail($file);
        }

        // فحص الفيروسات
        $this->scanForViruses($file);
    }

    protected function checkViewPermission(ProjectFile $file, int $userId): void
    {
        // منطق فحص الصلاحيات
        if (!$this->hasPermission($file, $userId, 'VIEW')) {
            throw new \UnauthorizedHttpException('لا تملك صلاحية عرض هذا الملف');
        }
    }

    protected function checkEditPermission(ProjectFile $file, int $userId): void
    {
        if (!$this->hasPermission($file, $userId, 'EDIT')) {
            throw new \UnauthorizedHttpException('لا تملك صلاحية تعديل هذا الملف');
        }
    }

    protected function hasPermission(ProjectFile $file, int $userId, string $permission): bool
    {
        // فحص صلاحيات المشروع
        $project = $file->project;
        
        if ($project->project_manager_id === $userId) {
            return true;
        }

        // فحص عضوية الفريق
        $teamMember = $project->teamMembers()->where('employee_id', $userId)->first();
        
        if ($teamMember) {
            $permissions = $teamMember->pivot->permissions ?? [];
            return in_array($permission, $permissions);
        }

        return false;
    }

    protected function canPreview(ProjectFile $file): bool
    {
        return in_array($file->extension, $this->previewableTypes);
    }

    protected function generatePreviewUrl(ProjectFile $file): string
    {
        return route('files.preview', $file->id);
    }

    protected function logFileView(ProjectFile $file, int $userId): void
    {
        $file->increment('view_count');
        
        $file->views()->create([
            'user_id' => $userId,
            'viewed_at' => now(),
            'ip_address' => request()->ip(),
        ]);
    }

    protected function notifyTeamMembers(ProjectFile $file, string $event): void
    {
        $project = $file->project;
        
        foreach ($project->teamMembers as $member) {
            if ($member->id !== $file->uploaded_by) {
                $member->notify(new \App\Notifications\FileActivityNotification($file, $event));
            }
        }
    }

    protected function notifyFileStakeholders(ProjectFile $file, FileComment $comment, string $event): void
    {
        // إشعار صاحب الملف
        if ($file->uploader && $file->uploader->id !== $comment->user_id) {
            $file->uploader->notify(new \App\Notifications\FileCommentNotification($comment));
        }

        // إشعار المعلقين السابقين
        $previousCommenters = $file->comments()
                                  ->where('user_id', '!=', $comment->user_id)
                                  ->distinct('user_id')
                                  ->with('user')
                                  ->get();

        foreach ($previousCommenters as $commenter) {
            $commenter->user->notify(new \App\Notifications\FileCommentNotification($comment));
        }
    }

    // دوال إضافية للمعالجة والتكامل
    protected function extractText(ProjectFile $file): void { /* تنفيذ استخراج النص */ }
    protected function generateThumbnail(ProjectFile $file): void { /* تنفيذ إنشاء الصورة المصغرة */ }
    protected function scanForViruses(ProjectFile $file): void { /* تنفيذ فحص الفيروسات */ }
    protected function archiveCurrentVersion(ProjectFile $file): void { /* تنفيذ أرشفة الإصدار */ }
    protected function getSearchStatistics(int $projectId, string $query): array { return []; }
    protected function syncWithGoogleDrive(Project $project, array $config): array { return []; }
    protected function syncWithDropbox(Project $project, array $config): array { return []; }
    protected function syncWithOneDrive(Project $project, array $config): array { return []; }
}
