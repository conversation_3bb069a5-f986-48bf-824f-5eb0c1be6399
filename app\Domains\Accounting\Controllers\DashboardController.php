<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Services\AdvancedFinancialAnalyticsService;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\Payment;
use App\Domains\Accounting\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Dashboard Controller
 * تحكم في لوحة التحكم المحاسبية
 */
class DashboardController extends Controller
{
    protected AdvancedFinancialAnalyticsService $analyticsService;

    public function __construct(AdvancedFinancialAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
        $this->middleware('auth');
    }

    /**
     * لوحة التحكم الرئيسية
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Account::class);

        $period = $request->period ?? 'current_month';
        $cacheKey = "accounting_dashboard_{$period}_" . auth()->id();

        $dashboardData = Cache::remember($cacheKey, 900, function () use ($period) {
            return $this->getDashboardData($period);
        });

        return response()->json([
            'success' => true,
            'data' => $dashboardData,
            'message' => 'تم جلب بيانات لوحة التحكم بنجاح'
        ]);
    }

    /**
     * الحصول على بيانات لوحة التحكم
     */
    protected function getDashboardData(string $period): array
    {
        $dates = $this->getPeriodDates($period);

        return [
            'summary_cards' => $this->getSummaryCards($dates),
            'financial_overview' => $this->getFinancialOverview($dates),
            'cash_flow_chart' => $this->getCashFlowChart($dates),
            'revenue_chart' => $this->getRevenueChart($dates),
            'expense_breakdown' => $this->getExpenseBreakdown($dates),
            'recent_transactions' => $this->getRecentTransactions(),
            'pending_approvals' => $this->getPendingApprovals(),
            'overdue_invoices' => $this->getOverdueInvoices(),
            'top_customers' => $this->getTopCustomers($dates),
            'financial_ratios' => $this->getFinancialRatios($dates),
            'alerts' => $this->getFinancialAlerts(),
        ];
    }

    /**
     * بطاقات الملخص
     */
    protected function getSummaryCards(array $dates): array
    {
        $currentPeriod = $this->analyticsService->getPeriodSummary($dates['current']['from'], $dates['current']['to']);
        $previousPeriod = $this->analyticsService->getPeriodSummary($dates['previous']['from'], $dates['previous']['to']);

        return [
            'total_revenue' => [
                'value' => $currentPeriod['revenue'],
                'previous_value' => $previousPeriod['revenue'],
                'change_percentage' => $this->calculateChangePercentage($currentPeriod['revenue'], $previousPeriod['revenue']),
                'trend' => $currentPeriod['revenue'] >= $previousPeriod['revenue'] ? 'up' : 'down',
                'currency' => 'SAR',
            ],
            'total_expenses' => [
                'value' => $currentPeriod['expenses'],
                'previous_value' => $previousPeriod['expenses'],
                'change_percentage' => $this->calculateChangePercentage($currentPeriod['expenses'], $previousPeriod['expenses']),
                'trend' => $currentPeriod['expenses'] <= $previousPeriod['expenses'] ? 'up' : 'down',
                'currency' => 'SAR',
            ],
            'net_profit' => [
                'value' => $currentPeriod['profit'],
                'previous_value' => $previousPeriod['profit'],
                'change_percentage' => $this->calculateChangePercentage($currentPeriod['profit'], $previousPeriod['profit']),
                'trend' => $currentPeriod['profit'] >= $previousPeriod['profit'] ? 'up' : 'down',
                'currency' => 'SAR',
            ],
            'cash_balance' => [
                'value' => $this->analyticsService->getCurrentCashBalance(),
                'previous_value' => $this->analyticsService->getCashBalanceAsOf($dates['previous']['to']),
                'change_percentage' => $this->calculateChangePercentage(
                    $this->analyticsService->getCurrentCashBalance(),
                    $this->analyticsService->getCashBalanceAsOf($dates['previous']['to'])
                ),
                'trend' => $this->analyticsService->getCurrentCashBalance() >= $this->analyticsService->getCashBalanceAsOf($dates['previous']['to']) ? 'up' : 'down',
                'currency' => 'SAR',
            ],
        ];
    }

    /**
     * نظرة عامة مالية
     */
    protected function getFinancialOverview(array $dates): array
    {
        return [
            'accounts_receivable' => $this->analyticsService->getAccountsReceivableTotal(),
            'accounts_payable' => $this->analyticsService->getAccountsPayableTotal(),
            'inventory_value' => $this->analyticsService->getInventoryValue(),
            'total_assets' => $this->analyticsService->getTotalAssets(),
            'total_liabilities' => $this->analyticsService->getTotalLiabilities(),
            'equity' => $this->analyticsService->getTotalEquity(),
        ];
    }

    /**
     * مخطط التدفق النقدي
     */
    protected function getCashFlowChart(array $dates): array
    {
        return $this->analyticsService->getCashFlowChartData($dates['current']['from'], $dates['current']['to']);
    }

    /**
     * مخطط الإيرادات
     */
    protected function getRevenueChart(array $dates): array
    {
        return $this->analyticsService->getRevenueChartData($dates['current']['from'], $dates['current']['to']);
    }

    /**
     * تفصيل المصروفات
     */
    protected function getExpenseBreakdown(array $dates): array
    {
        return $this->analyticsService->getExpenseBreakdown($dates['current']['from'], $dates['current']['to']);
    }

    /**
     * المعاملات الأخيرة
     */
    protected function getRecentTransactions(): array
    {
        $recentEntries = JournalEntry::with(['lines.account', 'creator'])
            ->where('status', 'POSTED')
            ->orderBy('entry_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return $recentEntries->map(function ($entry) {
            return [
                'id' => $entry->id,
                'entry_number' => $entry->entry_number,
                'date' => $entry->entry_date,
                'description' => $entry->description,
                'amount' => $entry->total_debit,
                'type' => $entry->source_type,
                'created_by' => $entry->creator->name ?? 'غير محدد',
            ];
        })->toArray();
    }

    /**
     * الموافقات المعلقة
     */
    protected function getPendingApprovals(): array
    {
        $pendingInvoices = Invoice::where('status', 'draft')
            ->where('requires_approval', true)
            ->count();

        $pendingPayments = Payment::where('status', 'pending')->count();

        $pendingEntries = JournalEntry::where('status', 'DRAFT')
            ->where('requires_review', true)
            ->count();

        return [
            'invoices' => $pendingInvoices,
            'payments' => $pendingPayments,
            'journal_entries' => $pendingEntries,
            'total' => $pendingInvoices + $pendingPayments + $pendingEntries,
        ];
    }

    /**
     * الفواتير المتأخرة
     */
    protected function getOverdueInvoices(): array
    {
        $overdueInvoices = Invoice::where('due_date', '<', now())
            ->whereIn('status', ['sent', 'viewed'])
            ->with('customer')
            ->orderBy('due_date')
            ->limit(5)
            ->get();

        return [
            'count' => $overdueInvoices->count(),
            'total_amount' => $overdueInvoices->sum('total_amount'),
            'invoices' => $overdueInvoices->map(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'customer_name' => $invoice->customer->name ?? 'غير محدد',
                    'amount' => $invoice->total_amount,
                    'due_date' => $invoice->due_date,
                    'days_overdue' => now()->diffInDays($invoice->due_date),
                ];
            })->toArray(),
        ];
    }

    /**
     * أفضل العملاء
     */
    protected function getTopCustomers(array $dates): array
    {
        return $this->analyticsService->getTopCustomers($dates['current']['from'], $dates['current']['to'], 5);
    }

    /**
     * النسب المالية
     */
    protected function getFinancialRatios(array $dates): array
    {
        return $this->analyticsService->calculateFinancialRatios($dates['current']['from'], $dates['current']['to']);
    }

    /**
     * التنبيهات المالية
     */
    protected function getFinancialAlerts(): array
    {
        $alerts = [];

        // تحقق من الرصيد النقدي المنخفض
        $cashBalance = $this->analyticsService->getCurrentCashBalance();
        $minCashThreshold = config('accounting.alerts.min_cash_balance', 10000);
        
        if ($cashBalance < $minCashThreshold) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'رصيد نقدي منخفض',
                'message' => "الرصيد النقدي الحالي ({$cashBalance} ريال) أقل من الحد الأدنى المطلوب",
                'action_url' => '/accounting/cash-flow',
            ];
        }

        // تحقق من الفواتير المتأخرة
        $overdueCount = Invoice::where('due_date', '<', now())
            ->whereIn('status', ['sent', 'viewed'])
            ->count();

        if ($overdueCount > 0) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'فواتير متأخرة',
                'message' => "يوجد {$overdueCount} فاتورة متأخرة تحتاج للمتابعة",
                'action_url' => '/accounting/invoices/overdue',
            ];
        }

        // تحقق من الموافقات المعلقة
        $pendingApprovals = $this->getPendingApprovals();
        if ($pendingApprovals['total'] > 0) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'موافقات معلقة',
                'message' => "يوجد {$pendingApprovals['total']} عنصر في انتظار الموافقة",
                'action_url' => '/accounting/approvals',
            ];
        }

        return $alerts;
    }

    /**
     * تحليلات متقدمة
     */
    public function getAdvancedAnalytics(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $period = $request->period ?? 'current_year';
        $dates = $this->getPeriodDates($period);

        $analytics = [
            'profitability_analysis' => $this->analyticsService->getProfitabilityAnalysis($dates['current']['from'], $dates['current']['to']),
            'liquidity_analysis' => $this->analyticsService->getLiquidityAnalysis(),
            'efficiency_ratios' => $this->analyticsService->getEfficiencyRatios($dates['current']['from'], $dates['current']['to']),
            'growth_metrics' => $this->analyticsService->getGrowthMetrics($dates['current']['from'], $dates['current']['to']),
            'cash_conversion_cycle' => $this->analyticsService->getCashConversionCycle(),
            'break_even_analysis' => $this->analyticsService->getBreakEvenAnalysis(),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
            'message' => 'تم جلب التحليلات المتقدمة بنجاح'
        ]);
    }

    /**
     * توقعات مالية
     */
    public function getFinancialForecasts(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $forecastPeriod = $request->forecast_period ?? 12; // months
        $method = $request->method ?? 'linear_regression';

        $forecasts = $this->analyticsService->generateFinancialForecasts($forecastPeriod, $method);

        return response()->json([
            'success' => true,
            'data' => $forecasts,
            'message' => 'تم إنشاء التوقعات المالية بنجاح'
        ]);
    }

    /**
     * مقارنة الأداء
     */
    public function getPerformanceComparison(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $currentPeriod = $request->current_period ?? 'current_month';
        $comparisonPeriod = $request->comparison_period ?? 'previous_month';

        $comparison = $this->analyticsService->comparePerformance($currentPeriod, $comparisonPeriod);

        return response()->json([
            'success' => true,
            'data' => $comparison,
            'message' => 'تم إنشاء مقارنة الأداء بنجاح'
        ]);
    }

    /**
     * الحصول على تواريخ الفترة
     */
    protected function getPeriodDates(string $period): array
    {
        $now = Carbon::now();

        switch ($period) {
            case 'current_month':
                return [
                    'current' => [
                        'from' => $now->copy()->startOfMonth()->format('Y-m-d'),
                        'to' => $now->copy()->endOfMonth()->format('Y-m-d'),
                    ],
                    'previous' => [
                        'from' => $now->copy()->subMonth()->startOfMonth()->format('Y-m-d'),
                        'to' => $now->copy()->subMonth()->endOfMonth()->format('Y-m-d'),
                    ],
                ];

            case 'current_quarter':
                return [
                    'current' => [
                        'from' => $now->copy()->startOfQuarter()->format('Y-m-d'),
                        'to' => $now->copy()->endOfQuarter()->format('Y-m-d'),
                    ],
                    'previous' => [
                        'from' => $now->copy()->subQuarter()->startOfQuarter()->format('Y-m-d'),
                        'to' => $now->copy()->subQuarter()->endOfQuarter()->format('Y-m-d'),
                    ],
                ];

            case 'current_year':
                return [
                    'current' => [
                        'from' => $now->copy()->startOfYear()->format('Y-m-d'),
                        'to' => $now->copy()->endOfYear()->format('Y-m-d'),
                    ],
                    'previous' => [
                        'from' => $now->copy()->subYear()->startOfYear()->format('Y-m-d'),
                        'to' => $now->copy()->subYear()->endOfYear()->format('Y-m-d'),
                    ],
                ];

            default:
                return [
                    'current' => [
                        'from' => $now->copy()->startOfMonth()->format('Y-m-d'),
                        'to' => $now->format('Y-m-d'),
                    ],
                    'previous' => [
                        'from' => $now->copy()->subMonth()->startOfMonth()->format('Y-m-d'),
                        'to' => $now->copy()->subMonth()->format('Y-m-d'),
                    ],
                ];
        }
    }

    /**
     * حساب نسبة التغيير
     */
    protected function calculateChangePercentage(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * تحديث بيانات لوحة التحكم
     */
    public function refreshDashboard(): JsonResponse
    {
        $this->authorize('viewAny', Account::class);

        // مسح الكاش
        Cache::tags(['accounting_dashboard'])->flush();

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث بيانات لوحة التحكم بنجاح'
        ]);
    }

    /**
     * تخصيص لوحة التحكم
     */
    public function customizeDashboard(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Account::class);

        $request->validate([
            'widgets' => 'required|array',
            'layout' => 'required|array',
        ]);

        // حفظ تخصيصات المستخدم
        auth()->user()->updateMeta('accounting_dashboard_config', [
            'widgets' => $request->widgets,
            'layout' => $request->layout,
            'updated_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم حفظ تخصيصات لوحة التحكم بنجاح'
        ]);
    }
}
