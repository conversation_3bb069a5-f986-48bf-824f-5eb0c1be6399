<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\TimeEntry;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Events\TimeEntryStarted;
use App\Domains\Projects\Events\TimeEntryStopped;
use App\Domains\Projects\Events\TimeEntryApproved;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة تتبع الوقت المتقدمة
 * إدارة شاملة لتتبع الوقت والإنتاجية
 */
class TimeTrackingService
{
    /**
     * بدء تتبع الوقت
     */
    public function startTimer(array $timerData): TimeEntry
    {
        DB::beginTransaction();

        try {
            // إيقاف أي مؤقت نشط للمستخدم
            $this->stopActiveTimer($timerData['user_id']);

            // إنشاء إدخال وقت جديد
            $timeEntry = TimeEntry::create([
                'user_id' => $timerData['user_id'],
                'project_id' => $timerData['project_id'],
                'task_id' => $timerData['task_id'] ?? null,
                'description' => $timerData['description'] ?? null,
                'start_time' => $timerData['start_time'] ?? now(),
                'is_billable' => $timerData['is_billable'] ?? true,
                'hourly_rate' => $timerData['hourly_rate'] ?? null,
                'status' => 'RUNNING',
            ]);

            // تسجيل النشاط
            $timeEntry->activities()->create([
                'user_id' => $timerData['user_id'],
                'action' => 'timer_started',
                'description' => 'تم بدء تتبع الوقت',
                'metadata' => [
                    'project_id' => $timerData['project_id'],
                    'task_id' => $timerData['task_id'] ?? null,
                ],
            ]);

            // إطلاق الحدث
            Event::dispatch(new TimeEntryStarted($timeEntry));

            DB::commit();

            return $timeEntry->load(['user', 'project', 'task']);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إيقاف تتبع الوقت
     */
    public function stopTimer(TimeEntry $timeEntry, ?string $description = null): TimeEntry
    {
        DB::beginTransaction();

        try {
            $endTime = now();
            $duration = $timeEntry->start_time->diffInMinutes($endTime);
            $hours = round($duration / 60, 2);

            // تحديث إدخال الوقت
            $timeEntry->update([
                'end_time' => $endTime,
                'duration_minutes' => $duration,
                'hours' => $hours,
                'description' => $description ?? $timeEntry->description,
                'status' => 'STOPPED',
            ]);

            // حساب التكلفة إذا كان قابلاً للفوترة
            if ($timeEntry->is_billable && $timeEntry->hourly_rate) {
                $timeEntry->update([
                    'billable_amount' => $hours * $timeEntry->hourly_rate,
                ]);
            }

            // تحديث الساعات المتبقية للمهمة
            if ($timeEntry->task_id) {
                $this->updateTaskRemainingHours($timeEntry->task);
            }

            // تسجيل النشاط
            $timeEntry->activities()->create([
                'user_id' => $timeEntry->user_id,
                'action' => 'timer_stopped',
                'description' => "تم إيقاف تتبع الوقت - {$hours} ساعة",
                'metadata' => [
                    'duration_hours' => $hours,
                    'billable_amount' => $timeEntry->billable_amount,
                ],
            ]);

            // إطلاق الحدث
            Event::dispatch(new TimeEntryStopped($timeEntry));

            DB::commit();

            return $timeEntry->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إيقاف المؤقت النشط للمستخدم
     */
    public function stopActiveTimer(int $userId): ?TimeEntry
    {
        $activeTimer = $this->getActiveTimer($userId);

        if ($activeTimer) {
            return $this->stopTimer($activeTimer);
        }

        return null;
    }

    /**
     * الحصول على المؤقت النشط للمستخدم
     */
    public function getActiveTimer(int $userId): ?TimeEntry
    {
        return TimeEntry::where('user_id', $userId)
            ->whereNull('end_time')
            ->where('status', 'RUNNING')
            ->with(['project', 'task'])
            ->first();
    }

    /**
     * إنشاء إدخال وقت يدوي
     */
    public function createManualEntry(array $entryData): TimeEntry
    {
        DB::beginTransaction();

        try {
            $startTime = Carbon::parse($entryData['start_time']);
            $endTime = Carbon::parse($entryData['end_time']);
            $duration = $startTime->diffInMinutes($endTime);
            $hours = round($duration / 60, 2);

            $timeEntry = TimeEntry::create([
                'user_id' => $entryData['user_id'],
                'project_id' => $entryData['project_id'],
                'task_id' => $entryData['task_id'] ?? null,
                'description' => $entryData['description'],
                'start_time' => $startTime,
                'end_time' => $endTime,
                'duration_minutes' => $duration,
                'hours' => $hours,
                'is_billable' => $entryData['is_billable'] ?? true,
                'hourly_rate' => $entryData['hourly_rate'] ?? null,
                'status' => 'MANUAL',
            ]);

            // حساب التكلفة
            if ($timeEntry->is_billable && $timeEntry->hourly_rate) {
                $timeEntry->update([
                    'billable_amount' => $hours * $timeEntry->hourly_rate,
                ]);
            }

            // تحديث الساعات المتبقية للمهمة
            if ($timeEntry->task_id) {
                $this->updateTaskRemainingHours($timeEntry->task);
            }

            // تسجيل النشاط
            $timeEntry->activities()->create([
                'user_id' => $entryData['user_id'],
                'action' => 'manual_entry_created',
                'description' => "تم إنشاء إدخال وقت يدوي - {$hours} ساعة",
                'metadata' => [
                    'duration_hours' => $hours,
                    'billable_amount' => $timeEntry->billable_amount,
                ],
            ]);

            DB::commit();

            return $timeEntry->load(['user', 'project', 'task']);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تحديث إدخال الوقت
     */
    public function updateTimeEntry(TimeEntry $timeEntry, array $updateData): TimeEntry
    {
        DB::beginTransaction();

        try {
            $originalData = $timeEntry->toArray();

            // إعادة حساب المدة إذا تم تغيير الأوقات
            if (isset($updateData['start_time']) || isset($updateData['end_time'])) {
                $startTime = Carbon::parse($updateData['start_time'] ?? $timeEntry->start_time);
                $endTime = Carbon::parse($updateData['end_time'] ?? $timeEntry->end_time);
                
                if ($endTime) {
                    $duration = $startTime->diffInMinutes($endTime);
                    $hours = round($duration / 60, 2);
                    
                    $updateData['duration_minutes'] = $duration;
                    $updateData['hours'] = $hours;
                }
            }

            $timeEntry->update($updateData);

            // إعادة حساب التكلفة
            if ($timeEntry->is_billable && $timeEntry->hourly_rate) {
                $timeEntry->update([
                    'billable_amount' => $timeEntry->hours * $timeEntry->hourly_rate,
                ]);
            }

            // تحديث الساعات المتبقية للمهمة
            if ($timeEntry->task_id) {
                $this->updateTaskRemainingHours($timeEntry->task);
            }

            // تسجيل النشاط
            $timeEntry->activities()->create([
                'user_id' => auth()->id(),
                'action' => 'time_entry_updated',
                'description' => 'تم تحديث إدخال الوقت',
                'metadata' => [
                    'original_data' => $originalData,
                    'updated_data' => $updateData,
                ],
            ]);

            DB::commit();

            return $timeEntry->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إنشاء تقرير الوقت
     */
    public function generateTimeReport(array $filters): array
    {
        $cacheKey = 'time_report_' . md5(serialize($filters));

        return Cache::remember($cacheKey, 300, function () use ($filters) {
            $query = TimeEntry::with(['user', 'project', 'task'])
                ->whereNotNull('end_time');

            // تطبيق المرشحات
            if (isset($filters['user_id'])) {
                $query->where('user_id', $filters['user_id']);
            }

            if (isset($filters['project_id'])) {
                $query->where('project_id', $filters['project_id']);
            }

            if (isset($filters['start_date']) && isset($filters['end_date'])) {
                $query->whereBetween('start_time', [
                    $filters['start_date'],
                    $filters['end_date']
                ]);
            }

            $timeEntries = $query->get();

            // تجميع البيانات حسب المرشح المحدد
            $groupBy = $filters['group_by'] ?? 'day';
            $groupedData = $this->groupTimeEntries($timeEntries, $groupBy);

            return [
                'summary' => [
                    'total_entries' => $timeEntries->count(),
                    'total_hours' => $timeEntries->sum('hours'),
                    'billable_hours' => $timeEntries->where('is_billable', true)->sum('hours'),
                    'non_billable_hours' => $timeEntries->where('is_billable', false)->sum('hours'),
                    'total_amount' => $timeEntries->sum('billable_amount'),
                    'average_hours_per_day' => $this->calculateAverageHoursPerDay($timeEntries, $filters),
                ],
                'grouped_data' => $groupedData,
                'top_projects' => $this->getTopProjectsByHours($timeEntries),
                'top_tasks' => $this->getTopTasksByHours($timeEntries),
                'productivity_metrics' => $this->calculateProductivityMetrics($timeEntries),
            ];
        });
    }

    /**
     * الحصول على إحصائيات الإنتاجية
     */
    public function getProductivityStatistics(array $filters): array
    {
        $cacheKey = 'productivity_stats_' . md5(serialize($filters));

        return Cache::remember($cacheKey, 600, function () use ($filters) {
            $userId = $filters['user_id'];
            $period = $filters['period'] ?? 'week';
            $projectId = $filters['project_id'] ?? null;

            $dateRange = $this->getDateRangeForPeriod($period);
            
            $query = TimeEntry::where('user_id', $userId)
                ->whereBetween('start_time', $dateRange)
                ->whereNotNull('end_time');

            if ($projectId) {
                $query->where('project_id', $projectId);
            }

            $timeEntries = $query->with(['project', 'task'])->get();

            return [
                'period_summary' => [
                    'total_hours' => $timeEntries->sum('hours'),
                    'billable_hours' => $timeEntries->where('is_billable', true)->sum('hours'),
                    'working_days' => $timeEntries->groupBy(function ($entry) {
                        return $entry->start_time->format('Y-m-d');
                    })->count(),
                    'average_hours_per_day' => $this->calculateAverageHoursPerDay($timeEntries, $filters),
                    'total_earnings' => $timeEntries->sum('billable_amount'),
                ],
                'daily_breakdown' => $this->getDailyBreakdown($timeEntries),
                'project_distribution' => $this->getProjectDistribution($timeEntries),
                'task_distribution' => $this->getTaskDistribution($timeEntries),
                'productivity_trends' => $this->getProductivityTrends($timeEntries),
                'efficiency_metrics' => $this->getEfficiencyMetrics($timeEntries),
                'comparison_with_previous_period' => $this->getComparisonWithPreviousPeriod($userId, $period, $projectId),
            ];
        });
    }

    /**
     * اعتماد إدخالات الوقت
     */
    public function approveTimeEntries(array $timeEntryIds, int $approvedBy, ?string $comment = null): int
    {
        DB::beginTransaction();

        try {
            $approvedCount = 0;

            foreach ($timeEntryIds as $timeEntryId) {
                $timeEntry = TimeEntry::find($timeEntryId);
                
                if ($timeEntry && $timeEntry->status !== 'APPROVED') {
                    $timeEntry->update([
                        'status' => 'APPROVED',
                        'approved_by' => $approvedBy,
                        'approved_at' => now(),
                        'approval_comment' => $comment,
                    ]);

                    // تسجيل النشاط
                    $timeEntry->activities()->create([
                        'user_id' => $approvedBy,
                        'action' => 'time_entry_approved',
                        'description' => 'تم اعتماد إدخال الوقت',
                        'metadata' => [
                            'comment' => $comment,
                        ],
                    ]);

                    // إطلاق الحدث
                    Event::dispatch(new TimeEntryApproved($timeEntry, $approvedBy));

                    $approvedCount++;
                }
            }

            DB::commit();

            return $approvedCount;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تصدير تقرير الوقت
     */
    public function exportTimeReport(array $exportData): string
    {
        $format = $exportData['format'];
        $reportData = $this->generateTimeReport($exportData);

        switch ($format) {
            case 'excel':
                return $this->exportToExcel($reportData);
            case 'pdf':
                return $this->exportToPdf($reportData);
            case 'csv':
                return $this->exportToCsv($reportData);
            default:
                throw new \Exception('تنسيق التصدير غير مدعوم');
        }
    }

    /**
     * تحديث الساعات المتبقية للمهمة
     */
    protected function updateTaskRemainingHours(Task $task): void
    {
        $totalLoggedHours = $task->timeEntries()->sum('hours');
        $estimatedHours = $task->estimated_hours;

        if ($estimatedHours) {
            $remainingHours = max(0, $estimatedHours - $totalLoggedHours);
            $task->update(['remaining_hours' => $remainingHours]);

            // تحديث نسبة التقدم بناءً على الوقت المسجل
            $progressPercentage = min(100, ($totalLoggedHours / $estimatedHours) * 100);
            
            if ($task->auto_update_progress) {
                $task->update(['progress_percentage' => round($progressPercentage)]);
            }
        }
    }

    /**
     * تجميع إدخالات الوقت
     */
    protected function groupTimeEntries(Collection $timeEntries, string $groupBy): array
    {
        switch ($groupBy) {
            case 'day':
                return $timeEntries->groupBy(function ($entry) {
                    return $entry->start_time->format('Y-m-d');
                })->map(function ($group, $date) {
                    return [
                        'date' => $date,
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            case 'week':
                return $timeEntries->groupBy(function ($entry) {
                    return $entry->start_time->format('Y-W');
                })->map(function ($group, $week) {
                    return [
                        'week' => $week,
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            case 'month':
                return $timeEntries->groupBy(function ($entry) {
                    return $entry->start_time->format('Y-m');
                })->map(function ($group, $month) {
                    return [
                        'month' => $month,
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            case 'project':
                return $timeEntries->groupBy('project.name')->map(function ($group, $projectName) {
                    return [
                        'project' => $projectName,
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            case 'task':
                return $timeEntries->groupBy('task.title')->map(function ($group, $taskTitle) {
                    return [
                        'task' => $taskTitle ?: 'بدون مهمة',
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            case 'user':
                return $timeEntries->groupBy('user.name')->map(function ($group, $userName) {
                    return [
                        'user' => $userName,
                        'total_hours' => $group->sum('hours'),
                        'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                        'entries_count' => $group->count(),
                        'total_amount' => $group->sum('billable_amount'),
                    ];
                })->values()->toArray();

            default:
                return [];
        }
    }

    /**
     * حساب متوسط الساعات في اليوم
     */
    protected function calculateAverageHoursPerDay(Collection $timeEntries, array $filters): float
    {
        if ($timeEntries->isEmpty()) {
            return 0;
        }

        $uniqueDays = $timeEntries->groupBy(function ($entry) {
            return $entry->start_time->format('Y-m-d');
        })->count();

        return $uniqueDays > 0 ? round($timeEntries->sum('hours') / $uniqueDays, 2) : 0;
    }

    /**
     * الحصول على أهم المشاريع حسب الساعات
     */
    protected function getTopProjectsByHours(Collection $timeEntries): array
    {
        return $timeEntries->groupBy('project.name')
            ->map(function ($group, $projectName) {
                return [
                    'project' => $projectName,
                    'total_hours' => $group->sum('hours'),
                    'percentage' => 0, // سيتم حسابه لاحقاً
                ];
            })
            ->sortByDesc('total_hours')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * الحصول على أهم المهام حسب الساعات
     */
    protected function getTopTasksByHours(Collection $timeEntries): array
    {
        return $timeEntries->where('task_id', '!=', null)
            ->groupBy('task.title')
            ->map(function ($group, $taskTitle) {
                return [
                    'task' => $taskTitle,
                    'total_hours' => $group->sum('hours'),
                    'percentage' => 0, // سيتم حسابه لاحقاً
                ];
            })
            ->sortByDesc('total_hours')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * حساب مقاييس الإنتاجية
     */
    protected function calculateProductivityMetrics(Collection $timeEntries): array
    {
        $totalHours = $timeEntries->sum('hours');
        $billableHours = $timeEntries->where('is_billable', true)->sum('hours');
        
        return [
            'billable_ratio' => $totalHours > 0 ? round(($billableHours / $totalHours) * 100, 2) : 0,
            'average_session_duration' => $timeEntries->avg('hours'),
            'total_sessions' => $timeEntries->count(),
            'productivity_score' => $this->calculateProductivityScore($timeEntries),
        ];
    }

    /**
     * حساب درجة الإنتاجية
     */
    protected function calculateProductivityScore(Collection $timeEntries): float
    {
        // خوارزمية بسيطة لحساب درجة الإنتاجية
        $billableRatio = $timeEntries->where('is_billable', true)->sum('hours') / max(1, $timeEntries->sum('hours'));
        $consistencyScore = $this->calculateConsistencyScore($timeEntries);
        
        return round(($billableRatio * 0.6 + $consistencyScore * 0.4) * 100, 2);
    }

    /**
     * حساب درجة الاتساق
     */
    protected function calculateConsistencyScore(Collection $timeEntries): float
    {
        $dailyHours = $timeEntries->groupBy(function ($entry) {
            return $entry->start_time->format('Y-m-d');
        })->map(function ($group) {
            return $group->sum('hours');
        });

        if ($dailyHours->count() < 2) {
            return 1;
        }

        $mean = $dailyHours->avg();
        $variance = $dailyHours->map(function ($hours) use ($mean) {
            return pow($hours - $mean, 2);
        })->avg();

        $standardDeviation = sqrt($variance);
        $coefficientOfVariation = $mean > 0 ? $standardDeviation / $mean : 0;

        // كلما قل معامل التباين، زادت درجة الاتساق
        return max(0, 1 - $coefficientOfVariation);
    }

    /**
     * الحصول على نطاق التاريخ للفترة
     */
    protected function getDateRangeForPeriod(string $period): array
    {
        $now = now();

        switch ($period) {
            case 'today':
                return [$now->startOfDay(), $now->endOfDay()];
            case 'week':
                return [$now->startOfWeek(), $now->endOfWeek()];
            case 'month':
                return [$now->startOfMonth(), $now->endOfMonth()];
            case 'quarter':
                return [$now->startOfQuarter(), $now->endOfQuarter()];
            case 'year':
                return [$now->startOfYear(), $now->endOfYear()];
            default:
                return [$now->startOfWeek(), $now->endOfWeek()];
        }
    }

    /**
     * الحصول على التفصيل اليومي
     */
    protected function getDailyBreakdown(Collection $timeEntries): array
    {
        return $timeEntries->groupBy(function ($entry) {
            return $entry->start_time->format('Y-m-d');
        })->map(function ($group, $date) {
            return [
                'date' => $date,
                'day_name' => Carbon::parse($date)->format('l'),
                'total_hours' => $group->sum('hours'),
                'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                'sessions' => $group->count(),
                'projects' => $group->groupBy('project_id')->count(),
            ];
        })->sortBy('date')->values()->toArray();
    }

    /**
     * الحصول على توزيع المشاريع
     */
    protected function getProjectDistribution(Collection $timeEntries): array
    {
        $totalHours = $timeEntries->sum('hours');

        return $timeEntries->groupBy('project.name')
            ->map(function ($group, $projectName) use ($totalHours) {
                $projectHours = $group->sum('hours');
                return [
                    'project' => $projectName,
                    'hours' => $projectHours,
                    'percentage' => $totalHours > 0 ? round(($projectHours / $totalHours) * 100, 2) : 0,
                    'sessions' => $group->count(),
                ];
            })
            ->sortByDesc('hours')
            ->values()
            ->toArray();
    }

    /**
     * الحصول على توزيع المهام
     */
    protected function getTaskDistribution(Collection $timeEntries): array
    {
        $totalHours = $timeEntries->sum('hours');

        return $timeEntries->where('task_id', '!=', null)
            ->groupBy('task.title')
            ->map(function ($group, $taskTitle) use ($totalHours) {
                $taskHours = $group->sum('hours');
                return [
                    'task' => $taskTitle,
                    'hours' => $taskHours,
                    'percentage' => $totalHours > 0 ? round(($taskHours / $totalHours) * 100, 2) : 0,
                    'sessions' => $group->count(),
                ];
            })
            ->sortByDesc('hours')
            ->take(10)
            ->values()
            ->toArray();
    }

    /**
     * الحصول على اتجاهات الإنتاجية
     */
    protected function getProductivityTrends(Collection $timeEntries): array
    {
        // تحليل الاتجاهات الأسبوعية
        $weeklyData = $timeEntries->groupBy(function ($entry) {
            return $entry->start_time->format('Y-W');
        })->map(function ($group, $week) {
            return [
                'week' => $week,
                'total_hours' => $group->sum('hours'),
                'billable_hours' => $group->where('is_billable', true)->sum('hours'),
                'productivity_score' => $this->calculateProductivityScore($group),
            ];
        })->sortBy('week')->values();

        return [
            'weekly_trends' => $weeklyData->toArray(),
            'trend_direction' => $this->calculateTrendDirection($weeklyData),
            'best_week' => $weeklyData->sortByDesc('productivity_score')->first(),
            'worst_week' => $weeklyData->sortBy('productivity_score')->first(),
        ];
    }

    /**
     * حساب اتجاه الاتجاه
     */
    protected function calculateTrendDirection(Collection $weeklyData): string
    {
        if ($weeklyData->count() < 2) {
            return 'STABLE';
        }

        $first = $weeklyData->first();
        $last = $weeklyData->last();

        $change = $last['productivity_score'] - $first['productivity_score'];

        if ($change > 5) return 'IMPROVING';
        if ($change < -5) return 'DECLINING';
        return 'STABLE';
    }

    /**
     * الحصول على مقاييس الكفاءة
     */
    protected function getEfficiencyMetrics(Collection $timeEntries): array
    {
        return [
            'average_session_length' => round($timeEntries->avg('hours'), 2),
            'longest_session' => $timeEntries->max('hours'),
            'shortest_session' => $timeEntries->min('hours'),
            'total_sessions' => $timeEntries->count(),
            'billable_efficiency' => $this->calculateBillableEfficiency($timeEntries),
            'time_distribution' => $this->getTimeDistribution($timeEntries),
        ];
    }

    /**
     * حساب كفاءة الفوترة
     */
    protected function calculateBillableEfficiency(Collection $timeEntries): float
    {
        $totalHours = $timeEntries->sum('hours');
        $billableHours = $timeEntries->where('is_billable', true)->sum('hours');

        return $totalHours > 0 ? round(($billableHours / $totalHours) * 100, 2) : 0;
    }

    /**
     * الحصول على توزيع الوقت
     */
    protected function getTimeDistribution(Collection $timeEntries): array
    {
        $hourlyDistribution = $timeEntries->groupBy(function ($entry) {
            return $entry->start_time->format('H');
        })->map(function ($group, $hour) {
            return [
                'hour' => (int) $hour,
                'sessions' => $group->count(),
                'total_hours' => $group->sum('hours'),
            ];
        })->sortBy('hour')->values();

        return [
            'hourly_distribution' => $hourlyDistribution->toArray(),
            'peak_hour' => $hourlyDistribution->sortByDesc('total_hours')->first()['hour'] ?? null,
            'most_productive_time' => $this->getMostProductiveTime($hourlyDistribution),
        ];
    }

    /**
     * الحصول على أكثر الأوقات إنتاجية
     */
    protected function getMostProductiveTime(Collection $hourlyDistribution): string
    {
        $peakHour = $hourlyDistribution->sortByDesc('total_hours')->first()['hour'] ?? 9;

        if ($peakHour >= 6 && $peakHour < 12) return 'MORNING';
        if ($peakHour >= 12 && $peakHour < 17) return 'AFTERNOON';
        if ($peakHour >= 17 && $peakHour < 21) return 'EVENING';
        return 'NIGHT';
    }

    /**
     * الحصول على مقارنة مع الفترة السابقة
     */
    protected function getComparisonWithPreviousPeriod(int $userId, string $period, ?int $projectId = null): array
    {
        $currentRange = $this->getDateRangeForPeriod($period);
        $previousRange = $this->getPreviousPeriodRange($period);

        $currentQuery = TimeEntry::where('user_id', $userId)
            ->whereBetween('start_time', $currentRange)
            ->whereNotNull('end_time');

        $previousQuery = TimeEntry::where('user_id', $userId)
            ->whereBetween('start_time', $previousRange)
            ->whereNotNull('end_time');

        if ($projectId) {
            $currentQuery->where('project_id', $projectId);
            $previousQuery->where('project_id', $projectId);
        }

        $currentData = $currentQuery->get();
        $previousData = $previousQuery->get();

        $currentHours = $currentData->sum('hours');
        $previousHours = $previousData->sum('hours');

        $change = $previousHours > 0 ? (($currentHours - $previousHours) / $previousHours) * 100 : 0;

        return [
            'current_period_hours' => $currentHours,
            'previous_period_hours' => $previousHours,
            'change_percentage' => round($change, 2),
            'change_direction' => $change > 0 ? 'INCREASE' : ($change < 0 ? 'DECREASE' : 'STABLE'),
            'current_billable_hours' => $currentData->where('is_billable', true)->sum('hours'),
            'previous_billable_hours' => $previousData->where('is_billable', true)->sum('hours'),
        ];
    }

    /**
     * الحصول على نطاق الفترة السابقة
     */
    protected function getPreviousPeriodRange(string $period): array
    {
        $now = now();

        switch ($period) {
            case 'today':
                return [$now->subDay()->startOfDay(), $now->subDay()->endOfDay()];
            case 'week':
                return [$now->subWeek()->startOfWeek(), $now->subWeek()->endOfWeek()];
            case 'month':
                return [$now->subMonth()->startOfMonth(), $now->subMonth()->endOfMonth()];
            case 'quarter':
                return [$now->subQuarter()->startOfQuarter(), $now->subQuarter()->endOfQuarter()];
            case 'year':
                return [$now->subYear()->startOfYear(), $now->subYear()->endOfYear()];
            default:
                return [$now->subWeek()->startOfWeek(), $now->subWeek()->endOfWeek()];
        }
    }

    /**
     * تصدير إلى Excel
     */
    protected function exportToExcel(array $reportData): string
    {
        // تنفيذ تصدير Excel
        // يمكن استخدام مكتبة مثل PhpSpreadsheet
        return 'exports/time_report_' . time() . '.xlsx';
    }

    /**
     * تصدير إلى PDF
     */
    protected function exportToPdf(array $reportData): string
    {
        // تنفيذ تصدير PDF
        // يمكن استخدام مكتبة مثل DomPDF
        return 'exports/time_report_' . time() . '.pdf';
    }

    /**
     * تصدير إلى CSV
     */
    protected function exportToCsv(array $reportData): string
    {
        // تنفيذ تصدير CSV
        return 'exports/time_report_' . time() . '.csv';
    }
}
