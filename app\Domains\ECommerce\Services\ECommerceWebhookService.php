<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceWebhook;
use App\Domains\ECommerce\Contracts\ECommercePlatformInterface;
use App\Domains\ECommerce\Exceptions\ECommerceWebhookException;
use App\Domains\ECommerce\Events\WebhookReceived;
use App\Domains\ECommerce\Events\WebhookProcessed;
use App\Domains\ECommerce\Events\WebhookFailed;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * خدمة معالجة Webhooks من منصات التجارة الإلكترونية
 * تستقبل وتعالج الأحداث الواردة من المنصات الخارجية
 */
class ECommerceWebhookService
{
    protected ECommerceSyncService $syncService;
    protected ECommerceDataTransformerService $transformer;
    protected ECommerceValidationService $validator;

    public function __construct(
        ECommerceSyncService $syncService,
        ECommerceDataTransformerService $transformer,
        ECommerceValidationService $validator
    ) {
        $this->syncService = $syncService;
        $this->transformer = $transformer;
        $this->validator = $validator;
    }

    /**
     * معالجة webhook وارد
     */
    public function processIncomingWebhook(
        Request $request,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $startTime = now();
        
        try {
            // التحقق من صحة الطلب
            $this->validateWebhookRequest($request, $integration, $driver);
            
            // إنشاء سجل webhook
            $webhook = $this->createWebhookRecord($request, $integration);
            
            Event::dispatch(new WebhookReceived($webhook, $integration));
            
            // معالجة البيانات
            $result = $this->processWebhookData($webhook, $integration, $driver);
            
            // تحديث سجل webhook
            $this->updateWebhookRecord($webhook, $result, $startTime);
            
            Event::dispatch(new WebhookProcessed($webhook, $integration, $result));
            
            return [
                'success' => true,
                'webhook_id' => $webhook->id,
                'processed_at' => now(),
                'result' => $result,
            ];
            
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);
            
            if (isset($webhook)) {
                $this->handleWebhookError($webhook, $e, $startTime);
                Event::dispatch(new WebhookFailed($webhook, $integration, $e));
            }
            
            throw new ECommerceWebhookException(
                'Webhook processing failed: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * التحقق من صحة طلب webhook
     */
    protected function validateWebhookRequest(
        Request $request,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): void {
        // التحقق من التوقيع
        if (!$driver->verifyWebhookSignature($request, $integration)) {
            throw new ECommerceWebhookException('Invalid webhook signature');
        }
        
        // التحقق من معدل الطلبات
        $this->checkRateLimit($integration);
        
        // التحقق من التكرار
        $this->checkDuplicateWebhook($request, $integration);
    }

    /**
     * إنشاء سجل webhook
     */
    protected function createWebhookRecord(
        Request $request,
        ECommerceIntegration $integration
    ): ECommerceWebhook {
        $headers = $request->headers->all();
        $payload = $request->all();
        
        // استخراج معلومات الحدث
        $eventInfo = $this->extractEventInfo($headers, $payload, $integration);
        
        return ECommerceWebhook::create([
            'integration_id' => $integration->id,
            'store_id' => $integration->store_id,
            'platform_id' => $integration->platform_id,
            'company_id' => $integration->company_id,
            'webhook_id' => $eventInfo['webhook_id'] ?? null,
            'event_type' => $eventInfo['event_type'] ?? 'unknown',
            'event_name' => $eventInfo['event_name'] ?? null,
            'topic' => $eventInfo['topic'] ?? null,
            'resource' => $eventInfo['resource'] ?? null,
            'resource_id' => $eventInfo['resource_id'] ?? null,
            'action' => $eventInfo['action'] ?? null,
            'source' => $eventInfo['source'] ?? $integration->platform->name,
            'api_version' => $eventInfo['api_version'] ?? null,
            'webhook_version' => $eventInfo['webhook_version'] ?? null,
            'format' => $request->getContentType() ?? 'json',
            'content_type' => $request->header('Content-Type'),
            'signature' => $request->header('X-Signature') ?? $request->header('X-Shopify-Hmac-Sha256'),
            'timestamp' => $this->parseWebhookTimestamp($headers, $payload),
            'delivery_id' => $request->header('X-Delivery-Id') ?? $request->header('X-Request-Id'),
            'attempt_number' => 1,
            'status' => 'received',
            'processing_status' => 'pending',
            'verification_status' => 'verified',
            'headers' => $headers,
            'payload' => $payload,
            'raw_payload' => $request->getContent(),
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'forwarded_for' => $request->header('X-Forwarded-For'),
            'real_ip' => $request->header('X-Real-IP'),
            'request_id' => $request->header('X-Request-Id'),
            'correlation_id' => $request->header('X-Correlation-Id'),
            'trace_id' => $request->header('X-Trace-Id'),
            'received_at' => now(),
            'is_verified' => true,
            'is_active' => true,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * معالجة بيانات webhook
     */
    protected function processWebhookData(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $webhook->update([
            'processing_status' => 'processing',
            'processed_at' => now(),
        ]);
        
        try {
            // تحديد نوع المعالجة بناءً على نوع الحدث
            $result = match ($webhook->event_type) {
                'product.created', 'product.updated' => $this->processProductWebhook($webhook, $integration, $driver),
                'order.created', 'order.updated', 'order.paid', 'order.fulfilled' => $this->processOrderWebhook($webhook, $integration, $driver),
                'customer.created', 'customer.updated' => $this->processCustomerWebhook($webhook, $integration, $driver),
                'inventory.updated' => $this->processInventoryWebhook($webhook, $integration, $driver),
                default => $this->processGenericWebhook($webhook, $integration, $driver),
            };
            
            $webhook->update([
                'processing_status' => 'completed',
                'is_processed' => true,
                'is_successful' => true,
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            $webhook->update([
                'processing_status' => 'failed',
                'is_processed' => true,
                'is_failed' => true,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_details' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ],
            ]);
            
            throw $e;
        }
    }

    /**
     * معالجة webhook المنتج
     */
    protected function processProductWebhook(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $payload = $webhook->getPayload();
        
        // تحويل البيانات
        $transformedData = $this->transformer->transformProduct($payload, $integration);
        
        // التحقق من صحة البيانات
        $validationResult = $this->validator->validateProduct($transformedData, $integration);
        
        if (!$validationResult['valid']) {
            throw new ECommerceWebhookException(
                'Product validation failed: ' . json_encode($validationResult['errors'])
            );
        }
        
        // البحث عن المنتج الموجود أو إنشاء جديد
        $product = $this->findOrCreateProduct($transformedData, $integration);
        
        return [
            'type' => 'product',
            'action' => $webhook->action,
            'product_id' => $product->id,
            'external_id' => $product->external_id,
            'success' => true,
        ];
    }

    /**
     * معالجة webhook الطلب
     */
    protected function processOrderWebhook(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $payload = $webhook->getPayload();
        
        // تحويل البيانات
        $transformedData = $this->transformer->transformOrder($payload, $integration);
        
        // التحقق من صحة البيانات
        $validationResult = $this->validator->validateOrder($transformedData, $integration);
        
        if (!$validationResult['valid']) {
            throw new ECommerceWebhookException(
                'Order validation failed: ' . json_encode($validationResult['errors'])
            );
        }
        
        // البحث عن الطلب الموجود أو إنشاء جديد
        $order = $this->findOrCreateOrder($transformedData, $integration);
        
        return [
            'type' => 'order',
            'action' => $webhook->action,
            'order_id' => $order->id,
            'external_id' => $order->external_id,
            'success' => true,
        ];
    }

    /**
     * معالجة webhook العميل
     */
    protected function processCustomerWebhook(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $payload = $webhook->getPayload();
        
        // تحويل البيانات
        $transformedData = $this->transformer->transformCustomer($payload, $integration);
        
        // التحقق من صحة البيانات
        $validationResult = $this->validator->validateCustomer($transformedData, $integration);
        
        if (!$validationResult['valid']) {
            throw new ECommerceWebhookException(
                'Customer validation failed: ' . json_encode($validationResult['errors'])
            );
        }
        
        // البحث عن العميل الموجود أو إنشاء جديد
        $customer = $this->findOrCreateCustomer($transformedData, $integration);
        
        return [
            'type' => 'customer',
            'action' => $webhook->action,
            'customer_id' => $customer->id,
            'external_id' => $customer->external_id,
            'success' => true,
        ];
    }

    /**
     * معالجة webhook المخزون
     */
    protected function processInventoryWebhook(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        $payload = $webhook->getPayload();
        
        // تحديث كمية المخزون
        $productId = $payload['product_id'] ?? $payload['id'] ?? null;
        $quantity = $payload['quantity'] ?? $payload['inventory_quantity'] ?? null;
        
        if ($productId && $quantity !== null) {
            $product = $integration->products()
                ->where('external_id', $productId)
                ->first();
                
            if ($product) {
                $product->update([
                    'inventory_quantity' => $quantity,
                    'in_stock' => $quantity > 0,
                    'last_synced_at' => now(),
                ]);
                
                return [
                    'type' => 'inventory',
                    'action' => 'updated',
                    'product_id' => $product->id,
                    'external_id' => $product->external_id,
                    'quantity' => $quantity,
                    'success' => true,
                ];
            }
        }
        
        throw new ECommerceWebhookException('Product not found for inventory update');
    }

    /**
     * معالجة webhook عامة
     */
    protected function processGenericWebhook(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver
    ): array {
        // معالجة عامة للأحداث غير المعروفة
        Log::info('Processing generic webhook', [
            'webhook_id' => $webhook->id,
            'event_type' => $webhook->event_type,
            'integration_id' => $integration->id,
        ]);
        
        return [
            'type' => 'generic',
            'action' => $webhook->action,
            'event_type' => $webhook->event_type,
            'success' => true,
        ];
    }

    /**
     * البحث عن المنتج أو إنشاء جديد
     */
    protected function findOrCreateProduct(array $data, ECommerceIntegration $integration)
    {
        $existingProduct = $integration->products()
            ->where('external_id', $data['external_id'])
            ->first();
            
        if ($existingProduct) {
            $existingProduct->update(array_merge($data, [
                'is_synced' => true,
                'last_synced_at' => now(),
                'updated_by' => auth()->id(),
            ]));
            
            return $existingProduct;
        }
        
        return $integration->products()->create(array_merge($data, [
            'integration_id' => $integration->id,
            'store_id' => $integration->store_id,
            'platform_id' => $integration->platform_id,
            'company_id' => $integration->company_id,
            'is_synced' => true,
            'last_synced_at' => now(),
            'created_by' => auth()->id(),
        ]));
    }

    /**
     * البحث عن الطلب أو إنشاء جديد
     */
    protected function findOrCreateOrder(array $data, ECommerceIntegration $integration)
    {
        $existingOrder = $integration->orders()
            ->where('external_id', $data['external_id'])
            ->first();
            
        if ($existingOrder) {
            $existingOrder->update(array_merge($data, [
                'is_synced' => true,
                'last_synced_at' => now(),
                'updated_by' => auth()->id(),
            ]));
            
            return $existingOrder;
        }
        
        return $integration->orders()->create(array_merge($data, [
            'integration_id' => $integration->id,
            'store_id' => $integration->store_id,
            'platform_id' => $integration->platform_id,
            'company_id' => $integration->company_id,
            'is_synced' => true,
            'last_synced_at' => now(),
            'created_by' => auth()->id(),
        ]));
    }

    /**
     * البحث عن العميل أو إنشاء جديد
     */
    protected function findOrCreateCustomer(array $data, ECommerceIntegration $integration)
    {
        $existingCustomer = $integration->customers()
            ->where('external_id', $data['external_id'])
            ->first();
            
        if ($existingCustomer) {
            $existingCustomer->update(array_merge($data, [
                'is_synced' => true,
                'last_synced_at' => now(),
                'updated_by' => auth()->id(),
            ]));
            
            return $existingCustomer;
        }
        
        return $integration->customers()->create(array_merge($data, [
            'integration_id' => $integration->id,
            'store_id' => $integration->store_id,
            'platform_id' => $integration->platform_id,
            'company_id' => $integration->company_id,
            'is_synced' => true,
            'last_synced_at' => now(),
            'created_by' => auth()->id(),
        ]));
    }

    /**
     * تحديث سجل webhook
     */
    protected function updateWebhookRecord(
        ECommerceWebhook $webhook,
        array $result,
        Carbon $startTime
    ): void {
        $webhook->update([
            'status' => 'completed',
            'completed_at' => now(),
            'processing_time' => now()->diffInMilliseconds($startTime),
            'total_time' => now()->diffInMilliseconds($webhook->received_at),
            'response_data' => $result,
            'is_successful' => true,
            'is_complete' => true,
        ]);
    }

    /**
     * معالجة خطأ webhook
     */
    protected function handleWebhookError(
        ECommerceWebhook $webhook,
        \Exception $e,
        Carbon $startTime
    ): void {
        $webhook->update([
            'status' => 'failed',
            'failed_at' => now(),
            'processing_time' => now()->diffInMilliseconds($startTime),
            'total_time' => now()->diffInMilliseconds($webhook->received_at),
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'error_type' => get_class($e),
            'error_details' => [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ],
            'is_failed' => true,
            'is_complete' => true,
        ]);
    }

    /**
     * استخراج معلومات الحدث
     */
    protected function extractEventInfo(array $headers, array $payload, ECommerceIntegration $integration): array
    {
        $platform = $integration->platform->slug;
        
        return match ($platform) {
            'shopify' => $this->extractShopifyEventInfo($headers, $payload),
            'woocommerce' => $this->extractWooCommerceEventInfo($headers, $payload),
            'magento' => $this->extractMagentoEventInfo($headers, $payload),
            default => $this->extractGenericEventInfo($headers, $payload),
        };
    }

    /**
     * استخراج معلومات حدث Shopify
     */
    protected function extractShopifyEventInfo(array $headers, array $payload): array
    {
        $topic = $headers['x-shopify-topic'][0] ?? null;
        $webhookId = $headers['x-shopify-webhook-id'][0] ?? null;
        $apiVersion = $headers['x-shopify-api-version'][0] ?? null;
        
        return [
            'webhook_id' => $webhookId,
            'event_type' => $topic,
            'event_name' => $topic,
            'topic' => $topic,
            'resource' => explode('/', $topic)[0] ?? null,
            'action' => explode('/', $topic)[1] ?? null,
            'resource_id' => $payload['id'] ?? null,
            'api_version' => $apiVersion,
            'webhook_version' => '1.0',
        ];
    }

    /**
     * استخراج معلومات حدث WooCommerce
     */
    protected function extractWooCommerceEventInfo(array $headers, array $payload): array
    {
        $event = $headers['x-wc-webhook-event'][0] ?? null;
        $resource = $headers['x-wc-webhook-resource'][0] ?? null;
        $webhookId = $headers['x-wc-webhook-id'][0] ?? null;
        
        return [
            'webhook_id' => $webhookId,
            'event_type' => $resource . '.' . $event,
            'event_name' => $event,
            'topic' => $resource . '.' . $event,
            'resource' => $resource,
            'action' => $event,
            'resource_id' => $payload['id'] ?? null,
            'api_version' => 'v3',
            'webhook_version' => '1.0',
        ];
    }

    /**
     * استخراج معلومات حدث Magento
     */
    protected function extractMagentoEventInfo(array $headers, array $payload): array
    {
        // Magento webhook implementation
        return [
            'webhook_id' => null,
            'event_type' => 'unknown',
            'event_name' => null,
            'topic' => null,
            'resource' => null,
            'action' => null,
            'resource_id' => null,
            'api_version' => '2.0',
            'webhook_version' => '1.0',
        ];
    }

    /**
     * استخراج معلومات حدث عامة
     */
    protected function extractGenericEventInfo(array $headers, array $payload): array
    {
        return [
            'webhook_id' => null,
            'event_type' => 'unknown',
            'event_name' => null,
            'topic' => null,
            'resource' => null,
            'action' => null,
            'resource_id' => $payload['id'] ?? null,
            'api_version' => null,
            'webhook_version' => '1.0',
        ];
    }

    /**
     * تحليل timestamp من webhook
     */
    protected function parseWebhookTimestamp(array $headers, array $payload): ?Carbon
    {
        // البحث عن timestamp في headers
        $timestampHeaders = ['x-timestamp', 'x-shopify-timestamp', 'x-wc-timestamp'];
        
        foreach ($timestampHeaders as $header) {
            if (isset($headers[$header][0])) {
                try {
                    return Carbon::parse($headers[$header][0]);
                } catch (\Exception $e) {
                    // تجاهل الأخطاء والمحاولة التالية
                }
            }
        }
        
        // البحث عن timestamp في payload
        $timestampFields = ['timestamp', 'created_at', 'updated_at', 'date_created', 'date_modified'];
        
        foreach ($timestampFields as $field) {
            if (isset($payload[$field])) {
                try {
                    return Carbon::parse($payload[$field]);
                } catch (\Exception $e) {
                    // تجاهل الأخطاء والمحاولة التالية
                }
            }
        }
        
        return now();
    }

    /**
     * التحقق من معدل الطلبات
     */
    protected function checkRateLimit(ECommerceIntegration $integration): void
    {
        $key = "webhook_rate_limit_{$integration->id}";
        $limit = $integration->webhook_rate_limit ?? 100; // طلبات في الدقيقة
        $window = 60; // ثانية
        
        $current = Cache::get($key, 0);
        
        if ($current >= $limit) {
            throw new ECommerceWebhookException('Webhook rate limit exceeded');
        }
        
        Cache::put($key, $current + 1, $window);
    }

    /**
     * التحقق من تكرار webhook
     */
    protected function checkDuplicateWebhook(Request $request, ECommerceIntegration $integration): void
    {
        $deliveryId = $request->header('X-Delivery-Id') ?? $request->header('X-Request-Id');
        
        if ($deliveryId) {
            $existing = ECommerceWebhook::where([
                'integration_id' => $integration->id,
                'delivery_id' => $deliveryId,
            ])->exists();
            
            if ($existing) {
                throw new ECommerceWebhookException('Duplicate webhook detected');
            }
        }
    }

    /**
     * إعادة معالجة webhook فاشل
     */
    public function retryFailedWebhook(ECommerceWebhook $webhook): array
    {
        if (!$webhook->isFailed() || !$webhook->canRetry()) {
            throw new ECommerceWebhookException('Webhook cannot be retried');
        }
        
        $integration = $webhook->integration;
        $driver = app($integration->platform->driver_class);
        
        $webhook->update([
            'retry_count' => $webhook->retry_count + 1,
            'retried_at' => now(),
            'status' => 'retrying',
            'processing_status' => 'pending',
            'is_failed' => false,
        ]);
        
        try {
            $result = $this->processWebhookData($webhook, $integration, $driver);
            
            return [
                'success' => true,
                'webhook_id' => $webhook->id,
                'retry_count' => $webhook->retry_count,
                'result' => $result,
            ];
            
        } catch (\Exception $e) {
            $this->handleWebhookError($webhook, $e, now());
            throw $e;
        }
    }

    /**
     * الحصول على إحصائيات webhooks
     */
    public function getWebhookStats(ECommerceIntegration $integration, int $days = 30): array
    {
        $webhooks = $integration->webhooks()
            ->where('created_at', '>=', now()->subDays($days))
            ->get();
            
        $total = $webhooks->count();
        $successful = $webhooks->where('is_successful', true)->count();
        $failed = $webhooks->where('is_failed', true)->count();
        $pending = $webhooks->where('is_pending', true)->count();
        
        $avgProcessingTime = $webhooks->where('processing_time', '>', 0)->avg('processing_time') ?? 0;
        
        return [
            'total_webhooks' => $total,
            'successful_webhooks' => $successful,
            'failed_webhooks' => $failed,
            'pending_webhooks' => $pending,
            'success_rate' => $total > 0 ? ($successful / $total) * 100 : 0,
            'failure_rate' => $total > 0 ? ($failed / $total) * 100 : 0,
            'avg_processing_time' => round($avgProcessingTime, 2),
            'event_types' => $webhooks->groupBy('event_type')->map->count(),
            'daily_counts' => $webhooks->groupBy(function ($webhook) {
                return $webhook->created_at->format('Y-m-d');
            })->map->count(),
        ];
    }
}
