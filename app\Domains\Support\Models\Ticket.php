<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasFiles;
use App\Domains\Shared\Traits\HasActivities;
use Laravel\Scout\Searchable;

/**
 * نموذج التذكرة - Enterprise Grade Support Ticket
 * يدعم الذكاء الاصطناعي والتكامل متعدد القنوات
 */
class Ticket extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasFiles, HasActivities, Searchable;

    protected $fillable = [
        'ticket_number',
        'subject',
        'description',
        'status',
        'priority',
        'category_id',
        'subcategory_id',
        'department_id',
        'customer_id',
        'assigned_to',
        'created_by',
        'channel',
        'source_reference',
        'language',
        'country_code',
        'timezone',
        'tags',
        'custom_fields',
        'ai_classification',
        'ai_sentiment',
        'ai_suggested_solutions',
        'sla_due_at',
        'first_response_at',
        'resolved_at',
        'closed_at',
        'satisfaction_rating',
        'satisfaction_feedback',
        'resolution_summary',
        'escalation_level',
        'escalated_at',
        'escalated_by',
        'escalation_reason',
        'is_internal',
        'is_billable',
        'billable_hours',
        'estimated_hours',
        'actual_hours',
        'related_order_id',
        'related_project_id',
        'related_invoice_id',
        'metadata',
    ];

    protected $casts = [
        'tags' => 'array',
        'custom_fields' => 'array',
        'ai_classification' => 'array',
        'ai_sentiment' => 'array',
        'ai_suggested_solutions' => 'array',
        'sla_due_at' => 'datetime',
        'first_response_at' => 'datetime',
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime',
        'escalated_at' => 'datetime',
        'is_internal' => 'boolean',
        'is_billable' => 'boolean',
        'billable_hours' => 'decimal:2',
        'estimated_hours' => 'decimal:2',
        'actual_hours' => 'decimal:2',
        'satisfaction_rating' => 'integer',
        'escalation_level' => 'integer',
        'metadata' => 'array',
    ];

    protected $dates = [
        'sla_due_at',
        'first_response_at',
        'resolved_at',
        'closed_at',
        'escalated_at',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\CRM\Models\Customer::class);
    }

    /**
     * العلاقة مع الوكيل المسؤول
     */
    public function assignedAgent(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع منشئ التذكرة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع الفئة
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class, 'category_id');
    }

    /**
     * العلاقة مع الفئة الفرعية
     */
    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class, 'subcategory_id');
    }

    /**
     * العلاقة مع القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * العلاقة مع الردود
     */
    public function replies(): HasMany
    {
        return $this->hasMany(TicketReply::class)->orderBy('created_at');
    }

    /**
     * العلاقة مع التصعيدات
     */
    public function escalations(): HasMany
    {
        return $this->hasMany(TicketEscalation::class);
    }

    /**
     * العلاقة مع سجل SLA
     */
    public function slaLogs(): HasMany
    {
        return $this->hasMany(TicketSlaLog::class);
    }

    /**
     * العلاقة مع التقييمات
     */
    public function satisfactionSurveys(): HasMany
    {
        return $this->hasMany(TicketSatisfactionSurvey::class);
    }

    /**
     * العلاقة مع الوسوم
     */
    public function ticketTags(): BelongsToMany
    {
        return $this->belongsToMany(TicketTag::class, 'ticket_tag_pivot');
    }

    /**
     * العلاقة مع المشروع المرتبط
     */
    public function relatedProject(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Projects\Models\Project::class, 'related_project_id');
    }

    /**
     * العلاقة مع الطلب المرتبط
     */
    public function relatedOrder(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Ecommerce\Models\Order::class, 'related_order_id');
    }

    /**
     * العلاقة مع الفاتورة المرتبطة
     */
    public function relatedInvoice(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Invoice::class, 'related_invoice_id');
    }

    /**
     * الحصول على رقم التذكرة المنسق
     */
    public function getFormattedTicketNumberAttribute(): string
    {
        return 'TKT-' . str_pad($this->ticket_number, 6, '0', STR_PAD_LEFT);
    }

    /**
     * التحقق من انتهاء SLA
     */
    public function getIsSlaBreachedAttribute(): bool
    {
        return $this->sla_due_at && now()->isAfter($this->sla_due_at) && !in_array($this->status, ['resolved', 'closed']);
    }

    /**
     * الحصول على الوقت المتبقي لـ SLA
     */
    public function getSlaTimeRemainingAttribute(): ?int
    {
        if (!$this->sla_due_at || in_array($this->status, ['resolved', 'closed'])) {
            return null;
        }

        return now()->diffInMinutes($this->sla_due_at, false);
    }

    /**
     * الحصول على مستوى الأولوية بالألوان
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'critical' => '#dc3545',
            'high' => '#fd7e14',
            'medium' => '#ffc107',
            'low' => '#28a745',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على حالة الشعور من الذكاء الاصطناعي
     */
    public function getAiSentimentLabelAttribute(): string
    {
        if (!$this->ai_sentiment) {
            return 'غير محدد';
        }

        return match ($this->ai_sentiment['label'] ?? 'neutral') {
            'positive' => 'إيجابي',
            'negative' => 'سلبي',
            'neutral' => 'محايد',
            'angry' => 'غاضب',
            'frustrated' => 'محبط',
            'satisfied' => 'راضي',
            default => 'غير محدد',
        };
    }

    /**
     * تعيين التذكرة لوكيل
     */
    public function assignTo(int $agentId, string $reason = null): bool
    {
        $this->update([
            'assigned_to' => $agentId,
            'status' => 'assigned',
        ]);

        // تسجيل النشاط
        $this->activities()->create([
            'type' => 'assigned',
            'description' => "تم تعيين التذكرة للوكيل: {$this->assignedAgent->name}",
            'metadata' => ['reason' => $reason],
            'created_by' => auth()->id(),
        ]);

        return true;
    }

    /**
     * تصعيد التذكرة
     */
    public function escalate(int $escalatedBy, string $reason, int $level = null): TicketEscalation
    {
        $currentLevel = $this->escalation_level ?? 0;
        $newLevel = $level ?? ($currentLevel + 1);

        $this->update([
            'escalation_level' => $newLevel,
            'escalated_at' => now(),
            'escalated_by' => $escalatedBy,
            'escalation_reason' => $reason,
            'status' => 'escalated',
        ]);

        return $this->escalations()->create([
            'escalated_by' => $escalatedBy,
            'escalated_to' => $this->getEscalationTarget($newLevel),
            'level' => $newLevel,
            'reason' => $reason,
            'escalated_at' => now(),
        ]);
    }

    /**
     * حل التذكرة
     */
    public function resolve(string $resolution, int $resolvedBy): bool
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution_summary' => $resolution,
        ]);

        // تسجيل النشاط
        $this->activities()->create([
            'type' => 'resolved',
            'description' => 'تم حل التذكرة',
            'metadata' => ['resolution' => $resolution],
            'created_by' => $resolvedBy,
        ]);

        // إرسال استبيان الرضا
        $this->sendSatisfactionSurvey();

        return true;
    }

    /**
     * إغلاق التذكرة
     */
    public function close(int $closedBy, string $reason = null): bool
    {
        $this->update([
            'status' => 'closed',
            'closed_at' => now(),
        ]);

        // تسجيل النشاط
        $this->activities()->create([
            'type' => 'closed',
            'description' => 'تم إغلاق التذكرة',
            'metadata' => ['reason' => $reason],
            'created_by' => $closedBy,
        ]);

        return true;
    }

    /**
     * إعادة فتح التذكرة
     */
    public function reopen(int $reopenedBy, string $reason): bool
    {
        $this->update([
            'status' => 'open',
            'resolved_at' => null,
            'closed_at' => null,
        ]);

        // تسجيل النشاط
        $this->activities()->create([
            'type' => 'reopened',
            'description' => 'تم إعادة فتح التذكرة',
            'metadata' => ['reason' => $reason],
            'created_by' => $reopenedBy,
        ]);

        return true;
    }

    /**
     * إضافة رد على التذكرة
     */
    public function addReply(array $replyData): TicketReply
    {
        $reply = $this->replies()->create($replyData);

        // تحديث وقت الاستجابة الأولى
        if (!$this->first_response_at && $replyData['reply_type'] === 'agent') {
            $this->update(['first_response_at' => now()]);
        }

        // تحديث حالة التذكرة
        if ($this->status === 'open' && $replyData['reply_type'] === 'agent') {
            $this->update(['status' => 'in_progress']);
        }

        return $reply;
    }

    /**
     * إرسال استبيان الرضا
     */
    protected function sendSatisfactionSurvey(): void
    {
        // سيتم تنفيذها في الخدمة
    }

    /**
     * الحصول على هدف التصعيد
     */
    protected function getEscalationTarget(int $level): ?int
    {
        // منطق تحديد المشرف حسب المستوى
        return null;
    }

    /**
     * فلترة التذاكر حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة التذاكر حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة التذاكر المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('sla_due_at', '<', now())
                    ->whereNotIn('status', ['resolved', 'closed']);
    }

    /**
     * فلترة التذاكر المعينة لوكيل
     */
    public function scopeAssignedTo($query, int $agentId)
    {
        return $query->where('assigned_to', $agentId);
    }

    /**
     * فلترة التذاكر حسب القناة
     */
    public function scopeFromChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * البحث في التذاكر
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('ticket_number', 'LIKE', "%{$search}%");
        });
    }

    /**
     * إعدادات البحث للـ Scout
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'ticket_number' => $this->ticket_number,
            'subject' => $this->subject,
            'description' => $this->description,
            'status' => $this->status,
            'priority' => $this->priority,
            'tags' => $this->tags,
            'customer_name' => $this->customer->name ?? '',
            'category_name' => $this->category->name ?? '',
        ];
    }
}
