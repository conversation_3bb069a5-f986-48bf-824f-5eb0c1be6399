<?php

namespace App\Domains\Integration\Services\RateLimiter;

use App\Domains\Integration\Contracts\RateLimiterInterface;
use App\Domains\Integration\Events\RateLimitExceeded;
use App\Domains\Integration\Services\Metrics\RateLimitMetrics;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Advanced Distributed Rate Limiter with Multiple Algorithms
 * 
 * Features:
 * - Token Bucket Algorithm with burst handling
 * - Sliding Window Log for precise rate limiting
 * - Fixed Window Counter with distributed coordination
 * - Sliding Window Counter for smooth rate limiting
 * - Adaptive rate limiting based on system load
 * - Hierarchical rate limiting (global, tenant, user, API key)
 * - Geographic rate limiting
 * - Time-based rate limiting (different limits for different times)
 * - Rate limit sharing across multiple instances
 * - Graceful degradation under high load
 * - Custom rate limit policies
 */
class DistributedRateLimiter implements RateLimiterInterface
{
    protected RateLimitMetrics $metrics;
    protected array $config;
    protected array $algorithms;

    public function __construct(RateLimitMetrics $metrics, array $config = [])
    {
        $this->metrics = $metrics;
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initializeAlgorithms();
    }

    /**
     * Check if request is allowed under rate limit
     */
    public function isAllowed(string $key, array $context = []): bool
    {
        $algorithm = $context['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $context['limit'] ?? $this->config['default_limit'];
        $window = $context['window'] ?? $this->config['default_window'];

        // Apply hierarchical rate limiting
        if (!$this->checkHierarchicalLimits($key, $context)) {
            return false;
        }

        // Apply geographic rate limiting
        if (!$this->checkGeographicLimits($key, $context)) {
            return false;
        }

        // Apply time-based rate limiting
        if (!$this->checkTimeBasedLimits($key, $context)) {
            return false;
        }

        // Execute the specified algorithm
        $allowed = $this->executeAlgorithm($algorithm, $key, $limit, $window, $context);

        if (!$allowed) {
            $this->handleRateLimitExceeded($key, $context);
        }

        return $allowed;
    }

    /**
     * Token Bucket Algorithm Implementation
     */
    protected function tokenBucket(string $key, int $limit, int $window, array $context): bool
    {
        $bucketKey = "rate_limit:token_bucket:{$key}";
        $now = microtime(true);
        
        // Get current bucket state
        $bucket = Redis::hmget($bucketKey, ['tokens', 'last_refill']);
        $tokens = (float) ($bucket[0] ?? $limit);
        $lastRefill = (float) ($bucket[1] ?? $now);
        
        // Calculate tokens to add based on time elapsed
        $timeElapsed = $now - $lastRefill;
        $tokensToAdd = ($timeElapsed / $window) * $limit;
        $tokens = min($limit, $tokens + $tokensToAdd);
        
        // Check if request can be served
        if ($tokens >= 1) {
            $tokens -= 1;
            
            // Update bucket state
            Redis::hmset($bucketKey, [
                'tokens' => $tokens,
                'last_refill' => $now
            ]);
            Redis::expire($bucketKey, $window * 2);
            
            $this->metrics->recordAllowedRequest($key);
            return true;
        }
        
        // Update last refill time even if request is denied
        Redis::hmset($bucketKey, [
            'tokens' => $tokens,
            'last_refill' => $now
        ]);
        Redis::expire($bucketKey, $window * 2);
        
        $this->metrics->recordDeniedRequest($key);
        return false;
    }

    /**
     * Sliding Window Log Algorithm Implementation
     */
    protected function slidingWindowLog(string $key, int $limit, int $window, array $context): bool
    {
        $logKey = "rate_limit:sliding_log:{$key}";
        $now = microtime(true);
        $windowStart = $now - $window;
        
        // Remove old entries
        Redis::zremrangebyscore($logKey, 0, $windowStart);
        
        // Count current requests in window
        $currentCount = Redis::zcard($logKey);
        
        if ($currentCount < $limit) {
            // Add current request
            Redis::zadd($logKey, $now, uniqid());
            Redis::expire($logKey, $window * 2);
            
            $this->metrics->recordAllowedRequest($key);
            return true;
        }
        
        $this->metrics->recordDeniedRequest($key);
        return false;
    }

    /**
     * Fixed Window Counter Algorithm Implementation
     */
    protected function fixedWindowCounter(string $key, int $limit, int $window, array $context): bool
    {
        $now = time();
        $windowStart = floor($now / $window) * $window;
        $counterKey = "rate_limit:fixed_window:{$key}:{$windowStart}";
        
        $current = Redis::incr($counterKey);
        
        if ($current === 1) {
            Redis::expire($counterKey, $window);
        }
        
        if ($current <= $limit) {
            $this->metrics->recordAllowedRequest($key);
            return true;
        }
        
        $this->metrics->recordDeniedRequest($key);
        return false;
    }

    /**
     * Sliding Window Counter Algorithm Implementation
     */
    protected function slidingWindowCounter(string $key, int $limit, int $window, array $context): bool
    {
        $now = time();
        $currentWindow = floor($now / $window) * $window;
        $previousWindow = $currentWindow - $window;
        
        $currentKey = "rate_limit:sliding_counter:{$key}:{$currentWindow}";
        $previousKey = "rate_limit:sliding_counter:{$key}:{$previousWindow}";
        
        $currentCount = Redis::incr($currentKey);
        $previousCount = (int) Redis::get($previousKey) ?: 0;
        
        if ($currentCount === 1) {
            Redis::expire($currentKey, $window * 2);
        }
        
        // Calculate weighted count
        $timeIntoWindow = $now - $currentWindow;
        $weightedPreviousCount = $previousCount * (1 - ($timeIntoWindow / $window));
        $totalCount = $currentCount + $weightedPreviousCount;
        
        if ($totalCount <= $limit) {
            $this->metrics->recordAllowedRequest($key);
            return true;
        }
        
        // Decrement since we're not allowing this request
        Redis::decr($currentKey);
        $this->metrics->recordDeniedRequest($key);
        return false;
    }

    /**
     * Adaptive Rate Limiting based on system load
     */
    protected function adaptiveRateLimit(string $key, int $limit, int $window, array $context): bool
    {
        $systemLoad = $this->getSystemLoad();
        $adaptedLimit = $this->calculateAdaptedLimit($limit, $systemLoad, $context);
        
        // Use sliding window counter as base algorithm
        return $this->slidingWindowCounter($key, $adaptedLimit, $window, $context);
    }

    /**
     * Check hierarchical rate limits (global -> tenant -> user -> API key)
     */
    protected function checkHierarchicalLimits(string $key, array $context): bool
    {
        $hierarchy = $context['hierarchy'] ?? [];
        
        foreach ($hierarchy as $level => $config) {
            $hierarchyKey = "{$level}:{$config['id']}";
            $allowed = $this->executeAlgorithm(
                $config['algorithm'] ?? 'sliding_window_counter',
                $hierarchyKey,
                $config['limit'],
                $config['window'],
                $context
            );
            
            if (!$allowed) {
                Log::info("Rate limit exceeded at {$level} level", [
                    'key' => $key,
                    'level' => $level,
                    'config' => $config
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check geographic rate limits
     */
    protected function checkGeographicLimits(string $key, array $context): bool
    {
        $geoConfig = $context['geographic'] ?? null;
        
        if (!$geoConfig) {
            return true;
        }
        
        $clientCountry = $context['client_country'] ?? 'unknown';
        $clientRegion = $context['client_region'] ?? 'unknown';
        
        // Check country-specific limits
        if (isset($geoConfig['countries'][$clientCountry])) {
            $countryConfig = $geoConfig['countries'][$clientCountry];
            $geoKey = "geo:country:{$clientCountry}:{$key}";
            
            $allowed = $this->executeAlgorithm(
                $countryConfig['algorithm'] ?? 'sliding_window_counter',
                $geoKey,
                $countryConfig['limit'],
                $countryConfig['window'],
                $context
            );
            
            if (!$allowed) {
                return false;
            }
        }
        
        // Check region-specific limits
        if (isset($geoConfig['regions'][$clientRegion])) {
            $regionConfig = $geoConfig['regions'][$clientRegion];
            $geoKey = "geo:region:{$clientRegion}:{$key}";
            
            $allowed = $this->executeAlgorithm(
                $regionConfig['algorithm'] ?? 'sliding_window_counter',
                $geoKey,
                $regionConfig['limit'],
                $regionConfig['window'],
                $context
            );
            
            if (!$allowed) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check time-based rate limits
     */
    protected function checkTimeBasedLimits(string $key, array $context): bool
    {
        $timeConfig = $context['time_based'] ?? null;
        
        if (!$timeConfig) {
            return true;
        }
        
        $now = Carbon::now();
        $hour = $now->hour;
        $dayOfWeek = $now->dayOfWeek;
        
        // Check hourly limits
        if (isset($timeConfig['hourly'][$hour])) {
            $hourlyConfig = $timeConfig['hourly'][$hour];
            $timeKey = "time:hour:{$hour}:{$key}";
            
            $allowed = $this->executeAlgorithm(
                $hourlyConfig['algorithm'] ?? 'sliding_window_counter',
                $timeKey,
                $hourlyConfig['limit'],
                $hourlyConfig['window'],
                $context
            );
            
            if (!$allowed) {
                return false;
            }
        }
        
        // Check day-of-week limits
        if (isset($timeConfig['daily'][$dayOfWeek])) {
            $dailyConfig = $timeConfig['daily'][$dayOfWeek];
            $timeKey = "time:day:{$dayOfWeek}:{$key}";
            
            $allowed = $this->executeAlgorithm(
                $dailyConfig['algorithm'] ?? 'sliding_window_counter',
                $timeKey,
                $dailyConfig['limit'],
                $dailyConfig['window'],
                $context
            );
            
            if (!$allowed) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Execute the specified rate limiting algorithm
     */
    protected function executeAlgorithm(string $algorithm, string $key, int $limit, int $window, array $context): bool
    {
        if (!isset($this->algorithms[$algorithm])) {
            Log::warning("Unknown rate limiting algorithm: {$algorithm}, falling back to default");
            $algorithm = $this->config['default_algorithm'];
        }

        return $this->algorithms[$algorithm]($key, $limit, $window, $context);
    }

    /**
     * Handle rate limit exceeded event
     */
    protected function handleRateLimitExceeded(string $key, array $context): void
    {
        $this->metrics->recordRateLimitExceeded($key);
        
        Event::dispatch(new RateLimitExceeded($key, $context));
        
        Log::info("Rate limit exceeded", [
            'key' => $key,
            'context' => $context,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get current system load
     */
    protected function getSystemLoad(): float
    {
        // Get system load average (Linux/Unix)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0] ?? 0.0;
        }
        
        // Fallback: use CPU usage from Redis metrics
        return (float) Redis::get('system:cpu_usage') ?: 0.0;
    }

    /**
     * Calculate adapted limit based on system load
     */
    protected function calculateAdaptedLimit(int $baseLimit, float $systemLoad, array $context): int
    {
        $loadThreshold = $context['load_threshold'] ?? 0.8;
        $minReduction = $context['min_reduction'] ?? 0.5;
        
        if ($systemLoad <= $loadThreshold) {
            return $baseLimit;
        }
        
        // Reduce limit based on load
        $reductionFactor = max($minReduction, 1 - (($systemLoad - $loadThreshold) / (1 - $loadThreshold)));
        
        return max(1, (int) ($baseLimit * $reductionFactor));
    }

    /**
     * Initialize available algorithms
     */
    protected function initializeAlgorithms(): void
    {
        $this->algorithms = [
            'token_bucket' => [$this, 'tokenBucket'],
            'sliding_window_log' => [$this, 'slidingWindowLog'],
            'fixed_window_counter' => [$this, 'fixedWindowCounter'],
            'sliding_window_counter' => [$this, 'slidingWindowCounter'],
            'adaptive' => [$this, 'adaptiveRateLimit'],
        ];
    }

    /**
     * Get default configuration
     */
    protected function getDefaultConfig(): array
    {
        return [
            'default_algorithm' => 'sliding_window_counter',
            'default_limit' => 100,
            'default_window' => 60,
            'enable_metrics' => true,
            'enable_events' => true,
        ];
    }

    /**
     * Get rate limit status for a key
     */
    public function getStatus(string $key, array $context = []): array
    {
        $algorithm = $context['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $context['limit'] ?? $this->config['default_limit'];
        $window = $context['window'] ?? $this->config['default_window'];
        
        switch ($algorithm) {
            case 'token_bucket':
                return $this->getTokenBucketStatus($key, $limit, $window);
            
            case 'sliding_window_log':
                return $this->getSlidingWindowLogStatus($key, $limit, $window);
            
            case 'fixed_window_counter':
                return $this->getFixedWindowCounterStatus($key, $limit, $window);
            
            case 'sliding_window_counter':
                return $this->getSlidingWindowCounterStatus($key, $limit, $window);
            
            default:
                return [
                    'algorithm' => $algorithm,
                    'limit' => $limit,
                    'window' => $window,
                    'remaining' => $limit,
                    'reset_time' => time() + $window,
                ];
        }
    }

    /**
     * Reset rate limit for a key
     */
    public function reset(string $key): void
    {
        $patterns = [
            "rate_limit:token_bucket:{$key}",
            "rate_limit:sliding_log:{$key}",
            "rate_limit:fixed_window:{$key}:*",
            "rate_limit:sliding_counter:{$key}:*",
        ];
        
        foreach ($patterns as $pattern) {
            $keys = Redis::keys($pattern);
            if (!empty($keys)) {
                Redis::del($keys);
            }
        }
        
        Log::info("Rate limit reset for key: {$key}");
    }

    // Status methods for different algorithms
    protected function getTokenBucketStatus(string $key, int $limit, int $window): array
    {
        $bucketKey = "rate_limit:token_bucket:{$key}";
        $bucket = Redis::hmget($bucketKey, ['tokens', 'last_refill']);
        $tokens = (float) ($bucket[0] ?? $limit);
        
        return [
            'algorithm' => 'token_bucket',
            'limit' => $limit,
            'window' => $window,
            'remaining' => (int) $tokens,
            'reset_time' => time() + $window,
        ];
    }

    protected function getSlidingWindowLogStatus(string $key, int $limit, int $window): array
    {
        $logKey = "rate_limit:sliding_log:{$key}";
        $now = microtime(true);
        $windowStart = $now - $window;
        
        Redis::zremrangebyscore($logKey, 0, $windowStart);
        $currentCount = Redis::zcard($logKey);
        
        return [
            'algorithm' => 'sliding_window_log',
            'limit' => $limit,
            'window' => $window,
            'remaining' => max(0, $limit - $currentCount),
            'reset_time' => time() + $window,
        ];
    }

    protected function getFixedWindowCounterStatus(string $key, int $limit, int $window): array
    {
        $now = time();
        $windowStart = floor($now / $window) * $window;
        $counterKey = "rate_limit:fixed_window:{$key}:{$windowStart}";
        
        $current = (int) Redis::get($counterKey) ?: 0;
        
        return [
            'algorithm' => 'fixed_window_counter',
            'limit' => $limit,
            'window' => $window,
            'remaining' => max(0, $limit - $current),
            'reset_time' => $windowStart + $window,
        ];
    }

    protected function getSlidingWindowCounterStatus(string $key, int $limit, int $window): array
    {
        $now = time();
        $currentWindow = floor($now / $window) * $window;
        $previousWindow = $currentWindow - $window;
        
        $currentKey = "rate_limit:sliding_counter:{$key}:{$currentWindow}";
        $previousKey = "rate_limit:sliding_counter:{$key}:{$previousWindow}";
        
        $currentCount = (int) Redis::get($currentKey) ?: 0;
        $previousCount = (int) Redis::get($previousKey) ?: 0;
        
        $timeIntoWindow = $now - $currentWindow;
        $weightedPreviousCount = $previousCount * (1 - ($timeIntoWindow / $window));
        $totalCount = $currentCount + $weightedPreviousCount;
        
        return [
            'algorithm' => 'sliding_window_counter',
            'limit' => $limit,
            'window' => $window,
            'remaining' => max(0, (int) ($limit - $totalCount)),
            'reset_time' => $currentWindow + $window,
        ];
    }
}
