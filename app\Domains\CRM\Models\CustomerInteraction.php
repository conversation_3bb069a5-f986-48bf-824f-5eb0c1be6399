<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج تفاعل العميل - Customer Interaction
 * تسجيل جميع التفاعلات مع العميل (مكالمات، رسائل، اجتماعات، إلخ)
 */
class CustomerInteraction extends Model
{
    use HasFactory, HasUuid, HasFiles;

    protected $fillable = [
        'customer_id',
        'opportunity_id',
        'contact_id',
        'user_id',
        'type',
        'direction',
        'channel',
        'subject',
        'description',
        'notes',
        'duration_minutes',
        'outcome',
        'sentiment',
        'priority',
        'status',
        'scheduled_at',
        'occurred_at',
        'completed_at',
        'follow_up_required',
        'follow_up_date',
        'follow_up_notes',
        'tags',
        'custom_fields',
        'ai_insights',
        'call_recording_url',
        'email_message_id',
        'whatsapp_message_id',
        'location',
        'participants',
        'related_id',
        'related_type',
        'metadata',
    ];

    protected $casts = [
        'duration_minutes' => 'integer',
        'scheduled_at' => 'datetime',
        'occurred_at' => 'datetime',
        'completed_at' => 'datetime',
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'datetime',
        'tags' => 'array',
        'custom_fields' => 'array',
        'ai_insights' => 'array',
        'participants' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع التفاعل
     */
    const TYPES = [
        'call' => 'مكالمة',
        'email' => 'بريد إلكتروني',
        'meeting' => 'اجتماع',
        'whatsapp' => 'واتساب',
        'sms' => 'رسالة نصية',
        'visit' => 'زيارة',
        'demo' => 'عرض توضيحي',
        'presentation' => 'عرض تقديمي',
        'proposal' => 'عرض سعر',
        'contract' => 'عقد',
        'support' => 'دعم فني',
        'social_media' => 'وسائل التواصل',
        'webinar' => 'ندوة إلكترونية',
        'chat' => 'دردشة',
        'other' => 'أخرى',
    ];

    /**
     * اتجاه التفاعل
     */
    const DIRECTIONS = [
        'inbound' => 'وارد',
        'outbound' => 'صادر',
    ];

    /**
     * قنوات التفاعل
     */
    const CHANNELS = [
        'phone' => 'هاتف',
        'email' => 'بريد إلكتروني',
        'whatsapp' => 'واتساب',
        'telegram' => 'تيليجرام',
        'facebook' => 'فيسبوك',
        'linkedin' => 'لينكد إن',
        'twitter' => 'تويتر',
        'instagram' => 'إنستجرام',
        'website' => 'موقع إلكتروني',
        'in_person' => 'شخصياً',
        'video_call' => 'مكالمة فيديو',
        'sms' => 'رسالة نصية',
        'other' => 'أخرى',
    ];

    /**
     * نتائج التفاعل
     */
    const OUTCOMES = [
        'successful' => 'ناجح',
        'follow_up_needed' => 'يحتاج متابعة',
        'not_interested' => 'غير مهتم',
        'callback_requested' => 'طلب إعادة اتصال',
        'meeting_scheduled' => 'تم جدولة اجتماع',
        'proposal_requested' => 'طلب عرض سعر',
        'deal_closed' => 'تم إغلاق الصفقة',
        'objection_raised' => 'تم إثارة اعتراض',
        'information_provided' => 'تم تقديم معلومات',
        'complaint_resolved' => 'تم حل الشكوى',
        'no_answer' => 'لا يوجد رد',
        'wrong_number' => 'رقم خاطئ',
        'busy' => 'مشغول',
        'other' => 'أخرى',
    ];

    /**
     * مستويات المشاعر
     */
    const SENTIMENTS = [
        'very_positive' => 'إيجابي جداً',
        'positive' => 'إيجابي',
        'neutral' => 'محايد',
        'negative' => 'سلبي',
        'very_negative' => 'سلبي جداً',
    ];

    /**
     * حالات التفاعل
     */
    const STATUSES = [
        'scheduled' => 'مجدول',
        'in_progress' => 'قيد التنفيذ',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي',
        'no_show' => 'لم يحضر',
        'rescheduled' => 'تم إعادة الجدولة',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع الفرصة التجارية
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * العلاقة مع جهة الاتصال
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * العلاقة مع المستخدم/الموظف
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    /**
     * العلاقة المتعددة الأشكال مع الكيان المرتبط
     */
    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الاتجاه
     */
    public function getDirectionLabelAttribute(): string
    {
        return self::DIRECTIONS[$this->direction] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية القناة
     */
    public function getChannelLabelAttribute(): string
    {
        return self::CHANNELS[$this->channel] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية النتيجة
     */
    public function getOutcomeLabelAttribute(): string
    {
        return self::OUTCOMES[$this->outcome] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية المشاعر
     */
    public function getSentimentLabelAttribute(): string
    {
        return self::SENTIMENTS[$this->sentiment] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على لون النوع
     */
    public function getTypeColorAttribute(): string
    {
        return match ($this->type) {
            'call' => '#28a745',
            'email' => '#007bff',
            'meeting' => '#6f42c1',
            'whatsapp' => '#25d366',
            'sms' => '#ffc107',
            'visit' => '#fd7e14',
            'demo' => '#20c997',
            'proposal' => '#e83e8c',
            'contract' => '#6c757d',
            'support' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون المشاعر
     */
    public function getSentimentColorAttribute(): string
    {
        return match ($this->sentiment) {
            'very_positive' => '#28a745',
            'positive' => '#20c997',
            'neutral' => '#6c757d',
            'negative' => '#fd7e14',
            'very_negative' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على أيقونة النوع
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'call' => 'fas fa-phone',
            'email' => 'fas fa-envelope',
            'meeting' => 'fas fa-users',
            'whatsapp' => 'fab fa-whatsapp',
            'sms' => 'fas fa-sms',
            'visit' => 'fas fa-map-marker-alt',
            'demo' => 'fas fa-desktop',
            'proposal' => 'fas fa-file-contract',
            'contract' => 'fas fa-handshake',
            'support' => 'fas fa-life-ring',
            'social_media' => 'fas fa-share-alt',
            'webinar' => 'fas fa-video',
            'chat' => 'fas fa-comments',
            default => 'fas fa-circle',
        };
    }

    /**
     * التحقق من كون التفاعل مكتمل
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من كون التفاعل مجدول
     */
    public function getIsScheduledAttribute(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * التحقق من الحاجة لمتابعة
     */
    public function getNeedsFollowUpAttribute(): bool
    {
        return $this->follow_up_required && 
               $this->follow_up_date && 
               $this->follow_up_date->isPast();
    }

    /**
     * الحصول على المدة المنسقة
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'غير محدد';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return "{$hours} ساعة و {$minutes} دقيقة";
        }

        return "{$minutes} دقيقة";
    }

    /**
     * الحصول على الوقت المتبقي للمتابعة
     */
    public function getFollowUpInAttribute(): ?string
    {
        if (!$this->follow_up_date) {
            return null;
        }

        $diff = now()->diffInDays($this->follow_up_date, false);

        if ($diff < 0) {
            return 'متأخر ' . abs($diff) . ' يوم';
        } elseif ($diff === 0) {
            return 'اليوم';
        } elseif ($diff === 1) {
            return 'غداً';
        } else {
            return "خلال {$diff} أيام";
        }
    }

    /**
     * تحديد ما إذا كان التفاعل إيجابي
     */
    public function getIsPositiveAttribute(): bool
    {
        return in_array($this->sentiment, ['positive', 'very_positive']);
    }

    /**
     * تحديد ما إذا كان التفاعل سلبي
     */
    public function getIsNegativeAttribute(): bool
    {
        return in_array($this->sentiment, ['negative', 'very_negative']);
    }

    /**
     * تحديث حالة التفاعل
     */
    public function markAsCompleted(array $data = []): bool
    {
        return $this->update(array_merge([
            'status' => 'completed',
            'completed_at' => now(),
        ], $data));
    }

    /**
     * تحديد موعد متابعة
     */
    public function scheduleFollowUp(\DateTime $date, string $notes = null): bool
    {
        return $this->update([
            'follow_up_required' => true,
            'follow_up_date' => $date,
            'follow_up_notes' => $notes,
        ]);
    }

    /**
     * إلغاء التفاعل
     */
    public function cancel(string $reason = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . "\n\nسبب الإلغاء: " . $reason,
        ]);
    }

    /**
     * إعادة جدولة التفاعل
     */
    public function reschedule(\DateTime $newDate, string $reason = null): bool
    {
        return $this->update([
            'status' => 'rescheduled',
            'scheduled_at' => $newDate,
            'notes' => $this->notes . "\n\nتم إعادة الجدولة: " . $reason,
        ]);
    }

    /**
     * فلترة التفاعلات حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة التفاعلات حسب الاتجاه
     */
    public function scopeDirection($query, string $direction)
    {
        return $query->where('direction', $direction);
    }

    /**
     * فلترة التفاعلات حسب القناة
     */
    public function scopeChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * فلترة التفاعلات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * فلترة التفاعلات المجدولة
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * فلترة التفاعلات التي تحتاج متابعة
     */
    public function scopeNeedsFollowUp($query)
    {
        return $query->where('follow_up_required', true)
                    ->where('follow_up_date', '<=', now());
    }

    /**
     * فلترة التفاعلات الإيجابية
     */
    public function scopePositive($query)
    {
        return $query->whereIn('sentiment', ['positive', 'very_positive']);
    }

    /**
     * فلترة التفاعلات السلبية
     */
    public function scopeNegative($query)
    {
        return $query->whereIn('sentiment', ['negative', 'very_negative']);
    }

    /**
     * فلترة حسب المستخدم
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * فلترة حسب فترة زمنية
     */
    public function scopeBetweenDates($query, \DateTime $from, \DateTime $to)
    {
        return $query->whereBetween('occurred_at', [$from, $to]);
    }

    /**
     * فلترة التفاعلات الأخيرة
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('occurred_at', '>=', now()->subDays($days));
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('occurred_at', 'desc');
    }

    /**
     * البحث في التفاعلات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('notes', 'LIKE', "%{$search}%");
        });
    }
}
