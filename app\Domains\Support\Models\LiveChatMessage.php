<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج رسالة الدردشة الحية - Live Chat Message
 */
class LiveChatMessage extends Model
{
    use HasFactory, SoftDeletes, HasUuid, HasFiles;

    protected $fillable = [
        'chat_id',
        'sender_type',
        'sender_id',
        'content',
        'message_type',
        'ai_generated',
        'ai_confidence',
        'response_time_seconds',
        'is_read',
        'read_at',
        'metadata',
    ];

    protected $casts = [
        'ai_generated' => 'boolean',
        'ai_confidence' => 'decimal:2',
        'response_time_seconds' => 'integer',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function chat(): BelongsTo
    {
        return $this->belongsTo(LiveChat::class, 'chat_id');
    }

    public function sender(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'sender_id');
    }
}
