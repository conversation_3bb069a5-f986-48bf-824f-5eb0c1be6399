<?php

namespace App\Domains\Integration\Services\Transformation\Enrichers;

use App\Domains\Integration\Exceptions\EnrichmentException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * Data Enricher
 * Enriches data with additional information from various sources
 */
class DataEnricher
{
    protected array $config;
    protected array $enrichers;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'cache_ttl' => 3600, // 1 hour
            'timeout' => 30,
            'max_retries' => 3,
            'retry_delay' => 1000, // milliseconds
        ], $config);
        
        $this->enrichers = [];
        $this->registerDefaultEnrichers();
    }

    /**
     * Enrich data using specified enrichers
     */
    public function enrich(array $data, array $enrichmentRules): array
    {
        try {
            $enrichedData = $data;
            
            foreach ($enrichmentRules as $rule) {
                $enricherName = $rule['enricher'] ?? null;
                $sourceField = $rule['source_field'] ?? null;
                $targetField = $rule['target_field'] ?? null;
                $options = $rule['options'] ?? [];
                
                if (!$enricherName || !$sourceField) {
                    continue;
                }
                
                $enricher = $this->getEnricher($enricherName);
                if (!$enricher) {
                    Log::warning("Enricher not found: {$enricherName}");
                    continue;
                }
                
                $sourceValue = $this->getNestedValue($enrichedData, $sourceField);
                if ($sourceValue === null) {
                    continue;
                }
                
                $enrichedValue = $enricher($sourceValue, $options);
                
                if ($enrichedValue !== null) {
                    if ($targetField) {
                        $this->setNestedValue($enrichedData, $targetField, $enrichedValue);
                    } else {
                        // Merge enriched data if no specific target field
                        if (is_array($enrichedValue)) {
                            $enrichedData = array_merge($enrichedData, $enrichedValue);
                        }
                    }
                }
            }
            
            return $enrichedData;
            
        } catch (\Exception $e) {
            Log::error('Data enrichment failed', [
                'error' => $e->getMessage(),
                'rules' => $enrichmentRules,
            ]);
            
            throw new EnrichmentException('Data enrichment failed: ' . $e->getMessage());
        }
    }

    /**
     * Register default enrichers
     */
    protected function registerDefaultEnrichers(): void
    {
        // Timestamp enricher
        $this->registerEnricher('timestamp', function ($value, $options) {
            $format = $options['format'] ?? 'Y-m-d H:i:s';
            $timezone = $options['timezone'] ?? 'UTC';
            
            try {
                $carbon = Carbon::parse($value, $timezone);
                return $carbon->format($format);
            } catch (\Exception $e) {
                return null;
            }
        });

        // Geolocation enricher
        $this->registerEnricher('geolocation', function ($value, $options) {
            return $this->enrichWithGeolocation($value, $options);
        });

        // Currency conversion enricher
        $this->registerEnricher('currency', function ($value, $options) {
            return $this->enrichWithCurrency($value, $options);
        });

        // Text analysis enricher
        $this->registerEnricher('text_analysis', function ($value, $options) {
            return $this->enrichWithTextAnalysis($value, $options);
        });

        // URL metadata enricher
        $this->registerEnricher('url_metadata', function ($value, $options) {
            return $this->enrichWithUrlMetadata($value, $options);
        });

        // Phone number enricher
        $this->registerEnricher('phone', function ($value, $options) {
            return $this->enrichWithPhoneData($value, $options);
        });

        // Email enricher
        $this->registerEnricher('email', function ($value, $options) {
            return $this->enrichWithEmailData($value, $options);
        });

        // Hash enricher
        $this->registerEnricher('hash', function ($value, $options) {
            $algorithm = $options['algorithm'] ?? 'sha256';
            return hash($algorithm, $value);
        });

        // UUID enricher
        $this->registerEnricher('uuid', function ($value, $options) {
            return \Illuminate\Support\Str::uuid()->toString();
        });

        // Slug enricher
        $this->registerEnricher('slug', function ($value, $options) {
            return \Illuminate\Support\Str::slug($value);
        });
    }

    /**
     * Register a custom enricher
     */
    public function registerEnricher(string $name, callable $enricher): void
    {
        $this->enrichers[$name] = $enricher;
    }

    /**
     * Get enricher by name
     */
    protected function getEnricher(string $name): ?callable
    {
        return $this->enrichers[$name] ?? null;
    }

    /**
     * Enrich with geolocation data
     */
    protected function enrichWithGeolocation(string $ipAddress, array $options): ?array
    {
        $cacheKey = "geolocation:{$ipAddress}";
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($ipAddress, $options) {
            try {
                // Using a free IP geolocation service (replace with your preferred service)
                $response = Http::timeout($this->config['timeout'])
                    ->get("http://ip-api.com/json/{$ipAddress}");
                
                if ($response->successful()) {
                    $data = $response->json();
                    
                    return [
                        'country' => $data['country'] ?? null,
                        'country_code' => $data['countryCode'] ?? null,
                        'region' => $data['regionName'] ?? null,
                        'city' => $data['city'] ?? null,
                        'latitude' => $data['lat'] ?? null,
                        'longitude' => $data['lon'] ?? null,
                        'timezone' => $data['timezone'] ?? null,
                        'isp' => $data['isp'] ?? null,
                    ];
                }
                
                return null;
            } catch (\Exception $e) {
                Log::error('Geolocation enrichment failed', [
                    'ip' => $ipAddress,
                    'error' => $e->getMessage(),
                ]);
                return null;
            }
        });
    }

    /**
     * Enrich with currency conversion
     */
    protected function enrichWithCurrency(float $amount, array $options): ?array
    {
        $fromCurrency = $options['from'] ?? 'USD';
        $toCurrency = $options['to'] ?? 'EUR';
        $cacheKey = "currency:{$fromCurrency}:{$toCurrency}";
        
        $rate = Cache::remember($cacheKey, 3600, function () use ($fromCurrency, $toCurrency) {
            try {
                // Using a free currency API (replace with your preferred service)
                $response = Http::timeout($this->config['timeout'])
                    ->get("https://api.exchangerate-api.com/v4/latest/{$fromCurrency}");
                
                if ($response->successful()) {
                    $data = $response->json();
                    return $data['rates'][$toCurrency] ?? null;
                }
                
                return null;
            } catch (\Exception $e) {
                Log::error('Currency enrichment failed', [
                    'from' => $fromCurrency,
                    'to' => $toCurrency,
                    'error' => $e->getMessage(),
                ]);
                return null;
            }
        });
        
        if ($rate) {
            return [
                'original_amount' => $amount,
                'original_currency' => $fromCurrency,
                'converted_amount' => round($amount * $rate, 2),
                'converted_currency' => $toCurrency,
                'exchange_rate' => $rate,
                'conversion_date' => now()->toISOString(),
            ];
        }
        
        return null;
    }

    /**
     * Enrich with text analysis
     */
    protected function enrichWithTextAnalysis(string $text, array $options): ?array
    {
        return [
            'length' => strlen($text),
            'word_count' => str_word_count($text),
            'character_count' => mb_strlen($text),
            'sentence_count' => preg_match_all('/[.!?]+/', $text),
            'language' => $this->detectLanguage($text),
            'sentiment' => $this->analyzeSentiment($text),
            'keywords' => $this->extractKeywords($text, $options['max_keywords'] ?? 10),
        ];
    }

    /**
     * Enrich with URL metadata
     */
    protected function enrichWithUrlMetadata(string $url, array $options): ?array
    {
        $cacheKey = "url_metadata:" . md5($url);
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($url) {
            try {
                $response = Http::timeout($this->config['timeout'])->get($url);
                
                if ($response->successful()) {
                    $html = $response->body();
                    
                    return [
                        'title' => $this->extractTitle($html),
                        'description' => $this->extractDescription($html),
                        'keywords' => $this->extractMetaKeywords($html),
                        'status_code' => $response->status(),
                        'content_type' => $response->header('Content-Type'),
                        'content_length' => strlen($html),
                    ];
                }
                
                return null;
            } catch (\Exception $e) {
                Log::error('URL metadata enrichment failed', [
                    'url' => $url,
                    'error' => $e->getMessage(),
                ]);
                return null;
            }
        });
    }

    /**
     * Enrich with phone number data
     */
    protected function enrichWithPhoneData(string $phone, array $options): ?array
    {
        // Basic phone number analysis (you might want to use a library like libphonenumber)
        $cleaned = preg_replace('/[^\d+]/', '', $phone);
        
        return [
            'original' => $phone,
            'cleaned' => $cleaned,
            'is_mobile' => $this->isMobileNumber($cleaned),
            'country_code' => $this->extractCountryCode($cleaned),
            'formatted' => $this->formatPhoneNumber($cleaned),
        ];
    }

    /**
     * Enrich with email data
     */
    protected function enrichWithEmailData(string $email, array $options): ?array
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return null;
        }
        
        [$localPart, $domain] = $parts;
        
        return [
            'original' => $email,
            'local_part' => $localPart,
            'domain' => $domain,
            'is_valid' => filter_var($email, FILTER_VALIDATE_EMAIL) !== false,
            'is_disposable' => $this->isDisposableEmail($domain),
            'domain_info' => $this->getDomainInfo($domain),
        ];
    }

    // Helper methods (simplified implementations)
    protected function getNestedValue(array $data, string $key): mixed
    {
        $keys = explode('.', $key);
        $value = $data;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    protected function setNestedValue(array &$data, string $key, mixed $value): void
    {
        $keys = explode('.', $key);
        $current = &$data;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    protected function detectLanguage(string $text): ?string
    {
        // Simplified language detection
        return 'en'; // Default to English
    }

    protected function analyzeSentiment(string $text): string
    {
        // Simplified sentiment analysis
        return 'neutral';
    }

    protected function extractKeywords(string $text, int $maxKeywords): array
    {
        // Simplified keyword extraction
        $words = str_word_count(strtolower($text), 1);
        $wordCounts = array_count_values($words);
        arsort($wordCounts);
        
        return array_slice(array_keys($wordCounts), 0, $maxKeywords);
    }

    protected function extractTitle(string $html): ?string
    {
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return null;
    }

    protected function extractDescription(string $html): ?string
    {
        if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    protected function extractMetaKeywords(string $html): ?string
    {
        if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    protected function isMobileNumber(string $phone): bool
    {
        // Simplified mobile detection
        return strlen($phone) >= 10;
    }

    protected function extractCountryCode(string $phone): ?string
    {
        // Simplified country code extraction
        if (str_starts_with($phone, '+')) {
            return substr($phone, 1, 2);
        }
        return null;
    }

    protected function formatPhoneNumber(string $phone): string
    {
        // Simplified phone formatting
        return $phone;
    }

    protected function isDisposableEmail(string $domain): bool
    {
        // Simplified disposable email detection
        $disposableDomains = ['10minutemail.com', 'tempmail.org', 'guerrillamail.com'];
        return in_array(strtolower($domain), $disposableDomains);
    }

    protected function getDomainInfo(string $domain): ?array
    {
        // Simplified domain info
        return [
            'domain' => $domain,
            'tld' => pathinfo($domain, PATHINFO_EXTENSION),
        ];
    }
}
