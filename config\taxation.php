<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Taxation System Configuration
    |--------------------------------------------------------------------------
    |
    | تكوين نظام الضرائب والامتثال الضريبي
    |
    */

    'enabled' => true,
    'default_country' => env('TAXATION_DEFAULT_COUNTRY', 'SA'),
    'multi_country_support' => env('TAXATION_MULTI_COUNTRY', true),

    /*
    |--------------------------------------------------------------------------
    | VAT Configuration
    |--------------------------------------------------------------------------
    */
    'vat' => [
        'enabled' => true,
        'default_rate' => 15, // Saudi Arabia VAT rate
        'registration_required' => true,
        'threshold_amount' => 375000, // SAR
        'return_frequency' => 'monthly', // monthly, quarterly
        'auto_calculation' => true,
        'reverse_charge_mechanism' => true,
        'zero_rated_supplies' => true,
        'exempt_supplies' => true,
        'input_tax_recovery' => true,
        'partial_exemption' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | E-Invoicing Configuration
    |--------------------------------------------------------------------------
    */
    'e_invoicing' => [
        'enabled' => true,
        'mandatory' => true,
        'phase_1_enabled' => true, // Generation and storage
        'phase_2_enabled' => true, // Integration with ZATCA
        'qr_code_required' => true,
        'digital_signature' => true,
        'xml_format' => 'ubl_2_1',
        'api_integration' => true,
        'real_time_submission' => true,
        'batch_submission' => false,
        'validation_rules' => true,
        'compliance_check' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Rates by Country
    |--------------------------------------------------------------------------
    */
    'tax_rates' => [
        'SA' => [
            'vat' => 15,
            'withholding_tax' => 5,
            'excise_tax' => [
                'tobacco' => 100,
                'energy_drinks' => 100,
                'soft_drinks' => 50,
            ],
            'customs_duty' => 5,
        ],
        'AE' => [
            'vat' => 5,
            'withholding_tax' => 0,
            'excise_tax' => [
                'tobacco' => 100,
                'energy_drinks' => 100,
                'soft_drinks' => 50,
            ],
        ],
        'EG' => [
            'vat' => 14,
            'withholding_tax' => 10,
            'income_tax' => 25,
        ],
        'MA' => [
            'vat' => 20,
            'withholding_tax' => 10,
            'corporate_tax' => 31,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Categories
    |--------------------------------------------------------------------------
    */
    'tax_categories' => [
        'standard' => [
            'name' => 'معدل قياسي',
            'rate' => 15,
            'code' => 'S',
        ],
        'zero_rated' => [
            'name' => 'معدل صفر',
            'rate' => 0,
            'code' => 'Z',
        ],
        'exempt' => [
            'name' => 'معفى',
            'rate' => 0,
            'code' => 'E',
        ],
        'out_of_scope' => [
            'name' => 'خارج النطاق',
            'rate' => 0,
            'code' => 'O',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Return Configuration
    |--------------------------------------------------------------------------
    */
    'tax_returns' => [
        'auto_generation' => true,
        'submission_deadline_days' => 30,
        'late_submission_penalty' => 5, // percentage
        'amendment_allowed' => true,
        'amendment_deadline_days' => 90,
        'electronic_submission' => true,
        'digital_signature_required' => true,
        'supporting_documents' => true,
        'audit_trail' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Withholding Tax
    |--------------------------------------------------------------------------
    */
    'withholding_tax' => [
        'enabled' => true,
        'default_rate' => 5,
        'applicable_transactions' => [
            'services' => 5,
            'rent' => 5,
            'royalties' => 5,
            'dividends' => 5,
            'interest' => 5,
        ],
        'threshold_amount' => 30000, // SAR
        'certificate_generation' => true,
        'monthly_return' => true,
        'annual_return' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Excise Tax
    |--------------------------------------------------------------------------
    */
    'excise_tax' => [
        'enabled' => true,
        'products' => [
            'tobacco' => [
                'rate' => 100,
                'calculation_method' => 'ad_valorem',
            ],
            'energy_drinks' => [
                'rate' => 100,
                'calculation_method' => 'ad_valorem',
            ],
            'soft_drinks' => [
                'rate' => 50,
                'calculation_method' => 'ad_valorem',
            ],
        ],
        'registration_required' => true,
        'monthly_return' => true,
        'warehouse_tracking' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance Monitoring
    |--------------------------------------------------------------------------
    */
    'compliance' => [
        'enabled' => true,
        'real_time_monitoring' => true,
        'automated_checks' => true,
        'risk_assessment' => true,
        'penalty_calculation' => true,
        'deadline_tracking' => true,
        'notification_system' => true,
        'audit_preparation' => true,
        'document_management' => true,
        'compliance_score' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Authorities Integration
    |--------------------------------------------------------------------------
    */
    'authorities' => [
        'SA' => [
            'zatca' => [
                'enabled' => true,
                'api_url' => env('ZATCA_API_URL'),
                'api_key' => env('ZATCA_API_KEY'),
                'certificate_path' => env('ZATCA_CERTIFICATE_PATH'),
                'sandbox_mode' => env('ZATCA_SANDBOX', true),
                'timeout' => 30,
                'retry_attempts' => 3,
            ],
            'gazt' => [
                'enabled' => true,
                'api_url' => env('GAZT_API_URL'),
                'username' => env('GAZT_USERNAME'),
                'password' => env('GAZT_PASSWORD'),
            ],
        ],
        'AE' => [
            'fta' => [
                'enabled' => true,
                'api_url' => env('FTA_API_URL'),
                'api_key' => env('FTA_API_KEY'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting Configuration
    |--------------------------------------------------------------------------
    */
    'reporting' => [
        'enabled' => true,
        'real_time_reports' => true,
        'scheduled_reports' => true,
        'custom_reports' => true,
        'export_formats' => ['pdf', 'excel', 'csv', 'xml'],
        'email_reports' => true,
        'dashboard_widgets' => true,
        'comparative_analysis' => true,
        'trend_analysis' => true,
        'variance_analysis' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Trail
    |--------------------------------------------------------------------------
    */
    'audit_trail' => [
        'enabled' => true,
        'track_all_changes' => true,
        'user_tracking' => true,
        'ip_tracking' => true,
        'timestamp_tracking' => true,
        'retention_period_years' => 7,
        'immutable_records' => true,
        'digital_signatures' => true,
        'hash_verification' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Validation
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'tax_number_validation' => true,
        'amount_validation' => true,
        'date_validation' => true,
        'rate_validation' => true,
        'calculation_validation' => true,
        'cross_reference_validation' => true,
        'business_rules_validation' => true,
        'real_time_validation' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encryption' => [
            'tax_data' => true,
            'certificates' => true,
            'api_communications' => true,
        ],
        'access_control' => [
            'role_based' => true,
            'field_level_security' => true,
            'ip_restrictions' => true,
        ],
        'backup' => [
            'auto_backup' => true,
            'backup_frequency' => 'daily',
            'retention_days' => 2555, // 7 years
            'encrypted_backups' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'accounting' => [
            'enabled' => true,
            'auto_sync' => true,
            'real_time_updates' => true,
            'journal_entry_creation' => true,
        ],
        'crm' => [
            'enabled' => true,
            'customer_tax_info' => true,
            'invoice_tax_calculation' => true,
        ],
        'ecommerce' => [
            'enabled' => true,
            'auto_tax_calculation' => true,
            'tax_inclusive_pricing' => true,
        ],
        'banking' => [
            'enabled' => true,
            'payment_reconciliation' => true,
            'tax_payment_tracking' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notifications
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => true,
        'channels' => ['email', 'sms', 'push', 'in_app'],
        'deadline_reminders' => true,
        'compliance_alerts' => true,
        'error_notifications' => true,
        'submission_confirmations' => true,
        'penalty_warnings' => true,
        'rate_change_alerts' => true,
        'audit_notifications' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'caching' => [
            'enabled' => true,
            'cache_duration' => 3600, // seconds
            'cache_tax_rates' => true,
            'cache_calculations' => true,
        ],
        'batch_processing' => [
            'enabled' => true,
            'batch_size' => 1000,
            'parallel_processing' => true,
        ],
        'archiving' => [
            'enabled' => true,
            'archive_after_years' => 3,
            'compress_archived_data' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization
    |--------------------------------------------------------------------------
    */
    'localization' => [
        'default_language' => 'ar',
        'supported_languages' => ['ar', 'en'],
        'rtl_support' => true,
        'date_format' => 'd/m/Y',
        'number_format' => [
            'decimal_separator' => '.',
            'thousands_separator' => ',',
            'decimal_places' => 2,
        ],
        'currency_display' => [
            'symbol_position' => 'after',
            'space_between' => true,
        ],
        'tax_period_format' => 'Y-m',
    ],
];
