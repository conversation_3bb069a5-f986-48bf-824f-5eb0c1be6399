<?php

namespace App\Domains\Payments\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * نموذج بوابة الدفع
 * يمثل بوابات الدفع المختلفة المدعومة في النظام
 */
class PaymentGateway extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'provider',
        'type',
        'status',
        'supported_countries',
        'supported_currencies',
        'supported_payment_methods',
        'configuration',
        'credentials',
        'fees_config',
        'limits_config',
        'features',
        'api_version',
        'webhook_url',
        'is_sandbox',
        'priority',
        'description',
        'logo_url',
        'documentation_url',
    ];

    protected $casts = [
        'supported_countries' => 'array',
        'supported_currencies' => 'array',
        'supported_payment_methods' => 'array',
        'configuration' => 'array',
        'credentials' => 'encrypted:array',
        'fees_config' => 'array',
        'limits_config' => 'array',
        'features' => 'array',
        'is_sandbox' => 'boolean',
        'priority' => 'integer',
    ];

    protected $hidden = [
        'credentials',
    ];

    /**
     * أنواع بوابات الدفع
     */
    const TYPES = [
        'card' => 'بطاقات ائتمانية',
        'bank_transfer' => 'تحويل بنكي',
        'digital_wallet' => 'محفظة رقمية',
        'mobile_payment' => 'دفع عبر الهاتف',
        'cash_on_delivery' => 'دفع عند الاستلام',
        'cryptocurrency' => 'عملة رقمية',
        'buy_now_pay_later' => 'اشتري الآن وادفع لاحقاً',
    ];

    /**
     * حالات بوابة الدفع
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'maintenance' => 'تحت الصيانة',
        'deprecated' => 'مهجور',
    ];

    /**
     * المزودين المدعومين
     */
    const PROVIDERS = [
        // المغرب
        'cmi_maroc' => 'CMI Maroc',
        'hps_morocco' => 'HPS Morocco',
        'attijariwafa' => 'Attijariwafa Bank',
        'banque_populaire' => 'Banque Populaire',
        'cih_bank' => 'CIH Bank',
        'orange_money_ma' => 'Orange Money Morocco',
        'wafacash' => 'Wafacash',
        'barid_cash' => 'Barid Cash',
        
        // السعودية
        'mada' => 'Mada',
        'hyperpay' => 'HyperPay',
        'tap_payments' => 'Tap Payments',
        'paytabs' => 'PayTabs',
        'stc_pay' => 'STC Pay',
        'al_rajhi_bank' => 'Al Rajhi Bank',
        'sabb' => 'SABB',
        'sadad' => 'SADAD',
        'sarie' => 'SARIE',
        
        // الإمارات
        'telr' => 'Telr',
        'payby' => 'PayBy',
        'checkout_com' => 'Checkout.com',
        'network_international' => 'Network International',
        'emirates_nbd' => 'Emirates NBD',
        'emirates_wallet' => 'Emirates Wallet',
        'careem_pay' => 'Careem Pay',
        
        // مصر
        'fawry' => 'Fawry',
        'paymob' => 'Paymob',
        'instapay' => 'InstaPay',
        'vodafone_cash' => 'Vodafone Cash',
        'orange_money_eg' => 'Orange Money Egypt',
        'etisalat_cash' => 'Etisalat Cash',
        
        // عالمية
        'stripe' => 'Stripe',
        'paypal' => 'PayPal',
        'visa' => 'Visa',
        'mastercard' => 'Mastercard',
        'apple_pay' => 'Apple Pay',
        'google_pay' => 'Google Pay',
        'swift' => 'SWIFT',
    ];

    /**
     * العلاقة مع المعاملات
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'gateway_id');
    }

    /**
     * العلاقة مع طرق الدفع
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class, 'gateway_id');
    }

    /**
     * التحقق من دعم الدولة
     */
    public function supportsCountry(string $countryCode): bool
    {
        return in_array($countryCode, $this->supported_countries ?? []);
    }

    /**
     * التحقق من دعم العملة
     */
    public function supportsCurrency(string $currencyCode): bool
    {
        return in_array($currencyCode, $this->supported_currencies ?? []);
    }

    /**
     * التحقق من دعم طريقة الدفع
     */
    public function supportsPaymentMethod(string $method): bool
    {
        return in_array($method, $this->supported_payment_methods ?? []);
    }

    /**
     * الحصول على رسوم المعاملة
     */
    public function calculateFees(float $amount, string $currency = 'USD'): array
    {
        $feesConfig = $this->fees_config ?? [];
        
        $fixedFee = $feesConfig['fixed'][$currency] ?? 0;
        $percentageFee = $feesConfig['percentage'] ?? 0;
        $minFee = $feesConfig['minimum'][$currency] ?? 0;
        $maxFee = $feesConfig['maximum'][$currency] ?? null;
        
        $calculatedFee = $fixedFee + ($amount * $percentageFee / 100);
        $calculatedFee = max($calculatedFee, $minFee);
        
        if ($maxFee !== null) {
            $calculatedFee = min($calculatedFee, $maxFee);
        }
        
        return [
            'amount' => round($calculatedFee, 2),
            'currency' => $currency,
            'breakdown' => [
                'fixed' => $fixedFee,
                'percentage' => $amount * $percentageFee / 100,
                'total' => $calculatedFee,
            ],
        ];
    }

    /**
     * التحقق من حدود المعاملة
     */
    public function checkLimits(float $amount, string $currency = 'USD'): array
    {
        $limits = $this->limits_config ?? [];
        
        $minAmount = $limits['min_amount'][$currency] ?? 0;
        $maxAmount = $limits['max_amount'][$currency] ?? null;
        $dailyLimit = $limits['daily_limit'][$currency] ?? null;
        $monthlyLimit = $limits['monthly_limit'][$currency] ?? null;
        
        $errors = [];
        
        if ($amount < $minAmount) {
            $errors[] = "المبلغ أقل من الحد الأدنى المسموح ({$minAmount} {$currency})";
        }
        
        if ($maxAmount !== null && $amount > $maxAmount) {
            $errors[] = "المبلغ أكبر من الحد الأقصى المسموح ({$maxAmount} {$currency})";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'limits' => [
                'min_amount' => $minAmount,
                'max_amount' => $maxAmount,
                'daily_limit' => $dailyLimit,
                'monthly_limit' => $monthlyLimit,
            ],
        ];
    }

    /**
     * التحقق من توفر الميزة
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * الحصول على إعدادات API
     */
    public function getApiConfig(): array
    {
        $config = $this->configuration ?? [];
        
        return [
            'base_url' => $config['base_url'] ?? '',
            'api_version' => $this->api_version,
            'timeout' => $config['timeout'] ?? 30,
            'retry_attempts' => $config['retry_attempts'] ?? 3,
            'webhook_url' => $this->webhook_url,
            'is_sandbox' => $this->is_sandbox,
        ];
    }

    /**
     * الحصول على بيانات الاعتماد المفكوكة
     */
    public function getCredentials(): array
    {
        return $this->credentials ?? [];
    }

    /**
     * تحديث بيانات الاعتماد
     */
    public function updateCredentials(array $credentials): void
    {
        $this->update(['credentials' => $credentials]);
    }

    /**
     * التحقق من صحة الإعدادات
     */
    public function validateConfiguration(): array
    {
        $errors = [];
        $credentials = $this->getCredentials();
        
        // التحقق من الحقول المطلوبة حسب المزود
        $requiredFields = $this->getRequiredCredentialFields();
        
        foreach ($requiredFields as $field) {
            if (empty($credentials[$field])) {
                $errors[] = "الحقل {$field} مطلوب";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * الحصول على الحقول المطلوبة حسب المزود
     */
    protected function getRequiredCredentialFields(): array
    {
        return match ($this->provider) {
            'stripe' => ['secret_key', 'publishable_key'],
            'paypal' => ['client_id', 'client_secret'],
            'mada' => ['merchant_id', 'terminal_id', 'secret_key'],
            'fawry' => ['merchant_code', 'security_key'],
            'cmi_maroc' => ['merchant_id', 'access_key', 'secret_key'],
            'hyperpay' => ['entity_id', 'access_token'],
            'tap_payments' => ['secret_key', 'public_key'],
            'paytabs' => ['profile_id', 'server_key'],
            'telr' => ['merchant_id', 'authentication_key'],
            'payby' => ['app_id', 'private_key'],
            'paymob' => ['api_key', 'integration_id', 'hmac_secret'],
            'instapay' => ['merchant_id', 'terminal_id', 'secret_key'],
            default => ['api_key'],
        };
    }

    /**
     * Scope للبوابات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope للبوابات حسب الدولة
     */
    public function scopeForCountry($query, string $countryCode)
    {
        return $query->whereJsonContains('supported_countries', $countryCode);
    }

    /**
     * Scope للبوابات حسب العملة
     */
    public function scopeForCurrency($query, string $currencyCode)
    {
        return $query->whereJsonContains('supported_currencies', $currencyCode);
    }

    /**
     * Scope للبوابات حسب نوع الدفع
     */
    public function scopeForPaymentMethod($query, string $method)
    {
        return $query->whereJsonContains('supported_payment_methods', $method);
    }

    /**
     * Scope للترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }
}
