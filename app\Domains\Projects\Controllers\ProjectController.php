<?php

namespace App\Domains\Projects\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Services\AdvancedProjectManagementService;
use App\Domains\Projects\Requests\StoreProjectRequest;
use App\Domains\Projects\Requests\UpdateProjectRequest;
use App\Domains\Projects\Resources\ProjectResource;
use App\Domains\Projects\Resources\ProjectCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

/**
 * متحكم المشاريع الاحترافي
 * إدارة شاملة للمشاريع مع جميع الميزات المتقدمة
 */
class ProjectController extends Controller
{
    protected AdvancedProjectManagementService $projectService;

    public function __construct(AdvancedProjectManagementService $projectService)
    {
        $this->projectService = $projectService;
    }

    /**
     * عرض قائمة المشاريع
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Project::class);

        $query = Project::with([
            'client', 'projectManager', 'team', 'tasks', 'milestones'
        ]);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب الأولوية
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // التصفية حسب العميل
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // التصفية حسب مدير المشروع
        if ($request->filled('project_manager_id')) {
            $query->where('project_manager_id', $request->project_manager_id);
        }

        // التصفية حسب المنهجية
        if ($request->filled('methodology')) {
            $query->where('methodology', $request->methodology);
        }

        // التصفية حسب الفئة
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // التصفية حسب التاريخ
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('start_date', [$request->start_date, $request->end_date]);
        }

        // التصفية حسب الميزانية
        if ($request->filled('min_budget')) {
            $query->where('budget', '>=', $request->min_budget);
        }
        if ($request->filled('max_budget')) {
            $query->where('budget', '<=', $request->max_budget);
        }

        // التصفية حسب التقدم
        if ($request->filled('min_progress')) {
            $query->where('progress_percentage', '>=', $request->min_progress);
        }

        // التصفية للمشاريع المتأخرة
        if ($request->boolean('overdue_only')) {
            $query->where('end_date', '<', now())
                  ->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
        }

        // التصفية للمشاريع النشطة
        if ($request->boolean('active_only')) {
            $query->whereIn('status', ['PLANNING', 'IN_PROGRESS', 'ON_HOLD']);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $projects = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new ProjectCollection($projects),
            'meta' => [
                'total' => $projects->total(),
                'per_page' => $projects->perPage(),
                'current_page' => $projects->currentPage(),
                'last_page' => $projects->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء مشروع جديد
     */
    public function store(StoreProjectRequest $request): JsonResponse
    {
        $this->authorize('create', Project::class);

        DB::beginTransaction();

        try {
            $project = $this->projectService->createAdvancedProject($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المشروع بنجاح',
                'data' => new ProjectResource($project->load([
                    'client', 'projectManager', 'team', 'milestones'
                ])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المشروع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل مشروع محدد
     */
    public function show(int $id): JsonResponse
    {
        $project = Project::with([
            'client',
            'projectManager',
            'team.user',
            'tasks.assignee',
            'milestones',
            'timeEntries.user',
            'documents',
            'comments.user',
            'activities.user',
            'risks',
            'sprints',
            'parent',
            'children',
        ])->findOrFail($id);

        $this->authorize('view', $project);

        // إضافة معلومات إضافية
        $additionalData = [
            'statistics' => $this->projectService->getProjectStatistics($project),
            'health_metrics' => $this->projectService->calculateHealthMetrics($project),
            'timeline_data' => $this->projectService->getTimelineData($project),
            'budget_analysis' => $this->projectService->getBudgetAnalysis($project),
            'team_performance' => $this->projectService->getTeamPerformance($project),
            'recent_activities' => $project->activities()->latest()->limit(10)->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => new ProjectResource($project),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث المشروع
     */
    public function update(UpdateProjectRequest $request, int $id): JsonResponse
    {
        $project = Project::findOrFail($id);
        $this->authorize('update', $project);

        DB::beginTransaction();

        try {
            $project = $this->projectService->updateProject($project, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث المشروع بنجاح',
                'data' => new ProjectResource($project->load([
                    'client', 'projectManager', 'team'
                ])),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث المشروع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف المشروع
     */
    public function destroy(int $id): JsonResponse
    {
        $project = Project::findOrFail($id);
        $this->authorize('delete', $project);

        try {
            // التحقق من وجود مهام أو بيانات مرتبطة
            if ($project->tasks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف المشروع لوجود مهام مرتبطة به',
                ], 422);
            }

            $project->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المشروع بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المشروع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تغيير حالة المشروع
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:PLANNING,IN_PROGRESS,ON_HOLD,COMPLETED,CANCELLED',
            'reason' => 'nullable|string|max:500',
        ]);

        $project = Project::findOrFail($id);
        $this->authorize('update', $project);

        try {
            $project = $this->projectService->changeProjectStatus(
                $project,
                $request->status,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تغيير حالة المشروع بنجاح',
                'data' => new ProjectResource($project),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير حالة المشروع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إضافة عضو للفريق
     */
    public function addTeamMember(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|string|max:100',
            'permissions' => 'nullable|array',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        $project = Project::findOrFail($id);
        $this->authorize('update', $project);

        try {
            $this->projectService->addTeamMember(
                $project,
                $request->user_id,
                $request->role,
                $request->permissions ?? [],
                $request->hourly_rate
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة عضو الفريق بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة عضو الفريق',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات المشاريع
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Project::class);

        $statistics = $this->projectService->getOverallStatistics($request->all());

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * تصدير المشاريع
     */
    public function export(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Project::class);

        $request->validate([
            'format' => 'required|string|in:excel,pdf,csv',
            'filters' => 'nullable|array',
        ]);

        try {
            $filePath = $this->projectService->exportProjects(
                $request->format,
                $request->filters ?? []
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تصدير المشاريع بنجاح',
                'download_url' => asset('storage/' . $filePath),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير المشاريع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
