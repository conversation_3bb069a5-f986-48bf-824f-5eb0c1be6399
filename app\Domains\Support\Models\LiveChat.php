<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الدردشة الحية - Live Chat Session
 * يدعم البوت الذكي والتحويل للوكلاء
 */
class LiveChat extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'session_id',
        'customer_id',
        'agent_id',
        'department_id',
        'status',
        'channel',
        'platform',
        'language',
        'country_code',
        'timezone',
        'visitor_info',
        'page_url',
        'referrer_url',
        'user_agent',
        'ip_address',
        'started_at',
        'agent_joined_at',
        'ended_at',
        'duration_seconds',
        'wait_time_seconds',
        'response_time_seconds',
        'customer_satisfaction',
        'agent_rating',
        'feedback',
        'tags',
        'priority',
        'is_bot_handled',
        'bot_confidence',
        'bot_fallback_reason',
        'escalation_reason',
        'transfer_count',
        'related_ticket_id',
        'conversation_summary',
        'ai_insights',
        'quality_score',
        'resolution_status',
        'follow_up_required',
        'follow_up_date',
        'metadata',
    ];

    protected $casts = [
        'visitor_info' => 'array',
        'started_at' => 'datetime',
        'agent_joined_at' => 'datetime',
        'ended_at' => 'datetime',
        'duration_seconds' => 'integer',
        'wait_time_seconds' => 'integer',
        'response_time_seconds' => 'integer',
        'customer_satisfaction' => 'integer',
        'agent_rating' => 'integer',
        'tags' => 'array',
        'is_bot_handled' => 'boolean',
        'bot_confidence' => 'decimal:2',
        'transfer_count' => 'integer',
        'ai_insights' => 'array',
        'quality_score' => 'integer',
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\CRM\Models\Customer::class);
    }

    /**
     * العلاقة مع الوكيل
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'agent_id');
    }

    /**
     * العلاقة مع القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * العلاقة مع الرسائل
     */
    public function messages(): HasMany
    {
        return $this->hasMany(LiveChatMessage::class, 'chat_id')->orderBy('created_at');
    }

    /**
     * العلاقة مع التذكرة المرتبطة
     */
    public function relatedTicket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class, 'related_ticket_id');
    }

    /**
     * العلاقة مع تحويلات الدردشة
     */
    public function transfers(): HasMany
    {
        return $this->hasMany(LiveChatTransfer::class, 'chat_id');
    }

    /**
     * الحصول على حالة الدردشة
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'waiting' => 'في الانتظار',
            'active' => 'نشطة',
            'transferred' => 'محولة',
            'ended' => 'منتهية',
            'abandoned' => 'متروكة',
            'bot_handled' => 'تم التعامل معها بواسطة البوت',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على مدة الدردشة المنسقة
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_seconds) {
            return '0 ثانية';
        }

        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;

        $parts = [];
        if ($hours > 0) $parts[] = "{$hours} ساعة";
        if ($minutes > 0) $parts[] = "{$minutes} دقيقة";
        if ($seconds > 0) $parts[] = "{$seconds} ثانية";

        return implode(' و ', $parts);
    }

    /**
     * الحصول على وقت الانتظار المنسق
     */
    public function getFormattedWaitTimeAttribute(): string
    {
        if (!$this->wait_time_seconds) {
            return '0 ثانية';
        }

        $minutes = floor($this->wait_time_seconds / 60);
        $seconds = $this->wait_time_seconds % 60;

        if ($minutes > 0) {
            return "{$minutes} دقيقة و {$seconds} ثانية";
        }

        return "{$seconds} ثانية";
    }

    /**
     * التحقق من نشاط الدردشة
     */
    public function getIsActiveAttribute(): bool
    {
        return in_array($this->status, ['waiting', 'active', 'transferred']);
    }

    /**
     * التحقق من انتهاء الدردشة
     */
    public function getIsEndedAttribute(): bool
    {
        return in_array($this->status, ['ended', 'abandoned']);
    }

    /**
     * الحصول على آخر رسالة
     */
    public function getLastMessageAttribute(): ?LiveChatMessage
    {
        return $this->messages()->latest()->first();
    }

    /**
     * الحصول على عدد الرسائل
     */
    public function getMessagesCountAttribute(): int
    {
        return $this->messages()->count();
    }

    /**
     * الحصول على عدد رسائل العميل
     */
    public function getCustomerMessagesCountAttribute(): int
    {
        return $this->messages()->where('sender_type', 'customer')->count();
    }

    /**
     * الحصول على عدد رسائل الوكيل
     */
    public function getAgentMessagesCountAttribute(): int
    {
        return $this->messages()->where('sender_type', 'agent')->count();
    }

    /**
     * الحصول على متوسط وقت الاستجابة
     */
    public function getAverageResponseTimeAttribute(): ?float
    {
        $agentMessages = $this->messages()
            ->where('sender_type', 'agent')
            ->whereNotNull('response_time_seconds')
            ->get();

        if ($agentMessages->isEmpty()) {
            return null;
        }

        return $agentMessages->avg('response_time_seconds');
    }

    /**
     * بدء الدردشة
     */
    public function start(): bool
    {
        return $this->update([
            'status' => 'waiting',
            'started_at' => now(),
        ]);
    }

    /**
     * انضمام الوكيل للدردشة
     */
    public function joinAgent(int $agentId): bool
    {
        $waitTime = $this->started_at ? now()->diffInSeconds($this->started_at) : 0;

        return $this->update([
            'agent_id' => $agentId,
            'status' => 'active',
            'agent_joined_at' => now(),
            'wait_time_seconds' => $waitTime,
        ]);
    }

    /**
     * تحويل الدردشة لوكيل آخر
     */
    public function transferTo(int $newAgentId, string $reason = null): LiveChatTransfer
    {
        $transfer = $this->transfers()->create([
            'from_agent_id' => $this->agent_id,
            'to_agent_id' => $newAgentId,
            'reason' => $reason,
            'transferred_at' => now(),
            'transferred_by' => auth()->id(),
        ]);

        $this->update([
            'agent_id' => $newAgentId,
            'status' => 'transferred',
            'transfer_count' => $this->transfer_count + 1,
        ]);

        // إضافة رسالة نظام
        $this->addSystemMessage("تم تحويل الدردشة إلى {$transfer->toAgent->name}");

        return $transfer;
    }

    /**
     * إنهاء الدردشة
     */
    public function end(string $reason = null, int $endedBy = null): bool
    {
        $duration = $this->started_at ? now()->diffInSeconds($this->started_at) : 0;

        $this->update([
            'status' => 'ended',
            'ended_at' => now(),
            'duration_seconds' => $duration,
        ]);

        // إضافة رسالة نظام
        $this->addSystemMessage('تم إنهاء الدردشة');

        // إنشاء ملخص تلقائي
        $this->generateSummary();

        return true;
    }

    /**
     * ترك الدردشة (من قبل العميل)
     */
    public function abandon(): bool
    {
        $duration = $this->started_at ? now()->diffInSeconds($this->started_at) : 0;

        return $this->update([
            'status' => 'abandoned',
            'ended_at' => now(),
            'duration_seconds' => $duration,
        ]);
    }

    /**
     * إضافة رسالة للدردشة
     */
    public function addMessage(array $messageData): LiveChatMessage
    {
        $messageData['chat_id'] = $this->id;
        
        // حساب وقت الاستجابة للوكيل
        if ($messageData['sender_type'] === 'agent') {
            $lastCustomerMessage = $this->messages()
                ->where('sender_type', 'customer')
                ->latest()
                ->first();

            if ($lastCustomerMessage) {
                $messageData['response_time_seconds'] = now()->diffInSeconds($lastCustomerMessage->created_at);
            }
        }

        return $this->messages()->create($messageData);
    }

    /**
     * إضافة رسالة نظام
     */
    public function addSystemMessage(string $content): LiveChatMessage
    {
        return $this->addMessage([
            'sender_type' => 'system',
            'sender_id' => null,
            'content' => $content,
            'message_type' => 'system',
        ]);
    }

    /**
     * تقييم الدردشة
     */
    public function rate(int $satisfaction, int $agentRating = null, string $feedback = null): bool
    {
        return $this->update([
            'customer_satisfaction' => $satisfaction,
            'agent_rating' => $agentRating,
            'feedback' => $feedback,
        ]);
    }

    /**
     * تحويل الدردشة إلى تذكرة
     */
    public function convertToTicket(array $ticketData = []): Ticket
    {
        $defaultData = [
            'subject' => 'دردشة حية - ' . $this->session_id,
            'description' => $this->conversation_summary ?? 'تم تحويل هذه الدردشة إلى تذكرة',
            'customer_id' => $this->customer_id,
            'assigned_to' => $this->agent_id,
            'channel' => 'live_chat',
            'source_reference' => $this->id,
            'priority' => $this->priority ?? 'medium',
            'status' => 'open',
        ];

        $ticket = Ticket::create(array_merge($defaultData, $ticketData));

        $this->update(['related_ticket_id' => $ticket->id]);

        return $ticket;
    }

    /**
     * توليد ملخص المحادثة
     */
    public function generateSummary(): void
    {
        $messages = $this->messages()
            ->whereIn('sender_type', ['customer', 'agent'])
            ->get();

        if ($messages->isEmpty()) {
            return;
        }

        $summary = "ملخص المحادثة:\n";
        $summary .= "عدد الرسائل: {$messages->count()}\n";
        $summary .= "مدة المحادثة: {$this->formatted_duration}\n";

        // استخراج النقاط الرئيسية
        $customerMessages = $messages->where('sender_type', 'customer');
        $agentMessages = $messages->where('sender_type', 'agent');

        if ($customerMessages->isNotEmpty()) {
            $summary .= "\nاستفسارات العميل الرئيسية:\n";
            foreach ($customerMessages->take(3) as $message) {
                $summary .= "- " . \Str::limit($message->content, 100) . "\n";
            }
        }

        if ($agentMessages->isNotEmpty()) {
            $summary .= "\nردود الوكيل:\n";
            foreach ($agentMessages->take(3) as $message) {
                $summary .= "- " . \Str::limit($message->content, 100) . "\n";
            }
        }

        $this->update(['conversation_summary' => $summary]);
    }

    /**
     * تحليل الدردشة بالذكاء الاصطناعي
     */
    public function analyzeWithAI(): array
    {
        // سيتم تنفيذها في الخدمة
        return [
            'sentiment' => 'neutral',
            'topics' => [],
            'resolution_status' => 'resolved',
            'quality_score' => 85,
        ];
    }

    /**
     * فلترة الدردشات النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['waiting', 'active', 'transferred']);
    }

    /**
     * فلترة الدردشات المنتهية
     */
    public function scopeEnded($query)
    {
        return $query->whereIn('status', ['ended', 'abandoned']);
    }

    /**
     * فلترة الدردشات في الانتظار
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * فلترة حسب الوكيل
     */
    public function scopeForAgent($query, int $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * فلترة حسب القسم
     */
    public function scopeForDepartment($query, int $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * فلترة الدردشات التي تعامل معها البوت
     */
    public function scopeBotHandled($query)
    {
        return $query->where('is_bot_handled', true);
    }

    /**
     * فلترة حسب التقييم
     */
    public function scopeWithSatisfaction($query, int $rating)
    {
        return $query->where('customer_satisfaction', $rating);
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}
