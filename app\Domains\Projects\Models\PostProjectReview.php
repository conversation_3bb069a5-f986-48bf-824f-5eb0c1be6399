<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج مراجعة ما بعد المشروع - Post Project Review
 */
class PostProjectReview extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'project_id',
        'reviewer_id',
        'overall_rating',
        'objectives_met',
        'timeline_performance',
        'budget_performance',
        'quality_rating',
        'team_performance',
        'communication_rating',
        'stakeholder_satisfaction',
        'what_went_well',
        'what_could_improve',
        'lessons_learned',
        'recommendations',
        'success_factors',
        'challenges_faced',
        'process_improvements',
        'tool_effectiveness',
        'team_feedback',
        'client_feedback',
        'would_recommend_approach',
        'metadata',
    ];

    protected $casts = [
        'overall_rating' => 'integer',
        'objectives_met' => 'integer',
        'timeline_performance' => 'integer',
        'budget_performance' => 'integer',
        'quality_rating' => 'integer',
        'team_performance' => 'integer',
        'communication_rating' => 'integer',
        'stakeholder_satisfaction' => 'integer',
        'what_went_well' => 'array',
        'what_could_improve' => 'array',
        'lessons_learned' => 'array',
        'recommendations' => 'array',
        'success_factors' => 'array',
        'challenges_faced' => 'array',
        'process_improvements' => 'array',
        'tool_effectiveness' => 'array',
        'team_feedback' => 'array',
        'would_recommend_approach' => 'boolean',
        'metadata' => 'array',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'reviewer_id');
    }
}
