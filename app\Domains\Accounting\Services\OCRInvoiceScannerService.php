<?php

namespace App\Domains\Accounting\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

/**
 * خدمة مسح الفواتير باستخدام OCR والذكاء الاصطناعي
 * تدعم العربية والفرنسية والإنجليزية
 */
class OCRInvoiceScannerService
{
    protected array $supportedLanguages = ['ar', 'fr', 'en'];
    protected array $ocrProviders = [];
    protected array $invoiceTemplates = [];

    public function __construct()
    {
        $this->loadOCRProviders();
        $this->loadInvoiceTemplates();
    }

    /**
     * مسح فاتورة من صورة
     */
    public function scanInvoice(string $imagePath, array $options = []): array
    {
        try {
            // تحسين جودة الصورة
            $processedImagePath = $this->preprocessImage($imagePath);
            
            // استخراج النص باستخدام OCR
            $extractedText = $this->extractTextFromImage($processedImagePath, $options);
            
            // تحليل النص المستخرج
            $parsedData = $this->parseInvoiceText($extractedText, $options);
            
            // التحقق من صحة البيانات
            $validatedData = $this->validateExtractedData($parsedData);
            
            // تحسين البيانات باستخدام الذكاء الاصطناعي
            $enhancedData = $this->enhanceDataWithAI($validatedData, $extractedText);

            return [
                'success' => true,
                'extracted_data' => $enhancedData,
                'confidence_score' => $this->calculateConfidenceScore($enhancedData),
                'raw_text' => $extractedText,
                'processing_time' => microtime(true) - LARAVEL_START,
                'language_detected' => $this->detectLanguage($extractedText),
                'template_matched' => $this->matchInvoiceTemplate($enhancedData),
            ];

        } catch (\Exception $e) {
            Log::error('خطأ في مسح الفاتورة', [
                'image_path' => $imagePath,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'extracted_data' => null,
            ];
        }
    }

    /**
     * معالجة الصورة مسبقاً لتحسين دقة OCR
     */
    protected function preprocessImage(string $imagePath): string
    {
        $image = Image::make($imagePath);
        
        // تحسين الدقة والوضوح
        $image->resize(null, 2000, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });
        
        // تحسين التباين
        $image->contrast(20);
        
        // تحسين السطوع
        $image->brightness(10);
        
        // تحويل لرمادي لتحسين OCR
        $image->greyscale();
        
        // حفظ الصورة المحسنة
        $processedPath = 'temp/processed_' . basename($imagePath);
        $image->save(storage_path('app/' . $processedPath));
        
        return storage_path('app/' . $processedPath);
    }

    /**
     * استخراج النص من الصورة
     */
    protected function extractTextFromImage(string $imagePath, array $options = []): string
    {
        $language = $options['language'] ?? 'ar+fr+en';
        $provider = $options['ocr_provider'] ?? 'tesseract';
        
        switch ($provider) {
            case 'google_vision':
                return $this->extractWithGoogleVision($imagePath, $language);
            case 'azure_cognitive':
                return $this->extractWithAzureCognitive($imagePath, $language);
            case 'aws_textract':
                return $this->extractWithAWSTextract($imagePath);
            case 'tesseract':
            default:
                return $this->extractWithTesseract($imagePath, $language);
        }
    }

    /**
     * استخراج النص باستخدام Tesseract
     */
    protected function extractWithTesseract(string $imagePath, string $language): string
    {
        // تحويل رموز اللغات لـ Tesseract
        $tesseractLang = $this->convertLanguageForTesseract($language);
        
        $command = "tesseract '{$imagePath}' stdout -l {$tesseractLang} --psm 6";
        
        $output = shell_exec($command);
        
        return $output ?: '';
    }

    /**
     * استخراج النص باستخدام Google Vision API
     */
    protected function extractWithGoogleVision(string $imagePath, string $language): string
    {
        $apiKey = config('services.google.vision_api_key');
        
        if (!$apiKey) {
            throw new \Exception('Google Vision API key not configured');
        }

        $imageData = base64_encode(file_get_contents($imagePath));
        
        $response = Http::post("https://vision.googleapis.com/v1/images:annotate?key={$apiKey}", [
            'requests' => [
                [
                    'image' => [
                        'content' => $imageData
                    ],
                    'features' => [
                        [
                            'type' => 'TEXT_DETECTION',
                            'maxResults' => 1
                        ]
                    ],
                    'imageContext' => [
                        'languageHints' => explode('+', $language)
                    ]
                ]
            ]
        ]);

        $result = $response->json();
        
        return $result['responses'][0]['textAnnotations'][0]['description'] ?? '';
    }

    /**
     * استخراج النص باستخدام Azure Cognitive Services
     */
    protected function extractWithAzureCognitive(string $imagePath, string $language): string
    {
        $endpoint = config('services.azure.cognitive_endpoint');
        $apiKey = config('services.azure.cognitive_api_key');
        
        if (!$endpoint || !$apiKey) {
            throw new \Exception('Azure Cognitive Services not configured');
        }

        $imageData = file_get_contents($imagePath);
        
        $response = Http::withHeaders([
            'Ocp-Apim-Subscription-Key' => $apiKey,
            'Content-Type' => 'application/octet-stream'
        ])->post($endpoint . '/vision/v3.2/ocr', $imageData, [
            'language' => $this->convertLanguageForAzure($language),
            'detectOrientation' => 'true'
        ]);

        $result = $response->json();
        
        $text = '';
        foreach ($result['regions'] ?? [] as $region) {
            foreach ($region['lines'] as $line) {
                foreach ($line['words'] as $word) {
                    $text .= $word['text'] . ' ';
                }
                $text .= "\n";
            }
        }
        
        return trim($text);
    }

    /**
     * استخراج النص باستخدام AWS Textract
     */
    protected function extractWithAWSTextract(string $imagePath): string
    {
        // تطبيق AWS Textract - يتطلب AWS SDK
        // هذا مثال مبسط
        return "AWS Textract integration needed";
    }

    /**
     * تحليل النص المستخرج
     */
    protected function parseInvoiceText(string $text, array $options = []): array
    {
        $parsedData = [
            'vendor_name' => null,
            'vendor_address' => null,
            'vendor_tax_id' => null,
            'invoice_number' => null,
            'invoice_date' => null,
            'due_date' => null,
            'currency' => 'MAD',
            'line_items' => [],
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'payment_terms' => null,
        ];

        // استخراج اسم المورد
        $parsedData['vendor_name'] = $this->extractVendorName($text);
        
        // استخراج رقم الفاتورة
        $parsedData['invoice_number'] = $this->extractInvoiceNumber($text);
        
        // استخراج التاريخ
        $parsedData['invoice_date'] = $this->extractInvoiceDate($text);
        
        // استخراج المبالغ
        $amounts = $this->extractAmounts($text);
        $parsedData = array_merge($parsedData, $amounts);
        
        // استخراج بنود الفاتورة
        $parsedData['line_items'] = $this->extractLineItems($text);
        
        // استخراج الرقم الضريبي
        $parsedData['vendor_tax_id'] = $this->extractTaxId($text);

        return $parsedData;
    }

    /**
     * استخراج اسم المورد
     */
    protected function extractVendorName(string $text): ?string
    {
        // أنماط مختلفة لاستخراج اسم المورد
        $patterns = [
            '/^([A-Z][A-Za-z\s&\-\.]+)$/m', // خط منفصل بأحرف كبيرة
            '/(?:من|FROM|DE)[\s:]+([A-Za-z\s&\-\.]+)/i',
            '/^([^\n]+)(?:\n.*(?:RC|ICE|IF))/m', // اسم قبل الأرقام التجارية
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $vendorName = trim($matches[1]);
                if (strlen($vendorName) > 3 && strlen($vendorName) < 100) {
                    return $vendorName;
                }
            }
        }

        return null;
    }

    /**
     * استخراج رقم الفاتورة
     */
    protected function extractInvoiceNumber(string $text): ?string
    {
        $patterns = [
            '/(?:فاتورة|FACTURE|INVOICE)[\s#:N°]+([A-Z0-9\-\/]+)/i',
            '/(?:N°|NO|#)[\s]*([A-Z0-9\-\/]+)/i',
            '/([A-Z]{2,}\d{3,})/i', // نمط عام للأرقام
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                return trim($matches[1]);
            }
        }

        return null;
    }

    /**
     * استخراج تاريخ الفاتورة
     */
    protected function extractInvoiceDate(string $text): ?string
    {
        $patterns = [
            '/(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})/',
            '/(\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2})/',
            '/(\d{1,2}\s+(?:يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)\s+\d{4})/i',
            '/(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                try {
                    $date = \Carbon\Carbon::parse($matches[1]);
                    return $date->format('Y-m-d');
                } catch (\Exception $e) {
                    continue;
                }
            }
        }

        return null;
    }

    /**
     * استخراج المبالغ
     */
    protected function extractAmounts(string $text): array
    {
        $amounts = [
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
        ];

        // استخراج المبلغ الإجمالي
        $totalPatterns = [
            '/(?:المجموع|TOTAL|MONTANT)[\s:]+([0-9,\.\s]+)(?:DH|MAD|€|$)/i',
            '/([0-9,\.\s]+)[\s]*(?:DH|MAD|€|$)[\s]*(?:TTC|شامل)/i',
        ];

        foreach ($totalPatterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $amounts['total_amount'] = $this->parseAmount($matches[1]);
                break;
            }
        }

        // استخراج ضريبة القيمة المضافة
        $taxPatterns = [
            '/(?:TVA|ضريبة)[\s:]+([0-9,\.\s]+)/i',
            '/([0-9,\.\s]+)[\s]*(?:DH|MAD)[\s]*(?:TVA|ضريبة)/i',
        ];

        foreach ($taxPatterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $amounts['tax_amount'] = $this->parseAmount($matches[1]);
                break;
            }
        }

        // حساب المبلغ قبل الضريبة
        if ($amounts['total_amount'] > 0 && $amounts['tax_amount'] > 0) {
            $amounts['subtotal'] = $amounts['total_amount'] - $amounts['tax_amount'];
        } elseif ($amounts['total_amount'] > 0) {
            $amounts['subtotal'] = $amounts['total_amount'];
        }

        return $amounts;
    }

    /**
     * تحويل النص إلى مبلغ رقمي
     */
    protected function parseAmount(string $amountText): float
    {
        // إزالة المسافات والرموز غير الرقمية
        $cleaned = preg_replace('/[^\d,\.]/', '', $amountText);
        
        // التعامل مع الفواصل والنقاط
        $cleaned = str_replace(',', '.', $cleaned);
        
        return (float) $cleaned;
    }

    /**
     * استخراج بنود الفاتورة
     */
    protected function extractLineItems(string $text): array
    {
        $lineItems = [];
        
        // نمط بسيط لاستخراج البنود
        $lines = explode("\n", $text);
        
        foreach ($lines as $line) {
            if (preg_match('/(.+?)\s+(\d+)\s+([0-9,\.]+)\s+([0-9,\.]+)/', $line, $matches)) {
                $lineItems[] = [
                    'description' => trim($matches[1]),
                    'quantity' => (int) $matches[2],
                    'unit_price' => $this->parseAmount($matches[3]),
                    'amount' => $this->parseAmount($matches[4]),
                ];
            }
        }

        return $lineItems;
    }

    /**
     * استخراج الرقم الضريبي
     */
    protected function extractTaxId(string $text): ?string
    {
        $patterns = [
            '/(?:ICE|RC|IF)[\s:]+([0-9]+)/i',
            '/([0-9]{8,15})/', // رقم طويل محتمل
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * التحقق من صحة البيانات المستخرجة
     */
    protected function validateExtractedData(array $data): array
    {
        // التحقق من المبالغ
        if ($data['total_amount'] < 0) {
            $data['total_amount'] = 0;
        }

        // التحقق من التاريخ
        if ($data['invoice_date']) {
            try {
                \Carbon\Carbon::parse($data['invoice_date']);
            } catch (\Exception $e) {
                $data['invoice_date'] = null;
            }
        }

        // التحقق من اسم المورد
        if ($data['vendor_name'] && strlen($data['vendor_name']) < 2) {
            $data['vendor_name'] = null;
        }

        return $data;
    }

    /**
     * تحسين البيانات باستخدام الذكاء الاصطناعي
     */
    protected function enhanceDataWithAI(array $data, string $rawText): array
    {
        // تطبيق خوارزميات الذكاء الاصطناعي لتحسين دقة البيانات
        
        // تصحيح اسم المورد
        if ($data['vendor_name']) {
            $data['vendor_name'] = $this->correctVendorNameWithAI($data['vendor_name']);
        }

        // تصحيح العملة
        $data['currency'] = $this->detectCurrency($rawText);

        // تحسين بنود الفاتورة
        $data['line_items'] = $this->enhanceLineItemsWithAI($data['line_items'], $rawText);

        return $data;
    }

    /**
     * حساب نقاط الثقة
     */
    protected function calculateConfidenceScore(array $data): float
    {
        $score = 0;
        $maxScore = 100;

        // نقاط لكل حقل مستخرج بنجاح
        if ($data['vendor_name']) $score += 20;
        if ($data['invoice_number']) $score += 15;
        if ($data['invoice_date']) $score += 15;
        if ($data['total_amount'] > 0) $score += 25;
        if (!empty($data['line_items'])) $score += 15;
        if ($data['vendor_tax_id']) $score += 10;

        return min($score, $maxScore);
    }

    /**
     * كشف اللغة
     */
    protected function detectLanguage(string $text): string
    {
        // كشف بسيط للغة
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
            return 'ar';
        } elseif (preg_match('/[àâäéèêëïîôöùûüÿç]/i', $text)) {
            return 'fr';
        } else {
            return 'en';
        }
    }

    /**
     * مطابقة قالب الفاتورة
     */
    protected function matchInvoiceTemplate(array $data): ?string
    {
        // مطابقة مع قوالب الفواتير المعروفة
        foreach ($this->invoiceTemplates as $templateName => $template) {
            if ($this->matchesTemplate($data, $template)) {
                return $templateName;
            }
        }

        return null;
    }

    // دوال مساعدة
    protected function loadOCRProviders(): void
    {
        $this->ocrProviders = [
            'tesseract' => ['enabled' => true, 'priority' => 1],
            'google_vision' => ['enabled' => true, 'priority' => 2],
            'azure_cognitive' => ['enabled' => true, 'priority' => 3],
            'aws_textract' => ['enabled' => false, 'priority' => 4],
        ];
    }

    protected function loadInvoiceTemplates(): void
    {
        $this->invoiceTemplates = [
            'standard_moroccan' => [
                'required_fields' => ['vendor_name', 'invoice_number', 'total_amount'],
                'tax_patterns' => ['TVA', 'ضريبة'],
                'currency' => 'MAD',
            ],
        ];
    }

    protected function convertLanguageForTesseract(string $language): string
    {
        return str_replace(['ar', 'fr', 'en'], ['ara', 'fra', 'eng'], $language);
    }

    protected function convertLanguageForAzure(string $language): string
    {
        return explode('+', $language)[0]; // أخذ اللغة الأولى فقط
    }

    protected function correctVendorNameWithAI(string $name): string
    {
        // تطبيق خوارزميات تصحيح الأسماء
        return trim($name);
    }

    protected function detectCurrency(string $text): string
    {
        if (preg_match('/DH|MAD|درهم/i', $text)) return 'MAD';
        if (preg_match('/€|EUR|يورو/i', $text)) return 'EUR';
        if (preg_match('/\$|USD|دولار/i', $text)) return 'USD';
        return 'MAD'; // افتراضي
    }

    protected function enhanceLineItemsWithAI(array $lineItems, string $rawText): array
    {
        // تحسين بنود الفاتورة باستخدام الذكاء الاصطناعي
        return $lineItems;
    }

    protected function matchesTemplate(array $data, array $template): bool
    {
        foreach ($template['required_fields'] as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }
        return true;
    }
}
