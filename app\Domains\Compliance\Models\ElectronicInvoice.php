<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Str;

/**
 * نموذج الفواتير الإلكترونية المتقدم
 * يدير جميع أنواع الفواتير الإلكترونية لجميع الدول
 */
class ElectronicInvoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_uuid',
        'invoice_number',
        'invoice_hash',
        'company_id',
        'user_id',
        'e_invoicing_configuration_id',
        'country_id',
        'related_model_type',
        'related_model_id',
        'invoice_type',
        'document_type',
        'invoice_subtype',
        'issue_date',
        'issue_time',
        'due_date',
        'supply_date',
        'currency',
        'exchange_rate',
        'seller_info',
        'buyer_info',
        'line_items',
        'tax_breakdown',
        'totals',
        'payment_terms',
        'delivery_info',
        'additional_info',
        'subtotal_amount',
        'discount_amount',
        'tax_amount',
        'total_amount',
        'paid_amount',
        'outstanding_amount',
        'qr_code',
        'digital_signature',
        'xml_content',
        'json_content',
        'pdf_content',
        'ubl_content',
        'validation_results',
        'compliance_status',
        'submission_status',
        'clearance_status',
        'government_response',
        'submission_reference',
        'clearance_reference',
        'zatca_reference',
        'eta_reference',
        'fta_reference',
        'dgi_reference',
        'error_messages',
        'warning_messages',
        'retry_count',
        'last_retry_at',
        'submitted_at',
        'cleared_at',
        'rejected_at',
        'cancelled_at',
        'metadata',
        'audit_trail',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'issue_time' => 'datetime',
        'due_date' => 'date',
        'supply_date' => 'date',
        'exchange_rate' => 'decimal:6',
        'seller_info' => 'array',
        'buyer_info' => 'array',
        'line_items' => 'array',
        'tax_breakdown' => 'array',
        'totals' => 'array',
        'payment_terms' => 'array',
        'delivery_info' => 'array',
        'additional_info' => 'array',
        'subtotal_amount' => 'decimal:4',
        'discount_amount' => 'decimal:4',
        'tax_amount' => 'decimal:4',
        'total_amount' => 'decimal:4',
        'paid_amount' => 'decimal:4',
        'outstanding_amount' => 'decimal:4',
        'digital_signature' => 'array',
        'validation_results' => 'array',
        'government_response' => 'array',
        'error_messages' => 'array',
        'warning_messages' => 'array',
        'metadata' => 'array',
        'audit_trail' => 'array',
        'submitted_at' => 'datetime',
        'cleared_at' => 'datetime',
        'rejected_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'last_retry_at' => 'datetime',
    ];

    /**
     * أنواع الفواتير
     */
    const INVOICE_TYPES = [
        'standard' => 'فاتورة عادية',
        'simplified' => 'فاتورة مبسطة',
        'debit_note' => 'إشعار دائن',
        'credit_note' => 'إشعار مدين',
        'prepayment' => 'فاتورة دفعة مقدمة',
        'tax_invoice' => 'فاتورة ضريبية',
        'commercial_invoice' => 'فاتورة تجارية',
        'proforma' => 'فاتورة أولية',
        'self_billing' => 'فاتورة ذاتية',
        'summary_invoice' => 'فاتورة إجمالية',
    ];

    /**
     * أنواع الوثائق
     */
    const DOCUMENT_TYPES = [
        '388' => 'Tax Invoice - فاتورة ضريبية',
        '381' => 'Credit Note - إشعار دائن',
        '383' => 'Debit Note - إشعار مدين',
        '386' => 'Prepayment Invoice - فاتورة دفعة مقدمة',
        '384' => 'Corrected Invoice - فاتورة مصححة',
        '380' => 'Commercial Invoice - فاتورة تجارية',
    ];

    /**
     * حالات الامتثال
     */
    const COMPLIANCE_STATUSES = [
        'compliant' => 'متوافق',
        'non_compliant' => 'غير متوافق',
        'pending_validation' => 'قيد التحقق',
        'validation_failed' => 'فشل التحقق',
        'requires_correction' => 'يتطلب تصحيح',
    ];

    /**
     * حالات التقديم
     */
    const SUBMISSION_STATUSES = [
        'draft' => 'مسودة',
        'ready_to_submit' => 'جاهز للإرسال',
        'submitting' => 'قيد الإرسال',
        'submitted' => 'مرسل',
        'acknowledged' => 'مستلم',
        'rejected' => 'مرفوض',
        'failed' => 'فشل',
        'cancelled' => 'ملغي',
    ];

    /**
     * حالات التخليص
     */
    const CLEARANCE_STATUSES = [
        'not_required' => 'غير مطلوب',
        'pending_clearance' => 'قيد التخليص',
        'cleared' => 'مخلص',
        'clearance_failed' => 'فشل التخليص',
        'requires_manual_review' => 'يتطلب مراجعة يدوية',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع تكوين الفوترة الإلكترونية
     */
    public function eInvoicingConfiguration(): BelongsTo
    {
        return $this->belongsTo(EInvoicingConfiguration::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع النموذج المرتبط (polymorphic)
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع سجلات الإرسال
     */
    public function submissionLogs(): HasMany
    {
        return $this->hasMany(EInvoiceSubmissionLog::class);
    }

    /**
     * العلاقة مع التعديلات
     */
    public function amendments(): HasMany
    {
        return $this->hasMany(EInvoiceAmendment::class);
    }

    /**
     * إنشاء فاتورة إلكترونية جديدة
     */
    public static function createFromInvoiceData(array $invoiceData, Company $company): self
    {
        $country = Country::where('code', $invoiceData['country_code'] ?? $company->country_code)->first();
        $eInvoicingConfig = $country?->eInvoicingSystem;

        if (!$eInvoicingConfig || !$eInvoicingConfig->is_active) {
            throw new \Exception('نظام الفوترة الإلكترونية غير متوفر لهذه الدولة');
        }

        $invoice = self::create([
            'invoice_uuid' => Str::uuid(),
            'invoice_number' => $invoiceData['invoice_number'],
            'company_id' => $company->id,
            'e_invoicing_configuration_id' => $eInvoicingConfig->id,
            'country_id' => $country->id,
            'invoice_type' => $invoiceData['invoice_type'] ?? 'standard',
            'document_type' => $invoiceData['document_type'] ?? '388',
            'issue_date' => $invoiceData['issue_date'] ?? now(),
            'issue_time' => $invoiceData['issue_time'] ?? now(),
            'due_date' => $invoiceData['due_date'] ?? null,
            'currency' => $invoiceData['currency'] ?? $country->currency,
            'seller_info' => $invoiceData['seller_info'],
            'buyer_info' => $invoiceData['buyer_info'],
            'line_items' => $invoiceData['line_items'],
            'payment_terms' => $invoiceData['payment_terms'] ?? [],
            'delivery_info' => $invoiceData['delivery_info'] ?? [],
            'additional_info' => $invoiceData['additional_info'] ?? [],
            'submission_status' => 'draft',
            'compliance_status' => 'pending_validation',
        ]);

        // حساب الإجماليات
        $invoice->calculateTotals();

        // توليد المحتوى المطلوب
        $invoice->generateRequiredContent();

        return $invoice;
    }

    /**
     * حساب الإجماليات
     */
    public function calculateTotals(): void
    {
        $lineItems = $this->line_items ?? [];
        $subtotal = 0;
        $totalDiscount = 0;
        $totalTax = 0;
        $taxBreakdown = [];

        foreach ($lineItems as $item) {
            $lineSubtotal = $item['quantity'] * $item['unit_price'];
            $lineDiscount = $item['discount_amount'] ?? 0;
            $lineTaxableAmount = $lineSubtotal - $lineDiscount;
            $lineTax = $lineTaxableAmount * ($item['tax_rate'] ?? 0) / 100;

            $subtotal += $lineSubtotal;
            $totalDiscount += $lineDiscount;
            $totalTax += $lineTax;

            // تجميع الضرائب حسب المعدل
            $taxRate = $item['tax_rate'] ?? 0;
            if (!isset($taxBreakdown[$taxRate])) {
                $taxBreakdown[$taxRate] = [
                    'rate' => $taxRate,
                    'taxable_amount' => 0,
                    'tax_amount' => 0,
                ];
            }
            $taxBreakdown[$taxRate]['taxable_amount'] += $lineTaxableAmount;
            $taxBreakdown[$taxRate]['tax_amount'] += $lineTax;
        }

        $total = $subtotal - $totalDiscount + $totalTax;

        $this->update([
            'subtotal_amount' => $subtotal,
            'discount_amount' => $totalDiscount,
            'tax_amount' => $totalTax,
            'total_amount' => $total,
            'outstanding_amount' => $total,
            'tax_breakdown' => array_values($taxBreakdown),
            'totals' => [
                'subtotal' => $subtotal,
                'discount' => $totalDiscount,
                'taxable_amount' => $subtotal - $totalDiscount,
                'tax' => $totalTax,
                'total' => $total,
            ],
        ]);
    }

    /**
     * توليد المحتوى المطلوب
     */
    public function generateRequiredContent(): void
    {
        $countryCode = $this->country->code;

        // توليد المحتوى حسب الدولة
        match ($countryCode) {
            'SA' => $this->generateSaudiContent(),
            'EG' => $this->generateEgyptianContent(),
            'AE' => $this->generateUAEContent(),
            'MA' => $this->generateMoroccanContent(),
            default => $this->generateGenericContent(),
        };

        // توليد QR Code
        $this->generateQRCode();

        // توليد Hash
        $this->generateInvoiceHash();
    }

    /**
     * توليد المحتوى السعودي (فاتورة)
     */
    protected function generateSaudiContent(): void
    {
        // توليد UBL XML
        $ublContent = $this->generateSaudiUBL();
        
        // توليد JSON
        $jsonContent = $this->generateSaudiJSON();

        $this->update([
            'ubl_content' => $ublContent,
            'json_content' => $jsonContent,
            'xml_content' => $ublContent,
        ]);
    }

    /**
     * توليد UBL للسعودية
     */
    protected function generateSaudiUBL(): string
    {
        $seller = $this->seller_info;
        $buyer = $this->buyer_info;
        $lineItems = $this->line_items;

        $ubl = '<?xml version="1.0" encoding="UTF-8"?>';
        $ubl .= '<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">';
        $ubl .= '<cbc:UBLVersionID>2.1</cbc:UBLVersionID>';
        $ubl .= '<cbc:CustomizationID>BR-KSA-25</cbc:CustomizationID>';
        $ubl .= '<cbc:ProfileID>reporting:1.0</cbc:ProfileID>';
        $ubl .= '<cbc:ID>' . $this->invoice_number . '</cbc:ID>';
        $ubl .= '<cbc:UUID>' . $this->invoice_uuid . '</cbc:UUID>';
        $ubl .= '<cbc:IssueDate>' . $this->issue_date->format('Y-m-d') . '</cbc:IssueDate>';
        $ubl .= '<cbc:IssueTime>' . $this->issue_time->format('H:i:s') . '</cbc:IssueTime>';
        $ubl .= '<cbc:InvoiceTypeCode>' . $this->document_type . '</cbc:InvoiceTypeCode>';
        $ubl .= '<cbc:DocumentCurrencyCode>' . $this->currency . '</cbc:DocumentCurrencyCode>';
        $ubl .= '<cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>';

        // معلومات البائع
        $ubl .= '<cac:AccountingSupplierParty>';
        $ubl .= '<cac:Party>';
        $ubl .= '<cac:PartyIdentification>';
        $ubl .= '<cbc:ID schemeID="CRN">' . $seller['commercial_registration'] . '</cbc:ID>';
        $ubl .= '</cac:PartyIdentification>';
        $ubl .= '<cac:PartyTaxScheme>';
        $ubl .= '<cbc:CompanyID>' . $seller['tax_number'] . '</cbc:CompanyID>';
        $ubl .= '<cac:TaxScheme>';
        $ubl .= '<cbc:ID>VAT</cbc:ID>';
        $ubl .= '</cac:TaxScheme>';
        $ubl .= '</cac:PartyTaxScheme>';
        $ubl .= '<cac:PartyLegalEntity>';
        $ubl .= '<cbc:RegistrationName>' . $seller['name'] . '</cbc:RegistrationName>';
        $ubl .= '</cac:PartyLegalEntity>';
        $ubl .= '</cac:Party>';
        $ubl .= '</cac:AccountingSupplierParty>';

        // معلومات المشتري
        $ubl .= '<cac:AccountingCustomerParty>';
        $ubl .= '<cac:Party>';
        if (isset($buyer['tax_number'])) {
            $ubl .= '<cac:PartyTaxScheme>';
            $ubl .= '<cbc:CompanyID>' . $buyer['tax_number'] . '</cbc:CompanyID>';
            $ubl .= '<cac:TaxScheme>';
            $ubl .= '<cbc:ID>VAT</cbc:ID>';
            $ubl .= '</cac:TaxScheme>';
            $ubl .= '</cac:PartyTaxScheme>';
        }
        $ubl .= '<cac:PartyLegalEntity>';
        $ubl .= '<cbc:RegistrationName>' . $buyer['name'] . '</cbc:RegistrationName>';
        $ubl .= '</cac:PartyLegalEntity>';
        $ubl .= '</cac:Party>';
        $ubl .= '</cac:AccountingCustomerParty>';

        // بنود الفاتورة
        foreach ($lineItems as $index => $item) {
            $ubl .= '<cac:InvoiceLine>';
            $ubl .= '<cbc:ID>' . ($index + 1) . '</cbc:ID>';
            $ubl .= '<cbc:InvoicedQuantity unitCode="' . ($item['unit_code'] ?? 'PCE') . '">' . $item['quantity'] . '</cbc:InvoicedQuantity>';
            $ubl .= '<cbc:LineExtensionAmount currencyID="' . $this->currency . '">' . ($item['quantity'] * $item['unit_price']) . '</cbc:LineExtensionAmount>';
            $ubl .= '<cac:Item>';
            $ubl .= '<cbc:Name>' . $item['name'] . '</cbc:Name>';
            $ubl .= '</cac:Item>';
            $ubl .= '<cac:Price>';
            $ubl .= '<cbc:PriceAmount currencyID="' . $this->currency . '">' . $item['unit_price'] . '</cbc:PriceAmount>';
            $ubl .= '</cac:Price>';
            $ubl .= '</cac:InvoiceLine>';
        }

        // الإجماليات الضريبية
        $ubl .= '<cac:TaxTotal>';
        $ubl .= '<cbc:TaxAmount currencyID="' . $this->currency . '">' . $this->tax_amount . '</cbc:TaxAmount>';
        foreach ($this->tax_breakdown as $taxGroup) {
            $ubl .= '<cac:TaxSubtotal>';
            $ubl .= '<cbc:TaxableAmount currencyID="' . $this->currency . '">' . $taxGroup['taxable_amount'] . '</cbc:TaxableAmount>';
            $ubl .= '<cbc:TaxAmount currencyID="' . $this->currency . '">' . $taxGroup['tax_amount'] . '</cbc:TaxAmount>';
            $ubl .= '<cac:TaxCategory>';
            $ubl .= '<cbc:ID>S</cbc:ID>';
            $ubl .= '<cbc:Percent>' . $taxGroup['rate'] . '</cbc:Percent>';
            $ubl .= '<cac:TaxScheme>';
            $ubl .= '<cbc:ID>VAT</cbc:ID>';
            $ubl .= '</cac:TaxScheme>';
            $ubl .= '</cac:TaxCategory>';
            $ubl .= '</cac:TaxSubtotal>';
        }
        $ubl .= '</cac:TaxTotal>';

        // الإجمالي النهائي
        $ubl .= '<cac:LegalMonetaryTotal>';
        $ubl .= '<cbc:LineExtensionAmount currencyID="' . $this->currency . '">' . $this->subtotal_amount . '</cbc:LineExtensionAmount>';
        $ubl .= '<cbc:TaxExclusiveAmount currencyID="' . $this->currency . '">' . ($this->subtotal_amount - $this->discount_amount) . '</cbc:TaxExclusiveAmount>';
        $ubl .= '<cbc:TaxInclusiveAmount currencyID="' . $this->currency . '">' . $this->total_amount . '</cbc:TaxInclusiveAmount>';
        $ubl .= '<cbc:PayableAmount currencyID="' . $this->currency . '">' . $this->total_amount . '</cbc:PayableAmount>';
        $ubl .= '</cac:LegalMonetaryTotal>';

        $ubl .= '</Invoice>';

        return $ubl;
    }

    /**
     * توليد JSON للسعودية
     */
    protected function generateSaudiJSON(): string
    {
        $data = [
            'invoice_number' => $this->invoice_number,
            'uuid' => $this->invoice_uuid,
            'issue_date' => $this->issue_date->format('Y-m-d'),
            'issue_time' => $this->issue_time->format('H:i:s'),
            'invoice_type' => $this->invoice_type,
            'document_type' => $this->document_type,
            'currency' => $this->currency,
            'seller' => $this->seller_info,
            'buyer' => $this->buyer_info,
            'line_items' => $this->line_items,
            'tax_breakdown' => $this->tax_breakdown,
            'totals' => $this->totals,
        ];

        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * توليد QR Code
     */
    protected function generateQRCode(): void
    {
        $countryCode = $this->country->code;

        $qrData = match ($countryCode) {
            'SA' => $this->generateSaudiQRData(),
            'EG' => $this->generateEgyptianQRData(),
            'AE' => $this->generateUAEQRData(),
            default => $this->generateGenericQRData(),
        };

        $this->update(['qr_code' => base64_encode($qrData)]);
    }

    /**
     * توليد QR Code للسعودية
     */
    protected function generateSaudiQRData(): string
    {
        $seller = $this->seller_info;
        
        // تنسيق QR Code حسب معايير ZATCA
        $qrData = '';
        $qrData .= $this->addQRField(1, $seller['name']); // اسم البائع
        $qrData .= $this->addQRField(2, $seller['tax_number']); // الرقم الضريبي
        $qrData .= $this->addQRField(3, $this->issue_date->format('Y-m-d\TH:i:s\Z')); // تاريخ الفاتورة
        $qrData .= $this->addQRField(4, $this->total_amount); // إجمالي الفاتورة
        $qrData .= $this->addQRField(5, $this->tax_amount); // إجمالي الضريبة

        return $qrData;
    }

    /**
     * إضافة حقل QR
     */
    protected function addQRField(int $tag, string $value): string
    {
        $tagHex = pack('C', $tag);
        $lengthHex = pack('C', strlen($value));
        return $tagHex . $lengthHex . $value;
    }

    /**
     * توليد Hash للفاتورة
     */
    protected function generateInvoiceHash(): void
    {
        $hashData = [
            'invoice_number' => $this->invoice_number,
            'issue_date' => $this->issue_date->format('Y-m-d'),
            'total_amount' => $this->total_amount,
            'seller_tax_number' => $this->seller_info['tax_number'] ?? '',
            'buyer_tax_number' => $this->buyer_info['tax_number'] ?? '',
        ];

        $hash = hash('sha256', json_encode($hashData, JSON_SORT_KEYS));
        $this->update(['invoice_hash' => $hash]);
    }

    /**
     * التحقق من صحة الفاتورة
     */
    public function validateInvoice(): array
    {
        $eInvoicingConfig = $this->eInvoicingConfiguration;
        $validationResults = $eInvoicingConfig->validateInvoiceData($this->toArray());

        $this->update([
            'validation_results' => $validationResults,
            'compliance_status' => $validationResults['valid'] ? 'compliant' : 'non_compliant',
        ]);

        return $validationResults;
    }

    /**
     * إرسال الفاتورة
     */
    public function submitInvoice(): array
    {
        if ($this->compliance_status !== 'compliant') {
            throw new \Exception('الفاتورة غير متوافقة ولا يمكن إرسالها');
        }

        $this->update(['submission_status' => 'submitting']);

        try {
            $eInvoicingConfig = $this->eInvoicingConfiguration;
            $response = $eInvoicingConfig->submitInvoice($this->prepareSubmissionData());

            if ($response['success']) {
                $this->update([
                    'submission_status' => 'submitted',
                    'submission_reference' => $response['submission_id'],
                    'government_response' => $response,
                    'submitted_at' => now(),
                ]);

                // معالجة التخليص إذا كان مطلوباً
                if ($this->requiresClearance()) {
                    $this->processClearance($response);
                }
            } else {
                $this->update([
                    'submission_status' => 'failed',
                    'error_messages' => $response['errors'] ?? [$response['error']],
                ]);
            }

            $this->logSubmission($response);
            return $response;

        } catch (\Exception $e) {
            $this->update([
                'submission_status' => 'failed',
                'error_messages' => [$e->getMessage()],
            ]);

            $this->logSubmission(['success' => false, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * تحضير بيانات الإرسال
     */
    protected function prepareSubmissionData(): array
    {
        return [
            'invoice_number' => $this->invoice_number,
            'uuid' => $this->invoice_uuid,
            'invoice_hash' => $this->invoice_hash,
            'issue_date' => $this->issue_date->format('Y-m-d'),
            'issue_time' => $this->issue_time->format('H:i:s'),
            'invoice_type' => $this->invoice_type,
            'document_type' => $this->document_type,
            'currency' => $this->currency,
            'seller_info' => $this->seller_info,
            'buyer_info' => $this->buyer_info,
            'line_items' => $this->line_items,
            'tax_breakdown' => $this->tax_breakdown,
            'totals' => $this->totals,
            'qr_code' => $this->qr_code,
            'xml_content' => $this->xml_content,
            'json_content' => $this->json_content,
            'digital_signature' => $this->digital_signature,
        ];
    }

    /**
     * التحقق من الحاجة للتخليص
     */
    protected function requiresClearance(): bool
    {
        $countryCode = $this->country->code;
        
        return match ($countryCode) {
            'SA' => $this->invoice_type === 'standard', // فقط الفواتير العادية تحتاج تخليص
            'EG' => true, // جميع الفواتير تحتاج تخليص في مصر
            default => false,
        };
    }

    /**
     * معالجة التخليص
     */
    protected function processClearance(array $submissionResponse): void
    {
        $this->update(['clearance_status' => 'pending_clearance']);

        // محاكاة عملية التخليص
        $clearanceResponse = [
            'success' => true,
            'clearance_reference' => 'CLR_' . strtoupper(uniqid()),
            'status' => 'cleared',
            'cleared_at' => now(),
        ];

        if ($clearanceResponse['success']) {
            $this->update([
                'clearance_status' => 'cleared',
                'clearance_reference' => $clearanceResponse['clearance_reference'],
                'cleared_at' => $clearanceResponse['cleared_at'],
            ]);
        } else {
            $this->update([
                'clearance_status' => 'clearance_failed',
                'error_messages' => array_merge($this->error_messages ?? [], [$clearanceResponse['error']]),
            ]);
        }
    }

    /**
     * تسجيل عملية الإرسال
     */
    protected function logSubmission(array $response): void
    {
        EInvoiceSubmissionLog::create([
            'electronic_invoice_id' => $this->id,
            'e_invoicing_configuration_id' => $this->e_invoicing_configuration_id,
            'submission_data' => $this->prepareSubmissionData(),
            'response_data' => $response,
            'status' => $response['success'] ? 'success' : 'failed',
            'submission_reference' => $response['submission_id'] ?? null,
            'error_message' => $response['error'] ?? null,
            'submitted_at' => now(),
        ]);
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    public function scopePendingSubmission($query)
    {
        return $query->where('compliance_status', 'compliant')
                    ->where('submission_status', 'ready_to_submit');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('submission_status', 'submitted');
    }

    public function scopeCleared($query)
    {
        return $query->where('clearance_status', 'cleared');
    }

    public function scopeFailed($query)
    {
        return $query->whereIn('submission_status', ['failed', 'rejected']);
    }

    // طرق مساعدة إضافية
    protected function generateEgyptianContent(): void
    {
        // منطق توليد المحتوى المصري
        $this->generateGenericContent();
    }

    protected function generateUAEContent(): void
    {
        // منطق توليد المحتوى الإماراتي
        $this->generateGenericContent();
    }

    protected function generateMoroccanContent(): void
    {
        // منطق توليد المحتوى المغربي
        $this->generateGenericContent();
    }

    protected function generateGenericContent(): void
    {
        $jsonContent = json_encode([
            'invoice_number' => $this->invoice_number,
            'issue_date' => $this->issue_date->format('Y-m-d'),
            'seller' => $this->seller_info,
            'buyer' => $this->buyer_info,
            'line_items' => $this->line_items,
            'totals' => $this->totals,
        ], JSON_UNESCAPED_UNICODE);

        $this->update(['json_content' => $jsonContent]);
    }

    protected function generateEgyptianQRData(): string
    {
        return json_encode([
            'invoice_number' => $this->invoice_number,
            'total_amount' => $this->total_amount,
            'tax_amount' => $this->tax_amount,
        ]);
    }

    protected function generateUAEQRData(): string
    {
        return json_encode([
            'invoice_number' => $this->invoice_number,
            'total_amount' => $this->total_amount,
            'tax_amount' => $this->tax_amount,
        ]);
    }

    protected function generateGenericQRData(): string
    {
        return json_encode([
            'invoice_number' => $this->invoice_number,
            'total_amount' => $this->total_amount,
        ]);
    }
}
