<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\TeamFeedback;
use App\Domains\Projects\Models\ProjectSurvey;
use App\Domains\Projects\Models\Recognition;
use App\Domains\Projects\Models\PostProjectReview;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التقييم والمشاركة - Team Engagement & Feedback Service
 * تدير تقييم الأداء والمشاركة والاعتراف بالفريق
 */
class TeamEngagementService
{
    protected array $recognitionTypes = [
        'STAR' => 'نجمة',
        'THANKS' => 'شكر',
        'EXCELLENCE' => 'تميز',
        'INNOVATION' => 'ابتكار',
        'COLLABORATION' => 'تعاون',
        'LEADERSHIP' => 'قيادة',
        'PROBLEM_SOLVING' => 'حل المشاكل',
        'DEDICATION' => 'تفاني',
    ];

    protected array $surveyTypes = [
        'PROJECT_SATISFACTION',
        'TEAM_COLLABORATION',
        'PROCESS_EFFECTIVENESS',
        'TOOL_SATISFACTION',
        'WORKLOAD_ASSESSMENT',
        'COMMUNICATION_QUALITY',
    ];

    /**
     * إنشاء مراجعة ما بعد المشروع
     */
    public function createPostProjectReview(
        int $projectId,
        int $reviewerId,
        array $reviewData
    ): PostProjectReview {
        try {
            DB::beginTransaction();

            $project = Project::findOrFail($projectId);

            $review = PostProjectReview::create([
                'project_id' => $projectId,
                'reviewer_id' => $reviewerId,
                'overall_rating' => $reviewData['overall_rating'],
                'objectives_met' => $reviewData['objectives_met'],
                'timeline_performance' => $reviewData['timeline_performance'],
                'budget_performance' => $reviewData['budget_performance'],
                'quality_rating' => $reviewData['quality_rating'],
                'team_performance' => $reviewData['team_performance'],
                'communication_rating' => $reviewData['communication_rating'],
                'stakeholder_satisfaction' => $reviewData['stakeholder_satisfaction'],
                'what_went_well' => $reviewData['what_went_well'] ?? [],
                'what_could_improve' => $reviewData['what_could_improve'] ?? [],
                'lessons_learned' => $reviewData['lessons_learned'] ?? [],
                'recommendations' => $reviewData['recommendations'] ?? [],
                'success_factors' => $reviewData['success_factors'] ?? [],
                'challenges_faced' => $reviewData['challenges_faced'] ?? [],
                'process_improvements' => $reviewData['process_improvements'] ?? [],
                'tool_effectiveness' => $reviewData['tool_effectiveness'] ?? [],
                'team_feedback' => $reviewData['team_feedback'] ?? [],
                'client_feedback' => $reviewData['client_feedback'] ?? null,
                'would_recommend_approach' => $reviewData['would_recommend_approach'] ?? true,
                'metadata' => $reviewData['metadata'] ?? [],
            ]);

            // إنشاء استبيان للفريق
            if ($reviewData['create_team_survey'] ?? true) {
                $this->createTeamSurvey($project, $reviewerId);
            }

            // تحديث إحصائيات المشروع
            $this->updateProjectStatistics($project, $review);

            DB::commit();

            return $review;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في إنشاء مراجعة ما بعد المشروع', [
                'project_id' => $projectId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * إنشاء استبيان للفريق
     */
    public function createTeamSurvey(
        Project $project,
        int $createdBy,
        string $type = 'PROJECT_SATISFACTION'
    ): ProjectSurvey {
        $survey = ProjectSurvey::create([
            'project_id' => $project->id,
            'title' => $this->getSurveyTitle($type),
            'description' => $this->getSurveyDescription($type),
            'type' => $type,
            'questions' => $this->getSurveyQuestions($type),
            'is_anonymous' => true,
            'is_active' => true,
            'expires_at' => now()->addDays(7),
            'created_by' => $createdBy,
        ]);

        // إرسال دعوات للفريق
        $this->sendSurveyInvitations($survey);

        return $survey;
    }

    /**
     * تقديم إجابات الاستبيان
     */
    public function submitSurveyResponse(
        int $surveyId,
        int $respondentId,
        array $responses
    ): array {
        $survey = ProjectSurvey::findOrFail($surveyId);

        // التحقق من عدم الإجابة مسبقاً
        if ($survey->responses()->where('respondent_id', $respondentId)->exists()) {
            throw new \InvalidArgumentException('تم الإجابة على هذا الاستبيان مسبقاً');
        }

        $response = $survey->responses()->create([
            'respondent_id' => $respondentId,
            'responses' => $responses,
            'submitted_at' => now(),
            'ip_address' => request()->ip(),
        ]);

        // تحليل الإجابات
        $analysis = $this->analyzeSurveyResponse($survey, $responses);

        return [
            'response' => $response,
            'analysis' => $analysis,
            'completion_rate' => $this->calculateCompletionRate($survey),
        ];
    }

    /**
     * منح اعتراف لعضو فريق
     */
    public function giveRecognition(
        int $projectId,
        int $giverId,
        int $recipientId,
        string $type,
        string $message = null,
        array $metadata = []
    ): Recognition {
        $recognition = Recognition::create([
            'project_id' => $projectId,
            'giver_id' => $giverId,
            'recipient_id' => $recipientId,
            'type' => $type,
            'message' => $message,
            'points' => $this->getRecognitionPoints($type),
            'is_public' => $metadata['is_public'] ?? true,
            'metadata' => $metadata,
        ]);

        // تحديث نقاط المستلم
        $this->updateRecipientPoints($recipientId, $recognition->points);

        // إشعار المستلم
        $this->notifyRecognitionRecipient($recognition);

        // نشر في تغذية المشروع إذا كان عام
        if ($recognition->is_public) {
            $this->publishRecognitionToFeed($recognition);
        }

        return $recognition;
    }

    /**
     * الحصول على إحصائيات المشاركة
     */
    public function getEngagementStatistics(int $projectId): array
    {
        $project = Project::with(['teamMembers', 'recognitions', 'surveys'])->findOrFail($projectId);

        return [
            'team_size' => $project->teamMembers->count(),
            'recognition_stats' => $this->getRecognitionStatistics($project),
            'survey_participation' => $this->getSurveyParticipationStats($project),
            'feedback_metrics' => $this->getFeedbackMetrics($project),
            'engagement_score' => $this->calculateEngagementScore($project),
            'satisfaction_trends' => $this->getSatisfactionTrends($project),
            'collaboration_index' => $this->calculateCollaborationIndex($project),
            'retention_indicators' => $this->getRetentionIndicators($project),
        ];
    }

    /**
     * تحليل رضا الفريق
     */
    public function analyzeTeamSatisfaction(int $projectId): array
    {
        $project = Project::findOrFail($projectId);
        $surveys = $project->surveys()->where('type', 'PROJECT_SATISFACTION')->get();

        $satisfactionData = [];
        foreach ($surveys as $survey) {
            $responses = $survey->responses()->with('respondent')->get();
            $satisfactionData[] = $this->analyzeSurveyResults($survey, $responses);
        }

        return [
            'overall_satisfaction' => $this->calculateOverallSatisfaction($satisfactionData),
            'satisfaction_by_category' => $this->categorizeSatisfaction($satisfactionData),
            'trends' => $this->identifySatisfactionTrends($satisfactionData),
            'improvement_areas' => $this->identifyImprovementAreas($satisfactionData),
            'strengths' => $this->identifyStrengths($satisfactionData),
            'recommendations' => $this->generateSatisfactionRecommendations($satisfactionData),
        ];
    }

    /**
     * إنشاء تقرير المشاركة
     */
    public function generateEngagementReport(int $projectId): array
    {
        $project = Project::with([
            'teamMembers',
            'recognitions',
            'surveys',
            'postProjectReviews'
        ])->findOrFail($projectId);

        return [
            'project_overview' => [
                'name' => $project->name,
                'team_size' => $project->teamMembers->count(),
                'duration' => $project->start_date->diffInDays($project->end_date ?? now()),
                'status' => $project->status,
            ],
            'engagement_metrics' => $this->getEngagementStatistics($projectId),
            'recognition_analysis' => $this->analyzeRecognitionPatterns($project),
            'feedback_summary' => $this->summarizeFeedback($project),
            'satisfaction_analysis' => $this->analyzeTeamSatisfaction($projectId),
            'collaboration_insights' => $this->analyzeCollaboration($project),
            'improvement_recommendations' => $this->generateImprovementRecommendations($project),
            'best_practices' => $this->identifyBestPractices($project),
            'lessons_learned' => $this->extractLessonsLearned($project),
        ];
    }

    /**
     * إنشاء خطة تحسين المشاركة
     */
    public function createEngagementImprovementPlan(
        int $projectId,
        array $analysisResults
    ): array {
        $improvementPlan = [
            'objectives' => $this->defineImprovementObjectives($analysisResults),
            'action_items' => $this->createActionItems($analysisResults),
            'timeline' => $this->createImplementationTimeline($analysisResults),
            'success_metrics' => $this->defineSuccessMetrics($analysisResults),
            'responsible_parties' => $this->assignResponsibilities($projectId, $analysisResults),
            'resources_needed' => $this->identifyResourceNeeds($analysisResults),
            'monitoring_plan' => $this->createMonitoringPlan($analysisResults),
        ];

        return $improvementPlan;
    }

    // دوال مساعدة
    protected function getSurveyTitle(string $type): string
    {
        return match ($type) {
            'PROJECT_SATISFACTION' => 'استبيان رضا المشروع',
            'TEAM_COLLABORATION' => 'استبيان التعاون الجماعي',
            'PROCESS_EFFECTIVENESS' => 'استبيان فعالية العمليات',
            'TOOL_SATISFACTION' => 'استبيان رضا الأدوات',
            'WORKLOAD_ASSESSMENT' => 'تقييم عبء العمل',
            'COMMUNICATION_QUALITY' => 'جودة التواصل',
            default => 'استبيان المشروع',
        };
    }

    protected function getSurveyDescription(string $type): string
    {
        return match ($type) {
            'PROJECT_SATISFACTION' => 'يهدف هذا الاستبيان لقياس مستوى رضاكم عن المشروع وتجربة العمل',
            'TEAM_COLLABORATION' => 'تقييم مستوى التعاون والتنسيق بين أعضاء الفريق',
            'PROCESS_EFFECTIVENESS' => 'تقييم فعالية العمليات والإجراءات المتبعة في المشروع',
            default => 'استبيان لتحسين تجربة العمل في المشروع',
        };
    }

    protected function getSurveyQuestions(string $type): array
    {
        return match ($type) {
            'PROJECT_SATISFACTION' => [
                ['question' => 'ما مدى رضاك عن المشروع بشكل عام؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'هل تشعر أن أهداف المشروع واضحة؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'ما مدى رضاك عن التواصل في الفريق؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'هل تشعر أن عبء العمل مناسب؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'ما أكثر شيء أعجبك في المشروع؟', 'type' => 'text'],
                ['question' => 'ما الذي يمكن تحسينه؟', 'type' => 'text'],
            ],
            'TEAM_COLLABORATION' => [
                ['question' => 'مدى فعالية التعاون مع زملائك؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'هل تحصل على الدعم المطلوب من الفريق؟', 'type' => 'rating', 'scale' => 5],
                ['question' => 'مدى وضوح الأدوار والمسؤوليات؟', 'type' => 'rating', 'scale' => 5],
            ],
            default => [
                ['question' => 'تقييم عام للمشروع', 'type' => 'rating', 'scale' => 5],
                ['question' => 'تعليقات إضافية', 'type' => 'text'],
            ],
        };
    }

    protected function getRecognitionPoints(string $type): int
    {
        return match ($type) {
            'STAR' => 10,
            'THANKS' => 5,
            'EXCELLENCE' => 25,
            'INNOVATION' => 20,
            'COLLABORATION' => 15,
            'LEADERSHIP' => 20,
            'PROBLEM_SOLVING' => 15,
            'DEDICATION' => 15,
            default => 5,
        };
    }

    protected function sendSurveyInvitations(ProjectSurvey $survey): void
    {
        foreach ($survey->project->teamMembers as $member) {
            $member->notify(new \App\Notifications\SurveyInvitationNotification($survey));
        }
    }

    protected function analyzeSurveyResponse(ProjectSurvey $survey, array $responses): array
    {
        // تحليل الإجابات وإرجاع النتائج
        return [
            'satisfaction_score' => $this->calculateSatisfactionScore($responses),
            'key_insights' => $this->extractKeyInsights($responses),
            'areas_of_concern' => $this->identifyAreasOfConcern($responses),
        ];
    }

    protected function calculateCompletionRate(ProjectSurvey $survey): float
    {
        $totalMembers = $survey->project->teamMembers->count();
        $responses = $survey->responses()->count();
        
        return $totalMembers > 0 ? ($responses / $totalMembers) * 100 : 0;
    }

    protected function updateRecipientPoints(int $recipientId, int $points): void
    {
        // تحديث نقاط الاعتراف للموظف
        $employee = \App\Domains\HR\Models\Employee::find($recipientId);
        if ($employee) {
            $employee->increment('recognition_points', $points);
        }
    }

    protected function notifyRecognitionRecipient(Recognition $recognition): void
    {
        $recognition->recipient->notify(
            new \App\Notifications\RecognitionReceivedNotification($recognition)
        );
    }

    protected function publishRecognitionToFeed(Recognition $recognition): void
    {
        // نشر في تغذية المشروع
        $recognition->project->activities()->create([
            'type' => 'RECOGNITION_GIVEN',
            'description' => "{$recognition->giver->name} منح {$recognition->recipient->name} {$this->recognitionTypes[$recognition->type]}",
            'data' => [
                'recognition_id' => $recognition->id,
                'type' => $recognition->type,
                'message' => $recognition->message,
            ],
        ]);
    }

    // دوال إضافية للتحليل والإحصائيات
    protected function getRecognitionStatistics(Project $project): array { return []; }
    protected function getSurveyParticipationStats(Project $project): array { return []; }
    protected function getFeedbackMetrics(Project $project): array { return []; }
    protected function calculateEngagementScore(Project $project): float { return 0; }
    protected function getSatisfactionTrends(Project $project): array { return []; }
    protected function calculateCollaborationIndex(Project $project): float { return 0; }
    protected function getRetentionIndicators(Project $project): array { return []; }
    protected function updateProjectStatistics(Project $project, PostProjectReview $review): void { /* تحديث الإحصائيات */ }
    protected function calculateSatisfactionScore(array $responses): float { return 0; }
    protected function extractKeyInsights(array $responses): array { return []; }
    protected function identifyAreasOfConcern(array $responses): array { return []; }
}
