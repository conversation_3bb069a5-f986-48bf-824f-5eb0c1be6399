<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceProduct;
use App\Domains\ECommerce\Models\ECommerceOrder;
use App\Domains\ECommerce\Models\ECommerceCustomer;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use App\Domains\ECommerce\Contracts\ECommercePlatformInterface;
use App\Domains\ECommerce\Exceptions\ECommerceSyncException;
use App\Domains\ECommerce\Events\ProductSynced;
use App\Domains\ECommerce\Events\OrderSynced;
use App\Domains\ECommerce\Events\CustomerSynced;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة مزامنة البيانات مع منصات التجارة الإلكترونية
 * تدير عمليات المزامنة للمنتجات والطلبات والعملاء
 */
class ECommerceSyncService
{
    protected ECommerceDataTransformerService $transformer;
    protected ECommerceValidationService $validator;
    protected ECommerceMappingService $mapper;

    public function __construct(
        ECommerceDataTransformerService $transformer,
        ECommerceValidationService $validator,
        ECommerceMappingService $mapper
    ) {
        $this->transformer = $transformer;
        $this->validator = $validator;
        $this->mapper = $mapper;
    }

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver,
        array $options = []
    ): array {
        $startTime = now();
        $stats = [
            'total' => 0,
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'skipped' => 0,
            'created' => 0,
            'updated' => 0,
            'deleted' => 0,
            'errors' => [],
            'warnings' => [],
        ];

        try {
            Log::info('Starting product sync', [
                'integration_id' => $integration->id,
                'options' => $options,
            ]);

            // جلب المنتجات من المنصة
            $externalProducts = $driver->getProducts($integration, $options);
            $stats['total'] = count($externalProducts);

            foreach ($externalProducts as $externalProduct) {
                try {
                    $stats['processed']++;
                    
                    // تحويل البيانات
                    $transformedData = $this->transformer->transformProduct(
                        $externalProduct,
                        $integration
                    );

                    // التحقق من صحة البيانات
                    $validationResult = $this->validator->validateProduct($transformedData);
                    
                    if (!$validationResult['valid']) {
                        $stats['failed']++;
                        $stats['errors'][] = [
                            'external_id' => $externalProduct['id'] ?? 'unknown',
                            'type' => 'validation_error',
                            'message' => 'Product validation failed',
                            'details' => $validationResult['errors'],
                        ];
                        continue;
                    }

                    // تطبيق قواعد العمل
                    if (!$this->shouldSyncProduct($transformedData, $integration)) {
                        $stats['skipped']++;
                        continue;
                    }

                    // البحث عن المنتج الموجود
                    $existingProduct = ECommerceProduct::where([
                        'integration_id' => $integration->id,
                        'external_id' => $transformedData['external_id'],
                    ])->first();

                    if ($existingProduct) {
                        // تحديث المنتج الموجود
                        $this->updateProduct($existingProduct, $transformedData);
                        $stats['updated']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new ProductSynced($existingProduct, 'updated', $integration));
                    } else {
                        // إنشاء منتج جديد
                        $newProduct = $this->createProduct($transformedData, $integration);
                        $stats['created']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new ProductSynced($newProduct, 'created', $integration));
                    }

                } catch (\Exception $e) {
                    $stats['failed']++;
                    $stats['errors'][] = [
                        'external_id' => $externalProduct['id'] ?? 'unknown',
                        'type' => 'processing_error',
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ];

                    Log::error('Product sync error', [
                        'integration_id' => $integration->id,
                        'external_id' => $externalProduct['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // معالجة المنتجات المحذوفة
            if ($options['handle_deletions'] ?? false) {
                $deletionStats = $this->handleProductDeletions($integration, $externalProducts);
                $stats['deleted'] = $deletionStats['deleted'];
            }

            $stats['success'] = $stats['failed'] === 0;
            $stats['duration'] = now()->diffInSeconds($startTime);

            Log::info('Product sync completed', [
                'integration_id' => $integration->id,
                'stats' => $stats,
            ]);

            return $stats;

        } catch (\Exception $e) {
            Log::error('Product sync failed', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage(),
            ]);

            throw new ECommerceSyncException(
                'Product sync failed: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver,
        array $options = []
    ): array {
        $startTime = now();
        $stats = [
            'total' => 0,
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'skipped' => 0,
            'created' => 0,
            'updated' => 0,
            'deleted' => 0,
            'errors' => [],
            'warnings' => [],
        ];

        try {
            Log::info('Starting order sync', [
                'integration_id' => $integration->id,
                'options' => $options,
            ]);

            // جلب الطلبات من المنصة
            $externalOrders = $driver->getOrders($integration, $options);
            $stats['total'] = count($externalOrders);

            foreach ($externalOrders as $externalOrder) {
                try {
                    $stats['processed']++;
                    
                    // تحويل البيانات
                    $transformedData = $this->transformer->transformOrder(
                        $externalOrder,
                        $integration
                    );

                    // التحقق من صحة البيانات
                    $validationResult = $this->validator->validateOrder($transformedData);
                    
                    if (!$validationResult['valid']) {
                        $stats['failed']++;
                        $stats['errors'][] = [
                            'external_id' => $externalOrder['id'] ?? 'unknown',
                            'type' => 'validation_error',
                            'message' => 'Order validation failed',
                            'details' => $validationResult['errors'],
                        ];
                        continue;
                    }

                    // تطبيق قواعد العمل
                    if (!$this->shouldSyncOrder($transformedData, $integration)) {
                        $stats['skipped']++;
                        continue;
                    }

                    // البحث عن الطلب الموجود
                    $existingOrder = ECommerceOrder::where([
                        'integration_id' => $integration->id,
                        'external_id' => $transformedData['external_id'],
                    ])->first();

                    if ($existingOrder) {
                        // تحديث الطلب الموجود
                        $this->updateOrder($existingOrder, $transformedData, $integration);
                        $stats['updated']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new OrderSynced($existingOrder, 'updated', $integration));
                    } else {
                        // إنشاء طلب جديد
                        $newOrder = $this->createOrder($transformedData, $integration);
                        $stats['created']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new OrderSynced($newOrder, 'created', $integration));
                    }

                } catch (\Exception $e) {
                    $stats['failed']++;
                    $stats['errors'][] = [
                        'external_id' => $externalOrder['id'] ?? 'unknown',
                        'type' => 'processing_error',
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ];

                    Log::error('Order sync error', [
                        'integration_id' => $integration->id,
                        'external_id' => $externalOrder['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $stats['success'] = $stats['failed'] === 0;
            $stats['duration'] = now()->diffInSeconds($startTime);

            Log::info('Order sync completed', [
                'integration_id' => $integration->id,
                'stats' => $stats,
            ]);

            return $stats;

        } catch (\Exception $e) {
            Log::error('Order sync failed', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage(),
            ]);

            throw new ECommerceSyncException(
                'Order sync failed: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(
        ECommerceIntegration $integration,
        ECommercePlatformInterface $driver,
        array $options = []
    ): array {
        $startTime = now();
        $stats = [
            'total' => 0,
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'skipped' => 0,
            'created' => 0,
            'updated' => 0,
            'deleted' => 0,
            'errors' => [],
            'warnings' => [],
        ];

        try {
            Log::info('Starting customer sync', [
                'integration_id' => $integration->id,
                'options' => $options,
            ]);

            // جلب العملاء من المنصة
            $externalCustomers = $driver->getCustomers($integration, $options);
            $stats['total'] = count($externalCustomers);

            foreach ($externalCustomers as $externalCustomer) {
                try {
                    $stats['processed']++;
                    
                    // تحويل البيانات
                    $transformedData = $this->transformer->transformCustomer(
                        $externalCustomer,
                        $integration
                    );

                    // التحقق من صحة البيانات
                    $validationResult = $this->validator->validateCustomer($transformedData);
                    
                    if (!$validationResult['valid']) {
                        $stats['failed']++;
                        $stats['errors'][] = [
                            'external_id' => $externalCustomer['id'] ?? 'unknown',
                            'type' => 'validation_error',
                            'message' => 'Customer validation failed',
                            'details' => $validationResult['errors'],
                        ];
                        continue;
                    }

                    // تطبيق قواعد العمل
                    if (!$this->shouldSyncCustomer($transformedData, $integration)) {
                        $stats['skipped']++;
                        continue;
                    }

                    // البحث عن العميل الموجود
                    $existingCustomer = ECommerceCustomer::where([
                        'integration_id' => $integration->id,
                        'external_id' => $transformedData['external_id'],
                    ])->first();

                    if ($existingCustomer) {
                        // تحديث العميل الموجود
                        $this->updateCustomer($existingCustomer, $transformedData);
                        $stats['updated']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new CustomerSynced($existingCustomer, 'updated', $integration));
                    } else {
                        // إنشاء عميل جديد
                        $newCustomer = $this->createCustomer($transformedData, $integration);
                        $stats['created']++;
                        $stats['successful']++;
                        
                        Event::dispatch(new CustomerSynced($newCustomer, 'created', $integration));
                    }

                } catch (\Exception $e) {
                    $stats['failed']++;
                    $stats['errors'][] = [
                        'external_id' => $externalCustomer['id'] ?? 'unknown',
                        'type' => 'processing_error',
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ];

                    Log::error('Customer sync error', [
                        'integration_id' => $integration->id,
                        'external_id' => $externalCustomer['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $stats['success'] = $stats['failed'] === 0;
            $stats['duration'] = now()->diffInSeconds($startTime);

            Log::info('Customer sync completed', [
                'integration_id' => $integration->id,
                'stats' => $stats,
            ]);

            return $stats;

        } catch (\Exception $e) {
            Log::error('Customer sync failed', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage(),
            ]);

            throw new ECommerceSyncException(
                'Customer sync failed: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * إنشاء منتج جديد
     */
    protected function createProduct(array $data, ECommerceIntegration $integration): ECommerceProduct
    {
        $data['integration_id'] = $integration->id;
        $data['store_id'] = $integration->store_id;
        $data['platform_id'] = $integration->platform_id;
        $data['company_id'] = $integration->company_id;
        $data['is_synced'] = true;
        $data['last_synced_at'] = now();
        $data['created_by'] = auth()->id();

        return ECommerceProduct::create($data);
    }

    /**
     * تحديث منتج موجود
     */
    protected function updateProduct(ECommerceProduct $product, array $data): void
    {
        $data['is_synced'] = true;
        $data['last_synced_at'] = now();
        $data['updated_by'] = auth()->id();

        $product->update($data);
    }

    /**
     * إنشاء طلب جديد
     */
    protected function createOrder(array $data, ECommerceIntegration $integration): ECommerceOrder
    {
        DB::beginTransaction();
        
        try {
            $data['integration_id'] = $integration->id;
            $data['store_id'] = $integration->store_id;
            $data['platform_id'] = $integration->platform_id;
            $data['company_id'] = $integration->company_id;
            $data['is_synced'] = true;
            $data['last_synced_at'] = now();
            $data['created_by'] = auth()->id();

            // إنشاء الطلب
            $order = ECommerceOrder::create($data);

            // إنشاء عناصر الطلب
            if (isset($data['line_items']) && is_array($data['line_items'])) {
                $this->createOrderItems($order, $data['line_items'], $integration);
            }

            DB::commit();
            
            return $order;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * تحديث طلب موجود
     */
    protected function updateOrder(
        ECommerceOrder $order,
        array $data,
        ECommerceIntegration $integration
    ): void {
        DB::beginTransaction();
        
        try {
            $data['is_synced'] = true;
            $data['last_synced_at'] = now();
            $data['updated_by'] = auth()->id();

            // تحديث الطلب
            $order->update($data);

            // تحديث عناصر الطلب
            if (isset($data['line_items']) && is_array($data['line_items'])) {
                $this->updateOrderItems($order, $data['line_items'], $integration);
            }

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * إنشاء عميل جديد
     */
    protected function createCustomer(array $data, ECommerceIntegration $integration): ECommerceCustomer
    {
        $data['integration_id'] = $integration->id;
        $data['store_id'] = $integration->store_id;
        $data['platform_id'] = $integration->platform_id;
        $data['company_id'] = $integration->company_id;
        $data['is_synced'] = true;
        $data['last_synced_at'] = now();
        $data['created_by'] = auth()->id();

        return ECommerceCustomer::create($data);
    }

    /**
     * تحديث عميل موجود
     */
    protected function updateCustomer(ECommerceCustomer $customer, array $data): void
    {
        $data['is_synced'] = true;
        $data['last_synced_at'] = now();
        $data['updated_by'] = auth()->id();

        $customer->update($data);
    }

    /**
     * إنشاء عناصر الطلب
     */
    protected function createOrderItems(
        ECommerceOrder $order,
        array $lineItems,
        ECommerceIntegration $integration
    ): void {
        foreach ($lineItems as $item) {
            $itemData = $this->transformer->transformOrderItem($item, $integration);
            $itemData['order_id'] = $order->id;
            $itemData['integration_id'] = $integration->id;
            $itemData['store_id'] = $integration->store_id;
            $itemData['platform_id'] = $integration->platform_id;
            $itemData['company_id'] = $integration->company_id;
            $itemData['is_synced'] = true;
            $itemData['last_synced_at'] = now();
            $itemData['created_by'] = auth()->id();

            $order->items()->create($itemData);
        }
    }

    /**
     * تحديث عناصر الطلب
     */
    protected function updateOrderItems(
        ECommerceOrder $order,
        array $lineItems,
        ECommerceIntegration $integration
    ): void {
        // حذف العناصر الموجودة
        $order->items()->delete();

        // إنشاء العناصر الجديدة
        $this->createOrderItems($order, $lineItems, $integration);
    }

    /**
     * معالجة حذف المنتجات
     */
    protected function handleProductDeletions(
        ECommerceIntegration $integration,
        array $externalProducts
    ): array {
        $externalIds = collect($externalProducts)->pluck('id')->toArray();
        
        $deletedCount = ECommerceProduct::where('integration_id', $integration->id)
            ->whereNotIn('external_id', $externalIds)
            ->delete();

        return ['deleted' => $deletedCount];
    }

    /**
     * تحديد ما إذا كان يجب مزامنة المنتج
     */
    protected function shouldSyncProduct(array $data, ECommerceIntegration $integration): bool
    {
        $rules = $integration->sync_rules['products'] ?? [];
        
        // تطبيق قواعد التصفية
        if (isset($rules['filters'])) {
            foreach ($rules['filters'] as $filter) {
                if (!$this->applyFilter($data, $filter)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * تحديد ما إذا كان يجب مزامنة الطلب
     */
    protected function shouldSyncOrder(array $data, ECommerceIntegration $integration): bool
    {
        $rules = $integration->sync_rules['orders'] ?? [];
        
        // تطبيق قواعد التصفية
        if (isset($rules['filters'])) {
            foreach ($rules['filters'] as $filter) {
                if (!$this->applyFilter($data, $filter)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * تحديد ما إذا كان يجب مزامنة العميل
     */
    protected function shouldSyncCustomer(array $data, ECommerceIntegration $integration): bool
    {
        $rules = $integration->sync_rules['customers'] ?? [];
        
        // تطبيق قواعد التصفية
        if (isset($rules['filters'])) {
            foreach ($rules['filters'] as $filter) {
                if (!$this->applyFilter($data, $filter)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * تطبيق مرشح
     */
    protected function applyFilter(array $data, array $filter): bool
    {
        $field = $filter['field'] ?? null;
        $operator = $filter['operator'] ?? '=';
        $value = $filter['value'] ?? null;

        if (!$field || !isset($data[$field])) {
            return true;
        }

        $fieldValue = $data[$field];

        switch ($operator) {
            case '=':
                return $fieldValue == $value;
            case '!=':
                return $fieldValue != $value;
            case '>':
                return $fieldValue > $value;
            case '>=':
                return $fieldValue >= $value;
            case '<':
                return $fieldValue < $value;
            case '<=':
                return $fieldValue <= $value;
            case 'in':
                return in_array($fieldValue, (array) $value);
            case 'not_in':
                return !in_array($fieldValue, (array) $value);
            case 'contains':
                return str_contains($fieldValue, $value);
            case 'starts_with':
                return str_starts_with($fieldValue, $value);
            case 'ends_with':
                return str_ends_with($fieldValue, $value);
            default:
                return true;
        }
    }
}
