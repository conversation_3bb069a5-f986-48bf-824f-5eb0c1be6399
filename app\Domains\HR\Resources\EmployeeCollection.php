<?php

namespace App\Domains\HR\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * مجموعة موارد الموظفين
 * تنسيق مجموعة الموظفين مع إحصائيات إضافية
 */
class EmployeeCollection extends ResourceCollection
{
    /**
     * تحويل مجموعة الموارد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($employee) use ($request) {
                return [
                    'id' => $employee->id,
                    'uuid' => $employee->uuid,
                    'employee_number' => $employee->employee_number,
                    'full_name' => $employee->full_name,
                    'full_name_ar' => $employee->full_name_ar,
                    'email' => $employee->email,
                    'mobile' => $employee->mobile,
                    'profile_picture' => $employee->profile_picture ? asset('storage/' . $employee->profile_picture) : null,
                    'avatar_url' => $employee->profile_picture ? asset('storage/' . $employee->profile_picture) : $this->getDefaultAvatar($employee->gender),
                    
                    // Employment Info
                    'department' => $employee->department ? [
                        'id' => $employee->department->id,
                        'name' => $employee->department->name,
                        'name_en' => $employee->department->name_en,
                    ] : null,
                    'position' => $employee->position ? [
                        'id' => $employee->position->id,
                        'title' => $employee->position->title,
                        'title_en' => $employee->position->title_en,
                        'level' => $employee->position->level,
                    ] : null,
                    'manager' => $employee->manager ? [
                        'id' => $employee->manager->id,
                        'name' => $employee->manager->full_name,
                        'employee_number' => $employee->manager->employee_number,
                    ] : null,
                    
                    // Status and Dates
                    'status' => $employee->status,
                    'status_label' => $employee->status ? \App\Domains\HR\Models\Employee::STATUSES[$employee->status] ?? $employee->status : null,
                    'status_color' => $this->getStatusColor($employee->status),
                    'contract_type' => $employee->contract_type,
                    'contract_type_label' => $employee->contract_type ? \App\Domains\HR\Models\Employee::CONTRACT_TYPES[$employee->contract_type] ?? $employee->contract_type : null,
                    'contract_type_color' => $this->getContractTypeColor($employee->contract_type),
                    'employment_type' => $employee->employment_type,
                    'employment_type_label' => $employee->employment_type ? \App\Domains\HR\Models\Employee::EMPLOYMENT_TYPES[$employee->employment_type] ?? $employee->employment_type : null,
                    'hire_date' => $employee->hire_date?->format('Y-m-d'),
                    'years_of_service' => $employee->years_of_service,
                    'years_of_service_formatted' => $this->getFormattedYearsOfService($employee->years_of_service),
                    'is_active' => $employee->is_active,
                    'is_probation_completed' => $employee->isProbationPeriodCompleted(),
                    
                    // Personal Info
                    'age' => $employee->age,
                    'gender' => $employee->gender,
                    'gender_label' => $employee->gender ? \App\Domains\HR\Models\Employee::GENDERS[$employee->gender] ?? $employee->gender : null,
                    'nationality' => $employee->nationality,
                    
                    // Financial Info
                    'basic_salary' => $employee->basic_salary,
                    'currency' => $employee->currency,
                    'formatted_salary' => $employee->currency . ' ' . number_format($employee->basic_salary, 2),
                    
                    // Document Expiry Warnings
                    'has_expiring_documents' => !empty($employee->getExpiringDocuments()),
                    'expiring_documents_count' => count($employee->getExpiringDocuments()),
                    
                    // Timestamps
                    'created_at' => $employee->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $employee->updated_at?->format('Y-m-d H:i:s'),
                    
                    // Permissions
                    'permissions' => [
                        'can_view' => $request->user()?->can('view', $employee),
                        'can_update' => $request->user()?->can('update', $employee),
                        'can_delete' => $request->user()?->can('delete', $employee),
                    ],
                ];
            }),
            
            // Collection Statistics
            'statistics' => $this->getCollectionStatistics(),
            
            // Aggregated Data
            'aggregations' => $this->getAggregatedData(),
        ];
    }

    /**
     * الحصول على إحصائيات المجموعة
     */
    protected function getCollectionStatistics(): array
    {
        $collection = $this->collection;
        
        return [
            'total_employees' => $collection->count(),
            'active_employees' => $collection->where('is_active', true)->count(),
            'inactive_employees' => $collection->where('is_active', false)->count(),
            'on_probation' => $collection->where('status', 'PROBATION')->count(),
            'terminated' => $collection->where('status', 'TERMINATED')->count(),
            'average_age' => round($collection->whereNotNull('date_of_birth')->avg('age'), 1),
            'average_years_of_service' => round($collection->avg('years_of_service'), 1),
            'total_salary_cost' => $collection->sum('basic_salary'),
            'average_salary' => round($collection->avg('basic_salary'), 2),
            'employees_with_expiring_docs' => $collection->filter(function ($employee) {
                return !empty($employee->getExpiringDocuments());
            })->count(),
        ];
    }

    /**
     * الحصول على البيانات المجمعة
     */
    protected function getAggregatedData(): array
    {
        $collection = $this->collection;
        
        return [
            'by_department' => $collection->groupBy('department.name')->map(function ($group, $department) {
                return [
                    'department' => $department ?: 'غير محدد',
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->values(),
            
            'by_position' => $collection->groupBy('position.title')->map(function ($group, $position) {
                return [
                    'position' => $position ?: 'غير محدد',
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->values()->take(10), // أعلى 10 مناصب
            
            'by_status' => $collection->groupBy('status')->map(function ($group, $status) {
                return [
                    'status' => $status,
                    'status_label' => \App\Domains\HR\Models\Employee::STATUSES[$status] ?? $status,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'color' => $this->getStatusColor($status),
                ];
            })->values(),
            
            'by_contract_type' => $collection->groupBy('contract_type')->map(function ($group, $contractType) {
                return [
                    'contract_type' => $contractType,
                    'contract_type_label' => \App\Domains\HR\Models\Employee::CONTRACT_TYPES[$contractType] ?? $contractType,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                    'color' => $this->getContractTypeColor($contractType),
                ];
            })->values(),
            
            'by_gender' => $collection->groupBy('gender')->map(function ($group, $gender) {
                return [
                    'gender' => $gender,
                    'gender_label' => \App\Domains\HR\Models\Employee::GENDERS[$gender] ?? $gender,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->values(),
            
            'by_nationality' => $collection->groupBy('nationality')->map(function ($group, $nationality) {
                return [
                    'nationality' => $nationality,
                    'count' => $group->count(),
                    'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
                ];
            })->sortByDesc('count')->values()->take(10), // أعلى 10 جنسيات
            
            'by_age_group' => $this->getAgeGroupDistribution($collection),
            
            'by_service_years' => $this->getServiceYearsDistribution($collection),
            
            'salary_ranges' => $this->getSalaryRangeDistribution($collection),
        ];
    }

    /**
     * الحصول على توزيع الفئات العمرية
     */
    protected function getAgeGroupDistribution($collection): array
    {
        $ageGroups = [
            '18-25' => $collection->whereBetween('age', [18, 25])->count(),
            '26-35' => $collection->whereBetween('age', [26, 35])->count(),
            '36-45' => $collection->whereBetween('age', [36, 45])->count(),
            '46-55' => $collection->whereBetween('age', [46, 55])->count(),
            '56-65' => $collection->whereBetween('age', [56, 65])->count(),
            '65+' => $collection->where('age', '>', 65)->count(),
        ];

        return collect($ageGroups)->map(function ($count, $range) {
            return [
                'age_range' => $range,
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على توزيع سنوات الخدمة
     */
    protected function getServiceYearsDistribution($collection): array
    {
        $serviceGroups = [
            '0-1' => $collection->whereBetween('years_of_service', [0, 1])->count(),
            '2-5' => $collection->whereBetween('years_of_service', [2, 5])->count(),
            '6-10' => $collection->whereBetween('years_of_service', [6, 10])->count(),
            '11-15' => $collection->whereBetween('years_of_service', [11, 15])->count(),
            '16-20' => $collection->whereBetween('years_of_service', [16, 20])->count(),
            '20+' => $collection->where('years_of_service', '>', 20)->count(),
        ];

        return collect($serviceGroups)->map(function ($count, $range) {
            return [
                'service_range' => $range . ' سنة',
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على توزيع نطاقات الرواتب
     */
    protected function getSalaryRangeDistribution($collection): array
    {
        $salaryRanges = [
            '0-5000' => $collection->whereBetween('basic_salary', [0, 5000])->count(),
            '5001-10000' => $collection->whereBetween('basic_salary', [5001, 10000])->count(),
            '10001-15000' => $collection->whereBetween('basic_salary', [10001, 15000])->count(),
            '15001-20000' => $collection->whereBetween('basic_salary', [15001, 20000])->count(),
            '20001-30000' => $collection->whereBetween('basic_salary', [20001, 30000])->count(),
            '30000+' => $collection->where('basic_salary', '>', 30000)->count(),
        ];

        return collect($salaryRanges)->map(function ($count, $range) {
            return [
                'salary_range' => $range,
                'count' => $count,
                'percentage' => $this->collection->count() > 0 ? round(($count / $this->collection->count()) * 100, 1) : 0,
            ];
        })->values()->toArray();
    }

    /**
     * الحصول على الصورة الافتراضية
     */
    protected function getDefaultAvatar(?string $gender): string
    {
        $genderType = $gender === 'FEMALE' ? 'female' : 'male';
        return asset("images/avatars/default-{$genderType}.png");
    }

    /**
     * الحصول على لون الحالة
     */
    protected function getStatusColor(?string $status): string
    {
        return match ($status) {
            'ACTIVE' => '#10B981',
            'PROBATION' => '#F59E0B',
            'SUSPENDED' => '#EF4444',
            'TERMINATED' => '#6B7280',
            'RESIGNED' => '#8B5CF6',
            'RETIRED' => '#3B82F6',
            'ON_LEAVE' => '#06B6D4',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على لون نوع العقد
     */
    protected function getContractTypeColor(?string $contractType): string
    {
        return match ($contractType) {
            'PERMANENT' => '#10B981',
            'TEMPORARY' => '#F59E0B',
            'CONTRACT' => '#3B82F6',
            'PART_TIME' => '#8B5CF6',
            'INTERNSHIP' => '#06B6D4',
            'CONSULTANT' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * الحصول على سنوات الخدمة منسقة
     */
    protected function getFormattedYearsOfService(float $yearsOfService): string
    {
        $years = floor($yearsOfService);
        $months = round(($yearsOfService - $years) * 12);

        if ($years == 0) {
            return "{$months} شهر";
        } elseif ($months == 0) {
            return "{$years} سنة";
        } else {
            return "{$years} سنة و {$months} شهر";
        }
    }
}
