<?php

namespace App\Domains\HR\Listeners;

use App\Domains\HR\Events\EmployeeCreated;
use App\Domains\HR\Services\LeaveManagementService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * مستمع إنشاء أرصدة الإجازات للموظف الجديد
 */
class CreateEmployeeLeaveBalances implements ShouldQueue
{
    use InteractsWithQueue;

    protected LeaveManagementService $leaveService;

    /**
     * إنشاء مثيل جديد من المستمع
     */
    public function __construct(LeaveManagementService $leaveService)
    {
        $this->leaveService = $leaveService;
    }

    /**
     * معالجة الحدث
     */
    public function handle(EmployeeCreated $event): void
    {
        try {
            $this->leaveService->createLeaveBalancesForEmployee($event->employee);
            
            Log::info('تم إنشاء أرصدة الإجازات للموظف الجديد', [
                'employee_id' => $event->employee->id,
                'employee_name' => $event->employee->full_name,
            ]);
            
        } catch (\Exception $e) {
            Log::error('فشل في إنشاء أرصدة الإجازات للموظف الجديد', [
                'employee_id' => $event->employee->id,
                'error' => $e->getMessage(),
            ]);
            
            // إعادة المحاولة
            $this->release(60);
        }
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(EmployeeCreated $event, \Throwable $exception): void
    {
        Log::error('فشل نهائي في إنشاء أرصدة الإجازات', [
            'employee_id' => $event->employee->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
