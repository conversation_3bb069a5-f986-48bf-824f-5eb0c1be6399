<?php

namespace App\Domains\Compliance\Repositories;

use App\Domains\Compliance\Models\ComplianceRule;
use App\Domains\Compliance\Models\Country;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * مستودع قواعد الامتثال
 */
class ComplianceRuleRepository
{
    /**
     * الحصول على جميع القواعد مع التصفية
     */
    public function getAll(array $filters = []): LengthAwarePaginator
    {
        $query = ComplianceRule::with(['country'])
            ->when(isset($filters['country_id']), fn($q) => $q->where('country_id', $filters['country_id']))
            ->when(isset($filters['category']), fn($q) => $q->where('rule_category', $filters['category']))
            ->when(isset($filters['type']), fn($q) => $q->where('rule_type', $filters['type']))
            ->when(isset($filters['status']), fn($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['risk_level']), fn($q) => $q->where('risk_level', $filters['risk_level']))
            ->when(isset($filters['search']), function($q) use ($filters) {
                $q->where(function($query) use ($filters) {
                    $query->where('rule_name_ar', 'like', "%{$filters['search']}%")
                          ->orWhere('rule_name_en', 'like', "%{$filters['search']}%")
                          ->orWhere('rule_description_ar', 'like', "%{$filters['search']}%");
                });
            })
            ->orderBy($filters['sort_by'] ?? 'created_at', $filters['sort_direction'] ?? 'desc');

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * الحصول على القواعد النشطة لدولة معينة
     */
    public function getActiveRulesForCountry(string $countryCode): Collection
    {
        return ComplianceRule::whereHas('country', fn($q) => $q->where('code', $countryCode))
            ->active()
            ->orderBy('priority', 'desc')
            ->get();
    }

    /**
     * الحصول على القواعد حسب الفئة
     */
    public function getRulesByCategory(string $category, string $countryCode = null): Collection
    {
        $query = ComplianceRule::where('rule_category', $category)->active();

        if ($countryCode) {
            $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
        }

        return $query->orderBy('priority', 'desc')->get();
    }

    /**
     * الحصول على القواعد الإلزامية
     */
    public function getMandatoryRules(string $countryCode = null): Collection
    {
        $query = ComplianceRule::where('rule_type', 'mandatory')->active();

        if ($countryCode) {
            $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
        }

        return $query->orderBy('priority', 'desc')->get();
    }

    /**
     * الحصول على القواعد عالية المخاطر
     */
    public function getHighRiskRules(string $countryCode = null): Collection
    {
        $query = ComplianceRule::whereIn('risk_level', ['high', 'critical'])->active();

        if ($countryCode) {
            $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
        }

        return $query->orderBy('risk_level', 'desc')
                    ->orderBy('priority', 'desc')
                    ->get();
    }

    /**
     * الحصول على القواعد التي تحتاج مراجعة
     */
    public function getRulesNeedingReview(): Collection
    {
        return ComplianceRule::active()
            ->dueForReview()
            ->orderBy('next_review_date', 'asc')
            ->get();
    }

    /**
     * البحث في القواعد
     */
    public function search(string $term, array $filters = []): Collection
    {
        $query = ComplianceRule::where(function($q) use ($term) {
            $q->where('rule_name_ar', 'like', "%{$term}%")
              ->orWhere('rule_name_en', 'like', "%{$term}%")
              ->orWhere('rule_description_ar', 'like', "%{$term}%")
              ->orWhere('rule_description_en', 'like', "%{$term}%")
              ->orWhere('legal_reference', 'like', "%{$term}%");
        });

        if (isset($filters['country_id'])) {
            $query->where('country_id', $filters['country_id']);
        }

        if (isset($filters['category'])) {
            $query->where('rule_category', $filters['category']);
        }

        return $query->active()->get();
    }

    /**
     * إنشاء قاعدة جديدة
     */
    public function create(array $data): ComplianceRule
    {
        return ComplianceRule::create($data);
    }

    /**
     * تحديث قاعدة
     */
    public function update(ComplianceRule $rule, array $data): bool
    {
        return $rule->update($data);
    }

    /**
     * حذف قاعدة
     */
    public function delete(ComplianceRule $rule): bool
    {
        return $rule->delete();
    }

    /**
     * تفعيل قاعدة
     */
    public function activate(ComplianceRule $rule): bool
    {
        return $rule->update(['status' => 'active']);
    }

    /**
     * إلغاء تفعيل قاعدة
     */
    public function deactivate(ComplianceRule $rule): bool
    {
        return $rule->update(['status' => 'inactive']);
    }

    /**
     * الحصول على إحصائيات القواعد
     */
    public function getStatistics(string $countryCode = null): array
    {
        $query = ComplianceRule::query();

        if ($countryCode) {
            $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
        }

        return [
            'total' => $query->count(),
            'active' => $query->where('status', 'active')->count(),
            'inactive' => $query->where('status', 'inactive')->count(),
            'mandatory' => $query->where('rule_type', 'mandatory')->count(),
            'high_risk' => $query->whereIn('risk_level', ['high', 'critical'])->count(),
            'need_review' => $query->where('next_review_date', '<=', now())->count(),
            'by_category' => $query->groupBy('rule_category')
                                  ->selectRaw('rule_category, count(*) as count')
                                  ->pluck('count', 'rule_category')
                                  ->toArray(),
        ];
    }

    /**
     * الحصول على القواعد المطبقة على كيان معين
     */
    public function getApplicableRules(array $entityData, string $countryCode = null): Collection
    {
        $query = ComplianceRule::active();

        if ($countryCode) {
            $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
        }

        $rules = $query->get();

        return $rules->filter(function($rule) use ($entityData) {
            return $rule->isApplicableToEntity($entityData);
        });
    }

    /**
     * تحديث تاريخ المراجعة التالية
     */
    public function updateNextReviewDate(ComplianceRule $rule, \Carbon\Carbon $date): bool
    {
        return $rule->update(['next_review_date' => $date]);
    }

    /**
     * الحصول على القواعد المنتهية الصلاحية
     */
    public function getExpiredRules(): Collection
    {
        return ComplianceRule::whereNotNull('expiry_date')
            ->where('expiry_date', '<', now())
            ->where('status', 'active')
            ->get();
    }

    /**
     * تحديث حالة القواعد المنتهية الصلاحية
     */
    public function markExpiredRulesAsInactive(): int
    {
        return ComplianceRule::whereNotNull('expiry_date')
            ->where('expiry_date', '<', now())
            ->where('status', 'active')
            ->update(['status' => 'expired']);
    }
}
