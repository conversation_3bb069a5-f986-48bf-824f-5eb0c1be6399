<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\Ticket;
use App\Models\Employee;
use App\Models\DashboardWidget;
use App\Models\DashboardLayout;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userRole = $this->getUserRole($user);
        
        // جلب تخطيط لوحة التحكم المخصص للمستخدم
        $layout = $this->getUserDashboardLayout($user);
        
        // جلب البيانات حسب دور المستخدم
        $dashboardData = $this->getDashboardDataByRole($userRole, $user);
        
        // جلب الإشعارات
        $notifications = $this->getNotifications($user);
        
        // جلب المهام
        $tasks = $this->getTasks($user);
        
        // جلب الأحداث القادمة
        $upcomingEvents = $this->getUpcomingEvents($user);
        
        return view('dashboard.index', compact(
            'user',
            'userRole',
            'layout',
            'dashboardData',
            'notifications',
            'tasks',
            'upcomingEvents'
        ));
    }

    /**
     * تحديد دور المستخدم
     */
    private function getUserRole($user)
    {
        if ($user->is_admin) {
            return 'general_manager';
        }
        
        // يمكن إضافة منطق أكثر تعقيداً هنا
        return $user->account_type ?? 'employee';
    }

    /**
     * جلب تخطيط لوحة التحكم للمستخدم
     */
    private function getUserDashboardLayout($user)
    {
        $layout = DashboardLayout::where('user_id', $user->id)
            ->where('is_active', true)
            ->first();
            
        if (!$layout) {
            // إنشاء تخطيط افتراضي
            $layout = $this->createDefaultLayout($user);
        }
        
        return $layout;
    }

    /**
     * إنشاء تخطيط افتراضي حسب دور المستخدم
     */
    private function createDefaultLayout($user)
    {
        $userRole = $this->getUserRole($user);
        $defaultWidgets = $this->getDefaultWidgetsByRole($userRole);
        
        $layout = DashboardLayout::create([
            'user_id' => $user->id,
            'name' => 'التخطيط الافتراضي',
            'layout_data' => json_encode($defaultWidgets),
            'is_active' => true,
            'is_default' => true
        ]);
        
        return $layout;
    }

    /**
     * جلب الودجات الافتراضية حسب الدور
     */
    private function getDefaultWidgetsByRole($role)
    {
        $widgets = [];
        
        switch ($role) {
            case 'general_manager':
                $widgets = [
                    ['type' => 'kpi_overview', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'revenue_chart', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 8, 'h' => 6]],
                    ['type' => 'project_status', 'position' => ['x' => 8, 'y' => 4], 'size' => ['w' => 4, 'h' => 6]],
                    ['type' => 'cash_flow', 'position' => ['x' => 0, 'y' => 10], 'size' => ['w' => 6, 'h' => 5]],
                    ['type' => 'profit_analysis', 'position' => ['x' => 6, 'y' => 10], 'size' => ['w' => 6, 'h' => 5]],
                    ['type' => 'geographic_sales', 'position' => ['x' => 0, 'y' => 15], 'size' => ['w' => 12, 'h' => 8]],
                ];
                break;
                
            case 'accountant':
                $widgets = [
                    ['type' => 'financial_overview', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'accounts_receivable', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                    ['type' => 'accounts_payable', 'position' => ['x' => 6, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                    ['type' => 'invoice_status', 'position' => ['x' => 0, 'y' => 10], 'size' => ['w' => 8, 'h' => 5]],
                    ['type' => 'tax_calendar', 'position' => ['x' => 8, 'y' => 10], 'size' => ['w' => 4, 'h' => 5]],
                ];
                break;
                
            case 'project_manager':
                $widgets = [
                    ['type' => 'project_overview', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'gantt_chart', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 12, 'h' => 8]],
                    ['type' => 'task_progress', 'position' => ['x' => 0, 'y' => 12], 'size' => ['w' => 8, 'h' => 6]],
                    ['type' => 'project_costs', 'position' => ['x' => 8, 'y' => 12], 'size' => ['w' => 4, 'h' => 6]],
                ];
                break;
                
            case 'support_supervisor':
                $widgets = [
                    ['type' => 'ticket_overview', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'response_time', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                    ['type' => 'customer_satisfaction', 'position' => ['x' => 6, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                    ['type' => 'ticket_heatmap', 'position' => ['x' => 0, 'y' => 10], 'size' => ['w' => 12, 'h' => 6]],
                ];
                break;
                
            case 'hr_manager':
                $widgets = [
                    ['type' => 'employee_overview', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'attendance_chart', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 8, 'h' => 6]],
                    ['type' => 'leave_requests', 'position' => ['x' => 8, 'y' => 4], 'size' => ['w' => 4, 'h' => 6]],
                    ['type' => 'performance_metrics', 'position' => ['x' => 0, 'y' => 10], 'size' => ['w' => 12, 'h' => 6]],
                ];
                break;
                
            default:
                $widgets = [
                    ['type' => 'welcome_widget', 'position' => ['x' => 0, 'y' => 0], 'size' => ['w' => 12, 'h' => 4]],
                    ['type' => 'my_tasks', 'position' => ['x' => 0, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                    ['type' => 'recent_activity', 'position' => ['x' => 6, 'y' => 4], 'size' => ['w' => 6, 'h' => 6]],
                ];
        }
        
        return $widgets;
    }

    /**
     * جلب بيانات لوحة التحكم حسب الدور
     */
    private function getDashboardDataByRole($role, $user)
    {
        $data = [];
        
        switch ($role) {
            case 'general_manager':
                $data = $this->getGeneralManagerData();
                break;
                
            case 'accountant':
                $data = $this->getAccountantData();
                break;
                
            case 'project_manager':
                $data = $this->getProjectManagerData($user);
                break;
                
            case 'support_supervisor':
                $data = $this->getSupportSupervisorData();
                break;
                
            case 'hr_manager':
                $data = $this->getHRManagerData();
                break;
                
            default:
                $data = $this->getEmployeeData($user);
        }
        
        return $data;
    }

    /**
     * بيانات المدير العام
     */
    private function getGeneralManagerData()
    {
        return [
            'kpis' => [
                'total_revenue' => $this->calculateTotalRevenue(),
                'net_profit' => $this->calculateNetProfit(),
                'profit_margin' => $this->calculateProfitMargin(),
                'active_projects' => $this->getActiveProjectsCount(),
                'customer_satisfaction' => $this->getCustomerSatisfactionScore(),
                'employee_count' => $this->getEmployeeCount(),
            ],
            'revenue_trend' => $this->getRevenueTrend(),
            'project_status_distribution' => $this->getProjectStatusDistribution(),
            'cash_flow_data' => $this->getCashFlowData(),
            'geographic_sales' => $this->getGeographicSalesData(),
            'top_performing_projects' => $this->getTopPerformingProjects(),
        ];
    }

    /**
     * بيانات المحاسب
     */
    private function getAccountantData()
    {
        return [
            'accounts_receivable' => $this->getAccountsReceivable(),
            'accounts_payable' => $this->getAccountsPayable(),
            'overdue_invoices' => $this->getOverdueInvoices(),
            'upcoming_tax_dates' => $this->getUpcomingTaxDates(),
            'monthly_expenses' => $this->getMonthlyExpenses(),
            'payment_trends' => $this->getPaymentTrends(),
        ];
    }

    /**
     * بيانات مدير المشروع
     */
    private function getProjectManagerData($user)
    {
        return [
            'my_projects' => $this->getUserProjects($user),
            'project_timeline' => $this->getProjectTimeline($user),
            'task_completion_rate' => $this->getTaskCompletionRate($user),
            'budget_utilization' => $this->getBudgetUtilization($user),
            'team_workload' => $this->getTeamWorkload($user),
        ];
    }

    /**
     * بيانات مشرف الدعم الفني
     */
    private function getSupportSupervisorData()
    {
        return [
            'open_tickets' => $this->getOpenTicketsCount(),
            'average_response_time' => $this->getAverageResponseTime(),
            'customer_satisfaction_score' => $this->getCustomerSatisfactionScore(),
            'ticket_volume_trend' => $this->getTicketVolumeTrend(),
            'agent_performance' => $this->getAgentPerformance(),
        ];
    }

    /**
     * بيانات مدير الموارد البشرية
     */
    private function getHRManagerData()
    {
        return [
            'employee_attendance' => $this->getEmployeeAttendance(),
            'leave_requests' => $this->getPendingLeaveRequests(),
            'performance_reviews' => $this->getUpcomingPerformanceReviews(),
            'recruitment_pipeline' => $this->getRecruitmentPipeline(),
            'employee_satisfaction' => $this->getEmployeeSatisfaction(),
        ];
    }

    /**
     * بيانات الموظف العادي
     */
    private function getEmployeeData($user)
    {
        return [
            'my_tasks' => $this->getUserTasks($user),
            'recent_activity' => $this->getUserRecentActivity($user),
            'upcoming_deadlines' => $this->getUserUpcomingDeadlines($user),
            'time_tracking' => $this->getUserTimeTracking($user),
        ];
    }

    /**
     * جلب الإشعارات
     */
    private function getNotifications($user)
    {
        return [
            'unread_count' => 15, // مؤقت
            'recent' => [
                [
                    'id' => 1,
                    'type' => 'invoice_overdue',
                    'title' => 'فاتورة متأخرة',
                    'message' => 'فاتورة رقم INV-2024-001 متأخرة بـ 5 أيام',
                    'created_at' => now()->subHours(2),
                    'is_critical' => true,
                ],
                [
                    'id' => 2,
                    'type' => 'project_milestone',
                    'title' => 'إنجاز مرحلة مشروع',
                    'message' => 'تم إنجاز المرحلة الثانية من مشروع تطوير الموقع',
                    'created_at' => now()->subHours(4),
                    'is_critical' => false,
                ],
                [
                    'id' => 3,
                    'type' => 'support_ticket',
                    'title' => 'تذكرة دعم جديدة',
                    'message' => 'تذكرة دعم عاجلة من العميل ABC Company',
                    'created_at' => now()->subHours(6),
                    'is_critical' => true,
                ],
            ]
        ];
    }

    /**
     * جلب المهام
     */
    private function getTasks($user)
    {
        return [
            'pending_count' => 8,
            'overdue_count' => 3,
            'today_tasks' => [
                [
                    'id' => 1,
                    'title' => 'مراجعة تقرير المبيعات الشهري',
                    'priority' => 'high',
                    'due_date' => now()->addHours(4),
                    'project' => 'تقارير الإدارة',
                ],
                [
                    'id' => 2,
                    'title' => 'اجتماع فريق التطوير',
                    'priority' => 'medium',
                    'due_date' => now()->addHours(6),
                    'project' => 'مشروع الموقع الجديد',
                ],
            ]
        ];
    }

    /**
     * جلب الأحداث القادمة
     */
    private function getUpcomingEvents($user)
    {
        return [
            [
                'id' => 1,
                'title' => 'اجتماع مجلس الإدارة',
                'date' => now()->addDays(2),
                'type' => 'meeting',
            ],
            [
                'id' => 2,
                'title' => 'موعد تسليم مشروع ABC',
                'date' => now()->addDays(5),
                'type' => 'deadline',
            ],
        ];
    }

    // Helper methods للحسابات (سيتم تطويرها لاحقاً)
    private function calculateTotalRevenue() { return 2500000; }
    private function calculateNetProfit() { return 450000; }
    private function calculateProfitMargin() { return 18.5; }
    private function getActiveProjectsCount() { return 12; }
    private function getCustomerSatisfactionScore() { return 4.7; }
    private function getEmployeeCount() { return 45; }
    private function getRevenueTrend() { return []; }
    private function getProjectStatusDistribution() { return []; }
    private function getCashFlowData() { return []; }
    private function getGeographicSalesData() { return []; }
    private function getTopPerformingProjects() { return []; }
    private function getAccountsReceivable() { return []; }
    private function getAccountsPayable() { return []; }
    private function getOverdueInvoices() { return []; }
    private function getUpcomingTaxDates() { return []; }
    private function getMonthlyExpenses() { return []; }
    private function getPaymentTrends() { return []; }
    private function getUserProjects($user) { return []; }
    private function getProjectTimeline($user) { return []; }
    private function getTaskCompletionRate($user) { return 85; }
    private function getBudgetUtilization($user) { return []; }
    private function getTeamWorkload($user) { return []; }
    private function getOpenTicketsCount() { return 23; }
    private function getAverageResponseTime() { return 2.5; }
    private function getTicketVolumeTrend() { return []; }
    private function getAgentPerformance() { return []; }
    private function getEmployeeAttendance() { return []; }
    private function getPendingLeaveRequests() { return []; }
    private function getUpcomingPerformanceReviews() { return []; }
    private function getRecruitmentPipeline() { return []; }
    private function getEmployeeSatisfaction() { return 4.2; }
    private function getUserTasks($user) { return []; }
    private function getUserRecentActivity($user) { return []; }
    private function getUserUpcomingDeadlines($user) { return []; }
    private function getUserTimeTracking($user) { return []; }
}
