<?php

namespace App\Domains\Accounting\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Payment Collection Resource
 */
class PaymentCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     */
    public $collects = PaymentResource::class;

    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'summary' => $this->calculateSummary(),
            'filters' => $this->getAvailableFilters(),
        ];
    }

    /**
     * Calculate summary statistics for the payments
     */
    protected function calculateSummary(): array
    {
        $payments = $this->collection;
        
        $totalAmount = $payments->sum('amount');
        $totalCount = $payments->count();
        
        $statusCounts = $payments->groupBy('status')->map->count();
        $methodCounts = $payments->groupBy('payment_method')->map->count();
        
        $averageAmount = $totalCount > 0 ? $totalAmount / $totalCount : 0;
        
        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'average_amount' => round($averageAmount, 2),
            'status_breakdown' => $statusCounts->toArray(),
            'method_breakdown' => $methodCounts->toArray(),
            'pending_amount' => $payments->where('status', 'pending')->sum('amount'),
            'completed_amount' => $payments->where('status', 'completed')->sum('amount'),
            'rejected_amount' => $payments->where('status', 'rejected')->sum('amount'),
        ];
    }

    /**
     * Get available filters for the payments
     */
    protected function getAvailableFilters(): array
    {
        return [
            'statuses' => [
                'pending' => 'معلق',
                'approved' => 'معتمد', 
                'completed' => 'مكتمل',
                'rejected' => 'مرفوض',
                'cancelled' => 'ملغي',
                'voided' => 'باطل',
            ],
            'payment_methods' => [
                'cash' => 'نقدي',
                'bank_transfer' => 'تحويل بنكي',
                'check' => 'شيك',
                'credit_card' => 'بطاقة ائتمان',
                'debit_card' => 'بطاقة خصم',
                'online' => 'دفع إلكتروني',
            ],
            'date_ranges' => [
                'today' => 'اليوم',
                'yesterday' => 'أمس',
                'this_week' => 'هذا الأسبوع',
                'last_week' => 'الأسبوع الماضي',
                'this_month' => 'هذا الشهر',
                'last_month' => 'الشهر الماضي',
                'this_quarter' => 'هذا الربع',
                'last_quarter' => 'الربع الماضي',
                'this_year' => 'هذا العام',
                'last_year' => 'العام الماضي',
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'pagination' => [
                    'current_page' => $this->currentPage(),
                    'last_page' => $this->lastPage(),
                    'per_page' => $this->perPage(),
                    'total' => $this->total(),
                    'from' => $this->firstItem(),
                    'to' => $this->lastItem(),
                ],
                'links' => [
                    'first' => $this->url(1),
                    'last' => $this->url($this->lastPage()),
                    'prev' => $this->previousPageUrl(),
                    'next' => $this->nextPageUrl(),
                ],
            ],
        ];
    }
}
