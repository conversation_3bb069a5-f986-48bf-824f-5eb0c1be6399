<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الميزانية
 * يدعم التحكم في الميزانية وتحليل الانحرافات
 */
class Budget extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'description',
        'budget_type',
        'period_type',
        'start_date',
        'end_date',
        'fiscal_year',
        'department_id',
        'project_id',
        'total_budget',
        'allocated_amount',
        'spent_amount',
        'remaining_amount',
        'status',
        'approval_level',
        'approved_by',
        'approved_at',
        'currency',
        'exchange_rate',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_budget' => 'decimal:2',
        'allocated_amount' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'approved_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * أنواع الميزانية
     */
    public const BUDGET_TYPES = [
        'OPERATIONAL' => 'تشغيلية',
        'CAPITAL' => 'رأسمالية',
        'PROJECT' => 'مشروع',
        'DEPARTMENT' => 'قسم',
        'MARKETING' => 'تسويق',
        'RESEARCH' => 'بحث وتطوير',
        'MAINTENANCE' => 'صيانة',
    ];

    /**
     * أنواع الفترة
     */
    public const PERIOD_TYPES = [
        'MONTHLY' => 'شهرية',
        'QUARTERLY' => 'ربع سنوية',
        'SEMI_ANNUAL' => 'نصف سنوية',
        'ANNUAL' => 'سنوية',
        'CUSTOM' => 'مخصصة',
    ];

    /**
     * حالات الميزانية
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING_APPROVAL' => 'في انتظار الموافقة',
        'APPROVED' => 'معتمدة',
        'ACTIVE' => 'نشطة',
        'SUSPENDED' => 'معلقة',
        'CLOSED' => 'مغلقة',
        'CANCELLED' => 'ملغية',
    ];

    /**
     * بنود الميزانية
     */
    public function items(): HasMany
    {
        return $this->hasMany(BudgetItem::class);
    }

    /**
     * تحليل الانحرافات
     */
    public function variances(): HasMany
    {
        return $this->hasMany(BudgetVariance::class);
    }

    /**
     * القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Department::class);
    }

    /**
     * المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Projects\Models\Project::class);
    }

    /**
     * المعتمد
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * حساب المجاميع
     */
    public function calculateTotals(): void
    {
        $this->total_budget = $this->items()->sum('budgeted_amount');
        $this->allocated_amount = $this->items()->sum('allocated_amount');
        $this->spent_amount = $this->items()->sum('actual_amount');
        $this->remaining_amount = $this->total_budget - $this->spent_amount;
    }

    /**
     * تحليل الانحرافات
     */
    public function analyzeVariances(): array
    {
        $analysis = [
            'total_variance' => $this->spent_amount - $this->total_budget,
            'variance_percentage' => $this->total_budget > 0
                ? (($this->spent_amount - $this->total_budget) / $this->total_budget) * 100
                : 0,
            'items_over_budget' => 0,
            'items_under_budget' => 0,
            'critical_variances' => [],
        ];

        foreach ($this->items as $item) {
            $variance = $item->actual_amount - $item->budgeted_amount;
            $variancePercentage = $item->budgeted_amount > 0
                ? ($variance / $item->budgeted_amount) * 100
                : 0;

            if ($variance > 0) {
                $analysis['items_over_budget']++;
            } elseif ($variance < 0) {
                $analysis['items_under_budget']++;
            }

            // الانحرافات الحرجة (أكثر من 20%)
            if (abs($variancePercentage) > 20) {
                $analysis['critical_variances'][] = [
                    'item' => $item->description,
                    'variance' => $variance,
                    'variance_percentage' => $variancePercentage,
                ];
            }
        }

        return $analysis;
    }

    /**
     * توليد تقرير الانحرافات
     */
    public function generateVarianceReport(): array
    {
        $report = [
            'budget_info' => [
                'name' => $this->name,
                'period' => $this->start_date->format('Y-m-d') . ' إلى ' . $this->end_date->format('Y-m-d'),
                'total_budget' => $this->total_budget,
                'spent_amount' => $this->spent_amount,
                'remaining_amount' => $this->remaining_amount,
            ],
            'summary' => $this->analyzeVariances(),
            'items' => [],
            'recommendations' => [],
        ];

        foreach ($this->items as $item) {
            $variance = $item->actual_amount - $item->budgeted_amount;
            $variancePercentage = $item->budgeted_amount > 0
                ? ($variance / $item->budgeted_amount) * 100
                : 0;

            $report['items'][] = [
                'description' => $item->description,
                'budgeted' => $item->budgeted_amount,
                'actual' => $item->actual_amount,
                'variance' => $variance,
                'variance_percentage' => $variancePercentage,
                'status' => $this->getVarianceStatus($variancePercentage),
            ];
        }

        // توليد التوصيات
        $report['recommendations'] = $this->generateRecommendations($report['summary']);

        return $report;
    }

    /**
     * الحصول على حالة الانحراف
     */
    protected function getVarianceStatus(float $variancePercentage): string
    {
        if ($variancePercentage > 20) {
            return 'تجاوز حرج';
        } elseif ($variancePercentage > 10) {
            return 'تجاوز متوسط';
        } elseif ($variancePercentage > 0) {
            return 'تجاوز طفيف';
        } elseif ($variancePercentage < -20) {
            return 'توفير كبير';
        } elseif ($variancePercentage < -10) {
            return 'توفير متوسط';
        } else {
            return 'ضمن الحدود';
        }
    }

    /**
     * توليد التوصيات
     */
    protected function generateRecommendations(array $summary): array
    {
        $recommendations = [];

        if ($summary['variance_percentage'] > 15) {
            $recommendations[] = 'يُنصح بمراجعة الميزانية وإعادة تخصيص الموارد';
        }

        if ($summary['items_over_budget'] > $summary['items_under_budget']) {
            $recommendations[] = 'هناك تجاوز في عدة بنود، يُنصح بتشديد الرقابة';
        }

        if (count($summary['critical_variances']) > 0) {
            $recommendations[] = 'توجد انحرافات حرجة تتطلب تدخل فوري';
        }

        return $recommendations;
    }

    /**
     * التحقق من إمكانية الإنفاق
     */
    public function canSpend(float $amount, string $itemCode = null): bool
    {
        if ($itemCode) {
            $item = $this->items()->where('item_code', $itemCode)->first();
            if ($item) {
                return ($item->actual_amount + $amount) <= $item->budgeted_amount;
            }
        }

        return ($this->spent_amount + $amount) <= $this->total_budget;
    }

    /**
     * نطاق للميزانيات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'ACTIVE');
    }

    /**
     * نطاق حسب النوع
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('budget_type', $type);
    }

    /**
     * نطاق حسب الفترة
     */
    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->where('start_date', '<=', $endDate)
            ->where('end_date', '>=', $startDate);
    }
}
