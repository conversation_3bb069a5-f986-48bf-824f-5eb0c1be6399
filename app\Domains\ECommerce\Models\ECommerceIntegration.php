<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج التكامل مع التجارة الإلكترونية
 * يدير عملية التكامل والمزامنة مع منصات التجارة الإلكترونية
 */
class ECommerceIntegration extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'platform_id',
        'store_id',
        'company_id',
        'name',
        'description',
        'status',
        'health_status',
        'connection_status',
        'sync_status',
        'last_sync_at',
        'last_successful_sync_at',
        'last_failed_sync_at',
        'next_sync_at',
        'sync_frequency',
        'sync_mode',
        'sync_direction',
        'auto_sync_enabled',
        'real_time_sync_enabled',
        'batch_sync_enabled',
        'webhook_enabled',
        'api_rate_limit',
        'api_calls_made',
        'api_calls_remaining',
        'api_reset_time',
        'connection_config',
        'authentication_config',
        'sync_config',
        'mapping_config',
        'transformation_config',
        'validation_config',
        'error_handling_config',
        'retry_config',
        'notification_config',
        'logging_config',
        'monitoring_config',
        'performance_config',
        'security_config',
        'compliance_config',
        'backup_config',
        'archive_config',
        'cleanup_config',
        'maintenance_config',
        'upgrade_config',
        'migration_config',
        'integration_settings',
        'platform_settings',
        'store_settings',
        'sync_settings',
        'api_settings',
        'webhook_settings',
        'notification_settings',
        'security_settings',
        'compliance_settings',
        'performance_settings',
        'monitoring_settings',
        'logging_settings',
        'backup_settings',
        'archive_settings',
        'cleanup_settings',
        'maintenance_settings',
        'upgrade_settings',
        'migration_settings',
        'custom_settings',
        'advanced_settings',
        'experimental_settings',
        'beta_settings',
        'debug_settings',
        'test_settings',
        'development_settings',
        'staging_settings',
        'production_settings',
        'environment_settings',
        'deployment_settings',
        'configuration_settings',
        'operational_settings',
        'business_settings',
        'technical_settings',
        'functional_settings',
        'non_functional_settings',
        'quality_settings',
        'usability_settings',
        'accessibility_settings',
        'internationalization_settings',
        'localization_settings',
        'globalization_settings',
        'regionalization_settings',
        'culturalization_settings',
        'personalization_settings',
        'customization_settings',
        'enabled_features',
        'disabled_features',
        'supported_operations',
        'enabled_operations',
        'disabled_operations',
        'sync_capabilities',
        'enabled_sync_types',
        'disabled_sync_types',
        'data_mapping',
        'field_mapping',
        'attribute_mapping',
        'category_mapping',
        'status_mapping',
        'currency_mapping',
        'language_mapping',
        'timezone_mapping',
        'country_mapping',
        'region_mapping',
        'tax_mapping',
        'shipping_mapping',
        'payment_mapping',
        'discount_mapping',
        'promotion_mapping',
        'coupon_mapping',
        'inventory_mapping',
        'pricing_mapping',
        'image_mapping',
        'media_mapping',
        'seo_mapping',
        'meta_mapping',
        'custom_field_mapping',
        'transformation_rules',
        'validation_rules',
        'business_rules',
        'sync_rules',
        'conflict_resolution_rules',
        'error_handling_rules',
        'retry_rules',
        'escalation_rules',
        'notification_rules',
        'alert_rules',
        'monitoring_rules',
        'performance_rules',
        'security_rules',
        'compliance_rules',
        'audit_rules',
        'logging_rules',
        'backup_rules',
        'archive_rules',
        'cleanup_rules',
        'maintenance_rules',
        'upgrade_rules',
        'migration_rules',
        'custom_rules',
        'advanced_rules',
        'experimental_rules',
        'beta_rules',
        'debug_rules',
        'test_rules',
        'development_rules',
        'staging_rules',
        'production_rules',
        'environment_rules',
        'deployment_rules',
        'configuration_rules',
        'operational_rules',
        'business_logic_rules',
        'technical_rules',
        'functional_rules',
        'non_functional_rules',
        'quality_rules',
        'usability_rules',
        'accessibility_rules',
        'internationalization_rules',
        'localization_rules',
        'globalization_rules',
        'regionalization_rules',
        'culturalization_rules',
        'personalization_rules',
        'customization_rules',
        'statistics',
        'metrics',
        'analytics',
        'performance_metrics',
        'sync_metrics',
        'api_metrics',
        'error_metrics',
        'success_metrics',
        'failure_metrics',
        'latency_metrics',
        'throughput_metrics',
        'availability_metrics',
        'reliability_metrics',
        'scalability_metrics',
        'efficiency_metrics',
        'effectiveness_metrics',
        'quality_metrics',
        'user_metrics',
        'business_metrics',
        'technical_metrics',
        'operational_metrics',
        'financial_metrics',
        'cost_metrics',
        'revenue_metrics',
        'profit_metrics',
        'roi_metrics',
        'value_metrics',
        'impact_metrics',
        'benefit_metrics',
        'outcome_metrics',
        'result_metrics',
        'achievement_metrics',
        'success_rate_metrics',
        'failure_rate_metrics',
        'error_rate_metrics',
        'accuracy_metrics',
        'precision_metrics',
        'recall_metrics',
        'f1_score_metrics',
        'auc_metrics',
        'confusion_matrix_metrics',
        'classification_metrics',
        'regression_metrics',
        'clustering_metrics',
        'anomaly_detection_metrics',
        'forecasting_metrics',
        'prediction_metrics',
        'recommendation_metrics',
        'optimization_metrics',
        'automation_metrics',
        'intelligence_metrics',
        'learning_metrics',
        'adaptation_metrics',
        'evolution_metrics',
        'innovation_metrics',
        'transformation_metrics',
        'disruption_metrics',
        'revolution_metrics',
        'paradigm_shift_metrics',
        'breakthrough_metrics',
        'discovery_metrics',
        'invention_metrics',
        'creation_metrics',
        'development_metrics',
        'advancement_metrics',
        'progression_metrics',
        'improvement_metrics',
        'enhancement_metrics',
        'optimization_advanced_metrics',
        'maximization_metrics',
        'minimization_metrics',
        'balance_metrics',
        'harmony_metrics',
        'synergy_metrics',
        'integration_metrics',
        'unification_metrics',
        'consolidation_metrics',
        'convergence_metrics',
        'alignment_metrics',
        'coordination_metrics',
        'collaboration_metrics',
        'cooperation_metrics',
        'partnership_metrics',
        'alliance_metrics',
        'coalition_metrics',
        'federation_metrics',
        'confederation_metrics',
        'union_metrics',
        'association_metrics',
        'organization_metrics',
        'institution_metrics',
        'establishment_metrics',
        'foundation_metrics',
        'framework_metrics',
        'structure_metrics',
        'architecture_metrics',
        'design_metrics',
        'blueprint_metrics',
        'plan_metrics',
        'strategy_metrics',
        'vision_metrics',
        'mission_metrics',
        'purpose_metrics',
        'goal_metrics',
        'objective_metrics',
        'target_metrics',
        'aim_metrics',
        'intention_metrics',
        'aspiration_metrics',
        'ambition_metrics',
        'dream_metrics',
        'hope_metrics',
        'wish_metrics',
        'desire_metrics',
        'want_metrics',
        'need_metrics',
        'requirement_metrics',
        'necessity_metrics',
        'essential_metrics',
        'critical_metrics',
        'vital_metrics',
        'crucial_metrics',
        'important_metrics',
        'significant_metrics',
        'meaningful_metrics',
        'valuable_metrics',
        'worthwhile_metrics',
        'beneficial_metrics',
        'advantageous_metrics',
        'profitable_metrics',
        'rewarding_metrics',
        'satisfying_metrics',
        'fulfilling_metrics',
        'gratifying_metrics',
        'pleasing_metrics',
        'enjoyable_metrics',
        'delightful_metrics',
        'wonderful_metrics',
        'amazing_metrics',
        'incredible_metrics',
        'fantastic_metrics',
        'marvelous_metrics',
        'spectacular_metrics',
        'outstanding_metrics',
        'exceptional_metrics',
        'extraordinary_metrics',
        'remarkable_metrics',
        'impressive_metrics',
        'stunning_metrics',
        'breathtaking_metrics',
        'awe_inspiring_metrics',
        'mind_blowing_metrics',
        'revolutionary_metrics',
        'groundbreaking_metrics',
        'pioneering_metrics',
        'innovative_metrics',
        'creative_metrics',
        'original_metrics',
        'unique_metrics',
        'distinctive_metrics',
        'special_metrics',
        'exclusive_metrics',
        'premium_metrics',
        'luxury_metrics',
        'elite_metrics',
        'superior_metrics',
        'supreme_metrics',
        'ultimate_metrics',
        'final_metrics',
        'complete_metrics',
        'comprehensive_metrics',
        'thorough_metrics',
        'exhaustive_metrics',
        'extensive_metrics',
        'detailed_metrics',
        'in_depth_metrics',
        'profound_metrics',
        'deep_metrics',
        'rich_metrics',
        'full_metrics',
        'total_metrics',
        'entire_metrics',
        'whole_metrics',
        'all_encompassing_metrics',
        'all_inclusive_metrics',
        'universal_metrics',
        'global_metrics',
        'worldwide_metrics',
        'international_metrics',
        'multinational_metrics',
        'transnational_metrics',
        'cross_border_metrics',
        'cross_cultural_metrics',
        'multicultural_metrics',
        'diverse_metrics',
        'varied_metrics',
        'different_metrics',
        'alternative_metrics',
        'optional_metrics',
        'flexible_metrics',
        'adaptable_metrics',
        'adjustable_metrics',
        'customizable_metrics',
        'configurable_metrics',
        'programmable_metrics',
        'scriptable_metrics',
        'automatable_metrics',
        'intelligent_metrics',
        'smart_metrics',
        'clever_metrics',
        'wise_metrics',
        'brilliant_metrics',
        'genius_metrics',
        'masterful_metrics',
        'expert_metrics',
        'professional_metrics',
        'enterprise_metrics',
        'commercial_metrics',
        'business_advanced_metrics',
        'corporate_metrics',
        'organizational_metrics',
        'institutional_metrics',
        'governmental_metrics',
        'public_metrics',
        'private_metrics',
        'personal_metrics',
        'individual_metrics',
        'custom_metrics',
        'bespoke_metrics',
        'tailored_metrics',
        'personalized_metrics',
        'individualized_metrics',
        'specialized_metrics',
        'focused_metrics',
        'targeted_metrics',
        'specific_metrics',
        'precise_metrics',
        'exact_metrics',
        'accurate_metrics',
        'correct_metrics',
        'right_metrics',
        'proper_metrics',
        'appropriate_metrics',
        'suitable_metrics',
        'fitting_metrics',
        'perfect_metrics',
        'is_active',
        'is_connected',
        'is_syncing',
        'is_healthy',
        'is_test_mode',
        'is_sandbox',
        'is_production',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'connection_config' => 'array',
        'authentication_config' => 'encrypted:array',
        'sync_config' => 'array',
        'mapping_config' => 'array',
        'transformation_config' => 'array',
        'validation_config' => 'array',
        'error_handling_config' => 'array',
        'retry_config' => 'array',
        'notification_config' => 'array',
        'logging_config' => 'array',
        'monitoring_config' => 'array',
        'performance_config' => 'array',
        'security_config' => 'array',
        'compliance_config' => 'array',
        'backup_config' => 'array',
        'archive_config' => 'array',
        'cleanup_config' => 'array',
        'maintenance_config' => 'array',
        'upgrade_config' => 'array',
        'migration_config' => 'array',
        'integration_settings' => 'array',
        'platform_settings' => 'array',
        'store_settings' => 'array',
        'sync_settings' => 'array',
        'api_settings' => 'array',
        'webhook_settings' => 'array',
        'notification_settings' => 'array',
        'security_settings' => 'array',
        'compliance_settings' => 'array',
        'performance_settings' => 'array',
        'monitoring_settings' => 'array',
        'logging_settings' => 'array',
        'backup_settings' => 'array',
        'archive_settings' => 'array',
        'cleanup_settings' => 'array',
        'maintenance_settings' => 'array',
        'upgrade_settings' => 'array',
        'migration_settings' => 'array',
        'custom_settings' => 'array',
        'advanced_settings' => 'array',
        'experimental_settings' => 'array',
        'beta_settings' => 'array',
        'debug_settings' => 'array',
        'test_settings' => 'array',
        'development_settings' => 'array',
        'staging_settings' => 'array',
        'production_settings' => 'array',
        'environment_settings' => 'array',
        'deployment_settings' => 'array',
        'configuration_settings' => 'array',
        'operational_settings' => 'array',
        'business_settings' => 'array',
        'technical_settings' => 'array',
        'functional_settings' => 'array',
        'non_functional_settings' => 'array',
        'quality_settings' => 'array',
        'usability_settings' => 'array',
        'accessibility_settings' => 'array',
        'internationalization_settings' => 'array',
        'localization_settings' => 'array',
        'globalization_settings' => 'array',
        'regionalization_settings' => 'array',
        'culturalization_settings' => 'array',
        'personalization_settings' => 'array',
        'customization_settings' => 'array',
        'enabled_features' => 'array',
        'disabled_features' => 'array',
        'supported_operations' => 'array',
        'enabled_operations' => 'array',
        'disabled_operations' => 'array',
        'sync_capabilities' => 'array',
        'enabled_sync_types' => 'array',
        'disabled_sync_types' => 'array',
        'data_mapping' => 'array',
        'field_mapping' => 'array',
        'attribute_mapping' => 'array',
        'category_mapping' => 'array',
        'status_mapping' => 'array',
        'currency_mapping' => 'array',
        'language_mapping' => 'array',
        'timezone_mapping' => 'array',
        'country_mapping' => 'array',
        'region_mapping' => 'array',
        'tax_mapping' => 'array',
        'shipping_mapping' => 'array',
        'payment_mapping' => 'array',
        'discount_mapping' => 'array',
        'promotion_mapping' => 'array',
        'coupon_mapping' => 'array',
        'inventory_mapping' => 'array',
        'pricing_mapping' => 'array',
        'image_mapping' => 'array',
        'media_mapping' => 'array',
        'seo_mapping' => 'array',
        'meta_mapping' => 'array',
        'custom_field_mapping' => 'array',
        'transformation_rules' => 'array',
        'validation_rules' => 'array',
        'business_rules' => 'array',
        'sync_rules' => 'array',
        'conflict_resolution_rules' => 'array',
        'error_handling_rules' => 'array',
        'retry_rules' => 'array',
        'escalation_rules' => 'array',
        'notification_rules' => 'array',
        'alert_rules' => 'array',
        'monitoring_rules' => 'array',
        'performance_rules' => 'array',
        'security_rules' => 'array',
        'compliance_rules' => 'array',
        'audit_rules' => 'array',
        'logging_rules' => 'array',
        'backup_rules' => 'array',
        'archive_rules' => 'array',
        'cleanup_rules' => 'array',
        'maintenance_rules' => 'array',
        'upgrade_rules' => 'array',
        'migration_rules' => 'array',
        'custom_rules' => 'array',
        'advanced_rules' => 'array',
        'experimental_rules' => 'array',
        'beta_rules' => 'array',
        'debug_rules' => 'array',
        'test_rules' => 'array',
        'development_rules' => 'array',
        'staging_rules' => 'array',
        'production_rules' => 'array',
        'environment_rules' => 'array',
        'deployment_rules' => 'array',
        'configuration_rules' => 'array',
        'operational_rules' => 'array',
        'business_logic_rules' => 'array',
        'technical_rules' => 'array',
        'functional_rules' => 'array',
        'non_functional_rules' => 'array',
        'quality_rules' => 'array',
        'usability_rules' => 'array',
        'accessibility_rules' => 'array',
        'internationalization_rules' => 'array',
        'localization_rules' => 'array',
        'globalization_rules' => 'array',
        'regionalization_rules' => 'array',
        'culturalization_rules' => 'array',
        'personalization_rules' => 'array',
        'customization_rules' => 'array',
        'statistics' => 'array',
        'metrics' => 'array',
        'analytics' => 'array',
        'performance_metrics' => 'array',
        'sync_metrics' => 'array',
        'api_metrics' => 'array',
        'error_metrics' => 'array',
        'success_metrics' => 'array',
        'failure_metrics' => 'array',
        'latency_metrics' => 'array',
        'throughput_metrics' => 'array',
        'availability_metrics' => 'array',
        'reliability_metrics' => 'array',
        'scalability_metrics' => 'array',
        'efficiency_metrics' => 'array',
        'effectiveness_metrics' => 'array',
        'quality_metrics' => 'array',
        'user_metrics' => 'array',
        'business_metrics' => 'array',
        'technical_metrics' => 'array',
        'operational_metrics' => 'array',
        'financial_metrics' => 'array',
        'cost_metrics' => 'array',
        'revenue_metrics' => 'array',
        'profit_metrics' => 'array',
        'roi_metrics' => 'array',
        'value_metrics' => 'array',
        'impact_metrics' => 'array',
        'benefit_metrics' => 'array',
        'outcome_metrics' => 'array',
        'result_metrics' => 'array',
        'achievement_metrics' => 'array',
        'metadata' => 'array',
        'last_sync_at' => 'datetime',
        'last_successful_sync_at' => 'datetime',
        'last_failed_sync_at' => 'datetime',
        'next_sync_at' => 'datetime',
        'api_reset_time' => 'datetime',
        'auto_sync_enabled' => 'boolean',
        'real_time_sync_enabled' => 'boolean',
        'batch_sync_enabled' => 'boolean',
        'webhook_enabled' => 'boolean',
        'is_active' => 'boolean',
        'is_connected' => 'boolean',
        'is_syncing' => 'boolean',
        'is_healthy' => 'boolean',
        'is_test_mode' => 'boolean',
        'is_sandbox' => 'boolean',
        'is_production' => 'boolean',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'status', 'sync_status', 'connection_status',
                'is_active', 'is_connected', 'auto_sync_enabled'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function syncLogs(): HasMany
    {
        return $this->hasMany(ECommerceSyncLog::class, 'integration_id');
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(ECommerceWebhook::class, 'integration_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(ECommerceProduct::class, 'integration_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(ECommerceOrder::class, 'integration_id');
    }

    public function customers(): HasMany
    {
        return $this->hasMany(ECommerceCustomer::class, 'integration_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeConnected($query)
    {
        return $query->where('is_connected', true);
    }

    public function scopeHealthy($query)
    {
        return $query->where('is_healthy', true);
    }

    public function scopeSyncing($query)
    {
        return $query->where('is_syncing', true);
    }

    public function scopeProduction($query)
    {
        return $query->where('is_production', true);
    }

    public function scopeTestMode($query)
    {
        return $query->where('is_test_mode', true);
    }

    public function scopeByPlatform($query, $platformId)
    {
        return $query->where('platform_id', $platformId);
    }

    public function scopeByStore($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeAutoSyncEnabled($query)
    {
        return $query->where('auto_sync_enabled', true);
    }

    public function scopeRealTimeSyncEnabled($query)
    {
        return $query->where('real_time_sync_enabled', true);
    }

    public function scopeBatchSyncEnabled($query)
    {
        return $query->where('batch_sync_enabled', true);
    }

    public function scopeWebhookEnabled($query)
    {
        return $query->where('webhook_enabled', true);
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isConnected(): bool
    {
        return $this->is_connected;
    }

    public function isHealthy(): bool
    {
        return $this->is_healthy;
    }

    public function isSyncing(): bool
    {
        return $this->is_syncing;
    }

    public function isProduction(): bool
    {
        return $this->is_production;
    }

    public function isTestMode(): bool
    {
        return $this->is_test_mode;
    }

    public function isSandbox(): bool
    {
        return $this->is_sandbox;
    }

    public function hasAutoSync(): bool
    {
        return $this->auto_sync_enabled;
    }

    public function hasRealTimeSync(): bool
    {
        return $this->real_time_sync_enabled;
    }

    public function hasBatchSync(): bool
    {
        return $this->batch_sync_enabled;
    }

    public function hasWebhooks(): bool
    {
        return $this->webhook_enabled;
    }

    public function getLastSyncStatus(): string
    {
        if ($this->last_successful_sync_at && $this->last_failed_sync_at) {
            return $this->last_successful_sync_at > $this->last_failed_sync_at ? 'success' : 'failed';
        }

        if ($this->last_successful_sync_at) {
            return 'success';
        }

        if ($this->last_failed_sync_at) {
            return 'failed';
        }

        return 'never';
    }

    public function getTimeSinceLastSync(): ?int
    {
        if (!$this->last_sync_at) {
            return null;
        }

        return now()->diffInMinutes($this->last_sync_at);
    }

    public function needsSync(): bool
    {
        if (!$this->auto_sync_enabled) {
            return false;
        }

        $frequency = $this->sync_frequency ?? 60; // minutes
        $timeSinceLastSync = $this->getTimeSinceLastSync();

        return $timeSinceLastSync === null || $timeSinceLastSync >= $frequency;
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->isConnected() && !$this->isSyncing();
    }

    public function getHealthScore(): float
    {
        $score = 0;
        $maxScore = 10;

        // Connection status (2 points)
        if ($this->isConnected()) {
            $score += 2;
        }

        // Health status (2 points)
        if ($this->isHealthy()) {
            $score += 2;
        }

        // Recent successful sync (2 points)
        if ($this->last_successful_sync_at && $this->last_successful_sync_at > now()->subHours(24)) {
            $score += 2;
        }

        // No recent failures (2 points)
        if (!$this->last_failed_sync_at || $this->last_failed_sync_at < now()->subHours(24)) {
            $score += 2;
        }

        // Active status (1 point)
        if ($this->isActive()) {
            $score += 1;
        }

        // Production readiness (1 point)
        if ($this->isProduction()) {
            $score += 1;
        }

        return ($score / $maxScore) * 100;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getHealthScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';

        return 'critical';
    }

    public function getApiUsage(): array
    {
        return [
            'calls_made' => $this->api_calls_made ?? 0,
            'calls_remaining' => $this->api_calls_remaining ?? 0,
            'rate_limit' => $this->api_rate_limit ?? 0,
            'reset_time' => $this->api_reset_time,
            'usage_percentage' => $this->api_rate_limit > 0 ?
                (($this->api_calls_made ?? 0) / $this->api_rate_limit) * 100 : 0,
        ];
    }

    public function isApiLimitReached(): bool
    {
        if (!$this->api_rate_limit) {
            return false;
        }

        return ($this->api_calls_made ?? 0) >= $this->api_rate_limit;
    }

    public function getEnabledSyncTypes(): array
    {
        return $this->enabled_sync_types ?? [];
    }

    public function supportsSyncType($type): bool
    {
        return in_array($type, $this->getEnabledSyncTypes());
    }

    public function getEnabledOperations(): array
    {
        return $this->enabled_operations ?? [];
    }

    public function supportsOperation($operation): bool
    {
        return in_array($operation, $this->getEnabledOperations());
    }

    public function getEnabledFeatures(): array
    {
        return $this->enabled_features ?? [];
    }

    public function hasFeature($feature): bool
    {
        return in_array($feature, $this->getEnabledFeatures());
    }

    public function getDataMapping($type = null): array
    {
        $mapping = $this->data_mapping ?? [];

        if ($type === null) {
            return $mapping;
        }

        return $mapping[$type] ?? [];
    }

    public function getFieldMapping($entity, $field = null): mixed
    {
        $mapping = $this->field_mapping ?? [];
        $entityMapping = $mapping[$entity] ?? [];

        if ($field === null) {
            return $entityMapping;
        }

        return $entityMapping[$field] ?? null;
    }

    public function mapField($entity, $localField, $remoteField): void
    {
        $mapping = $this->field_mapping ?? [];
        $mapping[$entity][$localField] = $remoteField;
        $this->field_mapping = $mapping;
        $this->save();
    }

    public function getTransformationRules($type = null): array
    {
        $rules = $this->transformation_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getValidationRules($type = null): array
    {
        $rules = $this->validation_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getBusinessRules($type = null): array
    {
        $rules = $this->business_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getSyncRules($type = null): array
    {
        $rules = $this->sync_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getErrorHandlingRules($type = null): array
    {
        $rules = $this->error_handling_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getRetryRules($type = null): array
    {
        $rules = $this->retry_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getNotificationRules($type = null): array
    {
        $rules = $this->notification_rules ?? [];

        if ($type === null) {
            return $rules;
        }

        return $rules[$type] ?? [];
    }

    public function getPerformanceMetrics(): array
    {
        return $this->performance_metrics ?? [];
    }

    public function getSyncMetrics(): array
    {
        return $this->sync_metrics ?? [];
    }

    public function getApiMetrics(): array
    {
        return $this->api_metrics ?? [];
    }

    public function getErrorMetrics(): array
    {
        return $this->error_metrics ?? [];
    }

    public function getSuccessMetrics(): array
    {
        return $this->success_metrics ?? [];
    }

    public function updateMetrics($type, $data): void
    {
        $metricsField = $type . '_metrics';
        $currentMetrics = $this->$metricsField ?? [];
        $currentMetrics[now()->format('Y-m-d H:i:s')] = $data;

        // Keep only last 100 entries
        if (count($currentMetrics) > 100) {
            $currentMetrics = array_slice($currentMetrics, -100, null, true);
        }

        $this->$metricsField = $currentMetrics;
        $this->save();
    }

    public function recordApiCall(): void
    {
        $this->increment('api_calls_made');

        if ($this->api_rate_limit && $this->api_calls_made >= $this->api_rate_limit) {
            $this->api_reset_time = now()->addHour();
            $this->save();
        }
    }

    public function resetApiUsage(): void
    {
        $this->update([
            'api_calls_made' => 0,
            'api_calls_remaining' => $this->api_rate_limit,
            'api_reset_time' => null,
        ]);
    }

    public function testConnection(): array
    {
        try {
            $integrationService = app(\App\Domains\ECommerce\Services\ECommerceIntegrationService::class);
            $result = $integrationService->testConnection($this);

            $this->update([
                'is_connected' => $result['success'],
                'connection_status' => $result['success'] ? 'connected' : 'disconnected',
                'health_status' => $result['success'] ? 'healthy' : 'unhealthy',
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->update([
                'is_connected' => false,
                'connection_status' => 'error',
                'health_status' => 'unhealthy',
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
        }
    }

    public function performHealthCheck(): array
    {
        $checks = [];
        $overallHealth = true;

        // Connection check
        $connectionResult = $this->testConnection();
        $checks['connection'] = $connectionResult;
        if (!$connectionResult['success']) {
            $overallHealth = false;
        }

        // API usage check
        $apiUsage = $this->getApiUsage();
        $apiHealthy = $apiUsage['usage_percentage'] < 90;
        $checks['api_usage'] = [
            'success' => $apiHealthy,
            'message' => $apiHealthy ? 'API usage is healthy' : 'API usage is high',
            'usage_percentage' => $apiUsage['usage_percentage'],
            'calls_remaining' => $apiUsage['calls_remaining'],
        ];
        if (!$apiHealthy) {
            $overallHealth = false;
        }

        // Sync status check
        $syncHealthy = $this->getLastSyncStatus() !== 'failed';
        $checks['sync'] = [
            'success' => $syncHealthy,
            'message' => $syncHealthy ? 'Sync is healthy' : 'Recent sync failures detected',
            'last_sync' => $this->last_sync_at,
            'last_successful_sync' => $this->last_successful_sync_at,
            'last_failed_sync' => $this->last_failed_sync_at,
        ];
        if (!$syncHealthy) {
            $overallHealth = false;
        }

        // Update health status
        $this->update([
            'is_healthy' => $overallHealth,
            'health_status' => $overallHealth ? 'healthy' : 'unhealthy',
        ]);

        return [
            'overall_health' => $overallHealth,
            'health_score' => $this->getHealthScore(),
            'health_status' => $this->getHealthStatus(),
            'checks' => $checks,
            'timestamp' => now(),
        ];
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'platform' => $this->platform->name,
            'store' => $this->store->name,
            'status' => $this->status,
            'health_status' => $this->getHealthStatus(),
            'health_score' => $this->getHealthScore(),
            'connection_status' => $this->connection_status,
            'sync_status' => $this->sync_status,
            'last_sync' => $this->last_sync_at,
            'is_active' => $this->isActive(),
            'is_connected' => $this->isConnected(),
            'is_healthy' => $this->isHealthy(),
            'is_syncing' => $this->isSyncing(),
            'auto_sync_enabled' => $this->hasAutoSync(),
            'real_time_sync_enabled' => $this->hasRealTimeSync(),
            'webhook_enabled' => $this->hasWebhooks(),
            'enabled_sync_types' => $this->getEnabledSyncTypes(),
            'enabled_operations' => $this->getEnabledOperations(),
            'api_usage' => $this->getApiUsage(),
        ];
    }
}
