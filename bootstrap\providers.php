<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Application Service Providers
    |--------------------------------------------------------------------------
    */
    App\Providers\AppServiceProvider::class,
    App\Providers\TelescopeServiceProvider::class,

    /*
    |--------------------------------------------------------------------------
    | Domain Service Providers
    |--------------------------------------------------------------------------
    |
    | Domain-specific service providers for the modular monolith architecture.
    | Each domain has its own service provider that handles registration of
    | services, routes, policies, events, and other domain-specific concerns.
    |
    */

    // Shared Domain (Core functionality)
    App\Domains\Shared\Providers\SharedServiceProvider::class,

    // Accounting Domain
    App\Domains\Accounting\Providers\AccountingServiceProvider::class,

    // Taxation Domain
    App\Domains\Taxation\Providers\TaxationServiceProvider::class,

    // HR & Payroll Domain
    App\Domains\HR\Providers\HRServiceProvider::class,

    // Projects & Tasks Domain
    App\Domains\Projects\Providers\ProjectsServiceProvider::class,

    // CRM & Support Domain
    App\Domains\CRM\Providers\CRMServiceProvider::class,

    // Support Domain
    App\Domains\Support\Providers\SupportServiceProvider::class,

    // E-Commerce Integration Domain
    App\Domains\Ecommerce\Providers\EcommerceServiceProvider::class,

    // Email & Communication Domain
    App\Domains\Email\Providers\EmailServiceProvider::class,

    // Payments & Banking Domain
    App\Domains\Payments\Providers\PaymentsServiceProvider::class,
];
