<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الدفعة
 * يمثل المدفوعات المستلمة والمدفوعة
 */
class Payment extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'payment_number',
        'customer_id',
        'invoice_id',
        'payment_date',
        'amount',
        'currency',
        'exchange_rate',
        'payment_method',
        'reference',
        'bank_account_id',
        'status',
        'notes',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * العلاقة مع الحساب البنكي
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * توليد رقم الدفعة
     */
    public static function generatePaymentNumber(): string
    {
        $lastPayment = self::orderBy('payment_number', 'desc')->first();
        
        if (!$lastPayment) {
            return 'PAY-000001';
        }

        $lastNumber = (int) substr($lastPayment->payment_number, -6);
        $newNumber = $lastNumber + 1;

        return 'PAY-' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}
