<?php

namespace App\Domains\CRM\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\CRM\Models\Customer;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أمان العملاء
 * Customer Security Policy
 */
class CustomerPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي عميل
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_customers') || 
               $user->hasRole(['admin', 'sales_manager', 'sales_rep']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض العميل
     */
    public function view(Employee $user, Customer $customer): bool
    {
        // المدراء يمكنهم رؤية جميع العملاء
        if ($user->hasRole(['admin', 'sales_manager'])) {
            return true;
        }

        // مندوب المبيعات يمكنه رؤية عملائه المخصصين له فقط
        if ($user->hasRole('sales_rep')) {
            return $customer->assigned_to === $user->id;
        }

        // الأقسام الأخرى حسب الصلاحيات
        return $user->hasPermissionTo('view_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء عميل
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_customers') || 
               $user->hasRole(['admin', 'sales_manager', 'sales_rep']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث العميل
     */
    public function update(Employee $user, Customer $customer): bool
    {
        // المدراء يمكنهم تحديث جميع العملاء
        if ($user->hasRole(['admin', 'sales_manager'])) {
            return true;
        }

        // مندوب المبيعات يمكنه تحديث عملائه المخصصين له فقط
        if ($user->hasRole('sales_rep')) {
            return $customer->assigned_to === $user->id;
        }

        return $user->hasPermissionTo('update_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف العميل
     */
    public function delete(Employee $user, Customer $customer): bool
    {
        // فقط المدراء يمكنهم حذف العملاء
        if ($user->hasRole(['admin', 'sales_manager'])) {
            return true;
        }

        return $user->hasPermissionTo('delete_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استعادة العميل
     */
    public function restore(Employee $user, Customer $customer): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('restore_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف العميل نهائياً
     */
    public function forceDelete(Employee $user, Customer $customer): bool
    {
        return $user->hasRole('admin') || 
               $user->hasPermissionTo('force_delete_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات الحساسة
     */
    public function viewSensitiveData(Employee $user, Customer $customer): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('view_sensitive_customer_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير بيانات العملاء
     */
    public function export(Employee $user): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('export_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد بيانات العملاء
     */
    public function import(Employee $user): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('import_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه دمج العملاء
     */
    public function merge(Employee $user): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('merge_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل تخصيص العميل
     */
    public function reassign(Employee $user, Customer $customer): bool
    {
        return $user->hasRole(['admin', 'sales_manager']) || 
               $user->hasPermissionTo('reassign_customers');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التحليلات المتقدمة
     */
    public function viewAnalytics(Employee $user, Customer $customer): bool
    {
        // المدراء يمكنهم رؤية جميع التحليلات
        if ($user->hasRole(['admin', 'sales_manager'])) {
            return true;
        }

        // مندوب المبيعات يمكنه رؤية تحليلات عملائه فقط
        if ($user->hasRole('sales_rep')) {
            return $customer->assigned_to === $user->id;
        }

        return $user->hasPermissionTo('view_customer_analytics');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة الشرائح
     */
    public function manageSegments(Employee $user): bool
    {
        return $user->hasRole(['admin', 'sales_manager', 'marketing_manager']) || 
               $user->hasPermissionTo('manage_customer_segments');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض ملف العميل 360°
     */
    public function viewProfile360(Employee $user, Customer $customer): bool
    {
        // المدراء يمكنهم رؤية الملف الكامل
        if ($user->hasRole(['admin', 'sales_manager'])) {
            return true;
        }

        // مندوب المبيعات يمكنه رؤية ملف عملائه المخصصين له
        if ($user->hasRole('sales_rep')) {
            return $customer->assigned_to === $user->id;
        }

        // أقسام أخرى حسب الصلاحيات
        return $user->hasPermissionTo('view_customer_profile_360');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تشغيل الأتمتة
     */
    public function runAutomation(Employee $user): bool
    {
        return $user->hasRole(['admin', 'sales_manager', 'marketing_manager']) || 
               $user->hasPermissionTo('run_customer_automation');
    }
}
