<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Domain-Driven Design Configuration
    |--------------------------------------------------------------------------
    |
    | تكوين الهندسة المعمارية القائمة على المجالات (DDD)
    | Modular Monolith with future Microservices capability
    |
    */

    'architecture_pattern' => 'modular_monolith',
    'microservices_ready' => true,

    /*
    |--------------------------------------------------------------------------
    | Domain Definitions
    |--------------------------------------------------------------------------
    |
    | تعريف جميع المجالات (Domains) في النظام
    |
    */
    'domains' => [
        'accounting' => [
            'name' => 'Accounting',
            'description' => 'نظام المحاسبة والمالية',
            'namespace' => 'App\\Domains\\Accounting',
            'path' => 'app/Domains/Accounting',
            'service_provider' => 'App\\Domains\\Accounting\\Providers\\AccountingServiceProvider',
            'api_prefix' => 'accounting',
            'web_prefix' => 'accounting',
            'database_prefix' => 'acc_',
            'cache_prefix' => 'accounting:',
            'queue_prefix' => 'accounting',
            'dependencies' => ['shared', 'hr'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'general_ledger',
                'accounts_payable',
                'accounts_receivable',
                'financial_reporting',
                'budgeting',
                'cost_accounting',
            ],
        ],

        'taxation' => [
            'name' => 'Taxation',
            'description' => 'نظام الضرائب والامتثال الضريبي',
            'namespace' => 'App\\Domains\\Taxation',
            'path' => 'app/Domains/Taxation',
            'service_provider' => 'App\\Domains\\Taxation\\Providers\\TaxationServiceProvider',
            'api_prefix' => 'taxation',
            'web_prefix' => 'taxation',
            'database_prefix' => 'tax_',
            'cache_prefix' => 'taxation:',
            'queue_prefix' => 'taxation',
            'dependencies' => ['shared', 'accounting'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'vat_management',
                'tax_calculation',
                'tax_reporting',
                'compliance_monitoring',
                'e_invoicing',
            ],
        ],

        'hr' => [
            'name' => 'HR & Payroll',
            'description' => 'نظام الموارد البشرية والرواتب',
            'namespace' => 'App\\Domains\\HR',
            'path' => 'app/Domains/HR',
            'service_provider' => 'App\\Domains\\HR\\Providers\\HRServiceProvider',
            'api_prefix' => 'hr',
            'web_prefix' => 'hr',
            'database_prefix' => 'hr_',
            'cache_prefix' => 'hr:',
            'queue_prefix' => 'hr',
            'dependencies' => ['shared'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'employee_management',
                'payroll_processing',
                'attendance_tracking',
                'performance_management',
                'recruitment',
                'training_development',
            ],
        ],

        'projects' => [
            'name' => 'Projects & Tasks',
            'description' => 'نظام إدارة المشاريع والمهام',
            'namespace' => 'App\\Domains\\Projects',
            'path' => 'app/Domains/Projects',
            'service_provider' => 'App\\Domains\\Projects\\Providers\\ProjectsServiceProvider',
            'api_prefix' => 'projects',
            'web_prefix' => 'projects',
            'database_prefix' => 'proj_',
            'cache_prefix' => 'projects:',
            'queue_prefix' => 'projects',
            'dependencies' => ['shared', 'hr', 'crm'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'project_management',
                'task_management',
                'resource_allocation',
                'time_tracking',
                'project_accounting',
                'collaboration',
            ],
        ],

        'crm' => [
            'name' => 'Support & CRM',
            'description' => 'نظام إدارة العملاء والدعم الفني',
            'namespace' => 'App\\Domains\\CRM',
            'path' => 'app/Domains/CRM',
            'service_provider' => 'App\\Domains\\CRM\\Providers\\CRMServiceProvider',
            'api_prefix' => 'crm',
            'web_prefix' => 'crm',
            'database_prefix' => 'crm_',
            'cache_prefix' => 'crm:',
            'queue_prefix' => 'crm',
            'dependencies' => ['shared', 'hr'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'customer_management',
                'sales_automation',
                'marketing_automation',
                'support_ticketing',
                'customer_analytics',
                'satisfaction_management',
            ],
        ],

        'support' => [
            'name' => 'Support',
            'description' => 'نظام الدعم الفني وخدمة العملاء',
            'namespace' => 'App\\Domains\\Support',
            'path' => 'app/Domains/Support',
            'service_provider' => 'App\\Domains\\Support\\Providers\\SupportServiceProvider',
            'api_prefix' => 'support',
            'web_prefix' => 'support',
            'database_prefix' => 'sup_',
            'cache_prefix' => 'support:',
            'queue_prefix' => 'support',
            'dependencies' => ['shared', 'hr', 'crm'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'ticket_management',
                'knowledge_base',
                'live_chat',
                'escalation_management',
                'sla_monitoring',
                'customer_feedback',
            ],
        ],

        'ecommerce' => [
            'name' => 'E-Commerce Integration',
            'description' => 'تكامل التجارة الإلكترونية',
            'namespace' => 'App\\Domains\\Ecommerce',
            'path' => 'app/Domains/Ecommerce',
            'service_provider' => 'App\\Domains\\Ecommerce\\Providers\\EcommerceServiceProvider',
            'api_prefix' => 'ecommerce',
            'web_prefix' => 'ecommerce',
            'database_prefix' => 'ecom_',
            'cache_prefix' => 'ecommerce:',
            'queue_prefix' => 'ecommerce',
            'dependencies' => ['shared', 'crm', 'accounting'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'product_catalog',
                'order_management',
                'inventory_management',
                'payment_processing',
                'shipping_logistics',
                'marketplace_integration',
            ],
        ],

        'email' => [
            'name' => 'Email & Communication',
            'description' => 'نظام البريد الإلكتروني والتواصل',
            'namespace' => 'App\\Domains\\Email',
            'path' => 'app/Domains/Email',
            'service_provider' => 'App\\Domains\\Email\\Providers\\EmailServiceProvider',
            'api_prefix' => 'email',
            'web_prefix' => 'email',
            'database_prefix' => 'email_',
            'cache_prefix' => 'email:',
            'queue_prefix' => 'email',
            'dependencies' => ['shared'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'email_management',
                'template_engine',
                'campaign_management',
                'delivery_tracking',
                'spam_filtering',
                'integration_apis',
            ],
        ],

        'payments' => [
            'name' => 'Payments & Banking',
            'description' => 'نظام المدفوعات والخدمات المصرفية',
            'namespace' => 'App\\Domains\\Payments',
            'path' => 'app/Domains/Payments',
            'service_provider' => 'App\\Domains\\Payments\\Providers\\PaymentsServiceProvider',
            'api_prefix' => 'payments',
            'web_prefix' => 'payments',
            'database_prefix' => 'pay_',
            'cache_prefix' => 'payments:',
            'queue_prefix' => 'payments',
            'dependencies' => ['shared', 'accounting'],
            'microservice_ready' => true,
            'bounded_contexts' => [
                'payment_processing',
                'bank_integration',
                'reconciliation',
                'fraud_detection',
                'compliance_monitoring',
                'reporting',
            ],
        ],

        'shared' => [
            'name' => 'Shared',
            'description' => 'المكونات المشتركة والأساسية',
            'namespace' => 'App\\Domains\\Shared',
            'path' => 'app/Domains/Shared',
            'service_provider' => 'App\\Domains\\Shared\\Providers\\SharedServiceProvider',
            'api_prefix' => 'shared',
            'web_prefix' => 'shared',
            'database_prefix' => 'shared_',
            'cache_prefix' => 'shared:',
            'queue_prefix' => 'shared',
            'dependencies' => [],
            'microservice_ready' => false,
            'bounded_contexts' => [
                'authentication',
                'authorization',
                'audit_logging',
                'file_management',
                'notification_system',
                'localization',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cross-Domain Communication
    |--------------------------------------------------------------------------
    |
    | تكوين التواصل بين المجالات
    |
    */
    'communication' => [
        'event_bus' => [
            'enabled' => true,
            'driver' => 'database', // database, redis, rabbitmq
            'async' => true,
        ],
        'api_gateway' => [
            'enabled' => false, // سيتم تفعيله عند التحول للـ Microservices
            'rate_limiting' => true,
            'authentication' => 'sanctum',
        ],
        'service_discovery' => [
            'enabled' => false, // للـ Microservices المستقبلية
            'registry' => 'consul',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Domain Isolation Rules
    |--------------------------------------------------------------------------
    |
    | قواعد عزل المجالات
    |
    */
    'isolation' => [
        'database_separation' => false, // حالياً قاعدة بيانات واحدة
        'cache_separation' => true,
        'queue_separation' => true,
        'log_separation' => true,
        'config_separation' => true,
        'route_separation' => true,
        'middleware_separation' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Microservices Migration Settings
    |--------------------------------------------------------------------------
    |
    | إعدادات التحول للـ Microservices
    |
    */
    'microservices' => [
        'migration_strategy' => 'strangler_fig', // strangler_fig, big_bang, parallel_run
        'api_versioning' => true,
        'backward_compatibility' => true,
        'gradual_migration' => true,
        'data_consistency' => [
            'pattern' => 'eventual_consistency',
            'saga_pattern' => true,
            'event_sourcing' => false,
        ],
        'monitoring' => [
            'distributed_tracing' => true,
            'service_mesh' => false,
            'health_checks' => true,
            'metrics_collection' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Domain Security
    |--------------------------------------------------------------------------
    |
    | إعدادات أمان المجالات
    |
    */
    'security' => [
        'domain_boundaries' => true,
        'access_control' => [
            'rbac' => true,
            'abac' => false,
            'domain_specific_permissions' => true,
        ],
        'data_encryption' => [
            'at_rest' => true,
            'in_transit' => true,
            'domain_specific_keys' => false,
        ],
        'audit_logging' => [
            'cross_domain_calls' => true,
            'data_access' => true,
            'configuration_changes' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    |
    | تحسين الأداء
    |
    */
    'performance' => [
        'caching' => [
            'domain_specific_cache' => true,
            'cache_invalidation' => 'event_driven',
            'distributed_cache' => false,
        ],
        'database' => [
            'connection_pooling' => true,
            'read_replicas' => false,
            'sharding' => false,
        ],
        'async_processing' => [
            'domain_specific_queues' => true,
            'priority_queues' => true,
            'dead_letter_queues' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Tools
    |--------------------------------------------------------------------------
    |
    | أدوات التطوير
    |
    */
    'development' => [
        'code_generation' => [
            'enabled' => true,
            'templates_path' => 'stubs/domain',
        ],
        'testing' => [
            'domain_isolation' => true,
            'integration_tests' => true,
            'contract_testing' => false,
        ],
        'documentation' => [
            'auto_generation' => true,
            'api_docs' => true,
            'domain_maps' => true,
        ],
    ],
];
