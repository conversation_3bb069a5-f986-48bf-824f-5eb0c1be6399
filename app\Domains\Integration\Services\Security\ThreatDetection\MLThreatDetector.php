<?php

namespace App\Domains\Integration\Services\Security\ThreatDetection;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Machine Learning Threat Detector
 * 
 * Advanced threat detection using machine learning algorithms
 * and behavioral analysis
 */
class MLThreatDetector
{
    protected array $config;
    protected array $models;
    protected array $features;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'anomaly_threshold' => 0.7,
            'learning_rate' => 0.01,
            'feature_window' => 3600, // 1 hour
            'model_update_interval' => 86400, // 24 hours
            'min_samples_for_training' => 1000,
        ], $config);

        $this->loadModels();
        $this->initializeFeatures();
    }

    /**
     * Detect threats using ML algorithms
     */
    public function detectThreats(array $request, array $context = []): array
    {
        try {
            // Extract features from request
            $features = $this->extractFeatures($request, $context);
            
            // Run through different detection models
            $threats = [];
            
            // Anomaly detection
            $anomalyScore = $this->detectAnomalies($features);
            if ($anomalyScore > $this->config['anomaly_threshold']) {
                $threats[] = [
                    'type' => 'anomaly',
                    'confidence' => $anomalyScore,
                    'description' => 'Anomalous behavior detected',
                    'features' => $features,
                ];
            }

            // Pattern-based detection
            $patternThreats = $this->detectPatterns($features, $context);
            $threats = array_merge($threats, $patternThreats);

            // Behavioral analysis
            $behavioralThreats = $this->analyzeBehavior($request, $context);
            $threats = array_merge($threats, $behavioralThreats);

            // Update learning models
            $this->updateModels($features, $threats);

            return $threats;

        } catch (\Exception $e) {
            Log::error('ML threat detection failed', [
                'error' => $e->getMessage(),
                'request_id' => $request['request_id'] ?? 'unknown',
            ]);

            return [];
        }
    }

    /**
     * Extract features from request for ML analysis
     */
    protected function extractFeatures(array $request, array $context): array
    {
        $sourceIp = $request['source_ip'] ?? 'unknown';
        $userAgent = $request['user_agent'] ?? 'unknown';
        $timestamp = now();

        return [
            // Request characteristics
            'request_size' => strlen(json_encode($request)),
            'header_count' => count($request['headers'] ?? []),
            'param_count' => count($request['parameters'] ?? []),
            'method' => $request['method'] ?? 'GET',
            'path_length' => strlen($request['path'] ?? ''),
            
            // Temporal features
            'hour_of_day' => $timestamp->hour,
            'day_of_week' => $timestamp->dayOfWeek,
            'is_weekend' => $timestamp->isWeekend(),
            
            // IP-based features
            'ip_reputation' => $this->getIpReputation($sourceIp),
            'ip_geolocation' => $this->getIpGeolocation($sourceIp),
            'ip_request_frequency' => $this->getIpRequestFrequency($sourceIp),
            'ip_error_rate' => $this->getIpErrorRate($sourceIp),
            
            // User agent features
            'ua_entropy' => $this->calculateEntropy($userAgent),
            'ua_length' => strlen($userAgent),
            'ua_is_bot' => $this->isBotUserAgent($userAgent),
            
            // Session features
            'session_duration' => $context['session_duration'] ?? 0,
            'session_request_count' => $context['session_request_count'] ?? 1,
            'session_unique_endpoints' => $context['session_unique_endpoints'] ?? 1,
            
            // Content features
            'has_sql_patterns' => $this->hasSqlInjectionPatterns($request),
            'has_xss_patterns' => $this->hasXssPatterns($request),
            'has_suspicious_keywords' => $this->hasSuspiciousKeywords($request),
        ];
    }

    /**
     * Detect anomalies using statistical methods
     */
    protected function detectAnomalies(array $features): float
    {
        $anomalyScore = 0.0;
        $weights = $this->models['anomaly_weights'] ?? [];

        foreach ($features as $feature => $value) {
            if (isset($weights[$feature])) {
                $normalizedValue = $this->normalizeFeature($feature, $value);
                $expectedValue = $this->getExpectedValue($feature);
                $deviation = abs($normalizedValue - $expectedValue);
                
                $anomalyScore += $deviation * $weights[$feature];
            }
        }

        return min(1.0, $anomalyScore);
    }

    /**
     * Detect known attack patterns
     */
    protected function detectPatterns(array $features, array $context): array
    {
        $threats = [];
        $patterns = $this->models['threat_patterns'] ?? [];

        foreach ($patterns as $pattern) {
            $matchScore = $this->calculatePatternMatch($features, $pattern);
            
            if ($matchScore > $pattern['threshold']) {
                $threats[] = [
                    'type' => $pattern['type'],
                    'confidence' => $matchScore,
                    'description' => $pattern['description'],
                    'pattern_id' => $pattern['id'],
                ];
            }
        }

        return $threats;
    }

    /**
     * Analyze behavioral patterns
     */
    protected function analyzeBehavior(array $request, array $context): array
    {
        $threats = [];
        $sourceIp = $request['source_ip'] ?? 'unknown';

        // Check for rapid-fire requests
        $requestFrequency = $this->getRecentRequestFrequency($sourceIp);
        if ($requestFrequency > 100) { // More than 100 requests per minute
            $threats[] = [
                'type' => 'high_frequency',
                'confidence' => min(1.0, $requestFrequency / 200),
                'description' => 'High frequency requests detected',
                'frequency' => $requestFrequency,
            ];
        }

        // Check for endpoint scanning
        $uniqueEndpoints = $this->getRecentUniqueEndpoints($sourceIp);
        if ($uniqueEndpoints > 50) { // More than 50 unique endpoints in short time
            $threats[] = [
                'type' => 'endpoint_scanning',
                'confidence' => min(1.0, $uniqueEndpoints / 100),
                'description' => 'Endpoint scanning behavior detected',
                'unique_endpoints' => $uniqueEndpoints,
            ];
        }

        // Check for error rate spikes
        $errorRate = $this->getRecentErrorRate($sourceIp);
        if ($errorRate > 0.5) { // More than 50% error rate
            $threats[] = [
                'type' => 'error_spike',
                'confidence' => $errorRate,
                'description' => 'High error rate detected',
                'error_rate' => $errorRate,
            ];
        }

        return $threats;
    }

    /**
     * Update ML models with new data
     */
    protected function updateModels(array $features, array $threats): void
    {
        // Update feature statistics
        foreach ($features as $feature => $value) {
            $this->updateFeatureStatistics($feature, $value);
        }

        // Update threat patterns if threats were detected
        if (!empty($threats)) {
            $this->updateThreatPatterns($features, $threats);
        }

        // Periodically retrain models
        if ($this->shouldRetrainModels()) {
            $this->retrainModels();
        }
    }

    /**
     * Load pre-trained models
     */
    protected function loadModels(): void
    {
        $this->models = Cache::remember('ml_threat_models', 3600, function () {
            return [
                'anomaly_weights' => $this->getDefaultAnomalyWeights(),
                'threat_patterns' => $this->getDefaultThreatPatterns(),
                'feature_stats' => $this->getDefaultFeatureStats(),
            ];
        });
    }

    /**
     * Initialize feature extractors
     */
    protected function initializeFeatures(): void
    {
        $this->features = [
            'numerical' => [
                'request_size', 'header_count', 'param_count', 'path_length',
                'ip_request_frequency', 'ip_error_rate', 'ua_entropy', 'ua_length',
                'session_duration', 'session_request_count', 'session_unique_endpoints'
            ],
            'categorical' => [
                'method', 'hour_of_day', 'day_of_week', 'ip_geolocation'
            ],
            'boolean' => [
                'is_weekend', 'ua_is_bot', 'has_sql_patterns', 'has_xss_patterns', 'has_suspicious_keywords'
            ],
        ];
    }

    // Helper methods
    protected function getIpReputation(string $ip): float
    {
        return Cache::remember("ip_reputation:{$ip}", 3600, function () {
            // Simplified reputation scoring
            return 0.5; // Neutral reputation
        });
    }

    protected function getIpGeolocation(string $ip): string
    {
        return Cache::remember("ip_geo:{$ip}", 86400, function () {
            return 'unknown';
        });
    }

    protected function getIpRequestFrequency(string $ip): float
    {
        $key = "ip_freq:{$ip}:" . now()->format('Y-m-d-H-i');
        return (float) Cache::get($key, 0);
    }

    protected function getIpErrorRate(string $ip): float
    {
        $requests = Cache::get("ip_requests:{$ip}", 0);
        $errors = Cache::get("ip_errors:{$ip}", 0);
        return $requests > 0 ? $errors / $requests : 0;
    }

    protected function calculateEntropy(string $text): float
    {
        $chars = array_count_values(str_split($text));
        $length = strlen($text);
        $entropy = 0;

        foreach ($chars as $count) {
            $probability = $count / $length;
            $entropy -= $probability * log($probability, 2);
        }

        return $entropy;
    }

    protected function isBotUserAgent(string $userAgent): bool
    {
        $botPatterns = ['bot', 'crawler', 'spider', 'scraper'];
        return str_contains(strtolower($userAgent), $botPatterns);
    }

    protected function hasSqlInjectionPatterns(array $request): bool
    {
        $sqlPatterns = ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', ';'];
        $content = json_encode($request);
        
        foreach ($sqlPatterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }

    protected function hasXssPatterns(array $request): bool
    {
        $xssPatterns = ['<script', 'javascript:', 'onload=', 'onerror=', 'alert('];
        $content = json_encode($request);
        
        foreach ($xssPatterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }

    protected function hasSuspiciousKeywords(array $request): bool
    {
        $suspiciousKeywords = ['admin', 'root', 'password', 'config', 'backup'];
        $content = strtolower(json_encode($request));
        
        foreach ($suspiciousKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }

    protected function normalizeFeature(string $feature, $value): float
    {
        $stats = $this->models['feature_stats'][$feature] ?? ['min' => 0, 'max' => 1];
        $min = $stats['min'];
        $max = $stats['max'];
        
        if ($max <= $min) {
            return 0.5;
        }
        
        return ($value - $min) / ($max - $min);
    }

    protected function getExpectedValue(string $feature): float
    {
        $stats = $this->models['feature_stats'][$feature] ?? ['mean' => 0.5];
        return $stats['mean'];
    }

    protected function calculatePatternMatch(array $features, array $pattern): float
    {
        $score = 0.0;
        $totalWeight = 0.0;

        foreach ($pattern['features'] as $feature => $expectedValue) {
            if (isset($features[$feature])) {
                $weight = $pattern['weights'][$feature] ?? 1.0;
                $similarity = 1 - abs($features[$feature] - $expectedValue);
                $score += $similarity * $weight;
                $totalWeight += $weight;
            }
        }

        return $totalWeight > 0 ? $score / $totalWeight : 0.0;
    }

    protected function getRecentRequestFrequency(string $ip): int
    {
        return (int) Cache::get("recent_freq:{$ip}", 0);
    }

    protected function getRecentUniqueEndpoints(string $ip): int
    {
        $endpoints = Cache::get("recent_endpoints:{$ip}", []);
        return count($endpoints);
    }

    protected function getRecentErrorRate(string $ip): float
    {
        $requests = Cache::get("recent_requests:{$ip}", 0);
        $errors = Cache::get("recent_errors:{$ip}", 0);
        return $requests > 0 ? $errors / $requests : 0;
    }

    protected function updateFeatureStatistics(string $feature, $value): void
    {
        // Update running statistics for the feature
        $key = "feature_stats:{$feature}";
        $stats = Cache::get($key, ['count' => 0, 'sum' => 0, 'sum_sq' => 0, 'min' => $value, 'max' => $value]);
        
        $stats['count']++;
        $stats['sum'] += $value;
        $stats['sum_sq'] += $value * $value;
        $stats['min'] = min($stats['min'], $value);
        $stats['max'] = max($stats['max'], $value);
        $stats['mean'] = $stats['sum'] / $stats['count'];
        
        Cache::put($key, $stats, 86400);
    }

    protected function updateThreatPatterns(array $features, array $threats): void
    {
        // Update threat pattern database
        foreach ($threats as $threat) {
            $patternKey = "threat_pattern:{$threat['type']}";
            $pattern = Cache::get($patternKey, []);
            
            // Update pattern with new features
            foreach ($features as $feature => $value) {
                if (!isset($pattern['features'][$feature])) {
                    $pattern['features'][$feature] = [];
                }
                $pattern['features'][$feature][] = $value;
            }
            
            Cache::put($patternKey, $pattern, 86400);
        }
    }

    protected function shouldRetrainModels(): bool
    {
        $lastRetrain = Cache::get('last_model_retrain', 0);
        return (time() - $lastRetrain) > $this->config['model_update_interval'];
    }

    protected function retrainModels(): void
    {
        // Placeholder for model retraining logic
        Cache::put('last_model_retrain', time(), 86400);
    }

    protected function getDefaultAnomalyWeights(): array
    {
        return [
            'request_size' => 0.1,
            'header_count' => 0.05,
            'param_count' => 0.05,
            'ip_request_frequency' => 0.3,
            'ip_error_rate' => 0.2,
            'ua_entropy' => 0.1,
            'session_request_count' => 0.2,
        ];
    }

    protected function getDefaultThreatPatterns(): array
    {
        return [
            [
                'id' => 'sql_injection',
                'type' => 'sql_injection',
                'description' => 'SQL injection attempt',
                'threshold' => 0.8,
                'features' => ['has_sql_patterns' => 1],
                'weights' => ['has_sql_patterns' => 1.0],
            ],
            [
                'id' => 'xss_attack',
                'type' => 'xss',
                'description' => 'Cross-site scripting attempt',
                'threshold' => 0.8,
                'features' => ['has_xss_patterns' => 1],
                'weights' => ['has_xss_patterns' => 1.0],
            ],
        ];
    }

    protected function getDefaultFeatureStats(): array
    {
        return [
            'request_size' => ['min' => 0, 'max' => 10000, 'mean' => 1000],
            'header_count' => ['min' => 0, 'max' => 50, 'mean' => 10],
            'param_count' => ['min' => 0, 'max' => 100, 'mean' => 5],
            'ip_request_frequency' => ['min' => 0, 'max' => 1000, 'mean' => 10],
            'ip_error_rate' => ['min' => 0, 'max' => 1, 'mean' => 0.05],
        ];
    }
}
