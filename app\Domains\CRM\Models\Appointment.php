<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج الموعد - Appointment
 */
class Appointment extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'customer_id',
        'contact_id',
        'opportunity_id',
        'assigned_to',
        'title',
        'description',
        'type',
        'status',
        'start_time',
        'end_time',
        'location',
        'meeting_url',
        'attendees',
        'notes',
        'outcome',
        'follow_up_required',
        'follow_up_date',
        'reminder_sent',
        'calendar_event_id',
        'metadata',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'datetime',
        'reminder_sent' => 'boolean',
        'attendees' => 'array',
        'metadata' => 'array',
    ];

    const TYPES = [
        'meeting' => 'اجتماع',
        'call' => 'مكالمة',
        'demo' => 'عرض توضيحي',
        'presentation' => 'عرض تقديمي',
        'consultation' => 'استشارة',
        'follow_up' => 'متابعة',
        'other' => 'أخرى',
    ];

    const STATUSES = [
        'scheduled' => 'مجدول',
        'confirmed' => 'مؤكد',
        'in_progress' => 'قيد التنفيذ',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي',
        'no_show' => 'لم يحضر',
        'rescheduled' => 'تم إعادة الجدولة',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    public function getDurationMinutesAttribute(): int
    {
        return $this->start_time->diffInMinutes($this->end_time);
    }

    public function getIsUpcomingAttribute(): bool
    {
        return $this->start_time->isFuture();
    }

    public function getIsTodayAttribute(): bool
    {
        return $this->start_time->isToday();
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now());
    }

    public function scopeToday($query)
    {
        return $query->whereDate('start_time', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('start_time', [now()->startOfWeek(), now()->endOfWeek()]);
    }
}
