<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Services\AdvancedFinancialAnalyticsService;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Report Controller
 * تحكم في التقارير المالية
 */
class ReportController extends Controller
{
    protected AdvancedFinancialAnalyticsService $analyticsService;

    public function __construct(AdvancedFinancialAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
        $this->middleware('auth');
    }

    /**
     * عرض قائمة التقارير المتاحة
     */
    public function index(): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $reports = [
            'financial_statements' => [
                'balance_sheet' => [
                    'name' => 'الميزانية العمومية',
                    'description' => 'تقرير الأصول والخصوم وحقوق الملكية',
                    'endpoint' => '/api/accounting/reports/balance-sheet',
                ],
                'income_statement' => [
                    'name' => 'قائمة الدخل',
                    'description' => 'تقرير الإيرادات والمصروفات',
                    'endpoint' => '/api/accounting/reports/income-statement',
                ],
                'cash_flow' => [
                    'name' => 'قائمة التدفقات النقدية',
                    'description' => 'تقرير التدفقات النقدية الداخلة والخارجة',
                    'endpoint' => '/api/accounting/reports/cash-flow',
                ],
                'trial_balance' => [
                    'name' => 'ميزان المراجعة',
                    'description' => 'تقرير أرصدة الحسابات',
                    'endpoint' => '/api/accounting/reports/trial-balance',
                ],
            ],
            'detailed_reports' => [
                'general_ledger' => [
                    'name' => 'دفتر الأستاذ العام',
                    'description' => 'تفاصيل حركة الحسابات',
                    'endpoint' => '/api/accounting/reports/general-ledger',
                ],
                'accounts_receivable' => [
                    'name' => 'تقرير الحسابات المدينة',
                    'description' => 'تفاصيل المبالغ المستحقة من العملاء',
                    'endpoint' => '/api/accounting/reports/accounts-receivable',
                ],
                'accounts_payable' => [
                    'name' => 'تقرير الحسابات الدائنة',
                    'description' => 'تفاصيل المبالغ المستحقة للموردين',
                    'endpoint' => '/api/accounting/reports/accounts-payable',
                ],
            ],
            'analytical_reports' => [
                'aged_receivables' => [
                    'name' => 'تقرير أعمار الديون',
                    'description' => 'تحليل أعمار المبالغ المستحقة',
                    'endpoint' => '/api/accounting/reports/aged-receivables',
                ],
                'profit_loss' => [
                    'name' => 'تقرير الأرباح والخسائر',
                    'description' => 'تحليل الربحية',
                    'endpoint' => '/api/accounting/reports/profit-loss',
                ],
                'budget_variance' => [
                    'name' => 'تقرير انحراف الميزانية',
                    'description' => 'مقارنة الفعلي بالمخطط',
                    'endpoint' => '/api/accounting/reports/budget-variance',
                ],
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $reports,
            'message' => 'تم جلب قائمة التقارير بنجاح'
        ]);
    }

    /**
     * تقرير الميزانية العمومية
     */
    public function balanceSheet(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $compareDate = $request->compare_date;

        $cacheKey = "balance_sheet_{$asOfDate}" . ($compareDate ? "_{$compareDate}" : '');
        
        $balanceSheet = Cache::remember($cacheKey, 1800, function () use ($asOfDate, $compareDate) {
            return $this->analyticsService->generateBalanceSheet($asOfDate, $compareDate);
        });

        return response()->json([
            'success' => true,
            'data' => $balanceSheet,
            'message' => 'تم إنشاء تقرير الميزانية العمومية بنجاح'
        ]);
    }

    /**
     * تقرير قائمة الدخل
     */
    public function incomeStatement(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');
        $comparePeriod = $request->compare_period;

        $cacheKey = "income_statement_{$dateFrom}_{$dateTo}" . ($comparePeriod ? "_{$comparePeriod}" : '');
        
        $incomeStatement = Cache::remember($cacheKey, 1800, function () use ($dateFrom, $dateTo, $comparePeriod) {
            return $this->analyticsService->generateIncomeStatement($dateFrom, $dateTo, $comparePeriod);
        });

        return response()->json([
            'success' => true,
            'data' => $incomeStatement,
            'message' => 'تم إنشاء تقرير قائمة الدخل بنجاح'
        ]);
    }

    /**
     * تقرير التدفقات النقدية
     */
    public function cashFlowStatement(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');
        $method = $request->method ?? 'indirect'; // direct or indirect

        $cacheKey = "cash_flow_{$dateFrom}_{$dateTo}_{$method}";
        
        $cashFlow = Cache::remember($cacheKey, 1800, function () use ($dateFrom, $dateTo, $method) {
            return $this->analyticsService->generateCashFlowStatement($dateFrom, $dateTo, $method);
        });

        return response()->json([
            'success' => true,
            'data' => $cashFlow,
            'message' => 'تم إنشاء تقرير التدفقات النقدية بنجاح'
        ]);
    }

    /**
     * تقرير ميزان المراجعة
     */
    public function trialBalance(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $includeZeroBalances = $request->boolean('include_zero_balances', false);

        $cacheKey = "trial_balance_{$asOfDate}_" . ($includeZeroBalances ? 'with_zeros' : 'no_zeros');
        
        $trialBalance = Cache::remember($cacheKey, 1800, function () use ($asOfDate, $includeZeroBalances) {
            return $this->analyticsService->generateTrialBalance($asOfDate, $includeZeroBalances);
        });

        return response()->json([
            'success' => true,
            'data' => $trialBalance,
            'message' => 'تم إنشاء تقرير ميزان المراجعة بنجاح'
        ]);
    }

    /**
     * تقرير دفتر الأستاذ العام
     */
    public function generalLedger(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $request->validate([
            'account_id' => 'nullable|exists:accounts,id',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        $accountId = $request->account_id;
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;

        $generalLedger = $this->analyticsService->generateGeneralLedger($accountId, $dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => $generalLedger,
            'message' => 'تم إنشاء تقرير دفتر الأستاذ العام بنجاح'
        ]);
    }

    /**
     * تقرير الحسابات المدينة
     */
    public function accountsReceivable(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $customerId = $request->customer_id;

        $accountsReceivable = $this->analyticsService->generateAccountsReceivableReport($asOfDate, $customerId);

        return response()->json([
            'success' => true,
            'data' => $accountsReceivable,
            'message' => 'تم إنشاء تقرير الحسابات المدينة بنجاح'
        ]);
    }

    /**
     * تقرير الحسابات الدائنة
     */
    public function accountsPayable(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $supplierId = $request->supplier_id;

        $accountsPayable = $this->analyticsService->generateAccountsPayableReport($asOfDate, $supplierId);

        return response()->json([
            'success' => true,
            'data' => $accountsPayable,
            'message' => 'تم إنشاء تقرير الحسابات الدائنة بنجاح'
        ]);
    }

    /**
     * تقرير أعمار الديون
     */
    public function agedReceivables(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $agingPeriods = $request->aging_periods ?? [30, 60, 90, 120];

        $agedReceivables = $this->analyticsService->generateAgedReceivablesReport($asOfDate, $agingPeriods);

        return response()->json([
            'success' => true,
            'data' => $agedReceivables,
            'message' => 'تم إنشاء تقرير أعمار الديون بنجاح'
        ]);
    }

    /**
     * تقرير أعمار الالتزامات
     */
    public function agedPayables(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $asOfDate = $request->as_of_date ?? now()->format('Y-m-d');
        $agingPeriods = $request->aging_periods ?? [30, 60, 90, 120];

        $agedPayables = $this->analyticsService->generateAgedPayablesReport($asOfDate, $agingPeriods);

        return response()->json([
            'success' => true,
            'data' => $agedPayables,
            'message' => 'تم إنشاء تقرير أعمار الالتزامات بنجاح'
        ]);
    }

    /**
     * تقرير الأرباح والخسائر
     */
    public function profitLoss(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');
        $groupBy = $request->group_by ?? 'month'; // month, quarter, year

        $profitLoss = $this->analyticsService->generateProfitLossReport($dateFrom, $dateTo, $groupBy);

        return response()->json([
            'success' => true,
            'data' => $profitLoss,
            'message' => 'تم إنشاء تقرير الأرباح والخسائر بنجاح'
        ]);
    }

    /**
     * تقرير انحراف الميزانية
     */
    public function budgetVariance(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $budgetId = $request->budget_id;
        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $budgetVariance = $this->analyticsService->generateBudgetVarianceReport($budgetId, $dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => $budgetVariance,
            'message' => 'تم إنشاء تقرير انحراف الميزانية بنجاح'
        ]);
    }

    /**
     * تقرير مخصص
     */
    public function customReport(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $request->validate([
            'report_name' => 'required|string|max:255',
            'accounts' => 'required|array',
            'accounts.*' => 'exists:accounts,id',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'group_by' => 'nullable|in:account,date,month,quarter,year',
            'filters' => 'nullable|array',
        ]);

        $customReport = $this->analyticsService->generateCustomReport([
            'name' => $request->report_name,
            'accounts' => $request->accounts,
            'date_from' => $request->date_from,
            'date_to' => $request->date_to,
            'group_by' => $request->group_by,
            'filters' => $request->filters ?? [],
        ]);

        return response()->json([
            'success' => true,
            'data' => $customReport,
            'message' => 'تم إنشاء التقرير المخصص بنجاح'
        ]);
    }

    /**
     * جدولة تقرير
     */
    public function scheduleReport(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $request->validate([
            'report_type' => 'required|string',
            'schedule_frequency' => 'required|in:daily,weekly,monthly,quarterly,yearly',
            'recipients' => 'required|array',
            'recipients.*' => 'email',
            'parameters' => 'nullable|array',
            'start_date' => 'required|date',
        ]);

        $scheduledReport = $this->analyticsService->scheduleReport([
            'report_type' => $request->report_type,
            'frequency' => $request->schedule_frequency,
            'recipients' => $request->recipients,
            'parameters' => $request->parameters ?? [],
            'start_date' => $request->start_date,
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'data' => $scheduledReport,
            'message' => 'تم جدولة التقرير بنجاح'
        ]);
    }

    /**
     * الحصول على التقارير المجدولة
     */
    public function getScheduledReports(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $scheduledReports = $this->analyticsService->getScheduledReports(auth()->id());

        return response()->json([
            'success' => true,
            'data' => $scheduledReports,
            'message' => 'تم جلب التقارير المجدولة بنجاح'
        ]);
    }

    /**
     * تصدير تقرير
     */
    public function exportReport(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $request->validate([
            'report_type' => 'required|string',
            'format' => 'required|in:pdf,excel,csv',
            'parameters' => 'nullable|array',
        ]);

        try {
            $exportResult = $this->analyticsService->exportReport(
                $request->report_type,
                $request->format,
                $request->parameters ?? []
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'download_url' => $exportResult['url'],
                    'filename' => $exportResult['filename'],
                    'file_size' => $exportResult['size'],
                ],
                'message' => 'تم تصدير التقرير بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على مؤشرات الأداء المالي
     */
    public function getFinancialKPIs(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $dateFrom = $request->date_from ?? now()->startOfYear()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $kpis = $this->analyticsService->calculateFinancialKPIs($dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => $kpis,
            'message' => 'تم جلب مؤشرات الأداء المالي بنجاح'
        ]);
    }

    /**
     * تحليل الاتجاهات المالية
     */
    public function getTrendAnalysis(Request $request): JsonResponse
    {
        $this->authorize('viewFinancialReports', Account::class);

        $request->validate([
            'metric' => 'required|in:revenue,expenses,profit,cash_flow',
            'period' => 'required|in:monthly,quarterly,yearly',
            'periods_count' => 'required|integer|min:2|max:24',
        ]);

        $trendAnalysis = $this->analyticsService->generateTrendAnalysis(
            $request->metric,
            $request->period,
            $request->periods_count
        );

        return response()->json([
            'success' => true,
            'data' => $trendAnalysis,
            'message' => 'تم إنشاء تحليل الاتجاهات بنجاح'
        ]);
    }
}
