<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج التقرير المالي
 * يمثل التقارير المالية المختلفة في النظام
 */
class FinancialReport extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'report_name',
        'report_type',
        'period_start',
        'period_end',
        'status',
        'data',
        'parameters',
        'generated_by',
        'generated_at',
        'file_path',
        'file_format',
        'is_public',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'data' => 'array',
        'parameters' => 'array',
        'generated_at' => 'datetime',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المستخدم المولد للتقرير
     */
    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'generated_by');
    }

    /**
     * الحصول على اسم التقرير المنسق
     */
    public function getFormattedNameAttribute(): string
    {
        $period = $this->period_start->format('M Y');
        if ($this->period_start->format('Y-m') !== $this->period_end->format('Y-m')) {
            $period = $this->period_start->format('M Y') . ' - ' . $this->period_end->format('M Y');
        }

        return "{$this->report_name} ({$period})";
    }

    /**
     * الحصول على حجم الملف
     */
    public function getFileSizeAttribute(): ?string
    {
        if (!$this->file_path || !file_exists(storage_path('app/' . $this->file_path))) {
            return null;
        }

        $bytes = filesize(storage_path('app/' . $this->file_path));
        
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * التحقق من انتهاء صلاحية التقرير
     */
    public function isExpired(): bool
    {
        // التقارير تنتهي صلاحيتها بعد 30 يوم
        return $this->generated_at->addDays(30)->isPast();
    }

    /**
     * توليد التقرير
     */
    public function generate(): bool
    {
        try {
            $data = $this->generateReportData();
            
            $this->update([
                'data' => $data,
                'status' => 'COMPLETED',
                'generated_at' => now(),
            ]);

            // توليد ملف PDF إذا كان مطلوباً
            if ($this->file_format === 'PDF') {
                $this->generatePDF();
            }

            return true;
        } catch (\Exception $e) {
            $this->update([
                'status' => 'FAILED',
                'notes' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * توليد بيانات التقرير
     */
    protected function generateReportData(): array
    {
        switch ($this->report_type) {
            case 'INCOME_STATEMENT':
                return $this->generateIncomeStatement();
            case 'BALANCE_SHEET':
                return $this->generateBalanceSheet();
            case 'CASH_FLOW':
                return $this->generateCashFlowStatement();
            case 'TRIAL_BALANCE':
                return $this->generateTrialBalance();
            case 'GENERAL_LEDGER':
                return $this->generateGeneralLedger();
            default:
                throw new \Exception('نوع التقرير غير مدعوم');
        }
    }

    /**
     * توليد قائمة الدخل
     */
    protected function generateIncomeStatement(): array
    {
        $revenue = $this->getAccountBalances(['REVENUE'], 'credit');
        $expenses = $this->getAccountBalances(['EXPENSE'], 'debit');
        $cogs = $this->getAccountBalances(['COST_OF_GOODS_SOLD'], 'debit');

        $totalRevenue = array_sum($revenue);
        $totalCOGS = array_sum($cogs);
        $totalExpenses = array_sum($expenses);

        $grossProfit = $totalRevenue - $totalCOGS;
        $netIncome = $grossProfit - $totalExpenses;

        return [
            'revenue' => [
                'accounts' => $revenue,
                'total' => $totalRevenue,
            ],
            'cost_of_goods_sold' => [
                'accounts' => $cogs,
                'total' => $totalCOGS,
            ],
            'gross_profit' => $grossProfit,
            'expenses' => [
                'accounts' => $expenses,
                'total' => $totalExpenses,
            ],
            'net_income' => $netIncome,
            'gross_profit_margin' => $totalRevenue > 0 ? ($grossProfit / $totalRevenue) * 100 : 0,
            'net_profit_margin' => $totalRevenue > 0 ? ($netIncome / $totalRevenue) * 100 : 0,
        ];
    }

    /**
     * توليد الميزانية العمومية
     */
    protected function generateBalanceSheet(): array
    {
        $assets = $this->getAccountBalances(['ASSET'], 'debit');
        $liabilities = $this->getAccountBalances(['LIABILITY'], 'credit');
        $equity = $this->getAccountBalances(['EQUITY'], 'credit');

        $totalAssets = array_sum($assets);
        $totalLiabilities = array_sum($liabilities);
        $totalEquity = array_sum($equity);

        return [
            'assets' => [
                'accounts' => $assets,
                'total' => $totalAssets,
            ],
            'liabilities' => [
                'accounts' => $liabilities,
                'total' => $totalLiabilities,
            ],
            'equity' => [
                'accounts' => $equity,
                'total' => $totalEquity,
            ],
            'total_liabilities_equity' => $totalLiabilities + $totalEquity,
            'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01,
        ];
    }

    /**
     * توليد قائمة التدفق النقدي
     */
    protected function generateCashFlowStatement(): array
    {
        // تطبيق مبسط - يمكن تطويره
        return [
            'operating_activities' => [
                'net_income' => 0,
                'adjustments' => [],
                'total' => 0,
            ],
            'investing_activities' => [
                'activities' => [],
                'total' => 0,
            ],
            'financing_activities' => [
                'activities' => [],
                'total' => 0,
            ],
            'net_change_in_cash' => 0,
        ];
    }

    /**
     * توليد ميزان المراجعة
     */
    protected function generateTrialBalance(): array
    {
        $accounts = Account::with(['journalEntryLines' => function ($query) {
            $query->whereHas('journalEntry', function ($q) {
                $q->where('status', 'POSTED')
                  ->whereBetween('entry_date', [$this->period_start, $this->period_end]);
            });
        }])->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $debitSum = $account->journalEntryLines->sum('debit_amount');
            $creditSum = $account->journalEntryLines->sum('credit_amount');

            if ($debitSum > 0 || $creditSum > 0) {
                $trialBalance[] = [
                    'account_code' => $account->code,
                    'account_name' => $account->name,
                    'debit_amount' => $debitSum,
                    'credit_amount' => $creditSum,
                ];

                $totalDebits += $debitSum;
                $totalCredits += $creditSum;
            }
        }

        return [
            'accounts' => $trialBalance,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
        ];
    }

    /**
     * توليد دفتر الأستاذ العام
     */
    protected function generateGeneralLedger(): array
    {
        $accountId = $this->parameters['account_id'] ?? null;
        
        $query = Account::query();
        if ($accountId) {
            $query->where('id', $accountId);
        }

        $accounts = $query->with(['journalEntryLines' => function ($q) {
            $q->whereHas('journalEntry', function ($query) {
                $query->where('status', 'POSTED')
                      ->whereBetween('entry_date', [$this->period_start, $this->period_end]);
            })->orderBy('created_at');
        }])->get();

        $ledger = [];
        foreach ($accounts as $account) {
            $balance = 0;
            $transactions = [];

            foreach ($account->journalEntryLines as $line) {
                $balance += $line->debit_amount - $line->credit_amount;
                
                $transactions[] = [
                    'date' => $line->journalEntry->entry_date,
                    'description' => $line->description,
                    'reference' => $line->journalEntry->entry_number,
                    'debit' => $line->debit_amount,
                    'credit' => $line->credit_amount,
                    'balance' => $balance,
                ];
            }

            if (!empty($transactions)) {
                $ledger[] = [
                    'account' => [
                        'code' => $account->code,
                        'name' => $account->name,
                    ],
                    'transactions' => $transactions,
                    'ending_balance' => $balance,
                ];
            }
        }

        return $ledger;
    }

    /**
     * الحصول على أرصدة الحسابات
     */
    protected function getAccountBalances(array $accountTypes, string $balanceType): array
    {
        $accounts = Account::whereIn('account_type', $accountTypes)
                          ->with(['journalEntryLines' => function ($query) {
                              $query->whereHas('journalEntry', function ($q) {
                                  $q->where('status', 'POSTED')
                                    ->whereBetween('entry_date', [$this->period_start, $this->period_end]);
                              });
                          }])
                          ->get();

        $balances = [];
        foreach ($accounts as $account) {
            $debitSum = $account->journalEntryLines->sum('debit_amount');
            $creditSum = $account->journalEntryLines->sum('credit_amount');
            
            $balance = $balanceType === 'debit' ? $debitSum - $creditSum : $creditSum - $debitSum;
            
            if ($balance != 0) {
                $balances[$account->name] = $balance;
            }
        }

        return $balances;
    }

    /**
     * توليد ملف PDF
     */
    protected function generatePDF(): void
    {
        // تطبيق توليد PDF - يمكن استخدام مكتبة مثل DomPDF
        $fileName = "report_{$this->id}_{$this->report_type}.pdf";
        $filePath = "reports/{$fileName}";
        
        // هنا يتم توليد PDF فعلياً
        
        $this->update(['file_path' => $filePath]);
    }

    /**
     * البحث في التقارير
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('report_name', 'LIKE', "%{$search}%")
              ->orWhere('report_type', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('report_type', $type);
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة التقارير العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * فلترة حسب الفترة
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('period_start', [$startDate, $endDate])
              ->orWhereBetween('period_end', [$startDate, $endDate]);
        });
    }
}
