<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج المنصب - إدارة المناصب والوصف الوظيفي
 */
class Position extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'title',
        'title_ar',
        'title_en',
        'title_fr',
        'code',
        'department_id',
        'level',
        'grade',
        'job_description',
        'job_description_ar',
        'job_description_en',
        'job_description_fr',
        'responsibilities',
        'requirements',
        'qualifications',
        'skills_required',
        'experience_required',
        'min_salary',
        'max_salary',
        'currency',
        'reports_to_position_id',
        'is_management',
        'is_active',
        'headcount_approved',
        'headcount_current',
        'metadata',
    ];

    protected $casts = [
        'responsibilities' => 'array',
        'requirements' => 'array',
        'qualifications' => 'array',
        'skills_required' => 'array',
        'min_salary' => 'decimal:2',
        'max_salary' => 'decimal:2',
        'is_management' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * مستويات المناصب
     */
    public const LEVELS = [
        'ENTRY' => 'مبتدئ',
        'JUNIOR' => 'مبتدئ متقدم',
        'INTERMEDIATE' => 'متوسط',
        'SENIOR' => 'كبير',
        'LEAD' => 'قائد فريق',
        'MANAGER' => 'مدير',
        'SENIOR_MANAGER' => 'مدير كبير',
        'DIRECTOR' => 'مدير عام',
        'VP' => 'نائب رئيس',
        'C_LEVEL' => 'مستوى تنفيذي',
    ];

    /**
     * درجات المناصب
     */
    public const GRADES = [
        'G1' => 'الدرجة الأولى',
        'G2' => 'الدرجة الثانية',
        'G3' => 'الدرجة الثالثة',
        'G4' => 'الدرجة الرابعة',
        'G5' => 'الدرجة الخامسة',
        'G6' => 'الدرجة السادسة',
        'G7' => 'الدرجة السابعة',
        'G8' => 'الدرجة الثامنة',
        'G9' => 'الدرجة التاسعة',
        'G10' => 'الدرجة العاشرة',
    ];

    /**
     * القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * المنصب الذي يرفع إليه
     */
    public function reportsTo(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'reports_to_position_id');
    }

    /**
     * المناصب التي ترفع لهذا المنصب
     */
    public function subordinatePositions(): HasMany
    {
        return $this->hasMany(Position::class, 'reports_to_position_id');
    }

    /**
     * الموظفين في هذا المنصب
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * الموظفين النشطين
     */
    public function activeEmployees(): HasMany
    {
        return $this->employees()->active();
    }

    /**
     * إعلانات التوظيف
     */
    public function jobPostings(): HasMany
    {
        return $this->hasMany(JobPosting::class);
    }

    /**
     * الحصول على العنوان المترجم
     */
    public function getLocalizedTitle(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->title_ar ?? $this->title,
            'en' => $this->title_en ?? $this->title,
            'fr' => $this->title_fr ?? $this->title,
            default => $this->title,
        };
    }

    /**
     * الحصول على الوصف الوظيفي المترجم
     */
    public function getLocalizedJobDescription(string $locale = 'ar'): string
    {
        return match ($locale) {
            'ar' => $this->job_description_ar ?? $this->job_description,
            'en' => $this->job_description_en ?? $this->job_description,
            'fr' => $this->job_description_fr ?? $this->job_description,
            default => $this->job_description,
        };
    }

    /**
     * الحصول على عدد الموظفين الحالي
     */
    public function getCurrentHeadcountAttribute(): int
    {
        return $this->activeEmployees()->count();
    }

    /**
     * الحصول على المناصب الشاغرة
     */
    public function getVacanciesAttribute(): int
    {
        return max(0, $this->headcount_approved - $this->current_headcount);
    }

    /**
     * التحقق من وجود شواغر
     */
    public function hasVacancies(): bool
    {
        return $this->vacancies > 0;
    }

    /**
     * التحقق من كون المنصب إداري
     */
    public function isManagement(): bool
    {
        return $this->is_management;
    }

    /**
     * الحصول على متوسط الراتب
     */
    public function getAverageSalaryAttribute(): float
    {
        return $this->activeEmployees()->avg('basic_salary') ?? 0;
    }

    /**
     * الحصول على نطاق الراتب
     */
    public function getSalaryRange(): array
    {
        return [
            'min' => $this->min_salary,
            'max' => $this->max_salary,
            'average' => $this->average_salary,
            'currency' => $this->currency,
        ];
    }

    /**
     * التحقق من كون الراتب ضمن النطاق
     */
    public function isSalaryInRange(float $salary): bool
    {
        if ($this->min_salary && $salary < $this->min_salary) {
            return false;
        }

        if ($this->max_salary && $salary > $this->max_salary) {
            return false;
        }

        return true;
    }

    /**
     * الحصول على الهيكل التنظيمي
     */
    public function getOrganizationalChart(): array
    {
        $chart = [
            'position' => $this,
            'subordinates' => [],
        ];

        foreach ($this->subordinatePositions as $subordinate) {
            $chart['subordinates'][] = $subordinate->getOrganizationalChart();
        }

        return $chart;
    }

    /**
     * الحصول على مسار التقرير
     */
    public function getReportingPath(): array
    {
        $path = [$this];
        $current = $this->reportsTo;

        while ($current) {
            $path[] = $current;
            $current = $current->reportsTo;
        }

        return array_reverse($path);
    }

    /**
     * إنشاء إعلان توظيف
     */
    public function createJobPosting(array $data): JobPosting
    {
        return $this->jobPostings()->create(array_merge($data, [
            'position_id' => $this->id,
            'department_id' => $this->department_id,
            'title' => $data['title'] ?? $this->title,
            'description' => $data['description'] ?? $this->job_description,
            'requirements' => $data['requirements'] ?? $this->requirements,
            'qualifications' => $data['qualifications'] ?? $this->qualifications,
            'skills_required' => $data['skills_required'] ?? $this->skills_required,
            'min_salary' => $data['min_salary'] ?? $this->min_salary,
            'max_salary' => $data['max_salary'] ?? $this->max_salary,
        ]));
    }

    /**
     * نطاق للمناصب النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق للمناصب الإدارية
     */
    public function scopeManagement($query)
    {
        return $query->where('is_management', true);
    }

    /**
     * نطاق حسب القسم
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * نطاق حسب المستوى
     */
    public function scopeByLevel($query, string $level)
    {
        return $query->where('level', $level);
    }

    /**
     * نطاق حسب الدرجة
     */
    public function scopeByGrade($query, string $grade)
    {
        return $query->where('grade', $grade);
    }

    /**
     * نطاق للمناصب التي لها شواغر
     */
    public function scopeWithVacancies($query)
    {
        return $query->whereRaw('headcount_approved > headcount_current');
    }

    /**
     * نطاق حسب نطاق الراتب
     */
    public function scopeBySalaryRange($query, float $minSalary = null, float $maxSalary = null)
    {
        if ($minSalary) {
            $query->where('max_salary', '>=', $minSalary);
        }

        if ($maxSalary) {
            $query->where('min_salary', '<=', $maxSalary);
        }

        return $query;
    }
}
