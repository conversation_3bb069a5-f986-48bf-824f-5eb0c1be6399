<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تعليق الملف - File Comment
 */
class FileComment extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'file_id',
        'user_id',
        'comment',
        'coordinates',
        'is_resolved',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'is_resolved' => 'boolean',
    ];

    public function file(): BelongsTo
    {
        return $this->belongsTo(ProjectFile::class, 'file_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }

    public function resolve(): bool
    {
        return $this->update(['is_resolved' => true]);
    }

    public function unresolve(): bool
    {
        return $this->update(['is_resolved' => false]);
    }
}
