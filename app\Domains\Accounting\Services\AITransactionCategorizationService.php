<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\BankTransaction;
use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\TransactionPattern;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التصنيف الذكي للمعاملات باستخدام الذكاء الاصطناعي
 * تحليل وتصنيف المعاملات البنكية تلقائياً
 */
class AITransactionCategorizationService
{
    protected array $categories = [
        'REVENUE' => [
            'name' => 'إيرادات',
            'keywords' => ['دفع', 'تحويل وارد', 'إيداع', 'مبيعات', 'خدمات'],
            'patterns' => ['/دفع.*فاتورة/i', '/تحويل.*من/i'],
            'account_types' => ['REVENUE', 'SALES'],
        ],
        'EXPENSE' => [
            'name' => 'مصروفات',
            'keywords' => ['شراء', 'دفع', 'فاتورة', 'مصروف', 'تكلفة'],
            'patterns' => ['/شراء.*من/i', '/دفع.*فاتورة/i'],
            'account_types' => ['EXPENSE', 'COST'],
        ],
        'SALARY' => [
            'name' => 'رواتب',
            'keywords' => ['راتب', 'أجر', 'مكافأة', 'حافز', 'بدل'],
            'patterns' => ['/راتب.*شهر/i', '/تحويل.*راتب/i'],
            'account_types' => ['SALARY_EXPENSE'],
        ],
        'TAX' => [
            'name' => 'ضرائب',
            'keywords' => ['ضريبة', 'TVA', 'IR', 'ضمان اجتماعي', 'CNSS'],
            'patterns' => ['/ضريبة.*دخل/i', '/TVA.*شهر/i'],
            'account_types' => ['TAX_PAYABLE', 'TAX_EXPENSE'],
        ],
        'BANK_FEES' => [
            'name' => 'رسوم بنكية',
            'keywords' => ['رسوم', 'عمولة', 'فوائد', 'خدمات بنكية'],
            'patterns' => ['/رسوم.*بنكية/i', '/عمولة.*تحويل/i'],
            'account_types' => ['BANK_CHARGES'],
        ],
        'LOAN' => [
            'name' => 'قروض',
            'keywords' => ['قرض', 'تمويل', 'قسط', 'سداد'],
            'patterns' => ['/قسط.*قرض/i', '/سداد.*تمويل/i'],
            'account_types' => ['LOAN_PAYABLE', 'INTEREST_EXPENSE'],
        ],
        'INVESTMENT' => [
            'name' => 'استثمارات',
            'keywords' => ['استثمار', 'أسهم', 'سندات', 'عائد'],
            'patterns' => ['/عائد.*استثمار/i', '/أرباح.*أسهم/i'],
            'account_types' => ['INVESTMENT', 'INVESTMENT_INCOME'],
        ],
        'ASSET_PURCHASE' => [
            'name' => 'شراء أصول',
            'keywords' => ['معدات', 'أثاث', 'سيارة', 'عقار', 'جهاز'],
            'patterns' => ['/شراء.*معدات/i', '/شراء.*سيارة/i'],
            'account_types' => ['FIXED_ASSET'],
        ],
    ];

    protected array $vendorPatterns = [];
    protected array $learningData = [];

    public function __construct()
    {
        $this->loadVendorPatterns();
        $this->loadLearningData();
    }

    /**
     * تصنيف معاملة بنكية
     */
    public function categorize(BankTransaction $transaction): array
    {
        $analysis = [
            'transaction_id' => $transaction->id,
            'amount' => $transaction->amount,
            'description' => $transaction->description,
            'predicted_category' => null,
            'confidence_score' => 0,
            'suggested_account' => null,
            'reasoning' => [],
            'alternative_categories' => [],
        ];

        // تحليل النص
        $textAnalysis = $this->analyzeTransactionText($transaction);
        
        // تحليل المبلغ
        $amountAnalysis = $this->analyzeTransactionAmount($transaction);
        
        // تحليل التاريخ والتوقيت
        $temporalAnalysis = $this->analyzeTransactionTiming($transaction);
        
        // تحليل المورد/العميل
        $vendorAnalysis = $this->analyzeVendorPattern($transaction);
        
        // دمج التحليلات
        $combinedAnalysis = $this->combineAnalyses([
            $textAnalysis,
            $amountAnalysis,
            $temporalAnalysis,
            $vendorAnalysis,
        ]);

        // تطبيق نموذج التعلم الآلي
        $mlPrediction = $this->applyMachineLearningModel($transaction, $combinedAnalysis);
        
        // تحديد التصنيف النهائي
        $finalCategory = $this->determineFinalCategory($combinedAnalysis, $mlPrediction);
        
        $analysis['predicted_category'] = $finalCategory['category'];
        $analysis['confidence_score'] = $finalCategory['confidence'];
        $analysis['suggested_account'] = $this->suggestAccount($finalCategory['category'], $transaction);
        $analysis['reasoning'] = $finalCategory['reasoning'];
        $analysis['alternative_categories'] = $finalCategory['alternatives'];

        // حفظ للتعلم المستقبلي
        $this->saveForLearning($transaction, $analysis);

        return $analysis;
    }

    /**
     * تحليل نص المعاملة
     */
    protected function analyzeTransactionText(BankTransaction $transaction): array
    {
        $description = strtolower($transaction->description);
        $scores = [];
        $matches = [];

        foreach ($this->categories as $categoryKey => $category) {
            $score = 0;
            $categoryMatches = [];

            // تحليل الكلمات المفتاحية
            foreach ($category['keywords'] as $keyword) {
                if (strpos($description, strtolower($keyword)) !== false) {
                    $score += 10;
                    $categoryMatches[] = "كلمة مفتاحية: {$keyword}";
                }
            }

            // تحليل الأنماط
            foreach ($category['patterns'] as $pattern) {
                if (preg_match($pattern, $description)) {
                    $score += 15;
                    $categoryMatches[] = "نمط: {$pattern}";
                }
            }

            if ($score > 0) {
                $scores[$categoryKey] = $score;
                $matches[$categoryKey] = $categoryMatches;
            }
        }

        return [
            'type' => 'text_analysis',
            'scores' => $scores,
            'matches' => $matches,
            'weight' => 0.4, // 40% من الوزن الإجمالي
        ];
    }

    /**
     * تحليل مبلغ المعاملة
     */
    protected function analyzeTransactionAmount(BankTransaction $transaction): array
    {
        $amount = abs($transaction->amount);
        $scores = [];
        $reasoning = [];

        // تحليل حسب نطاقات المبالغ المعتادة
        if ($amount < 100) {
            $scores['BANK_FEES'] = 20;
            $reasoning[] = "مبلغ صغير يشير لرسوم بنكية";
        } elseif ($amount >= 100 && $amount < 1000) {
            $scores['EXPENSE'] = 15;
            $scores['BANK_FEES'] = 10;
            $reasoning[] = "مبلغ متوسط يشير لمصروف عادي";
        } elseif ($amount >= 1000 && $amount < 10000) {
            $scores['SALARY'] = 20;
            $scores['EXPENSE'] = 15;
            $scores['REVENUE'] = 10;
            $reasoning[] = "مبلغ كبير قد يكون راتب أو مصروف كبير";
        } elseif ($amount >= 10000) {
            $scores['ASSET_PURCHASE'] = 25;
            $scores['LOAN'] = 20;
            $scores['INVESTMENT'] = 15;
            $reasoning[] = "مبلغ كبير جداً يشير لشراء أصل أو قرض";
        }

        // تحليل الأرقام المستديرة
        if ($amount % 1000 == 0) {
            $scores['SALARY'] = ($scores['SALARY'] ?? 0) + 10;
            $reasoning[] = "مبلغ مستدير يشير للراتب";
        }

        return [
            'type' => 'amount_analysis',
            'scores' => $scores,
            'reasoning' => $reasoning,
            'weight' => 0.2, // 20% من الوزن الإجمالي
        ];
    }

    /**
     * تحليل توقيت المعاملة
     */
    protected function analyzeTransactionTiming(BankTransaction $transaction): array
    {
        $date = $transaction->transaction_date;
        $scores = [];
        $reasoning = [];

        // تحليل اليوم من الشهر
        $dayOfMonth = $date->day;
        if ($dayOfMonth >= 25 || $dayOfMonth <= 5) {
            $scores['SALARY'] = 15;
            $reasoning[] = "توقيت نهاية/بداية الشهر يشير للراتب";
        }

        // تحليل يوم الأسبوع
        $dayOfWeek = $date->dayOfWeek;
        if ($dayOfWeek >= 1 && $dayOfWeek <= 5) { // أيام العمل
            $scores['EXPENSE'] = 5;
            $scores['REVENUE'] = 5;
            $reasoning[] = "يوم عمل - معاملة تجارية محتملة";
        }

        // تحليل الساعة
        $hour = $date->hour;
        if ($hour >= 9 && $hour <= 17) {
            $scores['REVENUE'] = 5;
            $scores['EXPENSE'] = 5;
            $reasoning[] = "ساعات العمل - معاملة تجارية";
        } elseif ($hour >= 18 || $hour <= 8) {
            $scores['BANK_FEES'] = 5;
            $reasoning[] = "خارج ساعات العمل - معاملة تلقائية محتملة";
        }

        return [
            'type' => 'temporal_analysis',
            'scores' => $scores,
            'reasoning' => $reasoning,
            'weight' => 0.1, // 10% من الوزن الإجمالي
        ];
    }

    /**
     * تحليل نمط المورد/العميل
     */
    protected function analyzeVendorPattern(BankTransaction $transaction): array
    {
        $description = $transaction->description;
        $scores = [];
        $reasoning = [];

        // البحث في أنماط الموردين المحفوظة
        foreach ($this->vendorPatterns as $pattern) {
            if (preg_match($pattern['regex'], $description)) {
                $scores[$pattern['category']] = 25;
                $reasoning[] = "تطابق مع نمط مورد معروف: {$pattern['vendor_name']}";
                break;
            }
        }

        // تحليل أرقام الحسابات البنكية
        if (preg_match('/\d{10,}/', $description)) {
            $scores['REVENUE'] = 10;
            $scores['EXPENSE'] = 10;
            $reasoning[] = "يحتوي على رقم حساب - تحويل بنكي";
        }

        return [
            'type' => 'vendor_analysis',
            'scores' => $scores,
            'reasoning' => $reasoning,
            'weight' => 0.3, // 30% من الوزن الإجمالي
        ];
    }

    /**
     * دمج التحليلات المختلفة
     */
    protected function combineAnalyses(array $analyses): array
    {
        $combinedScores = [];
        $combinedReasoning = [];

        foreach ($analyses as $analysis) {
            $weight = $analysis['weight'];
            
            foreach ($analysis['scores'] as $category => $score) {
                $weightedScore = $score * $weight;
                $combinedScores[$category] = ($combinedScores[$category] ?? 0) + $weightedScore;
            }

            if (isset($analysis['reasoning'])) {
                $combinedReasoning = array_merge($combinedReasoning, $analysis['reasoning']);
            }

            if (isset($analysis['matches'])) {
                foreach ($analysis['matches'] as $category => $matches) {
                    $combinedReasoning = array_merge($combinedReasoning, $matches);
                }
            }
        }

        // ترتيب النتائج
        arsort($combinedScores);

        return [
            'scores' => $combinedScores,
            'reasoning' => $combinedReasoning,
        ];
    }

    /**
     * تطبيق نموذج التعلم الآلي
     */
    protected function applyMachineLearningModel(BankTransaction $transaction, array $analysis): array
    {
        // نموذج بسيط للتعلم الآلي - يمكن تطويره لاستخدام مكتبات ML حقيقية
        $features = $this->extractFeatures($transaction);
        $prediction = $this->predictWithSimpleModel($features);

        return [
            'predicted_category' => $prediction['category'],
            'confidence' => $prediction['confidence'],
            'features_used' => $features,
        ];
    }

    /**
     * استخراج الخصائص للتعلم الآلي
     */
    protected function extractFeatures(BankTransaction $transaction): array
    {
        return [
            'amount_log' => log(abs($transaction->amount) + 1),
            'amount_rounded' => abs($transaction->amount) % 1000 == 0 ? 1 : 0,
            'description_length' => strlen($transaction->description),
            'day_of_month' => $transaction->transaction_date->day,
            'day_of_week' => $transaction->transaction_date->dayOfWeek,
            'hour' => $transaction->transaction_date->hour,
            'is_debit' => $transaction->amount < 0 ? 1 : 0,
            'has_numbers' => preg_match('/\d/', $transaction->description) ? 1 : 0,
        ];
    }

    /**
     * التنبؤ باستخدام نموذج بسيط
     */
    protected function predictWithSimpleModel(array $features): array
    {
        // نموذج بسيط - في التطبيق الحقيقي يمكن استخدام TensorFlow أو scikit-learn
        $weights = [
            'EXPENSE' => 0.3,
            'REVENUE' => 0.25,
            'SALARY' => 0.2,
            'BANK_FEES' => 0.1,
            'TAX' => 0.1,
            'LOAN' => 0.05,
        ];

        $maxCategory = array_keys($weights, max($weights))[0];
        
        return [
            'category' => $maxCategory,
            'confidence' => 0.7, // ثقة افتراضية
        ];
    }

    /**
     * تحديد التصنيف النهائي
     */
    protected function determineFinalCategory(array $combinedAnalysis, array $mlPrediction): array
    {
        $scores = $combinedAnalysis['scores'];
        
        if (empty($scores)) {
            return [
                'category' => 'EXPENSE', // افتراضي
                'confidence' => 0.3,
                'reasoning' => ['لم يتم العثور على تطابقات واضحة'],
                'alternatives' => [],
            ];
        }

        $topCategory = array_keys($scores)[0];
        $topScore = $scores[$topCategory];
        
        // حساب الثقة
        $totalScore = array_sum($scores);
        $confidence = $totalScore > 0 ? $topScore / $totalScore : 0;

        // الحصول على البدائل
        $alternatives = array_slice($scores, 1, 3, true);

        return [
            'category' => $topCategory,
            'confidence' => min($confidence, 0.95), // حد أقصى 95%
            'reasoning' => $combinedAnalysis['reasoning'],
            'alternatives' => $alternatives,
        ];
    }

    /**
     * اقتراح حساب محاسبي
     */
    protected function suggestAccount(string $category, BankTransaction $transaction): ?int
    {
        $categoryConfig = $this->categories[$category] ?? null;
        if (!$categoryConfig) {
            return null;
        }

        // البحث عن حساب مناسب
        $account = Account::whereIn('account_type', $categoryConfig['account_types'])
            ->where('is_active', true)
            ->first();

        return $account?->id;
    }

    /**
     * حفظ البيانات للتعلم المستقبلي
     */
    protected function saveForLearning(BankTransaction $transaction, array $analysis): void
    {
        // حفظ في cache للتعلم السريع
        $cacheKey = "transaction_learning_{$transaction->id}";
        Cache::put($cacheKey, [
            'transaction_data' => $transaction->toArray(),
            'analysis_result' => $analysis,
            'timestamp' => now(),
        ], now()->addDays(30));

        // يمكن أيضاً حفظ في قاعدة البيانات للتعلم طويل المدى
    }

    /**
     * تحميل أنماط الموردين
     */
    protected function loadVendorPatterns(): void
    {
        // يمكن تحميل هذه من قاعدة البيانات
        $this->vendorPatterns = [
            [
                'vendor_name' => 'CNSS',
                'regex' => '/CNSS|ضمان اجتماعي/i',
                'category' => 'TAX',
            ],
            [
                'vendor_name' => 'Direction des Impôts',
                'regex' => '/impots|ضرائب|TVA/i',
                'category' => 'TAX',
            ],
            [
                'vendor_name' => 'Salaire',
                'regex' => '/salaire|راتب|virement salaire/i',
                'category' => 'SALARY',
            ],
        ];
    }

    /**
     * تحميل بيانات التعلم
     */
    protected function loadLearningData(): void
    {
        // تحميل البيانات المحفوظة للتعلم
        $this->learningData = Cache::get('ml_training_data', []);
    }

    /**
     * تدريب النموذج بناءً على التصحيحات اليدوية
     */
    public function trainFromCorrections(array $corrections): void
    {
        foreach ($corrections as $correction) {
            $this->learningData[] = [
                'transaction_features' => $correction['features'],
                'correct_category' => $correction['category'],
                'timestamp' => now(),
            ];
        }

        // حفظ البيانات المحدثة
        Cache::put('ml_training_data', $this->learningData, now()->addDays(90));
        
        // إعادة تدريب النموذج (في التطبيق الحقيقي)
        $this->retrainModel();
    }

    /**
     * إعادة تدريب النموذج
     */
    protected function retrainModel(): void
    {
        // في التطبيق الحقيقي، هنا يتم إعادة تدريب نموذج ML
        Log::info('تم إعادة تدريب نموذج تصنيف المعاملات', [
            'training_samples' => count($this->learningData),
        ]);
    }
}
