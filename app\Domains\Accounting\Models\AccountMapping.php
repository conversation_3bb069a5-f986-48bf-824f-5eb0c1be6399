<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Exception;

/**
 * نموذج خريطة الحسابات
 * يربط الحسابات بين المعايير المحاسبية المختلفة
 */
class AccountMapping extends Model
{
    use HasFactory, HasUuid, Auditable;

    protected $fillable = [
        'source_chart_id',
        'target_chart_id',
        'source_account_id',
        'target_account_id',
        'mapping_type',
        'conversion_factor',
        'is_active',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'conversion_factor' => 'decimal:6',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * أنواع التحويل
     */
    public const MAPPING_TYPES = [
        'DIRECT' => 'تحويل مباشر',
        'SPLIT' => 'تقسيم على عدة حسابات',
        'MERGE' => 'دمج من عدة حسابات',
        'CALCULATED' => 'تحويل محسوب',
        'MANUAL' => 'تحويل يدوي',
    ];

    /**
     * دليل الحسابات المصدر
     */
    public function sourceChart(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccounts::class, 'source_chart_id');
    }

    /**
     * دليل الحسابات المستهدف
     */
    public function targetChart(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccounts::class, 'target_chart_id');
    }

    /**
     * الحساب المصدر
     */
    public function sourceAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'source_account_id');
    }

    /**
     * الحساب المستهدف
     */
    public function targetAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'target_account_id');
    }

    /**
     * تحويل مبلغ من المعيار المصدر إلى المستهدف
     */
    public function convertAmount(float $amount): float
    {
        return match ($this->mapping_type) {
            'DIRECT' => $amount * ($this->conversion_factor ?? 1),
            'SPLIT' => $amount * ($this->conversion_factor ?? 1),
            'CALCULATED' => $this->calculateAmount($amount),
            default => $amount,
        };
    }

    /**
     * حساب المبلغ المحول (للتحويلات المعقدة)
     */
    protected function calculateAmount(float $amount): float
    {
        // يمكن تخصيص هذه الدالة حسب قواعد التحويل المعقدة
        $metadata = $this->metadata ?? [];

        if (isset($metadata['formula'])) {
            // تطبيق معادلة مخصصة
            return $this->applyFormula($amount, $metadata['formula']);
        }

        return $amount * ($this->conversion_factor ?? 1);
    }

    /**
     * تطبيق معادلة مخصصة
     */
    protected function applyFormula(float $amount, string $formula): float
    {
        // استبدال المتغيرات في المعادلة
        $formula = str_replace('{amount}', $amount, $formula);
        $formula = str_replace('{factor}', $this->conversion_factor ?? 1, $formula);

        // تقييم المعادلة (يجب التأكد من الأمان)
        try {
            return eval("return {$formula};");
        } catch (Exception $e) {
            return $amount;
        }
    }

    /**
     * التحقق من صحة التحويل
     */
    public function validateMapping(): array
    {
        $errors = [];

        // التحقق من وجود الحسابات
        if (!$this->sourceAccount) {
            $errors[] = 'الحساب المصدر غير موجود';
        }

        if (!$this->targetAccount) {
            $errors[] = 'الحساب المستهدف غير موجود';
        }

        // التحقق من توافق أنواع الحسابات
        if ($this->sourceAccount && $this->targetAccount) {
            if ($this->sourceAccount->account_type !== $this->targetAccount->account_type) {
                $errors[] = 'عدم توافق أنواع الحسابات';
            }
        }

        // التحقق من معامل التحويل
        if ($this->mapping_type === 'CALCULATED' && !$this->conversion_factor) {
            $errors[] = 'معامل التحويل مطلوب للتحويل المحسوب';
        }

        return $errors;
    }

    /**
     * إنشاء تحويل عكسي
     */
    public function createReverseMapping(): self
    {
        return static::create([
            'source_chart_id' => $this->target_chart_id,
            'target_chart_id' => $this->source_chart_id,
            'source_account_id' => $this->target_account_id,
            'target_account_id' => $this->source_account_id,
            'mapping_type' => $this->mapping_type,
            'conversion_factor' => $this->conversion_factor ? (1 / $this->conversion_factor) : null,
            'is_active' => $this->is_active,
            'notes' => "تحويل عكسي لـ: {$this->notes}",
        ]);
    }

    /**
     * نطاق للتحويلات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * نطاق حسب نوع التحويل
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('mapping_type', $type);
    }

    /**
     * نطاق للتحويل بين معيارين
     */
    public function scopeBetweenStandards($query, string $sourceStandard, string $targetStandard)
    {
        return $query->whereHas('sourceChart', function ($q) use ($sourceStandard) {
            $q->where('standard', $sourceStandard);
        })->whereHas('targetChart', function ($q) use ($targetStandard) {
            $q->where('standard', $targetStandard);
        });
    }
}
