<?php

namespace App\Domains\CRM\Contracts;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\Opportunity;
use Carbon\Carbon;

/**
 * واجهة خدمة CRM الرئيسية
 * CRM Service Interface
 */
interface CRMServiceInterface
{
    /**
     * إنشاء ملف عميل 360°
     */
    public function createCustomer360Profile(Customer $customer): array;

    /**
     * إدارة دورة حياة العميل
     */
    public function manageCustomerLifecycle(Customer $customer): array;

    /**
     * تحليل سلوك العميل
     */
    public function analyzeCustomerBehavior(Customer $customer): array;

    /**
     * الحصول على توصيات الذكاء الاصطناعي
     */
    public function getAIRecommendations(Customer $customer): array;

    /**
     * حساب القيمة الدائمة للعميل
     */
    public function calculateCustomerLifetimeValue(Customer $customer): float;

    /**
     * تحليل مخاطر فقدان العميل
     */
    public function calculateChurnRisk(Customer $customer): float;

    /**
     * الحصول على مقاييس الأداء
     */
    public function getPerformanceMetrics(Carbon $dateFrom = null, Carbon $dateTo = null): array;
}
