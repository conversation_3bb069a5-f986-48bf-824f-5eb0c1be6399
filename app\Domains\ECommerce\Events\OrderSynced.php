<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceOrder;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث مزامنة الطلب
 * يتم إطلاقه عند مزامنة طلب مع المنصة
 */
class OrderSynced
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceOrder $order;
    public string $action;
    public ECommerceIntegration $integration;
    public array $syncData;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceOrder $order,
        string $action,
        ECommerceIntegration $integration,
        array $syncData = []
    ) {
        $this->order = $order;
        $this->action = $action;
        $this->integration = $integration;
        $this->syncData = $syncData;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'order_id' => $this->order->id,
            'external_id' => $this->order->external_id,
            'order_number' => $this->order->order_number,
            'order_total' => $this->order->total,
            'order_currency' => $this->order->currency,
            'order_status' => $this->order->status,
            'financial_status' => $this->order->financial_status,
            'fulfillment_status' => $this->order->fulfillment_status,
            'action' => $this->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'synced_at' => $this->order->last_synced_at,
            'sync_data' => $this->syncData,
        ];
    }

    /**
     * تحديد ما إذا كان الطلب جديد
     */
    public function isNewOrder(): bool
    {
        return $this->action === 'created';
    }

    /**
     * تحديد ما إذا كان الطلب محدث
     */
    public function isUpdatedOrder(): bool
    {
        return $this->action === 'updated';
    }

    /**
     * تحديد ما إذا كان الطلب مدفوع
     */
    public function isPaidOrder(): bool
    {
        return $this->action === 'paid' || $this->order->financial_status === 'paid';
    }

    /**
     * تحديد ما إذا كان الطلب ملغي
     */
    public function isCancelledOrder(): bool
    {
        return $this->action === 'cancelled' || $this->order->status === 'cancelled';
    }

    /**
     * تحديد ما إذا كان الطلب مكتمل
     */
    public function isCompletedOrder(): bool
    {
        return $this->order->fulfillment_status === 'fulfilled' || 
               $this->order->status === 'completed';
    }
}
