<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use App\Models\Company;

/**
 * نموذج أنشطة الامتثال
 * يتتبع جميع الأنشطة المتعلقة بالامتثال القانوني
 */
class ComplianceActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_id',
        'company_id',
        'user_id',
        'country_id',
        'compliance_rule_id',
        'related_model_type',
        'related_model_id',
        'activity_type',
        'activity_category',
        'title',
        'description',
        'status',
        'priority',
        'risk_level',
        'due_date',
        'completed_at',
        'assigned_to',
        'reviewed_by',
        'approved_by',
        'activity_data',
        'compliance_score_impact',
        'automation_level',
        'notification_sent',
        'escalation_level',
        'resolution_notes',
        'attachments',
        'metadata',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'activity_data' => 'array',
        'notification_sent' => 'boolean',
        'attachments' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع الأنشطة
     */
    const ACTIVITY_TYPES = [
        'tax_filing' => 'تقديم ضريبي',
        'tax_payment' => 'دفع ضريبي',
        'social_security_filing' => 'تقديم ضمان اجتماعي',
        'e_invoice_submission' => 'إرسال فاتورة إلكترونية',
        'compliance_check' => 'فحص امتثال',
        'audit_preparation' => 'إعداد مراجعة',
        'document_submission' => 'تقديم وثائق',
        'license_renewal' => 'تجديد ترخيص',
        'regulatory_update' => 'تحديث تنظيمي',
        'penalty_payment' => 'دفع غرامة',
        'appeal_submission' => 'تقديم استئناف',
        'correction_filing' => 'تقديم تصحيح',
    ];

    /**
     * فئات الأنشطة
     */
    const ACTIVITY_CATEGORIES = [
        'mandatory' => 'إلزامي',
        'voluntary' => 'طوعي',
        'corrective' => 'تصحيحي',
        'preventive' => 'وقائي',
        'emergency' => 'طوارئ',
    ];

    /**
     * حالات النشاط
     */
    const STATUSES = [
        'pending' => 'قيد الانتظار',
        'in_progress' => 'قيد التنفيذ',
        'completed' => 'مكتمل',
        'overdue' => 'متأخر',
        'cancelled' => 'ملغي',
        'failed' => 'فاشل',
        'under_review' => 'قيد المراجعة',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض',
    ];

    /**
     * مستويات الأولوية
     */
    const PRIORITIES = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'urgent' => 'عاجل',
        'critical' => 'حرج',
    ];

    /**
     * مستويات المخاطر
     */
    const RISK_LEVELS = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
    ];

    /**
     * مستويات الأتمتة
     */
    const AUTOMATION_LEVELS = [
        'manual' => 'يدوي',
        'semi_automated' => 'شبه آلي',
        'fully_automated' => 'آلي بالكامل',
        'ai_assisted' => 'بمساعدة الذكاء الاصطناعي',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع قاعدة الامتثال
     */
    public function complianceRule(): BelongsTo
    {
        return $this->belongsTo(ComplianceRule::class);
    }

    /**
     * العلاقة مع النموذج المرتبط (polymorphic)
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع المستخدم المكلف
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * العلاقة مع المراجع
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * العلاقة مع المعتمد
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * إنشاء نشاط امتثال جديد
     */
    public static function createActivity(array $activityData): self
    {
        $activity = self::create([
            'activity_id' => self::generateActivityId(),
            'company_id' => $activityData['company_id'],
            'user_id' => $activityData['user_id'] ?? auth()->id(),
            'country_id' => $activityData['country_id'],
            'compliance_rule_id' => $activityData['compliance_rule_id'] ?? null,
            'related_model_type' => $activityData['related_model_type'] ?? null,
            'related_model_id' => $activityData['related_model_id'] ?? null,
            'activity_type' => $activityData['activity_type'],
            'activity_category' => $activityData['activity_category'] ?? 'mandatory',
            'title' => $activityData['title'],
            'description' => $activityData['description'],
            'status' => 'pending',
            'priority' => $activityData['priority'] ?? 'medium',
            'risk_level' => $activityData['risk_level'] ?? 'medium',
            'due_date' => $activityData['due_date'] ?? null,
            'assigned_to' => $activityData['assigned_to'] ?? null,
            'activity_data' => $activityData['activity_data'] ?? [],
            'automation_level' => $activityData['automation_level'] ?? 'manual',
            'metadata' => $activityData['metadata'] ?? [],
        ]);

        // إرسال إشعار إذا كان مطلوباً
        if ($activityData['send_notification'] ?? true) {
            $activity->sendNotification();
        }

        return $activity;
    }

    /**
     * توليد معرف النشاط
     */
    protected static function generateActivityId(): string
    {
        return 'ACT_' . strtoupper(uniqid());
    }

    /**
     * تحديث حالة النشاط
     */
    public function updateStatus(string $status, array $data = []): void
    {
        $this->update([
            'status' => $status,
            'resolution_notes' => $data['notes'] ?? null,
            'completed_at' => $status === 'completed' ? now() : null,
            'reviewed_by' => $data['reviewed_by'] ?? null,
            'approved_by' => $data['approved_by'] ?? null,
        ]);

        // تسجيل تغيير الحالة
        $this->logStatusChange($status, $data);

        // إرسال إشعار بتغيير الحالة
        if ($data['send_notification'] ?? true) {
            $this->sendStatusChangeNotification($status);
        }
    }

    /**
     * تسجيل تغيير الحالة
     */
    protected function logStatusChange(string $newStatus, array $data): void
    {
        $metadata = $this->metadata ?? [];
        $metadata['status_history'][] = [
            'from_status' => $this->getOriginal('status'),
            'to_status' => $newStatus,
            'changed_by' => auth()->id(),
            'changed_at' => now()->toISOString(),
            'notes' => $data['notes'] ?? null,
        ];

        $this->update(['metadata' => $metadata]);
    }

    /**
     * إرسال إشعار
     */
    public function sendNotification(): void
    {
        // منطق إرسال الإشعار
        $this->update(['notification_sent' => true]);
    }

    /**
     * إرسال إشعار تغيير الحالة
     */
    protected function sendStatusChangeNotification(string $status): void
    {
        // منطق إرسال إشعار تغيير الحالة
    }

    /**
     * تصعيد النشاط
     */
    public function escalate(string $reason = null): void
    {
        $currentLevel = $this->escalation_level ?? 0;
        $newLevel = $currentLevel + 1;

        $this->update([
            'escalation_level' => $newLevel,
            'priority' => $this->getEscalatedPriority(),
        ]);

        // تسجيل التصعيد
        $metadata = $this->metadata ?? [];
        $metadata['escalations'][] = [
            'level' => $newLevel,
            'reason' => $reason,
            'escalated_by' => auth()->id(),
            'escalated_at' => now()->toISOString(),
        ];

        $this->update(['metadata' => $metadata]);

        // إرسال إشعار التصعيد
        $this->sendEscalationNotification($newLevel, $reason);
    }

    /**
     * الحصول على أولوية مصعدة
     */
    protected function getEscalatedPriority(): string
    {
        return match ($this->priority) {
            'low' => 'medium',
            'medium' => 'high',
            'high' => 'urgent',
            'urgent' => 'critical',
            default => 'critical',
        };
    }

    /**
     * إرسال إشعار التصعيد
     */
    protected function sendEscalationNotification(int $level, string $reason = null): void
    {
        // منطق إرسال إشعار التصعيد
    }

    /**
     * حساب تأثير النشاط على نقاط الامتثال
     */
    public function calculateComplianceImpact(): int
    {
        if ($this->compliance_score_impact) {
            return $this->compliance_score_impact;
        }

        $baseImpact = match ($this->activity_category) {
            'mandatory' => 20,
            'voluntary' => 5,
            'corrective' => 15,
            'preventive' => 10,
            'emergency' => 25,
            default => 10,
        };

        $riskMultiplier = match ($this->risk_level) {
            'critical' => 2.0,
            'high' => 1.5,
            'medium' => 1.0,
            'low' => 0.5,
            default => 1.0,
        };

        $statusMultiplier = match ($this->status) {
            'completed' => 1.0,
            'overdue' => -1.5,
            'failed' => -2.0,
            'cancelled' => -0.5,
            default => 0,
        };

        return round($baseImpact * $riskMultiplier * $statusMultiplier);
    }

    /**
     * التحقق من التأخير
     */
    public function isOverdue(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * الحصول على عدد الأيام المتبقية
     */
    public function getDaysRemaining(): ?int
    {
        if (!$this->due_date) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * الحصول على عدد الأيام المتأخرة
     */
    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->due_date->diffInDays(now());
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByActivityType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('due_date', [now()->startOfWeek(), now()->endOfWeek()])
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent', 'critical']);
    }

    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    public function scopeAutomated($query)
    {
        return $query->whereIn('automation_level', ['fully_automated', 'ai_assisted']);
    }

    public function scopeManual($query)
    {
        return $query->where('automation_level', 'manual');
    }

    /**
     * إضافة مرفق
     */
    public function addAttachment(array $attachment): void
    {
        $attachments = $this->attachments ?? [];
        $attachments[] = array_merge($attachment, [
            'uploaded_at' => now()->toISOString(),
            'uploaded_by' => auth()->id(),
        ]);

        $this->update(['attachments' => $attachments]);
    }

    /**
     * إزالة مرفق
     */
    public function removeAttachment(string $attachmentId): void
    {
        $attachments = collect($this->attachments ?? [])
            ->reject(fn($attachment) => $attachment['id'] === $attachmentId)
            ->values()
            ->toArray();

        $this->update(['attachments' => $attachments]);
    }

    /**
     * الحصول على ملخص النشاط
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->activity_id,
            'title' => $this->title,
            'type' => $this->activity_type,
            'status' => $this->status,
            'priority' => $this->priority,
            'due_date' => $this->due_date?->format('Y-m-d'),
            'days_remaining' => $this->getDaysRemaining(),
            'is_overdue' => $this->isOverdue(),
            'compliance_impact' => $this->calculateComplianceImpact(),
            'assigned_to' => $this->assignedUser?->name,
            'country' => $this->country?->name_ar,
        ];
    }
}
