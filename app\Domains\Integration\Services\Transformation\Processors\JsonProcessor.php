<?php

namespace App\Domains\Integration\Services\Transformation\Processors;

use App\Domains\Integration\Contracts\ProcessorInterface;
use App\Domains\Integration\Exceptions\TransformationException;
use Illuminate\Support\Facades\Log;

/**
 * JSON Data Processor
 * Handles JSON data transformation, validation, and processing
 */
class JsonProcessor implements ProcessorInterface
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'max_depth' => 512,
            'flags' => JSON_THROW_ON_ERROR,
            'pretty_print' => false,
            'preserve_zero_fraction' => false,
        ], $config);
    }

    /**
     * Process JSON data
     */
    public function process(mixed $data, array $options = []): array
    {
        try {
            // If data is already an array, return it
            if (is_array($data)) {
                return $data;
            }

            // If data is a string, decode it
            if (is_string($data)) {
                return $this->decode($data, $options);
            }

            // If data is an object, convert to array
            if (is_object($data)) {
                return json_decode(json_encode($data), true);
            }

            throw new TransformationException('Invalid data type for JSON processing');
        } catch (\Exception $e) {
            Log::error('JSON processing failed', [
                'error' => $e->getMessage(),
                'data_type' => gettype($data),
            ]);
            throw new TransformationException('JSON processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Decode JSON string
     */
    public function decode(string $json, array $options = []): array
    {
        $flags = $options['flags'] ?? $this->config['flags'];
        $depth = $options['max_depth'] ?? $this->config['max_depth'];

        try {
            $decoded = json_decode($json, true, $depth, $flags);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new TransformationException('JSON decode error: ' . json_last_error_msg());
            }

            return $decoded;
        } catch (\JsonException $e) {
            throw new TransformationException('JSON decode failed: ' . $e->getMessage());
        }
    }

    /**
     * Encode data to JSON
     */
    public function encode(mixed $data, array $options = []): string
    {
        $flags = JSON_THROW_ON_ERROR;
        
        if ($options['pretty_print'] ?? $this->config['pretty_print']) {
            $flags |= JSON_PRETTY_PRINT;
        }
        
        if ($options['preserve_zero_fraction'] ?? $this->config['preserve_zero_fraction']) {
            $flags |= JSON_PRESERVE_ZERO_FRACTION;
        }

        try {
            return json_encode($data, $flags);
        } catch (\JsonException $e) {
            throw new TransformationException('JSON encode failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate JSON structure
     */
    public function validate(mixed $data, array $schema = []): bool
    {
        try {
            if (is_string($data)) {
                $this->decode($data);
            }
            
            // Additional schema validation can be added here
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Transform JSON data using mapping rules
     */
    public function transform(array $data, array $mapping): array
    {
        $result = [];
        
        foreach ($mapping as $targetKey => $sourceKey) {
            if (is_array($sourceKey)) {
                // Complex mapping with transformation rules
                $result[$targetKey] = $this->applyTransformationRule($data, $sourceKey);
            } else {
                // Simple key mapping
                $result[$targetKey] = $this->getNestedValue($data, $sourceKey);
            }
        }
        
        return $result;
    }

    /**
     * Apply transformation rule
     */
    protected function applyTransformationRule(array $data, array $rule): mixed
    {
        $source = $rule['source'] ?? null;
        $default = $rule['default'] ?? null;
        $transform = $rule['transform'] ?? null;
        
        $value = $source ? $this->getNestedValue($data, $source) : $default;
        
        if ($transform && is_callable($transform)) {
            $value = $transform($value);
        }
        
        return $value;
    }

    /**
     * Get nested value from array using dot notation
     */
    protected function getNestedValue(array $data, string $key): mixed
    {
        $keys = explode('.', $key);
        $value = $data;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    /**
     * Flatten nested JSON structure
     */
    public function flatten(array $data, string $separator = '.'): array
    {
        $result = [];
        $this->flattenRecursive($data, $result, '', $separator);
        return $result;
    }

    /**
     * Recursive flatten helper
     */
    protected function flattenRecursive(array $data, array &$result, string $prefix, string $separator): void
    {
        foreach ($data as $key => $value) {
            $newKey = $prefix === '' ? $key : $prefix . $separator . $key;
            
            if (is_array($value)) {
                $this->flattenRecursive($value, $result, $newKey, $separator);
            } else {
                $result[$newKey] = $value;
            }
        }
    }

    /**
     * Unflatten flattened JSON structure
     */
    public function unflatten(array $data, string $separator = '.'): array
    {
        $result = [];
        
        foreach ($data as $key => $value) {
            $this->setNestedValue($result, $key, $value, $separator);
        }
        
        return $result;
    }

    /**
     * Set nested value using dot notation
     */
    protected function setNestedValue(array &$data, string $key, mixed $value, string $separator): void
    {
        $keys = explode($separator, $key);
        $current = &$data;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    /**
     * Get supported formats
     */
    public function getSupportedFormats(): array
    {
        return ['json', 'application/json'];
    }

    /**
     * Check if format is supported
     */
    public function supports(string $format): bool
    {
        return in_array(strtolower($format), $this->getSupportedFormats());
    }
}
