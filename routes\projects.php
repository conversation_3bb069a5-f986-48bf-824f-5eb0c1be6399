<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Projects\Http\Controllers\ProjectController;
use App\Domains\Projects\Http\Controllers\TaskController;
use App\Domains\Projects\Http\Controllers\TeamController;
use App\Domains\Projects\Http\Controllers\TimeTrackingController;
use App\Domains\Projects\Http\Controllers\FileController;
use App\Domains\Projects\Http\Controllers\ChatController;
use App\Domains\Projects\Http\Controllers\MilestoneController;
use App\Domains\Projects\Http\Controllers\SprintController;
use App\Domains\Projects\Http\Controllers\EpicController;
use App\Domains\Projects\Http\Controllers\RiskController;
use App\Domains\Projects\Http\Controllers\IssueController;
use App\Domains\Projects\Http\Controllers\ReportController;
use App\Domains\Projects\Http\Controllers\TemplateController;
use App\Domains\Projects\Http\Controllers\IntegrationController;
use App\Domains\Projects\Http\Controllers\WorkflowController;
use App\Domains\Projects\Http\Controllers\AnalyticsController;

/*
|--------------------------------------------------------------------------
| طرق نظام إدارة المشاريع
|--------------------------------------------------------------------------
*/

Route::middleware(['auth', 'verified'])->prefix('projects')->name('projects.')->group(function () {
    
    /*
    |--------------------------------------------------------------------------
    | إدارة المشاريع الأساسية
    |--------------------------------------------------------------------------
    */
    
    // عرض جميع المشاريع
    Route::get('/', [ProjectController::class, 'index'])->name('index');
    
    // إنشاء مشروع جديد
    Route::get('/create', [ProjectController::class, 'create'])->name('create');
    Route::post('/', [ProjectController::class, 'store'])->name('store');
    
    // عرض مشروع محدد
    Route::get('/{project}', [ProjectController::class, 'show'])->name('show');
    
    // تعديل مشروع
    Route::get('/{project}/edit', [ProjectController::class, 'edit'])->name('edit');
    Route::put('/{project}', [ProjectController::class, 'update'])->name('update');
    
    // حذف مشروع
    Route::delete('/{project}', [ProjectController::class, 'destroy'])->name('destroy');
    
    // أرشفة/إلغاء أرشفة مشروع
    Route::post('/{project}/archive', [ProjectController::class, 'archive'])->name('archive');
    Route::post('/{project}/unarchive', [ProjectController::class, 'unarchive'])->name('unarchive');
    
    // نسخ مشروع
    Route::post('/{project}/duplicate', [ProjectController::class, 'duplicate'])->name('duplicate');
    
    /*
    |--------------------------------------------------------------------------
    | إدارة المهام
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/tasks')->name('tasks.')->group(function () {
        Route::get('/', [TaskController::class, 'index'])->name('index');
        Route::get('/create', [TaskController::class, 'create'])->name('create');
        Route::post('/', [TaskController::class, 'store'])->name('store');
        Route::get('/{task}', [TaskController::class, 'show'])->name('show');
        Route::get('/{task}/edit', [TaskController::class, 'edit'])->name('edit');
        Route::put('/{task}', [TaskController::class, 'update'])->name('update');
        Route::delete('/{task}', [TaskController::class, 'destroy'])->name('destroy');
        
        // إجراءات المهام
        Route::post('/{task}/assign', [TaskController::class, 'assign'])->name('assign');
        Route::post('/{task}/start', [TaskController::class, 'start'])->name('start');
        Route::post('/{task}/complete', [TaskController::class, 'complete'])->name('complete');
        Route::post('/{task}/reopen', [TaskController::class, 'reopen'])->name('reopen');
        
        // تعليقات المهام
        Route::post('/{task}/comments', [TaskController::class, 'addComment'])->name('comments.store');
        Route::delete('/comments/{comment}', [TaskController::class, 'deleteComment'])->name('comments.destroy');
        
        // مرفقات المهام
        Route::post('/{task}/attachments', [TaskController::class, 'addAttachment'])->name('attachments.store');
        Route::delete('/attachments/{attachment}', [TaskController::class, 'deleteAttachment'])->name('attachments.destroy');
        
        // عرض Kanban
        Route::get('/kanban', [TaskController::class, 'kanban'])->name('kanban');
        Route::post('/kanban/update', [TaskController::class, 'updateKanban'])->name('kanban.update');
        
        // عرض Gantt
        Route::get('/gantt', [TaskController::class, 'gantt'])->name('gantt');
        
        // تصدير المهام
        Route::get('/export', [TaskController::class, 'export'])->name('export');
    });
    
    /*
    |--------------------------------------------------------------------------
    | إدارة الفريق
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/team')->name('team.')->group(function () {
        Route::get('/', [TeamController::class, 'index'])->name('index');
        Route::post('/invite', [TeamController::class, 'invite'])->name('invite');
        Route::post('/add', [TeamController::class, 'addMember'])->name('add');
        Route::put('/members/{member}', [TeamController::class, 'updateMember'])->name('update');
        Route::delete('/members/{member}', [TeamController::class, 'removeMember'])->name('remove');
        Route::get('/workload', [TeamController::class, 'workload'])->name('workload');
        Route::get('/performance', [TeamController::class, 'performance'])->name('performance');
    });
    
    /*
    |--------------------------------------------------------------------------
    | تتبع الوقت
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/time')->name('time.')->group(function () {
        Route::get('/', [TimeTrackingController::class, 'index'])->name('index');
        Route::post('/start', [TimeTrackingController::class, 'start'])->name('start');
        Route::post('/stop', [TimeTrackingController::class, 'stop'])->name('stop');
        Route::post('/log', [TimeTrackingController::class, 'logTime'])->name('log');
        Route::get('/entries', [TimeTrackingController::class, 'entries'])->name('entries');
        Route::put('/entries/{entry}', [TimeTrackingController::class, 'updateEntry'])->name('entries.update');
        Route::delete('/entries/{entry}', [TimeTrackingController::class, 'deleteEntry'])->name('entries.destroy');
        Route::post('/entries/{entry}/approve', [TimeTrackingController::class, 'approveEntry'])->name('entries.approve');
        Route::get('/reports', [TimeTrackingController::class, 'reports'])->name('reports');
        Route::get('/export', [TimeTrackingController::class, 'export'])->name('export');
    });
    
    /*
    |--------------------------------------------------------------------------
    | إدارة الملفات
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/files')->name('files.')->group(function () {
        Route::get('/', [FileController::class, 'index'])->name('index');
        Route::post('/upload', [FileController::class, 'upload'])->name('upload');
        Route::get('/{file}', [FileController::class, 'show'])->name('show');
        Route::get('/{file}/download', [FileController::class, 'download'])->name('download');
        Route::get('/{file}/preview', [FileController::class, 'preview'])->name('preview');
        Route::put('/{file}', [FileController::class, 'update'])->name('update');
        Route::delete('/{file}', [FileController::class, 'destroy'])->name('destroy');
        
        // إدارة الإصدارات
        Route::get('/{file}/versions', [FileController::class, 'versions'])->name('versions');
        Route::post('/{file}/versions', [FileController::class, 'createVersion'])->name('versions.create');
        Route::post('/versions/{version}/restore', [FileController::class, 'restoreVersion'])->name('versions.restore');
        
        // التعليقات على الملفات
        Route::post('/{file}/comments', [FileController::class, 'addComment'])->name('comments.store');
        Route::delete('/comments/{comment}', [FileController::class, 'deleteComment'])->name('comments.destroy');
        
        // مشاركة الملفات
        Route::post('/{file}/share', [FileController::class, 'share'])->name('share');
        Route::post('/{file}/public-link', [FileController::class, 'createPublicLink'])->name('public-link');
        Route::delete('/{file}/public-link', [FileController::class, 'revokePublicLink'])->name('public-link.revoke');
    });
    
    /*
    |--------------------------------------------------------------------------
    | الدردشة والتواصل
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/chat')->name('chat.')->group(function () {
        Route::get('/', [ChatController::class, 'index'])->name('index');
        Route::post('/messages', [ChatController::class, 'sendMessage'])->name('messages.send');
        Route::get('/messages', [ChatController::class, 'getMessages'])->name('messages.get');
        Route::put('/messages/{message}', [ChatController::class, 'updateMessage'])->name('messages.update');
        Route::delete('/messages/{message}', [ChatController::class, 'deleteMessage'])->name('messages.destroy');
        Route::post('/messages/{message}/react', [ChatController::class, 'reactToMessage'])->name('messages.react');
        Route::post('/messages/{message}/pin', [ChatController::class, 'pinMessage'])->name('messages.pin');
        Route::get('/search', [ChatController::class, 'search'])->name('search');
    });
    
    /*
    |--------------------------------------------------------------------------
    | المعالم
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/milestones')->name('milestones.')->group(function () {
        Route::get('/', [MilestoneController::class, 'index'])->name('index');
        Route::post('/', [MilestoneController::class, 'store'])->name('store');
        Route::get('/{milestone}', [MilestoneController::class, 'show'])->name('show');
        Route::put('/{milestone}', [MilestoneController::class, 'update'])->name('update');
        Route::delete('/{milestone}', [MilestoneController::class, 'destroy'])->name('destroy');
        Route::post('/{milestone}/complete', [MilestoneController::class, 'complete'])->name('complete');
    });
    
    /*
    |--------------------------------------------------------------------------
    | السبرنتات (Agile/Scrum)
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/sprints')->name('sprints.')->group(function () {
        Route::get('/', [SprintController::class, 'index'])->name('index');
        Route::post('/', [SprintController::class, 'store'])->name('store');
        Route::get('/{sprint}', [SprintController::class, 'show'])->name('show');
        Route::put('/{sprint}', [SprintController::class, 'update'])->name('update');
        Route::delete('/{sprint}', [SprintController::class, 'destroy'])->name('destroy');
        Route::post('/{sprint}/start', [SprintController::class, 'start'])->name('start');
        Route::post('/{sprint}/complete', [SprintController::class, 'complete'])->name('complete');
        Route::get('/{sprint}/burndown', [SprintController::class, 'burndown'])->name('burndown');
        Route::post('/{sprint}/tasks/{task}', [SprintController::class, 'addTask'])->name('tasks.add');
        Route::delete('/{sprint}/tasks/{task}', [SprintController::class, 'removeTask'])->name('tasks.remove');
    });
    
    /*
    |--------------------------------------------------------------------------
    | الملاحم (Epics)
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/epics')->name('epics.')->group(function () {
        Route::get('/', [EpicController::class, 'index'])->name('index');
        Route::post('/', [EpicController::class, 'store'])->name('store');
        Route::get('/{epic}', [EpicController::class, 'show'])->name('show');
        Route::put('/{epic}', [EpicController::class, 'update'])->name('update');
        Route::delete('/{epic}', [EpicController::class, 'destroy'])->name('destroy');
        Route::post('/{epic}/start', [EpicController::class, 'start'])->name('start');
        Route::post('/{epic}/complete', [EpicController::class, 'complete'])->name('complete');
        Route::post('/{epic}/breakdown', [EpicController::class, 'breakdown'])->name('breakdown');
    });
    
    /*
    |--------------------------------------------------------------------------
    | إدارة المخاطر
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/risks')->name('risks.')->group(function () {
        Route::get('/', [RiskController::class, 'index'])->name('index');
        Route::post('/', [RiskController::class, 'store'])->name('store');
        Route::get('/{risk}', [RiskController::class, 'show'])->name('show');
        Route::put('/{risk}', [RiskController::class, 'update'])->name('update');
        Route::delete('/{risk}', [RiskController::class, 'destroy'])->name('destroy');
        Route::post('/{risk}/assess', [RiskController::class, 'assess'])->name('assess');
        Route::post('/{risk}/mitigate', [RiskController::class, 'mitigate'])->name('mitigate');
        Route::get('/matrix', [RiskController::class, 'matrix'])->name('matrix');
        Route::get('/reports', [RiskController::class, 'reports'])->name('reports');
    });
    
    /*
    |--------------------------------------------------------------------------
    | إدارة المشكلات
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/issues')->name('issues.')->group(function () {
        Route::get('/', [IssueController::class, 'index'])->name('index');
        Route::post('/', [IssueController::class, 'store'])->name('store');
        Route::get('/{issue}', [IssueController::class, 'show'])->name('show');
        Route::put('/{issue}', [IssueController::class, 'update'])->name('update');
        Route::delete('/{issue}', [IssueController::class, 'destroy'])->name('destroy');
        Route::post('/{issue}/assign', [IssueController::class, 'assign'])->name('assign');
        Route::post('/{issue}/resolve', [IssueController::class, 'resolve'])->name('resolve');
        Route::post('/{issue}/close', [IssueController::class, 'close'])->name('close');
        Route::post('/{issue}/reopen', [IssueController::class, 'reopen'])->name('reopen');
        Route::post('/{issue}/comments', [IssueController::class, 'addComment'])->name('comments.store');
    });
    
    /*
    |--------------------------------------------------------------------------
    | التقارير والتحليلات
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/dashboard', [ReportController::class, 'dashboard'])->name('dashboard');
        Route::get('/progress', [ReportController::class, 'progress'])->name('progress');
        Route::get('/time', [ReportController::class, 'time'])->name('time');
        Route::get('/budget', [ReportController::class, 'budget'])->name('budget');
        Route::get('/team', [ReportController::class, 'team'])->name('team');
        Route::get('/quality', [ReportController::class, 'quality'])->name('quality');
        Route::get('/export/{type}', [ReportController::class, 'export'])->name('export');
    });
    
    /*
    |--------------------------------------------------------------------------
    | التحليلات المتقدمة
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/analytics')->name('analytics.')->group(function () {
        Route::get('/', [AnalyticsController::class, 'index'])->name('index');
        Route::get('/performance', [AnalyticsController::class, 'performance'])->name('performance');
        Route::get('/productivity', [AnalyticsController::class, 'productivity'])->name('productivity');
        Route::get('/trends', [AnalyticsController::class, 'trends'])->name('trends');
        Route::get('/predictions', [AnalyticsController::class, 'predictions'])->name('predictions');
    });
    
    /*
    |--------------------------------------------------------------------------
    | سير العمل والموافقات
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/workflows')->name('workflows.')->group(function () {
        Route::get('/', [WorkflowController::class, 'index'])->name('index');
        Route::post('/', [WorkflowController::class, 'store'])->name('store');
        Route::get('/{workflow}', [WorkflowController::class, 'show'])->name('show');
        Route::put('/{workflow}', [WorkflowController::class, 'update'])->name('update');
        Route::delete('/{workflow}', [WorkflowController::class, 'destroy'])->name('destroy');
        Route::post('/{workflow}/start', [WorkflowController::class, 'start'])->name('start');
        Route::post('/approvals/{approval}/approve', [WorkflowController::class, 'approve'])->name('approvals.approve');
        Route::post('/approvals/{approval}/reject', [WorkflowController::class, 'reject'])->name('approvals.reject');
    });
    
    /*
    |--------------------------------------------------------------------------
    | التكاملات
    |--------------------------------------------------------------------------
    */
    
    Route::prefix('{project}/integrations')->name('integrations.')->group(function () {
        Route::get('/', [IntegrationController::class, 'index'])->name('index');
        Route::post('/setup', [IntegrationController::class, 'setup'])->name('setup');
        Route::put('/{integration}', [IntegrationController::class, 'update'])->name('update');
        Route::delete('/{integration}', [IntegrationController::class, 'destroy'])->name('destroy');
        Route::post('/{integration}/sync', [IntegrationController::class, 'sync'])->name('sync');
        Route::post('/{integration}/test', [IntegrationController::class, 'test'])->name('test');
    });
});

/*
|--------------------------------------------------------------------------
| طرق القوالب
|--------------------------------------------------------------------------
*/

Route::middleware(['auth', 'verified'])->prefix('project-templates')->name('templates.')->group(function () {
    Route::get('/', [TemplateController::class, 'index'])->name('index');
    Route::get('/create', [TemplateController::class, 'create'])->name('create');
    Route::post('/', [TemplateController::class, 'store'])->name('store');
    Route::get('/{template}', [TemplateController::class, 'show'])->name('show');
    Route::get('/{template}/edit', [TemplateController::class, 'edit'])->name('edit');
    Route::put('/{template}', [TemplateController::class, 'update'])->name('update');
    Route::delete('/{template}', [TemplateController::class, 'destroy'])->name('destroy');
    Route::post('/{template}/apply', [TemplateController::class, 'apply'])->name('apply');
    Route::post('/{template}/duplicate', [TemplateController::class, 'duplicate'])->name('duplicate');
    Route::get('/{template}/export', [TemplateController::class, 'export'])->name('export');
    Route::post('/import', [TemplateController::class, 'import'])->name('import');
});

/*
|--------------------------------------------------------------------------
| طرق عامة
|--------------------------------------------------------------------------
*/

// الملفات العامة (روابط المشاركة)
Route::get('/files/public/{token}', [FileController::class, 'publicFile'])->name('files.public');

// Webhooks للتكاملات الخارجية
Route::post('/webhooks/{platform}', [IntegrationController::class, 'webhook'])->name('webhooks');

// API للتطبيقات الخارجية
Route::prefix('api/projects')->middleware(['auth:api'])->group(function () {
    // سيتم إضافة طرق API هنا
});
