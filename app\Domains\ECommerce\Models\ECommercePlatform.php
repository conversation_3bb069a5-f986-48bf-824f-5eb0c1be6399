<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج منصة التجارة الإلكترونية
 * يمثل منصات التجارة الإلكترونية المختلفة مثل Shopify, WooCommerce, Salla, إلخ
 */
class ECommercePlatform extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'name',
        'name_ar',
        'name_en',
        'slug',
        'type',
        'region',
        'description',
        'logo_url',
        'website_url',
        'api_documentation_url',
        'supported_features',
        'authentication_type',
        'api_version',
        'rate_limits',
        'webhook_support',
        'real_time_sync',
        'batch_sync',
        'supported_countries',
        'supported_currencies',
        'supported_languages',
        'integration_complexity',
        'setup_requirements',
        'pricing_model',
        'is_active',
        'is_popular',
        'sort_order',
        'configuration_schema',
        'default_settings',
        'sync_capabilities',
        'compliance_features',
        'security_features',
        'performance_metrics',
        'support_level',
        'documentation_quality',
        'community_size',
        'market_share',
        'reliability_score',
        'ease_of_integration',
        'feature_completeness',
        'cost_effectiveness',
        'scalability_rating',
        'maintenance_requirements',
        'update_frequency',
        'backward_compatibility',
        'migration_support',
        'testing_environment',
        'sandbox_available',
        'certification_required',
        'technical_requirements',
        'business_requirements',
        'legal_requirements',
        'data_retention_policy',
        'gdpr_compliance',
        'pci_compliance',
        'iso_compliance',
        'soc_compliance',
        'audit_capabilities',
        'monitoring_features',
        'alerting_system',
        'error_handling',
        'retry_mechanisms',
        'fallback_options',
        'disaster_recovery',
        'backup_features',
        'data_export_options',
        'data_import_options',
        'bulk_operations',
        'scheduled_sync',
        'custom_fields_support',
        'metadata_support',
        'tagging_system',
        'categorization_support',
        'search_capabilities',
        'filtering_options',
        'sorting_options',
        'pagination_support',
        'caching_support',
        'compression_support',
        'encryption_support',
        'signature_verification',
        'token_management',
        'session_management',
        'user_management',
        'role_management',
        'permission_system',
        'access_control',
        'ip_whitelisting',
        'geographic_restrictions',
        'time_based_restrictions',
        'usage_quotas',
        'throttling_mechanisms',
        'circuit_breaker',
        'health_checks',
        'status_monitoring',
        'performance_monitoring',
        'usage_analytics',
        'error_analytics',
        'success_metrics',
        'integration_metrics',
        'business_metrics',
        'technical_metrics',
        'user_experience_metrics',
        'satisfaction_scores',
        'recommendation_engine',
        'ai_capabilities',
        'ml_features',
        'automation_level',
        'workflow_support',
        'rule_engine',
        'condition_builder',
        'action_triggers',
        'event_handling',
        'notification_system',
        'alert_management',
        'escalation_procedures',
        'incident_management',
        'problem_resolution',
        'knowledge_base',
        'training_materials',
        'best_practices',
        'implementation_guides',
        'troubleshooting_guides',
        'faq_resources',
        'video_tutorials',
        'webinar_support',
        'consultation_services',
        'implementation_services',
        'migration_services',
        'training_services',
        'support_services',
        'maintenance_services',
        'upgrade_services',
        'customization_services',
        'integration_services',
        'optimization_services',
        'security_services',
        'compliance_services',
        'audit_services',
        'consulting_services',
        'strategic_planning',
        'roadmap_planning',
        'capacity_planning',
        'performance_planning',
        'security_planning',
        'compliance_planning',
        'risk_assessment',
        'impact_analysis',
        'cost_analysis',
        'roi_analysis',
        'benefit_analysis',
        'feasibility_study',
        'pilot_program',
        'proof_of_concept',
        'prototype_development',
        'mvp_development',
        'full_implementation',
        'rollout_strategy',
        'deployment_strategy',
        'testing_strategy',
        'validation_strategy',
        'verification_strategy',
        'acceptance_criteria',
        'success_criteria',
        'completion_criteria',
        'quality_criteria',
        'performance_criteria',
        'security_criteria',
        'compliance_criteria',
        'user_criteria',
        'business_criteria',
        'technical_criteria',
        'operational_criteria',
        'maintenance_criteria',
        'support_criteria',
        'documentation_criteria',
        'training_criteria',
        'handover_criteria',
        'warranty_terms',
        'support_terms',
        'maintenance_terms',
        'upgrade_terms',
        'licensing_terms',
        'usage_terms',
        'data_terms',
        'privacy_terms',
        'security_terms',
        'compliance_terms',
        'liability_terms',
        'indemnity_terms',
        'termination_terms',
        'renewal_terms',
        'modification_terms',
        'dispute_resolution',
        'governing_law',
        'jurisdiction',
        'arbitration_clause',
        'mediation_clause',
        'escalation_clause',
        'force_majeure',
        'confidentiality',
        'non_disclosure',
        'intellectual_property',
        'copyright_terms',
        'trademark_terms',
        'patent_terms',
        'trade_secret_terms',
        'proprietary_rights',
        'open_source_compliance',
        'third_party_licenses',
        'dependency_management',
        'version_control',
        'change_management',
        'release_management',
        'deployment_management',
        'configuration_management',
        'environment_management',
        'resource_management',
        'capacity_management',
        'performance_management',
        'availability_management',
        'reliability_management',
        'security_management',
        'compliance_management',
        'risk_management',
        'incident_management_system',
        'problem_management_system',
        'change_management_system',
        'release_management_system',
        'configuration_management_system',
        'asset_management_system',
        'service_management_system',
        'quality_management_system',
        'knowledge_management_system',
        'document_management_system',
        'workflow_management_system',
        'project_management_system',
        'task_management_system',
        'time_management_system',
        'resource_management_system',
        'budget_management_system',
        'cost_management_system',
        'financial_management_system',
        'accounting_integration',
        'erp_integration',
        'crm_integration',
        'inventory_integration',
        'warehouse_integration',
        'shipping_integration',
        'payment_integration',
        'tax_integration',
        'reporting_integration',
        'analytics_integration',
        'marketing_integration',
        'social_media_integration',
        'email_integration',
        'sms_integration',
        'notification_integration',
        'communication_integration',
        'collaboration_integration',
        'productivity_integration',
        'office_integration',
        'cloud_integration',
        'mobile_integration',
        'web_integration',
        'api_integration',
        'webhook_integration',
        'event_integration',
        'messaging_integration',
        'queue_integration',
        'cache_integration',
        'database_integration',
        'storage_integration',
        'cdn_integration',
        'security_integration',
        'monitoring_integration',
        'logging_integration',
        'debugging_integration',
        'testing_integration',
        'deployment_integration',
        'ci_cd_integration',
        'devops_integration',
        'infrastructure_integration',
        'platform_integration',
        'service_integration',
        'application_integration',
        'data_integration',
        'process_integration',
        'workflow_integration',
        'business_integration',
        'operational_integration',
        'strategic_integration',
        'tactical_integration',
        'technical_integration',
        'functional_integration',
        'cross_functional_integration',
        'enterprise_integration',
        'ecosystem_integration',
        'partner_integration',
        'vendor_integration',
        'supplier_integration',
        'customer_integration',
        'stakeholder_integration',
        'community_integration',
        'industry_integration',
        'regulatory_integration',
        'compliance_integration_features',
        'governance_integration',
        'risk_integration',
        'audit_integration',
        'security_integration_features',
        'privacy_integration',
        'data_protection_integration',
        'consent_management_integration',
        'preference_management_integration',
        'identity_management_integration',
        'access_management_integration',
        'authentication_integration',
        'authorization_integration',
        'single_sign_on_integration',
        'multi_factor_authentication',
        'biometric_authentication',
        'certificate_management',
        'key_management',
        'secret_management',
        'credential_management',
        'token_management_system',
        'session_management_system',
        'state_management',
        'cache_management',
        'memory_management',
        'storage_management',
        'backup_management',
        'archive_management',
        'retention_management',
        'disposal_management',
        'destruction_management',
        'recovery_management',
        'restoration_management',
        'migration_management',
        'transformation_management',
        'modernization_management',
        'optimization_management',
        'enhancement_management',
        'improvement_management',
        'innovation_management',
        'research_management',
        'development_management',
        'testing_management',
        'validation_management',
        'verification_management',
        'certification_management',
        'accreditation_management',
        'standardization_management',
        'harmonization_management',
        'integration_management',
        'coordination_management',
        'synchronization_management',
        'orchestration_management',
        'choreography_management',
        'composition_management',
        'decomposition_management',
        'modularization_management',
        'componentization_management',
        'service_orientation',
        'microservices_support',
        'containerization_support',
        'virtualization_support',
        'cloud_native_support',
        'serverless_support',
        'edge_computing_support',
        'iot_support',
        'mobile_first_support',
        'responsive_design_support',
        'progressive_web_app_support',
        'single_page_application_support',
        'multi_page_application_support',
        'hybrid_application_support',
        'native_application_support',
        'cross_platform_support',
        'multi_platform_support',
        'multi_device_support',
        'multi_channel_support',
        'omnichannel_support',
        'multichannel_support',
        'cross_channel_support',
        'unified_commerce_support',
        'headless_commerce_support',
        'api_first_support',
        'composable_commerce_support',
        'modular_commerce_support',
        'flexible_commerce_support',
        'scalable_commerce_support',
        'extensible_commerce_support',
        'customizable_commerce_support',
        'configurable_commerce_support',
        'adaptable_commerce_support',
        'agile_commerce_support',
        'lean_commerce_support',
        'efficient_commerce_support',
        'optimized_commerce_support',
        'high_performance_commerce',
        'high_availability_commerce',
        'high_reliability_commerce',
        'high_security_commerce',
        'high_quality_commerce',
        'user_friendly_commerce',
        'developer_friendly_commerce',
        'business_friendly_commerce',
        'cost_effective_commerce',
        'time_efficient_commerce',
        'resource_efficient_commerce',
        'energy_efficient_commerce',
        'environmentally_friendly_commerce',
        'sustainable_commerce_support',
        'green_commerce_support',
        'ethical_commerce_support',
        'responsible_commerce_support',
        'inclusive_commerce_support',
        'accessible_commerce_support',
        'universal_design_support',
        'internationalization_support',
        'localization_support',
        'globalization_support',
        'regionalization_support',
        'culturalization_support',
        'personalization_support',
        'customization_support',
        'individualization_support',
        'segmentation_support',
        'targeting_support',
        'recommendation_support',
        'prediction_support',
        'forecasting_support',
        'planning_support',
        'scheduling_support',
        'optimization_support',
        'automation_support',
        'intelligence_support',
        'learning_support',
        'adaptation_support',
        'evolution_support',
        'innovation_support',
        'transformation_support',
        'disruption_support',
        'revolution_support',
        'paradigm_shift_support',
        'future_readiness',
        'technology_readiness',
        'market_readiness',
        'business_readiness',
        'operational_readiness',
        'organizational_readiness',
        'cultural_readiness',
        'strategic_readiness',
        'tactical_readiness',
        'execution_readiness',
        'implementation_readiness',
        'deployment_readiness',
        'launch_readiness',
        'go_live_readiness',
        'production_readiness',
        'enterprise_readiness',
        'scale_readiness',
        'growth_readiness',
        'expansion_readiness',
        'global_readiness',
        'digital_readiness',
        'cloud_readiness',
        'mobile_readiness',
        'ai_readiness',
        'iot_readiness',
        'blockchain_readiness',
        'quantum_readiness',
        'next_gen_readiness',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'supported_features' => 'array',
        'rate_limits' => 'array',
        'supported_countries' => 'array',
        'supported_currencies' => 'array',
        'supported_languages' => 'array',
        'setup_requirements' => 'array',
        'configuration_schema' => 'array',
        'default_settings' => 'array',
        'sync_capabilities' => 'array',
        'compliance_features' => 'array',
        'security_features' => 'array',
        'performance_metrics' => 'array',
        'technical_requirements' => 'array',
        'business_requirements' => 'array',
        'legal_requirements' => 'array',
        'webhook_support' => 'boolean',
        'real_time_sync' => 'boolean',
        'batch_sync' => 'boolean',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
        'sandbox_available' => 'boolean',
        'certification_required' => 'boolean',
        'gdpr_compliance' => 'boolean',
        'pci_compliance' => 'boolean',
        'iso_compliance' => 'boolean',
        'soc_compliance' => 'boolean',
        'custom_fields_support' => 'boolean',
        'metadata_support' => 'boolean',
        'caching_support' => 'boolean',
        'compression_support' => 'boolean',
        'encryption_support' => 'boolean',
        'signature_verification' => 'boolean',
        'ai_capabilities' => 'array',
        'ml_features' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'type', 'region', 'is_active', 'api_version',
                'supported_features', 'sync_capabilities'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقة مع متاجر التجارة الإلكترونية
     */
    public function stores(): HasMany
    {
        return $this->hasMany(ECommerceStore::class, 'platform_id');
    }

    /**
     * العلاقة مع تكاملات المنصة
     */
    public function integrations(): HasMany
    {
        return $this->hasMany(ECommerceIntegration::class, 'platform_id');
    }

    /**
     * العلاقة مع إعدادات المنصة
     */
    public function platformSettings(): HasMany
    {
        return $this->hasMany(ECommercePlatformSetting::class, 'platform_id');
    }

    /**
     * العلاقة مع منشئ المنصة
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * العلاقة مع محدث المنصة
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    public function scopeByRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeSupportsFeature($query, $feature)
    {
        return $query->whereJsonContains('supported_features', $feature);
    }

    public function scopeSupportsCountry($query, $country)
    {
        return $query->whereJsonContains('supported_countries', $country);
    }

    public function scopeSupportsCurrency($query, $currency)
    {
        return $query->whereJsonContains('supported_currencies', $currency);
    }

    public function scopeSupportsLanguage($query, $language)
    {
        return $query->whereJsonContains('supported_languages', $language);
    }

    /**
     * الطرق المساعدة
     */
    public function isGlobal(): bool
    {
        return $this->region === 'global';
    }

    public function isRegional(): bool
    {
        return $this->region !== 'global';
    }

    public function isArabic(): bool
    {
        return in_array($this->region, ['arabic', 'middle_east', 'gulf', 'north_africa']);
    }

    public function supportsRealTimeSync(): bool
    {
        return $this->real_time_sync;
    }

    public function supportsBatchSync(): bool
    {
        return $this->batch_sync;
    }

    public function supportsWebhooks(): bool
    {
        return $this->webhook_support;
    }

    public function hasApiDocumentation(): bool
    {
        return !empty($this->api_documentation_url);
    }

    public function hasSandbox(): bool
    {
        return $this->sandbox_available;
    }

    public function requiresCertification(): bool
    {
        return $this->certification_required;
    }

    public function isCompliant($standard): bool
    {
        $complianceMap = [
            'gdpr' => $this->gdpr_compliance,
            'pci' => $this->pci_compliance,
            'iso' => $this->iso_compliance,
            'soc' => $this->soc_compliance,
        ];

        return $complianceMap[$standard] ?? false;
    }

    public function getIntegrationComplexity(): string
    {
        return $this->integration_complexity ?? 'medium';
    }

    public function getReliabilityScore(): float
    {
        return $this->reliability_score ?? 0.0;
    }

    public function getEaseOfIntegration(): float
    {
        return $this->ease_of_integration ?? 0.0;
    }

    public function getFeatureCompleteness(): float
    {
        return $this->feature_completeness ?? 0.0;
    }

    public function getCostEffectiveness(): float
    {
        return $this->cost_effectiveness ?? 0.0;
    }

    public function getScalabilityRating(): float
    {
        return $this->scalability_rating ?? 0.0;
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getReliabilityScore(),
            $this->getEaseOfIntegration(),
            $this->getFeatureCompleteness(),
            $this->getCostEffectiveness(),
            $this->getScalabilityRating(),
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);
        
        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0.0;
    }

    public function getRecommendationLevel(): string
    {
        $score = $this->getOverallScore();

        if ($score >= 4.5) return 'highly_recommended';
        if ($score >= 3.5) return 'recommended';
        if ($score >= 2.5) return 'acceptable';
        if ($score >= 1.5) return 'limited';
        
        return 'not_recommended';
    }

    public function getSupportedSyncCapabilities(): array
    {
        return $this->sync_capabilities ?? [];
    }

    public function supportsSyncCapability($capability): bool
    {
        return in_array($capability, $this->getSupportedSyncCapabilities());
    }

    public function getAuthenticationType(): string
    {
        return $this->authentication_type ?? 'api_key';
    }

    public function getRateLimits(): array
    {
        return $this->rate_limits ?? [];
    }

    public function hasRateLimit($type): bool
    {
        return isset($this->getRateLimits()[$type]);
    }

    public function getRateLimit($type): ?int
    {
        return $this->getRateLimits()[$type] ?? null;
    }

    public function getConfigurationSchema(): array
    {
        return $this->configuration_schema ?? [];
    }

    public function getDefaultSettings(): array
    {
        return $this->default_settings ?? [];
    }

    public function getSetupRequirements(): array
    {
        return $this->setup_requirements ?? [];
    }

    public function getTechnicalRequirements(): array
    {
        return $this->technical_requirements ?? [];
    }

    public function getBusinessRequirements(): array
    {
        return $this->business_requirements ?? [];
    }

    public function getLegalRequirements(): array
    {
        return $this->legal_requirements ?? [];
    }

    public function getSecurityFeatures(): array
    {
        return $this->security_features ?? [];
    }

    public function getComplianceFeatures(): array
    {
        return $this->compliance_features ?? [];
    }

    public function getPerformanceMetrics(): array
    {
        return $this->performance_metrics ?? [];
    }

    public function getAICapabilities(): array
    {
        return $this->ai_capabilities ?? [];
    }

    public function getMLFeatures(): array
    {
        return $this->ml_features ?? [];
    }

    public function hasAICapability($capability): bool
    {
        return in_array($capability, $this->getAICapabilities());
    }

    public function hasMLFeature($feature): bool
    {
        return in_array($feature, $this->getMLFeatures());
    }

    public function isAIEnabled(): bool
    {
        return !empty($this->getAICapabilities()) || !empty($this->getMLFeatures());
    }

    public function getMarketShare(): float
    {
        return $this->market_share ?? 0.0;
    }

    public function getCommunitySize(): int
    {
        return $this->community_size ?? 0;
    }

    public function getDocumentationQuality(): float
    {
        return $this->documentation_quality ?? 0.0;
    }

    public function getSupportLevel(): string
    {
        return $this->support_level ?? 'basic';
    }

    public function getUpdateFrequency(): string
    {
        return $this->update_frequency ?? 'monthly';
    }

    public function hasBackwardCompatibility(): bool
    {
        return $this->backward_compatibility ?? false;
    }

    public function supportsMigration(): bool
    {
        return $this->migration_support ?? false;
    }

    public function hasTestingEnvironment(): bool
    {
        return $this->testing_environment ?? false;
    }

    public function getMaintenanceRequirements(): array
    {
        return $this->maintenance_requirements ?? [];
    }

    public function getPricingModel(): string
    {
        return $this->pricing_model ?? 'subscription';
    }

    public function getDataRetentionPolicy(): array
    {
        return $this->data_retention_policy ?? [];
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'slug' => $this->slug,
            'type' => $this->type,
            'region' => $this->region,
            'description' => $this->description,
            'logo_url' => $this->logo_url,
            'website_url' => $this->website_url,
            'is_active' => $this->is_active,
            'is_popular' => $this->is_popular,
            'overall_score' => $this->getOverallScore(),
            'recommendation_level' => $this->getRecommendationLevel(),
            'integration_complexity' => $this->getIntegrationComplexity(),
            'authentication_type' => $this->getAuthenticationType(),
            'supports_real_time' => $this->supportsRealTimeSync(),
            'supports_webhooks' => $this->supportsWebhooks(),
            'has_sandbox' => $this->hasSandbox(),
            'supported_features' => $this->supported_features,
            'sync_capabilities' => $this->getSupportedSyncCapabilities(),
            'supported_countries' => $this->supported_countries,
            'supported_currencies' => $this->supported_currencies,
            'supported_languages' => $this->supported_languages,
        ];
    }
}
