<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * نموذج تكوين الفوترة الإلكترونية
 * يدير التكامل مع أنظمة الفوترة الإلكترونية الحكومية
 */
class EInvoicingConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'country_id',
        'system_name',
        'system_name_ar',
        'system_name_en',
        'authority_name',
        'api_base_url',
        'api_version',
        'authentication_method',
        'authentication_config',
        'certificate_config',
        'digital_signature_required',
        'encryption_required',
        'supported_formats',
        'mandatory_fields',
        'optional_fields',
        'validation_rules',
        'submission_methods',
        'real_time_submission',
        'batch_submission_allowed',
        'max_batch_size',
        'retry_configuration',
        'webhook_config',
        'status_check_config',
        'archival_requirements',
        'compliance_levels',
        'implementation_phases',
        'exemption_criteria',
        'penalty_structure',
        'technical_specifications',
        'testing_environment',
        'production_environment',
        'support_contacts',
        'documentation_links',
        'sdk_availability',
        'rate_limits',
        'maintenance_windows',
        'is_active',
        'mandatory_from',
        'last_updated_by_authority',
    ];

    protected $casts = [
        'authentication_config' => 'array',
        'certificate_config' => 'array',
        'supported_formats' => 'array',
        'mandatory_fields' => 'array',
        'optional_fields' => 'array',
        'validation_rules' => 'array',
        'submission_methods' => 'array',
        'retry_configuration' => 'array',
        'webhook_config' => 'array',
        'status_check_config' => 'array',
        'archival_requirements' => 'array',
        'compliance_levels' => 'array',
        'implementation_phases' => 'array',
        'exemption_criteria' => 'array',
        'penalty_structure' => 'array',
        'technical_specifications' => 'array',
        'testing_environment' => 'array',
        'production_environment' => 'array',
        'support_contacts' => 'array',
        'documentation_links' => 'array',
        'sdk_availability' => 'array',
        'rate_limits' => 'array',
        'maintenance_windows' => 'array',
        'digital_signature_required' => 'boolean',
        'encryption_required' => 'boolean',
        'real_time_submission' => 'boolean',
        'batch_submission_allowed' => 'boolean',
        'is_active' => 'boolean',
        'mandatory_from' => 'date',
        'last_updated_by_authority' => 'datetime',
    ];

    /**
     * أنظمة الفوترة الإلكترونية المدعومة
     */
    const SUPPORTED_SYSTEMS = [
        'SA_FATOORA' => [
            'name' => 'فاتورة',
            'authority' => 'ZATCA',
            'country' => 'SA',
            'phases' => ['phase1', 'phase2'],
        ],
        'EG_ETA' => [
            'name' => 'منظومة الفاتورة الإلكترونية',
            'authority' => 'ETA',
            'country' => 'EG',
            'phases' => ['b2b', 'b2c'],
        ],
        'AE_FTA' => [
            'name' => 'نظام الفوترة الإلكترونية',
            'authority' => 'FTA',
            'country' => 'AE',
            'phases' => ['voluntary', 'mandatory'],
        ],
        'MA_DGI' => [
            'name' => 'منصة الإفصاح الضريبي',
            'authority' => 'DGI',
            'country' => 'MA',
            'phases' => ['pilot', 'rollout'],
        ],
    ];

    /**
     * طرق المصادقة
     */
    const AUTHENTICATION_METHODS = [
        'oauth2' => 'OAuth 2.0',
        'api_key' => 'API Key',
        'certificate' => 'Digital Certificate',
        'jwt' => 'JSON Web Token',
        'basic_auth' => 'Basic Authentication',
        'mutual_tls' => 'Mutual TLS',
    ];

    /**
     * تنسيقات الفواتير المدعومة
     */
    const SUPPORTED_FORMATS = [
        'xml' => 'XML',
        'json' => 'JSON',
        'pdf' => 'PDF',
        'ubl' => 'UBL 2.1',
        'edifact' => 'UN/EDIFACT',
        'custom' => 'Custom Format',
    ];

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع الفواتير الإلكترونية
     */
    public function electronicInvoices(): HasMany
    {
        return $this->hasMany(ElectronicInvoice::class);
    }

    /**
     * العلاقة مع سجلات التقديم
     */
    public function submissionLogs(): HasMany
    {
        return $this->hasMany(EInvoiceSubmissionLog::class);
    }

    /**
     * إرسال فاتورة إلكترونية
     */
    public function submitInvoice(array $invoiceData): array
    {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateInvoiceData($invoiceData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                    'error_code' => 'VALIDATION_FAILED',
                ];
            }

            // تحضير البيانات للإرسال
            $preparedData = $this->prepareInvoiceData($invoiceData);

            // إضافة التوقيع الرقمي إذا كان مطلوباً
            if ($this->digital_signature_required) {
                $preparedData = $this->addDigitalSignature($preparedData);
            }

            // إضافة التشفير إذا كان مطلوباً
            if ($this->encryption_required) {
                $preparedData = $this->encryptData($preparedData);
            }

            // إرسال الفاتورة
            $response = $this->sendToAuthority($preparedData);

            // تسجيل النتيجة
            $this->logSubmission($invoiceData, $response);

            return $response;

        } catch (\Exception $e) {
            Log::error('فشل في إرسال الفاتورة الإلكترونية', [
                'country' => $this->country->code,
                'system' => $this->system_name,
                'error' => $e->getMessage(),
                'invoice_data' => $invoiceData,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'SUBMISSION_FAILED',
            ];
        }
    }

    /**
     * التحقق من صحة بيانات الفاتورة
     */
    protected function validateInvoiceData(array $invoiceData): array
    {
        $errors = [];
        $mandatoryFields = $this->mandatory_fields ?? [];
        $validationRules = $this->validation_rules ?? [];

        // فحص الحقول الإلزامية
        foreach ($mandatoryFields as $field) {
            if (!isset($invoiceData[$field]) || empty($invoiceData[$field])) {
                $errors[$field][] = "الحقل {$field} مطلوب";
            }
        }

        // تطبيق قواعد التحقق
        foreach ($validationRules as $field => $rules) {
            $value = $invoiceData[$field] ?? null;

            foreach ($rules as $rule) {
                if (!$this->validateField($value, $rule)) {
                    $errors[$field][] = $rule['message'] ?? "قيمة غير صحيحة للحقل {$field}";
                }
            }
        }

        // فحص خاص بكل دولة
        $countrySpecificErrors = $this->validateCountrySpecificRules($invoiceData);
        $errors = array_merge($errors, $countrySpecificErrors);

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * فحص قواعد خاصة بكل دولة
     */
    protected function validateCountrySpecificRules(array $invoiceData): array
    {
        $errors = [];
        $countryCode = $this->country->code;

        switch ($countryCode) {
            case 'SA':
                $errors = array_merge($errors, $this->validateSaudiRules($invoiceData));
                break;
            case 'EG':
                $errors = array_merge($errors, $this->validateEgyptianRules($invoiceData));
                break;
            case 'AE':
                $errors = array_merge($errors, $this->validateUAERules($invoiceData));
                break;
            case 'MA':
                $errors = array_merge($errors, $this->validateMoroccanRules($invoiceData));
                break;
        }

        return $errors;
    }

    /**
     * فحص قواعد السعودية (فاتورة)
     */
    protected function validateSaudiRules(array $invoiceData): array
    {
        $errors = [];

        // فحص QR Code
        if (empty($invoiceData['qr_code'])) {
            $errors['qr_code'][] = 'QR Code مطلوب للفواتير السعودية';
        }

        // فحص UUID
        if (empty($invoiceData['uuid']) || !$this->isValidUUID($invoiceData['uuid'])) {
            $errors['uuid'][] = 'UUID صحيح مطلوب';
        }

        // فحص رقم التسجيل الضريبي
        if (empty($invoiceData['seller_tax_number']) || !$this->isValidSaudiTaxNumber($invoiceData['seller_tax_number'])) {
            $errors['seller_tax_number'][] = 'رقم التسجيل الضريبي السعودي غير صحيح';
        }

        return $errors;
    }

    /**
     * فحص قواعد مصر (ETA)
     */
    protected function validateEgyptianRules(array $invoiceData): array
    {
        $errors = [];

        // فحص التوقيع الرقمي
        if (empty($invoiceData['digital_signature'])) {
            $errors['digital_signature'][] = 'التوقيع الرقمي مطلوب للفواتير المصرية';
        }

        // فحص الرقم الضريبي
        if (empty($invoiceData['seller_tax_number']) || !$this->isValidEgyptianTaxNumber($invoiceData['seller_tax_number'])) {
            $errors['seller_tax_number'][] = 'الرقم الضريبي المصري غير صحيح';
        }

        return $errors;
    }

    /**
     * فحص قواعد الإمارات (FTA)
     */
    protected function validateUAERules(array $invoiceData): array
    {
        $errors = [];

        // فحص TRN
        if (empty($invoiceData['seller_trn']) || !$this->isValidUAETRN($invoiceData['seller_trn'])) {
            $errors['seller_trn'][] = 'رقم التسجيل الضريبي الإماراتي (TRN) غير صحيح';
        }

        return $errors;
    }

    /**
     * فحص قواعد المغرب (DGI)
     */
    protected function validateMoroccanRules(array $invoiceData): array
    {
        $errors = [];

        // فحص المعرف الضريبي
        if (empty($invoiceData['seller_tax_id']) || !$this->isValidMoroccanTaxId($invoiceData['seller_tax_id'])) {
            $errors['seller_tax_id'][] = 'المعرف الضريبي المغربي غير صحيح';
        }

        return $errors;
    }

    /**
     * تحضير بيانات الفاتورة للإرسال
     */
    protected function prepareInvoiceData(array $invoiceData): array
    {
        $countryCode = $this->country->code;

        return match ($countryCode) {
            'SA' => $this->prepareSaudiInvoiceData($invoiceData),
            'EG' => $this->prepareEgyptianInvoiceData($invoiceData),
            'AE' => $this->prepareUAEInvoiceData($invoiceData),
            'MA' => $this->prepareMoroccanInvoiceData($invoiceData),
            default => $invoiceData,
        };
    }

    /**
     * تحضير بيانات الفاتورة السعودية
     */
    protected function prepareSaudiInvoiceData(array $invoiceData): array
    {
        return [
            'invoiceHash' => $this->generateInvoiceHash($invoiceData),
            'uuid' => $invoiceData['uuid'],
            'invoice' => $this->convertToUBL($invoiceData),
            'invoiceRequest' => [
                'invoice' => base64_encode($this->generateXML($invoiceData)),
                'invoiceHash' => $this->generateInvoiceHash($invoiceData),
                'uuid' => $invoiceData['uuid'],
            ],
        ];
    }

    /**
     * تحضير بيانات الفاتورة المصرية
     */
    protected function prepareEgyptianInvoiceData(array $invoiceData): array
    {
        return [
            'documents' => [
                [
                    'issuer' => $invoiceData['seller'],
                    'receiver' => $invoiceData['buyer'],
                    'documentType' => $invoiceData['document_type'] ?? 'I',
                    'documentTypeVersion' => '1.0',
                    'dateTimeIssued' => $invoiceData['issue_date'],
                    'taxpayerActivityCode' => $invoiceData['activity_code'],
                    'internalID' => $invoiceData['internal_id'],
                    'invoiceLines' => $invoiceData['line_items'],
                    'totalSalesAmount' => $invoiceData['total_before_tax'],
                    'totalTaxableFees' => $invoiceData['total_fees'],
                    'netAmount' => $invoiceData['net_amount'],
                    'taxTotals' => $invoiceData['tax_totals'],
                    'totalAmount' => $invoiceData['total_amount'],
                    'signatures' => [$invoiceData['digital_signature']],
                ]
            ]
        ];
    }

    /**
     * إرسال البيانات للسلطة المختصة
     */
    protected function sendToAuthority(array $data): array
    {
        $config = $this->production_environment;
        $headers = $this->buildHeaders();

        $response = Http::withHeaders($headers)
            ->timeout(30)
            ->retry(3, 1000)
            ->post($config['submission_endpoint'], $data);

        if ($response->successful()) {
            $responseData = $response->json();
            
            return [
                'success' => true,
                'submission_id' => $responseData['submissionId'] ?? null,
                'invoice_uuid' => $responseData['invoiceUuid'] ?? null,
                'clearance_status' => $responseData['clearanceStatus'] ?? 'CLEARED',
                'validation_results' => $responseData['validationResults'] ?? [],
                'qr_code' => $responseData['qrCode'] ?? null,
                'response_data' => $responseData,
            ];
        } else {
            return [
                'success' => false,
                'error' => $response->body(),
                'status_code' => $response->status(),
                'error_code' => 'API_ERROR',
            ];
        }
    }

    /**
     * بناء رؤوس HTTP
     */
    protected function buildHeaders(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        $authConfig = $this->authentication_config;
        $authMethod = $this->authentication_method;

        switch ($authMethod) {
            case 'oauth2':
                $token = $this->getOAuthToken();
                $headers['Authorization'] = "Bearer {$token}";
                break;
            case 'api_key':
                $headers['X-API-Key'] = $authConfig['api_key'];
                break;
            case 'basic_auth':
                $credentials = base64_encode($authConfig['username'] . ':' . $authConfig['password']);
                $headers['Authorization'] = "Basic {$credentials}";
                break;
        }

        return $headers;
    }

    /**
     * الحصول على رمز OAuth
     */
    protected function getOAuthToken(): string
    {
        $cacheKey = "oauth_token_{$this->country->code}_{$this->system_name}";
        
        return cache()->remember($cacheKey, 3600, function () {
            $authConfig = $this->authentication_config;
            
            $response = Http::asForm()->post($authConfig['token_url'], [
                'grant_type' => 'client_credentials',
                'client_id' => $authConfig['client_id'],
                'client_secret' => $authConfig['client_secret'],
                'scope' => $authConfig['scope'] ?? '',
            ]);

            if ($response->successful()) {
                return $response->json()['access_token'];
            }

            throw new \Exception('فشل في الحصول على رمز المصادقة');
        });
    }

    /**
     * إضافة التوقيع الرقمي
     */
    protected function addDigitalSignature(array $data): array
    {
        $certificateConfig = $this->certificate_config;
        
        // هنا يتم إضافة منطق التوقيع الرقمي
        // يعتمد على نوع الشهادة والخوارزمية المستخدمة
        
        $data['digital_signature'] = [
            'algorithm' => $certificateConfig['algorithm'] ?? 'RSA-SHA256',
            'certificate' => $certificateConfig['certificate_data'],
            'signature' => $this->generateSignature($data, $certificateConfig),
            'timestamp' => now()->toISOString(),
        ];

        return $data;
    }

    /**
     * توليد التوقيع
     */
    protected function generateSignature(array $data, array $certificateConfig): string
    {
        // منطق توليد التوقيع الرقمي
        // هذا مثال مبسط - في الواقع يحتاج لمكتبات تشفير متخصصة
        
        $dataToSign = json_encode($data);
        $privateKey = $certificateConfig['private_key'];
        
        openssl_sign($dataToSign, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        
        return base64_encode($signature);
    }

    /**
     * تشفير البيانات
     */
    protected function encryptData(array $data): array
    {
        // منطق تشفير البيانات حسب متطلبات كل دولة
        return $data;
    }

    /**
     * تسجيل عملية الإرسال
     */
    protected function logSubmission(array $invoiceData, array $response): void
    {
        EInvoiceSubmissionLog::create([
            'e_invoicing_configuration_id' => $this->id,
            'invoice_reference' => $invoiceData['invoice_number'] ?? null,
            'submission_data' => $invoiceData,
            'response_data' => $response,
            'status' => $response['success'] ? 'success' : 'failed',
            'submission_id' => $response['submission_id'] ?? null,
            'error_message' => $response['error'] ?? null,
            'submitted_at' => now(),
        ]);
    }

    /**
     * فحص حالة الفاتورة
     */
    public function checkInvoiceStatus(string $submissionId): array
    {
        $config = $this->production_environment;
        $headers = $this->buildHeaders();

        $response = Http::withHeaders($headers)
            ->get($config['status_endpoint'] . '/' . $submissionId);

        if ($response->successful()) {
            return [
                'success' => true,
                'status' => $response->json(),
            ];
        }

        return [
            'success' => false,
            'error' => $response->body(),
        ];
    }

    /**
     * التحقق من صحة الأرقام الضريبية
     */
    protected function isValidSaudiTaxNumber(string $taxNumber): bool
    {
        return preg_match('/^3\d{14}$/', $taxNumber);
    }

    protected function isValidEgyptianTaxNumber(string $taxNumber): bool
    {
        return preg_match('/^\d{9}$/', $taxNumber);
    }

    protected function isValidUAETRN(string $trn): bool
    {
        return preg_match('/^1\d{14}$/', $trn);
    }

    protected function isValidMoroccanTaxId(string $taxId): bool
    {
        return preg_match('/^\d{8}$/', $taxId);
    }

    protected function isValidUUID(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid);
    }

    /**
     * توليد hash للفاتورة
     */
    protected function generateInvoiceHash(array $invoiceData): string
    {
        $dataToHash = json_encode($invoiceData, JSON_SORT_KEYS);
        return hash('sha256', $dataToHash);
    }

    /**
     * تحويل إلى UBL
     */
    protected function convertToUBL(array $invoiceData): string
    {
        // منطق تحويل البيانات إلى تنسيق UBL
        // هذا مثال مبسط
        return '<?xml version="1.0" encoding="UTF-8"?><Invoice>...</Invoice>';
    }

    /**
     * توليد XML
     */
    protected function generateXML(array $invoiceData): string
    {
        // منطق توليد XML حسب المعايير المطلوبة
        return $this->convertToUBL($invoiceData);
    }

    /**
     * التحقق من صحة الحقل
     */
    protected function validateField($value, array $rule): bool
    {
        $type = $rule['type'];

        return match ($type) {
            'required' => !empty($value),
            'numeric' => is_numeric($value),
            'email' => filter_var($value, FILTER_VALIDATE_EMAIL) !== false,
            'date' => strtotime($value) !== false,
            'regex' => preg_match($rule['pattern'], $value),
            'min_length' => strlen($value) >= $rule['value'],
            'max_length' => strlen($value) <= $rule['value'],
            'in' => in_array($value, $rule['values']),
            default => true,
        };
    }

    /**
     * Scope للأنظمة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للأنظمة الإلزامية
     */
    public function scopeMandatory($query)
    {
        return $query->where('mandatory_from', '<=', now());
    }
}
