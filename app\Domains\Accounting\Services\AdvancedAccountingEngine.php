<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\JournalEntryLine;
use App\Domains\Accounting\Models\AccountingPeriod;
use App\Domains\Accounting\Models\AccountMapping;
use App\Domains\Accounting\Models\BankTransaction;
use App\Domains\Accounting\Models\ChartOfAccounts;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * محرك المحاسبة المتقدم - Enterprise-Grade
 * يدعم معايير محاسبية متعددة والتحويل التلقائي بينها مع الذكاء الاصطناعي
 */
class AdvancedAccountingEngine
{
    protected array $supportedStandards = [
        'IFRS' => 'المعايير الدولية لإعداد التقارير المالية',
        'PCGM' => 'المخطط المحاسبي العام المغربي',
        'GAAP' => 'مبادئ المحاسبة المقبولة عموماً',
        'SOCOMA' => 'نظام المحاسبة الموحد للشركات العربية',
    ];

    protected string $defaultStandard = 'IFRS';
    protected array $aiServices = [];

    public function __construct()
    {
        $this->loadAIServices();
    }

    /**
     * تحميل خدمات الذكاء الاصطناعي
     */
    protected function loadAIServices(): void
    {
        $this->aiServices = [
            'transaction_categorization' => app(\App\Domains\Accounting\Services\AITransactionCategorizationService::class),
            'anomaly_detection' => app(\App\Domains\Accounting\Services\AnomalyDetectionService::class),
            'predictive_analytics' => app(\App\Domains\Accounting\Services\PredictiveAccountingService::class),
            'ocr_invoice_scanner' => app(\App\Domains\Accounting\Services\OCRInvoiceScannerService::class),
        ];
    }

    /**
     * إنشاء قيد محاسبي متقدم مع دعم المعايير المتعددة
     */
    public function createJournalEntry(array $entryData, string $standard = null): JournalEntry
    {
        $standard = $standard ?? $this->defaultStandard;

        DB::beginTransaction();
        try {
            // تطبيق قواعد المعيار المحاسبي
            $entryData = $this->applyAccountingStandard($entryData, $standard);

            $entry = JournalEntry::create([
                'entry_number' => $this->generateEntryNumber($standard),
                'entry_date' => $entryData['entry_date'],
                'description' => $entryData['description'],
                'reference' => $entryData['reference'] ?? null,
                'source_type' => $entryData['source_type'] ?? null,
                'source_id' => $entryData['source_id'] ?? null,
                'currency' => $entryData['currency'] ?? 'MAD',
                'exchange_rate' => $entryData['exchange_rate'] ?? 1.0,
                'accounting_standard' => $standard,
                'status' => 'DRAFT',
                'created_by' => auth()->id(),
                'metadata' => $entryData['metadata'] ?? [],
            ]);

            $totalDebits = 0;
            $totalCredits = 0;

            foreach ($entryData['lines'] as $lineData) {
                // تطبيق خريطة الحسابات للمعيار المحدد
                $accountId = $this->mapAccountToStandard($lineData['account_id'], $standard);

                $line = $entry->lines()->create([
                    'account_id' => $accountId,
                    'debit_amount' => $lineData['debit_amount'] ?? 0,
                    'credit_amount' => $lineData['credit_amount'] ?? 0,
                    'description' => $lineData['description'] ?? $entry->description,
                    'cost_center_id' => $lineData['cost_center_id'] ?? null,
                    'project_id' => $lineData['project_id'] ?? null,
                    'department_id' => $lineData['department_id'] ?? null,
                    'employee_id' => $lineData['employee_id'] ?? null,
                    'tax_code' => $lineData['tax_code'] ?? null,
                    'analytics_tags' => $lineData['analytics_tags'] ?? [],
                ]);

                $totalDebits += $line->debit_amount;
                $totalCredits += $line->credit_amount;
            }

            // التحقق من توازن القيد
            if (abs($totalDebits - $totalCredits) > 0.01) {
                throw new \Exception('القيد غير متوازن: المدين = ' . $totalDebits . '، الدائن = ' . $totalCredits);
            }

            $entry->update([
                'total_debit' => $totalDebits,
                'total_credit' => $totalCredits,
            ]);

            // تطبيق الذكاء الاصطناعي للتحقق من صحة القيد
            $this->validateEntryWithAI($entry);

            DB::commit();
            return $entry;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('خطأ في إنشاء القيد المحاسبي', [
                'error' => $e->getMessage(),
                'data' => $entryData,
                'standard' => $standard,
            ]);
            throw $e;
        }
    }

    /**
     * تطبيق قواعد المعيار المحاسبي
     */
    protected function applyAccountingStandard(array $entryData, string $standard): array
    {
        switch ($standard) {
            case 'PCGM':
                return $this->applyPCGMRules($entryData);
            case 'IFRS':
                return $this->applyIFRSRules($entryData);
            case 'GAAP':
                return $this->applyGAAPRules($entryData);
            case 'SOCOMA':
                return $this->applySOCOMARules($entryData);
            default:
                return $entryData;
        }
    }

    /**
     * تطبيق قواعد PCGM المغربي
     */
    protected function applyPCGMRules(array $entryData): array
    {
        // تطبيق قواعد المخطط المحاسبي العام المغربي
        foreach ($entryData['lines'] as &$line) {
            // تحويل رموز الحسابات إلى نظام PCGM
            if (isset($line['account_code'])) {
                $line['account_id'] = $this->getPCGMAccountId($line['account_code']);
            }

            // تطبيق قواعد TVA المغربية
            if (isset($line['tax_code'])) {
                $line = $this->applyMoroccanVATRules($line);
            }
        }

        return $entryData;
    }

    /**
     * تطبيق قواعد IFRS
     */
    protected function applyIFRSRules(array $entryData): array
    {
        // تطبيق قواعد المعايير الدولية
        foreach ($entryData['lines'] as &$line) {
            // تطبيق قواعد الاعتراف والقياس
            $line = $this->applyIFRSRecognitionRules($line);
        }

        return $entryData;
    }

    /**
     * ربط الحساب بالمعيار المحاسبي
     */
    protected function mapAccountToStandard(int $accountId, string $standard): int
    {
        $mapping = AccountMapping::where('source_account_id', $accountId)
            ->where('target_standard', $standard)
            ->first();

        return $mapping ? $mapping->target_account_id : $accountId;
    }

    /**
     * التحقق من صحة القيد باستخدام الذكاء الاصطناعي
     */
    protected function validateEntryWithAI(JournalEntry $entry): void
    {
        $anomalyService = $this->aiServices['anomaly_detection'];
        $anomalies = $anomalyService->detectAnomalies($entry);

        if (!empty($anomalies)) {
            $entry->update([
                'ai_flags' => $anomalies,
                'requires_review' => true,
            ]);

            // إرسال تنبيه للمحاسب
            $this->notifyAccountantOfAnomalies($entry, $anomalies);
        }
    }

    /**
     * المطابقة التلقائية للمعاملات البنكية
     */
    public function autoReconcileBankTransactions(int $bankAccountId): array
    {
        $unmatchedTransactions = BankTransaction::where('bank_account_id', $bankAccountId)
            ->where('status', 'UNMATCHED')
            ->get();

        $matchedCount = 0;
        $suggestions = [];

        foreach ($unmatchedTransactions as $transaction) {
            // تصنيف المعاملة باستخدام الذكاء الاصطناعي
            $category = $this->categorizeTransactionWithAI($transaction);

            // البحث عن قيود مطابقة
            $matchingEntries = $this->findMatchingJournalEntries($transaction, $category);

            if ($matchingEntries->count() === 1) {
                // مطابقة تلقائية
                $this->matchTransactionToEntry($transaction, $matchingEntries->first());
                $matchedCount++;
            } elseif ($matchingEntries->count() > 1) {
                // اقتراح مطابقات متعددة
                $suggestions[] = [
                    'transaction' => $transaction,
                    'possible_matches' => $matchingEntries,
                    'confidence_scores' => $this->calculateMatchConfidence($transaction, $matchingEntries),
                    'ai_category' => $category,
                ];
            } else {
                // اقتراح إنشاء قيد جديد
                $suggestedEntry = $this->suggestJournalEntryFromTransaction($transaction, $category);
                $suggestions[] = [
                    'transaction' => $transaction,
                    'suggested_entry' => $suggestedEntry,
                    'action' => 'CREATE_NEW_ENTRY',
                    'ai_category' => $category,
                ];
            }
        }

        return [
            'matched_automatically' => $matchedCount,
            'suggestions' => $suggestions,
            'total_processed' => $unmatchedTransactions->count(),
        ];
    }

    /**
     * التصنيف الذكي للمعاملات
     */
    public function categorizeTransactionWithAI(BankTransaction $transaction): array
    {
        $categorizationService = $this->aiServices['transaction_categorization'];
        return $categorizationService->categorize($transaction);
    }

    /**
     * مسح الفواتير باستخدام OCR والذكاء الاصطناعي
     */
    public function scanInvoiceWithOCR(string $imagePath): array
    {
        $ocrService = $this->aiServices['ocr_invoice_scanner'];
        return $ocrService->scanInvoice($imagePath);
    }

    /**
     * إنشاء قيد من فاتورة ممسوحة
     */
    public function createEntryFromScannedInvoice(array $scannedData): JournalEntry
    {
        $entryData = [
            'entry_date' => $scannedData['invoice_date'] ?? now(),
            'description' => "فاتورة من {$scannedData['vendor_name']} - {$scannedData['invoice_number']}",
            'reference' => $scannedData['invoice_number'],
            'source_type' => 'SCANNED_INVOICE',
            'currency' => $scannedData['currency'] ?? 'MAD',
            'lines' => [],
        ];

        // إنشاء خطوط القيد
        foreach ($scannedData['line_items'] as $item) {
            // حساب المصروف
            $entryData['lines'][] = [
                'account_id' => $this->getExpenseAccountForItem($item),
                'debit_amount' => $item['amount'],
                'credit_amount' => 0,
                'description' => $item['description'],
            ];
        }

        // حساب الضريبة
        if (isset($scannedData['tax_amount']) && $scannedData['tax_amount'] > 0) {
            $entryData['lines'][] = [
                'account_id' => $this->getTaxAccountId(),
                'debit_amount' => $scannedData['tax_amount'],
                'credit_amount' => 0,
                'description' => 'ضريبة القيمة المضافة',
            ];
        }

        // حساب الدائن (المورد)
        $entryData['lines'][] = [
            'account_id' => $this->getVendorAccountId($scannedData['vendor_name']),
            'debit_amount' => 0,
            'credit_amount' => $scannedData['total_amount'],
            'description' => "مستحق للمورد: {$scannedData['vendor_name']}",
        ];

        return $this->createJournalEntry($entryData);
    }

    /**
     * التحويل بين المعايير المحاسبية
     */
    public function convertBetweenStandards(JournalEntry $entry, string $targetStandard): JournalEntry
    {
        if ($entry->accounting_standard === $targetStandard) {
            return $entry;
        }

        $convertedData = [
            'entry_date' => $entry->entry_date,
            'description' => $entry->description . " (محول من {$entry->accounting_standard})",
            'reference' => $entry->reference,
            'source_type' => $entry->source_type,
            'source_id' => $entry->source_id,
            'currency' => $entry->currency,
            'exchange_rate' => $entry->exchange_rate,
            'lines' => [],
        ];

        foreach ($entry->lines as $line) {
            $mappedAccountId = $this->mapAccountToStandard($line->account_id, $targetStandard);

            $convertedData['lines'][] = [
                'account_id' => $mappedAccountId,
                'debit_amount' => $line->debit_amount,
                'credit_amount' => $line->credit_amount,
                'description' => $line->description,
                'cost_center_id' => $line->cost_center_id,
                'project_id' => $line->project_id,
            ];
        }

        return $this->createJournalEntry($convertedData, $targetStandard);
    }

    /**
     * توليد رقم القيد المتقدم
     */
    protected function generateEntryNumber(string $standard = null): string
    {
        $standard = $standard ?? $this->defaultStandard;
        $year = now()->year;
        $prefix = $this->getStandardPrefix($standard);

        $lastEntry = JournalEntry::whereYear('entry_date', $year)
            ->where('accounting_standard', $standard)
            ->orderBy('entry_number', 'desc')
            ->first();

        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->entry_number, -6);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على بادئة المعيار
     */
    protected function getStandardPrefix(string $standard): string
    {
        return match ($standard) {
            'PCGM' => 'PCG-',
            'IFRS' => 'IFR-',
            'GAAP' => 'GAP-',
            'SOCOMA' => 'SOC-',
            default => 'GEN-',
        };
    }

    // دوال مساعدة
    protected function applyGAAPRules(array $entryData): array { return $entryData; }
    protected function applySOCOMARules(array $entryData): array { return $entryData; }
    protected function getPCGMAccountId(string $accountCode): int { return 1; }
    protected function applyMoroccanVATRules(array $line): array { return $line; }
    protected function applyIFRSRecognitionRules(array $line): array { return $line; }
    protected function notifyAccountantOfAnomalies(JournalEntry $entry, array $anomalies): void {}
    protected function findMatchingJournalEntries(BankTransaction $transaction, array $category) { return collect(); }
    protected function matchTransactionToEntry(BankTransaction $transaction, JournalEntry $entry): void {}
    protected function suggestJournalEntryFromTransaction(BankTransaction $transaction, array $category): array { return []; }
    protected function getExpenseAccountForItem(array $item): int { return 1; }
    protected function getTaxAccountId(): int { return 1; }
    protected function getVendorAccountId(string $vendorName): int { return 1; }

    /**
     * مطابقة المدفوعات تلقائياً مع المعاملات البنكية
     */
    public function autoMatchPayments(int $bankAccountId, string $dateFrom, string $dateTo): array
    {
        try {
            $bankTransactions = BankTransaction::where('bank_account_id', $bankAccountId)
                ->whereBetween('transaction_date', [$dateFrom, $dateTo])
                ->whereNull('matched_payment_id')
                ->get();

            $matchedPayments = [];

            foreach ($bankTransactions as $transaction) {
                $matchedPayment = $this->findMatchingPayment($transaction);

                if ($matchedPayment) {
                    // ربط المعاملة بالدفعة
                    $transaction->update([
                        'matched_payment_id' => $matchedPayment->id,
                        'match_confidence' => $this->calculatePaymentMatchConfidence($transaction, $matchedPayment),
                        'matched_at' => now(),
                    ]);

                    $matchedPayments[] = [
                        'transaction' => $transaction,
                        'payment' => $matchedPayment,
                        'confidence' => $transaction->match_confidence,
                    ];
                }
            }

            return $matchedPayments;

        } catch (\Exception $e) {
            Log::error('Auto match payments failed', [
                'bank_account_id' => $bankAccountId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * عكس قيد محاسبي
     */
    public function reverseJournalEntry(JournalEntry $journalEntry, string $reason): JournalEntry
    {
        try {
            DB::beginTransaction();

            // إنشاء قيد عكسي
            $reversalEntry = JournalEntry::create([
                'entry_number' => $this->generateEntryNumber(),
                'entry_date' => now()->toDateString(),
                'description' => "عكس قيد: {$journalEntry->description}",
                'reference' => "REV-{$journalEntry->entry_number}",
                'source_type' => 'REVERSAL',
                'source_id' => $journalEntry->id,
                'status' => 'posted',
                'total_debit' => $journalEntry->total_credit,
                'total_credit' => $journalEntry->total_debit,
                'created_by' => auth()->id(),
                'notes' => "سبب العكس: {$reason}",
                'reversed_entry_id' => $journalEntry->id,
            ]);

            // إنشاء سطور القيد العكسي
            foreach ($journalEntry->lines as $line) {
                JournalEntryLine::create([
                    'journal_entry_id' => $reversalEntry->id,
                    'account_id' => $line->account_id,
                    'debit_amount' => $line->credit_amount, // عكس المبالغ
                    'credit_amount' => $line->debit_amount,
                    'description' => "عكس: {$line->description}",
                    'reference' => $line->reference,
                ]);
            }

            // تحديث القيد الأصلي
            $journalEntry->update([
                'status' => 'reversed',
                'reversed_at' => now(),
                'reversed_by' => auth()->id(),
                'reversal_reason' => $reason,
            ]);

            // تحديث أرصدة الحسابات
            $this->updateAccountBalances($reversalEntry);

            DB::commit();

            Log::info('Journal entry reversed successfully', [
                'original_entry_id' => $journalEntry->id,
                'reversal_entry_id' => $reversalEntry->id,
                'reason' => $reason
            ]);

            return $reversalEntry;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Journal entry reversal failed', [
                'entry_id' => $journalEntry->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * البحث عن دفعة مطابقة للمعاملة البنكية
     */
    protected function findMatchingPayment(BankTransaction $transaction): ?object
    {
        // البحث بالمبلغ والتاريخ
        $payments = DB::table('payments')
            ->where('amount', $transaction->amount)
            ->where('status', 'completed')
            ->whereBetween('payment_date', [
                $transaction->transaction_date->subDays(3),
                $transaction->transaction_date->addDays(3)
            ])
            ->whereNull('bank_transaction_id')
            ->get();

        if ($payments->count() === 1) {
            return $payments->first();
        }

        // البحث بالمرجع
        if ($transaction->reference) {
            $payment = DB::table('payments')
                ->where('reference', 'LIKE', "%{$transaction->reference}%")
                ->whereNull('bank_transaction_id')
                ->first();

            if ($payment) {
                return $payment;
            }
        }

        return null;
    }

    /**
     * حساب مستوى الثقة في مطابقة الدفعات
     */
    protected function calculatePaymentMatchConfidence(BankTransaction $transaction, object $payment): float
    {
        $confidence = 0;

        // مطابقة المبلغ (40%)
        if ($transaction->amount == $payment->amount) {
            $confidence += 40;
        }

        // مطابقة التاريخ (30%)
        $daysDiff = abs($transaction->transaction_date->diffInDays($payment->payment_date));
        if ($daysDiff === 0) {
            $confidence += 30;
        } elseif ($daysDiff <= 1) {
            $confidence += 20;
        } elseif ($daysDiff <= 3) {
            $confidence += 10;
        }

        // مطابقة المرجع (30%)
        if ($transaction->reference && $payment->reference) {
            $similarity = similar_text(
                strtolower($transaction->reference),
                strtolower($payment->reference)
            );
            $confidence += ($similarity / 100) * 30;
        }

        return round($confidence, 2);
    }



    /**
     * حساب مستوى الثقة في المطابقة (النسخة الأصلية)
     */
    protected function calculateMatchConfidence(BankTransaction $transaction, $entries): array
    {
        return [];
    }

    /**
     * تصدير الحسابات بصيغ مختلفة
     */
    public function exportAccounts(string $format = 'excel'): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $accounts = Account::with(['parentAccount', 'subAccounts'])->get();

        switch ($format) {
            case 'excel':
                return $this->exportToExcel($accounts);
            case 'csv':
                return $this->exportToCsv($accounts);
            case 'pdf':
                return $this->exportToPdf($accounts);
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }
    }

    /**
     * تصدير إلى Excel
     */
    protected function exportToExcel($accounts): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'accounts_' . now()->format('Y-m-d_H-i-s') . '.xlsx';
        $filepath = storage_path('app/exports/' . $filename);

        // إنشاء مجلد التصدير إذا لم يكن موجوداً
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        // بيانات وهمية للتصدير (يمكن استبدالها بمكتبة Excel حقيقية)
        $csvContent = "Account Code,Account Name,Account Type,Account Category,Balance\n";
        foreach ($accounts as $account) {
            $csvContent .= sprintf(
                "%s,%s,%s,%s,%s\n",
                $account->account_code,
                $account->account_name,
                $account->account_type,
                $account->account_category,
                $account->current_balance ?? 0
            );
        }

        file_put_contents($filepath, $csvContent);

        return response()->download($filepath)->deleteFileAfterSend();
    }

    /**
     * تصدير إلى CSV
     */
    protected function exportToCsv($accounts): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'accounts_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $filepath = storage_path('app/exports/' . $filename);

        // إنشاء مجلد التصدير إذا لم يكن موجوداً
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $csvContent = "Account Code,Account Name,Account Type,Account Category,Balance\n";
        foreach ($accounts as $account) {
            $csvContent .= sprintf(
                "%s,%s,%s,%s,%s\n",
                $account->account_code,
                $account->account_name,
                $account->account_type,
                $account->account_category,
                $account->current_balance ?? 0
            );
        }

        file_put_contents($filepath, $csvContent);

        return response()->download($filepath)->deleteFileAfterSend();
    }

    /**
     * تصدير إلى PDF
     */
    protected function exportToPdf($accounts): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'accounts_' . now()->format('Y-m-d_H-i-s') . '.pdf';
        $filepath = storage_path('app/exports/' . $filename);

        // إنشاء مجلد التصدير إذا لم يكن موجوداً
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        // محتوى PDF بسيط (يمكن استبداله بمكتبة PDF حقيقية)
        $pdfContent = "Chart of Accounts Report\n";
        $pdfContent .= "Generated on: " . now()->format('Y-m-d H:i:s') . "\n\n";

        foreach ($accounts as $account) {
            $pdfContent .= sprintf(
                "Code: %s | Name: %s | Type: %s | Category: %s | Balance: %s\n",
                $account->account_code,
                $account->account_name,
                $account->account_type,
                $account->account_category,
                $account->current_balance ?? 0
            );
        }

        file_put_contents($filepath, $pdfContent);

        return response()->download($filepath)->deleteFileAfterSend();
    }
}
