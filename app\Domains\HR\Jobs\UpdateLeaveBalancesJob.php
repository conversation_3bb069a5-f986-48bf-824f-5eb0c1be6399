<?php

namespace App\Domains\HR\Jobs;

use App\Domains\HR\Models\LeaveBalance;
use App\Domains\HR\Services\LeaveManagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * مهمة تحديث أرصدة الإجازات
 * تحديث دوري لأرصدة الإجازات وترحيل الأرصدة
 */
class UpdateLeaveBalancesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $year;
    public string $updateType;
    public array $options;

    /**
     * عدد المحاولات
     */
    public int $tries = 3;

    /**
     * مهلة انتهاء المهمة (بالثواني)
     */
    public int $timeout = 900; // 15 دقيقة

    /**
     * أنواع التحديث
     */
    const UPDATE_TYPES = [
        'RECALCULATE' => 'إعادة حساب',
        'CARRY_FORWARD' => 'ترحيل',
        'ACCRUAL' => 'استحقاق',
        'EXPIRY' => 'انتهاء صلاحية',
    ];

    /**
     * إنشاء مثيل جديد من المهمة
     */
    public function __construct(
        int $year,
        string $updateType = 'RECALCULATE',
        array $options = []
    ) {
        $this->year = $year;
        $this->updateType = $updateType;
        $this->options = $options;
    }

    /**
     * تنفيذ المهمة
     */
    public function handle(LeaveManagementService $leaveService): void
    {
        Log::info('بدء تحديث أرصدة الإجازات', [
            'year' => $this->year,
            'update_type' => $this->updateType,
            'options' => $this->options,
        ]);

        try {
            $updatedCount = 0;

            switch ($this->updateType) {
                case 'RECALCULATE':
                    $updatedCount = $this->recalculateBalances($leaveService);
                    break;

                case 'CARRY_FORWARD':
                    $updatedCount = $this->carryForwardBalances($leaveService);
                    break;

                case 'ACCRUAL':
                    $updatedCount = $this->processAccruals();
                    break;

                case 'EXPIRY':
                    $updatedCount = $this->processExpiries();
                    break;

                default:
                    throw new \InvalidArgumentException("نوع التحديث غير صحيح: {$this->updateType}");
            }

            Log::info('انتهاء تحديث أرصدة الإجازات', [
                'year' => $this->year,
                'update_type' => $this->updateType,
                'updated_count' => $updatedCount,
            ]);

        } catch (\Exception $e) {
            Log::error('فشل في تحديث أرصدة الإجازات', [
                'year' => $this->year,
                'update_type' => $this->updateType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * إعادة حساب الأرصدة
     */
    protected function recalculateBalances(LeaveManagementService $leaveService): int
    {
        return $leaveService->updateAllLeaveBalances();
    }

    /**
     * ترحيل الأرصدة للسنة الجديدة
     */
    protected function carryForwardBalances(LeaveManagementService $leaveService): int
    {
        return $leaveService->carryForwardLeaveBalances($this->year);
    }

    /**
     * معالجة الاستحقاقات الشهرية
     */
    protected function processAccruals(): int
    {
        $balances = LeaveBalance::where('year', $this->year)
                               ->where('is_active', true)
                               ->where('is_frozen', false)
                               ->where('accrual_method', 'MONTHLY')
                               ->where('next_accrual_date', '<=', now())
                               ->get();

        $updatedCount = 0;

        foreach ($balances as $balance) {
            try {
                if ($balance->updateAccrual()) {
                    $updatedCount++;
                    
                    Log::debug('تم تحديث استحقاق الإجازة', [
                        'balance_id' => $balance->id,
                        'employee_id' => $balance->employee_id,
                        'leave_type_id' => $balance->leave_type_id,
                        'accrued_amount' => $balance->accrual_rate_per_month,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('فشل في تحديث استحقاق الإجازة', [
                    'balance_id' => $balance->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $updatedCount;
    }

    /**
     * معالجة انتهاء صلاحية الأرصدة
     */
    protected function processExpiries(): int
    {
        $expiredBalances = LeaveBalance::where('year', $this->year)
                                     ->where('is_active', true)
                                     ->where('expires_at', '<', now())
                                     ->get();

        $updatedCount = 0;

        foreach ($expiredBalances as $balance) {
            try {
                // تجميد الرصيد المنتهي الصلاحية
                $balance->update([
                    'is_active' => false,
                    'remaining_days' => 0,
                    'notes' => ($balance->notes ?? '') . "\n" . now()->format('Y-m-d H:i') . ": انتهت صلاحية الرصيد",
                ]);

                $updatedCount++;

                Log::info('تم تجميد رصيد منتهي الصلاحية', [
                    'balance_id' => $balance->id,
                    'employee_id' => $balance->employee_id,
                    'leave_type_id' => $balance->leave_type_id,
                    'expired_days' => $balance->remaining_days,
                ]);

            } catch (\Exception $e) {
                Log::error('فشل في تجميد الرصيد المنتهي الصلاحية', [
                    'balance_id' => $balance->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $updatedCount;
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('فشل نهائي في تحديث أرصدة الإجازات', [
            'year' => $this->year,
            'update_type' => $this->updateType,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * الحصول على معرف فريد للمهمة
     */
    public function uniqueId(): string
    {
        return "leave-balances-{$this->updateType}-{$this->year}";
    }

    /**
     * تحديد العلامات للمهمة
     */
    public function tags(): array
    {
        return [
            'leave-management',
            'hr',
            "year:{$this->year}",
            "type:{$this->updateType}",
        ];
    }
}
