<?php

namespace App\Domains\ECommerce\Exceptions;

use Exception;

/**
 * الاستثناء الأساسي للتجارة الإلكترونية
 */
class ECommerceException extends Exception
{
    protected array $context = [];
    protected string $errorCode = '';
    protected array $details = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        Exception $previous = null,
        array $context = [],
        string $errorCode = '',
        array $details = []
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->context = $context;
        $this->errorCode = $errorCode;
        $this->details = $details;
    }

    /**
     * الحصول على السياق
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * تعيين السياق
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * الحصول على رمز الخطأ
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * تعيين رمز الخطأ
     */
    public function setErrorCode(string $errorCode): self
    {
        $this->errorCode = $errorCode;
        return $this;
    }

    /**
     * الحصول على التفاصيل
     */
    public function getDetails(): array
    {
        return $this->details;
    }

    /**
     * تعيين التفاصيل
     */
    public function setDetails(array $details): self
    {
        $this->details = $details;
        return $this;
    }

    /**
     * تحويل الاستثناء إلى مصفوفة
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'error_code' => $this->getErrorCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->getContext(),
            'details' => $this->getDetails(),
            'trace' => $this->getTraceAsString(),
        ];
    }

    /**
     * تحويل الاستثناء إلى JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}
