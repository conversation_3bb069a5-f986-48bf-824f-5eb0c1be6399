<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج مهمة الفرصة التجارية - Opportunity Task
 * المهام المرتبطة بالفرص التجارية
 */
class OpportunityTask extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'opportunity_id',
        'assigned_to',
        'created_by',
        'title',
        'description',
        'type',
        'priority',
        'status',
        'due_date',
        'completed_at',
        'estimated_hours',
        'actual_hours',
        'stage_requirement',
        'auto_generated',
        'depends_on_task_id',
        'completion_percentage',
        'notes',
        'tags',
        'metadata',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'estimated_hours' => 'decimal:2',
        'actual_hours' => 'decimal:2',
        'auto_generated' => 'boolean',
        'completion_percentage' => 'integer',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع المهام
     */
    const TYPES = [
        'qualification' => 'تأهيل',
        'discovery' => 'اكتشاف الاحتياجات',
        'demo' => 'عرض توضيحي',
        'proposal' => 'إعداد عرض',
        'follow_up' => 'متابعة',
        'negotiation' => 'مفاوضة',
        'contract' => 'عقد',
        'research' => 'بحث',
        'presentation' => 'عرض تقديمي',
        'meeting' => 'اجتماع',
        'call' => 'مكالمة',
        'email' => 'بريد إلكتروني',
        'documentation' => 'توثيق',
        'approval' => 'موافقة',
        'closing' => 'إغلاق',
        'other' => 'أخرى',
    ];

    /**
     * مستويات الأولوية
     */
    const PRIORITIES = [
        'low' => 'منخفضة',
        'medium' => 'متوسطة',
        'high' => 'عالية',
        'urgent' => 'عاجلة',
    ];

    /**
     * حالات المهمة
     */
    const STATUSES = [
        'pending' => 'معلقة',
        'in_progress' => 'قيد التنفيذ',
        'completed' => 'مكتملة',
        'cancelled' => 'ملغية',
        'on_hold' => 'متوقفة',
        'blocked' => 'محجوبة',
    ];

    /**
     * متطلبات المرحلة
     */
    const STAGE_REQUIREMENTS = [
        'lead_to_qualified' => 'للانتقال من عميل محتمل إلى مؤهل',
        'qualified_to_proposal' => 'للانتقال من مؤهل إلى عرض سعر',
        'proposal_to_negotiation' => 'للانتقال من عرض سعر إلى مفاوضة',
        'negotiation_to_contract' => 'للانتقال من مفاوضة إلى عقد',
        'contract_to_won' => 'للانتقال من عقد إلى مكسوب',
        'optional' => 'اختيارية',
    ];

    /**
     * العلاقة مع الفرصة التجارية
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * العلاقة مع المسؤول عن المهمة
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع منشئ المهمة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع المهمة التي تعتمد عليها
     */
    public function dependsOnTask(): BelongsTo
    {
        return $this->belongsTo(self::class, 'depends_on_task_id');
    }

    /**
     * العلاقة مع المهام التي تعتمد على هذه المهمة
     */
    public function dependentTasks(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(self::class, 'depends_on_task_id');
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الأولوية
     */
    public function getPriorityLabelAttribute(): string
    {
        return self::PRIORITIES[$this->priority] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية متطلب المرحلة
     */
    public function getStageRequirementLabelAttribute(): string
    {
        return self::STAGE_REQUIREMENTS[$this->stage_requirement] ?? 'غير محدد';
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => '#28a745',
            'medium' => '#ffc107',
            'high' => '#fd7e14',
            'urgent' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => '#6c757d',
            'in_progress' => '#17a2b8',
            'completed' => '#28a745',
            'cancelled' => '#dc3545',
            'on_hold' => '#ffc107',
            'blocked' => '#fd7e14',
            default => '#6c757d',
        };
    }

    /**
     * التحقق من كون المهمة مكتملة
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من كون المهمة متأخرة
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !$this->is_completed;
    }

    /**
     * التحقق من كون المهمة مستحقة اليوم
     */
    public function getIsDueTodayAttribute(): bool
    {
        return $this->due_date && $this->due_date->isToday();
    }

    /**
     * التحقق من كون المهمة مولدة تلقائياً
     */
    public function getIsAutoGeneratedAttribute(): bool
    {
        return $this->auto_generated;
    }

    /**
     * التحقق من إمكانية البدء في المهمة
     */
    public function getCanStartAttribute(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        // التحقق من المهام المعتمدة
        if ($this->depends_on_task_id) {
            $dependentTask = $this->dependsOnTask;
            return $dependentTask && $dependentTask->is_completed;
        }

        return true;
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        if (!$this->due_date || $this->is_completed) {
            return 0;
        }

        return max(0, now()->diffInDays($this->due_date, false));
    }

    /**
     * الحصول على نسبة الإنجاز
     */
    public function getProgressPercentageAttribute(): int
    {
        if ($this->is_completed) {
            return 100;
        }

        return $this->completion_percentage ?? 0;
    }

    /**
     * الحصول على الوقت المقدر مقابل الفعلي
     */
    public function getTimeVarianceAttribute(): float
    {
        if (!$this->estimated_hours || !$this->actual_hours) {
            return 0;
        }

        return $this->actual_hours - $this->estimated_hours;
    }

    /**
     * تحديد ما إذا كانت المهمة متطلب للمرحلة
     */
    public function getIsStageRequirementAttribute(): bool
    {
        return $this->stage_requirement && $this->stage_requirement !== 'optional';
    }

    /**
     * بدء المهمة
     */
    public function start(): bool
    {
        if (!$this->can_start) {
            return false;
        }

        return $this->update([
            'status' => 'in_progress',
            'completion_percentage' => 0,
        ]);
    }

    /**
     * إكمال المهمة
     */
    public function complete(array $data = []): bool
    {
        $updated = $this->update(array_merge([
            'status' => 'completed',
            'completed_at' => now(),
            'completion_percentage' => 100,
        ], $data));

        if ($updated) {
            // تحديث الفرصة التجارية
            $this->updateOpportunityProgress();
            
            // تفعيل المهام المعتمدة
            $this->activateDependentTasks();
            
            // التحقق من إمكانية تقدم المرحلة
            $this->checkStageProgression();
        }

        return $updated;
    }

    /**
     * إيقاف المهمة مؤقتاً
     */
    public function pause(string $reason = null): bool
    {
        return $this->update([
            'status' => 'on_hold',
            'notes' => $this->notes . "\n\nتم الإيقاف: " . $reason,
        ]);
    }

    /**
     * إلغاء المهمة
     */
    public function cancel(string $reason = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . "\n\nتم الإلغاء: " . $reason,
        ]);
    }

    /**
     * حجب المهمة
     */
    public function block(string $reason): bool
    {
        return $this->update([
            'status' => 'blocked',
            'notes' => $this->notes . "\n\nتم الحجب: " . $reason,
        ]);
    }

    /**
     * تحديث نسبة الإنجاز
     */
    public function updateProgress(int $percentage): bool
    {
        $percentage = max(0, min(100, $percentage));
        
        $updated = $this->update(['completion_percentage' => $percentage]);
        
        if ($percentage === 100) {
            $this->complete();
        }
        
        return $updated;
    }

    /**
     * تحديث تقدم الفرصة التجارية
     */
    protected function updateOpportunityProgress(): void
    {
        $opportunity = $this->opportunity;
        
        // حساب نسبة إنجاز المهام
        $totalTasks = $opportunity->tasks()->count();
        $completedTasks = $opportunity->tasks()->completed()->count();
        
        if ($totalTasks > 0) {
            $progressPercentage = round(($completedTasks / $totalTasks) * 100);
            
            // تحديث نقاط الفرصة بناءً على التقدم
            $opportunity->updateScore();
        }
    }

    /**
     * تفعيل المهام المعتمدة
     */
    protected function activateDependentTasks(): void
    {
        $dependentTasks = $this->dependentTasks()->where('status', 'pending')->get();
        
        foreach ($dependentTasks as $task) {
            if ($task->can_start) {
                // إشعار المسؤول عن المهمة
                $this->notifyTaskAssignee($task);
            }
        }
    }

    /**
     * التحقق من إمكانية تقدم المرحلة
     */
    protected function checkStageProgression(): void
    {
        if (!$this->is_stage_requirement) {
            return;
        }

        $opportunity = $this->opportunity;
        $currentStage = $opportunity->stage;
        
        // التحقق من اكتمال جميع متطلبات المرحلة
        $stageRequirements = $opportunity->tasks()
                                        ->where('stage_requirement', "like", "{$currentStage}_to_%")
                                        ->get();
        
        $allCompleted = $stageRequirements->every(function ($task) {
            return $task->is_completed;
        });
        
        if ($allCompleted) {
            // اقتراح تقدم المرحلة
            $this->suggestStageProgression();
        }
    }

    /**
     * اقتراح تقدم المرحلة
     */
    protected function suggestStageProgression(): void
    {
        $opportunity = $this->opportunity;
        $currentStage = $opportunity->stage;
        
        $stageProgression = [
            'lead' => 'qualified',
            'qualified' => 'proposal',
            'proposal' => 'negotiation',
            'negotiation' => 'contract',
            'contract' => 'won',
        ];
        
        if (isset($stageProgression[$currentStage])) {
            $nextStage = $stageProgression[$currentStage];
            
            // إنشاء إشعار أو مهمة لمراجعة تقدم المرحلة
            OpportunityTask::create([
                'opportunity_id' => $opportunity->id,
                'assigned_to' => $opportunity->assigned_to,
                'title' => "مراجعة تقدم المرحلة إلى {$nextStage}",
                'description' => "تم اكتمال جميع متطلبات المرحلة الحالية. يرجى مراجعة إمكانية التقدم للمرحلة التالية.",
                'type' => 'approval',
                'priority' => 'high',
                'due_date' => now()->addDays(1),
                'stage_requirement' => 'optional',
                'auto_generated' => true,
                'created_by' => 1, // System user
            ]);
        }
    }

    /**
     * إشعار المسؤول عن المهمة
     */
    protected function notifyTaskAssignee(OpportunityTask $task): void
    {
        // إرسال إشعار للمسؤول عن المهمة
        // سيتم تطوير هذا في نظام الإشعارات
    }

    /**
     * فلترة المهام المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * فلترة المهام المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * فلترة المهام المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('status', '!=', 'completed');
    }

    /**
     * فلترة المهام عالية الأولوية
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * فلترة المهام المستحقة اليوم
     */
    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today());
    }

    /**
     * فلترة المهام المستحقة هذا الأسبوع
     */
    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('due_date', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * فلترة متطلبات المرحلة
     */
    public function scopeStageRequirements($query)
    {
        return $query->where('stage_requirement', '!=', 'optional');
    }

    /**
     * فلترة المهام المولدة تلقائياً
     */
    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب المسؤول
     */
    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * البحث في المهام
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('notes', 'LIKE', "%{$search}%");
        });
    }
}
