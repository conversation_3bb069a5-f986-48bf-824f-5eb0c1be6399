<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج بند الفاتورة
 */
class InvoiceItem extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'invoice_id',
        'product_id',
        'description',
        'quantity',
        'unit_price',
        'discount_type',
        'discount_value',
        'discount_amount',
        'tax_rate',
        'tax_amount',
        'line_total',
        'account_id',
        'metadata',
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'line_total' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * أنواع الخصم
     */
    public const DISCOUNT_TYPES = [
        'NONE' => 'بدون خصم',
        'PERCENTAGE' => 'نسبة مئوية',
        'FIXED' => 'مبلغ ثابت',
    ];

    /**
     * الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Ecommerce\Models\Product::class);
    }

    /**
     * الحساب المحاسبي
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * حساب المجاميع عند الحفظ
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->calculateTotals();
        });

        static::saved(function ($item) {
            $item->invoice->calculateTotals();
            $item->invoice->save();
        });
    }

    /**
     * حساب المجاميع
     */
    public function calculateTotals(): void
    {
        $subtotal = $this->quantity * $this->unit_price;

        // حساب الخصم
        $this->discount_amount = match ($this->discount_type) {
            'PERCENTAGE' => $subtotal * ($this->discount_value / 100),
            'FIXED' => $this->discount_value,
            default => 0,
        };

        $afterDiscount = $subtotal - $this->discount_amount;

        // حساب الضريبة
        $this->tax_amount = $afterDiscount * ($this->tax_rate / 100);

        // المجموع النهائي
        $this->line_total = $afterDiscount + $this->tax_amount;
    }
}
