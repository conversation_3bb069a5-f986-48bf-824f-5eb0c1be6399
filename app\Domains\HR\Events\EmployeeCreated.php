<?php

namespace App\Domains\HR\Events;

use App\Domains\HR\Models\Employee;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنشاء موظف جديد
 */
class EmployeeCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Employee $employee;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Employee $employee)
    {
        $this->employee = $employee;
    }
}
