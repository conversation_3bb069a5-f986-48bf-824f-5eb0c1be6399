<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج سجل مزامنة التجارة الإلكترونية
 * يسجل جميع عمليات المزامنة مع منصات التجارة الإلكترونية
 */
class ECommerceSyncLog extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'integration_id',
        'store_id',
        'platform_id',
        'company_id',
        'sync_type',
        'sync_direction',
        'sync_mode',
        'entity_type',
        'entity_id',
        'external_entity_id',
        'operation',
        'status',
        'started_at',
        'completed_at',
        'duration',
        'records_total',
        'records_processed',
        'records_successful',
        'records_failed',
        'records_skipped',
        'records_created',
        'records_updated',
        'records_deleted',
        'success_rate',
        'failure_rate',
        'skip_rate',
        'throughput',
        'performance_score',
        'quality_score',
        'reliability_score',
        'efficiency_score',
        'effectiveness_score',
        'request_data',
        'response_data',
        'error_data',
        'warning_data',
        'debug_data',
        'metadata',
        'context',
        'environment',
        'version',
        'api_version',
        'sdk_version',
        'client_version',
        'server_version',
        'protocol_version',
        'format_version',
        'schema_version',
        'data_version',
        'config_version',
        'mapping_version',
        'transformation_version',
        'validation_version',
        'business_rules_version',
        'sync_rules_version',
        'error_handling_version',
        'retry_version',
        'notification_version',
        'logging_version',
        'monitoring_version',
        'security_version',
        'compliance_version',
        'audit_version',
        'backup_version',
        'archive_version',
        'cleanup_version',
        'maintenance_version',
        'upgrade_version',
        'migration_version',
        'deployment_version',
        'configuration_version',
        'customization_version',
        'personalization_version',
        'localization_version',
        'globalization_version',
        'internationalization_version',
        'regionalization_version',
        'culturalization_version',
        'user_agent',
        'ip_address',
        'session_id',
        'request_id',
        'correlation_id',
        'trace_id',
        'span_id',
        'parent_span_id',
        'trace_flags',
        'trace_state',
        'baggage',
        'tags',
        'labels',
        'annotations',
        'events',
        'links',
        'attributes',
        'resources',
        'instrumentation_library',
        'service_name',
        'service_version',
        'service_instance_id',
        'service_namespace',
        'deployment_environment',
        'host_name',
        'host_id',
        'host_type',
        'host_arch',
        'host_image_name',
        'host_image_id',
        'host_image_version',
        'os_type',
        'os_description',
        'os_name',
        'os_version',
        'process_id',
        'process_executable_name',
        'process_executable_path',
        'process_command',
        'process_command_line',
        'process_command_args',
        'process_owner',
        'process_runtime_name',
        'process_runtime_version',
        'process_runtime_description',
        'container_name',
        'container_id',
        'container_image_name',
        'container_image_tag',
        'k8s_cluster_name',
        'k8s_namespace_name',
        'k8s_pod_name',
        'k8s_pod_uid',
        'k8s_container_name',
        'k8s_replicaset_name',
        'k8s_replicaset_uid',
        'k8s_deployment_name',
        'k8s_deployment_uid',
        'k8s_statefulset_name',
        'k8s_statefulset_uid',
        'k8s_daemonset_name',
        'k8s_daemonset_uid',
        'k8s_job_name',
        'k8s_job_uid',
        'k8s_cronjob_name',
        'k8s_cronjob_uid',
        'k8s_node_name',
        'k8s_node_uid',
        'cloud_provider',
        'cloud_account_id',
        'cloud_region',
        'cloud_availability_zone',
        'cloud_platform',
        'aws_ec2_instance_id',
        'aws_ec2_instance_type',
        'aws_ec2_ami_id',
        'aws_ecs_container_arn',
        'aws_ecs_cluster_arn',
        'aws_ecs_launchtype',
        'aws_ecs_task_arn',
        'aws_ecs_task_family',
        'aws_ecs_task_revision',
        'aws_eks_cluster_name',
        'aws_lambda_function_name',
        'aws_lambda_function_version',
        'aws_lambda_invoked_arn',
        'gcp_instance_id',
        'gcp_instance_name',
        'gcp_machine_type',
        'gcp_project_id',
        'gcp_zone',
        'azure_vm_id',
        'azure_vm_name',
        'azure_vm_size',
        'azure_resource_group_name',
        'azure_subscription_id',
        'faas_name',
        'faas_id',
        'faas_version',
        'faas_instance',
        'faas_max_memory',
        'faas_execution',
        'faas_coldstart',
        'faas_document_collection',
        'faas_document_operation',
        'faas_document_name',
        'faas_time',
        'faas_cron',
        'messaging_system',
        'messaging_destination',
        'messaging_destination_kind',
        'messaging_temp_destination',
        'messaging_protocol',
        'messaging_protocol_version',
        'messaging_url',
        'messaging_message_id',
        'messaging_conversation_id',
        'messaging_message_payload_size_bytes',
        'messaging_message_payload_compressed_size_bytes',
        'messaging_operation',
        'messaging_consumer_id',
        'messaging_rabbitmq_routing_key',
        'messaging_kafka_message_key',
        'messaging_kafka_consumer_group',
        'messaging_kafka_client_id',
        'messaging_kafka_partition',
        'messaging_kafka_tombstone',
        'rpc_system',
        'rpc_service',
        'rpc_method',
        'rpc_grpc_status_code',
        'rpc_jsonrpc_version',
        'rpc_jsonrpc_request_id',
        'rpc_jsonrpc_error_code',
        'rpc_jsonrpc_error_message',
        'db_system',
        'db_connection_string',
        'db_user',
        'db_name',
        'db_statement',
        'db_operation',
        'db_mssql_instance_name',
        'db_jdbc_driver_classname',
        'db_cassandra_keyspace',
        'db_cassandra_page_size',
        'db_cassandra_consistency_level',
        'db_cassandra_table',
        'db_cassandra_idempotence',
        'db_cassandra_speculative_execution_count',
        'db_cassandra_coordinator_id',
        'db_cassandra_coordinator_dc',
        'db_hbase_namespace',
        'db_redis_database_index',
        'db_mongodb_collection',
        'db_sql_table',
        'exception_type',
        'exception_message',
        'exception_stacktrace',
        'exception_escaped',
        'http_method',
        'http_url',
        'http_scheme',
        'http_host',
        'http_target',
        'http_route',
        'http_status_code',
        'http_status_text',
        'http_flavor',
        'http_user_agent',
        'http_request_content_length',
        'http_request_content_length_uncompressed',
        'http_response_content_length',
        'http_response_content_length_uncompressed',
        'http_server_name',
        'http_client_ip',
        'net_transport',
        'net_peer_ip',
        'net_peer_port',
        'net_peer_name',
        'net_host_ip',
        'net_host_port',
        'net_host_name',
        'net_host_connection_type',
        'net_host_connection_subtype',
        'net_host_carrier_name',
        'net_host_carrier_mcc',
        'net_host_carrier_mnc',
        'net_host_carrier_icc',
        'enduser_id',
        'enduser_role',
        'enduser_scope',
        'thread_id',
        'thread_name',
        'code_function',
        'code_namespace',
        'code_filepath',
        'code_lineno',
        'is_successful',
        'is_failed',
        'is_partial',
        'is_skipped',
        'is_retried',
        'is_cancelled',
        'is_timeout',
        'is_rate_limited',
        'is_quota_exceeded',
        'is_permission_denied',
        'is_authentication_failed',
        'is_authorization_failed',
        'is_validation_failed',
        'is_business_rule_failed',
        'is_data_integrity_failed',
        'is_conflict',
        'is_duplicate',
        'is_not_found',
        'is_service_unavailable',
        'is_network_error',
        'is_timeout_error',
        'is_connection_error',
        'is_ssl_error',
        'is_certificate_error',
        'is_dns_error',
        'is_firewall_error',
        'is_proxy_error',
        'is_load_balancer_error',
        'is_gateway_error',
        'is_server_error',
        'is_client_error',
        'is_application_error',
        'is_system_error',
        'is_infrastructure_error',
        'is_platform_error',
        'is_service_error',
        'is_api_error',
        'is_database_error',
        'is_cache_error',
        'is_queue_error',
        'is_storage_error',
        'is_file_error',
        'is_memory_error',
        'is_cpu_error',
        'is_disk_error',
        'is_network_bandwidth_error',
        'is_network_latency_error',
        'is_network_packet_loss_error',
        'is_network_jitter_error',
        'is_security_error',
        'is_compliance_error',
        'is_audit_error',
        'is_monitoring_error',
        'is_logging_error',
        'is_alerting_error',
        'is_notification_error',
        'is_backup_error',
        'is_restore_error',
        'is_archive_error',
        'is_cleanup_error',
        'is_maintenance_error',
        'is_upgrade_error',
        'is_migration_error',
        'is_deployment_error',
        'is_configuration_error',
        'is_customization_error',
        'is_personalization_error',
        'is_localization_error',
        'is_globalization_error',
        'is_internationalization_error',
        'is_regionalization_error',
        'is_culturalization_error',
        'is_business_error',
        'is_technical_error',
        'is_functional_error',
        'is_non_functional_error',
        'is_performance_error',
        'is_scalability_error',
        'is_availability_error',
        'is_reliability_error',
        'is_maintainability_error',
        'is_usability_error',
        'is_accessibility_error',
        'is_compatibility_error',
        'is_interoperability_error',
        'is_portability_error',
        'is_reusability_error',
        'is_testability_error',
        'is_modularity_error',
        'is_flexibility_error',
        'is_extensibility_error',
        'is_adaptability_error',
        'is_configurability_error',
        'is_customizability_error',
        'is_personalizability_error',
        'is_localizability_error',
        'is_globalizability_error',
        'is_internationalizability_error',
        'is_regionalizability_error',
        'is_culturalizability_error',
        'is_user_error',
        'is_operator_error',
        'is_administrator_error',
        'is_developer_error',
        'is_designer_error',
        'is_architect_error',
        'is_manager_error',
        'is_executive_error',
        'is_stakeholder_error',
        'is_customer_error',
        'is_vendor_error',
        'is_partner_error',
        'is_supplier_error',
        'is_distributor_error',
        'is_reseller_error',
        'is_retailer_error',
        'is_wholesaler_error',
        'is_manufacturer_error',
        'is_producer_error',
        'is_creator_error',
        'is_inventor_error',
        'is_innovator_error',
        'is_pioneer_error',
        'is_leader_error',
        'is_follower_error',
        'is_early_adopter_error',
        'is_early_majority_error',
        'is_late_majority_error',
        'is_laggard_error',
        'is_mainstream_error',
        'is_niche_error',
        'is_mass_market_error',
        'is_target_market_error',
        'is_addressable_market_error',
        'is_serviceable_market_error',
        'is_obtainable_market_error',
        'is_total_addressable_market_error',
        'is_serviceable_addressable_market_error',
        'is_serviceable_obtainable_market_error',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'error_data' => 'array',
        'warning_data' => 'array',
        'debug_data' => 'array',
        'metadata' => 'array',
        'context' => 'array',
        'tags' => 'array',
        'labels' => 'array',
        'annotations' => 'array',
        'events' => 'array',
        'links' => 'array',
        'attributes' => 'array',
        'resources' => 'array',
        'process_command_args' => 'array',
        'duration' => 'integer',
        'records_total' => 'integer',
        'records_processed' => 'integer',
        'records_successful' => 'integer',
        'records_failed' => 'integer',
        'records_skipped' => 'integer',
        'records_created' => 'integer',
        'records_updated' => 'integer',
        'records_deleted' => 'integer',
        'success_rate' => 'decimal:4',
        'failure_rate' => 'decimal:4',
        'skip_rate' => 'decimal:4',
        'throughput' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'reliability_score' => 'decimal:2',
        'efficiency_score' => 'decimal:2',
        'effectiveness_score' => 'decimal:2',
        'process_id' => 'integer',
        'net_peer_port' => 'integer',
        'net_host_port' => 'integer',
        'thread_id' => 'integer',
        'code_lineno' => 'integer',
        'http_status_code' => 'integer',
        'http_request_content_length' => 'integer',
        'http_request_content_length_uncompressed' => 'integer',
        'http_response_content_length' => 'integer',
        'http_response_content_length_uncompressed' => 'integer',
        'rpc_grpc_status_code' => 'integer',
        'rpc_jsonrpc_error_code' => 'integer',
        'db_redis_database_index' => 'integer',
        'db_cassandra_page_size' => 'integer',
        'db_cassandra_speculative_execution_count' => 'integer',
        'messaging_message_payload_size_bytes' => 'integer',
        'messaging_message_payload_compressed_size_bytes' => 'integer',
        'messaging_kafka_partition' => 'integer',
        'faas_max_memory' => 'integer',
        'is_successful' => 'boolean',
        'is_failed' => 'boolean',
        'is_partial' => 'boolean',
        'is_skipped' => 'boolean',
        'is_retried' => 'boolean',
        'is_cancelled' => 'boolean',
        'is_timeout' => 'boolean',
        'is_rate_limited' => 'boolean',
        'is_quota_exceeded' => 'boolean',
        'is_permission_denied' => 'boolean',
        'is_authentication_failed' => 'boolean',
        'is_authorization_failed' => 'boolean',
        'is_validation_failed' => 'boolean',
        'is_business_rule_failed' => 'boolean',
        'is_data_integrity_failed' => 'boolean',
        'is_conflict' => 'boolean',
        'is_duplicate' => 'boolean',
        'is_not_found' => 'boolean',
        'is_service_unavailable' => 'boolean',
        'is_network_error' => 'boolean',
        'is_timeout_error' => 'boolean',
        'is_connection_error' => 'boolean',
        'is_ssl_error' => 'boolean',
        'is_certificate_error' => 'boolean',
        'is_dns_error' => 'boolean',
        'is_firewall_error' => 'boolean',
        'is_proxy_error' => 'boolean',
        'is_load_balancer_error' => 'boolean',
        'is_gateway_error' => 'boolean',
        'is_server_error' => 'boolean',
        'is_client_error' => 'boolean',
        'is_application_error' => 'boolean',
        'is_system_error' => 'boolean',
        'is_infrastructure_error' => 'boolean',
        'is_platform_error' => 'boolean',
        'is_service_error' => 'boolean',
        'is_api_error' => 'boolean',
        'is_database_error' => 'boolean',
        'is_cache_error' => 'boolean',
        'is_queue_error' => 'boolean',
        'is_storage_error' => 'boolean',
        'is_file_error' => 'boolean',
        'is_memory_error' => 'boolean',
        'is_cpu_error' => 'boolean',
        'is_disk_error' => 'boolean',
        'is_network_bandwidth_error' => 'boolean',
        'is_network_latency_error' => 'boolean',
        'is_network_packet_loss_error' => 'boolean',
        'is_network_jitter_error' => 'boolean',
        'is_security_error' => 'boolean',
        'is_compliance_error' => 'boolean',
        'is_audit_error' => 'boolean',
        'is_monitoring_error' => 'boolean',
        'is_logging_error' => 'boolean',
        'is_alerting_error' => 'boolean',
        'is_notification_error' => 'boolean',
        'is_backup_error' => 'boolean',
        'is_restore_error' => 'boolean',
        'is_archive_error' => 'boolean',
        'is_cleanup_error' => 'boolean',
        'is_maintenance_error' => 'boolean',
        'is_upgrade_error' => 'boolean',
        'is_migration_error' => 'boolean',
        'is_deployment_error' => 'boolean',
        'is_configuration_error' => 'boolean',
        'is_customization_error' => 'boolean',
        'is_personalization_error' => 'boolean',
        'is_localization_error' => 'boolean',
        'is_globalization_error' => 'boolean',
        'is_internationalization_error' => 'boolean',
        'is_regionalization_error' => 'boolean',
        'is_culturalization_error' => 'boolean',
        'is_business_error' => 'boolean',
        'is_technical_error' => 'boolean',
        'is_functional_error' => 'boolean',
        'is_non_functional_error' => 'boolean',
        'is_performance_error' => 'boolean',
        'is_scalability_error' => 'boolean',
        'is_availability_error' => 'boolean',
        'is_reliability_error' => 'boolean',
        'is_maintainability_error' => 'boolean',
        'is_usability_error' => 'boolean',
        'is_accessibility_error' => 'boolean',
        'is_compatibility_error' => 'boolean',
        'is_interoperability_error' => 'boolean',
        'is_portability_error' => 'boolean',
        'is_reusability_error' => 'boolean',
        'is_testability_error' => 'boolean',
        'is_modularity_error' => 'boolean',
        'is_flexibility_error' => 'boolean',
        'is_extensibility_error' => 'boolean',
        'is_adaptability_error' => 'boolean',
        'is_configurability_error' => 'boolean',
        'is_customizability_error' => 'boolean',
        'is_personalizability_error' => 'boolean',
        'is_localizability_error' => 'boolean',
        'is_globalizability_error' => 'boolean',
        'is_internationalizability_error' => 'boolean',
        'is_regionalizability_error' => 'boolean',
        'is_culturalizability_error' => 'boolean',
        'is_user_error' => 'boolean',
        'is_operator_error' => 'boolean',
        'is_administrator_error' => 'boolean',
        'is_developer_error' => 'boolean',
        'is_designer_error' => 'boolean',
        'is_architect_error' => 'boolean',
        'is_manager_error' => 'boolean',
        'is_executive_error' => 'boolean',
        'is_stakeholder_error' => 'boolean',
        'is_customer_error' => 'boolean',
        'is_vendor_error' => 'boolean',
        'is_partner_error' => 'boolean',
        'is_supplier_error' => 'boolean',
        'is_distributor_error' => 'boolean',
        'is_reseller_error' => 'boolean',
        'is_retailer_error' => 'boolean',
        'is_wholesaler_error' => 'boolean',
        'is_manufacturer_error' => 'boolean',
        'is_producer_error' => 'boolean',
        'is_creator_error' => 'boolean',
        'is_inventor_error' => 'boolean',
        'is_innovator_error' => 'boolean',
        'is_pioneer_error' => 'boolean',
        'is_leader_error' => 'boolean',
        'is_follower_error' => 'boolean',
        'is_early_adopter_error' => 'boolean',
        'is_early_majority_error' => 'boolean',
        'is_late_majority_error' => 'boolean',
        'is_laggard_error' => 'boolean',
        'is_mainstream_error' => 'boolean',
        'is_niche_error' => 'boolean',
        'is_mass_market_error' => 'boolean',
        'is_target_market_error' => 'boolean',
        'is_addressable_market_error' => 'boolean',
        'is_serviceable_market_error' => 'boolean',
        'is_obtainable_market_error' => 'boolean',
        'is_total_addressable_market_error' => 'boolean',
        'is_serviceable_addressable_market_error' => 'boolean',
        'is_serviceable_obtainable_market_error' => 'boolean',
        'messaging_kafka_tombstone' => 'boolean',
        'db_cassandra_idempotence' => 'boolean',
        'exception_escaped' => 'boolean',
        'faas_coldstart' => 'boolean',
        'messaging_temp_destination' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'faas_time' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'sync_type', 'entity_type', 'operation', 'status',
                'records_total', 'records_successful', 'records_failed'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ECommerceIntegration::class, 'integration_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(ECommerceStore::class, 'store_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeSuccessful($query)
    {
        return $query->where('is_successful', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('is_failed', true);
    }

    public function scopePartial($query)
    {
        return $query->where('is_partial', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeBySyncType($query, $type)
    {
        return $query->where('sync_type', $type);
    }

    public function scopeByEntityType($query, $type)
    {
        return $query->where('entity_type', $type);
    }

    public function scopeByOperation($query, $operation)
    {
        return $query->where('operation', $operation);
    }

    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('started_at', '>=', now()->subHours($hours));
    }

    /**
     * الطرق المساعدة
     */
    public function isSuccessful(): bool
    {
        return $this->is_successful;
    }

    public function isFailed(): bool
    {
        return $this->is_failed;
    }

    public function isPartial(): bool
    {
        return $this->is_partial;
    }

    public function isCompleted(): bool
    {
        return $this->completed_at !== null;
    }

    public function getDuration(): int
    {
        return $this->duration ?? 0;
    }

    public function getRecordsTotal(): int
    {
        return $this->records_total ?? 0;
    }

    public function getRecordsProcessed(): int
    {
        return $this->records_processed ?? 0;
    }

    public function getRecordsSuccessful(): int
    {
        return $this->records_successful ?? 0;
    }

    public function getRecordsFailed(): int
    {
        return $this->records_failed ?? 0;
    }

    public function getRecordsSkipped(): int
    {
        return $this->records_skipped ?? 0;
    }

    public function getRecordsCreated(): int
    {
        return $this->records_created ?? 0;
    }

    public function getRecordsUpdated(): int
    {
        return $this->records_updated ?? 0;
    }

    public function getRecordsDeleted(): int
    {
        return $this->records_deleted ?? 0;
    }

    public function getSuccessRate(): float
    {
        return $this->success_rate ?? 0;
    }

    public function getFailureRate(): float
    {
        return $this->failure_rate ?? 0;
    }

    public function getSkipRate(): float
    {
        return $this->skip_rate ?? 0;
    }

    public function getThroughput(): float
    {
        return $this->throughput ?? 0;
    }

    public function getPerformanceScore(): float
    {
        return $this->performance_score ?? 0;
    }

    public function getQualityScore(): float
    {
        return $this->quality_score ?? 0;
    }

    public function getReliabilityScore(): float
    {
        return $this->reliability_score ?? 0;
    }

    public function getEfficiencyScore(): float
    {
        return $this->efficiency_score ?? 0;
    }

    public function getEffectivenessScore(): float
    {
        return $this->effectiveness_score ?? 0;
    }

    public function getRequestData(): array
    {
        return $this->request_data ?? [];
    }

    public function getResponseData(): array
    {
        return $this->response_data ?? [];
    }

    public function getErrorData(): array
    {
        return $this->error_data ?? [];
    }

    public function getWarningData(): array
    {
        return $this->warning_data ?? [];
    }

    public function getDebugData(): array
    {
        return $this->debug_data ?? [];
    }

    public function getMetadata(): array
    {
        return $this->metadata ?? [];
    }

    public function getContext(): array
    {
        return $this->context ?? [];
    }

    public function hasErrors(): bool
    {
        return !empty($this->getErrorData()) || $this->isFailed();
    }

    public function hasWarnings(): bool
    {
        return !empty($this->getWarningData());
    }

    public function getOverallScore(): float
    {
        $scores = [
            $this->getPerformanceScore(),
            $this->getQualityScore(),
            $this->getReliabilityScore(),
            $this->getEfficiencyScore(),
            $this->getEffectivenessScore(),
            $this->getSuccessRate(),
        ];

        $validScores = array_filter($scores, fn($score) => $score > 0);

        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getHealthStatus(): string
    {
        if ($this->isFailed()) {
            return 'critical';
        }

        if ($this->isPartial() || $this->hasErrors()) {
            return 'poor';
        }

        if ($this->hasWarnings()) {
            return 'fair';
        }

        $score = $this->getOverallScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';

        return 'critical';
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'sync_type' => $this->sync_type,
            'entity_type' => $this->entity_type,
            'operation' => $this->operation,
            'status' => $this->status,
            'started_at' => $this->started_at,
            'completed_at' => $this->completed_at,
            'duration' => $this->getDuration(),
            'records_total' => $this->getRecordsTotal(),
            'records_processed' => $this->getRecordsProcessed(),
            'records_successful' => $this->getRecordsSuccessful(),
            'records_failed' => $this->getRecordsFailed(),
            'records_skipped' => $this->getRecordsSkipped(),
            'success_rate' => $this->getSuccessRate(),
            'failure_rate' => $this->getFailureRate(),
            'throughput' => $this->getThroughput(),
            'is_successful' => $this->isSuccessful(),
            'is_failed' => $this->isFailed(),
            'is_partial' => $this->isPartial(),
            'has_errors' => $this->hasErrors(),
            'has_warnings' => $this->hasWarnings(),
            'overall_score' => $this->getOverallScore(),
            'health_status' => $this->getHealthStatus(),
        ];
    }
}
