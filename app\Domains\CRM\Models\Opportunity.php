<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج الفرصة التجارية - Sales Opportunity
 * إدارة دورة المبيعات من الاستفسار إلى الإغلاق
 */
class Opportunity extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasFiles;

    protected $fillable = [
        'opportunity_number',
        'customer_id',
        'contact_id',
        'assigned_to',
        'created_by',
        'title',
        'description',
        'value',
        'currency',
        'probability',
        'stage',
        'source',
        'type',
        'priority',
        'expected_close_date',
        'actual_close_date',
        'lost_reason',
        'competitor',
        'products_services',
        'custom_fields',
        'tags',
        'notes',
        'ai_insights',
        'score',
        'temperature',
        'next_action',
        'next_action_date',
        'last_activity_at',
        'stage_changed_at',
        'days_in_stage',
        'total_interactions',
        'email_interactions',
        'call_interactions',
        'meeting_interactions',
        'proposal_sent_at',
        'proposal_viewed_at',
        'contract_sent_at',
        'contract_signed_at',
        'metadata',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'probability' => 'integer',
        'expected_close_date' => 'date',
        'actual_close_date' => 'date',
        'products_services' => 'array',
        'custom_fields' => 'array',
        'tags' => 'array',
        'ai_insights' => 'array',
        'score' => 'integer',
        'next_action_date' => 'datetime',
        'last_activity_at' => 'datetime',
        'stage_changed_at' => 'datetime',
        'days_in_stage' => 'integer',
        'total_interactions' => 'integer',
        'email_interactions' => 'integer',
        'call_interactions' => 'integer',
        'meeting_interactions' => 'integer',
        'proposal_sent_at' => 'datetime',
        'proposal_viewed_at' => 'datetime',
        'contract_sent_at' => 'datetime',
        'contract_signed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * مراحل دورة المبيعات
     */
    const STAGES = [
        'lead' => 'عميل محتمل',
        'qualified' => 'مؤهل',
        'proposal' => 'عرض سعر',
        'negotiation' => 'مفاوضة',
        'contract' => 'عقد',
        'won' => 'مكسوب',
        'lost' => 'مفقود',
        'closed' => 'مغلق',
    ];

    /**
     * مصادر الفرص
     */
    const SOURCES = [
        'website' => 'الموقع الإلكتروني',
        'referral' => 'إحالة',
        'cold_call' => 'مكالمة باردة',
        'email_campaign' => 'حملة بريدية',
        'social_media' => 'وسائل التواصل',
        'trade_show' => 'معرض تجاري',
        'partner' => 'شريك',
        'advertisement' => 'إعلان',
        'other' => 'أخرى',
    ];

    /**
     * أنواع الفرص
     */
    const TYPES = [
        'new_business' => 'عمل جديد',
        'existing_customer' => 'عميل حالي',
        'upsell' => 'بيع إضافي',
        'cross_sell' => 'بيع متقاطع',
        'renewal' => 'تجديد',
        'upgrade' => 'ترقية',
    ];

    /**
     * مستويات الأولوية
     */
    const PRIORITIES = [
        'low' => 'منخفضة',
        'medium' => 'متوسطة',
        'high' => 'عالية',
        'urgent' => 'عاجلة',
    ];

    /**
     * درجة الحرارة (مدى اهتمام العميل)
     */
    const TEMPERATURES = [
        'cold' => 'باردة',
        'warm' => 'دافئة',
        'hot' => 'ساخنة',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع جهة الاتصال
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * العلاقة مع مندوب المبيعات المسؤول
     */
    public function assignedSalesRep(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع منشئ الفرصة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع الأنشطة
     */
    public function activities(): HasMany
    {
        return $this->hasMany(OpportunityActivity::class);
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(OpportunityTask::class);
    }

    /**
     * العلاقة مع المواعيد
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * العلاقة مع عروض الأسعار
     */
    public function quotes(): HasMany
    {
        return $this->hasMany(\App\Domains\Sales\Models\Quote::class);
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Invoice::class);
    }

    /**
     * العلاقة مع المشاريع
     */
    public function projects(): HasMany
    {
        return $this->hasMany(\App\Domains\Projects\Models\Project::class);
    }

    /**
     * الحصول على تسمية المرحلة
     */
    public function getStageLabelAttribute(): string
    {
        return self::STAGES[$this->stage] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية المصدر
     */
    public function getSourceLabelAttribute(): string
    {
        return self::SOURCES[$this->source] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الأولوية
     */
    public function getPriorityLabelAttribute(): string
    {
        return self::PRIORITIES[$this->priority] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية درجة الحرارة
     */
    public function getTemperatureLabelAttribute(): string
    {
        return self::TEMPERATURES[$this->temperature] ?? 'غير محدد';
    }

    /**
     * الحصول على لون المرحلة
     */
    public function getStageColorAttribute(): string
    {
        return match ($this->stage) {
            'lead' => '#6c757d',
            'qualified' => '#17a2b8',
            'proposal' => '#ffc107',
            'negotiation' => '#fd7e14',
            'contract' => '#6f42c1',
            'won' => '#28a745',
            'lost' => '#dc3545',
            'closed' => '#6c757d',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => '#28a745',
            'medium' => '#ffc107',
            'high' => '#fd7e14',
            'urgent' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون درجة الحرارة
     */
    public function getTemperatureColorAttribute(): string
    {
        return match ($this->temperature) {
            'cold' => '#17a2b8',
            'warm' => '#ffc107',
            'hot' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * التحقق من كون الفرصة مفتوحة
     */
    public function getIsOpenAttribute(): bool
    {
        return !in_array($this->stage, ['won', 'lost', 'closed']);
    }

    /**
     * التحقق من كون الفرصة مكسوبة
     */
    public function getIsWonAttribute(): bool
    {
        return $this->stage === 'won';
    }

    /**
     * التحقق من كون الفرصة مفقودة
     */
    public function getIsLostAttribute(): bool
    {
        return $this->stage === 'lost';
    }

    /**
     * التحقق من تأخر الفرصة
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->expected_close_date && 
               $this->expected_close_date->isPast() && 
               $this->is_open;
    }

    /**
     * الحصول على عدد الأيام المتبقية
     */
    public function getDaysToCloseAttribute(): int
    {
        if (!$this->expected_close_date || !$this->is_open) {
            return 0;
        }

        return now()->diffInDays($this->expected_close_date, false);
    }

    /**
     * الحصول على القيمة المرجحة
     */
    public function getWeightedValueAttribute(): float
    {
        return $this->value * ($this->probability / 100);
    }

    /**
     * الحصول على آخر نشاط
     */
    public function getLastActivityAttribute(): ?OpportunityActivity
    {
        return $this->activities()->latest()->first();
    }

    /**
     * الحصول على النشاط التالي
     */
    public function getNextActivityAttribute(): ?OpportunityActivity
    {
        return $this->activities()
                   ->where('scheduled_at', '>', now())
                   ->orderBy('scheduled_at')
                   ->first();
    }

    /**
     * الحصول على عدد الأيام في المرحلة الحالية
     */
    public function getDaysInCurrentStageAttribute(): int
    {
        return $this->stage_changed_at ? 
               $this->stage_changed_at->diffInDays(now()) : 
               $this->created_at->diffInDays(now());
    }

    /**
     * تحديث المرحلة
     */
    public function updateStage(string $newStage, string $reason = null): bool
    {
        $oldStage = $this->stage;
        
        $updated = $this->update([
            'stage' => $newStage,
            'stage_changed_at' => now(),
            'days_in_stage' => 0,
        ]);

        if ($updated) {
            // تسجيل تغيير المرحلة
            $this->activities()->create([
                'type' => 'stage_change',
                'description' => "تم تغيير المرحلة من {$oldStage} إلى {$newStage}",
                'notes' => $reason,
                'created_by' => auth()->id(),
                'occurred_at' => now(),
            ]);

            // إجراءات خاصة حسب المرحلة
            $this->handleStageChange($newStage, $oldStage);
        }

        return $updated;
    }

    /**
     * معالجة تغيير المرحلة
     */
    protected function handleStageChange(string $newStage, string $oldStage): void
    {
        switch ($newStage) {
            case 'won':
                $this->update([
                    'actual_close_date' => now(),
                    'probability' => 100,
                ]);
                
                // إنشاء مشروع تلقائياً إذا كان مطلوباً
                $this->createProjectIfNeeded();
                break;

            case 'lost':
                $this->update([
                    'actual_close_date' => now(),
                    'probability' => 0,
                ]);
                break;

            case 'proposal':
                $this->update(['proposal_sent_at' => now()]);
                break;

            case 'contract':
                $this->update(['contract_sent_at' => now()]);
                break;
        }
    }

    /**
     * إنشاء مشروع من الفرصة
     */
    protected function createProjectIfNeeded(): void
    {
        if ($this->type === 'new_business' && $this->value >= 10000) {
            \App\Domains\Projects\Models\Project::create([
                'name' => "مشروع: {$this->title}",
                'description' => $this->description,
                'customer_id' => $this->customer_id,
                'opportunity_id' => $this->id,
                'budget' => $this->value,
                'status' => 'planning',
                'start_date' => now(),
                'manager_id' => $this->assigned_to,
            ]);
        }
    }

    /**
     * تحديث النقاط والاحتمالية
     */
    public function updateScore(): void
    {
        $score = 0;

        // نقاط بناءً على التفاعلات
        $score += min($this->total_interactions * 5, 50);

        // نقاط بناءً على نوع التفاعل
        $score += $this->meeting_interactions * 15;
        $score += $this->call_interactions * 10;
        $score += $this->email_interactions * 5;

        // نقاط بناءً على المرحلة
        $stageScores = [
            'lead' => 10,
            'qualified' => 25,
            'proposal' => 50,
            'negotiation' => 70,
            'contract' => 85,
        ];
        $score += $stageScores[$this->stage] ?? 0;

        // نقاط بناءً على سرعة التقدم
        if ($this->days_in_stage <= 7) {
            $score += 20;
        } elseif ($this->days_in_stage <= 14) {
            $score += 10;
        } elseif ($this->days_in_stage > 30) {
            $score -= 20;
        }

        // نقاط بناءً على قيمة الفرصة
        if ($this->value >= 100000) {
            $score += 30;
        } elseif ($this->value >= 50000) {
            $score += 20;
        } elseif ($this->value >= 10000) {
            $score += 10;
        }

        $score = max(0, min(100, $score));

        // تحديث الاحتمالية بناءً على النقاط والمرحلة
        $probability = $this->calculateProbability($score);

        $this->update([
            'score' => $score,
            'probability' => $probability,
        ]);
    }

    /**
     * حساب الاحتمالية
     */
    protected function calculateProbability(int $score): int
    {
        $baseProbability = match ($this->stage) {
            'lead' => 10,
            'qualified' => 25,
            'proposal' => 50,
            'negotiation' => 75,
            'contract' => 90,
            'won' => 100,
            'lost' => 0,
            default => 10,
        };

        // تعديل بناءً على النقاط
        $adjustment = ($score - 50) * 0.5;
        
        return max(0, min(100, $baseProbability + $adjustment));
    }

    /**
     * إضافة نشاط جديد
     */
    public function addActivity(array $activityData): OpportunityActivity
    {
        $activityData['opportunity_id'] = $this->id;
        $activity = OpportunityActivity::create($activityData);
        
        // تحديث إحصائيات التفاعل
        $this->updateInteractionStats($activityData['type']);
        
        return $activity;
    }

    /**
     * تحديث إحصائيات التفاعل
     */
    protected function updateInteractionStats(string $activityType): void
    {
        $this->increment('total_interactions');
        
        switch ($activityType) {
            case 'email':
                $this->increment('email_interactions');
                break;
            case 'call':
                $this->increment('call_interactions');
                break;
            case 'meeting':
                $this->increment('meeting_interactions');
                break;
        }

        $this->update(['last_activity_at' => now()]);
        $this->updateScore();
    }

    /**
     * فلترة الفرص المفتوحة
     */
    public function scopeOpen($query)
    {
        return $query->whereNotIn('stage', ['won', 'lost', 'closed']);
    }

    /**
     * فلترة الفرص المكسوبة
     */
    public function scopeWon($query)
    {
        return $query->where('stage', 'won');
    }

    /**
     * فلترة الفرص المفقودة
     */
    public function scopeLost($query)
    {
        return $query->where('stage', 'lost');
    }

    /**
     * فلترة حسب مندوب المبيعات
     */
    public function scopeAssignedTo($query, int $salesRepId)
    {
        return $query->where('assigned_to', $salesRepId);
    }

    /**
     * فلترة حسب المرحلة
     */
    public function scopeInStage($query, string $stage)
    {
        return $query->where('stage', $stage);
    }

    /**
     * فلترة الفرص المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('expected_close_date', '<', now())
                    ->whereNotIn('stage', ['won', 'lost', 'closed']);
    }

    /**
     * فلترة حسب القيمة
     */
    public function scopeValueBetween($query, float $min, float $max)
    {
        return $query->whereBetween('value', [$min, $max]);
    }

    /**
     * فلترة الفرص الساخنة
     */
    public function scopeHot($query)
    {
        return $query->where('temperature', 'hot')
                    ->orWhere('score', '>=', 80);
    }
}
