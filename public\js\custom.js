// Custom JavaScript for Hesabiai

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initSmoothScrolling();
    initBackToTop();
    initLocationModal();
    initFormValidation();
    initAnimations();
    initPricingCards();
    initTestimonials();
});

// Mobile Menu Toggle
function initMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Toggle icon
            const icon = mobileMenuBtn.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });
    }
}

// Smooth Scrolling for Navigation Links
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    const icon = document.querySelector('#mobile-menu-btn i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }
        });
    });
}

// Back to Top Button
function initBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    
    if (backToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Location Modal
function initLocationModal() {
    const locationModal = document.getElementById('location-modal');
    
    if (locationModal) {
        // Show modal on first visit
        if (!localStorage.getItem('selectedCountry')) {
            locationModal.style.display = 'flex';
        } else {
            locationModal.style.display = 'none';
        }
    }
}

// Select Country Function
function selectCountry(countryCode) {
    localStorage.setItem('selectedCountry', countryCode);
    
    const locationModal = document.getElementById('location-modal');
    if (locationModal) {
        locationModal.style.display = 'none';
    }
    
    // Update country in registration form if exists
    const countrySelect = document.getElementById('country');
    if (countrySelect) {
        countrySelect.value = countryCode;
        updateCurrency();
    }
    
    console.log('Selected country:', countryCode);
}

// Update Currency Based on Country
function updateCurrency() {
    const country = document.getElementById('country')?.value;
    const currencyInput = document.getElementById('currency');
    
    if (!currencyInput) return;
    
    const currencies = {
        'MA': 'درهم مغربي (MAD)',
        'SA': 'ريال سعودي (SAR)',
        'AE': 'درهم إماراتي (AED)',
        'EG': 'جنيه مصري (EGP)',
        'TN': 'دينار تونسي (TND)',
        'DZ': 'دينار جزائري (DZD)',
        'JO': 'دينار أردني (JOD)',
        'KW': 'دينار كويتي (KWD)',
        'QA': 'ريال قطري (QAR)'
    };
    
    currencyInput.value = currencies[country] || '';
}

// Form Validation
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    showFieldError(field, 'هذا الحقل مطلوب');
                } else {
                    field.classList.remove('border-red-500');
                    hideFieldError(field);
                }
            });
            
            // Password validation
            const password = form.querySelector('#password');
            const confirmPassword = form.querySelector('#password_confirmation');
            
            if (password && confirmPassword) {
                if (password.value !== confirmPassword.value) {
                    isValid = false;
                    confirmPassword.classList.add('border-red-500');
                    showFieldError(confirmPassword, 'كلمة المرور غير متطابقة');
                }
                
                if (password.value.length < 10) {
                    isValid = false;
                    password.classList.add('border-red-500');
                    showFieldError(password, 'كلمة المرور يجب أن تكون 10 أحرف على الأقل');
                }
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    });
}

// Show Field Error
function showFieldError(field, message) {
    hideFieldError(field); // Remove existing error
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// Hide Field Error
function hideFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// Toggle Password Visibility
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleButton = passwordInput.parentNode.querySelector('button[type="button"]');
    const toggleIcon = toggleButton.querySelector('i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Fill Admin Credentials
function fillAdminCredentials() {
    const emailField = document.getElementById('email');
    const passwordField = document.getElementById('password');
    
    if (emailField && passwordField) {
        emailField.value = '<EMAIL>';
        passwordField.value = 'Colorado2020@';
        
        // Add visual feedback
        emailField.classList.add('bg-blue-50');
        passwordField.classList.add('bg-blue-50');
        
        setTimeout(() => {
            emailField.classList.remove('bg-blue-50');
            passwordField.classList.remove('bg-blue-50');
        }, 1000);
    }
}

// Initialize Animations
function initAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.card-hover, .service-card, .testimonial-card').forEach(el => {
        observer.observe(el);
    });
}

// Pricing Cards Enhancement
function initPricingCards() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Testimonials Carousel (if needed)
function initTestimonials() {
    const testimonials = document.querySelectorAll('.testimonial-card');
    
    if (testimonials.length > 3) {
        // Add carousel functionality here if needed
        console.log('Testimonials carousel can be implemented here');
    }
}

// Multi-step Form Functions
let currentStep = 1;
const totalSteps = 3;

function nextStep() {
    if (validateStep(currentStep)) {
        // Hide current step
        document.getElementById(`step-${currentStep}`).classList.remove('active');
        document.getElementById(`step-indicator-${currentStep}`).classList.add('completed');
        document.getElementById(`step-indicator-${currentStep}`).classList.remove('active');
        
        // Show next step
        currentStep++;
        document.getElementById(`step-${currentStep}`).classList.add('active');
        document.getElementById(`step-indicator-${currentStep}`).classList.add('active');
        
        // Show/hide company fields based on account type
        if (currentStep === 3) {
            toggleCompanyFields();
        }
        
        // Scroll to top of form
        document.querySelector('.bg-white.rounded-xl').scrollIntoView({ behavior: 'smooth' });
    }
}

function prevStep() {
    // Hide current step
    document.getElementById(`step-${currentStep}`).classList.remove('active');
    document.getElementById(`step-indicator-${currentStep}`).classList.remove('active');
    
    // Show previous step
    currentStep--;
    document.getElementById(`step-${currentStep}`).classList.add('active');
    document.getElementById(`step-indicator-${currentStep}`).classList.add('active');
    document.getElementById(`step-indicator-${currentStep}`).classList.remove('completed');
    
    // Scroll to top of form
    document.querySelector('.bg-white.rounded-xl').scrollIntoView({ behavior: 'smooth' });
}

function validateStep(step) {
    const stepElement = document.getElementById(`step-${step}`);
    const requiredFields = stepElement.querySelectorAll('[required]');
    
    for (let field of requiredFields) {
        if (!field.value.trim()) {
            field.focus();
            showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
    }
    
    // Additional validation for step 1
    if (step === 1) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('password_confirmation').value;
        
        if (password.length < 10) {
            showFieldError(document.getElementById('password'), 'كلمة المرور يجب أن تكون 10 أحرف على الأقل');
            return false;
        }
        
        if (password !== confirmPassword) {
            showFieldError(document.getElementById('password_confirmation'), 'كلمة المرور غير متطابقة');
            return false;
        }
    }
    
    return true;
}

function toggleCompanyFields() {
    const accountType = document.querySelector('input[name="account_type"]:checked');
    const companyFields = document.getElementById('company-fields');
    const companyName = document.getElementById('company_name');
    
    if (accountType && (accountType.value === 'sme' || accountType.value === 'enterprise' || accountType.value === 'accounting_office')) {
        companyFields.style.display = 'block';
        companyName.required = true;
    } else {
        companyFields.style.display = 'none';
        companyName.required = false;
    }
}

// Listen for account type changes
document.addEventListener('change', function(e) {
    if (e.target.name === 'account_type') {
        toggleCompanyFields();
    }
    
    if (e.target.id === 'country') {
        updateCurrency();
    }
});

// Utility Functions
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
