<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('scaling_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_gateway_id')->constrained()->onDelete('cascade');
            $table->foreignId('api_endpoint_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('event_type'); // scale_up, scale_down
            $table->string('trigger_reason'); // cpu_high, memory_high, request_rate_high, manual, scheduled
            $table->json('trigger_metrics')->nullable();
            $table->string('scaling_action'); // auto_scaling, manual_scaling, scheduled_scaling
            $table->integer('instances_before');
            $table->integer('instances_after')->nullable();
            $table->integer('target_instances');
            $table->json('scaling_config')->nullable();
            $table->string('status'); // pending, in_progress, completed, failed
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->boolean('success')->default(false);
            $table->text('error_message')->nullable();
            $table->decimal('cost_impact', 10, 2)->nullable();
            $table->decimal('performance_impact', 5, 2)->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['api_gateway_id', 'created_at']);
            $table->index(['event_type', 'status']);
            $table->index(['trigger_reason', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('scaling_events');
    }
};
