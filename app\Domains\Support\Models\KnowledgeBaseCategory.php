<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج فئة قاعدة المعرفة - Knowledge Base Category
 */
class KnowledgeBaseCategory extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'name_ar',
        'name_fr', 
        'name_en',
        'description',
        'parent_id',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'is_public',
        'access_level',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'is_public' => 'boolean',
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function articles(): Has<PERSON>any
    {
        return $this->hasMany(KnowledgeBaseArticle::class, 'category_id');
    }

    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            default => $this->name,
        };
    }
}
