<?php

namespace App\Domains\ECommerce\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

/**
 * نموذج متجر التجارة الإلكترونية
 * يمثل متجر محدد على منصة تجارة إلكترونية
 */
class ECommerceStore extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'platform_id',
        'company_id',
        'name',
        'name_ar',
        'name_en',
        'slug',
        'description',
        'store_url',
        'admin_url',
        'api_url',
        'webhook_url',
        'logo_url',
        'favicon_url',
        'banner_url',
        'theme',
        'language',
        'currency',
        'timezone',
        'country',
        'region',
        'city',
        'address',
        'phone',
        'email',
        'contact_person',
        'business_type',
        'industry',
        'category',
        'subcategory',
        'tags',
        'status',
        'health_status',
        'sync_status',
        'last_sync_at',
        'last_successful_sync_at',
        'last_failed_sync_at',
        'sync_frequency',
        'auto_sync_enabled',
        'real_time_sync_enabled',
        'webhook_enabled',
        'api_credentials',
        'authentication_config',
        'connection_config',
        'sync_config',
        'mapping_config',
        'transformation_config',
        'validation_config',
        'error_handling_config',
        'retry_config',
        'notification_config',
        'security_config',
        'compliance_config',
        'performance_config',
        'monitoring_config',
        'logging_config',
        'backup_config',
        'archive_config',
        'cleanup_config',
        'maintenance_config',
        'upgrade_config',
        'migration_config',
        'integration_settings',
        'platform_settings',
        'store_settings',
        'sync_settings',
        'api_settings',
        'webhook_settings',
        'notification_settings',
        'security_settings',
        'compliance_settings',
        'performance_settings',
        'monitoring_settings',
        'logging_settings',
        'backup_settings',
        'archive_settings',
        'cleanup_settings',
        'maintenance_settings',
        'upgrade_settings',
        'migration_settings',
        'custom_settings',
        'advanced_settings',
        'experimental_settings',
        'beta_settings',
        'debug_settings',
        'test_settings',
        'development_settings',
        'staging_settings',
        'production_settings',
        'environment_settings',
        'deployment_settings',
        'configuration_settings',
        'operational_settings',
        'business_settings',
        'technical_settings',
        'functional_settings',
        'non_functional_settings',
        'quality_settings',
        'usability_settings',
        'accessibility_settings',
        'internationalization_settings',
        'localization_settings',
        'globalization_settings',
        'regionalization_settings',
        'culturalization_settings',
        'personalization_settings',
        'customization_settings',
        'branding_settings',
        'styling_settings',
        'theming_settings',
        'layout_settings',
        'design_settings',
        'ui_settings',
        'ux_settings',
        'interaction_settings',
        'navigation_settings',
        'search_settings',
        'filtering_settings',
        'sorting_settings',
        'pagination_settings',
        'display_settings',
        'formatting_settings',
        'presentation_settings',
        'visualization_settings',
        'reporting_settings',
        'analytics_settings',
        'tracking_settings',
        'measurement_settings',
        'kpi_settings',
        'metrics_settings',
        'dashboard_settings',
        'widget_settings',
        'chart_settings',
        'graph_settings',
        'table_settings',
        'list_settings',
        'grid_settings',
        'card_settings',
        'tile_settings',
        'panel_settings',
        'section_settings',
        'page_settings',
        'view_settings',
        'screen_settings',
        'window_settings',
        'dialog_settings',
        'modal_settings',
        'popup_settings',
        'tooltip_settings',
        'notification_display_settings',
        'alert_settings',
        'message_settings',
        'feedback_settings',
        'validation_display_settings',
        'error_display_settings',
        'warning_settings',
        'info_settings',
        'success_settings',
        'confirmation_settings',
        'prompt_settings',
        'input_settings',
        'form_settings',
        'field_settings',
        'control_settings',
        'button_settings',
        'link_settings',
        'menu_settings',
        'toolbar_settings',
        'sidebar_settings',
        'header_settings',
        'footer_settings',
        'content_settings',
        'media_settings',
        'image_settings',
        'video_settings',
        'audio_settings',
        'document_settings',
        'file_settings',
        'download_settings',
        'upload_settings',
        'import_settings',
        'export_settings',
        'print_settings',
        'email_settings',
        'sms_settings',
        'push_settings',
        'social_settings',
        'sharing_settings',
        'communication_settings',
        'collaboration_settings',
        'workflow_settings',
        'approval_settings',
        'review_settings',
        'comment_settings',
        'rating_settings',
        'feedback_collection_settings',
        'survey_settings',
        'poll_settings',
        'quiz_settings',
        'test_settings_display',
        'exam_settings',
        'assessment_settings',
        'evaluation_settings',
        'scoring_settings',
        'grading_settings',
        'certification_settings',
        'achievement_settings',
        'badge_settings',
        'reward_settings',
        'incentive_settings',
        'loyalty_settings',
        'membership_settings',
        'subscription_settings',
        'billing_settings',
        'payment_settings',
        'pricing_settings',
        'discount_settings',
        'promotion_settings',
        'coupon_settings',
        'voucher_settings',
        'gift_settings',
        'refund_settings',
        'return_settings',
        'exchange_settings',
        'warranty_settings',
        'support_settings',
        'help_settings',
        'documentation_settings',
        'tutorial_settings',
        'guide_settings',
        'faq_settings',
        'knowledge_base_settings',
        'learning_settings',
        'training_settings',
        'onboarding_settings',
        'welcome_settings',
        'introduction_settings',
        'tour_settings',
        'walkthrough_settings',
        'demo_settings',
        'trial_settings',
        'preview_settings',
        'sample_settings',
        'example_settings',
        'template_settings',
        'preset_settings',
        'default_settings',
        'initial_settings',
        'startup_settings',
        'launch_settings',
        'activation_settings',
        'enablement_settings',
        'configuration_wizard_settings',
        'setup_wizard_settings',
        'installation_settings',
        'deployment_wizard_settings',
        'migration_wizard_settings',
        'upgrade_wizard_settings',
        'maintenance_wizard_settings',
        'troubleshooting_settings',
        'diagnostic_settings',
        'health_check_settings',
        'status_check_settings',
        'connectivity_settings',
        'network_settings',
        'bandwidth_settings',
        'latency_settings',
        'throughput_settings',
        'capacity_settings',
        'scalability_settings',
        'elasticity_settings',
        'flexibility_settings',
        'adaptability_settings',
        'extensibility_settings',
        'modularity_settings',
        'composability_settings',
        'reusability_settings',
        'maintainability_settings',
        'supportability_settings',
        'operability_settings',
        'manageability_settings',
        'controllability_settings',
        'observability_settings',
        'visibility_settings',
        'transparency_settings',
        'traceability_settings',
        'auditability_settings',
        'accountability_settings',
        'responsibility_settings',
        'governance_settings',
        'policy_settings',
        'rule_settings',
        'constraint_settings',
        'limitation_settings',
        'restriction_settings',
        'permission_settings',
        'authorization_settings',
        'authentication_settings',
        'identity_settings',
        'access_control_settings',
        'security_policy_settings',
        'privacy_settings',
        'confidentiality_settings',
        'integrity_settings',
        'availability_settings',
        'reliability_settings',
        'durability_settings',
        'consistency_settings',
        'coherence_settings',
        'synchronization_settings',
        'coordination_settings',
        'orchestration_settings',
        'choreography_settings',
        'composition_settings',
        'integration_advanced_settings',
        'interoperability_settings',
        'compatibility_settings',
        'portability_settings',
        'mobility_settings',
        'migration_advanced_settings',
        'transformation_advanced_settings',
        'modernization_settings',
        'optimization_settings',
        'enhancement_settings',
        'improvement_settings',
        'innovation_settings',
        'evolution_settings',
        'revolution_settings',
        'disruption_settings',
        'transformation_digital_settings',
        'digitalization_settings',
        'digitization_settings',
        'automation_settings',
        'intelligence_settings',
        'ai_settings',
        'ml_settings',
        'dl_settings',
        'nlp_settings',
        'cv_settings',
        'robotics_settings',
        'iot_settings',
        'edge_settings',
        'cloud_settings',
        'hybrid_settings',
        'multi_cloud_settings',
        'serverless_settings',
        'containerization_settings',
        'microservices_settings',
        'api_first_settings',
        'headless_settings',
        'jamstack_settings',
        'progressive_settings',
        'responsive_settings',
        'adaptive_settings',
        'mobile_first_settings',
        'desktop_settings',
        'tablet_settings',
        'wearable_settings',
        'voice_settings',
        'gesture_settings',
        'touch_settings',
        'haptic_settings',
        'ar_settings',
        'vr_settings',
        'mr_settings',
        'xr_settings',
        'metaverse_settings',
        'web3_settings',
        'blockchain_settings',
        'crypto_settings',
        'nft_settings',
        'defi_settings',
        'dao_settings',
        'smart_contract_settings',
        'dapp_settings',
        'decentralized_settings',
        'distributed_settings',
        'peer_to_peer_settings',
        'consensus_settings',
        'governance_token_settings',
        'tokenomics_settings',
        'staking_settings',
        'mining_settings',
        'farming_settings',
        'liquidity_settings',
        'yield_settings',
        'rewards_settings',
        'incentives_settings',
        'gamification_settings',
        'social_trading_settings',
        'copy_trading_settings',
        'algorithmic_trading_settings',
        'automated_trading_settings',
        'high_frequency_trading_settings',
        'quantitative_trading_settings',
        'risk_management_settings',
        'portfolio_management_settings',
        'asset_management_settings',
        'wealth_management_settings',
        'investment_settings',
        'trading_settings',
        'exchange_settings',
        'marketplace_settings',
        'platform_trading_settings',
        'broker_settings',
        'dealer_settings',
        'market_maker_settings',
        'liquidity_provider_settings',
        'price_discovery_settings',
        'order_matching_settings',
        'execution_settings',
        'settlement_settings',
        'clearing_settings',
        'custody_settings',
        'escrow_settings',
        'trust_settings',
        'fiduciary_settings',
        'regulatory_settings',
        'compliance_advanced_settings',
        'legal_settings',
        'tax_settings',
        'accounting_settings',
        'financial_settings',
        'economic_settings',
        'business_model_settings',
        'revenue_model_settings',
        'cost_model_settings',
        'pricing_model_settings',
        'value_proposition_settings',
        'competitive_advantage_settings',
        'differentiation_settings',
        'positioning_settings',
        'branding_advanced_settings',
        'marketing_settings',
        'sales_settings',
        'customer_acquisition_settings',
        'customer_retention_settings',
        'customer_engagement_settings',
        'customer_experience_settings',
        'customer_journey_settings',
        'customer_lifecycle_settings',
        'customer_segmentation_settings',
        'customer_profiling_settings',
        'customer_analytics_settings',
        'customer_intelligence_settings',
        'customer_insights_settings',
        'customer_feedback_settings',
        'customer_satisfaction_settings',
        'customer_loyalty_settings',
        'customer_advocacy_settings',
        'customer_referral_settings',
        'customer_success_settings',
        'customer_support_advanced_settings',
        'customer_service_settings',
        'customer_care_settings',
        'customer_relations_settings',
        'customer_communication_settings',
        'customer_interaction_settings',
        'customer_touchpoint_settings',
        'customer_channel_settings',
        'omnichannel_settings',
        'multichannel_settings',
        'cross_channel_settings',
        'unified_commerce_settings',
        'headless_commerce_settings',
        'composable_commerce_settings',
        'api_first_commerce_settings',
        'microservices_commerce_settings',
        'cloud_native_commerce_settings',
        'serverless_commerce_settings',
        'edge_commerce_settings',
        'mobile_commerce_settings',
        'social_commerce_settings',
        'conversational_commerce_settings',
        'voice_commerce_settings',
        'visual_commerce_settings',
        'video_commerce_settings',
        'live_commerce_settings',
        'interactive_commerce_settings',
        'immersive_commerce_settings',
        'experiential_commerce_settings',
        'personalized_commerce_settings',
        'contextual_commerce_settings',
        'predictive_commerce_settings',
        'intelligent_commerce_settings',
        'autonomous_commerce_settings',
        'automated_commerce_settings',
        'self_service_commerce_settings',
        'assisted_commerce_settings',
        'guided_commerce_settings',
        'curated_commerce_settings',
        'recommended_commerce_settings',
        'discovery_commerce_settings',
        'search_commerce_settings',
        'browse_commerce_settings',
        'explore_commerce_settings',
        'navigate_commerce_settings',
        'journey_commerce_settings',
        'flow_commerce_settings',
        'process_commerce_settings',
        'workflow_commerce_settings',
        'pipeline_commerce_settings',
        'funnel_commerce_settings',
        'conversion_settings',
        'optimization_commerce_settings',
        'testing_commerce_settings',
        'experimentation_settings',
        'ab_testing_settings',
        'multivariate_testing_settings',
        'split_testing_settings',
        'feature_testing_settings',
        'usability_testing_settings',
        'user_testing_settings',
        'acceptance_testing_settings',
        'performance_testing_settings',
        'load_testing_settings',
        'stress_testing_settings',
        'volume_testing_settings',
        'scalability_testing_settings',
        'reliability_testing_settings',
        'availability_testing_settings',
        'security_testing_settings',
        'penetration_testing_settings',
        'vulnerability_testing_settings',
        'compliance_testing_settings',
        'accessibility_testing_settings',
        'compatibility_testing_settings',
        'integration_testing_settings',
        'system_testing_settings',
        'end_to_end_testing_settings',
        'regression_testing_settings',
        'smoke_testing_settings',
        'sanity_testing_settings',
        'exploratory_testing_settings',
        'manual_testing_settings',
        'automated_testing_settings',
        'continuous_testing_settings',
        'shift_left_testing_settings',
        'shift_right_testing_settings',
        'testing_in_production_settings',
        'chaos_testing_settings',
        'fault_injection_settings',
        'resilience_testing_settings',
        'disaster_recovery_testing_settings',
        'business_continuity_testing_settings',
        'backup_testing_settings',
        'restore_testing_settings',
        'recovery_testing_settings',
        'failover_testing_settings',
        'failback_testing_settings',
        'high_availability_testing_settings',
        'clustering_testing_settings',
        'replication_testing_settings',
        'synchronization_testing_settings',
        'consistency_testing_settings',
        'data_integrity_testing_settings',
        'data_quality_testing_settings',
        'data_validation_testing_settings',
        'data_migration_testing_settings',
        'data_transformation_testing_settings',
        'data_cleansing_testing_settings',
        'data_enrichment_testing_settings',
        'data_governance_testing_settings',
        'data_lineage_testing_settings',
        'data_catalog_testing_settings',
        'metadata_testing_settings',
        'schema_testing_settings',
        'api_testing_settings',
        'contract_testing_settings',
        'service_testing_settings',
        'component_testing_settings',
        'unit_testing_settings',
        'mock_testing_settings',
        'stub_testing_settings',
        'fake_testing_settings',
        'test_double_settings',
        'test_data_settings',
        'test_environment_settings',
        'test_infrastructure_settings',
        'test_automation_settings',
        'test_framework_settings',
        'test_tools_settings',
        'test_reporting_settings',
        'test_metrics_settings',
        'test_coverage_settings',
        'test_quality_settings',
        'test_efficiency_settings',
        'test_effectiveness_settings',
        'test_optimization_settings',
        'test_strategy_settings',
        'test_planning_settings',
        'test_design_settings',
        'test_execution_settings',
        'test_management_settings',
        'test_governance_settings',
        'test_compliance_settings',
        'test_audit_settings',
        'test_documentation_settings',
        'test_training_settings',
        'test_certification_settings',
        'test_best_practices_settings',
        'test_standards_settings',
        'test_guidelines_settings',
        'test_procedures_settings',
        'test_protocols_settings',
        'test_policies_settings',
        'test_rules_settings',
        'test_regulations_settings',
        'test_requirements_settings',
        'test_specifications_settings',
        'test_criteria_settings',
        'test_conditions_settings',
        'test_scenarios_settings',
        'test_cases_settings',
        'test_scripts_settings',
        'test_suites_settings',
        'test_plans_settings',
        'test_schedules_settings',
        'test_timelines_settings',
        'test_milestones_settings',
        'test_deliverables_settings',
        'test_artifacts_settings',
        'test_assets_settings',
        'test_resources_settings',
        'test_capacity_settings',
        'test_allocation_settings',
        'test_utilization_settings',
        'test_productivity_settings',
        'test_performance_settings',
        'test_roi_settings',
        'test_value_settings',
        'test_impact_settings',
        'test_benefits_settings',
        'test_outcomes_settings',
        'test_results_settings',
        'test_findings_settings',
        'test_insights_settings',
        'test_recommendations_settings',
        'test_actions_settings',
        'test_improvements_settings',
        'test_enhancements_settings',
        'test_innovations_settings',
        'test_transformations_settings',
        'test_evolutions_settings',
        'test_revolutions_settings',
        'test_disruptions_settings',
        'test_paradigm_shifts_settings',
        'test_breakthroughs_settings',
        'test_discoveries_settings',
        'test_inventions_settings',
        'test_creations_settings',
        'test_developments_settings',
        'test_advancements_settings',
        'test_progressions_settings',
        'test_achievements_settings',
        'test_accomplishments_settings',
        'test_successes_settings',
        'test_victories_settings',
        'test_wins_settings',
        'test_triumphs_settings',
        'test_conquests_settings',
        'test_mastery_settings',
        'test_excellence_settings',
        'test_perfection_settings',
        'test_optimization_advanced_settings',
        'test_maximization_settings',
        'test_minimization_settings',
        'test_balance_settings',
        'test_harmony_settings',
        'test_synergy_settings',
        'test_integration_ultimate_settings',
        'test_unification_settings',
        'test_consolidation_settings',
        'test_convergence_settings',
        'test_alignment_settings',
        'test_coordination_ultimate_settings',
        'test_collaboration_settings',
        'test_cooperation_settings',
        'test_partnership_settings',
        'test_alliance_settings',
        'test_coalition_settings',
        'test_federation_settings',
        'test_confederation_settings',
        'test_union_settings',
        'test_association_settings',
        'test_organization_settings',
        'test_institution_settings',
        'test_establishment_settings',
        'test_foundation_settings',
        'test_framework_ultimate_settings',
        'test_structure_settings',
        'test_architecture_ultimate_settings',
        'test_design_ultimate_settings',
        'test_blueprint_settings',
        'test_plan_ultimate_settings',
        'test_strategy_ultimate_settings',
        'test_vision_settings',
        'test_mission_settings',
        'test_purpose_settings',
        'test_goal_settings',
        'test_objective_settings',
        'test_target_settings',
        'test_aim_settings',
        'test_intention_settings',
        'test_aspiration_settings',
        'test_ambition_settings',
        'test_dream_settings',
        'test_hope_settings',
        'test_wish_settings',
        'test_desire_settings',
        'test_want_settings',
        'test_need_settings',
        'test_requirement_ultimate_settings',
        'test_necessity_settings',
        'test_essential_settings',
        'test_critical_settings',
        'test_vital_settings',
        'test_crucial_settings',
        'test_important_settings',
        'test_significant_settings',
        'test_meaningful_settings',
        'test_valuable_settings',
        'test_worthwhile_settings',
        'test_beneficial_settings',
        'test_advantageous_settings',
        'test_profitable_settings',
        'test_rewarding_settings',
        'test_satisfying_settings',
        'test_fulfilling_settings',
        'test_gratifying_settings',
        'test_pleasing_settings',
        'test_enjoyable_settings',
        'test_delightful_settings',
        'test_wonderful_settings',
        'test_amazing_settings',
        'test_incredible_settings',
        'test_fantastic_settings',
        'test_marvelous_settings',
        'test_spectacular_settings',
        'test_outstanding_settings',
        'test_exceptional_settings',
        'test_extraordinary_settings',
        'test_remarkable_settings',
        'test_impressive_settings',
        'test_stunning_settings',
        'test_breathtaking_settings',
        'test_awe_inspiring_settings',
        'test_mind_blowing_settings',
        'test_revolutionary_settings',
        'test_groundbreaking_settings',
        'test_pioneering_settings',
        'test_innovative_ultimate_settings',
        'test_creative_settings',
        'test_original_settings',
        'test_unique_settings',
        'test_distinctive_settings',
        'test_special_settings',
        'test_exclusive_settings',
        'test_premium_settings',
        'test_luxury_settings',
        'test_elite_settings',
        'test_superior_settings',
        'test_supreme_settings',
        'test_ultimate_settings',
        'test_final_settings',
        'test_complete_settings',
        'test_comprehensive_settings',
        'test_thorough_settings',
        'test_exhaustive_settings',
        'test_extensive_settings',
        'test_detailed_settings',
        'test_in_depth_settings',
        'test_profound_settings',
        'test_deep_settings',
        'test_rich_settings',
        'test_full_settings',
        'test_total_settings',
        'test_entire_settings',
        'test_whole_settings',
        'test_all_encompassing_settings',
        'test_all_inclusive_settings',
        'test_universal_settings',
        'test_global_ultimate_settings',
        'test_worldwide_settings',
        'test_international_settings',
        'test_multinational_settings',
        'test_transnational_settings',
        'test_cross_border_settings',
        'test_cross_cultural_settings',
        'test_multicultural_settings',
        'test_diverse_settings',
        'test_varied_settings',
        'test_different_settings',
        'test_alternative_settings',
        'test_optional_settings',
        'test_flexible_ultimate_settings',
        'test_adaptable_ultimate_settings',
        'test_adjustable_settings',
        'test_customizable_ultimate_settings',
        'test_configurable_ultimate_settings',
        'test_programmable_settings',
        'test_scriptable_settings',
        'test_automatable_settings',
        'test_intelligent_ultimate_settings',
        'test_smart_settings',
        'test_clever_settings',
        'test_wise_settings',
        'test_brilliant_settings',
        'test_genius_settings',
        'test_masterful_settings',
        'test_expert_settings',
        'test_professional_settings',
        'test_enterprise_ultimate_settings',
        'test_commercial_settings',
        'test_business_ultimate_settings',
        'test_corporate_settings',
        'test_organizational_settings',
        'test_institutional_ultimate_settings',
        'test_governmental_settings',
        'test_public_settings',
        'test_private_settings',
        'test_personal_settings',
        'test_individual_settings',
        'test_custom_ultimate_settings',
        'test_bespoke_settings',
        'test_tailored_settings',
        'test_personalized_ultimate_settings',
        'test_individualized_settings',
        'test_specialized_settings',
        'test_focused_settings',
        'test_targeted_settings',
        'test_specific_settings',
        'test_precise_settings',
        'test_exact_settings',
        'test_accurate_settings',
        'test_correct_settings',
        'test_right_settings',
        'test_proper_settings',
        'test_appropriate_settings',
        'test_suitable_settings',
        'test_fitting_settings',
        'test_perfect_ultimate_settings',
        'is_active',
        'is_connected',
        'is_syncing',
        'is_healthy',
        'is_test_mode',
        'is_sandbox',
        'is_production',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'tags' => 'array',
        'api_credentials' => 'encrypted:array',
        'authentication_config' => 'array',
        'connection_config' => 'array',
        'sync_config' => 'array',
        'mapping_config' => 'array',
        'transformation_config' => 'array',
        'validation_config' => 'array',
        'error_handling_config' => 'array',
        'retry_config' => 'array',
        'notification_config' => 'array',
        'security_config' => 'array',
        'compliance_config' => 'array',
        'performance_config' => 'array',
        'monitoring_config' => 'array',
        'logging_config' => 'array',
        'backup_config' => 'array',
        'archive_config' => 'array',
        'cleanup_config' => 'array',
        'maintenance_config' => 'array',
        'upgrade_config' => 'array',
        'migration_config' => 'array',
        'integration_settings' => 'array',
        'platform_settings' => 'array',
        'store_settings' => 'array',
        'sync_settings' => 'array',
        'api_settings' => 'array',
        'webhook_settings' => 'array',
        'notification_settings' => 'array',
        'security_settings' => 'array',
        'compliance_settings' => 'array',
        'performance_settings' => 'array',
        'monitoring_settings' => 'array',
        'logging_settings' => 'array',
        'backup_settings' => 'array',
        'archive_settings' => 'array',
        'cleanup_settings' => 'array',
        'maintenance_settings' => 'array',
        'upgrade_settings' => 'array',
        'migration_settings' => 'array',
        'custom_settings' => 'array',
        'advanced_settings' => 'array',
        'experimental_settings' => 'array',
        'beta_settings' => 'array',
        'debug_settings' => 'array',
        'test_settings' => 'array',
        'development_settings' => 'array',
        'staging_settings' => 'array',
        'production_settings' => 'array',
        'environment_settings' => 'array',
        'deployment_settings' => 'array',
        'configuration_settings' => 'array',
        'operational_settings' => 'array',
        'business_settings' => 'array',
        'technical_settings' => 'array',
        'functional_settings' => 'array',
        'non_functional_settings' => 'array',
        'quality_settings' => 'array',
        'usability_settings' => 'array',
        'accessibility_settings' => 'array',
        'internationalization_settings' => 'array',
        'localization_settings' => 'array',
        'globalization_settings' => 'array',
        'regionalization_settings' => 'array',
        'culturalization_settings' => 'array',
        'personalization_settings' => 'array',
        'customization_settings' => 'array',
        'metadata' => 'array',
        'last_sync_at' => 'datetime',
        'last_successful_sync_at' => 'datetime',
        'last_failed_sync_at' => 'datetime',
        'auto_sync_enabled' => 'boolean',
        'real_time_sync_enabled' => 'boolean',
        'webhook_enabled' => 'boolean',
        'is_active' => 'boolean',
        'is_connected' => 'boolean',
        'is_syncing' => 'boolean',
        'is_healthy' => 'boolean',
        'is_test_mode' => 'boolean',
        'is_sandbox' => 'boolean',
        'is_production' => 'boolean',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * الحصول على خيارات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'status', 'sync_status', 'is_active', 'is_connected',
                'auto_sync_enabled', 'real_time_sync_enabled'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقات
     */
    public function platform(): BelongsTo
    {
        return $this->belongsTo(ECommercePlatform::class, 'platform_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Company::class, 'company_id');
    }

    public function integration(): HasOne
    {
        return $this->hasOne(ECommerceIntegration::class, 'store_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(ECommerceProduct::class, 'store_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(ECommerceOrder::class, 'store_id');
    }

    public function customers(): HasMany
    {
        return $this->hasMany(ECommerceCustomer::class, 'store_id');
    }

    public function syncLogs(): HasMany
    {
        return $this->hasMany(ECommerceSyncLog::class, 'store_id');
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(ECommerceWebhook::class, 'store_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * النطاقات المحلية
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeConnected($query)
    {
        return $query->where('is_connected', true);
    }

    public function scopeHealthy($query)
    {
        return $query->where('is_healthy', true);
    }

    public function scopeProduction($query)
    {
        return $query->where('is_production', true);
    }

    public function scopeTestMode($query)
    {
        return $query->where('is_test_mode', true);
    }

    public function scopeByPlatform($query, $platformId)
    {
        return $query->where('platform_id', $platformId);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeAutoSyncEnabled($query)
    {
        return $query->where('auto_sync_enabled', true);
    }

    public function scopeRealTimeSyncEnabled($query)
    {
        return $query->where('real_time_sync_enabled', true);
    }

    public function scopeWebhookEnabled($query)
    {
        return $query->where('webhook_enabled', true);
    }

    /**
     * الطرق المساعدة
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isConnected(): bool
    {
        return $this->is_connected;
    }

    public function isHealthy(): bool
    {
        return $this->is_healthy;
    }

    public function isSyncing(): bool
    {
        return $this->is_syncing;
    }

    public function isProduction(): bool
    {
        return $this->is_production;
    }

    public function isTestMode(): bool
    {
        return $this->is_test_mode;
    }

    public function isSandbox(): bool
    {
        return $this->is_sandbox;
    }

    public function hasAutoSync(): bool
    {
        return $this->auto_sync_enabled;
    }

    public function hasRealTimeSync(): bool
    {
        return $this->real_time_sync_enabled;
    }

    public function hasWebhooks(): bool
    {
        return $this->webhook_enabled;
    }

    public function getFullUrl(): string
    {
        return $this->store_url;
    }

    public function getAdminUrl(): string
    {
        return $this->admin_url ?? $this->store_url . '/admin';
    }

    public function getApiUrl(): string
    {
        return $this->api_url ?? $this->store_url . '/api';
    }

    public function getWebhookUrl(): string
    {
        return $this->webhook_url ?? route('ecommerce.webhooks.handle', ['store' => $this->uuid]);
    }

    public function getLastSyncStatus(): string
    {
        if ($this->last_successful_sync_at && $this->last_failed_sync_at) {
            return $this->last_successful_sync_at > $this->last_failed_sync_at ? 'success' : 'failed';
        }

        if ($this->last_successful_sync_at) {
            return 'success';
        }

        if ($this->last_failed_sync_at) {
            return 'failed';
        }

        return 'never';
    }

    public function getTimeSinceLastSync(): ?int
    {
        if (!$this->last_sync_at) {
            return null;
        }

        return now()->diffInMinutes($this->last_sync_at);
    }

    public function needsSync(): bool
    {
        if (!$this->auto_sync_enabled) {
            return false;
        }

        $frequency = $this->sync_frequency ?? 60; // minutes
        $timeSinceLastSync = $this->getTimeSinceLastSync();

        return $timeSinceLastSync === null || $timeSinceLastSync >= $frequency;
    }

    public function canSync(): bool
    {
        return $this->isActive() && $this->isConnected() && !$this->isSyncing();
    }

    public function getHealthScore(): float
    {
        $score = 0;
        $maxScore = 10;

        // Connection status (2 points)
        if ($this->isConnected()) {
            $score += 2;
        }

        // Health status (2 points)
        if ($this->isHealthy()) {
            $score += 2;
        }

        // Recent successful sync (2 points)
        if ($this->last_successful_sync_at && $this->last_successful_sync_at > now()->subHours(24)) {
            $score += 2;
        }

        // No recent failures (2 points)
        if (!$this->last_failed_sync_at || $this->last_failed_sync_at < now()->subHours(24)) {
            $score += 2;
        }

        // Active status (1 point)
        if ($this->isActive()) {
            $score += 1;
        }

        // Production readiness (1 point)
        if ($this->isProduction()) {
            $score += 1;
        }

        return ($score / $maxScore) * 100;
    }

    public function getHealthStatus(): string
    {
        $score = $this->getHealthScore();

        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 50) return 'fair';
        if ($score >= 25) return 'poor';
        
        return 'critical';
    }

    public function getApiCredentials(): array
    {
        return $this->api_credentials ?? [];
    }

    public function hasValidCredentials(): bool
    {
        $credentials = $this->getApiCredentials();
        
        switch ($this->platform->authentication_type) {
            case 'api_key':
                return !empty($credentials['api_key']);
            case 'oauth':
                return !empty($credentials['access_token']);
            case 'basic':
                return !empty($credentials['username']) && !empty($credentials['password']);
            case 'bearer':
                return !empty($credentials['token']);
            default:
                return false;
        }
    }

    public function getSyncCapabilities(): array
    {
        return array_intersect(
            $this->platform->getSupportedSyncCapabilities(),
            $this->sync_config['enabled_capabilities'] ?? []
        );
    }

    public function supportsSyncCapability($capability): bool
    {
        return in_array($capability, $this->getSyncCapabilities());
    }

    public function getEnabledSyncTypes(): array
    {
        $enabled = [];
        
        if ($this->supportsSyncCapability('products')) {
            $enabled[] = 'products';
        }
        
        if ($this->supportsSyncCapability('orders')) {
            $enabled[] = 'orders';
        }
        
        if ($this->supportsSyncCapability('customers')) {
            $enabled[] = 'customers';
        }
        
        if ($this->supportsSyncCapability('inventory')) {
            $enabled[] = 'inventory';
        }

        return $enabled;
    }

    public function getConfiguration($key = null, $default = null)
    {
        $config = array_merge(
            $this->platform->getDefaultSettings(),
            $this->platform_settings ?? [],
            $this->store_settings ?? []
        );

        if ($key === null) {
            return $config;
        }

        return data_get($config, $key, $default);
    }

    public function updateConfiguration($key, $value): void
    {
        $settings = $this->store_settings ?? [];
        data_set($settings, $key, $value);
        $this->store_settings = $settings;
        $this->save();
    }

    public function resetToDefaults(): void
    {
        $this->store_settings = $this->platform->getDefaultSettings();
        $this->save();
    }

    public function testConnection(): array
    {
        try {
            // Test API connection
            $integrationService = app(\App\Domains\ECommerce\Services\ECommerceIntegrationService::class);
            $result = $integrationService->testConnection($this);

            $this->update([
                'is_connected' => $result['success'],
                'health_status' => $result['success'] ? 'healthy' : 'unhealthy',
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->update([
                'is_connected' => false,
                'health_status' => 'unhealthy',
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
        }
    }

    public function performHealthCheck(): array
    {
        $checks = [];
        $overallHealth = true;

        // Connection check
        $connectionResult = $this->testConnection();
        $checks['connection'] = $connectionResult;
        if (!$connectionResult['success']) {
            $overallHealth = false;
        }

        // Credentials check
        $credentialsValid = $this->hasValidCredentials();
        $checks['credentials'] = [
            'success' => $credentialsValid,
            'message' => $credentialsValid ? 'Credentials are valid' : 'Invalid or missing credentials',
        ];
        if (!$credentialsValid) {
            $overallHealth = false;
        }

        // Sync status check
        $syncHealthy = $this->getLastSyncStatus() !== 'failed';
        $checks['sync'] = [
            'success' => $syncHealthy,
            'message' => $syncHealthy ? 'Sync is healthy' : 'Recent sync failures detected',
            'last_sync' => $this->last_sync_at,
            'last_successful_sync' => $this->last_successful_sync_at,
            'last_failed_sync' => $this->last_failed_sync_at,
        ];
        if (!$syncHealthy) {
            $overallHealth = false;
        }

        // Update health status
        $this->update([
            'is_healthy' => $overallHealth,
            'health_status' => $overallHealth ? 'healthy' : 'unhealthy',
        ]);

        return [
            'overall_health' => $overallHealth,
            'health_score' => $this->getHealthScore(),
            'health_status' => $this->getHealthStatus(),
            'checks' => $checks,
            'timestamp' => now(),
        ];
    }

    /**
     * تحويل النموذج إلى مصفوفة للعرض
     */
    public function toDisplayArray(): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'slug' => $this->slug,
            'platform' => $this->platform->name,
            'platform_type' => $this->platform->type,
            'store_url' => $this->getFullUrl(),
            'status' => $this->status,
            'health_status' => $this->getHealthStatus(),
            'health_score' => $this->getHealthScore(),
            'sync_status' => $this->sync_status,
            'last_sync' => $this->last_sync_at,
            'is_active' => $this->isActive(),
            'is_connected' => $this->isConnected(),
            'is_healthy' => $this->isHealthy(),
            'auto_sync_enabled' => $this->hasAutoSync(),
            'real_time_sync_enabled' => $this->hasRealTimeSync(),
            'webhook_enabled' => $this->hasWebhooks(),
            'sync_capabilities' => $this->getSyncCapabilities(),
            'enabled_sync_types' => $this->getEnabledSyncTypes(),
        ];
    }
}
