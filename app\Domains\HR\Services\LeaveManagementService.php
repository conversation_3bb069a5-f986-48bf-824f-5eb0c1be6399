<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\LeaveRequest;
use App\Domains\HR\Models\LeaveType;
use App\Domains\HR\Models\LeaveBalance;
use App\Domains\HR\Events\LeaveRequestSubmitted;
use App\Domains\HR\Events\LeaveRequestApproved;
use App\Domains\HR\Events\LeaveRequestRejected;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Carbon\Carbon;

/**
 * خدمة إدارة الإجازات المتقدمة
 * نظام شامل لإدارة دورة حياة الإجازات
 */
class LeaveManagementService
{
    /**
     * إنشاء طلب إجازة جديد
     */
    public function createLeaveRequest(array $data): LeaveRequest
    {
        DB::beginTransaction();

        try {
            // التحقق من صحة البيانات
            $this->validateLeaveRequest($data);

            // حساب أيام العمل
            $workingDays = $this->calculateWorkingDays(
                Carbon::parse($data['start_date']),
                Carbon::parse($data['end_date']),
                $data['half_day'] ?? false
            );

            // إنشاء الطلب
            $leaveRequest = LeaveRequest::create([
                'employee_id' => $data['employee_id'],
                'leave_type_id' => $data['leave_type_id'],
                'request_number' => LeaveRequest::generateRequestNumber(),
                'title' => $data['title'] ?? null,
                'description' => $data['description'] ?? null,
                'reason' => $data['reason'],
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'return_date' => $data['return_date'] ?? Carbon::parse($data['end_date'])->addDay(),
                'total_days' => Carbon::parse($data['start_date'])->diffInDays(Carbon::parse($data['end_date'])) + 1,
                'working_days' => $workingDays,
                'half_day' => $data['half_day'] ?? false,
                'half_day_period' => $data['half_day_period'] ?? null,
                'status' => 'SUBMITTED',
                'priority' => $data['priority'] ?? 'NORMAL',
                'urgency_level' => $data['urgency_level'] ?? 1,
                'requested_at' => now(),
                'coverage_employee_id' => $data['coverage_employee_id'] ?? null,
                'handover_notes' => $data['handover_notes'] ?? null,
                'emergency_contact' => $data['emergency_contact'] ?? null,
                'emergency_phone' => $data['emergency_phone'] ?? null,
                'location_during_leave' => $data['location_during_leave'] ?? null,
                'supporting_documents' => $data['supporting_documents'] ?? [],
                'employee_notes' => $data['employee_notes'] ?? null,
            ]);

            // تحديد متطلبات الموافقة
            $this->setApprovalWorkflow($leaveRequest);

            // تحديث الأيام المعلقة في الرصيد
            $this->updatePendingDays($leaveRequest, 'add');

            DB::commit();

            // إرسال الأحداث
            Event::dispatch(new LeaveRequestSubmitted($leaveRequest));

            return $leaveRequest;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * التحقق من صحة طلب الإجازة
     */
    protected function validateLeaveRequest(array $data): void
    {
        $employee = Employee::findOrFail($data['employee_id']);
        $leaveType = LeaveType::findOrFail($data['leave_type_id']);

        // التحقق من أهلية الموظف
        if (!$leaveType->isEmployeeEligible($employee)) {
            throw new \Exception('الموظف غير مؤهل لهذا النوع من الإجازة');
        }

        // التحقق من الرصيد المتاح
        $balance = $this->getEmployeeLeaveBalance($employee, $data['leave_type_id']);
        $requestedDays = $this->calculateWorkingDays(
            Carbon::parse($data['start_date']),
            Carbon::parse($data['end_date']),
            $data['half_day'] ?? false
        );

        if ($balance['available'] < $requestedDays) {
            throw new \Exception('الرصيد المتاح غير كافي لهذا الطلب');
        }

        // التحقق من الحد الأدنى والأقصى للأيام
        if ($leaveType->min_days_per_request && $requestedDays < $leaveType->min_days_per_request) {
            throw new \Exception("الحد الأدنى للطلب هو {$leaveType->min_days_per_request} أيام");
        }

        if ($leaveType->max_days_per_request && $requestedDays > $leaveType->max_days_per_request) {
            throw new \Exception("الحد الأقصى للطلب هو {$leaveType->max_days_per_request} أيام");
        }

        // التحقق من فترة الإشعار المسبق
        if ($leaveType->advance_notice_days) {
            $noticeDate = Carbon::parse($data['start_date'])->subDays($leaveType->advance_notice_days);
            if (now() > $noticeDate && $data['priority'] !== 'EMERGENCY') {
                throw new \Exception("يجب تقديم الطلب قبل {$leaveType->advance_notice_days} أيام على الأقل");
            }
        }

        // التحقق من تضارب الإجازات
        $hasConflict = LeaveRequest::where('employee_id', $data['employee_id'])
            ->where('status', 'APPROVED')
            ->where(function ($query) use ($data) {
                $query->whereBetween('start_date', [$data['start_date'], $data['end_date']])
                      ->orWhereBetween('end_date', [$data['start_date'], $data['end_date']])
                      ->orWhere(function ($q) use ($data) {
                          $q->where('start_date', '<=', $data['start_date'])
                            ->where('end_date', '>=', $data['end_date']);
                      });
            })
            ->exists();

        if ($hasConflict) {
            throw new \Exception('يوجد تضارب مع إجازة أخرى معتمدة');
        }

        // التحقق من فترات المنع
        $startDate = Carbon::parse($data['start_date']);
        if (!$leaveType->canRequestOnDate($startDate)) {
            throw new \Exception('لا يمكن طلب إجازة في هذا التاريخ');
        }
    }

    /**
     * حساب أيام العمل
     */
    public function calculateWorkingDays(Carbon $startDate, Carbon $endDate, bool $halfDay = false): float
    {
        $workingDays = 0;
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            // تحقق من أن اليوم ليس عطلة نهاية أسبوع
            if (!$currentDate->isWeekend()) {
                // تحقق من أن اليوم ليس عطلة رسمية
                if (!$this->isHoliday($currentDate)) {
                    $workingDays++;
                }
            }
            $currentDate->addDay();
        }

        return $halfDay ? $workingDays / 2 : $workingDays;
    }

    /**
     * التحقق من العطل الرسمية
     */
    protected function isHoliday(Carbon $date): bool
    {
        // يمكن تطوير هذه الدالة للتحقق من العطل الرسمية
        // من جدول منفصل أو خدمة خارجية
        return false;
    }

    /**
     * تحديد سير عمل الموافقة
     */
    protected function setApprovalWorkflow(LeaveRequest $leaveRequest): void
    {
        $leaveType = $leaveRequest->leaveType;
        $employee = $leaveRequest->employee;

        // تحديد المعتمد الحالي
        if ($leaveType->requires_manager_approval) {
            $leaveRequest->update([
                'current_approver_id' => $employee->manager_id,
                'status' => 'PENDING_MANAGER',
                'approval_level' => 1,
            ]);
        } elseif ($leaveType->requires_hr_approval) {
            $hrManager = $this->getHRManager();
            $leaveRequest->update([
                'current_approver_id' => $hrManager->id,
                'status' => 'PENDING_HR',
                'approval_level' => 2,
            ]);
        }

        // الموافقة التلقائية للطلبات الصغيرة
        if ($leaveType->auto_approve_threshold_days && 
            $leaveRequest->working_days <= $leaveType->auto_approve_threshold_days) {
            $this->approveLeaveRequest($leaveRequest->id, null, 'موافقة تلقائية');
        }
    }

    /**
     * الموافقة على طلب الإجازة
     */
    public function approveLeaveRequest(int $requestId, int $approverId = null, string $notes = null): LeaveRequest
    {
        DB::beginTransaction();

        try {
            $leaveRequest = LeaveRequest::findOrFail($requestId);
            
            if (!$leaveRequest->canBeApproved()) {
                throw new \Exception('لا يمكن الموافقة على هذا الطلب');
            }

            // الموافقة على الطلب
            $leaveRequest->approve($approverId ?? auth()->id(), $notes);

            // تحديث الأيام المعلقة والمستخدمة في الرصيد
            $this->updatePendingDays($leaveRequest, 'remove');
            $this->updateUsedDays($leaveRequest, 'add');

            DB::commit();

            // إرسال الأحداث
            Event::dispatch(new LeaveRequestApproved($leaveRequest));

            return $leaveRequest;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * رفض طلب الإجازة
     */
    public function rejectLeaveRequest(int $requestId, int $approverId, string $reason): LeaveRequest
    {
        DB::beginTransaction();

        try {
            $leaveRequest = LeaveRequest::findOrFail($requestId);
            
            if (!$leaveRequest->canBeRejected()) {
                throw new \Exception('لا يمكن رفض هذا الطلب');
            }

            // رفض الطلب
            $leaveRequest->reject($approverId, $reason);

            // إزالة الأيام المعلقة من الرصيد
            $this->updatePendingDays($leaveRequest, 'remove');

            DB::commit();

            // إرسال الأحداث
            Event::dispatch(new LeaveRequestRejected($leaveRequest));

            return $leaveRequest;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إلغاء طلب الإجازة
     */
    public function cancelLeaveRequest(int $requestId, string $reason = null): LeaveRequest
    {
        DB::beginTransaction();

        try {
            $leaveRequest = LeaveRequest::findOrFail($requestId);
            
            if (!$leaveRequest->canBeCancelled()) {
                throw new \Exception('لا يمكن إلغاء هذا الطلب');
            }

            // إلغاء الطلب
            $leaveRequest->cancel($reason);

            // تحديث الرصيد
            if ($leaveRequest->status === 'APPROVED') {
                $this->updateUsedDays($leaveRequest, 'remove');
            } else {
                $this->updatePendingDays($leaveRequest, 'remove');
            }

            DB::commit();

            return $leaveRequest;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * الحصول على رصيد إجازات الموظف
     */
    public function getEmployeeLeaveBalance(Employee $employee, int $leaveTypeId = null): array
    {
        $query = $employee->leaveBalances()
                          ->with('leaveType')
                          ->where('year', now()->year)
                          ->where('is_active', true);

        if ($leaveTypeId) {
            $query->where('leave_type_id', $leaveTypeId);
        }

        $balances = $query->get();

        if ($leaveTypeId) {
            $balance = $balances->first();
            return $balance ? [
                'entitled' => $balance->entitled_days,
                'used' => $balance->used_days,
                'remaining' => $balance->remaining_days,
                'pending' => $balance->pending_days,
                'available' => $balance->available_days,
                'carried_forward' => $balance->carried_forward_days,
            ] : [
                'entitled' => 0,
                'used' => 0,
                'remaining' => 0,
                'pending' => 0,
                'available' => 0,
                'carried_forward' => 0,
            ];
        }

        return $balances->mapWithKeys(function ($balance) {
            return [$balance->leaveType->name => [
                'entitled' => $balance->entitled_days,
                'used' => $balance->used_days,
                'remaining' => $balance->remaining_days,
                'pending' => $balance->pending_days,
                'available' => $balance->available_days,
                'carried_forward' => $balance->carried_forward_days,
            ]];
        })->toArray();
    }

    /**
     * تحديث الأيام المعلقة في الرصيد
     */
    protected function updatePendingDays(LeaveRequest $leaveRequest, string $action): void
    {
        $balance = LeaveBalance::where('employee_id', $leaveRequest->employee_id)
                              ->where('leave_type_id', $leaveRequest->leave_type_id)
                              ->where('year', now()->year)
                              ->where('is_active', true)
                              ->first();

        if ($balance) {
            if ($action === 'add') {
                $balance->increment('pending_days', $leaveRequest->working_days);
            } else {
                $balance->decrement('pending_days', $leaveRequest->working_days);
            }
        }
    }

    /**
     * تحديث الأيام المستخدمة في الرصيد
     */
    protected function updateUsedDays(LeaveRequest $leaveRequest, string $action): void
    {
        $balance = LeaveBalance::where('employee_id', $leaveRequest->employee_id)
                              ->where('leave_type_id', $leaveRequest->leave_type_id)
                              ->where('year', now()->year)
                              ->where('is_active', true)
                              ->first();

        if ($balance) {
            if ($action === 'add') {
                $balance->increment('used_days', $leaveRequest->working_days);
                $balance->decrement('remaining_days', $leaveRequest->working_days);
            } else {
                $balance->decrement('used_days', $leaveRequest->working_days);
                $balance->increment('remaining_days', $leaveRequest->working_days);
            }
        }
    }

    /**
     * إنشاء أرصدة الإجازات للموظف الجديد
     */
    public function createLeaveBalancesForEmployee(Employee $employee): void
    {
        $leaveTypes = LeaveType::active()->get();

        foreach ($leaveTypes as $leaveType) {
            if ($leaveType->isEmployeeEligible($employee)) {
                $leaveType->createBalanceForEmployee($employee);
            }
        }
    }

    /**
     * ترحيل أرصدة الإجازات للسنة الجديدة
     */
    public function carryForwardLeaveBalances(int $year): int
    {
        $balances = LeaveBalance::where('year', $year - 1)
                               ->where('auto_carry_forward', true)
                               ->where('remaining_days', '>', 0)
                               ->get();

        $carriedForward = 0;

        foreach ($balances as $balance) {
            if ($balance->carryForward($year)) {
                $carriedForward++;
            }
        }

        return $carriedForward;
    }

    /**
     * الحصول على مدير الموارد البشرية
     */
    protected function getHRManager(): \App\Models\User
    {
        // يمكن تطوير هذه الدالة للحصول على مدير الموارد البشرية
        // من جدول الأدوار أو الإعدادات
        return \App\Models\User::whereHas('roles', function ($query) {
            $query->where('name', 'hr-manager');
        })->first();
    }

    /**
     * تحديث جميع أرصدة الإجازات
     */
    public function updateAllLeaveBalances(): int
    {
        $balances = LeaveBalance::where('is_active', true)
                               ->where('is_frozen', false)
                               ->get();

        $updated = 0;

        foreach ($balances as $balance) {
            if ($balance->recalculate()) {
                $updated++;
            }
        }

        return $updated;
    }

    /**
     * الحصول على إحصائيات الإجازات
     */
    public function getLeaveStatistics(array $filters = []): array
    {
        $query = LeaveRequest::query();

        // تطبيق المرشحات
        if (isset($filters['employee_id'])) {
            $query->where('employee_id', $filters['employee_id']);
        }

        if (isset($filters['department_id'])) {
            $query->whereHas('employee', function ($q) use ($filters) {
                $q->where('department_id', $filters['department_id']);
            });
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('start_date', [$filters['start_date'], $filters['end_date']]);
        }

        $requests = $query->get();

        return [
            'total_requests' => $requests->count(),
            'approved_requests' => $requests->where('status', 'APPROVED')->count(),
            'pending_requests' => $requests->whereIn('status', ['SUBMITTED', 'PENDING_MANAGER', 'PENDING_HR'])->count(),
            'rejected_requests' => $requests->where('status', 'REJECTED')->count(),
            'total_days_requested' => $requests->sum('working_days'),
            'total_days_approved' => $requests->where('status', 'APPROVED')->sum('working_days'),
            'average_request_days' => $requests->avg('working_days'),
            'by_leave_type' => $requests->groupBy('leave_type_id')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_days' => $group->sum('working_days'),
                ];
            }),
            'by_status' => $requests->groupBy('status')->map->count(),
        ];
    }
}
