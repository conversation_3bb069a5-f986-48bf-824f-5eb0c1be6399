<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\CustomerInteraction;
use App\Domains\CRM\Models\MarketingCampaign;
use App\Domains\CRM\Models\LeadSource;
use App\Domains\CRM\Models\CustomerSatisfactionSurvey;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * خدمة التقارير المتقدمة - CRM Reporting Service
 * إنشاء تقارير شاملة ومتقدمة لنظام CRM
 */
class CRMReportingService
{
    /**
     * تقرير الأداء الشامل
     */
    public function generateExecutiveDashboard(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'summary' => $this->getExecutiveSummary($dateFrom, $dateTo),
            'kpis' => $this->getKeyPerformanceIndicators($dateFrom, $dateTo),
            'trends' => $this->getPerformanceTrends($dateFrom, $dateTo),
            'forecasts' => $this->getPerformanceForecasts(),
            'alerts' => $this->getPerformanceAlerts(),
            'recommendations' => $this->getStrategicRecommendations(),
        ];
    }

    /**
     * تقرير أداء المبيعات
     */
    public function generateSalesReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'overview' => $this->getSalesOverview($dateFrom, $dateTo),
            'pipeline_analysis' => $this->analyzeSalesPipeline($dateFrom, $dateTo),
            'conversion_funnel' => $this->analyzeConversionFunnel($dateFrom, $dateTo),
            'sales_rep_performance' => $this->analyzeSalesRepPerformance($dateFrom, $dateTo),
            'opportunity_analysis' => $this->analyzeOpportunities($dateFrom, $dateTo),
            'win_loss_analysis' => $this->analyzeWinLoss($dateFrom, $dateTo),
            'forecasting' => $this->generateSalesForecast($dateFrom, $dateTo),
        ];
    }

    /**
     * تقرير أداء التسويق
     */
    public function generateMarketingReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'campaign_performance' => $this->analyzeCampaignPerformance($dateFrom, $dateTo),
            'lead_generation' => $this->analyzeLeadGeneration($dateFrom, $dateTo),
            'source_analysis' => $this->analyzeLeadSources($dateFrom, $dateTo),
            'channel_effectiveness' => $this->analyzeChannelEffectiveness($dateFrom, $dateTo),
            'attribution_analysis' => $this->analyzeAttribution($dateFrom, $dateTo),
            'roi_analysis' => $this->analyzeMarketingROI($dateFrom, $dateTo),
            'customer_journey' => $this->analyzeCustomerJourney($dateFrom, $dateTo),
        ];
    }

    /**
     * تقرير رضا العملاء
     */
    public function generateCustomerSatisfactionReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'satisfaction_overview' => $this->getSatisfactionOverview($dateFrom, $dateTo),
            'nps_analysis' => $this->analyzeNPSScores($dateFrom, $dateTo),
            'csat_analysis' => $this->analyzeCSATScores($dateFrom, $dateTo),
            'feedback_analysis' => $this->analyzeFeedback($dateFrom, $dateTo),
            'satisfaction_trends' => $this->analyzeSatisfactionTrends($dateFrom, $dateTo),
            'improvement_areas' => $this->identifyImprovementAreas($dateFrom, $dateTo),
            'action_items' => $this->generateActionItems($dateFrom, $dateTo),
        ];
    }

    /**
     * تقرير تحليل العملاء
     */
    public function generateCustomerAnalysisReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'customer_overview' => $this->getCustomerOverview($dateFrom, $dateTo),
            'segmentation_analysis' => $this->analyzeCustomerSegmentation(),
            'lifecycle_analysis' => $this->analyzeCustomerLifecycle(),
            'value_analysis' => $this->analyzeCustomerValue(),
            'retention_analysis' => $this->analyzeCustomerRetention($dateFrom, $dateTo),
            'churn_analysis' => $this->analyzeCustomerChurn($dateFrom, $dateTo),
            'growth_opportunities' => $this->identifyGrowthOpportunities(),
        ];
    }

    /**
     * تقرير أداء الفريق
     */
    public function generateTeamPerformanceReport(Carbon $dateFrom = null, Carbon $dateTo = null): array
    {
        $dateFrom = $dateFrom ?? now()->subDays(30);
        $dateTo = $dateTo ?? now();

        return [
            'team_overview' => $this->getTeamOverview($dateFrom, $dateTo),
            'individual_performance' => $this->analyzeIndividualPerformance($dateFrom, $dateTo),
            'activity_analysis' => $this->analyzeTeamActivity($dateFrom, $dateTo),
            'productivity_metrics' => $this->analyzeProductivityMetrics($dateFrom, $dateTo),
            'goal_tracking' => $this->trackTeamGoals($dateFrom, $dateTo),
            'training_needs' => $this->identifyTrainingNeeds($dateFrom, $dateTo),
            'performance_recommendations' => $this->generatePerformanceRecommendations($dateFrom, $dateTo),
        ];
    }

    /**
     * تقرير مخصص
     */
    public function generateCustomReport(array $config): array
    {
        $dateFrom = Carbon::parse($config['date_from'] ?? now()->subDays(30));
        $dateTo = Carbon::parse($config['date_to'] ?? now());
        
        $report = [
            'config' => $config,
            'generated_at' => now(),
            'data' => [],
        ];

        // إضافة البيانات حسب التكوين
        foreach ($config['sections'] ?? [] as $section) {
            $report['data'][$section] = $this->generateReportSection($section, $dateFrom, $dateTo, $config);
        }

        return $report;
    }

    /**
     * تصدير التقرير
     */
    public function exportReport(array $reportData, string $format = 'pdf'): string
    {
        switch ($format) {
            case 'pdf':
                return $this->exportToPDF($reportData);
            case 'excel':
                return $this->exportToExcel($reportData);
            case 'csv':
                return $this->exportToCSV($reportData);
            case 'json':
                return $this->exportToJSON($reportData);
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }
    }

    // دوال مساعدة للتقارير

    protected function getExecutiveSummary(Carbon $dateFrom, Carbon $dateTo): array
    {
        return [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'total_opportunities' => Opportunity::count(),
            'won_opportunities' => Opportunity::where('stage', 'won')->count(),
            'total_revenue' => Opportunity::where('stage', 'won')->sum('value'),
            'pipeline_value' => Opportunity::whereNotIn('stage', ['won', 'lost'])->sum('value'),
            'conversion_rate' => $this->calculateOverallConversionRate(),
            'customer_satisfaction' => $this->calculateAverageCustomerSatisfaction(),
        ];
    }

    protected function getKeyPerformanceIndicators(Carbon $dateFrom, Carbon $dateTo): array
    {
        $previousPeriod = $dateFrom->copy()->subDays($dateTo->diffInDays($dateFrom));
        
        return [
            'customer_acquisition' => [
                'current' => Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
                'previous' => Customer::whereBetween('created_at', [$previousPeriod, $dateFrom])->count(),
                'change_percentage' => $this->calculateChangePercentage(
                    Customer::whereBetween('created_at', [$previousPeriod, $dateFrom])->count(),
                    Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count()
                ),
            ],
            'revenue' => [
                'current' => Opportunity::where('stage', 'won')
                                       ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                       ->sum('value'),
                'previous' => Opportunity::where('stage', 'won')
                                        ->whereBetween('updated_at', [$previousPeriod, $dateFrom])
                                        ->sum('value'),
                'change_percentage' => $this->calculateChangePercentage(
                    Opportunity::where('stage', 'won')
                              ->whereBetween('updated_at', [$previousPeriod, $dateFrom])
                              ->sum('value'),
                    Opportunity::where('stage', 'won')
                              ->whereBetween('updated_at', [$dateFrom, $dateTo])
                              ->sum('value')
                ),
            ],
            'conversion_rate' => [
                'current' => $this->calculateConversionRate($dateFrom, $dateTo),
                'previous' => $this->calculateConversionRate($previousPeriod, $dateFrom),
                'change_percentage' => $this->calculateChangePercentage(
                    $this->calculateConversionRate($previousPeriod, $dateFrom),
                    $this->calculateConversionRate($dateFrom, $dateTo)
                ),
            ],
        ];
    }

    protected function getSalesOverview(Carbon $dateFrom, Carbon $dateTo): array
    {
        return [
            'total_opportunities' => Opportunity::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'won_opportunities' => Opportunity::where('stage', 'won')
                                             ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                             ->count(),
            'lost_opportunities' => Opportunity::where('stage', 'lost')
                                              ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                              ->count(),
            'total_revenue' => Opportunity::where('stage', 'won')
                                         ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                         ->sum('value'),
            'average_deal_size' => Opportunity::where('stage', 'won')
                                             ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                             ->avg('value'),
            'sales_cycle_length' => $this->calculateAverageSalesCycleLength($dateFrom, $dateTo),
            'pipeline_velocity' => $this->calculatePipelineVelocity($dateFrom, $dateTo),
        ];
    }

    protected function analyzeSalesPipeline(Carbon $dateFrom, Carbon $dateTo): array
    {
        $pipeline = [];
        
        foreach (Opportunity::STAGES as $stage => $label) {
            $opportunities = Opportunity::where('stage', $stage)
                                       ->whereBetween('created_at', [$dateFrom, $dateTo])
                                       ->get();
            
            $pipeline[$stage] = [
                'label' => $label,
                'count' => $opportunities->count(),
                'value' => $opportunities->sum('value'),
                'average_value' => $opportunities->avg('value'),
                'conversion_rate' => $this->calculateStageConversionRate($stage, $dateFrom, $dateTo),
            ];
        }

        return $pipeline;
    }

    protected function analyzeCampaignPerformance(Carbon $dateFrom, Carbon $dateTo): array
    {
        $campaigns = MarketingCampaign::whereBetween('created_at', [$dateFrom, $dateTo])->get();
        
        return $campaigns->map(function ($campaign) {
            return [
                'id' => $campaign->id,
                'name' => $campaign->name,
                'type' => $campaign->type,
                'channel' => $campaign->channel,
                'recipients_count' => $campaign->recipients_count,
                'open_rate' => $campaign->open_rate,
                'click_rate' => $campaign->click_rate,
                'conversion_rate' => $campaign->conversion_rate,
                'generated_leads' => $campaign->generatedOpportunities()->count(),
                'revenue_generated' => $campaign->generatedOpportunities()
                                               ->where('stage', 'won')
                                               ->sum('value'),
                'roi' => $this->calculateCampaignROI($campaign),
            ];
        })->toArray();
    }

    protected function analyzeLeadSources(Carbon $dateFrom, Carbon $dateTo): array
    {
        $sources = LeadSource::active()->get();
        
        return $sources->map(function ($source) use ($dateFrom, $dateTo) {
            $customers = $source->customers()
                               ->whereBetween('created_at', [$dateFrom, $dateTo])
                               ->get();
            
            return [
                'id' => $source->id,
                'name' => $source->name,
                'type' => $source->type,
                'channel' => $source->channel,
                'leads_count' => $customers->count(),
                'converted_count' => $customers->where('status', '!=', 'lead')->count(),
                'conversion_rate' => $customers->count() > 0 ? 
                    round(($customers->where('status', '!=', 'lead')->count() / $customers->count()) * 100, 2) : 0,
                'total_revenue' => $customers->sum('total_spent'),
                'cost_per_lead' => $source->cost_per_lead,
                'customer_acquisition_cost' => $source->customer_acquisition_cost,
                'roi' => $source->return_on_investment,
                'quality_score' => $source->calculated_quality_score,
            ];
        })->toArray();
    }

    protected function getSatisfactionOverview(Carbon $dateFrom, Carbon $dateTo): array
    {
        $surveys = CustomerSatisfactionSurvey::completed()
                                           ->whereBetween('completed_at', [$dateFrom, $dateTo])
                                           ->get();

        return [
            'total_surveys' => $surveys->count(),
            'response_rate' => $this->calculateSurveyResponseRate($dateFrom, $dateTo),
            'average_rating' => $surveys->avg('rating'),
            'average_nps' => $surveys->whereNotNull('nps_score')->avg('nps_score'),
            'average_csat' => $surveys->whereNotNull('csat_score')->avg('csat_score'),
            'promoters_count' => $surveys->where('nps_score', '>=', 9)->count(),
            'detractors_count' => $surveys->where('nps_score', '<=', 6)->count(),
            'nps_score' => $this->calculateNPSScore($surveys),
        ];
    }

    // دوال مساعدة للحسابات

    protected function calculateOverallConversionRate(): float
    {
        $totalOpportunities = Opportunity::count();
        $wonOpportunities = Opportunity::where('stage', 'won')->count();
        
        return $totalOpportunities > 0 ? round(($wonOpportunities / $totalOpportunities) * 100, 2) : 0;
    }

    protected function calculateAverageCustomerSatisfaction(): float
    {
        return CustomerSatisfactionSurvey::completed()->avg('rating') ?? 0;
    }

    protected function calculateChangePercentage(float $previous, float $current): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }

    protected function calculateConversionRate(Carbon $dateFrom, Carbon $dateTo): float
    {
        $opportunities = Opportunity::whereBetween('created_at', [$dateFrom, $dateTo]);
        $total = $opportunities->count();
        $won = $opportunities->where('stage', 'won')->count();
        
        return $total > 0 ? round(($won / $total) * 100, 2) : 0;
    }

    protected function calculateAverageSalesCycleLength(Carbon $dateFrom, Carbon $dateTo): float
    {
        $wonOpportunities = Opportunity::where('stage', 'won')
                                     ->whereBetween('updated_at', [$dateFrom, $dateTo])
                                     ->get();

        if ($wonOpportunities->isEmpty()) {
            return 0;
        }

        $totalDays = $wonOpportunities->sum(function ($opportunity) {
            return $opportunity->created_at->diffInDays($opportunity->updated_at);
        });

        return round($totalDays / $wonOpportunities->count(), 1);
    }

    protected function calculatePipelineVelocity(Carbon $dateFrom, Carbon $dateTo): float
    {
        // حساب سرعة الأنبوب (Pipeline Velocity)
        $opportunities = Opportunity::whereBetween('created_at', [$dateFrom, $dateTo])->get();
        
        if ($opportunities->isEmpty()) {
            return 0;
        }

        $totalValue = $opportunities->sum('value');
        $averageProbability = $opportunities->avg('probability') / 100;
        $averageCycleLength = $this->calculateAverageSalesCycleLength($dateFrom, $dateTo);
        
        return $averageCycleLength > 0 ? round(($totalValue * $averageProbability) / $averageCycleLength, 2) : 0;
    }

    protected function calculateStageConversionRate(string $stage, Carbon $dateFrom, Carbon $dateTo): float
    {
        // حساب معدل التحويل من مرحلة معينة
        $stageOpportunities = Opportunity::where('stage', $stage)
                                        ->whereBetween('created_at', [$dateFrom, $dateTo])
                                        ->count();
        
        $nextStageOpportunities = $this->getNextStageOpportunities($stage, $dateFrom, $dateTo);
        
        return $stageOpportunities > 0 ? round(($nextStageOpportunities / $stageOpportunities) * 100, 2) : 0;
    }

    protected function getNextStageOpportunities(string $currentStage, Carbon $dateFrom, Carbon $dateTo): int
    {
        $stageProgression = [
            'lead' => 'qualified',
            'qualified' => 'proposal',
            'proposal' => 'negotiation',
            'negotiation' => 'contract',
            'contract' => 'won',
        ];

        $nextStage = $stageProgression[$currentStage] ?? null;
        
        if (!$nextStage) {
            return 0;
        }

        return Opportunity::where('stage', $nextStage)
                         ->whereBetween('updated_at', [$dateFrom, $dateTo])
                         ->count();
    }

    protected function calculateCampaignROI(MarketingCampaign $campaign): float
    {
        $revenue = $campaign->generatedOpportunities()->where('stage', 'won')->sum('value');
        $cost = $campaign->budget ?? 0;
        
        return $cost > 0 ? round((($revenue - $cost) / $cost) * 100, 2) : 0;
    }

    protected function calculateSurveyResponseRate(Carbon $dateFrom, Carbon $dateTo): float
    {
        $totalSent = CustomerSatisfactionSurvey::whereBetween('sent_at', [$dateFrom, $dateTo])->count();
        $totalCompleted = CustomerSatisfactionSurvey::completed()
                                                   ->whereBetween('completed_at', [$dateFrom, $dateTo])
                                                   ->count();
        
        return $totalSent > 0 ? round(($totalCompleted / $totalSent) * 100, 2) : 0;
    }

    protected function calculateNPSScore($surveys): float
    {
        $npsScores = $surveys->whereNotNull('nps_score');
        $total = $npsScores->count();
        
        if ($total == 0) {
            return 0;
        }

        $promoters = $npsScores->where('nps_score', '>=', 9)->count();
        $detractors = $npsScores->where('nps_score', '<=', 6)->count();
        
        return round((($promoters - $detractors) / $total) * 100, 1);
    }

    // دوال التصدير

    protected function exportToPDF(array $reportData): string
    {
        // تنفيذ تصدير PDF
        return 'pdf_file_path.pdf';
    }

    protected function exportToExcel(array $reportData): string
    {
        // تنفيذ تصدير Excel
        return 'excel_file_path.xlsx';
    }

    protected function exportToCSV(array $reportData): string
    {
        // تنفيذ تصدير CSV
        return 'csv_file_path.csv';
    }

    protected function exportToJSON(array $reportData): string
    {
        return json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    // دوال مساعدة إضافية (ستكون فارغة للآن)
    protected function getPerformanceTrends($from, $to): array { return []; }
    protected function getPerformanceForecasts(): array { return []; }
    protected function getPerformanceAlerts(): array { return []; }
    protected function getStrategicRecommendations(): array { return []; }
    protected function analyzeConversionFunnel($from, $to): array { return []; }
    protected function analyzeSalesRepPerformance($from, $to): array { return []; }
    protected function analyzeOpportunities($from, $to): array { return []; }
    protected function analyzeWinLoss($from, $to): array { return []; }
    protected function generateSalesForecast($from, $to): array { return []; }
    protected function analyzeLeadGeneration($from, $to): array { return []; }
    protected function analyzeChannelEffectiveness($from, $to): array { return []; }
    protected function analyzeAttribution($from, $to): array { return []; }
    protected function analyzeMarketingROI($from, $to): array { return []; }
    protected function analyzeCustomerJourney($from, $to): array { return []; }
    protected function analyzeNPSScores($from, $to): array { return []; }
    protected function analyzeCSATScores($from, $to): array { return []; }
    protected function analyzeFeedback($from, $to): array { return []; }
    protected function analyzeSatisfactionTrends($from, $to): array { return []; }
    protected function identifyImprovementAreas($from, $to): array { return []; }
    protected function generateActionItems($from, $to): array { return []; }
    protected function getCustomerOverview($from, $to): array { return []; }
    protected function analyzeCustomerSegmentation(): array { return []; }
    protected function analyzeCustomerLifecycle(): array { return []; }
    protected function analyzeCustomerValue(): array { return []; }
    protected function analyzeCustomerRetention($from, $to): array { return []; }
    protected function analyzeCustomerChurn($from, $to): array { return []; }
    protected function identifyGrowthOpportunities(): array { return []; }
    protected function getTeamOverview($from, $to): array { return []; }
    protected function analyzeIndividualPerformance($from, $to): array { return []; }
    protected function analyzeTeamActivity($from, $to): array { return []; }
    protected function analyzeProductivityMetrics($from, $to): array { return []; }
    protected function trackTeamGoals($from, $to): array { return []; }
    protected function identifyTrainingNeeds($from, $to): array { return []; }
    protected function generatePerformanceRecommendations($from, $to): array { return []; }
    protected function generateReportSection($section, $from, $to, $config): array { return []; }
}
