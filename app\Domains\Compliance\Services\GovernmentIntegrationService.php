<?php

namespace App\Domains\Compliance\Services;

use App\Domains\Compliance\Models\Country;
use App\Domains\Compliance\Models\GovernmentIntegration;
use App\Domains\Compliance\Models\GovernmentApiCall;
use App\Models\Company;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة التكاملات الحكومية المتقدمة
 * تدير جميع التكاملات مع الأنظمة الحكومية
 */
class GovernmentIntegrationService
{
    /**
     * إرسال بيانات للنظام الحكومي
     */
    public function submitToGovernment(
        string $countryCode,
        string $systemCode,
        string $operation,
        array $data,
        Company $company
    ): array {
        $integration = $this->getIntegration($countryCode, $systemCode);
        
        if (!$integration) {
            throw new \Exception("التكامل غير موجود: {$countryCode}/{$systemCode}");
        }

        if (!$integration->is_active) {
            throw new \Exception("التكامل غير نشط: {$systemCode}");
        }

        // تحضير البيانات حسب متطلبات كل دولة
        $preparedData = $this->prepareDataForCountry($countryCode, $systemCode, $operation, $data, $company);

        // التحقق من صحة البيانات
        $this->validateSubmissionData($integration, $operation, $preparedData);

        // إجراء الإرسال
        try {
            $response = $integration->makeApiCall($operation, $preparedData);
            
            // معالجة الاستجابة حسب الدولة
            $processedResponse = $this->processCountryResponse($countryCode, $systemCode, $response);
            
            // تسجيل النجاح
            $this->logSuccessfulSubmission($company, $integration, $operation, $preparedData, $processedResponse);
            
            return $processedResponse;

        } catch (\Exception $e) {
            // تسجيل الفشل
            $this->logFailedSubmission($company, $integration, $operation, $preparedData, $e);
            
            // إعادة المحاولة إذا كان مناسباً
            if ($this->shouldRetry($e, $integration)) {
                return $this->retrySubmission($integration, $operation, $preparedData);
            }
            
            throw $e;
        }
    }

    /**
     * تحضير البيانات حسب متطلبات كل دولة
     */
    protected function prepareDataForCountry(
        string $countryCode,
        string $systemCode,
        string $operation,
        array $data,
        Company $company
    ): array {
        return match ($countryCode) {
            'MA' => $this->prepareMoroccanData($systemCode, $operation, $data, $company),
            'SA' => $this->prepareSaudiData($systemCode, $operation, $data, $company),
            'AE' => $this->prepareUAEData($systemCode, $operation, $data, $company),
            'KW' => $this->prepareKuwaitData($systemCode, $operation, $data, $company),
            'QA' => $this->prepareQatarData($systemCode, $operation, $data, $company),
            'JO' => $this->prepareJordanData($systemCode, $operation, $data, $company),
            'EG' => $this->prepareEgyptData($systemCode, $operation, $data, $company),
            'TN' => $this->prepareTunisiaData($systemCode, $operation, $data, $company),
            'DZ' => $this->prepareAlgeriaData($systemCode, $operation, $data, $company),
            default => $data,
        };
    }

    /**
     * تحضير البيانات المغربية
     */
    protected function prepareMoroccanData(string $systemCode, string $operation, array $data, Company $company): array
    {
        return match ($systemCode) {
            'DGI' => $this->prepareDGIData($operation, $data, $company),
            'CNSS' => $this->prepareCNSSData($operation, $data, $company),
            'AMO' => $this->prepareAMOData($operation, $data, $company),
            default => $data,
        };
    }

    /**
     * تحضير بيانات DGI المغربية
     */
    protected function prepareDGIData(string $operation, array $data, Company $company): array
    {
        $baseData = [
            'identifiant_fiscal' => $company->tax_number,
            'raison_sociale' => $company->name,
            'ice' => $company->ice_number,
            'date_soumission' => now()->format('Y-m-d\TH:i:s'),
        ];

        return match ($operation) {
            'submit_tva_declaration' => array_merge($baseData, [
                'periode' => $data['period'],
                'chiffre_affaires_ht' => $data['revenue_excluding_tax'],
                'tva_collectee' => $data['output_vat'],
                'tva_deductible' => $data['input_vat'],
                'tva_due' => $data['vat_due'],
                'details_operations' => $data['transaction_details'] ?? [],
            ]),
            'submit_is_declaration' => array_merge($baseData, [
                'exercice' => $data['fiscal_year'],
                'resultat_comptable' => $data['accounting_result'],
                'resultat_fiscal' => $data['fiscal_result'],
                'impot_sur_societes' => $data['corporate_tax'],
                'acomptes_verses' => $data['advance_payments'] ?? 0,
            ]),
            default => array_merge($baseData, $data),
        };
    }

    /**
     * تحضير البيانات السعودية
     */
    protected function prepareSaudiData(string $systemCode, string $operation, array $data, Company $company): array
    {
        return match ($systemCode) {
            'ZATCA' => $this->prepareZATCAData($operation, $data, $company),
            'GOSI' => $this->prepareGOSIData($operation, $data, $company),
            default => $data,
        };
    }

    /**
     * تحضير بيانات ZATCA السعودية
     */
    protected function prepareZATCAData(string $operation, array $data, Company $company): array
    {
        $baseData = [
            'seller_info' => [
                'vat_number' => $company->vat_number,
                'cr_number' => $company->cr_number,
                'name' => $company->name_ar,
                'address' => $company->address,
            ],
            'submission_timestamp' => now()->toISOString(),
        ];

        return match ($operation) {
            'submit_e_invoice' => array_merge($baseData, [
                'invoice_type' => $data['invoice_type'] ?? 'standard',
                'invoice_number' => $data['invoice_number'],
                'issue_date' => $data['issue_date'],
                'buyer_info' => $data['buyer_info'],
                'line_items' => $data['line_items'],
                'tax_totals' => $data['tax_totals'],
                'invoice_total' => $data['invoice_total'],
                'qr_code' => $this->generateSaudiQRCode($data),
                'digital_signature' => $this->generateDigitalSignature($data),
            ]),
            'submit_vat_return' => array_merge($baseData, [
                'return_period' => $data['return_period'],
                'total_sales' => $data['total_sales'],
                'total_purchases' => $data['total_purchases'],
                'output_vat' => $data['output_vat'],
                'input_vat' => $data['input_vat'],
                'net_vat' => $data['net_vat'],
            ]),
            default => array_merge($baseData, $data),
        };
    }

    /**
     * تحضير البيانات الإماراتية
     */
    protected function prepareUAEData(string $systemCode, string $operation, array $data, Company $company): array
    {
        return match ($systemCode) {
            'FTA' => $this->prepareFTAData($operation, $data, $company),
            'SSA' => $this->prepareSSAData($operation, $data, $company),
            default => $data,
        };
    }

    /**
     * تحضير بيانات FTA الإماراتية
     */
    protected function prepareFTAData(string $operation, array $data, Company $company): array
    {
        $baseData = [
            'trn' => $company->trn_number,
            'trade_license' => $company->trade_license,
            'company_name' => $company->name,
            'submission_date' => now()->format('Y-m-d'),
        ];

        return match ($operation) {
            'submit_vat_return' => array_merge($baseData, [
                'tax_period' => $data['tax_period'],
                'standard_rated_supplies' => $data['standard_rated_supplies'] ?? 0,
                'zero_rated_supplies' => $data['zero_rated_supplies'] ?? 0,
                'exempt_supplies' => $data['exempt_supplies'] ?? 0,
                'output_vat' => $data['output_vat'] ?? 0,
                'input_vat' => $data['input_vat'] ?? 0,
                'net_vat' => $data['net_vat'] ?? 0,
            ]),
            'submit_excise_return' => array_merge($baseData, [
                'excise_period' => $data['excise_period'],
                'excisable_goods' => $data['excisable_goods'] ?? [],
                'total_excise_tax' => $data['total_excise_tax'] ?? 0,
            ]),
            default => array_merge($baseData, $data),
        };
    }

    /**
     * معالجة الاستجابة حسب الدولة
     */
    protected function processCountryResponse(string $countryCode, string $systemCode, array $response): array
    {
        return match ($countryCode) {
            'MA' => $this->processMoroccanResponse($systemCode, $response),
            'SA' => $this->processSaudiResponse($systemCode, $response),
            'AE' => $this->processUAEResponse($systemCode, $response),
            default => $response,
        };
    }

    /**
     * معالجة الاستجابة المغربية
     */
    protected function processMoroccanResponse(string $systemCode, array $response): array
    {
        $data = $response['data'] ?? [];
        
        return [
            'success' => $response['success'] ?? true,
            'reference_number' => $data['numero_reference'] ?? null,
            'status' => $data['statut'] ?? 'received',
            'message' => $data['message'] ?? 'تم الاستلام بنجاح',
            'processing_time' => $data['delai_traitement'] ?? '3-5 أيام عمل',
            'next_steps' => $data['prochaines_etapes'] ?? [],
            'original_response' => $response,
        ];
    }

    /**
     * معالجة الاستجابة السعودية
     */
    protected function processSaudiResponse(string $systemCode, array $response): array
    {
        $data = $response['data'] ?? [];
        
        return match ($systemCode) {
            'ZATCA' => [
                'success' => $response['success'] ?? true,
                'clearance_status' => $data['clearanceStatus'] ?? 'CLEARED',
                'invoice_hash' => $data['invoiceHash'] ?? null,
                'qr_code' => $data['qrCode'] ?? null,
                'validation_results' => $data['validationResults'] ?? [],
                'warnings' => $data['warningMessages'] ?? [],
                'original_response' => $response,
            ],
            default => $response,
        };
    }

    /**
     * معالجة الاستجابة الإماراتية
     */
    protected function processUAEResponse(string $systemCode, array $response): array
    {
        $data = $response['data'] ?? [];
        
        return [
            'success' => $response['success'] ?? true,
            'submission_id' => $data['submissionId'] ?? null,
            'status' => $data['status'] ?? 'submitted',
            'acknowledgment' => $data['acknowledgment'] ?? null,
            'processing_date' => $data['processingDate'] ?? null,
            'original_response' => $response,
        ];
    }

    /**
     * فحص صحة جميع التكاملات
     */
    public function checkAllIntegrationsHealth(): array
    {
        $integrations = GovernmentIntegration::active()->get();
        $healthResults = [];

        foreach ($integrations as $integration) {
            $healthCheck = $integration->checkHealth();
            $healthResults[] = [
                'country' => $integration->country->code,
                'system' => $integration->system_code,
                'status' => $healthCheck['status'],
                'response_time' => $healthCheck['response_time'] ?? 0,
                'last_check' => $healthCheck['checked_at'],
                'uptime' => $integration->calculateUptime(),
            ];
        }

        return [
            'overall_status' => $this->calculateOverallStatus($healthResults),
            'total_integrations' => count($healthResults),
            'operational_count' => count(array_filter($healthResults, fn($r) => $r['status'] === 'operational')),
            'degraded_count' => count(array_filter($healthResults, fn($r) => $r['status'] === 'degraded')),
            'outage_count' => count(array_filter($healthResults, fn($r) => in_array($r['status'], ['partial_outage', 'major_outage']))),
            'integrations' => $healthResults,
            'checked_at' => now(),
        ];
    }

    /**
     * الحصول على إحصائيات التكامل
     */
    public function getIntegrationStatistics(string $countryCode = null, int $days = 30): array
    {
        $query = GovernmentApiCall::where('created_at', '>=', now()->subDays($days));
        
        if ($countryCode) {
            $query->whereHas('governmentIntegration.country', fn($q) => $q->where('code', $countryCode));
        }

        $totalCalls = $query->count();
        $successfulCalls = $query->where('status', 'success')->count();
        $failedCalls = $query->where('status', 'failed')->count();
        $averageResponseTime = $query->where('status', 'success')->avg('response_time') ?? 0;

        return [
            'period_days' => $days,
            'total_calls' => $totalCalls,
            'successful_calls' => $successfulCalls,
            'failed_calls' => $failedCalls,
            'success_rate' => $totalCalls > 0 ? round(($successfulCalls / $totalCalls) * 100, 2) : 100,
            'failure_rate' => $totalCalls > 0 ? round(($failedCalls / $totalCalls) * 100, 2) : 0,
            'average_response_time' => round($averageResponseTime, 2),
            'calls_by_country' => $this->getCallsByCountry($days),
            'calls_by_system' => $this->getCallsBySystem($days),
            'daily_trend' => $this->getDailyCallTrend($days),
        ];
    }

    /**
     * مراقبة التكاملات وإنشاء تنبيهات
     */
    public function monitorIntegrationsAndCreateAlerts(): Collection
    {
        $alerts = collect();
        $integrations = GovernmentIntegration::active()->get();

        foreach ($integrations as $integration) {
            // فحص الصحة
            $healthCheck = $integration->checkHealth();
            
            if ($healthCheck['status'] !== 'operational') {
                $alerts->push($this->createIntegrationAlert($integration, $healthCheck));
            }

            // فحص معدل الفشل
            $failureRate = $this->calculateRecentFailureRate($integration);
            if ($failureRate > 10) {
                $alerts->push($this->createHighFailureRateAlert($integration, $failureRate));
            }

            // فحص وقت الاستجابة
            $avgResponseTime = $this->calculateRecentResponseTime($integration);
            if ($avgResponseTime > 5000) { // أكثر من 5 ثوان
                $alerts->push($this->createSlowResponseAlert($integration, $avgResponseTime));
            }
        }

        return $alerts;
    }

    // طرق مساعدة
    protected function getIntegration(string $countryCode, string $systemCode): ?GovernmentIntegration
    {
        return GovernmentIntegration::whereHas('country', fn($q) => $q->where('code', $countryCode))
            ->where('system_code', $systemCode)
            ->first();
    }

    protected function validateSubmissionData(GovernmentIntegration $integration, string $operation, array $data): void
    {
        $validationRules = $integration->validation_rules[$operation] ?? [];
        
        foreach ($validationRules as $field => $rules) {
            if ($rules['required'] && !isset($data[$field])) {
                throw new \Exception("الحقل المطلوب {$field} مفقود");
            }
        }
    }

    protected function logSuccessfulSubmission(Company $company, GovernmentIntegration $integration, string $operation, array $data, array $response): void
    {
        Log::info('Government submission successful', [
            'company_id' => $company->id,
            'integration' => $integration->system_code,
            'operation' => $operation,
            'response_time' => $response['response_time'] ?? 0,
        ]);
    }

    protected function logFailedSubmission(Company $company, GovernmentIntegration $integration, string $operation, array $data, \Exception $e): void
    {
        Log::error('Government submission failed', [
            'company_id' => $company->id,
            'integration' => $integration->system_code,
            'operation' => $operation,
            'error' => $e->getMessage(),
        ]);
    }

    protected function shouldRetry(\Exception $e, GovernmentIntegration $integration): bool
    {
        $retryConfig = $integration->retry_config ?? [];
        $retryableErrors = $retryConfig['retryable_errors'] ?? ['timeout', 'network_error'];
        
        foreach ($retryableErrors as $retryableError) {
            if (str_contains(strtolower($e->getMessage()), $retryableError)) {
                return true;
            }
        }
        
        return false;
    }

    protected function retrySubmission(GovernmentIntegration $integration, string $operation, array $data): array
    {
        $retryConfig = $integration->retry_config ?? [];
        $maxRetries = $retryConfig['max_retries'] ?? 3;
        $retryDelay = $retryConfig['retry_delay'] ?? 1000; // milliseconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                sleep($retryDelay / 1000); // Convert to seconds
                return $integration->makeApiCall($operation, $data);
            } catch (\Exception $e) {
                if ($attempt === $maxRetries) {
                    throw $e;
                }
                $retryDelay *= 2; // Exponential backoff
            }
        }
    }

    protected function generateSaudiQRCode(array $data): string
    {
        // منطق توليد QR Code للفواتير السعودية
        return base64_encode('QR_CODE_DATA');
    }

    protected function generateDigitalSignature(array $data): string
    {
        // منطق توليد التوقيع الرقمي
        return hash('sha256', json_encode($data));
    }

    protected function calculateOverallStatus(array $healthResults): string
    {
        $operationalCount = count(array_filter($healthResults, fn($r) => $r['status'] === 'operational'));
        $totalCount = count($healthResults);
        
        if ($totalCount === 0) return 'unknown';
        
        $operationalPercentage = ($operationalCount / $totalCount) * 100;
        
        return match (true) {
            $operationalPercentage === 100 => 'operational',
            $operationalPercentage >= 80 => 'degraded',
            $operationalPercentage >= 50 => 'partial_outage',
            default => 'major_outage',
        };
    }

    protected function getCallsByCountry(int $days): array
    {
        return GovernmentApiCall::where('created_at', '>=', now()->subDays($days))
            ->join('government_integrations', 'government_api_calls.government_integration_id', '=', 'government_integrations.id')
            ->join('countries', 'government_integrations.country_id', '=', 'countries.id')
            ->selectRaw('countries.code, COUNT(*) as count')
            ->groupBy('countries.code')
            ->pluck('count', 'code')
            ->toArray();
    }

    protected function getCallsBySystem(int $days): array
    {
        return GovernmentApiCall::where('created_at', '>=', now()->subDays($days))
            ->join('government_integrations', 'government_api_calls.government_integration_id', '=', 'government_integrations.id')
            ->selectRaw('government_integrations.system_code, COUNT(*) as count')
            ->groupBy('government_integrations.system_code')
            ->pluck('count', 'system_code')
            ->toArray();
    }

    protected function getDailyCallTrend(int $days): array
    {
        return GovernmentApiCall::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }

    protected function calculateRecentFailureRate(GovernmentIntegration $integration): float
    {
        $recentCalls = $integration->apiCalls()
            ->where('created_at', '>=', now()->subHours(24))
            ->count();
            
        if ($recentCalls === 0) return 0;
        
        $failedCalls = $integration->apiCalls()
            ->where('created_at', '>=', now()->subHours(24))
            ->where('status', 'failed')
            ->count();
            
        return round(($failedCalls / $recentCalls) * 100, 2);
    }

    protected function calculateRecentResponseTime(GovernmentIntegration $integration): float
    {
        return $integration->apiCalls()
            ->where('created_at', '>=', now()->subHours(24))
            ->where('status', 'success')
            ->avg('response_time') ?? 0;
    }

    protected function createIntegrationAlert(GovernmentIntegration $integration, array $healthCheck): array
    {
        return [
            'type' => 'integration_failure',
            'integration' => $integration->system_code,
            'country' => $integration->country->code,
            'status' => $healthCheck['status'],
            'message' => "مشكلة في التكامل مع {$integration->system_name}",
        ];
    }

    protected function createHighFailureRateAlert(GovernmentIntegration $integration, float $failureRate): array
    {
        return [
            'type' => 'high_failure_rate',
            'integration' => $integration->system_code,
            'country' => $integration->country->code,
            'failure_rate' => $failureRate,
            'message' => "معدل فشل عالي ({$failureRate}%) في {$integration->system_name}",
        ];
    }

    protected function createSlowResponseAlert(GovernmentIntegration $integration, float $responseTime): array
    {
        return [
            'type' => 'slow_response',
            'integration' => $integration->system_code,
            'country' => $integration->country->code,
            'response_time' => $responseTime,
            'message' => "استجابة بطيئة ({$responseTime}ms) من {$integration->system_name}",
        ];
    }

    // طرق تحضير البيانات للدول الأخرى (يمكن تطويرها لاحقاً)
    protected function prepareKuwaitData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareQatarData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareJordanData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareEgyptData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareTunisiaData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareAlgeriaData(string $systemCode, string $operation, array $data, Company $company): array { return $data; }
    protected function prepareCNSSData(string $operation, array $data, Company $company): array { return $data; }
    protected function prepareAMOData(string $operation, array $data, Company $company): array { return $data; }
    protected function prepareGOSIData(string $operation, array $data, Company $company): array { return $data; }
    protected function prepareSSAData(string $operation, array $data, Company $company): array { return $data; }
}
