<?php

namespace App\Domains\Integration\Services\Security\Firewall;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Web Application Firewall
 * 
 * Advanced WAF with OWASP Top 10 protection, custom rules,
 * rate limiting, and intelligent threat detection
 */
class WebApplicationFirewall
{
    protected array $config;
    protected array $rules;
    protected array $patterns;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'enabled' => true,
            'block_mode' => true, // false for monitor mode
            'rate_limit_window' => 60, // seconds
            'rate_limit_threshold' => 100, // requests per window
            'geo_blocking_enabled' => false,
            'blocked_countries' => [],
            'whitelist_ips' => [],
            'blacklist_ips' => [],
        ], $config);

        $this->loadRules();
        $this->loadPatterns();
    }

    /**
     * Analyze incoming request for threats
     */
    public function analyzeRequest(array $request): array
    {
        if (!$this->config['enabled']) {
            return ['action' => 'allow', 'reason' => 'WAF disabled'];
        }

        try {
            $sourceIp = $request['source_ip'] ?? 'unknown';
            $userAgent = $request['user_agent'] ?? '';
            $path = $request['path'] ?? '/';
            $method = $request['method'] ?? 'GET';
            $headers = $request['headers'] ?? [];
            $body = $request['body'] ?? '';
            $queryParams = $request['query_params'] ?? [];

            // Check IP whitelist/blacklist
            $ipCheck = $this->checkIpLists($sourceIp);
            if ($ipCheck['action'] !== 'allow') {
                return $ipCheck;
            }

            // Check rate limiting
            $rateLimitCheck = $this->checkRateLimit($sourceIp);
            if ($rateLimitCheck['action'] !== 'allow') {
                return $rateLimitCheck;
            }

            // Check geographic restrictions
            $geoCheck = $this->checkGeographicRestrictions($sourceIp);
            if ($geoCheck['action'] !== 'allow') {
                return $geoCheck;
            }

            // Run security rules
            $rulesCheck = $this->runSecurityRules($request);
            if ($rulesCheck['action'] !== 'allow') {
                return $rulesCheck;
            }

            // Check for common attack patterns
            $patternCheck = $this->checkAttackPatterns($request);
            if ($patternCheck['action'] !== 'allow') {
                return $patternCheck;
            }

            // Check request size limits
            $sizeCheck = $this->checkRequestSize($request);
            if ($sizeCheck['action'] !== 'allow') {
                return $sizeCheck;
            }

            // All checks passed
            return ['action' => 'allow', 'reason' => 'Request passed all WAF checks'];

        } catch (\Exception $e) {
            Log::error('WAF analysis failed', [
                'error' => $e->getMessage(),
                'source_ip' => $sourceIp ?? 'unknown',
            ]);

            // Fail open in case of WAF error
            return ['action' => 'allow', 'reason' => 'WAF analysis error'];
        }
    }

    /**
     * Check IP whitelist and blacklist
     */
    protected function checkIpLists(string $sourceIp): array
    {
        // Check whitelist first
        if (in_array($sourceIp, $this->config['whitelist_ips'])) {
            return ['action' => 'allow', 'reason' => 'IP whitelisted'];
        }

        // Check blacklist
        if (in_array($sourceIp, $this->config['blacklist_ips'])) {
            return [
                'action' => 'block',
                'reason' => 'IP blacklisted',
                'rule_id' => 'ip_blacklist',
                'severity' => 'high',
            ];
        }

        // Check dynamic blacklist
        if ($this->isDynamicallyBlacklisted($sourceIp)) {
            return [
                'action' => 'block',
                'reason' => 'IP dynamically blacklisted',
                'rule_id' => 'dynamic_blacklist',
                'severity' => 'high',
            ];
        }

        return ['action' => 'allow', 'reason' => 'IP check passed'];
    }

    /**
     * Check rate limiting
     */
    protected function checkRateLimit(string $sourceIp): array
    {
        $window = $this->config['rate_limit_window'];
        $threshold = $this->config['rate_limit_threshold'];
        $key = "waf_rate_limit:{$sourceIp}:" . floor(time() / $window);

        $currentCount = Cache::get($key, 0);
        Cache::put($key, $currentCount + 1, $window);

        if ($currentCount >= $threshold) {
            return [
                'action' => 'block',
                'reason' => 'Rate limit exceeded',
                'rule_id' => 'rate_limit',
                'severity' => 'medium',
                'current_count' => $currentCount,
                'threshold' => $threshold,
            ];
        }

        return ['action' => 'allow', 'reason' => 'Rate limit check passed'];
    }

    /**
     * Check geographic restrictions
     */
    protected function checkGeographicRestrictions(string $sourceIp): array
    {
        if (!$this->config['geo_blocking_enabled']) {
            return ['action' => 'allow', 'reason' => 'Geo blocking disabled'];
        }

        $country = $this->getCountryFromIp($sourceIp);
        
        if (in_array($country, $this->config['blocked_countries'])) {
            return [
                'action' => 'block',
                'reason' => 'Geographic restriction',
                'rule_id' => 'geo_block',
                'severity' => 'medium',
                'country' => $country,
            ];
        }

        return ['action' => 'allow', 'reason' => 'Geographic check passed'];
    }

    /**
     * Run security rules
     */
    protected function runSecurityRules(array $request): array
    {
        foreach ($this->rules as $rule) {
            if (!$rule['enabled']) {
                continue;
            }

            $result = $this->evaluateRule($rule, $request);
            if ($result['matched']) {
                $action = $this->config['block_mode'] ? 'block' : 'monitor';
                
                return [
                    'action' => $action,
                    'reason' => $rule['description'],
                    'rule_id' => $rule['id'],
                    'severity' => $rule['severity'],
                    'matched_value' => $result['matched_value'],
                ];
            }
        }

        return ['action' => 'allow', 'reason' => 'Security rules check passed'];
    }

    /**
     * Check for attack patterns
     */
    protected function checkAttackPatterns(array $request): array
    {
        $content = $this->extractRequestContent($request);
        
        foreach ($this->patterns as $patternType => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern['regex'], $content, $matches)) {
                    $action = $this->config['block_mode'] ? 'block' : 'monitor';
                    
                    return [
                        'action' => $action,
                        'reason' => "Attack pattern detected: {$patternType}",
                        'rule_id' => "pattern_{$patternType}",
                        'severity' => $pattern['severity'],
                        'matched_pattern' => $pattern['name'],
                        'matched_value' => $matches[0] ?? '',
                    ];
                }
            }
        }

        return ['action' => 'allow', 'reason' => 'Attack pattern check passed'];
    }

    /**
     * Check request size limits
     */
    protected function checkRequestSize(array $request): array
    {
        $maxHeaderSize = 8192; // 8KB
        $maxBodySize = 1048576; // 1MB
        $maxUrlLength = 2048; // 2KB

        $headerSize = strlen(json_encode($request['headers'] ?? []));
        $bodySize = strlen($request['body'] ?? '');
        $urlLength = strlen($request['path'] ?? '') + strlen(http_build_query($request['query_params'] ?? []));

        if ($headerSize > $maxHeaderSize) {
            return [
                'action' => 'block',
                'reason' => 'Header size limit exceeded',
                'rule_id' => 'header_size_limit',
                'severity' => 'medium',
                'size' => $headerSize,
                'limit' => $maxHeaderSize,
            ];
        }

        if ($bodySize > $maxBodySize) {
            return [
                'action' => 'block',
                'reason' => 'Body size limit exceeded',
                'rule_id' => 'body_size_limit',
                'severity' => 'medium',
                'size' => $bodySize,
                'limit' => $maxBodySize,
            ];
        }

        if ($urlLength > $maxUrlLength) {
            return [
                'action' => 'block',
                'reason' => 'URL length limit exceeded',
                'rule_id' => 'url_length_limit',
                'severity' => 'medium',
                'length' => $urlLength,
                'limit' => $maxUrlLength,
            ];
        }

        return ['action' => 'allow', 'reason' => 'Size check passed'];
    }

    /**
     * Evaluate individual security rule
     */
    protected function evaluateRule(array $rule, array $request): array
    {
        $target = $this->extractTargetValue($rule['target'], $request);
        
        switch ($rule['operator']) {
            case 'contains':
                foreach ($rule['values'] as $value) {
                    if (stripos($target, $value) !== false) {
                        return ['matched' => true, 'matched_value' => $value];
                    }
                }
                break;
                
            case 'regex':
                foreach ($rule['values'] as $pattern) {
                    if (preg_match($pattern, $target, $matches)) {
                        return ['matched' => true, 'matched_value' => $matches[0]];
                    }
                }
                break;
                
            case 'equals':
                if (in_array($target, $rule['values'])) {
                    return ['matched' => true, 'matched_value' => $target];
                }
                break;
                
            case 'length_gt':
                if (strlen($target) > $rule['values'][0]) {
                    return ['matched' => true, 'matched_value' => strlen($target)];
                }
                break;
        }

        return ['matched' => false];
    }

    /**
     * Extract target value from request
     */
    protected function extractTargetValue(string $target, array $request): string
    {
        switch ($target) {
            case 'uri':
                return $request['path'] ?? '';
            case 'query_string':
                return http_build_query($request['query_params'] ?? []);
            case 'body':
                return $request['body'] ?? '';
            case 'headers':
                return json_encode($request['headers'] ?? []);
            case 'user_agent':
                return $request['user_agent'] ?? '';
            case 'method':
                return $request['method'] ?? '';
            case 'all':
                return $this->extractRequestContent($request);
            default:
                return '';
        }
    }

    /**
     * Extract all request content for pattern matching
     */
    protected function extractRequestContent(array $request): string
    {
        $content = '';
        $content .= $request['path'] ?? '';
        $content .= ' ' . http_build_query($request['query_params'] ?? []);
        $content .= ' ' . json_encode($request['headers'] ?? []);
        $content .= ' ' . ($request['body'] ?? '');
        $content .= ' ' . ($request['user_agent'] ?? '');
        
        return strtolower($content);
    }

    /**
     * Load security rules
     */
    protected function loadRules(): void
    {
        $this->rules = [
            [
                'id' => 'sql_injection',
                'enabled' => true,
                'description' => 'SQL Injection attempt detected',
                'severity' => 'high',
                'target' => 'all',
                'operator' => 'regex',
                'values' => [
                    '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
                    '/\b(select|insert|update|delete|drop|create|alter)\b.*\b(from|into|table|database)\b/i',
                    '/(\bor\b|\band\b)\s+\d+\s*=\s*\d+/i',
                    '/\'\s*(or|and)\s*\'\w*\'\s*=\s*\'\w*\'/i',
                ],
            ],
            [
                'id' => 'xss_attack',
                'enabled' => true,
                'description' => 'Cross-Site Scripting attempt detected',
                'severity' => 'high',
                'target' => 'all',
                'operator' => 'regex',
                'values' => [
                    '/<script[^>]*>.*?<\/script>/i',
                    '/javascript\s*:/i',
                    '/on\w+\s*=\s*["\']?[^"\']*["\']?/i',
                    '/<iframe[^>]*>.*?<\/iframe>/i',
                ],
            ],
            [
                'id' => 'path_traversal',
                'enabled' => true,
                'description' => 'Path traversal attempt detected',
                'severity' => 'high',
                'target' => 'uri',
                'operator' => 'contains',
                'values' => ['../', '..\\', '%2e%2e%2f', '%2e%2e%5c'],
            ],
            [
                'id' => 'command_injection',
                'enabled' => true,
                'description' => 'Command injection attempt detected',
                'severity' => 'high',
                'target' => 'all',
                'operator' => 'regex',
                'values' => [
                    '/[;&|`$(){}[\]]/i',
                    '/\b(cat|ls|pwd|id|whoami|uname|wget|curl)\b/i',
                ],
            ],
        ];
    }

    /**
     * Load attack patterns
     */
    protected function loadPatterns(): void
    {
        $this->patterns = [
            'sql_injection' => [
                [
                    'name' => 'Union-based SQL injection',
                    'regex' => '/\bunion\b.*\bselect\b/i',
                    'severity' => 'high',
                ],
                [
                    'name' => 'Boolean-based SQL injection',
                    'regex' => '/(\bor\b|\band\b)\s+\d+\s*=\s*\d+/i',
                    'severity' => 'high',
                ],
            ],
            'xss' => [
                [
                    'name' => 'Script tag injection',
                    'regex' => '/<script[^>]*>/i',
                    'severity' => 'high',
                ],
                [
                    'name' => 'Event handler injection',
                    'regex' => '/on\w+\s*=/i',
                    'severity' => 'medium',
                ],
            ],
            'lfi' => [
                [
                    'name' => 'Local file inclusion',
                    'regex' => '/\.\.[\/\\]/i',
                    'severity' => 'high',
                ],
            ],
        ];
    }

    /**
     * Check if IP is dynamically blacklisted
     */
    protected function isDynamicallyBlacklisted(string $ip): bool
    {
        return Cache::has("waf_blacklist:{$ip}");
    }

    /**
     * Add IP to dynamic blacklist
     */
    public function blacklistIp(string $ip, int $duration = 3600): void
    {
        Cache::put("waf_blacklist:{$ip}", true, $duration);
        Log::warning('IP added to WAF blacklist', ['ip' => $ip, 'duration' => $duration]);
    }

    /**
     * Remove IP from dynamic blacklist
     */
    public function removeFromBlacklist(string $ip): void
    {
        Cache::forget("waf_blacklist:{$ip}");
        Log::info('IP removed from WAF blacklist', ['ip' => $ip]);
    }

    /**
     * Get country from IP address
     */
    protected function getCountryFromIp(string $ip): string
    {
        // Placeholder for GeoIP lookup
        return 'US'; // Default to US
    }

    /**
     * Log WAF event
     */
    public function logEvent(array $result, array $request): void
    {
        $logData = [
            'timestamp' => now(),
            'source_ip' => $request['source_ip'] ?? 'unknown',
            'user_agent' => $request['user_agent'] ?? '',
            'method' => $request['method'] ?? '',
            'path' => $request['path'] ?? '',
            'action' => $result['action'],
            'reason' => $result['reason'],
            'rule_id' => $result['rule_id'] ?? null,
            'severity' => $result['severity'] ?? 'info',
        ];

        // Log to file
        Log::channel('waf')->info('WAF Event', $logData);

        // Store in database for analysis
        DB::table('waf_events')->insert($logData);
    }

    /**
     * Get WAF statistics
     */
    public function getStatistics(Carbon $start, Carbon $end): array
    {
        $stats = DB::table('waf_events')
            ->whereBetween('timestamp', [$start, $end])
            ->selectRaw('
                COUNT(*) as total_events,
                SUM(CASE WHEN action = "block" THEN 1 ELSE 0 END) as blocked_requests,
                SUM(CASE WHEN action = "allow" THEN 1 ELSE 0 END) as allowed_requests,
                COUNT(DISTINCT source_ip) as unique_ips,
                rule_id,
                COUNT(*) as rule_triggers
            ')
            ->groupBy('rule_id')
            ->get();

        return [
            'period' => [
                'start' => $start->toISOString(),
                'end' => $end->toISOString(),
            ],
            'summary' => $stats->first(),
            'rule_statistics' => $stats->toArray(),
        ];
    }

    /**
     * Update WAF rules
     */
    public function updateRules(array $newRules): void
    {
        $this->rules = $newRules;
        Cache::put('waf_rules', $this->rules, 86400);
        Log::info('WAF rules updated', ['rule_count' => count($newRules)]);
    }

    /**
     * Enable/disable WAF
     */
    public function setEnabled(bool $enabled): void
    {
        $this->config['enabled'] = $enabled;
        Cache::put('waf_config', $this->config, 86400);
        Log::info('WAF status changed', ['enabled' => $enabled]);
    }

    /**
     * Set block mode
     */
    public function setBlockMode(bool $blockMode): void
    {
        $this->config['block_mode'] = $blockMode;
        Cache::put('waf_config', $this->config, 86400);
        Log::info('WAF mode changed', ['block_mode' => $blockMode]);
    }
}
