<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تنفيذ الأتمتة - Automation Execution
 */
class AutomationExecution extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'rule_id',
        'event_data',
        'started_at',
        'completed_at',
        'status',
        'result',
        'error',
    ];

    protected $casts = [
        'event_data' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'result' => 'array',
    ];

    public function rule(): BelongsTo
    {
        return $this->belongsTo(AutomationRule::class, 'rule_id');
    }
}
