<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج معدل الضريبة
 * يمثل معدلات الضرائب المختلفة في النظام
 */
class TaxRate extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'rate',
        'tax_type',
        'country',
        'description',
        'is_active',
        'effective_from',
        'effective_to',
        'tax_account_id',
        'metadata',
    ];

    protected $casts = [
        'rate' => 'decimal:4',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع حساب الضريبة
     */
    public function taxAccount()
    {
        return $this->belongsTo(Account::class, 'tax_account_id');
    }

    /**
     * العلاقة مع بنود الفواتير
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * حساب مبلغ الضريبة
     */
    public function calculateTax(float $amount): float
    {
        return $amount * ($this->rate / 100);
    }

    /**
     * حساب المبلغ شامل الضريبة
     */
    public function calculateInclusiveAmount(float $exclusiveAmount): float
    {
        return $exclusiveAmount + $this->calculateTax($exclusiveAmount);
    }

    /**
     * حساب المبلغ بدون ضريبة من المبلغ الشامل
     */
    public function calculateExclusiveAmount(float $inclusiveAmount): float
    {
        return $inclusiveAmount / (1 + ($this->rate / 100));
    }

    /**
     * التحقق من فعالية المعدل في تاريخ معين
     */
    public function isEffectiveOn(\Carbon\Carbon $date): bool
    {
        $effectiveFrom = $this->effective_from ?? now()->startOfYear();
        $effectiveTo = $this->effective_to ?? now()->endOfYear();

        return $date->between($effectiveFrom, $effectiveTo);
    }

    /**
     * الحصول على معدل الضريبة الافتراضي
     */
    public static function getDefault(): ?self
    {
        return self::where('is_active', true)
                   ->where('tax_type', 'VAT')
                   ->first();
    }

    /**
     * الحصول على معدل الضريبة حسب البلد
     */
    public static function getByCountry(string $country): Collection
    {
        return self::where('country', $country)
                   ->where('is_active', true)
                   ->get();
    }

    /**
     * فلترة المعدلات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('tax_type', $type);
    }

    /**
     * فلترة حسب البلد
     */
    public function scopeForCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * فلترة المعدلات الفعالة في تاريخ معين
     */
    public function scopeEffectiveOn($query, \Carbon\Carbon $date)
    {
        return $query->where(function ($q) use ($date) {
            $q->where('effective_from', '<=', $date)
              ->where(function ($subQ) use ($date) {
                  $subQ->whereNull('effective_to')
                       ->orWhere('effective_to', '>=', $date);
              });
        });
    }

    /**
     * البحث في معدلات الضريبة
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('name_ar', 'LIKE', "%{$search}%")
              ->orWhere('code', 'LIKE', "%{$search}%");
        });
    }
}
