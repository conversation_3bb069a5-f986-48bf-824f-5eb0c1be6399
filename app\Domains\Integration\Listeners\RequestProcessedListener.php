<?php

namespace App\Domains\Integration\Listeners;

use App\Domains\Integration\Events\RequestProcessed;
use App\Domains\Integration\Events\PerformanceThresholdExceeded;
use App\Domains\Integration\Services\Monitoring\RealTimeMonitor;
use App\Domains\Integration\Services\Analytics\AdvancedAnalytics;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Request Processed Listener
 * 
 * Handles post-processing tasks when an API request is processed
 */
class RequestProcessedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected RealTimeMonitor $monitor;
    protected AdvancedAnalytics $analytics;

    /**
     * Create the event listener.
     */
    public function __construct(RealTimeMonitor $monitor, AdvancedAnalytics $analytics)
    {
        $this->monitor = $monitor;
        $this->analytics = $analytics;
    }

    /**
     * Handle the event.
     */
    public function handle(RequestProcessed $event): void
    {
        try {
            // Store request metrics
            $this->storeRequestMetrics($event);

            // Update real-time monitoring
            $this->updateRealTimeMonitoring($event);

            // Check performance thresholds
            $this->checkPerformanceThresholds($event);

            // Update analytics data
            $this->updateAnalyticsData($event);

            // Update API key usage
            $this->updateApiKeyUsage($event);

            // Update endpoint statistics
            $this->updateEndpointStatistics($event);

            // Handle error tracking
            if ($event->isFailed()) {
                $this->handleErrorTracking($event);
            }

            // Update caching metrics
            $this->updateCachingMetrics($event);

            Log::debug('Request processed event handled successfully', [
                'request_id' => $event->requestId,
                'gateway_id' => $event->gateway->gateway_id,
                'processing_time' => $event->processingTime,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle request processed event', [
                'request_id' => $event->requestId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Store request metrics in database
     */
    protected function storeRequestMetrics(RequestProcessed $event): void
    {
        DB::table('api_request_logs')->insert([
            'request_id' => $event->requestId,
            'gateway_id' => $event->gateway->id,
            'endpoint_id' => $event->endpoint?->id,
            'api_key_id' => $event->apiKey?->id,
            'method' => $event->requestData['method'] ?? 'POST',
            'path' => $event->requestData['path'] ?? '/',
            'status_code' => $event->responseData['status_code'] ?? ($event->isSuccessful() ? 200 : 500),
            'processing_time' => $event->processingTime,
            'request_size' => strlen(json_encode($event->requestData)),
            'response_size' => strlen(json_encode($event->responseData)),
            'source_ip' => $event->requestData['source_ip'] ?? 'unknown',
            'user_agent' => $event->requestData['user_agent'] ?? 'unknown',
            'error_message' => $event->errorMessage,
            'metrics' => json_encode($event->metrics),
            'processed_at' => $event->processedAt,
            'created_at' => now(),
        ]);
    }

    /**
     * Update real-time monitoring metrics
     */
    protected function updateRealTimeMonitoring(RequestProcessed $event): void
    {
        $this->monitor->recordRequest(
            $event->gateway->gateway_id,
            $event->endpoint?->endpoint_id,
            $event->processingTime,
            $event->isSuccessful(),
            $event->metrics
        );

        // Update gateway metrics
        $this->updateGatewayMetrics($event);

        // Update endpoint metrics
        if ($event->endpoint) {
            $this->updateEndpointMetrics($event);
        }
    }

    /**
     * Check performance thresholds
     */
    protected function checkPerformanceThresholds(RequestProcessed $event): void
    {
        $thresholds = config('integration.performance.thresholds', []);

        // Check response time threshold
        $responseTimeThreshold = $thresholds['response_time_ms'] ?? 1000;
        if ($event->getProcessingTimeMs() > $responseTimeThreshold) {
            event(new PerformanceThresholdExceeded(
                $event->requestId,
                'response_time',
                $event->getProcessingTimeMs(),
                $responseTimeThreshold,
                $this->getSeverityLevel($event->getProcessingTimeMs(), $responseTimeThreshold),
                $event->gateway,
                $event->endpoint,
                [
                    'actual_time' => $event->getProcessingTimeMs(),
                    'threshold' => $responseTimeThreshold,
                    'endpoint' => $event->endpoint?->path,
                ]
            ));
        }

        // Check memory usage threshold
        $memoryThreshold = $thresholds['memory_mb'] ?? 128;
        $memoryUsageMb = ($event->metrics['memory_usage'] ?? 0) / 1024 / 1024;
        if ($memoryUsageMb > $memoryThreshold) {
            event(new PerformanceThresholdExceeded(
                $event->requestId,
                'memory_usage',
                $memoryUsageMb,
                $memoryThreshold,
                $this->getSeverityLevel($memoryUsageMb, $memoryThreshold),
                $event->gateway,
                $event->endpoint,
                [
                    'actual_memory' => $memoryUsageMb,
                    'threshold' => $memoryThreshold,
                ]
            ));
        }
    }

    /**
     * Update analytics data
     */
    protected function updateAnalyticsData(RequestProcessed $event): void
    {
        $this->analytics->recordRequestEvent(
            $event->gateway->gateway_id,
            $event->endpoint?->endpoint_id,
            $event->apiKey?->id,
            [
                'processing_time' => $event->processingTime,
                'status' => $event->status,
                'request_size' => strlen(json_encode($event->requestData)),
                'response_size' => strlen(json_encode($event->responseData)),
                'timestamp' => $event->processedAt,
                'metrics' => $event->metrics,
            ]
        );
    }

    /**
     * Update API key usage statistics
     */
    protected function updateApiKeyUsage(RequestProcessed $event): void
    {
        if (!$event->apiKey) {
            return;
        }

        $cacheKey = "api_key_usage:{$event->apiKey->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($cacheKey . ':requests');
        Cache::increment($cacheKey . ':total_time', (int) ($event->processingTime * 1000));
        
        if ($event->isFailed()) {
            Cache::increment($cacheKey . ':errors');
        }

        // Set expiration to 25 hours to ensure we don't lose data
        Cache::expire($cacheKey . ':requests', 90000);
        Cache::expire($cacheKey . ':total_time', 90000);
        Cache::expire($cacheKey . ':errors', 90000);
    }

    /**
     * Update endpoint statistics
     */
    protected function updateEndpointStatistics(RequestProcessed $event): void
    {
        if (!$event->endpoint) {
            return;
        }

        $cacheKey = "endpoint_stats:{$event->endpoint->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($cacheKey . ':requests');
        Cache::increment($cacheKey . ':total_time', (int) ($event->processingTime * 1000));
        
        if ($event->isFailed()) {
            Cache::increment($cacheKey . ':errors');
        }

        if ($event->isSlowRequest()) {
            Cache::increment($cacheKey . ':slow_requests');
        }

        // Set expiration
        Cache::expire($cacheKey . ':requests', 90000);
        Cache::expire($cacheKey . ':total_time', 90000);
        Cache::expire($cacheKey . ':errors', 90000);
        Cache::expire($cacheKey . ':slow_requests', 90000);
    }

    /**
     * Handle error tracking
     */
    protected function handleErrorTracking(RequestProcessed $event): void
    {
        $errorKey = "error_tracking:{$event->gateway->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($errorKey . ':count');
        
        // Store error details
        $errorDetails = [
            'request_id' => $event->requestId,
            'endpoint_id' => $event->endpoint?->id,
            'error_message' => $event->errorMessage,
            'timestamp' => $event->processedAt->toISOString(),
        ];

        $errorList = Cache::get($errorKey . ':details', []);
        $errorList[] = $errorDetails;
        
        // Keep only last 100 errors
        if (count($errorList) > 100) {
            $errorList = array_slice($errorList, -100);
        }
        
        Cache::put($errorKey . ':details', $errorList, 90000);
    }

    /**
     * Update caching metrics
     */
    protected function updateCachingMetrics(RequestProcessed $event): void
    {
        $cacheHit = $event->metrics['cache_hit'] ?? false;
        $cacheKey = "cache_metrics:{$event->gateway->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($cacheKey . ':total');
        
        if ($cacheHit) {
            Cache::increment($cacheKey . ':hits');
        } else {
            Cache::increment($cacheKey . ':misses');
        }

        Cache::expire($cacheKey . ':total', 90000);
        Cache::expire($cacheKey . ':hits', 90000);
        Cache::expire($cacheKey . ':misses', 90000);
    }

    /**
     * Update gateway metrics
     */
    protected function updateGatewayMetrics(RequestProcessed $event): void
    {
        $cacheKey = "gateway_metrics:{$event->gateway->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($cacheKey . ':requests');
        Cache::increment($cacheKey . ':total_time', (int) ($event->processingTime * 1000));
        
        if ($event->isFailed()) {
            Cache::increment($cacheKey . ':errors');
        }

        Cache::expire($cacheKey . ':requests', 90000);
        Cache::expire($cacheKey . ':total_time', 90000);
        Cache::expire($cacheKey . ':errors', 90000);
    }

    /**
     * Update endpoint metrics
     */
    protected function updateEndpointMetrics(RequestProcessed $event): void
    {
        $cacheKey = "endpoint_metrics:{$event->endpoint->id}:" . now()->format('Y-m-d-H');
        
        Cache::increment($cacheKey . ':requests');
        Cache::increment($cacheKey . ':total_time', (int) ($event->processingTime * 1000));
        
        if ($event->isFailed()) {
            Cache::increment($cacheKey . ':errors');
        }

        Cache::expire($cacheKey . ':requests', 90000);
        Cache::expire($cacheKey . ':total_time', 90000);
        Cache::expire($cacheKey . ':errors', 90000);
    }

    /**
     * Get severity level based on threshold breach
     */
    protected function getSeverityLevel(float $actual, float $threshold): string
    {
        $ratio = $actual / $threshold;
        
        if ($ratio >= 3) {
            return 'critical';
        } elseif ($ratio >= 2) {
            return 'high';
        } elseif ($ratio >= 1.5) {
            return 'medium';
        } else {
            return 'warning';
        }
    }
}
