<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج خطوة سير العمل - Workflow Step
 */
class WorkflowStep extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'workflow_id',
        'step_number',
        'name',
        'description',
        'approver_id',
        'approver_group_id',
        'approver_role',
        'escalation_approver_id',
        'is_required',
        'timeout_hours',
        'conditions',
        'settings',
    ];

    protected $casts = [
        'step_number' => 'integer',
        'is_required' => 'boolean',
        'timeout_hours' => 'integer',
        'conditions' => 'array',
        'settings' => 'array',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'workflow_id');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'approver_id');
    }

    public function escalationApprover(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'escalation_approver_id');
    }
}
