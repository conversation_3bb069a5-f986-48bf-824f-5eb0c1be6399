<?php

namespace App\Domains\Integration\Services;

use App\Domains\Integration\Models\ExternalIntegration;
use App\Domains\Integration\Models\IntegrationSyncLog;
use App\Domains\Integration\Models\IntegrationErrorLog;
use App\Domains\Integration\Contracts\IntegrationProviderInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة التكاملات الخارجية المتقدمة
 * تدير جميع التكاملات مع الأنظمة الخارجية والخدمات
 */
class ExternalIntegrationService
{
    protected array $providers = [];
    protected array $config;

    public function __construct()
    {
        $this->config = config('integration.external', []);
        $this->loadProviders();
    }

    /**
     * إنشاء تكامل جديد
     */
    public function createIntegration(array $data): ExternalIntegration
    {
        // التحقق من صحة البيانات
        $this->validateIntegrationData($data);
        
        // إنشاء معرف فريد
        $data['integration_id'] = 'INT_' . strtoupper(uniqid());
        
        // تعيين الإعدادات الافتراضية
        $data = $this->setDefaultConfigurations($data);
        
        // إنشاء التكامل
        $integration = ExternalIntegration::create($data);
        
        // اختبار الاتصال الأولي
        try {
            $testResult = $integration->testConnection();
            if (!$testResult['success']) {
                Log::warning('Integration connection test failed during creation', [
                    'integration_id' => $integration->integration_id,
                    'error' => $testResult['error'] ?? 'Unknown error',
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Integration connection test error during creation', [
                'integration_id' => $integration->integration_id,
                'error' => $e->getMessage(),
            ]);
        }
        
        // جدولة المزامنة التلقائية إذا كانت مفعلة
        if ($integration->auto_sync_enabled) {
            $integration->scheduleAutoSync();
        }
        
        return $integration;
    }

    /**
     * تحديث تكامل موجود
     */
    public function updateIntegration(ExternalIntegration $integration, array $data): ExternalIntegration
    {
        // التحقق من صحة البيانات
        $this->validateIntegrationData($data, $integration);
        
        // حفظ الإعدادات القديمة للمقارنة
        $oldConfig = $integration->toArray();
        
        // تحديث التكامل
        $integration->update($data);
        
        // اختبار الاتصال إذا تغيرت إعدادات الاتصال
        if ($this->connectionConfigChanged($oldConfig, $data)) {
            try {
                $testResult = $integration->testConnection();
                if (!$testResult['success']) {
                    Log::warning('Integration connection test failed after update', [
                        'integration_id' => $integration->integration_id,
                        'error' => $testResult['error'] ?? 'Unknown error',
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Integration connection test error after update', [
                    'integration_id' => $integration->integration_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
        
        // إعادة جدولة المزامنة التلقائية إذا تغيرت الإعدادات
        if ($integration->auto_sync_enabled && 
            ($oldConfig['sync_frequency'] !== $integration->sync_frequency || 
             $oldConfig['auto_sync_enabled'] !== $integration->auto_sync_enabled)) {
            $integration->scheduleAutoSync();
        }
        
        return $integration->fresh();
    }

    /**
     * تنفيذ مزامنة شاملة
     */
    public function performSync(ExternalIntegration $integration, array $options = []): array
    {
        Log::info('Starting integration sync', [
            'integration_id' => $integration->integration_id,
            'provider' => $integration->provider,
            'sync_type' => $options['sync_type'] ?? 'full',
        ]);
        
        try {
            // التحقق من حالة التكامل
            if (!$integration->is_active) {
                throw new \Exception('Integration is not active');
            }
            
            // التحقق من صحة الاتصال
            $healthCheck = $integration->performHealthCheck();
            if ($healthCheck['connection']['success'] !== true) {
                throw new \Exception('Integration health check failed');
            }
            
            // الحصول على مقدم الخدمة
            $provider = $this->getProvider($integration->provider);
            
            // تنفيذ المزامنة
            $result = $provider->sync($integration, $options);
            
            // معالجة النتائج
            $processedResult = $this->processSyncResult($integration, $result, $options);
            
            Log::info('Integration sync completed successfully', [
                'integration_id' => $integration->integration_id,
                'records_processed' => $processedResult['records_processed'] ?? 0,
                'records_success' => $processedResult['records_success'] ?? 0,
                'records_failed' => $processedResult['records_failed'] ?? 0,
            ]);
            
            return $processedResult;
            
        } catch (\Exception $e) {
            Log::error('Integration sync failed', [
                'integration_id' => $integration->integration_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // تسجيل الخطأ
            $this->logSyncError($integration, $e, $options);
            
            throw $e;
        }
    }

    /**
     * تنفيذ مزامنة تلقائية مجدولة
     */
    public function performScheduledSync(ExternalIntegration $integration): array
    {
        if (!$integration->auto_sync_enabled) {
            throw new \Exception('Auto sync is not enabled for this integration');
        }
        
        if ($integration->next_sync_at && $integration->next_sync_at->isFuture()) {
            throw new \Exception('Sync is not due yet');
        }
        
        $options = [
            'sync_type' => 'scheduled',
            'auto_sync' => true,
        ];
        
        $result = $this->performSync($integration, $options);
        
        // جدولة المزامنة التالية
        $integration->scheduleAutoSync();
        
        return $result;
    }

    /**
     * إرسال webhook
     */
    public function sendWebhook(ExternalIntegration $integration, string $event, array $data): array
    {
        Log::info('Sending webhook', [
            'integration_id' => $integration->integration_id,
            'event' => $event,
            'data_size' => strlen(json_encode($data)),
        ]);
        
        try {
            $result = $integration->sendWebhook($event, $data);
            
            Log::info('Webhook sent successfully', [
                'integration_id' => $integration->integration_id,
                'event' => $event,
                'results' => $result,
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Webhook sending failed', [
                'integration_id' => $integration->integration_id,
                'event' => $event,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * معالجة webhook واردة
     */
    public function processIncomingWebhook(string $provider, array $payload, array $headers = []): array
    {
        Log::info('Processing incoming webhook', [
            'provider' => $provider,
            'payload_size' => strlen(json_encode($payload)),
            'headers' => array_keys($headers),
        ]);
        
        try {
            // العثور على التكامل
            $integration = ExternalIntegration::where('provider', $provider)
                                            ->where('is_active', true)
                                            ->first();
            
            if (!$integration) {
                throw new \Exception("No active integration found for provider: {$provider}");
            }
            
            // التحقق من التوقيع إذا كان مطلوباً
            if ($integration->webhook_config['verify_signature'] ?? false) {
                $this->verifyWebhookSignature($integration, $payload, $headers);
            }
            
            // الحصول على مقدم الخدمة
            $providerInstance = $this->getProvider($provider);
            
            // معالجة الـ webhook
            $result = $providerInstance->processWebhook($integration, $payload, $headers);
            
            Log::info('Incoming webhook processed successfully', [
                'integration_id' => $integration->integration_id,
                'provider' => $provider,
                'result' => $result,
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Incoming webhook processing failed', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * تحويل البيانات بين الأنظمة
     */
    public function transformData(ExternalIntegration $integration, array $data, string $direction = 'inbound'): array
    {
        try {
            // تطبيق تعيين الحقول
            $mappedData = $integration->mapFields($data, $direction);
            
            // تطبيق قواعد التحويل
            $transformedData = $integration->transformData($mappedData, $direction);
            
            // الحصول على مقدم الخدمة للتحويلات المخصصة
            $provider = $this->getProvider($integration->provider);
            
            if (method_exists($provider, 'transformData')) {
                $transformedData = $provider->transformData($integration, $transformedData, $direction);
            }
            
            return $transformedData;
            
        } catch (\Exception $e) {
            Log::error('Data transformation failed', [
                'integration_id' => $integration->integration_id,
                'direction' => $direction,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * مراقبة صحة جميع التكاملات
     */
    public function monitorAllIntegrations(): array
    {
        $integrations = ExternalIntegration::where('is_active', true)->get();
        $results = [];
        
        foreach ($integrations as $integration) {
            try {
                $healthCheck = $integration->performHealthCheck();
                $results[$integration->integration_id] = [
                    'status' => 'success',
                    'health_status' => $integration->health_status,
                    'health_check' => $healthCheck,
                ];
            } catch (\Exception $e) {
                $results[$integration->integration_id] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
                
                Log::error('Integration health check failed', [
                    'integration_id' => $integration->integration_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
        
        return $results;
    }

    /**
     * الحصول على إحصائيات شاملة
     */
    public function getIntegrationsStatistics(array $filters = []): array
    {
        $query = ExternalIntegration::query();
        
        // تطبيق المرشحات
        if (isset($filters['provider'])) {
            $query->where('provider', $filters['provider']);
        }
        
        if (isset($filters['category'])) {
            $query->where('category', $filters['category']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        $integrations = $query->get();
        
        return [
            'total_integrations' => $integrations->count(),
            'active_integrations' => $integrations->where('is_active', true)->count(),
            'healthy_integrations' => $integrations->where('health_status', 'healthy')->count(),
            'by_provider' => $integrations->groupBy('provider')->map->count(),
            'by_category' => $integrations->groupBy('category')->map->count(),
            'by_status' => $integrations->groupBy('status')->map->count(),
            'by_health_status' => $integrations->groupBy('health_status')->map->count(),
            'total_syncs' => $integrations->sum('total_syncs'),
            'successful_syncs' => $integrations->sum('successful_syncs'),
            'failed_syncs' => $integrations->sum('failed_syncs'),
            'average_uptime' => $integrations->avg('uptime_percentage'),
            'average_response_time' => $integrations->avg('average_response_time'),
        ];
    }

    /**
     * تنظيف السجلات القديمة
     */
    public function cleanupOldLogs(int $daysToKeep = 90): array
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $syncLogsDeleted = IntegrationSyncLog::where('created_at', '<', $cutoffDate)->delete();
        $errorLogsDeleted = IntegrationErrorLog::where('created_at', '<', $cutoffDate)->delete();
        
        Log::info('Integration logs cleanup completed', [
            'sync_logs_deleted' => $syncLogsDeleted,
            'error_logs_deleted' => $errorLogsDeleted,
            'cutoff_date' => $cutoffDate,
        ]);
        
        return [
            'sync_logs_deleted' => $syncLogsDeleted,
            'error_logs_deleted' => $errorLogsDeleted,
            'cutoff_date' => $cutoffDate,
        ];
    }

    /**
     * إعادة تشغيل التكامل المعطل
     */
    public function restartIntegration(ExternalIntegration $integration): array
    {
        Log::info('Restarting integration', [
            'integration_id' => $integration->integration_id,
            'current_status' => $integration->status,
        ]);
        
        try {
            // إعادة تعيين حالة الخطأ
            $integration->update([
                'status' => 'testing',
                'health_status' => 'unknown',
                'error_count' => 0,
                'last_error' => null,
            ]);
            
            // اختبار الاتصال
            $testResult = $integration->testConnection();
            
            if ($testResult['success']) {
                $integration->update([
                    'status' => 'active',
                    'health_status' => 'healthy',
                ]);
                
                // جدولة المزامنة التلقائية إذا كانت مفعلة
                if ($integration->auto_sync_enabled) {
                    $integration->scheduleAutoSync();
                }
                
                Log::info('Integration restarted successfully', [
                    'integration_id' => $integration->integration_id,
                ]);
                
                return [
                    'success' => true,
                    'message' => 'Integration restarted successfully',
                    'test_result' => $testResult,
                ];
            } else {
                $integration->update([
                    'status' => 'error',
                    'health_status' => 'unhealthy',
                ]);
                
                throw new \Exception('Connection test failed: ' . ($testResult['error'] ?? 'Unknown error'));
            }
            
        } catch (\Exception $e) {
            $integration->update([
                'status' => 'error',
                'health_status' => 'unhealthy',
                'last_error' => $e->getMessage(),
            ]);
            
            Log::error('Integration restart failed', [
                'integration_id' => $integration->integration_id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * تحميل مقدمي الخدمات
     */
    protected function loadProviders(): void
    {
        $providerClasses = [
            'shopify' => \App\Domains\Integration\Providers\ShopifyProvider::class,
            'salla' => \App\Domains\Integration\Providers\SallaProvider::class,
            'zatca_saudi' => \App\Domains\Integration\Providers\ZatcaProvider::class,
            'dgi_morocco' => \App\Domains\Integration\Providers\DgiProvider::class,
            'facebook_ads' => \App\Domains\Integration\Providers\FacebookAdsProvider::class,
            'google_ads' => \App\Domains\Integration\Providers\GoogleAdsProvider::class,
            // إضافة المزيد من المقدمين حسب الحاجة
        ];
        
        foreach ($providerClasses as $provider => $class) {
            if (class_exists($class)) {
                $this->providers[$provider] = new $class();
            }
        }
    }

    /**
     * الحصول على مقدم الخدمة
     */
    protected function getProvider(string $provider): IntegrationProviderInterface
    {
        if (!isset($this->providers[$provider])) {
            throw new \Exception("Provider not found: {$provider}");
        }
        
        return $this->providers[$provider];
    }

    /**
     * التحقق من صحة بيانات التكامل
     */
    protected function validateIntegrationData(array $data, ?ExternalIntegration $existing = null): void
    {
        $requiredFields = ['name', 'provider', 'category', 'integration_type'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \Exception("Required field missing: {$field}");
            }
        }
        
        // التحقق من صحة المقدم
        if (!isset(ExternalIntegration::PROVIDERS[$data['provider']])) {
            throw new \Exception("Invalid provider: {$data['provider']}");
        }
        
        // التحقق من صحة الفئة
        if (!isset(ExternalIntegration::CATEGORIES[$data['category']])) {
            throw new \Exception("Invalid category: {$data['category']}");
        }
        
        // التحقق من صحة نوع التكامل
        if (!isset(ExternalIntegration::INTEGRATION_TYPES[$data['integration_type']])) {
            throw new \Exception("Invalid integration type: {$data['integration_type']}");
        }
    }

    /**
     * تعيين الإعدادات الافتراضية
     */
    protected function setDefaultConfigurations(array $data): array
    {
        $defaults = [
            'status' => 'testing',
            'environment' => 'production',
            'health_status' => 'unknown',
            'sync_status' => 'idle',
            'is_active' => false,
            'auto_sync_enabled' => false,
            'sync_frequency' => 'daily',
            'rate_limit_config' => [
                'enabled' => true,
                'requests_per_minute' => 60,
                'burst_limit' => 100,
            ],
            'retry_config' => [
                'enabled' => true,
                'max_retries' => 3,
                'retry_delay' => 5,
                'exponential_backoff' => true,
            ],
            'timeout_config' => [
                'connection_timeout' => 30,
                'request_timeout' => 60,
                'webhook_timeout' => 30,
            ],
            'security_config' => [
                'verify_ssl' => true,
                'require_signature' => false,
                'encrypt_credentials' => true,
            ],
            'monitoring_config' => [
                'health_check_interval' => 300, // 5 minutes
                'alert_on_failure' => true,
                'log_all_requests' => false,
            ],
            'created_by' => auth()->id(),
        ];
        
        return array_merge($defaults, $data);
    }

    /**
     * التحقق من تغيير إعدادات الاتصال
     */
    protected function connectionConfigChanged(array $oldConfig, array $newData): bool
    {
        $connectionFields = [
            'base_url',
            'authentication_type',
            'authentication_config',
            'connection_config',
        ];
        
        foreach ($connectionFields as $field) {
            if (isset($newData[$field]) && 
                json_encode($oldConfig[$field] ?? null) !== json_encode($newData[$field])) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * معالجة نتيجة المزامنة
     */
    protected function processSyncResult(ExternalIntegration $integration, array $result, array $options): array
    {
        // تحديث إحصائيات التكامل
        $integration->increment('total_syncs');
        
        if ($result['success'] ?? false) {
            $integration->increment('successful_syncs');
        } else {
            $integration->increment('failed_syncs');
        }
        
        $integration->update(['last_sync_at' => now()]);
        
        return $result;
    }

    /**
     * تسجيل خطأ المزامنة
     */
    protected function logSyncError(ExternalIntegration $integration, \Exception $e, array $options): void
    {
        IntegrationErrorLog::create([
            'external_integration_id' => $integration->id,
            'operation' => 'sync',
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'stack_trace' => $e->getTraceAsString(),
            'context' => $options,
        ]);
    }

    /**
     * التحقق من توقيع الـ webhook
     */
    protected function verifyWebhookSignature(ExternalIntegration $integration, array $payload, array $headers): void
    {
        $webhookConfig = $integration->webhook_config ?? [];
        $secret = $webhookConfig['secret'] ?? '';
        
        if (empty($secret)) {
            throw new \Exception('Webhook secret not configured');
        }
        
        $expectedSignature = hash_hmac('sha256', json_encode($payload), $secret);
        $receivedSignature = $headers['X-Signature'] ?? $headers['X-Hub-Signature-256'] ?? '';
        
        if (!hash_equals($expectedSignature, $receivedSignature)) {
            throw new \Exception('Invalid webhook signature');
        }
    }
}
