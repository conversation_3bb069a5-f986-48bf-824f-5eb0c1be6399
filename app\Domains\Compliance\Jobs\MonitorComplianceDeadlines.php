<?php

namespace App\Domains\Compliance\Jobs;

use App\Domains\Compliance\Services\ComplianceManagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * مهمة مراقبة مواعيد الامتثال
 */
class MonitorComplianceDeadlines implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes
    public $tries = 2;

    /**
     * تنفيذ المهمة
     */
    public function handle(ComplianceManagementService $complianceService): void
    {
        try {
            Log::info('Starting compliance deadlines monitoring');

            // مراقبة المواعيد النهائية وإنشاء تنبيهات
            $alerts = $complianceService->monitorDeadlinesAndCreateAlerts();

            Log::info('Compliance deadlines monitoring completed', [
                'alerts_created' => $alerts->count(),
            ]);

            // إرسال ملخص يومي إذا كان هناك تنبيهات
            if ($alerts->isNotEmpty()) {
                $this->sendDailySummary($alerts);
            }

        } catch (\Exception $e) {
            Log::error('Compliance deadlines monitoring failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * إرسال ملخص يومي
     */
    protected function sendDailySummary($alerts): void
    {
        // منطق إرسال الملخص اليومي
        Log::info('Daily compliance summary sent', [
            'total_alerts' => $alerts->count(),
            'critical_alerts' => $alerts->where('severity', 'critical')->count(),
        ]);
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Compliance deadlines monitoring job failed permanently', [
            'error' => $exception->getMessage(),
        ]);
    }
}
