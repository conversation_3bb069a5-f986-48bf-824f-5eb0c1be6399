<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج تعليق المشكلة - Issue Comment
 */
class IssueComment extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'issue_id',
        'user_id',
        'comment',
        'comment_type',
        'is_internal',
        'attachments',
    ];

    protected $casts = [
        'is_internal' => 'boolean',
        'attachments' => 'array',
    ];

    public function issue(): BelongsTo
    {
        return $this->belongsTo(ProjectIssue::class, 'issue_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }
}
