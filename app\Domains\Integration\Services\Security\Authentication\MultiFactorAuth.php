<?php

namespace App\Domains\Integration\Services\Security\Authentication;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

/**
 * Multi-Factor Authentication Service
 * 
 * Provides comprehensive MFA capabilities including TOTP, SMS, Email,
 * Hardware tokens, and biometric authentication
 */
class MultiFactorAuth
{
    protected array $config;
    protected array $providers;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'totp_window' => 30, // seconds
            'totp_digits' => 6,
            'sms_timeout' => 300, // 5 minutes
            'email_timeout' => 600, // 10 minutes
            'backup_codes_count' => 10,
            'max_attempts' => 3,
            'lockout_duration' => 900, // 15 minutes
        ], $config);

        $this->initializeProviders();
    }

    /**
     * Generate MFA challenge for user
     */
    public function generateChallenge(string $userId, array $methods = []): array
    {
        try {
            $availableMethods = $this->getAvailableMethods($userId);
            $requestedMethods = empty($methods) ? $availableMethods : array_intersect($methods, $availableMethods);

            if (empty($requestedMethods)) {
                throw new \Exception('No MFA methods available for user');
            }

            $challenges = [];
            $challengeId = uniqid('mfa_');

            foreach ($requestedMethods as $method) {
                $challenge = $this->generateMethodChallenge($userId, $method, $challengeId);
                if ($challenge) {
                    $challenges[$method] = $challenge;
                }
            }

            // Store challenge session
            $this->storeChallengeSession($challengeId, $userId, $challenges);

            return [
                'challenge_id' => $challengeId,
                'methods' => array_keys($challenges),
                'challenges' => $challenges,
                'expires_at' => now()->addMinutes(10)->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error('MFA challenge generation failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Verify MFA response
     */
    public function verifyChallenge(string $challengeId, string $method, string $response): array
    {
        try {
            $session = $this->getChallengeSession($challengeId);
            if (!$session) {
                throw new \Exception('Invalid or expired challenge');
            }

            // Check attempt limits
            if ($this->isUserLockedOut($session['user_id'])) {
                throw new \Exception('User is locked out due to too many failed attempts');
            }

            // Verify the response
            $isValid = $this->verifyMethodResponse($session['user_id'], $method, $response, $session);

            if ($isValid) {
                $this->clearFailedAttempts($session['user_id']);
                $this->invalidateChallengeSession($challengeId);
                
                return [
                    'success' => true,
                    'user_id' => $session['user_id'],
                    'method' => $method,
                    'verified_at' => now()->toISOString(),
                ];
            } else {
                $this->recordFailedAttempt($session['user_id'], $method);
                
                return [
                    'success' => false,
                    'error' => 'Invalid verification code',
                    'attempts_remaining' => $this->getRemainingAttempts($session['user_id']),
                ];
            }

        } catch (\Exception $e) {
            Log::error('MFA verification failed', [
                'challenge_id' => $challengeId,
                'method' => $method,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Setup MFA for user
     */
    public function setupMFA(string $userId, string $method, array $config = []): array
    {
        try {
            switch ($method) {
                case 'totp':
                    return $this->setupTOTP($userId, $config);
                case 'sms':
                    return $this->setupSMS($userId, $config);
                case 'email':
                    return $this->setupEmail($userId, $config);
                case 'backup_codes':
                    return $this->setupBackupCodes($userId);
                default:
                    throw new \Exception("Unsupported MFA method: {$method}");
            }
        } catch (\Exception $e) {
            Log::error('MFA setup failed', [
                'user_id' => $userId,
                'method' => $method,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Setup TOTP (Time-based One-Time Password)
     */
    protected function setupTOTP(string $userId, array $config): array
    {
        $secret = $this->generateTOTPSecret();
        $qrCode = $this->generateTOTPQRCode($userId, $secret, $config);

        // Store secret temporarily until verified
        Cache::put("totp_setup:{$userId}", [
            'secret' => $secret,
            'created_at' => now(),
        ], 600); // 10 minutes

        return [
            'method' => 'totp',
            'secret' => $secret,
            'qr_code' => $qrCode,
            'backup_codes' => $this->generateBackupCodes($userId),
            'instructions' => 'Scan the QR code with your authenticator app and enter the verification code',
        ];
    }

    /**
     * Setup SMS authentication
     */
    protected function setupSMS(string $userId, array $config): array
    {
        $phoneNumber = $config['phone_number'] ?? null;
        if (!$phoneNumber) {
            throw new \Exception('Phone number is required for SMS setup');
        }

        // Send verification SMS
        $code = $this->generateVerificationCode();
        $this->sendSMS($phoneNumber, "Your verification code is: {$code}");

        // Store setup data temporarily
        Cache::put("sms_setup:{$userId}", [
            'phone_number' => $phoneNumber,
            'verification_code' => $code,
            'created_at' => now(),
        ], 300); // 5 minutes

        return [
            'method' => 'sms',
            'phone_number' => $this->maskPhoneNumber($phoneNumber),
            'instructions' => 'Enter the verification code sent to your phone',
        ];
    }

    /**
     * Setup Email authentication
     */
    protected function setupEmail(string $userId, array $config): array
    {
        $email = $config['email'] ?? null;
        if (!$email) {
            throw new \Exception('Email address is required for email setup');
        }

        // Send verification email
        $code = $this->generateVerificationCode();
        $this->sendEmail($email, 'MFA Setup Verification', "Your verification code is: {$code}");

        // Store setup data temporarily
        Cache::put("email_setup:{$userId}", [
            'email' => $email,
            'verification_code' => $code,
            'created_at' => now(),
        ], 600); // 10 minutes

        return [
            'method' => 'email',
            'email' => $this->maskEmail($email),
            'instructions' => 'Enter the verification code sent to your email',
        ];
    }

    /**
     * Setup backup codes
     */
    protected function setupBackupCodes(string $userId): array
    {
        $codes = $this->generateBackupCodes($userId);
        
        // Store backup codes
        DB::table('user_mfa_backup_codes')->where('user_id', $userId)->delete();
        
        foreach ($codes as $code) {
            DB::table('user_mfa_backup_codes')->insert([
                'user_id' => $userId,
                'code' => Hash::make($code),
                'used' => false,
                'created_at' => now(),
            ]);
        }

        return [
            'method' => 'backup_codes',
            'codes' => $codes,
            'instructions' => 'Save these backup codes in a secure location. Each code can only be used once.',
        ];
    }

    /**
     * Get available MFA methods for user
     */
    protected function getAvailableMethods(string $userId): array
    {
        $methods = [];
        
        $userMFA = DB::table('user_mfa_settings')->where('user_id', $userId)->get();
        
        foreach ($userMFA as $setting) {
            if ($setting->is_active) {
                $methods[] = $setting->method;
            }
        }

        return $methods;
    }

    /**
     * Generate method-specific challenge
     */
    protected function generateMethodChallenge(string $userId, string $method, string $challengeId): ?array
    {
        switch ($method) {
            case 'totp':
                return ['type' => 'totp', 'message' => 'Enter code from your authenticator app'];
                
            case 'sms':
                $phoneNumber = $this->getUserPhoneNumber($userId);
                if ($phoneNumber) {
                    $code = $this->generateVerificationCode();
                    $this->sendSMS($phoneNumber, "Your verification code is: {$code}");
                    
                    Cache::put("sms_challenge:{$challengeId}", $code, 300);
                    
                    return [
                        'type' => 'sms',
                        'message' => 'Enter code sent to ' . $this->maskPhoneNumber($phoneNumber),
                        'phone_number' => $this->maskPhoneNumber($phoneNumber),
                    ];
                }
                break;
                
            case 'email':
                $email = $this->getUserEmail($userId);
                if ($email) {
                    $code = $this->generateVerificationCode();
                    $this->sendEmail($email, 'Verification Code', "Your verification code is: {$code}");
                    
                    Cache::put("email_challenge:{$challengeId}", $code, 600);
                    
                    return [
                        'type' => 'email',
                        'message' => 'Enter code sent to ' . $this->maskEmail($email),
                        'email' => $this->maskEmail($email),
                    ];
                }
                break;
                
            case 'backup_codes':
                return ['type' => 'backup_code', 'message' => 'Enter one of your backup codes'];
        }

        return null;
    }

    /**
     * Verify method-specific response
     */
    protected function verifyMethodResponse(string $userId, string $method, string $response, array $session): bool
    {
        switch ($method) {
            case 'totp':
                return $this->verifyTOTP($userId, $response);
                
            case 'sms':
                $expectedCode = Cache::get("sms_challenge:{$session['challenge_id']}");
                return $expectedCode && $expectedCode === $response;
                
            case 'email':
                $expectedCode = Cache::get("email_challenge:{$session['challenge_id']}");
                return $expectedCode && $expectedCode === $response;
                
            case 'backup_codes':
                return $this->verifyBackupCode($userId, $response);
        }

        return false;
    }

    /**
     * Verify TOTP code
     */
    protected function verifyTOTP(string $userId, string $code): bool
    {
        $secret = $this->getUserTOTPSecret($userId);
        if (!$secret) {
            return false;
        }

        $timeSlice = floor(time() / $this->config['totp_window']);
        
        // Check current time slice and adjacent ones for clock drift
        for ($i = -1; $i <= 1; $i++) {
            $calculatedCode = $this->calculateTOTP($secret, $timeSlice + $i);
            if (hash_equals($calculatedCode, $code)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Verify backup code
     */
    protected function verifyBackupCode(string $userId, string $code): bool
    {
        $backupCodes = DB::table('user_mfa_backup_codes')
            ->where('user_id', $userId)
            ->where('used', false)
            ->get();

        foreach ($backupCodes as $backupCode) {
            if (Hash::check($code, $backupCode->code)) {
                // Mark code as used
                DB::table('user_mfa_backup_codes')
                    ->where('id', $backupCode->id)
                    ->update(['used' => true, 'used_at' => now()]);
                
                return true;
            }
        }

        return false;
    }

    /**
     * Initialize MFA providers
     */
    protected function initializeProviders(): void
    {
        $this->providers = [
            'sms' => new SMSProvider($this->config),
            'email' => new EmailProvider($this->config),
        ];
    }

    // Helper methods
    protected function generateTOTPSecret(): string
    {
        return base32_encode(random_bytes(20));
    }

    protected function generateTOTPQRCode(string $userId, string $secret, array $config): string
    {
        $issuer = $config['issuer'] ?? config('app.name');
        $label = $config['label'] ?? $userId;
        
        $url = "otpauth://totp/{$label}?secret={$secret}&issuer={$issuer}&digits={$this->config['totp_digits']}&period={$this->config['totp_window']}";
        
        // Generate QR code (placeholder - use actual QR code library)
        return base64_encode("QR_CODE_DATA_FOR: {$url}");
    }

    protected function calculateTOTP(string $secret, int $timeSlice): string
    {
        $key = base32_decode($secret);
        $time = pack('N*', 0) . pack('N*', $timeSlice);
        $hash = hash_hmac('sha1', $time, $key, true);
        $offset = ord($hash[19]) & 0xf;
        $code = (
            ((ord($hash[$offset + 0]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % pow(10, $this->config['totp_digits']);
        
        return str_pad($code, $this->config['totp_digits'], '0', STR_PAD_LEFT);
    }

    protected function generateVerificationCode(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    protected function generateBackupCodes(string $userId): array
    {
        $codes = [];
        for ($i = 0; $i < $this->config['backup_codes_count']; $i++) {
            $codes[] = strtoupper(substr(md5(uniqid() . $userId . $i), 0, 8));
        }
        return $codes;
    }

    protected function sendSMS(string $phoneNumber, string $message): void
    {
        // Placeholder for SMS sending logic
        Log::info('SMS sent', ['phone' => $this->maskPhoneNumber($phoneNumber), 'message' => $message]);
    }

    protected function sendEmail(string $email, string $subject, string $message): void
    {
        // Placeholder for email sending logic
        Log::info('Email sent', ['email' => $this->maskEmail($email), 'subject' => $subject]);
    }

    protected function maskPhoneNumber(string $phoneNumber): string
    {
        return substr($phoneNumber, 0, 3) . '***' . substr($phoneNumber, -2);
    }

    protected function maskEmail(string $email): string
    {
        $parts = explode('@', $email);
        return substr($parts[0], 0, 2) . '***@' . $parts[1];
    }

    protected function storeChallengeSession(string $challengeId, string $userId, array $challenges): void
    {
        Cache::put("mfa_challenge:{$challengeId}", [
            'user_id' => $userId,
            'challenges' => $challenges,
            'created_at' => now(),
        ], 600); // 10 minutes
    }

    protected function getChallengeSession(string $challengeId): ?array
    {
        return Cache::get("mfa_challenge:{$challengeId}");
    }

    protected function invalidateChallengeSession(string $challengeId): void
    {
        Cache::forget("mfa_challenge:{$challengeId}");
    }

    protected function isUserLockedOut(string $userId): bool
    {
        $lockout = Cache::get("mfa_lockout:{$userId}");
        return $lockout && $lockout['expires_at'] > now();
    }

    protected function recordFailedAttempt(string $userId, string $method): void
    {
        $key = "mfa_attempts:{$userId}";
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, 900); // 15 minutes
        
        if ($attempts >= $this->config['max_attempts']) {
            Cache::put("mfa_lockout:{$userId}", [
                'expires_at' => now()->addSeconds($this->config['lockout_duration']),
                'attempts' => $attempts,
            ], $this->config['lockout_duration']);
        }
    }

    protected function clearFailedAttempts(string $userId): void
    {
        Cache::forget("mfa_attempts:{$userId}");
        Cache::forget("mfa_lockout:{$userId}");
    }

    protected function getRemainingAttempts(string $userId): int
    {
        $attempts = Cache::get("mfa_attempts:{$userId}", 0);
        return max(0, $this->config['max_attempts'] - $attempts);
    }

    protected function getUserTOTPSecret(string $userId): ?string
    {
        $setting = DB::table('user_mfa_settings')
            ->where('user_id', $userId)
            ->where('method', 'totp')
            ->where('is_active', true)
            ->first();
            
        return $setting ? decrypt($setting->secret) : null;
    }

    protected function getUserPhoneNumber(string $userId): ?string
    {
        $setting = DB::table('user_mfa_settings')
            ->where('user_id', $userId)
            ->where('method', 'sms')
            ->where('is_active', true)
            ->first();
            
        return $setting ? decrypt($setting->phone_number) : null;
    }

    protected function getUserEmail(string $userId): ?string
    {
        $setting = DB::table('user_mfa_settings')
            ->where('user_id', $userId)
            ->where('method', 'email')
            ->where('is_active', true)
            ->first();
            
        return $setting ? decrypt($setting->email) : null;
    }
}

// Helper function for base32 encoding/decoding
if (!function_exists('base32_encode')) {
    function base32_encode($data) {
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $v = 0;
        $vbits = 0;
        
        for ($i = 0, $j = strlen($data); $i < $j; $i++) {
            $v <<= 8;
            $v += ord($data[$i]);
            $vbits += 8;
            
            while ($vbits >= 5) {
                $vbits -= 5;
                $output .= $alphabet[$v >> $vbits];
                $v &= ((1 << $vbits) - 1);
            }
        }
        
        if ($vbits > 0) {
            $v <<= (5 - $vbits);
            $output .= $alphabet[$v];
        }
        
        return $output;
    }
}

if (!function_exists('base32_decode')) {
    function base32_decode($data) {
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $v = 0;
        $vbits = 0;
        
        for ($i = 0, $j = strlen($data); $i < $j; $i++) {
            $v <<= 5;
            $v += strpos($alphabet, $data[$i]);
            $vbits += 5;
            
            if ($vbits >= 8) {
                $vbits -= 8;
                $output .= chr($v >> $vbits);
                $v &= ((1 << $vbits) - 1);
            }
        }
        
        return $output;
    }
}

// Placeholder provider classes
class SMSProvider {
    public function __construct(array $config) {}
}

class EmailProvider {
    public function __construct(array $config) {}
}
