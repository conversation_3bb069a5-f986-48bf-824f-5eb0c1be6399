<?php

namespace App\Domains\Payments\Contracts;

use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\PaymentMethod;

/**
 * واجهة بوابة الدفع
 * تحدد العمليات المطلوبة لجميع معالجات بوابات الدفع
 */
interface PaymentGatewayInterface
{
    /**
     * معالجة دفعة جديدة
     *
     * @param array $paymentData بيانات الدفع
     * @return array نتيجة المعالجة
     */
    public function processPayment(array $paymentData): array;

    /**
     * معالجة استرداد الأموال
     *
     * @param PaymentTransaction $transaction المعاملة الأصلية
     * @param float $amount مبلغ الاسترداد
     * @param string|null $reason سبب الاسترداد
     * @return array نتيجة الاسترداد
     */
    public function processRefund(PaymentTransaction $transaction, float $amount, ?string $reason = null): array;

    /**
     * إنشاء طريقة دفع محفوظة (Tokenization)
     *
     * @param array $paymentMethodData بيانات طريقة الدفع
     * @return array نتيجة الإنشاء
     */
    public function createPaymentMethod(array $paymentMethodData): array;

    /**
     * حذف طريقة دفع محفوظة
     *
     * @param PaymentMethod $paymentMethod طريقة الدفع
     * @return array نتيجة الحذف
     */
    public function deletePaymentMethod(PaymentMethod $paymentMethod): array;

    /**
     * التحقق من حالة المعاملة
     *
     * @param string $transactionId معرف المعاملة في البوابة
     * @return array حالة المعاملة
     */
    public function getTransactionStatus(string $transactionId): array;

    /**
     * معالجة Webhook من البوابة
     *
     * @param array $payload بيانات Webhook
     * @param array $headers رؤوس HTTP
     * @return array نتيجة المعالجة
     */
    public function processWebhook(array $payload, array $headers = []): array;

    /**
     * التحقق من صحة Webhook
     *
     * @param array $payload بيانات Webhook
     * @param array $headers رؤوس HTTP
     * @param string $signature التوقيع
     * @return bool صحة Webhook
     */
    public function verifyWebhook(array $payload, array $headers, string $signature): bool;

    /**
     * اختبار الاتصال مع البوابة
     *
     * @return array نتيجة الاختبار
     */
    public function testConnection(): array;

    /**
     * الحصول على رابط الدفع (للبوابات المستضافة)
     *
     * @param array $paymentData بيانات الدفع
     * @return array رابط الدفع
     */
    public function getPaymentUrl(array $paymentData): array;

    /**
     * معالجة الدفع المتكرر
     *
     * @param PaymentMethod $paymentMethod طريقة الدفع المحفوظة
     * @param array $paymentData بيانات الدفع
     * @return array نتيجة المعالجة
     */
    public function processRecurringPayment(PaymentMethod $paymentMethod, array $paymentData): array;

    /**
     * إلغاء المعاملة
     *
     * @param PaymentTransaction $transaction المعاملة
     * @return array نتيجة الإلغاء
     */
    public function cancelTransaction(PaymentTransaction $transaction): array;

    /**
     * تأكيد المعاملة (للمعاملات التي تتطلب تأكيد)
     *
     * @param PaymentTransaction $transaction المعاملة
     * @param array $confirmationData بيانات التأكيد
     * @return array نتيجة التأكيد
     */
    public function confirmTransaction(PaymentTransaction $transaction, array $confirmationData = []): array;

    /**
     * الحصول على معلومات العميل من البوابة
     *
     * @param string $customerId معرف العميل في البوابة
     * @return array معلومات العميل
     */
    public function getCustomerInfo(string $customerId): array;

    /**
     * إنشاء عميل في البوابة
     *
     * @param array $customerData بيانات العميل
     * @return array نتيجة الإنشاء
     */
    public function createCustomer(array $customerData): array;

    /**
     * تحديث معلومات العميل
     *
     * @param string $customerId معرف العميل
     * @param array $customerData البيانات المحدثة
     * @return array نتيجة التحديث
     */
    public function updateCustomer(string $customerId, array $customerData): array;

    /**
     * الحصول على طرق الدفع المحفوظة للعميل
     *
     * @param string $customerId معرف العميل
     * @return array طرق الدفع
     */
    public function getCustomerPaymentMethods(string $customerId): array;

    /**
     * الحصول على تقرير المعاملات
     *
     * @param array $filters مرشحات التقرير
     * @return array التقرير
     */
    public function getTransactionReport(array $filters = []): array;

    /**
     * الحصول على الرصيد المتاح (للمحافظ الرقمية)
     *
     * @return array معلومات الرصيد
     */
    public function getBalance(): array;

    /**
     * تحويل الأموال (للمحافظ الرقمية والتحويلات البنكية)
     *
     * @param array $transferData بيانات التحويل
     * @return array نتيجة التحويل
     */
    public function transferFunds(array $transferData): array;

    /**
     * الحصول على حدود المعاملات
     *
     * @return array الحدود
     */
    public function getTransactionLimits(): array;

    /**
     * الحصول على رسوم المعاملة
     *
     * @param float $amount المبلغ
     * @param string $currency العملة
     * @param string $paymentMethod طريقة الدفع
     * @return array الرسوم
     */
    public function calculateFees(float $amount, string $currency, string $paymentMethod): array;

    /**
     * التحقق من دعم العملة
     *
     * @param string $currency العملة
     * @return bool مدعومة أم لا
     */
    public function supportsCurrency(string $currency): bool;

    /**
     * التحقق من دعم طريقة الدفع
     *
     * @param string $paymentMethod طريقة الدفع
     * @return bool مدعومة أم لا
     */
    public function supportsPaymentMethod(string $paymentMethod): bool;

    /**
     * التحقق من دعم الدولة
     *
     * @param string $country الدولة
     * @return bool مدعومة أم لا
     */
    public function supportsCountry(string $country): bool;

    /**
     * الحصول على العمليات المدعومة
     *
     * @return array قائمة العمليات
     */
    public function getSupportedOperations(): array;

    /**
     * الحصول على معلومات البوابة
     *
     * @return array معلومات البوابة
     */
    public function getGatewayInfo(): array;

    /**
     * تحديث إعدادات البوابة
     *
     * @param array $settings الإعدادات الجديدة
     * @return array نتيجة التحديث
     */
    public function updateSettings(array $settings): array;

    /**
     * الحصول على سجل المعاملات
     *
     * @param array $filters المرشحات
     * @return array السجل
     */
    public function getTransactionLog(array $filters = []): array;

    /**
     * إنشاء رابط دفع للفاتورة
     *
     * @param array $invoiceData بيانات الفاتورة
     * @return array رابط الدفع
     */
    public function createInvoicePaymentLink(array $invoiceData): array;

    /**
     * معالجة دفع بالتقسيط
     *
     * @param array $installmentData بيانات التقسيط
     * @return array نتيجة المعالجة
     */
    public function processInstallmentPayment(array $installmentData): array;

    /**
     * التحقق من هوية العميل (KYC)
     *
     * @param array $customerData بيانات العميل
     * @return array نتيجة التحقق
     */
    public function verifyCustomerIdentity(array $customerData): array;

    /**
     * إنشاء محفظة رقمية
     *
     * @param array $walletData بيانات المحفظة
     * @return array نتيجة الإنشاء
     */
    public function createDigitalWallet(array $walletData): array;

    /**
     * شحن المحفظة الرقمية
     *
     * @param string $walletId معرف المحفظة
     * @param float $amount المبلغ
     * @param array $sourceData مصدر الشحن
     * @return array نتيجة الشحن
     */
    public function topUpWallet(string $walletId, float $amount, array $sourceData): array;

    /**
     * سحب من المحفظة الرقمية
     *
     * @param string $walletId معرف المحفظة
     * @param float $amount المبلغ
     * @param array $destinationData وجهة السحب
     * @return array نتيجة السحب
     */
    public function withdrawFromWallet(string $walletId, float $amount, array $destinationData): array;

    /**
     * الحصول على تاريخ المحفظة
     *
     * @param string $walletId معرف المحفظة
     * @param array $filters المرشحات
     * @return array التاريخ
     */
    public function getWalletHistory(string $walletId, array $filters = []): array;

    /**
     * إنشاء رمز QR للدفع
     *
     * @param array $paymentData بيانات الدفع
     * @return array رمز QR
     */
    public function generateQRCode(array $paymentData): array;

    /**
     * معالجة دفع عبر رمز QR
     *
     * @param string $qrCode رمز QR
     * @param array $paymentData بيانات الدفع
     * @return array نتيجة المعالجة
     */
    public function processQRPayment(string $qrCode, array $paymentData): array;

    /**
     * إنشاء رابط دفع للمشاركة
     *
     * @param array $paymentData بيانات الدفع
     * @return array رابط الدفع
     */
    public function createPaymentLink(array $paymentData): array;

    /**
     * الحصول على إحصائيات البوابة
     *
     * @param array $filters المرشحات
     * @return array الإحصائيات
     */
    public function getGatewayStatistics(array $filters = []): array;

    /**
     * تصدير تقرير المعاملات
     *
     * @param array $filters المرشحات
     * @param string $format تنسيق التصدير
     * @return array ملف التقرير
     */
    public function exportTransactionReport(array $filters = [], string $format = 'csv'): array;

    /**
     * جدولة دفعة متكررة
     *
     * @param array $scheduleData بيانات الجدولة
     * @return array نتيجة الجدولة
     */
    public function scheduleRecurringPayment(array $scheduleData): array;

    /**
     * إلغاء دفعة متكررة مجدولة
     *
     * @param string $scheduleId معرف الجدولة
     * @return array نتيجة الإلغاء
     */
    public function cancelRecurringPayment(string $scheduleId): array;

    /**
     * تحديث جدولة دفعة متكررة
     *
     * @param string $scheduleId معرف الجدولة
     * @param array $updateData البيانات المحدثة
     * @return array نتيجة التحديث
     */
    public function updateRecurringPayment(string $scheduleId, array $updateData): array;

    /**
     * الحصول على قائمة الدفعات المتكررة
     *
     * @param array $filters المرشحات
     * @return array قائمة الدفعات
     */
    public function getRecurringPayments(array $filters = []): array;
}
