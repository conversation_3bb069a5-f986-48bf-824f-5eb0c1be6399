<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\CustomerInteraction;
use App\Domains\CRM\Models\Contact;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * خدمة CRM المتقدمة - Advanced CRM Service
 * الخدمة الرئيسية لإدارة العملاء والعلاقات
 */
class AdvancedCRMService
{
    protected CustomerSegmentationService $segmentationService;
    protected SalesAutomationService $salesAutomationService;
    protected MarketingAutomationService $marketingAutomationService;
    protected CustomerAnalyticsService $analyticsService;

    public function __construct(
        CustomerSegmentationService $segmentationService,
        SalesAutomationService $salesAutomationService,
        MarketingAutomationService $marketingAutomationService,
        CustomerAnalyticsService $analyticsService
    ) {
        $this->segmentationService = $segmentationService;
        $this->salesAutomationService = $salesAutomationService;
        $this->marketingAutomationService = $marketingAutomationService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * إنشاء ملف عميل 360° شامل
     */
    public function createCustomer360Profile(int $customerId): array
    {
        $customer = Customer::with([
            'industry',
            'assignedSalesRep',
            'contacts',
            'opportunities',
            'interactions',
            'appointments',
            'campaigns',
            'invoices',
            'projects',
            'supportTickets',
            'ecommerceOrders',
            'segments',
            'satisfactionSurveys'
        ])->findOrFail($customerId);

        return [
            'basic_info' => $this->getBasicCustomerInfo($customer),
            'contact_information' => $this->getContactInformation($customer),
            'financial_profile' => $this->getFinancialProfile($customer),
            'interaction_history' => $this->getInteractionHistory($customer),
            'sales_pipeline' => $this->getSalesPipeline($customer),
            'marketing_engagement' => $this->getMarketingEngagement($customer),
            'support_history' => $this->getSupportHistory($customer),
            'project_history' => $this->getProjectHistory($customer),
            'satisfaction_metrics' => $this->getSatisfactionMetrics($customer),
            'behavioral_insights' => $this->getBehavioralInsights($customer),
            'ai_recommendations' => $this->getAIRecommendations($customer),
            'risk_assessment' => $this->getRiskAssessment($customer),
            'growth_opportunities' => $this->getGrowthOpportunities($customer),
            'communication_preferences' => $this->getCommunicationPreferences($customer),
            'timeline' => $this->getCustomerTimeline($customer),
        ];
    }

    /**
     * إدارة دورة حياة العميل
     */
    public function manageCustomerLifecycle(Customer $customer): array
    {
        $currentStage = $this->determineLifecycleStage($customer);
        $recommendations = $this->getLifecycleRecommendations($customer, $currentStage);
        
        // تنفيذ الإجراءات التلقائية
        $automatedActions = $this->executeLifecycleActions($customer, $currentStage);

        return [
            'current_stage' => $currentStage,
            'stage_duration' => $this->getStageDuration($customer, $currentStage),
            'next_stage' => $this->getNextStage($currentStage),
            'recommendations' => $recommendations,
            'automated_actions' => $automatedActions,
            'health_score' => $this->calculateCustomerHealthScore($customer),
            'churn_risk' => $this->calculateChurnRisk($customer),
            'expansion_potential' => $this->calculateExpansionPotential($customer),
        ];
    }

    /**
     * أتمتة المبيعات الذكية
     */
    public function automateSmartSales(array $criteria = []): array
    {
        return $this->salesAutomationService->executeSmartAutomation($criteria);
    }

    /**
     * تحليل الفرص التجارية
     */
    public function analyzeOpportunities(array $filters = []): array
    {
        $opportunities = Opportunity::with(['customer', 'assignedSalesRep', 'activities'])
                                   ->when(!empty($filters['stage']), function ($q) use ($filters) {
                                       return $q->where('stage', $filters['stage']);
                                   })
                                   ->when(!empty($filters['assigned_to']), function ($q) use ($filters) {
                                       return $q->where('assigned_to', $filters['assigned_to']);
                                   })
                                   ->when(!empty($filters['date_from']), function ($q) use ($filters) {
                                       return $q->where('created_at', '>=', $filters['date_from']);
                                   })
                                   ->get();

        return [
            'pipeline_analysis' => $this->analyzePipeline($opportunities),
            'conversion_rates' => $this->calculateConversionRates($opportunities),
            'sales_velocity' => $this->calculateSalesVelocity($opportunities),
            'win_loss_analysis' => $this->analyzeWinLoss($opportunities),
            'forecasting' => $this->generateSalesForecast($opportunities),
            'bottlenecks' => $this->identifyPipelineBottlenecks($opportunities),
            'recommendations' => $this->generateSalesRecommendations($opportunities),
        ];
    }

    /**
     * تحليل التفاعلات والسلوك
     */
    public function analyzeCustomerBehavior(Customer $customer): array
    {
        $interactions = $customer->interactions()
                                ->with(['user', 'opportunity'])
                                ->orderBy('occurred_at', 'desc')
                                ->get();

        return [
            'interaction_patterns' => $this->analyzeInteractionPatterns($interactions),
            'communication_preferences' => $this->analyzeCommunicationPreferences($interactions),
            'engagement_score' => $this->calculateEngagementScore($interactions),
            'response_patterns' => $this->analyzeResponsePatterns($interactions),
            'sentiment_analysis' => $this->analyzeSentimentTrends($interactions),
            'touchpoint_effectiveness' => $this->analyzeTouchpointEffectiveness($interactions),
            'optimal_contact_times' => $this->findOptimalContactTimes($interactions),
            'behavioral_segments' => $this->identifyBehavioralSegments($customer),
        ];
    }

    /**
     * إدارة العلاقات الذكية
     */
    public function manageSmartRelationships(): array
    {
        return [
            'relationship_health' => $this->assessRelationshipHealth(),
            'at_risk_customers' => $this->identifyAtRiskCustomers(),
            'growth_opportunities' => $this->identifyGrowthOpportunities(),
            'cross_sell_opportunities' => $this->identifyCrossSellOpportunities(),
            'upsell_opportunities' => $this->identifyUpsellOpportunities(),
            'referral_opportunities' => $this->identifyReferralOpportunities(),
            'retention_strategies' => $this->generateRetentionStrategies(),
            'expansion_strategies' => $this->generateExpansionStrategies(),
        ];
    }

    /**
     * تحليل الأداء والمقاييس
     */
    public function getPerformanceMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        return [
            'customer_metrics' => $this->getCustomerMetrics($dateFrom, $dateTo),
            'sales_metrics' => $this->getSalesMetrics($dateFrom, $dateTo),
            'marketing_metrics' => $this->getMarketingMetrics($dateFrom, $dateTo),
            'service_metrics' => $this->getServiceMetrics($dateFrom, $dateTo),
            'financial_metrics' => $this->getFinancialMetrics($dateFrom, $dateTo),
            'team_performance' => $this->getTeamPerformance($dateFrom, $dateTo),
            'roi_analysis' => $this->getROIAnalysis($dateFrom, $dateTo),
            'trends' => $this->getTrends($dateFrom, $dateTo),
        ];
    }

    /**
     * التكامل مع الأنظمة الأخرى
     */
    public function syncWithExternalSystems(): array
    {
        return [
            'ecommerce_sync' => $this->syncEcommerceData(),
            'accounting_sync' => $this->syncAccountingData(),
            'support_sync' => $this->syncSupportData(),
            'marketing_sync' => $this->syncMarketingData(),
            'project_sync' => $this->syncProjectData(),
            'email_sync' => $this->syncEmailData(),
            'calendar_sync' => $this->syncCalendarData(),
        ];
    }

    // دوال مساعدة للملف الشخصي 360°
    protected function getBasicCustomerInfo(Customer $customer): array
    {
        return [
            'id' => $customer->id,
            'customer_number' => $customer->customer_number,
            'name' => $customer->full_name,
            'type' => $customer->type,
            'industry' => $customer->industry?->name,
            'status' => $customer->status,
            'tier' => $customer->tier,
            'assigned_sales_rep' => $customer->assignedSalesRep?->name,
            'created_at' => $customer->created_at,
            'last_contact_at' => $customer->last_contact_at,
            'tags' => $customer->tags,
        ];
    }

    protected function getContactInformation(Customer $customer): array
    {
        return [
            'primary_contact' => $customer->contacts()->where('is_primary', true)->first(),
            'decision_makers' => $customer->contacts()->where('is_decision_maker', true)->get(),
            'all_contacts' => $customer->contacts()->orderByImportance()->get(),
            'communication_channels' => [
                'email' => $customer->email,
                'phone' => $customer->phone,
                'mobile' => $customer->mobile,
                'whatsapp' => $customer->whatsapp,
                'website' => $customer->website,
            ],
            'address' => [
                'full_address' => $customer->full_address,
                'country' => $customer->country_code,
                'city' => $customer->city,
                'timezone' => $customer->timezone,
            ],
        ];
    }

    protected function getFinancialProfile(Customer $customer): array
    {
        return [
            'lifetime_value' => $customer->lifetime_value,
            'total_spent' => $customer->total_spent,
            'average_order_value' => $customer->average_order_value,
            'outstanding_balance' => $customer->outstanding_balance,
            'credit_limit' => $customer->credit_limit,
            'payment_terms' => $customer->payment_terms,
            'payment_history' => $this->getPaymentHistory($customer),
            'revenue_trend' => $this->getRevenueTrend($customer),
            'profitability' => $this->calculateProfitability($customer),
        ];
    }

    protected function getInteractionHistory(Customer $customer): array
    {
        $interactions = $customer->interactions()
                               ->with(['user', 'opportunity'])
                               ->latest('occurred_at')
                               ->limit(50)
                               ->get();

        return [
            'total_interactions' => $customer->interactions()->count(),
            'recent_interactions' => $interactions,
            'interaction_summary' => $this->summarizeInteractions($interactions),
            'communication_frequency' => $this->calculateCommunicationFrequency($customer),
            'preferred_channels' => $this->getPreferredChannels($interactions),
            'response_rates' => $this->calculateResponseRates($interactions),
        ];
    }

    protected function getSalesPipeline(Customer $customer): array
    {
        $opportunities = $customer->opportunities()->with(['assignedSalesRep', 'activities'])->get();

        return [
            'open_opportunities' => $opportunities->where('is_open', true),
            'won_opportunities' => $opportunities->where('is_won', true),
            'lost_opportunities' => $opportunities->where('is_lost', true),
            'total_pipeline_value' => $opportunities->where('is_open', true)->sum('value'),
            'weighted_pipeline_value' => $opportunities->where('is_open', true)->sum('weighted_value'),
            'average_deal_size' => $opportunities->avg('value'),
            'sales_cycle_length' => $this->calculateAverageSalesCycle($opportunities),
            'win_rate' => $this->calculateWinRate($opportunities),
        ];
    }

    protected function getMarketingEngagement(Customer $customer): array
    {
        $campaigns = $customer->campaigns()->with('pivot')->get();

        return [
            'campaigns_received' => $campaigns->count(),
            'email_engagement' => $this->calculateEmailEngagement($campaigns),
            'campaign_responses' => $this->getCampaignResponses($campaigns),
            'marketing_qualified_leads' => $this->getMarketingQualifiedLeads($customer),
            'content_engagement' => $this->getContentEngagement($customer),
            'social_engagement' => $this->getSocialEngagement($customer),
        ];
    }

    protected function getSupportHistory(Customer $customer): array
    {
        $tickets = $customer->supportTickets()->with(['assignedAgent', 'category'])->get();

        return [
            'total_tickets' => $tickets->count(),
            'open_tickets' => $tickets->whereNotIn('status', ['resolved', 'closed'])->count(),
            'average_resolution_time' => $tickets->where('status', 'resolved')->avg('resolution_time_hours'),
            'satisfaction_score' => $tickets->whereNotNull('satisfaction_rating')->avg('satisfaction_rating'),
            'common_issues' => $this->getCommonIssues($tickets),
            'escalation_rate' => $this->calculateEscalationRate($tickets),
        ];
    }

    protected function getBehavioralInsights(Customer $customer): array
    {
        return [
            'purchase_behavior' => $this->analyzePurchaseBehavior($customer),
            'engagement_patterns' => $this->analyzeEngagementPatterns($customer),
            'seasonal_trends' => $this->analyzeSeasonalTrends($customer),
            'product_preferences' => $this->analyzeProductPreferences($customer),
            'channel_preferences' => $this->analyzeChannelPreferences($customer),
            'timing_preferences' => $this->analyzeTimingPreferences($customer),
        ];
    }

    protected function getAIRecommendations(Customer $customer): array
    {
        return [
            'next_best_action' => $this->getNextBestAction($customer),
            'product_recommendations' => $this->getProductRecommendations($customer),
            'optimal_contact_strategy' => $this->getOptimalContactStrategy($customer),
            'retention_actions' => $this->getRetentionActions($customer),
            'upsell_opportunities' => $this->getUpsellOpportunities($customer),
            'cross_sell_opportunities' => $this->getCrossSellOpportunities($customer),
        ];
    }

    // دوال تحليل دورة الحياة
    protected function determineLifecycleStage(Customer $customer): string
    {
        $daysSinceCreation = $customer->created_at->diffInDays(now());
        $totalSpent = $customer->total_spent;
        $lastPurchase = $customer->last_purchase_at;
        $interactionFrequency = $customer->interactions()->count();

        if ($daysSinceCreation <= 30 && $totalSpent == 0) {
            return 'prospect';
        } elseif ($totalSpent > 0 && $daysSinceCreation <= 90) {
            return 'new_customer';
        } elseif ($totalSpent > 0 && $lastPurchase && $lastPurchase->diffInDays(now()) <= 180) {
            return 'active_customer';
        } elseif ($totalSpent > 0 && $customer->total_orders >= 5) {
            return 'loyal_customer';
        } elseif ($totalSpent >= 50000 || $customer->tier === 'vip') {
            return 'vip_customer';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 365) {
            return 'at_risk';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 730) {
            return 'churned';
        } else {
            return 'dormant';
        }
    }

    protected function calculateCustomerHealthScore(Customer $customer): int
    {
        $score = 0;

        // نقاط بناءً على التفاعل الأخير
        $daysSinceLastContact = $customer->days_since_last_contact;
        if ($daysSinceLastContact <= 7) $score += 25;
        elseif ($daysSinceLastContact <= 30) $score += 20;
        elseif ($daysSinceLastContact <= 90) $score += 10;

        // نقاط بناءً على الإنفاق
        if ($customer->total_spent >= 100000) $score += 25;
        elseif ($customer->total_spent >= 50000) $score += 20;
        elseif ($customer->total_spent >= 10000) $score += 15;
        elseif ($customer->total_spent > 0) $score += 10;

        // نقاط بناءً على الرضا
        $avgSatisfaction = $customer->average_satisfaction;
        if ($avgSatisfaction >= 4.5) $score += 25;
        elseif ($avgSatisfaction >= 4.0) $score += 20;
        elseif ($avgSatisfaction >= 3.5) $score += 15;
        elseif ($avgSatisfaction >= 3.0) $score += 10;

        // نقاط بناءً على الفرص المفتوحة
        $openOpportunities = $customer->open_opportunities_count;
        if ($openOpportunities >= 3) $score += 15;
        elseif ($openOpportunities >= 1) $score += 10;

        // نقاط بناءً على عدم وجود تذاكر دعم مفتوحة
        if ($customer->open_tickets_count == 0) $score += 10;

        return min(100, $score);
    }

    protected function calculateChurnRisk(Customer $customer): array
    {
        $riskFactors = [];
        $riskScore = 0;

        // عوامل الخطر
        if ($customer->days_since_last_contact > 90) {
            $riskFactors[] = 'لا يوجد تواصل منذ أكثر من 90 يوم';
            $riskScore += 30;
        }

        if ($customer->last_purchase_at && $customer->last_purchase_at->diffInDays(now()) > 180) {
            $riskFactors[] = 'لا يوجد شراء منذ أكثر من 6 أشهر';
            $riskScore += 25;
        }

        if ($customer->average_satisfaction < 3.0) {
            $riskFactors[] = 'مستوى رضا منخفض';
            $riskScore += 20;
        }

        if ($customer->open_tickets_count > 2) {
            $riskFactors[] = 'عدد كبير من تذاكر الدعم المفتوحة';
            $riskScore += 15;
        }

        $riskLevel = 'low';
        if ($riskScore >= 50) $riskLevel = 'high';
        elseif ($riskScore >= 25) $riskLevel = 'medium';

        return [
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'recommended_actions' => $this->getChurnPreventionActions($riskLevel),
        ];
    }

    // دوال مساعدة إضافية
    protected function getPaymentHistory(Customer $customer): array { return []; }
    protected function getRevenueTrend(Customer $customer): array { return []; }
    protected function calculateProfitability(Customer $customer): float { return 0; }
    protected function summarizeInteractions($interactions): array { return []; }
    protected function calculateCommunicationFrequency(Customer $customer): float { return 0; }
    protected function getPreferredChannels($interactions): array { return []; }
    protected function calculateResponseRates($interactions): array { return []; }
    protected function calculateAverageSalesCycle($opportunities): int { return 0; }
    protected function calculateWinRate($opportunities): float { return 0; }
    protected function calculateEmailEngagement($campaigns): array { return []; }
    protected function getCampaignResponses($campaigns): array { return []; }
    protected function getMarketingQualifiedLeads(Customer $customer): int { return 0; }
    protected function getContentEngagement(Customer $customer): array { return []; }
    protected function getSocialEngagement(Customer $customer): array { return []; }
    protected function getCommonIssues($tickets): array { return []; }
    protected function calculateEscalationRate($tickets): float { return 0; }
    protected function analyzePurchaseBehavior(Customer $customer): array { return []; }
    protected function analyzeEngagementPatterns(Customer $customer): array { return []; }
    protected function analyzeSeasonalTrends(Customer $customer): array { return []; }
    protected function analyzeProductPreferences(Customer $customer): array { return []; }
    protected function analyzeChannelPreferences(Customer $customer): array { return []; }
    protected function analyzeTimingPreferences(Customer $customer): array { return []; }
    protected function getNextBestAction(Customer $customer): string { return ''; }
    protected function getProductRecommendations(Customer $customer): array { return []; }
    protected function getOptimalContactStrategy(Customer $customer): array { return []; }
    protected function getRetentionActions(Customer $customer): array { return []; }
    protected function getUpsellOpportunities(Customer $customer): array { return []; }
    protected function getCrossSellOpportunities(Customer $customer): array { return []; }
    protected function getChurnPreventionActions(string $riskLevel): array { return []; }
}
