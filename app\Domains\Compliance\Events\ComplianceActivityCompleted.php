<?php

namespace App\Domains\Compliance\Events;

use App\Domains\Compliance\Models\ComplianceActivity;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إكمال نشاط الامتثال
 */
class ComplianceActivityCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public ComplianceActivity $activity,
        public array $completionData = []
    ) {}

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'activity_id' => $this->activity->id,
            'activity_title' => $this->activity->title,
            'company_id' => $this->activity->company_id,
            'country_code' => $this->activity->country->code,
            'completion_data' => $this->completionData,
            'completed_at' => $this->activity->completed_at,
            'compliance_impact' => $this->activity->calculateComplianceImpact(),
        ];
    }
}
