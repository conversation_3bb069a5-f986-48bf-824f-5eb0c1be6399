<?php

namespace App\Domains\ECommerce\Listeners;

use App\Domains\ECommerce\Events\ECommerceWebhookReceived;
use App\Domains\ECommerce\Services\ECommerceWebhookService;
use App\Domains\ECommerce\Jobs\ProcessECommerceWebhookJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * مستمع Webhooks التجارة الإلكترونية
 */
class ECommerceWebhookListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected ECommerceWebhookService $webhookService;

    /**
     * إنشاء مثيل جديد من المستمع
     */
    public function __construct(ECommerceWebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * معالجة الحدث
     */
    public function handle(ECommerceWebhookReceived $event): void
    {
        try {
            Log::info('تم استقبال webhook للتجارة الإلكترونية', [
                'integration_id' => $event->integration->id,
                'platform' => $event->integration->platform->name,
                'event_type' => $event->eventType,
                'payload_size' => strlen(json_encode($event->payload)),
            ]);

            // معالجة Webhook في الخلفية
            ProcessECommerceWebhookJob::dispatch(
                $event->integration,
                $event->eventType,
                $event->payload,
                $event->headers
            )->onQueue('webhooks');

        } catch (\Exception $e) {
            Log::error('فشل في معالجة webhook التجارة الإلكترونية', [
                'integration_id' => $event->integration->id,
                'event_type' => $event->eventType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // إعادة رمي الاستثناء للمحاولة مرة أخرى
            throw $e;
        }
    }

    /**
     * معالجة فشل المستمع
     */
    public function failed(ECommerceWebhookReceived $event, \Throwable $exception): void
    {
        Log::error('فشل نهائي في معالجة webhook التجارة الإلكترونية', [
            'integration_id' => $event->integration->id,
            'event_type' => $event->eventType,
            'error' => $exception->getMessage(),
        ]);

        // يمكن إضافة إشعارات للمطورين هنا
        // أو تسجيل الفشل في قاعدة البيانات
    }
}
