# 👥 نظام إدارة الموارد البشرية المتقدم - Advanced HR Management System

نظام شامل ومتقدم لإدارة الموارد البشرية مبني بـ Laravel يدعم جميع جوانب إدارة الموظفين مع ميزات الذكاء الاصطناعي والامتثال للقوانين المحلية.

## 🌟 الميزات الرئيسية

### 👤 إدارة الموظفين
- **ملفات شخصية شاملة**: معلومات كاملة للموظفين مع الوثائق والصور
- **الهيكل التنظيمي**: إدارة الأقسام والمناصب والتسلسل الإداري
- **دورة حياة الموظف**: من التوظيف إلى إنهاء الخدمة
- **إدارة العقود**: عقود متقدمة مع تتبع التجديد والانتهاء
- **الوثائق الذكية**: تتبع انتهاء صلاحية الوثائق مع التنبيهات

### ⏰ نظام الحضور والانصراف
- **تسجيل متعدد الطرق**: بصمة، كارت، تطبيق جوال، تعرف على الوجه
- **الورديات المرنة**: دعم أنظمة العمل المختلفة والورديات الدوارة
- **التتبع الجغرافي**: تحديد المواقع وحدود العمل
- **كشف الشذوذ**: اكتشاف تلقائي للأنماط غير العادية
- **التقارير التفصيلية**: تقارير شاملة للحضور والإنتاجية

### 🏖️ إدارة الإجازات
- **أنواع إجازات متعددة**: سنوية، مرضية، أمومة، حج، طوارئ
- **الأرصدة الذكية**: حساب تلقائي للاستحقاقات والترحيل
- **سير عمل الموافقة**: موافقات متدرجة حسب السياسات
- **التقويم التفاعلي**: عرض مرئي للإجازات والتخطيط
- **الامتثال القانوني**: دعم قوانين العمل المحلية

### 💰 نظام الرواتب
- **حساب متقدم**: رواتب، بدلات، خصومات، ضرائب
- **كشوف الرواتب**: تقارير مفصلة وقابلة للتخصيص
- **التكامل البنكي**: تحويلات مباشرة وملفات البنوك
- **الامتثال الضريبي**: حساب الضرائب والتأمينات
- **التقارير المالية**: تحليلات تكلفة العمالة

### 📊 التحليلات والتقارير
- **لوحة تحكم تفاعلية**: مؤشرات الأداء الرئيسية
- **تحليلات الموظفين**: إحصائيات شاملة ومؤشرات الاتجاهات
- **تقارير مخصصة**: منشئ تقارير مرن
- **التنبؤات الذكية**: توقعات الاحتياجات والتكاليف
- **مقارنات الأداء**: معايير الصناعة والمقارنات

## 🏗️ البنية المعمارية

```
app/Domains/HR/
├── Controllers/           # تحكم API و Web
│   ├── EmployeeController.php
│   ├── AttendanceController.php
│   ├── LeaveController.php
│   └── PayrollController.php
├── Models/               # نماذج البيانات
│   ├── Employee.php
│   ├── AttendanceRecord.php
│   ├── LeaveRequest.php
│   ├── EmployeeContract.php
│   ├── WorkShift.php
│   ├── LeaveType.php
│   └── LeaveBalance.php
├── Services/             # خدمات الأعمال
│   ├── PayrollCalculationService.php
│   ├── LeaveManagementService.php
│   ├── AttendanceManagementService.php
│   └── AdvancedHRManagementService.php
├── Repositories/         # طبقة الوصول للبيانات
├── Events/               # الأحداث
│   ├── EmployeeCreated.php
│   ├── EmployeeTerminated.php
│   ├── EmployeeCheckedIn.php
│   └── AttendanceAnomalyDetected.php
├── Listeners/            # المستمعين
├── Jobs/                 # المهام المؤجلة
│   ├── ProcessPayrollJob.php
│   └── UpdateLeaveBalancesJob.php
├── Requests/             # طلبات التحقق
│   ├── StoreEmployeeRequest.php
│   └── UpdateEmployeeRequest.php
├── Resources/            # موارد API
│   ├── EmployeeResource.php
│   └── EmployeeCollection.php
├── Policies/             # سياسات الأذونات
├── Middleware/           # الوسطيات
└── Providers/            # مزودي الخدمات
```

## 🚀 التثبيت والإعداد

### 1. تثبيت الحزمة

```bash
# إضافة Service Provider إلى config/app.php
App\Domains\HR\Providers\HRServiceProvider::class,
```

### 2. نشر الملفات

```bash
# نشر التكوين
php artisan vendor:publish --tag=hr-config

# نشر الهجرات
php artisan vendor:publish --tag=hr-migrations

# نشر العروض
php artisan vendor:publish --tag=hr-views

# نشر الترجمات
php artisan vendor:publish --tag=hr-lang
```

### 3. تشغيل الهجرات

```bash
php artisan migrate
```

### 4. إعداد المهام المجدولة

```bash
# إضافة إلى crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## ⚙️ التكوين

### ملف التكوين الأساسي

```php
// config/hr.php
return [
    'default_currency' => 'SAR',
    'default_timezone' => 'Asia/Riyadh',
    'fiscal_year_start' => '01-01',
    
    'payroll' => [
        'processing_day' => 25,
        'auto_process' => true,
        'backup_enabled' => true,
    ],
    
    'attendance' => [
        'grace_period_minutes' => 15,
        'overtime_threshold' => 8,
        'auto_clock_out_hours' => 12,
        'anomaly_detection' => true,
    ],
    
    'leave' => [
        'auto_approval_threshold' => 1,
        'carry_forward_enabled' => true,
        'max_carry_forward_days' => 30,
    ],
];
```

### متغيرات البيئة

```env
# الموارد البشرية العامة
HR_DEFAULT_CURRENCY=SAR
HR_DEFAULT_TIMEZONE=Asia/Riyadh
HR_FISCAL_YEAR_START=01-01

# الرواتب
HR_PAYROLL_AUTO_PROCESS=true
HR_PAYROLL_PROCESSING_DAY=25
HR_PAYROLL_BACKUP_ENABLED=true

# الحضور
HR_ATTENDANCE_GRACE_PERIOD=15
HR_OVERTIME_THRESHOLD=8
HR_AUTO_CLOCK_OUT=12
HR_ANOMALY_DETECTION=true

# الإجازات
HR_LEAVE_AUTO_APPROVAL_THRESHOLD=1
HR_LEAVE_CARRY_FORWARD=true
HR_MAX_CARRY_FORWARD_DAYS=30

# التكامل
HR_BIOMETRIC_ENABLED=true
HR_GPS_TRACKING_ENABLED=true
HR_FACE_RECOGNITION_ENABLED=false
```

## 📝 الاستخدام

### إنشاء موظف جديد

```php
use App\Domains\HR\Models\Employee;

$employee = Employee::create([
    'employee_number' => 'EMP-2024-0001',
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'mobile' => '+966501234567',
    'date_of_birth' => '1990-01-15',
    'hire_date' => now(),
    'department_id' => 1,
    'position_id' => 1,
    'basic_salary' => 8000,
    'currency' => 'SAR',
]);
```

### تسجيل الحضور

```php
use App\Domains\HR\Services\AttendanceManagementService;

$attendanceService = app(AttendanceManagementService::class);

// تسجيل الحضور
$record = $attendanceService->checkIn(1, [
    'method' => 'BIOMETRIC',
    'device' => 'Terminal-01',
    'location' => [
        'latitude' => 24.7136,
        'longitude' => 46.6753,
    ],
]);

// تسجيل الانصراف
$record = $attendanceService->checkOut(1, [
    'method' => 'BIOMETRIC',
    'device' => 'Terminal-01',
]);
```

### إنشاء طلب إجازة

```php
use App\Domains\HR\Services\LeaveManagementService;

$leaveService = app(LeaveManagementService::class);

$leaveRequest = $leaveService->createLeaveRequest([
    'employee_id' => 1,
    'leave_type_id' => 1,
    'start_date' => '2024-02-01',
    'end_date' => '2024-02-05',
    'reason' => 'إجازة سنوية',
    'coverage_employee_id' => 2,
]);
```

### معالجة كشوف الرواتب

```php
use App\Domains\HR\Jobs\ProcessPayrollJob;

// معالجة كشوف الرواتب لشهر معين
ProcessPayrollJob::dispatch(2, 2024); // فبراير 2024

// معالجة لموظفين محددين
ProcessPayrollJob::dispatch(2, 2024, [1, 2, 3]);
```

## 🔌 APIs

### نقاط النهاية الرئيسية

```http
# الموظفين
GET    /api/hr/employees
POST   /api/hr/employees
GET    /api/hr/employees/{id}
PUT    /api/hr/employees/{id}
DELETE /api/hr/employees/{id}
POST   /api/hr/employees/{id}/terminate

# الحضور والانصراف
GET    /api/hr/attendance
POST   /api/hr/attendance/check-in
POST   /api/hr/attendance/check-out
POST   /api/hr/attendance/start-break
POST   /api/hr/attendance/end-break

# الإجازات
GET    /api/hr/leave-requests
POST   /api/hr/leave-requests
PUT    /api/hr/leave-requests/{id}
POST   /api/hr/leave-requests/{id}/approve
POST   /api/hr/leave-requests/{id}/reject

# الرواتب
GET    /api/hr/payroll
POST   /api/hr/payroll/process
GET    /api/hr/payroll/payslips/{employee_id}
```

### مثال على استخدام API

```javascript
// إنشاء موظف جديد
const response = await fetch('/api/hr/employees', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token,
    },
    body: JSON.stringify({
        first_name: 'سارة',
        last_name: 'أحمد',
        email: '<EMAIL>',
        mobile: '+966501234567',
        date_of_birth: '1992-05-20',
        hire_date: '2024-01-01',
        department_id: 2,
        position_id: 3,
        basic_salary: 7000,
        currency: 'SAR'
    })
});

const employee = await response.json();
```

## 🎯 الأحداث والمستمعين

### الأحداث المتاحة

```php
// أحداث الموظفين
EmployeeCreated::class
EmployeeUpdated::class
EmployeeTerminated::class

// أحداث الحضور
EmployeeCheckedIn::class
EmployeeCheckedOut::class
AttendanceAnomalyDetected::class

// أحداث الإجازات
LeaveRequestSubmitted::class
LeaveRequestApproved::class
LeaveRequestRejected::class

// أحداث الرواتب
PayrollProcessed::class
PayslipGenerated::class
```

### مثال على مستمع مخصص

```php
use App\Domains\HR\Events\EmployeeCreated;

class SendWelcomeEmail
{
    public function handle(EmployeeCreated $event)
    {
        // إرسال بريد ترحيبي للموظف الجديد
        Mail::to($event->employee->email)
            ->send(new WelcomeEmployeeMail($event->employee));
    }
}
```

## 🔒 الأمان والأذونات

### الأذونات المتاحة

```php
// أذونات الموظفين
'view-employees'
'create-employees'
'update-employees'
'delete-employees'
'terminate-employees'

// أذونات الحضور
'view-attendance'
'manage-attendance'
'approve-attendance'

// أذونات الإجازات
'view-leave-requests'
'create-leave-requests'
'approve-leave-requests'
'manage-leave-types'

// أذونات الرواتب
'view-payroll'
'process-payroll'
'manage-payroll'
```

## 📊 التقارير المتاحة

### تقارير الموظفين
- **قائمة الموظفين** - Employee Roster
- **الهيكل التنظيمي** - Organization Chart
- **إحصائيات الموظفين** - Employee Statistics
- **تقرير الوثائق المنتهية** - Expiring Documents Report

### تقارير الحضور
- **تقرير الحضور اليومي** - Daily Attendance Report
- **تقرير العمل الإضافي** - Overtime Report
- **تقرير التأخير** - Late Arrival Report
- **تحليل الإنتاجية** - Productivity Analysis

### تقارير الإجازات
- **أرصدة الإجازات** - Leave Balances Report
- **تقرير الإجازات المعتمدة** - Approved Leaves Report
- **إحصائيات الإجازات** - Leave Statistics
- **تقرير الغياب** - Absence Report

### تقارير الرواتب
- **كشوف الرواتب** - Payroll Summary
- **تقرير التكاليف** - Cost Analysis Report
- **تقرير الضرائب** - Tax Report
- **تحليل الرواتب** - Salary Analysis

## 🔧 المهام المجدولة

```php
// في app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // معالجة كشوف الرواتب الشهرية
    $schedule->job(new ProcessPayrollJob(now()->month, now()->year))
             ->monthlyOn(25, '09:00');
    
    // تحديث أرصدة الإجازات
    $schedule->job(new UpdateLeaveBalancesJob(now()->year, 'ACCRUAL'))
             ->monthly();
    
    // ترحيل أرصدة الإجازات السنوية
    $schedule->job(new UpdateLeaveBalancesJob(now()->year, 'CARRY_FORWARD'))
             ->yearly();
    
    // تنظيف البيانات القديمة
    $schedule->call(function () {
        AttendanceRecord::where('date', '<', now()->subYears(2))->delete();
    })->weekly();
}
```

## 🧪 الاختبارات

```bash
# تشغيل جميع اختبارات الموارد البشرية
php artisan test --testsuite=HR

# اختبار ميزة محددة
php artisan test tests/Feature/HR/EmployeeTest.php

# اختبار مع التغطية
php artisan test --coverage --testsuite=HR
```

## 📈 الأداء والتحسين

### نصائح الأداء
- استخدم الكاش للتقارير الثقيلة
- فعل الفهرسة على الحقول المهمة
- استخدم المعالجة المتوازية للمهام الكبيرة
- فعل ضغط البيانات للأرشيف

### مراقبة الأداء
```php
// في config/hr.php
'performance' => [
    'cache_enabled' => true,
    'cache_duration' => 3600,
    'queue_enabled' => true,
    'parallel_processing' => true,
],
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة الاختبارات
4. تنفيذ الميزة
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

- **الوثائق**: [docs.hrpro.com](https://docs.hrpro.com)
- **المجتمع**: [community.hrpro.com](https://community.hrpro.com)
- **الدعم الفني**: <EMAIL>
- **GitHub Issues**: [github.com/hrpro/hr-system/issues](https://github.com/hrpro/hr-system/issues)

---

**تم تطويره بـ ❤️ من فريق HR Pro**
