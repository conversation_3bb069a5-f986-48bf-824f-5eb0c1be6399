<?php

namespace App\Domains\Taxation\Services;

use App\Domains\Taxation\Models\TaxReturn;
use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Models\TaxRule;
use App\Domains\Taxation\Events\TaxReturnCreated;
use App\Domains\Taxation\Events\TaxReturnSubmitted;
use App\Domains\Taxation\Events\TaxReturnApproved;
use App\Domains\Taxation\Events\TaxReturnCancelled;
use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\JournalEntry;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة إدارة الإقرارات الضريبية
 * خدمة شاملة لإدارة دورة حياة الإقرارات الضريبية
 */
class TaxReturnService
{
    protected DynamicTaxEngine $taxEngine;
    protected TaxAuthorityIntegrationService $integrationService;

    public function __construct(
        DynamicTaxEngine $taxEngine,
        TaxAuthorityIntegrationService $integrationService
    ) {
        $this->taxEngine = $taxEngine;
        $this->integrationService = $integrationService;
    }

    /**
     * إنشاء إقرار ضريبي جديد
     */
    public function createTaxReturn(array $data): TaxReturn
    {
        DB::beginTransaction();

        try {
            // إنشاء رقم الإقرار
            $data['return_number'] = $this->generateReturnNumber($data['tax_system_id'], $data['type']);

            // تحديد الفترة الضريبية إذا لم تكن محددة
            if (!isset($data['tax_period_from']) || !isset($data['tax_period_to'])) {
                $period = $this->calculateTaxPeriod($data['tax_system_id'], $data['type']);
                $data['tax_period_from'] = $period['from'];
                $data['tax_period_to'] = $period['to'];
            }

            // تحديد تاريخ الاستحقاق
            if (!isset($data['due_date'])) {
                $data['due_date'] = $this->calculateDueDate($data['tax_system_id'], $data['type'], $data['tax_period_to']);
            }

            // إنشاء الإقرار
            $taxReturn = TaxReturn::create(array_merge($data, [
                'status' => 'DRAFT',
                'created_by' => auth()->id(),
                'uuid' => \Str::uuid(),
            ]));

            // حساب الضرائب الأولي
            $this->calculateTaxes($taxReturn);

            // إطلاق الحدث
            event(new TaxReturnCreated($taxReturn));

            DB::commit();

            return $taxReturn->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('فشل في إنشاء الإقرار الضريبي', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * تحديث الإقرار الضريبي
     */
    public function updateTaxReturn(TaxReturn $taxReturn, array $data): TaxReturn
    {
        DB::beginTransaction();

        try {
            // التحقق من إمكانية التحديث
            if (!$this->canUpdate($taxReturn)) {
                throw new \Exception('لا يمكن تحديث الإقرار الضريبي في الحالة الحالية');
            }

            // حفظ البيانات القديمة للمقارنة
            $oldData = $taxReturn->toArray();

            // تحديث البيانات
            $taxReturn->update(array_merge($data, [
                'updated_by' => auth()->id(),
            ]));

            // إعادة حساب الضرائب إذا تغيرت البيانات المالية
            if ($this->hasFinancialChanges($oldData, $data)) {
                $this->calculateTaxes($taxReturn);
            }

            // تسجيل التغييرات
            $this->logChanges($taxReturn, $oldData, $data);

            DB::commit();

            return $taxReturn->fresh();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('فشل في تحديث الإقرار الضريبي', [
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * حساب الضرائب للإقرار
     */
    public function calculateTaxes(TaxReturn $taxReturn): array
    {
        try {
            $calculations = [];
            $totalTaxAmount = 0;

            // حساب ضريبة القيمة المضافة
            if ($taxReturn->vat_applicable) {
                $vatCalculation = $this->calculateVAT($taxReturn);
                $calculations['vat'] = $vatCalculation;
                $totalTaxAmount += $vatCalculation['tax_amount'];
            }

            // حساب ضريبة الشركات
            if ($taxReturn->corporate_tax_applicable) {
                $corporateCalculation = $this->calculateCorporateTax($taxReturn);
                $calculations['corporate_tax'] = $corporateCalculation;
                $totalTaxAmount += $corporateCalculation['tax_amount'];
            }

            // حساب ضريبة الاستقطاع
            if ($taxReturn->withholding_tax_applicable) {
                $withholdingCalculation = $this->calculateWithholdingTax($taxReturn);
                $calculations['withholding_tax'] = $withholdingCalculation;
                $totalTaxAmount += $withholdingCalculation['tax_amount'];
            }

            // حساب الضريبة الانتقائية
            if ($taxReturn->excise_tax_applicable) {
                $exciseCalculation = $this->calculateExciseTax($taxReturn);
                $calculations['excise_tax'] = $exciseCalculation;
                $totalTaxAmount += $exciseCalculation['tax_amount'];
            }

            // حساب الرسوم الجمركية
            if ($taxReturn->customs_duty_applicable) {
                $customsCalculation = $this->calculateCustomsDuty($taxReturn);
                $calculations['customs_duty'] = $customsCalculation;
                $totalTaxAmount += $customsCalculation['tax_amount'];
            }

            // حساب الغرامات والفوائد
            $penaltiesAndInterest = $this->calculatePenaltiesAndInterest($taxReturn);
            if ($penaltiesAndInterest['total_amount'] > 0) {
                $calculations['penalties_and_interest'] = $penaltiesAndInterest;
                $totalTaxAmount += $penaltiesAndInterest['total_amount'];
            }

            // تطبيق الخصومات والإعفاءات
            $discountsAndExemptions = $this->calculateDiscountsAndExemptions($taxReturn, $totalTaxAmount);
            if ($discountsAndExemptions['total_discount'] > 0) {
                $calculations['discounts_and_exemptions'] = $discountsAndExemptions;
                $totalTaxAmount -= $discountsAndExemptions['total_discount'];
            }

            // تحديث الإقرار بالمبلغ الإجمالي
            $taxReturn->update([
                'total_tax_amount' => max(0, $totalTaxAmount),
                'tax_calculations' => $calculations,
                'calculated_at' => now(),
                'calculated_by' => auth()->id(),
            ]);

            // حفظ تفاصيل الحسابات
            $this->saveTaxCalculations($taxReturn, $calculations);

            return $calculations;

        } catch (\Exception $e) {
            Log::error('فشل في حساب الضرائب', [
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * التحقق من صحة الإقرار الضريبي
     */
    public function validateTaxReturn(TaxReturn $taxReturn): array
    {
        $errors = [];
        $warnings = [];

        // التحقق من البيانات الأساسية
        if (!$taxReturn->company_id) {
            $errors[] = 'معرف الشركة مطلوب';
        }

        if (!$taxReturn->tax_period_from || !$taxReturn->tax_period_to) {
            $errors[] = 'الفترة الضريبية مطلوبة';
        }

        if ($taxReturn->tax_period_from >= $taxReturn->tax_period_to) {
            $errors[] = 'تاريخ نهاية الفترة الضريبية يجب أن يكون بعد تاريخ البداية';
        }

        // التحقق من البيانات المالية
        if ($taxReturn->vat_applicable && (!$taxReturn->vat_sales || !$taxReturn->vat_purchases)) {
            $warnings[] = 'بيانات ضريبة القيمة المضافة غير مكتملة';
        }

        if ($taxReturn->corporate_tax_applicable && !$taxReturn->taxable_income) {
            $warnings[] = 'الدخل الخاضع للضريبة غير محدد';
        }

        // التحقق من المرفقات المطلوبة
        $requiredDocuments = $this->getRequiredDocuments($taxReturn);
        $missingDocuments = [];

        foreach ($requiredDocuments as $document) {
            if (!$taxReturn->attachments()->where('type', $document)->exists()) {
                $missingDocuments[] = $document;
            }
        }

        if (!empty($missingDocuments)) {
            $warnings[] = 'المستندات المطلوبة مفقودة: ' . implode(', ', $missingDocuments);
        }

        // التحقق من التوقيع الرقمي
        if ($taxReturn->taxSystem->compliance_settings['require_digital_signature'] ?? false) {
            if (!$taxReturn->digital_signature) {
                $errors[] = 'التوقيع الرقمي مطلوب';
            }
        }

        // التحقق من الرقم الضريبي
        if ($taxReturn->taxSystem->compliance_settings['require_tax_number'] ?? false) {
            if (!$taxReturn->company->tax_number) {
                $errors[] = 'الرقم الضريبي للشركة مطلوب';
            }
        }

        // التحقق من تاريخ الاستحقاق
        if ($taxReturn->due_date < now()) {
            $warnings[] = 'تاريخ الاستحقاق قد انتهى';
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'validation_score' => $this->calculateValidationScore($errors, $warnings),
            'validated_at' => now(),
        ];
    }

    /**
     * تقديم الإقرار الضريبي
     */
    public function submitTaxReturn(TaxReturn $taxReturn): array
    {
        DB::beginTransaction();

        try {
            // التحقق من صحة الإقرار
            $validation = $this->validateTaxReturn($taxReturn);
            if (!$validation['is_valid']) {
                throw new \Exception('الإقرار الضريبي غير صحيح: ' . implode(', ', $validation['errors']));
            }

            // التحقق من إمكانية التقديم
            if (!$this->canSubmit($taxReturn)) {
                throw new \Exception('لا يمكن تقديم الإقرار الضريبي في الحالة الحالية');
            }

            // إنشاء رقم مرجعي للتقديم
            $submissionReference = $this->generateSubmissionReference($taxReturn);

            // تقديم الإقرار للهيئة الضريبية
            $submissionResult = null;
            if ($taxReturn->taxSystem->authority_integration['enabled'] ?? false) {
                $submissionResult = $this->integrationService->submitTaxReturn($taxReturn);
            }

            // تحديث حالة الإقرار
            $taxReturn->update([
                'status' => 'SUBMITTED',
                'submitted_at' => now(),
                'submitted_by' => auth()->id(),
                'submission_reference' => $submissionReference,
                'authority_response' => $submissionResult,
                'authority_status' => $submissionResult['status'] ?? 'PENDING',
            ]);

            // إطلاق الحدث
            event(new TaxReturnSubmitted($taxReturn));

            DB::commit();

            return [
                'success' => true,
                'submission_reference' => $submissionReference,
                'authority_response' => $submissionResult,
                'submitted_at' => $taxReturn->submitted_at,
            ];

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('فشل في تقديم الإقرار الضريبي', [
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * إلغاء تقديم الإقرار الضريبي
     */
    public function cancelTaxReturn(TaxReturn $taxReturn): array
    {
        DB::beginTransaction();

        try {
            // التحقق من إمكانية الإلغاء
            if (!$this->canCancel($taxReturn)) {
                throw new \Exception('لا يمكن إلغاء الإقرار الضريبي في الحالة الحالية');
            }

            // إلغاء التقديم من الهيئة الضريبية
            $cancellationResult = null;
            if ($taxReturn->taxSystem->authority_integration['enabled'] ?? false && $taxReturn->submission_reference) {
                $cancellationResult = $this->integrationService->cancelTaxReturn($taxReturn);
            }

            // تحديث حالة الإقرار
            $taxReturn->update([
                'status' => 'CANCELLED',
                'cancelled_at' => now(),
                'cancelled_by' => auth()->id(),
                'cancellation_reason' => request('cancellation_reason'),
                'authority_response' => $cancellationResult,
            ]);

            // إطلاق الحدث
            event(new TaxReturnCancelled($taxReturn));

            DB::commit();

            return [
                'success' => true,
                'cancelled_at' => $taxReturn->cancelled_at,
                'authority_response' => $cancellationResult,
            ];

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('فشل في إلغاء الإقرار الضريبي', [
                'tax_return_id' => $taxReturn->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * تصدير الإقرار الضريبي
     */
    public function exportTaxReturn(TaxReturn $taxReturn, string $format, bool $includeAttachments = false): string
    {
        try {
            $exportData = $this->prepareExportData($taxReturn, $includeAttachments);
            
            switch (strtoupper($format)) {
                case 'PDF':
                    return $this->exportToPDF($taxReturn, $exportData);
                case 'XML':
                    return $this->exportToXML($taxReturn, $exportData);
                case 'EXCEL':
                    return $this->exportToExcel($taxReturn, $exportData);
                case 'JSON':
                    return $this->exportToJSON($taxReturn, $exportData);
                default:
                    throw new \Exception('تنسيق التصدير غير مدعوم');
            }

        } catch (\Exception $e) {
            Log::error('فشل في تصدير الإقرار الضريبي', [
                'tax_return_id' => $taxReturn->id,
                'format' => $format,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * إنشاء رقم الإقرار
     */
    protected function generateReturnNumber(int $taxSystemId, string $type): string
    {
        $taxSystem = TaxSystem::find($taxSystemId);
        $year = now()->year;
        $month = now()->format('m');
        
        $prefix = $taxSystem->country_code . '-' . $type . '-' . $year . $month;
        
        $lastReturn = TaxReturn::where('tax_system_id', $taxSystemId)
            ->where('type', $type)
            ->where('return_number', 'like', $prefix . '%')
            ->orderBy('return_number', 'desc')
            ->first();

        $sequence = 1;
        if ($lastReturn) {
            $lastSequence = (int) substr($lastReturn->return_number, -4);
            $sequence = $lastSequence + 1;
        }

        return $prefix . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * حساب الفترة الضريبية
     */
    protected function calculateTaxPeriod(int $taxSystemId, string $type): array
    {
        $taxSystem = TaxSystem::find($taxSystemId);
        $periods = $taxSystem->tax_periods ?? [];
        
        $period = collect($periods)->firstWhere('tax_type', $type);
        
        if (!$period) {
            // افتراضي: شهري
            $from = now()->startOfMonth()->subMonth();
            $to = now()->startOfMonth()->subDay();
        } else {
            switch ($period['type']) {
                case 'MONTHLY':
                    $from = now()->startOfMonth()->subMonth();
                    $to = now()->startOfMonth()->subDay();
                    break;
                case 'QUARTERLY':
                    $from = now()->startOfQuarter()->subQuarter();
                    $to = now()->startOfQuarter()->subDay();
                    break;
                case 'ANNUALLY':
                    $from = now()->startOfYear()->subYear();
                    $to = now()->startOfYear()->subDay();
                    break;
                default:
                    $from = now()->startOfMonth()->subMonth();
                    $to = now()->startOfMonth()->subDay();
            }
        }

        return [
            'from' => $from->format('Y-m-d'),
            'to' => $to->format('Y-m-d'),
        ];
    }

    /**
     * حساب تاريخ الاستحقاق
     */
    protected function calculateDueDate(int $taxSystemId, string $type, string $periodTo): Carbon
    {
        $taxSystem = TaxSystem::find($taxSystemId);
        $periods = $taxSystem->tax_periods ?? [];
        
        $period = collect($periods)->firstWhere('tax_type', $type);
        $dueDays = $period['due_days'] ?? 30;

        return Carbon::parse($periodTo)->addDays($dueDays);
    }

    /**
     * حساب ضريبة القيمة المضافة
     */
    protected function calculateVAT(TaxReturn $taxReturn): array
    {
        $vatSales = $taxReturn->vat_sales ?? 0;
        $vatPurchases = $taxReturn->vat_purchases ?? 0;
        
        $salesTax = $this->taxEngine->calculateVAT($vatSales, 'SALES');
        $purchasesTax = $this->taxEngine->calculateVAT($vatPurchases, 'PURCHASES');
        
        $netTax = $salesTax['tax_amount'] - $purchasesTax['tax_amount'];

        return [
            'sales_amount' => $vatSales,
            'sales_tax' => $salesTax['tax_amount'],
            'purchases_amount' => $vatPurchases,
            'purchases_tax' => $purchasesTax['tax_amount'],
            'net_tax' => $netTax,
            'tax_amount' => max(0, $netTax),
        ];
    }

    /**
     * حساب ضريبة الشركات
     */
    protected function calculateCorporateTax(TaxReturn $taxReturn): array
    {
        $taxableIncome = $taxReturn->taxable_income ?? 0;
        
        $calculation = $this->taxEngine->calculateCorporateTax($taxableIncome);

        return [
            'taxable_income' => $taxableIncome,
            'tax_amount' => $calculation['tax_amount'],
            'effective_rate' => $calculation['effective_rate'],
            'deductions' => $calculation['deductions'] ?? 0,
        ];
    }

    /**
     * حساب ضريبة الاستقطاع
     */
    protected function calculateWithholdingTax(TaxReturn $taxReturn): array
    {
        $withholdingAmount = $taxReturn->withholding_amount ?? 0;
        
        $calculation = $this->taxEngine->calculateWithholdingTax($withholdingAmount);

        return [
            'withholding_amount' => $withholdingAmount,
            'tax_amount' => $calculation['tax_amount'],
            'rate' => $calculation['rate'],
        ];
    }

    /**
     * حساب الضريبة الانتقائية
     */
    protected function calculateExciseTax(TaxReturn $taxReturn): array
    {
        $exciseItems = $taxReturn->excise_items ?? [];
        $totalTax = 0;

        foreach ($exciseItems as $item) {
            $calculation = $this->taxEngine->calculateExciseTax($item['amount'], $item['category']);
            $totalTax += $calculation['tax_amount'];
        }

        return [
            'items' => $exciseItems,
            'tax_amount' => $totalTax,
        ];
    }

    /**
     * حساب الرسوم الجمركية
     */
    protected function calculateCustomsDuty(TaxReturn $taxReturn): array
    {
        $customsItems = $taxReturn->customs_items ?? [];
        $totalDuty = 0;

        foreach ($customsItems as $item) {
            $calculation = $this->taxEngine->calculateCustomsDuty($item['amount'], $item['hs_code']);
            $totalDuty += $calculation['duty_amount'];
        }

        return [
            'items' => $customsItems,
            'tax_amount' => $totalDuty,
        ];
    }

    /**
     * حساب الغرامات والفوائد
     */
    protected function calculatePenaltiesAndInterest(TaxReturn $taxReturn): array
    {
        $penalties = 0;
        $interest = 0;

        // حساب الغرامات للتأخير
        if ($taxReturn->due_date < now() && $taxReturn->status !== 'SUBMITTED') {
            $daysLate = now()->diffInDays($taxReturn->due_date);
            $penaltyRate = $taxReturn->taxSystem->compliance_settings['penalty_rate'] ?? 0;
            
            if ($penaltyRate > 0) {
                $penalties = ($taxReturn->total_tax_amount * $penaltyRate / 100) * ($daysLate / 30);
            }
        }

        return [
            'penalties' => $penalties,
            'interest' => $interest,
            'total_amount' => $penalties + $interest,
        ];
    }

    /**
     * حساب الخصومات والإعفاءات
     */
    protected function calculateDiscountsAndExemptions(TaxReturn $taxReturn, float $totalTax): array
    {
        $discounts = 0;
        $exemptions = 0;

        // تطبيق الخصومات والإعفاءات حسب القواعد
        // يمكن تطوير هذا بناءً على قواعد النظام الضريبي

        return [
            'discounts' => $discounts,
            'exemptions' => $exemptions,
            'total_discount' => $discounts + $exemptions,
        ];
    }

    /**
     * حفظ تفاصيل الحسابات
     */
    protected function saveTaxCalculations(TaxReturn $taxReturn, array $calculations): void
    {
        // حذف الحسابات السابقة
        $taxReturn->taxCalculations()->delete();

        // حفظ الحسابات الجديدة
        foreach ($calculations as $type => $calculation) {
            $taxReturn->taxCalculations()->create([
                'calculation_type' => $type,
                'amount' => $calculation['tax_amount'] ?? 0,
                'details' => $calculation,
                'calculated_at' => now(),
                'calculated_by' => auth()->id(),
            ]);
        }
    }

    /**
     * التحقق من إمكانية التحديث
     */
    protected function canUpdate(TaxReturn $taxReturn): bool
    {
        return in_array($taxReturn->status, ['DRAFT', 'PENDING']);
    }

    /**
     * التحقق من إمكانية التقديم
     */
    protected function canSubmit(TaxReturn $taxReturn): bool
    {
        return in_array($taxReturn->status, ['DRAFT', 'PENDING']);
    }

    /**
     * التحقق من إمكانية الإلغاء
     */
    protected function canCancel(TaxReturn $taxReturn): bool
    {
        return in_array($taxReturn->status, ['SUBMITTED', 'PENDING']);
    }

    /**
     * التحقق من وجود تغييرات مالية
     */
    protected function hasFinancialChanges(array $oldData, array $newData): bool
    {
        $financialFields = [
            'vat_sales', 'vat_purchases', 'taxable_income', 'withholding_amount',
            'excise_items', 'customs_items'
        ];

        foreach ($financialFields as $field) {
            if (isset($newData[$field]) && ($oldData[$field] ?? null) !== $newData[$field]) {
                return true;
            }
        }

        return false;
    }

    /**
     * تسجيل التغييرات
     */
    protected function logChanges(TaxReturn $taxReturn, array $oldData, array $newData): void
    {
        $changes = [];
        foreach ($newData as $key => $value) {
            if (isset($oldData[$key]) && $oldData[$key] !== $value) {
                $changes[$key] = [
                    'old' => $oldData[$key],
                    'new' => $value,
                ];
            }
        }

        if (!empty($changes)) {
            activity()
                ->performedOn($taxReturn)
                ->causedBy(auth()->user())
                ->withProperties([
                    'changes' => $changes,
                    'return_number' => $taxReturn->return_number,
                ])
                ->log('تم تحديث الإقرار الضريبي');
        }
    }

    /**
     * الحصول على المستندات المطلوبة
     */
    protected function getRequiredDocuments(TaxReturn $taxReturn): array
    {
        $documents = [];

        if ($taxReturn->vat_applicable) {
            $documents[] = 'VAT_RECORDS';
        }

        if ($taxReturn->corporate_tax_applicable) {
            $documents[] = 'FINANCIAL_STATEMENTS';
            $documents[] = 'PROFIT_LOSS_STATEMENT';
        }

        if ($taxReturn->withholding_tax_applicable) {
            $documents[] = 'WITHHOLDING_CERTIFICATES';
        }

        return $documents;
    }

    /**
     * حساب درجة التحقق
     */
    protected function calculateValidationScore(array $errors, array $warnings): int
    {
        $score = 100;
        $score -= count($errors) * 20;
        $score -= count($warnings) * 5;

        return max(0, $score);
    }

    /**
     * إنشاء رقم مرجعي للتقديم
     */
    protected function generateSubmissionReference(TaxReturn $taxReturn): string
    {
        return 'SUB-' . $taxReturn->return_number . '-' . now()->format('YmdHis');
    }

    /**
     * تحضير بيانات التصدير
     */
    protected function prepareExportData(TaxReturn $taxReturn, bool $includeAttachments): array
    {
        $data = $taxReturn->toArray();
        
        if ($includeAttachments) {
            $data['attachments'] = $taxReturn->attachments;
        }

        return $data;
    }

    /**
     * التصدير إلى PDF
     */
    protected function exportToPDF(TaxReturn $taxReturn, array $data): string
    {
        // تنفيذ التصدير إلى PDF
        $filename = "tax_return_{$taxReturn->return_number}.pdf";
        // كود إنشاء PDF...
        
        return $filename;
    }

    /**
     * التصدير إلى XML
     */
    protected function exportToXML(TaxReturn $taxReturn, array $data): string
    {
        // تنفيذ التصدير إلى XML
        $filename = "tax_return_{$taxReturn->return_number}.xml";
        // كود إنشاء XML...
        
        return $filename;
    }

    /**
     * التصدير إلى Excel
     */
    protected function exportToExcel(TaxReturn $taxReturn, array $data): string
    {
        // تنفيذ التصدير إلى Excel
        $filename = "tax_return_{$taxReturn->return_number}.xlsx";
        // كود إنشاء Excel...
        
        return $filename;
    }

    /**
     * التصدير إلى JSON
     */
    protected function exportToJSON(TaxReturn $taxReturn, array $data): string
    {
        $filename = "tax_return_{$taxReturn->return_number}.json";
        $path = "exports/tax_returns/{$filename}";
        
        Storage::put($path, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        return $path;
    }
}
