<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - حسابي AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .step { display: none; }
        .step.active { display: block; }
        .step-indicator { background: #e5e7eb; }
        .step-indicator.active { background: #8b5cf6; }
        .step-indicator.completed { background: #10b981; }
    </style>
</head>
<body class="gradient-bg min-h-screen py-8">
    <!-- Location Confirmation Modal -->
    <div id="location-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <i class="fas fa-map-marker-alt text-4xl text-purple-600 mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-800 mb-2">تأكيد الموقع</h3>
                <p class="text-gray-600">يرجى تأكيد موقعك الحالي أو اختيار دولة أخرى</p>
            </div>
            
            <div class="space-y-4">
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('SA')">
                    <div class="flex items-center">
                        <img src="/images/flags/sa.png" alt="السعودية" class="w-8 h-6 mr-3">
                        <span class="font-semibold">المملكة العربية السعودية</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('AE')">
                    <div class="flex items-center">
                        <img src="/images/flags/ae.png" alt="الإمارات" class="w-8 h-6 mr-3">
                        <span class="font-semibold">دولة الإمارات العربية المتحدة</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('MA')">
                    <div class="flex items-center">
                        <img src="/images/flags/ma.png" alt="المغرب" class="w-8 h-6 mr-3">
                        <span class="font-semibold">المملكة المغربية</span>
                    </div>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" onclick="selectCountry('EG')">
                    <div class="flex items-center">
                        <img src="/images/flags/eg.png" alt="مصر" class="w-8 h-6 mr-3">
                        <span class="font-semibold">جمهورية مصر العربية</span>
                    </div>
                </div>
                
                <select class="w-full p-4 border border-gray-200 rounded-lg" onchange="selectCountry(this.value)">
                    <option value="">اختر دولة أخرى...</option>
                    <option value="JO">الأردن</option>
                    <option value="KW">الكويت</option>
                    <option value="QA">قطر</option>
                    <option value="BH">البحرين</option>
                    <option value="OM">عمان</option>
                    <option value="LB">لبنان</option>
                    <option value="SY">سوريا</option>
                    <option value="IQ">العراق</option>
                    <option value="YE">اليمن</option>
                    <option value="LY">ليبيا</option>
                    <option value="TN">تونس</option>
                    <option value="DZ">الجزائر</option>
                    <option value="SD">السودان</option>
                </select>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center space-x-4 space-x-reverse mb-4">
                <img src="/images/logo.png" alt="حسابي AI" class="h-12 w-12">
                <span class="text-3xl font-bold text-white">حسابي AI</span>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">مرحباً بك في حسابي AI</h1>
            <p class="text-white opacity-90">ابدأ رحلتك في إدارة عملك بذكاء واحترافية</p>
        </div>

        <!-- Step Indicator -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full step-indicator active flex items-center justify-center text-white font-semibold" id="step-indicator-1">1</div>
                    <span class="mr-2 text-white text-sm">المعلومات الشخصية</span>
                </div>
                <div class="w-8 h-1 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full step-indicator flex items-center justify-center text-white font-semibold" id="step-indicator-2">2</div>
                    <span class="mr-2 text-white text-sm">نوع الحساب والموقع</span>
                </div>
                <div class="w-8 h-1 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full step-indicator flex items-center justify-center text-white font-semibold" id="step-indicator-3">3</div>
                    <span class="mr-2 text-white text-sm">معلومات الشركة</span>
                </div>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-2xl p-8">
            <form method="POST" action="{{ route('register') }}" id="registration-form">
                @csrf
                
                <!-- Step 1: Personal Information -->
                <div class="step active" id="step-1">
                    <h2 class="text-2xl font-bold text-gray-800 text-center mb-8">المعلومات الشخصية</h2>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-gray-700 font-semibold mb-2">الاسم الكامل *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                   placeholder="أدخل اسمك الكامل"
                                   required>
                        </div>
                        
                        <div>
                            <label for="username" class="block text-gray-700 font-semibold mb-2">اسم المستخدم *</label>
                            <input type="text" 
                                   id="username" 
                                   name="username" 
                                   value="{{ old('username') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                   placeholder="اختر اسم مستخدم فريد"
                                   required>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="email" class="block text-gray-700 font-semibold mb-2">البريد الإلكتروني *</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="{{ old('email') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                               placeholder="أدخل بريدك الإلكتروني"
                               required>
                        <p class="text-sm text-gray-500 mt-1">سيتم استخدامه كمعرّف الدخول</p>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6 mt-6">
                        <div>
                            <label for="password" class="block text-gray-700 font-semibold mb-2">كلمة المرور *</label>
                            <div class="relative">
                                <input type="password" 
                                       id="password" 
                                       name="password" 
                                       class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="كلمة المرور (10 أحرف على الأقل)"
                                       required
                                       minlength="10">
                                <button type="button" onclick="togglePassword('password')" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">يجب أن تتكون من 10 أحرف على الأقل وتحتوي على أرقام وإشارات</p>
                        </div>
                        
                        <div>
                            <label for="password_confirmation" class="block text-gray-700 font-semibold mb-2">تأكيد كلمة المرور *</label>
                            <div class="relative">
                                <input type="password" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="أعد إدخال كلمة المرور"
                                       required>
                                <button type="button" onclick="togglePassword('password_confirmation')" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end mt-8">
                        <button type="button" onclick="nextStep()" class="bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition duration-300">
                            التالي <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Step 2: Account Type and Location -->
                <div class="step" id="step-2">
                    <h2 class="text-2xl font-bold text-gray-800 text-center mb-8">نوع الحساب والموقع</h2>
                    
                    <div class="mb-8">
                        <label class="block text-gray-700 font-semibold mb-4">نوع الحساب *</label>
                        <div class="grid md:grid-cols-2 gap-4">
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="account_type" value="freelancer" class="text-purple-600 mr-3" required>
                                <div>
                                    <div class="font-semibold">فرد / مستقل</div>
                                    <div class="text-sm text-gray-600">محاسب، مبرمج، مستشار</div>
                                </div>
                            </label>
                            
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="account_type" value="sme" class="text-purple-600 mr-3" required>
                                <div>
                                    <div class="font-semibold">شركة صغيرة</div>
                                    <div class="text-sm text-gray-600">شركة من 1 إلى 50 موظفاً</div>
                                </div>
                            </label>
                            
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="account_type" value="enterprise" class="text-purple-600 mr-3" required>
                                <div>
                                    <div class="font-semibold">شركة كبيرة</div>
                                    <div class="text-sm text-gray-600">أكثر من 50 موظفاً</div>
                                </div>
                            </label>
                            
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="account_type" value="accounting_office" class="text-purple-600 mr-3" required>
                                <div>
                                    <div class="font-semibold">مكتب محاسبة / استشارات</div>
                                    <div class="text-sm text-gray-600">يقدم الخدمات لعملاء</div>
                                </div>
                            </label>
                            
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="account_type" value="mcp_partner" class="text-purple-600 mr-3" required>
                                <div>
                                    <div class="font-semibold">شريك تقني (MCP Partner)</div>
                                    <div class="text-sm text-gray-600">مطور، متكامل، مسوق</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="country" class="block text-gray-700 font-semibold mb-2">الدولة *</label>
                            <select id="country" 
                                    name="country" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                    required
                                    onchange="updateCurrency()">
                                <option value="">اختر الدولة</option>
                                <option value="MA">المملكة المغربية</option>
                                <option value="SA">المملكة العربية السعودية</option>
                                <option value="AE">دولة الإمارات العربية المتحدة</option>
                                <option value="EG">جمهورية مصر العربية</option>
                                <option value="TN">تونس</option>
                                <option value="DZ">الجزائر</option>
                                <option value="JO">الأردن</option>
                                <option value="KW">الكويت</option>
                                <option value="QA">قطر</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="city" class="block text-gray-700 font-semibold mb-2">المدينة</label>
                            <input type="text" 
                                   id="city" 
                                   name="city" 
                                   value="{{ old('city') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                   placeholder="أدخل اسم المدينة">
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="currency" class="block text-gray-700 font-semibold mb-2">العملة الافتراضية</label>
                        <input type="text" 
                               id="currency" 
                               name="currency" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100" 
                               placeholder="سيتم تحديدها تلقائياً حسب الدولة"
                               readonly>
                    </div>

                    <div class="flex justify-between mt-8">
                        <button type="button" onclick="prevStep()" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            <i class="fas fa-arrow-right mr-2"></i> السابق
                        </button>
                        <button type="button" onclick="nextStep()" class="bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition duration-300">
                            التالي <i class="fas fa-arrow-left mr-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Company Information -->
                <div class="step" id="step-3">
                    <h2 class="text-2xl font-bold text-gray-800 text-center mb-8">معلومات الشركة</h2>
                    <p class="text-center text-gray-600 mb-8">تُظهر هذه الحقول فقط إذا كان نوع الحساب "شركة" أو "مكتب"</p>
                    
                    <div id="company-fields" style="display: none;">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="company_name" class="block text-gray-700 font-semibold mb-2">اسم الشركة *</label>
                                <input type="text" 
                                       id="company_name" 
                                       name="company_name" 
                                       value="{{ old('company_name') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="أدخل اسم الشركة">
                            </div>
                            
                            <div>
                                <label for="commercial_register" class="block text-gray-700 font-semibold mb-2">السجل التجاري</label>
                                <input type="text" 
                                       id="commercial_register" 
                                       name="commercial_register" 
                                       value="{{ old('commercial_register') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="رقم السجل التجاري">
                                <p class="text-sm text-gray-500 mt-1">يُطلب لاحقاً عند التفعيل الكامل</p>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="tax_id" class="block text-gray-700 font-semibold mb-2">الرقم الضريبي</label>
                                <input type="text" 
                                       id="tax_id" 
                                       name="tax_id" 
                                       value="{{ old('tax_id') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="الرقم الضريبي أو VAT ID">
                                <p class="text-sm text-gray-500 mt-1">مهم للسعودية، المغرب، مصر...</p>
                            </div>
                            
                            <div>
                                <label for="website" class="block text-gray-700 font-semibold mb-2">موقع الشركة الإلكتروني</label>
                                <input type="url" 
                                       id="website" 
                                       name="website" 
                                       value="{{ old('website') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" 
                                       placeholder="https://example.com">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="employees_count" class="block text-gray-700 font-semibold mb-2">عدد الموظفين</label>
                                <select id="employees_count" 
                                        name="employees_count" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="">اختر عدد الموظفين</option>
                                    <option value="1-5">1-5 موظفين</option>
                                    <option value="6-10">6-10 موظفين</option>
                                    <option value="11-25">11-25 موظف</option>
                                    <option value="26-50">26-50 موظف</option>
                                    <option value="51-100">51-100 موظف</option>
                                    <option value="100+">أكثر من 100 موظف</option>
                                </select>
                                <p class="text-sm text-gray-500 mt-1">لتحديد الخطة المناسبة</p>
                            </div>
                            
                            <div>
                                <label for="industry" class="block text-gray-700 font-semibold mb-2">القطاع</label>
                                <select id="industry" 
                                        name="industry" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="">اختر القطاع</option>
                                    <option value="trade">تجارة</option>
                                    <option value="software">برمجيات</option>
                                    <option value="construction">بناء</option>
                                    <option value="accounting">محاسبة</option>
                                    <option value="education">تعليم</option>
                                    <option value="healthcare">رعاية صحية</option>
                                    <option value="manufacturing">تصنيع</option>
                                    <option value="services">خدمات</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                        <label class="flex items-start">
                            <input type="checkbox" name="terms" class="mt-1 rounded border-gray-300 text-purple-600 shadow-sm focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50" required>
                            <span class="mr-3 text-gray-700">
                                أوافق على 
                                <a href="#" class="text-purple-600 hover:text-purple-800 underline">الشروط والأحكام</a>
                                و
                                <a href="#" class="text-purple-600 hover:text-purple-800 underline">سياسة الخصوصية</a>
                            </span>
                        </label>
                    </div>

                    <div class="flex justify-between mt-8">
                        <button type="button" onclick="prevStep()" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            <i class="fas fa-arrow-right mr-2"></i> السابق
                        </button>
                        <button type="submit" class="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                            إنشاء الحساب <i class="fas fa-check mr-2"></i>
                        </button>
                    </div>
                </div>
            </form>

            <!-- Social Registration -->
            <div class="mt-8 border-t pt-8">
                <div class="text-center mb-4">
                    <span class="text-gray-500">أو سجل باستخدام</span>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <button class="w-full flex items-center justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        <i class="fab fa-google text-red-500 ml-2"></i>
                        تسجيل الدخول عبر Google
                    </button>
                    <button class="w-full flex items-center justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        <i class="fab fa-microsoft text-blue-500 ml-2"></i>
                        تسجيل الدخول عبر Microsoft
                    </button>
                </div>
            </div>

            <!-- Login Link -->
            <div class="mt-8 text-center">
                <p class="text-gray-600">
                    لديك حساب بالفعل؟ 
                    <a href="{{ route('login') }}" class="text-purple-600 hover:text-purple-800 font-semibold">
                        سجل الدخول
                    </a>
                </p>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-6">
            <a href="{{ route('welcome') }}" class="text-white hover:text-gray-200 transition duration-300">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 3;

        // Show location modal on page load
        window.addEventListener('load', function() {
            if (!localStorage.getItem('selectedCountry')) {
                document.getElementById('location-modal').style.display = 'flex';
            }
        });

        // Select country function
        function selectCountry(countryCode) {
            localStorage.setItem('selectedCountry', countryCode);
            document.getElementById('location-modal').style.display = 'none';
            
            // Auto-select country in form
            const countrySelect = document.getElementById('country');
            if (countrySelect) {
                countrySelect.value = countryCode;
                updateCurrency();
            }
        }

        // Update currency based on selected country
        function updateCurrency() {
            const country = document.getElementById('country').value;
            const currencyInput = document.getElementById('currency');
            
            const currencies = {
                'MA': 'درهم مغربي (MAD)',
                'SA': 'ريال سعودي (SAR)',
                'AE': 'درهم إماراتي (AED)',
                'EG': 'جنيه مصري (EGP)',
                'TN': 'دينار تونسي (TND)',
                'DZ': 'دينار جزائري (DZD)',
                'JO': 'دينار أردني (JOD)',
                'KW': 'دينار كويتي (KWD)',
                'QA': 'ريال قطري (QAR)'
            };
            
            currencyInput.value = currencies[country] || '';
        }

        // Toggle password visibility
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = passwordInput.nextElementSibling.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Next step function
        function nextStep() {
            if (validateStep(currentStep)) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`step-indicator-${currentStep}`).classList.add('completed');
                document.getElementById(`step-indicator-${currentStep}`).classList.remove('active');
                
                // Show next step
                currentStep++;
                document.getElementById(`step-${currentStep}`).classList.add('active');
                document.getElementById(`step-indicator-${currentStep}`).classList.add('active');
                
                // Show/hide company fields based on account type
                if (currentStep === 3) {
                    toggleCompanyFields();
                }
            }
        }

        // Previous step function
        function prevStep() {
            // Hide current step
            document.getElementById(`step-${currentStep}`).classList.remove('active');
            document.getElementById(`step-indicator-${currentStep}`).classList.remove('active');
            
            // Show previous step
            currentStep--;
            document.getElementById(`step-${currentStep}`).classList.add('active');
            document.getElementById(`step-indicator-${currentStep}`).classList.add('active');
            document.getElementById(`step-indicator-${currentStep}`).classList.remove('completed');
        }

        // Validate step
        function validateStep(step) {
            const stepElement = document.getElementById(`step-${step}`);
            const requiredFields = stepElement.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }
            }
            
            // Additional validation for step 1
            if (step === 1) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('password_confirmation').value;
                
                if (password.length < 10) {
                    alert('كلمة المرور يجب أن تكون 10 أحرف على الأقل');
                    return false;
                }
                
                if (password !== confirmPassword) {
                    alert('كلمة المرور وتأكيد كلمة المرور غير متطابقين');
                    return false;
                }
            }
            
            return true;
        }

        // Toggle company fields based on account type
        function toggleCompanyFields() {
            const accountType = document.querySelector('input[name="account_type"]:checked');
            const companyFields = document.getElementById('company-fields');
            const companyName = document.getElementById('company_name');
            
            if (accountType && (accountType.value === 'sme' || accountType.value === 'enterprise' || accountType.value === 'accounting_office')) {
                companyFields.style.display = 'block';
                companyName.required = true;
            } else {
                companyFields.style.display = 'none';
                companyName.required = false;
            }
        }

        // Listen for account type changes
        document.addEventListener('change', function(e) {
            if (e.target.name === 'account_type') {
                toggleCompanyFields();
            }
        });

        // Get plan from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const selectedPlan = urlParams.get('plan');
        if (selectedPlan) {
            // You can use this to pre-select a plan or show plan-specific information
            console.log('Selected plan:', selectedPlan);
        }
    </script>
</body>
</html>
