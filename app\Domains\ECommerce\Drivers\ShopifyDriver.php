<?php

namespace App\Domains\ECommerce\Drivers;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Exceptions\ECommerceApiException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * برنامج تشغيل Shopify
 * يدير التكامل مع منصة Shopify
 */
class ShopifyDriver extends AbstractECommercePlatformDriver
{
    protected string $platformName = 'shopify';
    protected string $apiVersion = '2023-10';
    protected int $maxPageSize = 250;
    protected int $defaultPageSize = 50;
    protected int $maxRequestsPerSecond = 2;
    protected int $maxRequestsPerMinute = 40;

    /**
     * الحصول على نقطة نهاية الاختبار
     */
    protected function getTestEndpoint(): string
    {
        return 'shop.json';
    }

    /**
     * الحصول على URL الأساسي للAPI
     */
    public function getApiBaseUrl(ECommerceIntegration $integration): string
    {
        $shopDomain = $integration->authentication_config['shop_domain'] ?? '';
        return "https://{$shopDomain}/admin/api/{$this->apiVersion}";
    }

    /**
     * الحصول على headers المصادقة
     */
    protected function getAuthHeaders(ECommerceIntegration $integration): array
    {
        $accessToken = $integration->authentication_config['access_token'] ?? '';

        return [
            'X-Shopify-Access-Token' => $accessToken,
        ];
    }

    /**
     * الحصول على معلومات المتجر
     */
    public function getStoreInfo(ECommerceIntegration $integration): array
    {
        $response = $this->makeApiRequest('GET', 'shop.json', [], $integration);
        return $response['shop'] ?? [];
    }

    /**
     * جلب المنتجات من Shopify
     */
    public function getProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'status' => $options['status'] ?? 'any',
        ];

        if (isset($options['since_id'])) {
            $params['since_id'] = $options['since_id'];
        }

        if (isset($options['created_at_min'])) {
            $params['created_at_min'] = $options['created_at_min'];
        }

        if (isset($options['updated_at_min'])) {
            $params['updated_at_min'] = $options['updated_at_min'];
        }

        $response = $this->makeApiRequest('GET', 'products.json', $params, $integration);
        return $response['products'] ?? [];
    }

    /**
     * جلب منتج واحد من Shopify
     */
    public function getProduct(ECommerceIntegration $integration, string $productId): array
    {
        $response = $this->makeApiRequest('GET', "products/{$productId}.json", [], $integration);
        return $response['product'] ?? [];
    }

    /**
     * إنشاء منتج في Shopify
     */
    public function createProduct(ECommerceIntegration $integration, array $productData): array
    {
        $data = ['product' => $this->transformToExternalFormat($productData, 'product')];
        $response = $this->makeApiRequest('POST', 'products.json', $data, $integration);
        return $response['product'] ?? [];
    }

    /**
     * تحديث منتج في Shopify
     */
    public function updateProduct(ECommerceIntegration $integration, string $productId, array $productData): array
    {
        $data = ['product' => $this->transformToExternalFormat($productData, 'product')];
        $response = $this->makeApiRequest('PUT', "products/{$productId}.json", $data, $integration);
        return $response['product'] ?? [];
    }

    /**
     * حذف منتج من Shopify
     */
    public function deleteProduct(ECommerceIntegration $integration, string $productId): array
    {
        $this->makeApiRequest('DELETE', "products/{$productId}.json", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب الطلبات من Shopify
     */
    public function getOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
            'status' => $options['status'] ?? 'any',
            'financial_status' => $options['financial_status'] ?? 'any',
            'fulfillment_status' => $options['fulfillment_status'] ?? 'any',
        ];

        if (isset($options['since_id'])) {
            $params['since_id'] = $options['since_id'];
        }

        if (isset($options['created_at_min'])) {
            $params['created_at_min'] = $options['created_at_min'];
        }

        if (isset($options['updated_at_min'])) {
            $params['updated_at_min'] = $options['updated_at_min'];
        }

        $response = $this->makeApiRequest('GET', 'orders.json', $params, $integration);
        return $response['orders'] ?? [];
    }

    /**
     * جلب طلب واحد من Shopify
     */
    public function getOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $response = $this->makeApiRequest('GET', "orders/{$orderId}.json", [], $integration);
        return $response['order'] ?? [];
    }

    /**
     * إنشاء طلب في Shopify
     */
    public function createOrder(ECommerceIntegration $integration, array $orderData): array
    {
        $data = ['order' => $this->transformToExternalFormat($orderData, 'order')];
        $response = $this->makeApiRequest('POST', 'orders.json', $data, $integration);
        return $response['order'] ?? [];
    }

    /**
     * تحديث طلب في Shopify
     */
    public function updateOrder(ECommerceIntegration $integration, string $orderId, array $orderData): array
    {
        $data = ['order' => $this->transformToExternalFormat($orderData, 'order')];
        $response = $this->makeApiRequest('PUT', "orders/{$orderId}.json", $data, $integration);
        return $response['order'] ?? [];
    }

    /**
     * إلغاء طلب في Shopify
     */
    public function cancelOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $data = ['reason' => 'other'];
        $response = $this->makeApiRequest('POST', "orders/{$orderId}/cancel.json", $data, $integration);
        return $response['order'] ?? [];
    }

    /**
     * جلب العملاء من Shopify
     */
    public function getCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'limit' => $options['limit'] ?? $this->defaultPageSize,
        ];

        if (isset($options['since_id'])) {
            $params['since_id'] = $options['since_id'];
        }

        if (isset($options['created_at_min'])) {
            $params['created_at_min'] = $options['created_at_min'];
        }

        if (isset($options['updated_at_min'])) {
            $params['updated_at_min'] = $options['updated_at_min'];
        }

        $response = $this->makeApiRequest('GET', 'customers.json', $params, $integration);
        return $response['customers'] ?? [];
    }

    /**
     * جلب عميل واحد من Shopify
     */
    public function getCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $response = $this->makeApiRequest('GET', "customers/{$customerId}.json", [], $integration);
        return $response['customer'] ?? [];
    }

    /**
     * إنشاء عميل في Shopify
     */
    public function createCustomer(ECommerceIntegration $integration, array $customerData): array
    {
        $data = ['customer' => $this->transformToExternalFormat($customerData, 'customer')];
        $response = $this->makeApiRequest('POST', 'customers.json', $data, $integration);
        return $response['customer'] ?? [];
    }

    /**
     * تحديث عميل في Shopify
     */
    public function updateCustomer(ECommerceIntegration $integration, string $customerId, array $customerData): array
    {
        $data = ['customer' => $this->transformToExternalFormat($customerData, 'customer')];
        $response = $this->makeApiRequest('PUT', "customers/{$customerId}.json", $data, $integration);
        return $response['customer'] ?? [];
    }

    /**
     * حذف عميل من Shopify
     */
    public function deleteCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $this->makeApiRequest('DELETE', "customers/{$customerId}.json", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب الفئات من Shopify
     */
    public function getCategories(ECommerceIntegration $integration, array $options = []): array
    {
        // Shopify uses collections instead of categories
        $response = $this->makeApiRequest('GET', 'collections.json', [], $integration);
        return $response['collections'] ?? [];
    }

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $products = $this->getProducts($integration, $options);

        return [
            'total' => count($products),
            'processed' => count($products),
            'successful' => count($products),
            'failed' => 0,
            'data' => $products,
        ];
    }

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $orders = $this->getOrders($integration, $options);

        return [
            'total' => count($orders),
            'processed' => count($orders),
            'successful' => count($orders),
            'failed' => 0,
            'data' => $orders,
        ];
    }

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $customers = $this->getCustomers($integration, $options);

        return [
            'total' => count($customers),
            'processed' => count($customers),
            'successful' => count($customers),
            'failed' => 0,
            'data' => $customers,
        ];
    }

    /**
     * مزامنة الفئات
     */
    public function syncCategories(ECommerceIntegration $integration, array $options = []): array
    {
        $categories = $this->getCategories($integration, $options);

        return [
            'total' => count($categories),
            'processed' => count($categories),
            'successful' => count($categories),
            'failed' => 0,
            'data' => $categories,
        ];
    }

    /**
     * معالجة webhook من Shopify
     */
    public function processWebhook(ECommerceIntegration $integration, array $payload, array $headers = []): array
    {
        $topic = $headers['x-shopify-topic'][0] ?? '';

        return [
            'success' => true,
            'topic' => $topic,
            'data' => $payload,
        ];
    }

    /**
     * التحقق من توقيع webhook
     */
    public function verifyWebhookSignature(Request $request, ECommerceIntegration $integration): bool
    {
        $signature = $request->header('X-Shopify-Hmac-Sha256');
        $webhookSecret = $integration->authentication_config['webhook_secret'] ?? '';

        if (!$signature || !$webhookSecret) {
            return false;
        }

        $calculatedSignature = base64_encode(hash_hmac('sha256', $request->getContent(), $webhookSecret, true));

        return hash_equals($signature, $calculatedSignature);
    }

    /**
     * تحويل البيانات إلى تنسيق Shopify
     */
    public function transformToExternalFormat(array $data, string $entityType): array
    {
        switch ($entityType) {
            case 'product':
                return $this->transformProductToShopify($data);
            case 'order':
                return $this->transformOrderToShopify($data);
            case 'customer':
                return $this->transformCustomerToShopify($data);
            default:
                return $data;
        }
    }

    /**
     * تحويل البيانات من تنسيق Shopify
     */
    public function transformFromExternalFormat(array $data, string $entityType): array
    {
        switch ($entityType) {
            case 'product':
                return $this->transformProductFromShopify($data);
            case 'order':
                return $this->transformOrderFromShopify($data);
            case 'customer':
                return $this->transformCustomerFromShopify($data);
            default:
                return $data;
        }
    }

    /**
     * تحويل المنتج إلى تنسيق Shopify
     */
    protected function transformProductToShopify(array $data): array
    {
        return [
            'title' => $data['name'] ?? '',
            'body_html' => $data['description'] ?? '',
            'vendor' => $data['vendor'] ?? '',
            'product_type' => $data['product_type'] ?? '',
            'tags' => is_array($data['tags'] ?? null) ? implode(',', $data['tags']) : ($data['tags'] ?? ''),
            'status' => $data['status'] ?? 'active',
            'variants' => [
                [
                    'sku' => $data['sku'] ?? '',
                    'price' => $data['price'] ?? 0,
                    'compare_at_price' => $data['compare_price'] ?? null,
                    'inventory_quantity' => $data['inventory_quantity'] ?? 0,
                    'weight' => $data['weight'] ?? 0,
                    'weight_unit' => $data['weight_unit'] ?? 'kg',
                ]
            ],
        ];
    }

    /**
     * تحويل المنتج من تنسيق Shopify
     */
    protected function transformProductFromShopify(array $data): array
    {
        $variant = $data['variants'][0] ?? [];

        return [
            'external_id' => (string) $data['id'],
            'name' => $data['title'] ?? '',
            'description' => $data['body_html'] ?? '',
            'sku' => $variant['sku'] ?? '',
            'price' => (float) ($variant['price'] ?? 0),
            'compare_price' => $variant['compare_at_price'] ? (float) $variant['compare_at_price'] : null,
            'inventory_quantity' => (int) ($variant['inventory_quantity'] ?? 0),
            'weight' => (float) ($variant['weight'] ?? 0),
            'weight_unit' => $variant['weight_unit'] ?? 'kg',
            'vendor' => $data['vendor'] ?? '',
            'product_type' => $data['product_type'] ?? '',
            'tags' => $data['tags'] ? explode(',', $data['tags']) : [],
            'status' => $data['status'] ?? 'active',
            'images' => $data['images'] ?? [],
            'featured_image' => $data['image']['src'] ?? null,
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
        ];
    }

    /**
     * تحويل الطلب من تنسيق Shopify
     */
    protected function transformOrderFromShopify(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'order_number' => $data['order_number'] ?? $data['name'] ?? '',
            'email' => $data['email'] ?? '',
            'total' => (float) ($data['total_price'] ?? 0),
            'subtotal' => (float) ($data['subtotal_price'] ?? 0),
            'total_tax' => (float) ($data['total_tax'] ?? 0),
            'total_discounts' => (float) ($data['total_discounts'] ?? 0),
            'currency' => $data['currency'] ?? 'USD',
            'financial_status' => $data['financial_status'] ?? '',
            'fulfillment_status' => $data['fulfillment_status'] ?? '',
            'billing_address' => $data['billing_address'] ?? [],
            'shipping_address' => $data['shipping_address'] ?? [],
            'line_items' => $data['line_items'] ?? [],
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
        ];
    }

    /**
     * تحويل العميل من تنسيق Shopify
     */
    protected function transformCustomerFromShopify(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'email' => $data['email'] ?? '',
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'phone' => $data['phone'] ?? '',
            'total_spent' => (float) ($data['total_spent'] ?? 0),
            'orders_count' => (int) ($data['orders_count'] ?? 0),
            'state' => $data['state'] ?? '',
            'tags' => $data['tags'] ? explode(',', $data['tags']) : [],
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
        ];
    }

    /**
     * الحصول على الحقول المطلوبة
     */
    public function getRequiredFields(): array
    {
        return [
            'shop_domain',
            'access_token',
        ];
    }

    /**
     * الحصول على الحقول الاختيارية
     */
    public function getOptionalFields(): array
    {
        return [
            'webhook_secret',
            'api_version',
        ];
    }

    /**
     * الحصول على العمليات المدعومة
     */
    public function getSupportedOperations(): array
    {
        return [
            'products.read', 'products.write',
            'orders.read', 'orders.write',
            'customers.read', 'customers.write',
            'inventory.read', 'inventory.write',
            'webhooks.read', 'webhooks.write',
        ];
    }

    /**
     * الحصول على أنواع المزامنة المدعومة
     */
    public function getSupportedSyncTypes(): array
    {
        return ['full', 'incremental', 'real-time'];
    }

    /**
     * الحصول على أنواع الأحداث المدعومة
     */
    public function getSupportedEventTypes(): array
    {
        return [
            'products/create', 'products/update', 'products/delete',
            'orders/create', 'orders/updated', 'orders/paid', 'orders/cancelled',
            'customers/create', 'customers/update', 'customers/delete',
            'inventory_levels/update',
        ];
    }

    /**
     * الحصول على تنسيقات البيانات المدعومة
     */
    public function getSupportedDataFormats(): array
    {
        return ['json'];
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    public function getDefaultConfiguration(): array
    {
        return [
            'api_version' => $this->apiVersion,
            'timeout' => 30,
            'max_retries' => 3,
            'page_size' => $this->defaultPageSize,
        ];
    }

    /**
     * جلب الفئات من Shopify (Collections)
     */
    public function getCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $response = $this->makeApiRequest('GET', "collections/{$categoryId}.json", [], $integration);
        return $response['collection'] ?? [];
    }

    /**
     * إنشاء فئة في Shopify
     */
    public function createCategory(ECommerceIntegration $integration, array $categoryData): array
    {
        $data = ['collection' => $this->transformCategoryToShopify($categoryData)];
        $response = $this->makeApiRequest('POST', 'collections.json', $data, $integration);
        return $response['collection'] ?? [];
    }

    /**
     * تحديث فئة في Shopify
     */
    public function updateCategory(ECommerceIntegration $integration, string $categoryId, array $categoryData): array
    {
        $data = ['collection' => $this->transformCategoryToShopify($categoryData)];
        $response = $this->makeApiRequest('PUT', "collections/{$categoryId}.json", $data, $integration);
        return $response['collection'] ?? [];
    }

    /**
     * حذف فئة من Shopify
     */
    public function deleteCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $this->makeApiRequest('DELETE', "collections/{$categoryId}.json", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب المخزون من Shopify
     */
    public function getInventory(ECommerceIntegration $integration, array $options = []): array
    {
        $response = $this->makeApiRequest('GET', 'inventory_levels.json', $options, $integration);
        return $response['inventory_levels'] ?? [];
    }

    /**
     * تحديث المخزون في Shopify
     */
    public function updateInventory(ECommerceIntegration $integration, string $productId, int $quantity): array
    {
        // Get variant first
        $product = $this->getProduct($integration, $productId);
        $variantId = $product['variants'][0]['id'] ?? null;

        if (!$variantId) {
            throw new ECommerceApiException('Product variant not found');
        }

        $data = [
            'inventory_item_id' => $variantId,
            'available' => $quantity,
        ];

        $response = $this->makeApiRequest('POST', 'inventory_levels/set.json', $data, $integration);
        return $response;
    }

    /**
     * جلب الكوبونات من Shopify
     */
    public function getCoupons(ECommerceIntegration $integration, array $options = []): array
    {
        $response = $this->makeApiRequest('GET', 'discount_codes.json', $options, $integration);
        return $response['discount_codes'] ?? [];
    }

    /**
     * إنشاء كوبون في Shopify
     */
    public function createCoupon(ECommerceIntegration $integration, array $couponData): array
    {
        $data = ['discount_code' => $this->transformCouponToShopify($couponData)];
        $response = $this->makeApiRequest('POST', 'discount_codes.json', $data, $integration);
        return $response['discount_code'] ?? [];
    }

    /**
     * تحديث كوبون في Shopify
     */
    public function updateCoupon(ECommerceIntegration $integration, string $couponId, array $couponData): array
    {
        $data = ['discount_code' => $this->transformCouponToShopify($couponData)];
        $response = $this->makeApiRequest('PUT', "discount_codes/{$couponId}.json", $data, $integration);
        return $response['discount_code'] ?? [];
    }

    /**
     * حذف كوبون من Shopify
     */
    public function deleteCoupon(ECommerceIntegration $integration, string $couponId): array
    {
        $this->makeApiRequest('DELETE', "discount_codes/{$couponId}.json", [], $integration);
        return ['success' => true];
    }

    /**
     * جلب التقارير من Shopify
     */
    public function getReports(ECommerceIntegration $integration, array $options = []): array
    {
        $reports = [];

        // Sales report
        $reports['sales'] = $this->getSalesReport($integration, $options);

        // Products report
        $reports['products'] = $this->getProductsReport($integration, $options);

        // Customers report
        $reports['customers'] = $this->getCustomersReport($integration, $options);

        return $reports;
    }

    /**
     * جلب تقرير المبيعات
     */
    public function getSalesReport(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'created_at_min' => $options['date_from'] ?? now()->subDays(30)->toISOString(),
            'created_at_max' => $options['date_to'] ?? now()->toISOString(),
            'financial_status' => 'paid',
        ];

        $orders = $this->getOrders($integration, $params);

        $totalSales = 0;
        $totalOrders = count($orders);
        $totalItems = 0;

        foreach ($orders as $order) {
            $totalSales += (float) $order['total_price'];
            $totalItems += count($order['line_items'] ?? []);
        }

        return [
            'total_sales' => $totalSales,
            'total_orders' => $totalOrders,
            'total_items' => $totalItems,
            'average_order_value' => $totalOrders > 0 ? $totalSales / $totalOrders : 0,
            'period' => [
                'from' => $params['created_at_min'],
                'to' => $params['created_at_max'],
            ],
        ];
    }

    /**
     * جلب تقرير المنتجات
     */
    public function getProductsReport(ECommerceIntegration $integration, array $options = []): array
    {
        $products = $this->getProducts($integration, $options);

        $totalProducts = count($products);
        $publishedProducts = 0;
        $outOfStockProducts = 0;
        $totalValue = 0;

        foreach ($products as $product) {
            if ($product['status'] === 'active') {
                $publishedProducts++;
            }

            $variant = $product['variants'][0] ?? [];
            if (($variant['inventory_quantity'] ?? 0) <= 0) {
                $outOfStockProducts++;
            }

            $totalValue += (float) ($variant['price'] ?? 0) * ($variant['inventory_quantity'] ?? 0);
        }

        return [
            'total_products' => $totalProducts,
            'published_products' => $publishedProducts,
            'draft_products' => $totalProducts - $publishedProducts,
            'out_of_stock_products' => $outOfStockProducts,
            'total_inventory_value' => $totalValue,
        ];
    }

    /**
     * جلب تقرير العملاء
     */
    public function getCustomersReport(ECommerceIntegration $integration, array $options = []): array
    {
        $customers = $this->getCustomers($integration, $options);

        $totalCustomers = count($customers);
        $totalSpent = 0;
        $totalOrders = 0;

        foreach ($customers as $customer) {
            $totalSpent += (float) ($customer['total_spent'] ?? 0);
            $totalOrders += (int) ($customer['orders_count'] ?? 0);
        }

        return [
            'total_customers' => $totalCustomers,
            'total_spent' => $totalSpent,
            'total_orders' => $totalOrders,
            'average_spent_per_customer' => $totalCustomers > 0 ? $totalSpent / $totalCustomers : 0,
            'average_orders_per_customer' => $totalCustomers > 0 ? $totalOrders / $totalCustomers : 0,
        ];
    }
}
