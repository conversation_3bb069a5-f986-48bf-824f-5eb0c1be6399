<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول تخطيطات لوحة التحكم
        Schema::create('dashboard_layouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->json('layout_data'); // بيانات التخطيط والودجات
            $table->boolean('is_active')->default(false);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_shared')->default(false);
            $table->json('shared_with_roles')->nullable(); // الأدوار المشاركة معها
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();
            
            $table->index(['user_id', 'is_active']);
            $table->index(['is_shared', 'shared_with_roles']);
        });

        // جدول الودجات المتاحة
        Schema::create('dashboard_widgets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type')->unique();
            $table->string('component'); // اسم المكون في Vue.js
            $table->text('description')->nullable();
            $table->string('category');
            $table->json('required_permissions')->nullable(); // الصلاحيات المطلوبة
            $table->json('default_size'); // الحجم الافتراضي {w: 4, h: 3}
            $table->json('min_size')->nullable(); // الحد الأدنى للحجم
            $table->json('max_size')->nullable(); // الحد الأقصى للحجم
            $table->json('configuration_schema')->nullable(); // مخطط التكوين
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['category', 'is_active']);
            $table->index('sort_order');
        });

        // جدول إعدادات الودجات للمستخدمين
        Schema::create('user_widget_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('widget_type');
            $table->json('configuration'); // إعدادات الودجت المخصصة
            $table->json('position'); // الموقع في الشبكة
            $table->json('size'); // الحجم
            $table->boolean('is_visible')->default(true);
            $table->timestamps();
            
            $table->unique(['user_id', 'widget_type']);
            $table->index(['user_id', 'is_visible']);
        });

        // جدول الإشعارات
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type');
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // بيانات إضافية
            $table->string('action_url')->nullable(); // رابط الإجراء
            $table->boolean('is_read')->default(false);
            $table->boolean('is_critical')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'is_read']);
            $table->index(['type', 'is_critical']);
            $table->index('expires_at');
        });

        // جدول المهام
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->timestamp('due_date')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('estimated_hours')->nullable();
            $table->integer('actual_hours')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['assigned_to', 'status']);
            $table->index(['project_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index('priority');
        });

        // جدول الأحداث والتقويم
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->enum('type', ['meeting', 'deadline', 'reminder', 'holiday', 'other'])->default('other');
            $table->timestamp('start_date');
            $table->timestamp('end_date')->nullable();
            $table->boolean('is_all_day')->default(false);
            $table->string('location')->nullable();
            $table->json('attendees')->nullable(); // قائمة الحاضرين
            $table->string('meeting_url')->nullable(); // رابط الاجتماع
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->json('reminder_settings')->nullable(); // إعدادات التذكير
            $table->timestamps();
            
            $table->index(['user_id', 'start_date']);
            $table->index(['type', 'status']);
            $table->index('start_date');
        });

        // جدول KPIs (مؤشرات الأداء الرئيسية)
        Schema::create('kpis', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key')->unique();
            $table->text('description')->nullable();
            $table->string('category');
            $table->decimal('current_value', 15, 2);
            $table->decimal('target_value', 15, 2)->nullable();
            $table->decimal('previous_value', 15, 2)->nullable();
            $table->string('unit')->nullable(); // الوحدة (%, SAR, عدد، إلخ)
            $table->enum('trend', ['up', 'down', 'stable'])->nullable();
            $table->date('period_start');
            $table->date('period_end');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['category', 'period_end']);
            $table->index(['key', 'period_end']);
        });

        // جدول تتبع النشاط
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action');
            $table->string('model_type')->nullable();
            $table->unsignedBigInteger('model_id')->nullable();
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index(['model_type', 'model_id']);
            $table->index('action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
        Schema::dropIfExists('kpis');
        Schema::dropIfExists('calendar_events');
        Schema::dropIfExists('tasks');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('user_widget_settings');
        Schema::dropIfExists('dashboard_widgets');
        Schema::dropIfExists('dashboard_layouts');
    }
};
