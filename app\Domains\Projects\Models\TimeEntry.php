<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج تسجيل الوقت - Advanced Time Tracking
 * يدعم تتبع الوقت المتقدم مع الفوترة والتحليلات
 */
class TimeEntry extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'task_id',
        'employee_id',
        'start_time',
        'end_time',
        'hours',
        'description',
        'is_billable',
        'hourly_rate',
        'total_cost',
        'status',
        'approved_by',
        'approved_at',
        'invoice_id',
        'invoiced_at',
        'tags',
        'location',
        'device_info',
        'screenshots',
        'activity_level',
        'break_time',
        'overtime_hours',
        'metadata',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'hours' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'break_time' => 'decimal:2',
        'activity_level' => 'decimal:2',
        'is_billable' => 'boolean',
        'approved_at' => 'datetime',
        'invoiced_at' => 'datetime',
        'tags' => 'array',
        'device_info' => 'array',
        'screenshots' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المهمة
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * العلاقة مع الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class);
    }

    /**
     * العلاقة مع الموافق
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'approved_by');
    }

    /**
     * العلاقة مع الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\Accounting\Models\Invoice::class);
    }

    /**
     * الحصول على المدة المنسقة
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalMinutes = $this->hours * 60;
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * الحصول على التكلفة المحسوبة
     */
    public function getCalculatedCostAttribute(): float
    {
        return $this->hours * $this->hourly_rate;
    }

    /**
     * التحقق من كون التسجيل نشط
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'RUNNING' && !$this->end_time;
    }

    /**
     * التحقق من الموافقة
     */
    public function getIsApprovedAttribute(): bool
    {
        return $this->status === 'APPROVED';
    }

    /**
     * التحقق من الفوترة
     */
    public function getIsInvoicedAttribute(): bool
    {
        return !is_null($this->invoice_id);
    }

    /**
     * بدء تسجيل الوقت
     */
    public static function startTimer(int $projectId, int $employeeId, int $taskId = null, string $description = null): self
    {
        // إيقاف أي مؤقت نشط للموظف
        self::stopActiveTimers($employeeId);

        $timeEntry = self::create([
            'project_id' => $projectId,
            'task_id' => $taskId,
            'employee_id' => $employeeId,
            'start_time' => now(),
            'description' => $description,
            'status' => 'RUNNING',
            'device_info' => [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'platform' => self::detectPlatform(),
            ],
        ]);

        // تسجيل النشاط
        $timeEntry->logActivity('timer_started');

        return $timeEntry;
    }

    /**
     * إيقاف المؤقت
     */
    public function stopTimer(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $endTime = now();
        $hours = $this->start_time->diffInMinutes($endTime) / 60;

        // حساب الساعات الإضافية
        $overtimeHours = $this->calculateOvertimeHours($hours);

        $this->update([
            'end_time' => $endTime,
            'hours' => $hours,
            'overtime_hours' => $overtimeHours,
            'status' => 'STOPPED',
            'hourly_rate' => $this->hourly_rate ?? $this->getDefaultHourlyRate(),
        ]);

        // حساب التكلفة
        $this->calculateTotalCost();

        // تحديث ساعات المهمة
        if ($this->task) {
            $this->task->increment('actual_hours', $hours);
        }

        $this->logActivity('timer_stopped', ['hours' => $hours]);

        return true;
    }

    /**
     * إيقاف جميع المؤقتات النشطة للموظف
     */
    public static function stopActiveTimers(int $employeeId): void
    {
        $activeTimers = self::where('employee_id', $employeeId)
                           ->where('status', 'RUNNING')
                           ->get();

        foreach ($activeTimers as $timer) {
            $timer->stopTimer();
        }
    }

    /**
     * تحديث الوصف
     */
    public function updateDescription(string $description): void
    {
        $this->update(['description' => $description]);
        $this->logActivity('description_updated');
    }

    /**
     * تحديد كقابل للفوترة
     */
    public function markAsBillable(bool $billable = true, float $hourlyRate = null): void
    {
        $this->update([
            'is_billable' => $billable,
            'hourly_rate' => $hourlyRate ?? $this->hourly_rate,
        ]);

        $this->calculateTotalCost();
        $this->logActivity($billable ? 'marked_billable' : 'marked_non_billable');
    }

    /**
     * الموافقة على التسجيل
     */
    public function approve(int $approvedBy): bool
    {
        if ($this->status !== 'STOPPED') {
            return false;
        }

        $this->update([
            'status' => 'APPROVED',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        $this->logActivity('approved', ['approved_by' => $approvedBy]);

        // إشعار الموظف
        $this->employee->notify(new \App\Notifications\TimeEntryApprovedNotification($this));

        return true;
    }

    /**
     * رفض التسجيل
     */
    public function reject(int $rejectedBy, string $reason = null): bool
    {
        $this->update([
            'status' => 'REJECTED',
            'metadata' => array_merge($this->metadata ?? [], [
                'rejected_by' => $rejectedBy,
                'rejected_at' => now(),
                'rejection_reason' => $reason,
            ]),
        ]);

        $this->logActivity('rejected', [
            'rejected_by' => $rejectedBy,
            'reason' => $reason,
        ]);

        // إشعار الموظف
        $this->employee->notify(new \App\Notifications\TimeEntryRejectedNotification($this, $reason));

        return true;
    }

    /**
     * إضافة للفاتورة
     */
    public function addToInvoice(int $invoiceId): void
    {
        $this->update([
            'invoice_id' => $invoiceId,
            'invoiced_at' => now(),
        ]);

        $this->logActivity('invoiced', ['invoice_id' => $invoiceId]);
    }

    /**
     * حساب التكلفة الإجمالية
     */
    protected function calculateTotalCost(): void
    {
        $regularCost = $this->hours * $this->hourly_rate;
        $overtimeCost = $this->overtime_hours * ($this->hourly_rate * 1.5); // 50% إضافي للساعات الإضافية
        
        $this->update(['total_cost' => $regularCost + $overtimeCost]);
    }

    /**
     * حساب الساعات الإضافية
     */
    protected function calculateOvertimeHours(float $totalHours): float
    {
        $standardWorkDay = 8; // 8 ساعات عمل قياسية
        return max(0, $totalHours - $standardWorkDay);
    }

    /**
     * الحصول على المعدل الافتراضي للساعة
     */
    protected function getDefaultHourlyRate(): float
    {
        // من إعدادات الموظف أو المشروع
        return $this->employee->hourly_rate ?? 
               $this->project->hourly_rate ?? 
               config('projects.default_hourly_rate', 50);
    }

    /**
     * كشف المنصة
     */
    protected static function detectPlatform(): string
    {
        $userAgent = request()->userAgent();
        
        if (str_contains($userAgent, 'Mobile')) {
            return 'Mobile';
        } elseif (str_contains($userAgent, 'Windows')) {
            return 'Windows';
        } elseif (str_contains($userAgent, 'Mac')) {
            return 'Mac';
        } elseif (str_contains($userAgent, 'Linux')) {
            return 'Linux';
        }
        
        return 'Unknown';
    }

    /**
     * إضافة لقطة شاشة
     */
    public function addScreenshot(string $filePath, array $metadata = []): void
    {
        $screenshots = $this->screenshots ?? [];
        $screenshots[] = [
            'file_path' => $filePath,
            'timestamp' => now(),
            'metadata' => $metadata,
        ];

        $this->update(['screenshots' => $screenshots]);
    }

    /**
     * تحديث مستوى النشاط
     */
    public function updateActivityLevel(float $level): void
    {
        $this->update(['activity_level' => $level]);
    }

    /**
     * إضافة وقت استراحة
     */
    public function addBreakTime(float $minutes): void
    {
        $this->increment('break_time', $minutes / 60);
    }

    /**
     * الحصول على إحصائيات التسجيل
     */
    public function getStatistics(): array
    {
        return [
            'total_hours' => $this->hours,
            'billable_hours' => $this->is_billable ? $this->hours : 0,
            'overtime_hours' => $this->overtime_hours,
            'break_time' => $this->break_time,
            'activity_level' => $this->activity_level,
            'cost' => $this->total_cost,
            'screenshots_count' => count($this->screenshots ?? []),
        ];
    }

    /**
     * تصدير التسجيل
     */
    public function toExportArray(): array
    {
        return [
            'date' => $this->start_time->format('Y-m-d'),
            'employee' => $this->employee->name,
            'project' => $this->project->name,
            'task' => $this->task?->title,
            'start_time' => $this->start_time->format('H:i'),
            'end_time' => $this->end_time?->format('H:i'),
            'duration' => $this->formatted_duration,
            'description' => $this->description,
            'is_billable' => $this->is_billable ? 'Yes' : 'No',
            'hourly_rate' => $this->hourly_rate,
            'total_cost' => $this->total_cost,
            'status' => $this->status,
        ];
    }

    /**
     * البحث في التسجيلات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('description', 'LIKE', "%{$search}%");
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة القابلة للفوترة
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * فلترة المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * فلترة غير المفوترة
     */
    public function scopeNotInvoiced($query)
    {
        return $query->whereNull('invoice_id');
    }

    /**
     * فلترة حسب الفترة
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_time', [$startDate, $endDate]);
    }

    /**
     * فلترة حسب الموظف
     */
    public function scopeForEmployee($query, int $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * فلترة حسب المشروع
     */
    public function scopeForProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * فلترة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'RUNNING');
    }

    /**
     * ترتيب حسب التاريخ
     */
    public function scopeOrderByDate($query, string $direction = 'desc')
    {
        return $query->orderBy('start_time', $direction);
    }
}
