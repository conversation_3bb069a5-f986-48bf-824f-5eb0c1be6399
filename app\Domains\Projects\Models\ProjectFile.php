<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج ملف المشروع - Project File Management
 * يدعم إدارة الملفات المتقدمة مع التحكم في الإصدارات
 */
class ProjectFile extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'name',
        'original_name',
        'file_path',
        'file_size',
        'mime_type',
        'extension',
        'uploaded_by',
        'updated_by',
        'version',
        'is_active',
        'permissions',
        'tags',
        'description',
        'category',
        'view_count',
        'download_count',
        'public_link_token',
        'public_link_expires_at',
        'public_link_permissions',
        'public_link_password',
        'extracted_text',
        'thumbnail_path',
        'preview_path',
        'virus_scan_status',
        'virus_scan_result',
        'metadata',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'version' => 'integer',
        'view_count' => 'integer',
        'download_count' => 'integer',
        'is_active' => 'boolean',
        'permissions' => 'array',
        'tags' => 'array',
        'public_link_expires_at' => 'datetime',
        'public_link_permissions' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع الرافع
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'uploaded_by');
    }

    /**
     * العلاقة مع المحدث
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'updated_by');
    }

    /**
     * العلاقة مع الإصدارات
     */
    public function versions(): HasMany
    {
        return $this->hasMany(FileVersion::class, 'file_id');
    }

    /**
     * العلاقة مع التعليقات
     */
    public function comments(): HasMany
    {
        return $this->hasMany(FileComment::class, 'file_id');
    }

    /**
     * العلاقة مع المشاهدات
     */
    public function views(): HasMany
    {
        return $this->hasMany(FileView::class, 'file_id');
    }

    /**
     * العلاقة مع المشاركين
     */
    public function sharedWith(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Domains\HR\Models\Employee::class,
            'file_shares',
            'file_id',
            'employee_id'
        )->withPivot([
            'permissions',
            'shared_by',
            'shared_at',
            'message'
        ])->withTimestamps();
    }

    /**
     * الحصول على حجم الملف المنسق
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * التحقق من إمكانية المعاينة
     */
    public function getCanPreviewAttribute(): bool
    {
        $previewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'css', 'js', 'json'];
        return in_array(strtolower($this->extension), $previewableTypes);
    }

    /**
     * الحصول على رابط التحميل
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('files.download', $this->id);
    }

    /**
     * الحصول على رابط المعاينة
     */
    public function getPreviewUrlAttribute(): ?string
    {
        return $this->can_preview ? route('files.preview', $this->id) : null;
    }

    /**
     * التحقق من انتهاء صلاحية الرابط العام
     */
    public function getIsPublicLinkExpiredAttribute(): bool
    {
        return $this->public_link_expires_at && $this->public_link_expires_at->isPast();
    }

    /**
     * إنشاء إصدار جديد
     */
    public function createVersion(int $createdBy, string $notes = null): FileVersion
    {
        // أرشفة الإصدار الحالي
        $this->versions()->update(['is_current' => false]);

        return $this->versions()->create([
            'version_number' => $this->version,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'created_by' => $createdBy,
            'notes' => $notes,
            'is_current' => true,
        ]);
    }

    /**
     * استعادة إصدار سابق
     */
    public function restoreVersion(int $versionId, int $restoredBy): bool
    {
        $version = $this->versions()->findOrFail($versionId);

        $this->update([
            'file_path' => $version->file_path,
            'file_size' => $version->file_size,
            'version' => $this->version + 1,
            'updated_by' => $restoredBy,
        ]);

        // إنشاء إصدار جديد للاستعادة
        $this->createVersion($restoredBy, "تم استعادة الإصدار {$version->version_number}");

        return true;
    }

    /**
     * إضافة تعليق
     */
    public function addComment(int $userId, string $comment, array $coordinates = null): FileComment
    {
        return $this->comments()->create([
            'user_id' => $userId,
            'comment' => $comment,
            'coordinates' => $coordinates,
            'is_resolved' => false,
        ]);
    }

    /**
     * تسجيل مشاهدة
     */
    public function recordView(int $userId): void
    {
        $this->increment('view_count');
        
        $this->views()->create([
            'user_id' => $userId,
            'viewed_at' => now(),
            'ip_address' => request()->ip(),
        ]);
    }

    /**
     * تسجيل تحميل
     */
    public function recordDownload(int $userId): void
    {
        $this->increment('download_count');
        
        $this->downloads()->create([
            'user_id' => $userId,
            'downloaded_at' => now(),
            'ip_address' => request()->ip(),
        ]);
    }

    /**
     * مشاركة الملف
     */
    public function shareWith(array $userIds, array $permissions, int $sharedBy, string $message = null): void
    {
        $shareData = [];
        foreach ($userIds as $userId) {
            $shareData[$userId] = [
                'permissions' => $permissions,
                'shared_by' => $sharedBy,
                'shared_at' => now(),
                'message' => $message,
            ];
        }

        $this->sharedWith()->syncWithoutDetaching($shareData);
    }

    /**
     * إنشاء رابط عام
     */
    public function createPublicLink(array $options = []): string
    {
        $token = \Str::random(32);
        
        $this->update([
            'public_link_token' => $token,
            'public_link_expires_at' => $options['expires_at'] ?? now()->addDays(7),
            'public_link_permissions' => $options['permissions'] ?? ['VIEW'],
            'public_link_password' => $options['password'] ?? null,
        ]);

        return route('files.public', ['token' => $token]);
    }

    /**
     * إلغاء الرابط العام
     */
    public function revokePublicLink(): void
    {
        $this->update([
            'public_link_token' => null,
            'public_link_expires_at' => null,
            'public_link_permissions' => null,
            'public_link_password' => null,
        ]);
    }

    /**
     * نسخ الملف
     */
    public function duplicate(int $newProjectId = null, int $copiedBy = null): self
    {
        $newFile = self::create([
            'project_id' => $newProjectId ?? $this->project_id,
            'name' => $this->name . ' (نسخة)',
            'original_name' => $this->original_name,
            'file_path' => $this->file_path, // سيتم نسخ الملف فعلياً
            'file_size' => $this->file_size,
            'mime_type' => $this->mime_type,
            'extension' => $this->extension,
            'uploaded_by' => $copiedBy ?? auth()->id(),
            'version' => 1,
            'is_active' => true,
            'permissions' => $this->permissions,
            'tags' => $this->tags,
            'description' => $this->description,
            'category' => $this->category,
        ]);

        // نسخ التعليقات إذا كانت مطلوبة
        foreach ($this->comments as $comment) {
            $newFile->addComment($comment->user_id, $comment->comment, $comment->coordinates);
        }

        return $newFile;
    }

    /**
     * البحث في الملفات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('extracted_text', 'LIKE', "%{$search}%")
              ->orWhereJsonContains('tags', $search);
        });
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $extension)
    {
        return $query->where('extension', $extension);
    }

    /**
     * فلترة الملفات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب الرافع
     */
    public function scopeUploadedBy($query, int $userId)
    {
        return $query->where('uploaded_by', $userId);
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('updated_at', 'desc');
    }

    /**
     * ترتيب حسب الأكثر مشاهدة
     */
    public function scopeMostViewed($query)
    {
        return $query->orderBy('view_count', 'desc');
    }
}
