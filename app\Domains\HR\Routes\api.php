<?php

use Illuminate\Support\Facades\Route;
use App\Domains\HR\Controllers\EmployeeController;
use App\Domains\HR\Controllers\PayrollController;
use App\Domains\HR\Controllers\AttendanceController;
use App\Domains\HR\Controllers\LeaveController;
use App\Domains\HR\Controllers\PerformanceController;
use App\Domains\HR\Controllers\RecruitmentController;
use App\Domains\HR\Controllers\TrainingController;
use App\Domains\HR\Controllers\BenefitsController;
use App\Domains\HR\Controllers\HRAnalyticsController;

/*
|--------------------------------------------------------------------------
| HR API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام الموارد البشرية
|
*/

// Employee Management
Route::apiResource('employees', EmployeeController::class);
Route::prefix('employees')->group(function () {
    Route::get('{employee}/profile', [EmployeeController::class, 'getProfile']);
    Route::put('{employee}/profile', [EmployeeController::class, 'updateProfile']);
    Route::get('{employee}/documents', [EmployeeController::class, 'getDocuments']);
    Route::post('{employee}/documents', [EmployeeController::class, 'uploadDocument']);
    Route::delete('{employee}/documents/{document}', [EmployeeController::class, 'deleteDocument']);
    Route::get('{employee}/contracts', [EmployeeController::class, 'getContracts']);
    Route::post('{employee}/contracts', [EmployeeController::class, 'createContract']);
    Route::put('{employee}/contracts/{contract}', [EmployeeController::class, 'updateContract']);
    Route::get('{employee}/emergency-contacts', [EmployeeController::class, 'getEmergencyContacts']);
    Route::post('{employee}/emergency-contacts', [EmployeeController::class, 'addEmergencyContact']);
    Route::put('{employee}/emergency-contacts/{contact}', [EmployeeController::class, 'updateEmergencyContact']);
    Route::delete('{employee}/emergency-contacts/{contact}', [EmployeeController::class, 'deleteEmergencyContact']);
    Route::get('{employee}/dependents', [EmployeeController::class, 'getDependents']);
    Route::post('{employee}/dependents', [EmployeeController::class, 'addDependent']);
    Route::put('{employee}/dependents/{dependent}', [EmployeeController::class, 'updateDependent']);
    Route::delete('{employee}/dependents/{dependent}', [EmployeeController::class, 'deleteDependent']);
    Route::post('{employee}/terminate', [EmployeeController::class, 'terminate']);
    Route::post('{employee}/rehire', [EmployeeController::class, 'rehire']);
    Route::get('{employee}/history', [EmployeeController::class, 'getHistory']);
    Route::get('search', [EmployeeController::class, 'search']);
    Route::get('by-department/{department}', [EmployeeController::class, 'getByDepartment']);
    Route::get('by-position/{position}', [EmployeeController::class, 'getByPosition']);
    Route::get('birthdays', [EmployeeController::class, 'getBirthdays']);
    Route::get('work-anniversaries', [EmployeeController::class, 'getWorkAnniversaries']);
});

// Payroll Management
Route::apiResource('payroll', PayrollController::class);
Route::prefix('payroll')->group(function () {
    Route::post('process', [PayrollController::class, 'processPayroll']);
    Route::post('{payroll}/approve', [PayrollController::class, 'approve']);
    Route::post('{payroll}/reject', [PayrollController::class, 'reject']);
    Route::get('{payroll}/payslip', [PayrollController::class, 'getPayslip']);
    Route::post('{payroll}/send-payslip', [PayrollController::class, 'sendPayslip']);
    Route::get('employee/{employee}', [PayrollController::class, 'getEmployeePayroll']);
    Route::get('period/{period}', [PayrollController::class, 'getPayrollByPeriod']);
    Route::get('pending-approval', [PayrollController::class, 'getPendingApproval']);
    Route::post('bulk-approve', [PayrollController::class, 'bulkApprove']);
    Route::get('summary/{period}', [PayrollController::class, 'getPayrollSummary']);
    Route::get('tax-report/{period}', [PayrollController::class, 'getTaxReport']);
    Route::post('export/{period}', [PayrollController::class, 'exportPayroll']);
    Route::get('bank-transfer/{period}', [PayrollController::class, 'getBankTransferFile']);
});

// Salary Components
Route::prefix('salary-components')->group(function () {
    Route::get('/', [PayrollController::class, 'getSalaryComponents']);
    Route::post('/', [PayrollController::class, 'createSalaryComponent']);
    Route::put('{component}', [PayrollController::class, 'updateSalaryComponent']);
    Route::delete('{component}', [PayrollController::class, 'deleteSalaryComponent']);
    Route::get('employee/{employee}', [PayrollController::class, 'getEmployeeSalaryComponents']);
    Route::post('employee/{employee}', [PayrollController::class, 'assignSalaryComponent']);
    Route::put('employee/{employee}/{component}', [PayrollController::class, 'updateEmployeeSalaryComponent']);
    Route::delete('employee/{employee}/{component}', [PayrollController::class, 'removeEmployeeSalaryComponent']);
});

// Attendance Management
Route::apiResource('attendance', AttendanceController::class);
Route::prefix('attendance')->group(function () {
    Route::post('check-in', [AttendanceController::class, 'checkIn']);
    Route::post('check-out', [AttendanceController::class, 'checkOut']);
    Route::post('break-start', [AttendanceController::class, 'startBreak']);
    Route::post('break-end', [AttendanceController::class, 'endBreak']);
    Route::get('employee/{employee}', [AttendanceController::class, 'getEmployeeAttendance']);
    Route::get('employee/{employee}/summary', [AttendanceController::class, 'getAttendanceSummary']);
    Route::get('daily/{date}', [AttendanceController::class, 'getDailyAttendance']);
    Route::get('monthly/{year}/{month}', [AttendanceController::class, 'getMonthlyAttendance']);
    Route::get('overtime', [AttendanceController::class, 'getOvertimeRecords']);
    Route::post('{attendance}/approve-overtime', [AttendanceController::class, 'approveOvertime']);
    Route::get('late-arrivals', [AttendanceController::class, 'getLateArrivals']);
    Route::get('early-departures', [AttendanceController::class, 'getEarlyDepartures']);
    Route::get('absent-employees', [AttendanceController::class, 'getAbsentEmployees']);
    Route::post('bulk-import', [AttendanceController::class, 'bulkImport']);
    Route::get('export/{period}', [AttendanceController::class, 'exportAttendance']);
});

// Shift Management
Route::prefix('shifts')->group(function () {
    Route::get('/', [AttendanceController::class, 'getShifts']);
    Route::post('/', [AttendanceController::class, 'createShift']);
    Route::put('{shift}', [AttendanceController::class, 'updateShift']);
    Route::delete('{shift}', [AttendanceController::class, 'deleteShift']);
    Route::get('employee/{employee}', [AttendanceController::class, 'getEmployeeShifts']);
    Route::post('employee/{employee}/assign', [AttendanceController::class, 'assignShift']);
    Route::get('schedule/{date}', [AttendanceController::class, 'getShiftSchedule']);
    Route::post('schedule', [AttendanceController::class, 'createShiftSchedule']);
});

// Leave Management
Route::apiResource('leaves', LeaveController::class);
Route::prefix('leaves')->group(function () {
    Route::post('{leave}/approve', [LeaveController::class, 'approve']);
    Route::post('{leave}/reject', [LeaveController::class, 'reject']);
    Route::post('{leave}/cancel', [LeaveController::class, 'cancel']);
    Route::get('employee/{employee}', [LeaveController::class, 'getEmployeeLeaves']);
    Route::get('employee/{employee}/balance', [LeaveController::class, 'getLeaveBalance']);
    Route::get('employee/{employee}/entitlement', [LeaveController::class, 'getLeaveEntitlement']);
    Route::get('pending-approval', [LeaveController::class, 'getPendingApproval']);
    Route::get('calendar', [LeaveController::class, 'getLeaveCalendar']);
    Route::get('conflicts', [LeaveController::class, 'getLeaveConflicts']);
    Route::post('bulk-approve', [LeaveController::class, 'bulkApprove']);
    Route::get('reports/{period}', [LeaveController::class, 'getLeaveReports']);
});

// Leave Types
Route::prefix('leave-types')->group(function () {
    Route::get('/', [LeaveController::class, 'getLeaveTypes']);
    Route::post('/', [LeaveController::class, 'createLeaveType']);
    Route::put('{type}', [LeaveController::class, 'updateLeaveType']);
    Route::delete('{type}', [LeaveController::class, 'deleteLeaveType']);
    Route::get('{type}/policies', [LeaveController::class, 'getLeaveTypePolicies']);
    Route::post('{type}/policies', [LeaveController::class, 'updateLeaveTypePolicies']);
});

// Performance Management
Route::apiResource('performance', PerformanceController::class);
Route::prefix('performance')->group(function () {
    Route::get('employee/{employee}', [PerformanceController::class, 'getEmployeePerformance']);
    Route::post('employee/{employee}/review', [PerformanceController::class, 'createReview']);
    Route::put('review/{review}', [PerformanceController::class, 'updateReview']);
    Route::post('review/{review}/submit', [PerformanceController::class, 'submitReview']);
    Route::post('review/{review}/approve', [PerformanceController::class, 'approveReview']);
    Route::get('goals', [PerformanceController::class, 'getGoals']);
    Route::post('goals', [PerformanceController::class, 'createGoal']);
    Route::put('goals/{goal}', [PerformanceController::class, 'updateGoal']);
    Route::delete('goals/{goal}', [PerformanceController::class, 'deleteGoal']);
    Route::get('employee/{employee}/goals', [PerformanceController::class, 'getEmployeeGoals']);
    Route::post('employee/{employee}/goals', [PerformanceController::class, 'assignGoal']);
    Route::get('kpis', [PerformanceController::class, 'getKPIs']);
    Route::post('kpis', [PerformanceController::class, 'createKPI']);
    Route::put('kpis/{kpi}', [PerformanceController::class, 'updateKPI']);
    Route::delete('kpis/{kpi}', [PerformanceController::class, 'deleteKPI']);
    Route::get('employee/{employee}/kpis', [PerformanceController::class, 'getEmployeeKPIs']);
    Route::post('feedback', [PerformanceController::class, 'submitFeedback']);
    Route::get('employee/{employee}/feedback', [PerformanceController::class, 'getEmployeeFeedback']);
});

// Recruitment
Route::apiResource('recruitment', RecruitmentController::class);
Route::prefix('recruitment')->group(function () {
    Route::get('jobs', [RecruitmentController::class, 'getJobs']);
    Route::post('jobs', [RecruitmentController::class, 'createJob']);
    Route::put('jobs/{job}', [RecruitmentController::class, 'updateJob']);
    Route::delete('jobs/{job}', [RecruitmentController::class, 'deleteJob']);
    Route::post('jobs/{job}/publish', [RecruitmentController::class, 'publishJob']);
    Route::post('jobs/{job}/close', [RecruitmentController::class, 'closeJob']);
    Route::get('jobs/{job}/applications', [RecruitmentController::class, 'getJobApplications']);
    Route::get('applications', [RecruitmentController::class, 'getApplications']);
    Route::post('applications', [RecruitmentController::class, 'createApplication']);
    Route::put('applications/{application}', [RecruitmentController::class, 'updateApplication']);
    Route::post('applications/{application}/review', [RecruitmentController::class, 'reviewApplication']);
    Route::post('applications/{application}/interview', [RecruitmentController::class, 'scheduleInterview']);
    Route::post('applications/{application}/hire', [RecruitmentController::class, 'hireCandidate']);
    Route::post('applications/{application}/reject', [RecruitmentController::class, 'rejectApplication']);
    Route::get('interviews', [RecruitmentController::class, 'getInterviews']);
    Route::post('interviews/{interview}/feedback', [RecruitmentController::class, 'submitInterviewFeedback']);
});

// Training & Development
Route::apiResource('training', TrainingController::class);
Route::prefix('training')->group(function () {
    Route::get('courses', [TrainingController::class, 'getCourses']);
    Route::post('courses', [TrainingController::class, 'createCourse']);
    Route::put('courses/{course}', [TrainingController::class, 'updateCourse']);
    Route::delete('courses/{course}', [TrainingController::class, 'deleteCourse']);
    Route::get('employee/{employee}/courses', [TrainingController::class, 'getEmployeeCourses']);
    Route::post('employee/{employee}/enroll', [TrainingController::class, 'enrollEmployee']);
    Route::post('employee/{employee}/complete', [TrainingController::class, 'completeCourse']);
    Route::get('certifications', [TrainingController::class, 'getCertifications']);
    Route::post('certifications', [TrainingController::class, 'createCertification']);
    Route::get('employee/{employee}/certifications', [TrainingController::class, 'getEmployeeCertifications']);
    Route::post('employee/{employee}/certifications', [TrainingController::class, 'awardCertification']);
    Route::get('skills', [TrainingController::class, 'getSkills']);
    Route::post('skills', [TrainingController::class, 'createSkill']);
    Route::get('employee/{employee}/skills', [TrainingController::class, 'getEmployeeSkills']);
    Route::post('employee/{employee}/skills', [TrainingController::class, 'assessSkill']);
    Route::get('training-plans', [TrainingController::class, 'getTrainingPlans']);
    Route::post('training-plans', [TrainingController::class, 'createTrainingPlan']);
    Route::get('employee/{employee}/training-plan', [TrainingController::class, 'getEmployeeTrainingPlan']);
});

// Benefits Management
Route::apiResource('benefits', BenefitsController::class);
Route::prefix('benefits')->group(function () {
    Route::get('employee/{employee}', [BenefitsController::class, 'getEmployeeBenefits']);
    Route::post('employee/{employee}/enroll', [BenefitsController::class, 'enrollInBenefit']);
    Route::post('employee/{employee}/unenroll', [BenefitsController::class, 'unenrollFromBenefit']);
    Route::get('enrollment-periods', [BenefitsController::class, 'getEnrollmentPeriods']);
    Route::post('enrollment-periods', [BenefitsController::class, 'createEnrollmentPeriod']);
    Route::get('claims', [BenefitsController::class, 'getClaims']);
    Route::post('claims', [BenefitsController::class, 'submitClaim']);
    Route::put('claims/{claim}', [BenefitsController::class, 'updateClaim']);
    Route::post('claims/{claim}/approve', [BenefitsController::class, 'approveClaim']);
    Route::post('claims/{claim}/reject', [BenefitsController::class, 'rejectClaim']);
    Route::get('employee/{employee}/claims', [BenefitsController::class, 'getEmployeeClaims']);
});

// Analytics and Reports
Route::prefix('analytics')->group(function () {
    Route::get('dashboard', [HRAnalyticsController::class, 'getDashboard']);
    Route::get('headcount', [HRAnalyticsController::class, 'getHeadcount']);
    Route::get('turnover', [HRAnalyticsController::class, 'getTurnoverAnalysis']);
    Route::get('attendance-summary', [HRAnalyticsController::class, 'getAttendanceSummary']);
    Route::get('payroll-summary', [HRAnalyticsController::class, 'getPayrollSummary']);
    Route::get('performance-metrics', [HRAnalyticsController::class, 'getPerformanceMetrics']);
    Route::get('training-effectiveness', [HRAnalyticsController::class, 'getTrainingEffectiveness']);
    Route::get('recruitment-metrics', [HRAnalyticsController::class, 'getRecruitmentMetrics']);
    Route::get('employee-satisfaction', [HRAnalyticsController::class, 'getEmployeeSatisfaction']);
    Route::get('diversity-metrics', [HRAnalyticsController::class, 'getDiversityMetrics']);
    Route::get('cost-analysis', [HRAnalyticsController::class, 'getCostAnalysis']);
    Route::get('predictive-insights', [HRAnalyticsController::class, 'getPredictiveInsights']);
});

// Self Service
Route::prefix('self-service')->group(function () {
    Route::get('profile', [EmployeeController::class, 'getSelfProfile']);
    Route::put('profile', [EmployeeController::class, 'updateSelfProfile']);
    Route::get('payslips', [PayrollController::class, 'getSelfPayslips']);
    Route::get('attendance', [AttendanceController::class, 'getSelfAttendance']);
    Route::post('leave-request', [LeaveController::class, 'submitLeaveRequest']);
    Route::get('leave-balance', [LeaveController::class, 'getSelfLeaveBalance']);
    Route::get('benefits', [BenefitsController::class, 'getSelfBenefits']);
    Route::post('expense-claim', [BenefitsController::class, 'submitExpenseClaim']);
    Route::get('training-history', [TrainingController::class, 'getSelfTrainingHistory']);
    Route::post('training-request', [TrainingController::class, 'submitTrainingRequest']);
    Route::get('performance-reviews', [PerformanceController::class, 'getSelfPerformanceReviews']);
    Route::post('feedback', [PerformanceController::class, 'submitSelfFeedback']);
});

// Compliance & Legal
Route::prefix('compliance')->group(function () {
    Route::get('audit-trail', [HRAnalyticsController::class, 'getAuditTrail']);
    Route::get('compliance-status', [HRAnalyticsController::class, 'getComplianceStatus']);
    Route::get('labor-law-compliance', [HRAnalyticsController::class, 'getLaborLawCompliance']);
    Route::get('visa-expiry-alerts', [HRAnalyticsController::class, 'getVisaExpiryAlerts']);
    Route::get('contract-renewals', [HRAnalyticsController::class, 'getContractRenewals']);
    Route::get('gosi-reports', [HRAnalyticsController::class, 'getGOSIReports']);
    Route::get('mol-reports', [HRAnalyticsController::class, 'getMOLReports']);
    Route::get('nitaqat-status', [HRAnalyticsController::class, 'getNitaqatStatus']);
});

// Import/Export
Route::prefix('import-export')->group(function () {
    Route::post('import-employees', [EmployeeController::class, 'importEmployees']);
    Route::post('import-attendance', [AttendanceController::class, 'importAttendance']);
    Route::post('import-payroll', [PayrollController::class, 'importPayroll']);
    Route::get('export-employees', [EmployeeController::class, 'exportEmployees']);
    Route::get('export-payroll/{period}', [PayrollController::class, 'exportPayroll']);
    Route::get('export-attendance/{period}', [AttendanceController::class, 'exportAttendance']);
    Route::post('backup-data', [HRAnalyticsController::class, 'backupHRData']);
    Route::post('restore-data', [HRAnalyticsController::class, 'restoreHRData']);
});
