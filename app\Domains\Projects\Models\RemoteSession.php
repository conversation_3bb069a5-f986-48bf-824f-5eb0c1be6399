<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج جلسة العمل عن بُعد - Remote Session
 */
class RemoteSession extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'employee_id',
        'project_id',
        'platform',
        'device_info',
        'location_info',
        'connection_quality',
        'started_at',
        'ended_at',
        'is_active',
        'sync_strategy',
        'offline_capabilities',
        'session_duration',
        'final_sync_status',
        'last_sync_at',
        'last_ping_at',
        'connection_status',
        'optimizations',
        'metadata',
    ];

    protected $casts = [
        'device_info' => 'array',
        'location_info' => 'array',
        'connection_quality' => 'array',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'is_active' => 'boolean',
        'offline_capabilities' => 'array',
        'session_duration' => 'integer',
        'last_sync_at' => 'datetime',
        'last_ping_at' => 'datetime',
        'connection_status' => 'array',
        'optimizations' => 'array',
        'metadata' => 'array',
    ];

    public function employee(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function offlineActivities(): HasMany
    {
        return $this->hasMany(OfflineActivity::class, 'session_id');
    }
}
