<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\JobApplication;
use App\Domains\HR\Models\Candidate;
use App\Domains\HR\Models\JobPosting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التوظيف بالذكاء الاصطناعي
 * تقييم المرشحين وفلترة السير الذاتية تلقائياً
 */
class AIRecruitmentService
{
    protected array $config;

    public function __construct()
    {
        $this->config = config('hr.ai_recruitment', []);
    }

    /**
     * تقييم مرشح باستخدام الذكاء الاصطناعي
     */
    public function scoreCandidate(JobApplication $application): array
    {
        $candidate = $application->candidate;
        $jobPosting = $application->jobPosting;

        // استخراج البيانات للتحليل
        $candidateData = $this->extractCandidateData($candidate);
        $jobRequirements = $this->extractJobRequirements($jobPosting);

        // تحليل المهارات
        $skillsMatch = $this->analyzeSkillsMatch($candidateData['skills'], $jobRequirements['skills']);
        
        // تحليل الخبرة
        $experienceMatch = $this->analyzeExperienceMatch($candidateData['experience'], $jobRequirements['experience']);
        
        // تحليل التعليم
        $educationMatch = $this->analyzeEducationMatch($candidateData['education'], $jobRequirements['education']);
        
        // تحليل الملاءمة الثقافية
        $culturalFit = $this->analyzeCulturalFit($candidateData, $jobRequirements);

        // حساب النقاط الإجمالية
        $overallScore = $this->calculateOverallScore([
            'skills' => $skillsMatch,
            'experience' => $experienceMatch,
            'education' => $educationMatch,
            'cultural_fit' => $culturalFit,
        ]);

        return [
            'overall_score' => $overallScore,
            'skills_match' => $skillsMatch,
            'experience_match' => $experienceMatch,
            'education_match' => $educationMatch,
            'cultural_fit' => $culturalFit,
            'analysis' => $this->generateAnalysisReport($candidateData, $jobRequirements, [
                'skills' => $skillsMatch,
                'experience' => $experienceMatch,
                'education' => $educationMatch,
                'cultural_fit' => $culturalFit,
            ]),
        ];
    }

    /**
     * استخراج بيانات المرشح
     */
    protected function extractCandidateData(Candidate $candidate): array
    {
        return [
            'skills' => $this->extractSkills($candidate->resume_text ?? ''),
            'experience' => $this->extractExperience($candidate),
            'education' => $this->extractEducation($candidate),
            'languages' => $candidate->languages ?? [],
            'certifications' => $candidate->certifications ?? [],
            'achievements' => $candidate->achievements ?? [],
            'personality_traits' => $this->extractPersonalityTraits($candidate->resume_text ?? ''),
        ];
    }

    /**
     * استخراج متطلبات الوظيفة
     */
    protected function extractJobRequirements(JobPosting $jobPosting): array
    {
        return [
            'skills' => $jobPosting->skills_required ?? [],
            'experience' => $jobPosting->experience_required ?? 0,
            'education' => $jobPosting->qualifications ?? [],
            'job_level' => $jobPosting->position->level ?? 'INTERMEDIATE',
            'department' => $jobPosting->department->name ?? '',
            'company_culture' => $this->getCompanyCultureKeywords($jobPosting->company),
        ];
    }

    /**
     * استخراج المهارات من النص
     */
    protected function extractSkills(string $text): array
    {
        // قائمة المهارات الشائعة (يمكن توسيعها)
        $skillsDatabase = [
            // تقنية
            'PHP', 'Laravel', 'JavaScript', 'Vue.js', 'React', 'Node.js', 'Python', 'Java',
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Docker', 'Kubernetes',
            'AWS', 'Azure', 'Google Cloud', 'Git', 'CI/CD', 'DevOps',
            
            // إدارية
            'إدارة المشاريع', 'القيادة', 'التخطيط الاستراتيجي', 'إدارة الفرق',
            'التفاوض', 'العرض والتقديم', 'حل المشكلات', 'اتخاذ القرارات',
            
            // مالية
            'المحاسبة', 'التحليل المالي', 'إعداد الميزانيات', 'التدقيق',
            'الضرائب', 'التخطيط المالي', 'إدارة المخاطر',
            
            // تسويقية
            'التسويق الرقمي', 'وسائل التواصل الاجتماعي', 'تحسين محركات البحث',
            'إعلانات جوجل', 'تحليل البيانات', 'إدارة العلامة التجارية',
        ];

        $foundSkills = [];
        $text = strtolower($text);

        foreach ($skillsDatabase as $skill) {
            if (strpos($text, strtolower($skill)) !== false) {
                $foundSkills[] = $skill;
            }
        }

        return array_unique($foundSkills);
    }

    /**
     * استخراج الخبرة
     */
    protected function extractExperience(Candidate $candidate): array
    {
        $workHistory = $candidate->work_history ?? [];
        $totalYears = 0;
        $positions = [];

        foreach ($workHistory as $job) {
            $startDate = \Carbon\Carbon::parse($job['start_date'] ?? now());
            $endDate = $job['end_date'] ? \Carbon\Carbon::parse($job['end_date']) : now();
            
            $years = $startDate->diffInYears($endDate, true);
            $totalYears += $years;
            
            $positions[] = [
                'title' => $job['title'] ?? '',
                'company' => $job['company'] ?? '',
                'years' => $years,
                'responsibilities' => $job['responsibilities'] ?? [],
            ];
        }

        return [
            'total_years' => $totalYears,
            'positions' => $positions,
            'industries' => array_unique(array_column($workHistory, 'industry')),
        ];
    }

    /**
     * استخراج التعليم
     */
    protected function extractEducation(Candidate $candidate): array
    {
        $education = $candidate->education ?? [];
        
        return [
            'highest_degree' => $this->getHighestDegree($education),
            'degrees' => $education,
            'gpa' => $candidate->gpa ?? null,
            'relevant_courses' => $this->extractRelevantCourses($education),
        ];
    }

    /**
     * تحليل مطابقة المهارات
     */
    protected function analyzeSkillsMatch(array $candidateSkills, array $requiredSkills): float
    {
        if (empty($requiredSkills)) {
            return 100; // إذا لم تكن هناك مهارات مطلوبة محددة
        }

        $matchedSkills = array_intersect(
            array_map('strtolower', $candidateSkills),
            array_map('strtolower', $requiredSkills)
        );

        $matchPercentage = (count($matchedSkills) / count($requiredSkills)) * 100;
        
        // إضافة نقاط إضافية للمهارات الإضافية
        $extraSkills = count($candidateSkills) - count($requiredSkills);
        if ($extraSkills > 0) {
            $matchPercentage += min($extraSkills * 2, 20); // حد أقصى 20 نقطة إضافية
        }

        return min($matchPercentage, 100);
    }

    /**
     * تحليل مطابقة الخبرة
     */
    protected function analyzeExperienceMatch(array $candidateExperience, int $requiredYears): float
    {
        $candidateYears = $candidateExperience['total_years'] ?? 0;
        
        if ($requiredYears == 0) {
            return 100;
        }

        if ($candidateYears >= $requiredYears) {
            // نقاط إضافية للخبرة الزائدة (حتى حد معين)
            $extraYears = $candidateYears - $requiredYears;
            $bonus = min($extraYears * 5, 20);
            return min(100 + $bonus, 100);
        }

        // تقليل النقاط للخبرة الأقل
        return ($candidateYears / $requiredYears) * 100;
    }

    /**
     * تحليل مطابقة التعليم
     */
    protected function analyzeEducationMatch(array $candidateEducation, array $requiredEducation): float
    {
        if (empty($requiredEducation)) {
            return 100;
        }

        $candidateDegree = $candidateEducation['highest_degree'] ?? '';
        $degreeHierarchy = ['High School', 'Diploma', 'Bachelor', 'Master', 'PhD'];
        
        $score = 50; // نقاط أساسية
        
        foreach ($requiredEducation as $requirement) {
            if (stripos($candidateDegree, $requirement) !== false) {
                $score += 25;
            }
        }

        return min($score, 100);
    }

    /**
     * تحليل الملاءمة الثقافية
     */
    protected function analyzeCulturalFit(array $candidateData, array $jobRequirements): float
    {
        // تحليل بسيط للملاءمة الثقافية
        $score = 70; // نقاط أساسية
        
        // تحليل الكلمات المفتاحية في السيرة الذاتية
        $culturalKeywords = [
            'فريق', 'تعاون', 'قيادة', 'ابتكار', 'إبداع', 'مسؤولية',
            'team', 'collaboration', 'leadership', 'innovation', 'creativity'
        ];

        $personalityTraits = $candidateData['personality_traits'] ?? [];
        $matchedTraits = array_intersect($personalityTraits, $culturalKeywords);
        
        $score += count($matchedTraits) * 5;

        return min($score, 100);
    }

    /**
     * حساب النقاط الإجمالية
     */
    protected function calculateOverallScore(array $scores): float
    {
        $weights = [
            'skills' => 0.4,        // 40% للمهارات
            'experience' => 0.3,    // 30% للخبرة
            'education' => 0.2,     // 20% للتعليم
            'cultural_fit' => 0.1,  // 10% للملاءمة الثقافية
        ];

        $totalScore = 0;
        foreach ($scores as $category => $score) {
            $totalScore += $score * ($weights[$category] ?? 0);
        }

        return round($totalScore, 2);
    }

    /**
     * توليد تقرير التحليل
     */
    protected function generateAnalysisReport(array $candidateData, array $jobRequirements, array $scores): array
    {
        $strengths = [];
        $weaknesses = [];
        $recommendations = [];

        // تحليل نقاط القوة
        if ($scores['skills'] >= 80) {
            $strengths[] = 'مهارات تقنية ممتازة تتطابق مع متطلبات الوظيفة';
        }
        
        if ($scores['experience'] >= 80) {
            $strengths[] = 'خبرة عملية قوية في المجال المطلوب';
        }

        // تحليل نقاط الضعف
        if ($scores['skills'] < 60) {
            $weaknesses[] = 'نقص في بعض المهارات المطلوبة';
            $recommendations[] = 'يُنصح بتوفير تدريب إضافي في المهارات المفقودة';
        }

        if ($scores['experience'] < 60) {
            $weaknesses[] = 'خبرة أقل من المطلوب';
            $recommendations[] = 'قد يحتاج إشراف إضافي في البداية';
        }

        return [
            'strengths' => $strengths,
            'weaknesses' => $weaknesses,
            'recommendations' => $recommendations,
            'summary' => $this->generateSummary($scores),
        ];
    }

    /**
     * توليد ملخص التقييم
     */
    protected function generateSummary(array $scores): string
    {
        $overallScore = $scores['skills'] * 0.4 + $scores['experience'] * 0.3 + 
                       $scores['education'] * 0.2 + $scores['cultural_fit'] * 0.1;

        if ($overallScore >= 90) {
            return 'مرشح ممتاز يتطابق بشكل كبير مع متطلبات الوظيفة';
        } elseif ($overallScore >= 80) {
            return 'مرشح جيد جداً مع بعض النقاط التي تحتاج تطوير';
        } elseif ($overallScore >= 70) {
            return 'مرشح جيد يمكن النظر فيه مع توفير التدريب المناسب';
        } elseif ($overallScore >= 60) {
            return 'مرشح متوسط قد يحتاج تقييم إضافي';
        } else {
            return 'مرشح لا يتطابق مع متطلبات الوظيفة الحالية';
        }
    }

    /**
     * استخراج السمات الشخصية من النص
     */
    protected function extractPersonalityTraits(string $text): array
    {
        $traits = [
            'قيادة', 'تعاون', 'إبداع', 'مسؤولية', 'تنظيم', 'تواصل',
            'leadership', 'teamwork', 'creativity', 'responsibility', 'organization', 'communication'
        ];

        $foundTraits = [];
        $text = strtolower($text);

        foreach ($traits as $trait) {
            if (strpos($text, strtolower($trait)) !== false) {
                $foundTraits[] = $trait;
            }
        }

        return $foundTraits;
    }

    /**
     * الحصول على أعلى درجة تعليمية
     */
    protected function getHighestDegree(array $education): string
    {
        $degrees = array_column($education, 'degree');
        $hierarchy = ['PhD', 'Master', 'Bachelor', 'Diploma', 'High School'];

        foreach ($hierarchy as $degree) {
            foreach ($degrees as $candidateDegree) {
                if (stripos($candidateDegree, $degree) !== false) {
                    return $degree;
                }
            }
        }

        return 'Unknown';
    }

    /**
     * استخراج الدورات ذات الصلة
     */
    protected function extractRelevantCourses(array $education): array
    {
        $courses = [];
        foreach ($education as $edu) {
            if (isset($edu['courses'])) {
                $courses = array_merge($courses, $edu['courses']);
            }
        }
        return $courses;
    }

    /**
     * الحصول على كلمات مفتاحية لثقافة الشركة
     */
    protected function getCompanyCultureKeywords($company): array
    {
        // يمكن تخصيص هذا حسب ثقافة كل شركة
        return [
            'innovation', 'teamwork', 'excellence', 'integrity', 'customer-focus',
            'ابتكار', 'عمل جماعي', 'تميز', 'نزاهة', 'تركيز على العميل'
        ];
    }

    /**
     * فلترة المرشحين تلقائياً
     */
    public function filterCandidates(JobPosting $jobPosting, float $minimumScore = 70): \Illuminate\Database\Eloquent\Collection
    {
        return $jobPosting->applications()
            ->with('candidate')
            ->where('ai_score', '>=', $minimumScore)
            ->orderByDesc('ai_score')
            ->get();
    }

    /**
     * اقتراح أسئلة مقابلة مخصصة
     */
    public function suggestInterviewQuestions(JobApplication $application): array
    {
        $candidate = $application->candidate;
        $jobPosting = $application->jobPosting;
        $analysis = $application->ai_analysis ?? [];

        $questions = [
            'general' => [
                'حدثني عن نفسك وخبراتك المهنية',
                'لماذا تريد العمل في هذا المنصب؟',
                'ما هي نقاط قوتك وضعفك؟',
            ],
            'technical' => [],
            'behavioral' => [],
            'situational' => [],
        ];

        // أسئلة تقنية حسب المهارات المطلوبة
        $requiredSkills = $jobPosting->skills_required ?? [];
        foreach ($requiredSkills as $skill) {
            $questions['technical'][] = "اشرح خبرتك في {$skill}";
        }

        // أسئلة سلوكية حسب نقاط الضعف المحددة
        $weaknesses = $analysis['weaknesses'] ?? [];
        foreach ($weaknesses as $weakness) {
            $questions['behavioral'][] = "كيف تتعامل مع التحديات في {$weakness}؟";
        }

        return $questions;
    }
}
