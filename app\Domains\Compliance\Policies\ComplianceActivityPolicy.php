<?php

namespace App\Domains\Compliance\Policies;

use App\Domains\Compliance\Models\ComplianceActivity;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أنشطة الامتثال
 */
class ComplianceActivityPolicy
{
    use HandlesAuthorization;

    /**
     * عرض جميع الأنشطة
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('compliance.activities.view');
    }

    /**
     * عرض نشاط محدد
     */
    public function view(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.view') &&
               $this->canAccessActivity($user, $activity);
    }

    /**
     * إنشاء نشاط جديد
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('compliance.activities.create');
    }

    /**
     * تحديث نشاط
     */
    public function update(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.update') &&
               $this->canAccessActivity($user, $activity) &&
               !in_array($activity->status, ['completed', 'cancelled']);
    }

    /**
     * حذف نشاط
     */
    public function delete(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.delete') &&
               $this->canAccessActivity($user, $activity) &&
               $activity->status === 'pending';
    }

    /**
     * تعيين نشاط لمستخدم
     */
    public function assign(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.assign') &&
               $this->canAccessActivity($user, $activity);
    }

    /**
     * إكمال نشاط
     */
    public function complete(User $user, ComplianceActivity $activity): bool
    {
        return ($user->hasPermissionTo('compliance.activities.complete') ||
                $activity->assigned_to === $user->id) &&
               $this->canAccessActivity($user, $activity) &&
               in_array($activity->status, ['pending', 'in_progress']);
    }

    /**
     * مراجعة نشاط
     */
    public function review(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.review') &&
               $this->canAccessActivity($user, $activity) &&
               $activity->status === 'completed';
    }

    /**
     * اعتماد نشاط
     */
    public function approve(User $user, ComplianceActivity $activity): bool
    {
        return $user->hasPermissionTo('compliance.activities.approve') &&
               $this->canAccessActivity($user, $activity) &&
               $activity->status === 'under_review';
    }

    /**
     * التحقق من إمكانية الوصول للنشاط
     */
    protected function canAccessActivity(User $user, ComplianceActivity $activity): bool
    {
        // إذا كان المستخدم مدير عام
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // إذا كان النشاط مُعيَّن للمستخدم
        if ($activity->assigned_to === $user->id) {
            return true;
        }

        // التحقق من صلاحيات الشركة
        if (!$this->canAccessCompany($user, $activity->company_id)) {
            return false;
        }

        // التحقق من صلاحيات الدولة
        return $this->canAccessCountry($user, $activity->country->code);
    }

    /**
     * التحقق من إمكانية الوصول للشركة
     */
    protected function canAccessCompany(User $user, int $companyId): bool
    {
        // منطق التحقق من صلاحيات الشركة
        return $user->companies()->where('id', $companyId)->exists() ||
               $user->hasPermissionTo('compliance.all_companies');
    }

    /**
     * التحقق من إمكانية الوصول للدولة
     */
    protected function canAccessCountry(User $user, string $countryCode): bool
    {
        return $user->hasRole('super-admin') ||
               $user->hasPermissionTo("compliance.countries.{$countryCode}");
    }
}
