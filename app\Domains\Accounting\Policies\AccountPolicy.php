<?php

namespace App\Domains\Accounting\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\Accounting\Models\Account;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة أمان الحسابات المحاسبية
 * Account Security Policy
 */
class AccountPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض أي حساب
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_accounts') || 
               $user->hasRole(['admin', 'accountant', 'financial_manager', 'auditor']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الحساب
     */
    public function view(Employee $user, Account $account): bool
    {
        // المدراء والمحاسبون يمكنهم رؤية جميع الحسابات
        if ($user->hasRole(['admin', 'financial_manager', 'accountant'])) {
            return true;
        }

        // المراجعون يمكنهم رؤية الحسابات للمراجعة
        if ($user->hasRole('auditor')) {
            return true;
        }

        // الموظفون الآخرون حسب الصلاحيات
        return $user->hasPermissionTo('view_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء حساب
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_accounts') || 
               $user->hasRole(['admin', 'financial_manager', 'senior_accountant']);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الحساب
     */
    public function update(Employee $user, Account $account): bool
    {
        // منع تعديل الحسابات النظامية
        if ($account->is_system_account && !$user->hasRole('admin')) {
            return false;
        }

        // المدراء والمحاسبون الكبار يمكنهم تحديث الحسابات
        if ($user->hasRole(['admin', 'financial_manager', 'senior_accountant'])) {
            return true;
        }

        return $user->hasPermissionTo('update_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الحساب
     */
    public function delete(Employee $user, Account $account): bool
    {
        // منع حذف الحسابات النظامية
        if ($account->is_system_account) {
            return false;
        }

        // منع حذف الحسابات التي لها معاملات
        if ($account->journalEntryDetails()->exists()) {
            return false;
        }

        // فقط المدراء يمكنهم حذف الحسابات
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('delete_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تعديل رصيد الحساب
     */
    public function adjustBalance(Employee $user, Account $account): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('adjust_account_balances');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض تاريخ الحساب
     */
    public function viewHistory(Employee $user, Account $account): bool
    {
        return $user->hasRole(['admin', 'financial_manager', 'accountant', 'auditor']) || 
               $user->hasPermissionTo('view_account_history');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إنشاء قيود يومية للحساب
     */
    public function createJournalEntry(Employee $user, Account $account): bool
    {
        // التحقق من السماح بالإدخال اليدوي
        if (!$account->allow_manual_entry) {
            return false;
        }

        return $user->hasRole(['admin', 'financial_manager', 'accountant']) || 
               $user->hasPermissionTo('create_journal_entries');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تصدير بيانات الحسابات
     */
    public function export(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager', 'auditor']) || 
               $user->hasPermissionTo('export_accounting_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استيراد بيانات الحسابات
     */
    public function import(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('import_accounting_data');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض التقارير المالية
     */
    public function viewFinancialReports(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager', 'accountant', 'auditor', 'ceo']) || 
               $user->hasPermissionTo('view_financial_reports');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إدارة دليل الحسابات
     */
    public function manageChartOfAccounts(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('manage_chart_of_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إعادة حساب الأرصدة
     */
    public function recalculateBalances(Employee $user): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('recalculate_account_balances');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض البيانات الحساسة
     */
    public function viewSensitiveData(Employee $user, Account $account): bool
    {
        // البيانات البنكية والمالية الحساسة
        if (in_array($account->account_category, ['current_assets', 'investments'])) {
            return $user->hasRole(['admin', 'financial_manager', 'ceo']) || 
                   $user->hasPermissionTo('view_sensitive_financial_data');
        }

        return $this->view($user, $account);
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إجراء مراجعة للحساب
     */
    public function audit(Employee $user, Account $account): bool
    {
        return $user->hasRole(['admin', 'auditor', 'financial_manager']) || 
               $user->hasPermissionTo('audit_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تجميد الحساب
     */
    public function freeze(Employee $user, Account $account): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('freeze_accounts');
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه إلغاء تجميد الحساب
     */
    public function unfreeze(Employee $user, Account $account): bool
    {
        return $user->hasRole(['admin', 'financial_manager']) || 
               $user->hasPermissionTo('unfreeze_accounts');
    }
}
