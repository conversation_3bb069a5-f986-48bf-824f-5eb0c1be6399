<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\CustomerSegment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * خدمة تقسيم العملاء - Customer Segmentation Service
 * تقسيم العملاء إلى شرائح للاستهداف التسويقي الذكي
 */
class CustomerSegmentationService
{
    /**
     * إنشاء شريحة عملاء جديدة
     */
    public function createSegment(array $segmentData): CustomerSegment
    {
        $segment = CustomerSegment::create($segmentData);

        if ($segment->is_dynamic) {
            $segment->recalculate();
        }

        return $segment;
    }

    /**
     * تحديث جميع الشرائح الديناميكية
     */
    public function updateAllDynamicSegments(): array
    {
        $segments = CustomerSegment::needsRecalculation()->get();
        $results = [];

        foreach ($segments as $segment) {
            $oldSize = $segment->actual_size;
            $newSize = $segment->recalculate();
            
            $results[] = [
                'segment_id' => $segment->id,
                'segment_name' => $segment->name,
                'old_size' => $oldSize,
                'new_size' => $newSize,
                'change' => $newSize - $oldSize,
                'change_percentage' => $oldSize > 0 ? round((($newSize - $oldSize) / $oldSize) * 100, 1) : 0,
            ];
        }

        return $results;
    }

    /**
     * الحصول على العملاء حسب قواعد التقسيم
     */
    public function getCustomersByRules(array $rules): array
    {
        $query = Customer::query();

        foreach ($rules as $rule) {
            $query = $this->applyRule($query, $rule);
        }

        return $query->pluck('id')->toArray();
    }

    /**
     * تحليل سلوك العملاء للتقسيم التلقائي
     */
    public function analyzeBehavioralSegments(): array
    {
        return [
            'high_value_customers' => $this->identifyHighValueCustomers(),
            'frequent_buyers' => $this->identifyFrequentBuyers(),
            'at_risk_customers' => $this->identifyAtRiskCustomers(),
            'new_customers' => $this->identifyNewCustomers(),
            'loyal_customers' => $this->identifyLoyalCustomers(),
            'dormant_customers' => $this->identifyDormantCustomers(),
            'price_sensitive' => $this->identifyPriceSensitiveCustomers(),
            'early_adopters' => $this->identifyEarlyAdopters(),
        ];
    }

    /**
     * إنشاء شرائح تلقائية بناءً على السلوك
     */
    public function createAutomaticBehavioralSegments(): array
    {
        $segments = [];
        $behavioralData = $this->analyzeBehavioralSegments();

        foreach ($behavioralData as $type => $customerIds) {
            if (count($customerIds) >= 10) { // حد أدنى للشريحة
                $segment = $this->createBehavioralSegment($type, $customerIds);
                $segments[] = $segment;
            }
        }

        return $segments;
    }

    /**
     * تحليل RFM (Recency, Frequency, Monetary)
     */
    public function performRFMAnalysis(): array
    {
        $customers = Customer::with(['ecommerceOrders' => function ($query) {
            $query->where('status', 'completed');
        }])->get();

        $rfmData = [];

        foreach ($customers as $customer) {
            $orders = $customer->ecommerceOrders;
            
            if ($orders->isEmpty()) {
                continue;
            }

            // Recency: أيام منذ آخر شراء
            $lastOrder = $orders->sortByDesc('created_at')->first();
            $recency = $lastOrder->created_at->diffInDays(now());

            // Frequency: عدد الطلبات
            $frequency = $orders->count();

            // Monetary: إجمالي الإنفاق
            $monetary = $orders->sum('total_amount');

            $rfmData[$customer->id] = [
                'customer' => $customer,
                'recency' => $recency,
                'frequency' => $frequency,
                'monetary' => $monetary,
                'recency_score' => $this->calculateRecencyScore($recency),
                'frequency_score' => $this->calculateFrequencyScore($frequency),
                'monetary_score' => $this->calculateMonetaryScore($monetary),
            ];
        }

        return $this->categorizeRFMSegments($rfmData);
    }

    /**
     * تقسيم العملاء حسب دورة الحياة
     */
    public function segmentByLifecycle(): array
    {
        $segments = [
            'prospects' => [],
            'new_customers' => [],
            'active_customers' => [],
            'loyal_customers' => [],
            'vip_customers' => [],
            'at_risk' => [],
            'churned' => [],
            'dormant' => [],
        ];

        $customers = Customer::with(['ecommerceOrders', 'interactions'])->get();

        foreach ($customers as $customer) {
            $stage = $this->determineLifecycleStage($customer);
            $segments[$stage][] = $customer->id;
        }

        return $segments;
    }

    /**
     * تقسيم العملاء حسب القيمة
     */
    public function segmentByValue(): array
    {
        $customers = Customer::where('total_spent', '>', 0)
                           ->orderBy('total_spent', 'desc')
                           ->get();

        $totalCustomers = $customers->count();
        
        return [
            'platinum' => $customers->take(intval($totalCustomers * 0.05))->pluck('id')->toArray(), // أعلى 5%
            'gold' => $customers->skip(intval($totalCustomers * 0.05))
                               ->take(intval($totalCustomers * 0.15))
                               ->pluck('id')->toArray(), // 15% التالية
            'silver' => $customers->skip(intval($totalCustomers * 0.20))
                                 ->take(intval($totalCustomers * 0.30))
                                 ->pluck('id')->toArray(), // 30% التالية
            'bronze' => $customers->skip(intval($totalCustomers * 0.50))
                                 ->pluck('id')->toArray(), // الباقي
        ];
    }

    /**
     * تقسيم العملاء حسب الموقع الجغرافي
     */
    public function segmentByGeography(): array
    {
        return [
            'by_country' => Customer::select('country_code', DB::raw('count(*) as count'))
                                  ->groupBy('country_code')
                                  ->pluck('count', 'country_code')
                                  ->toArray(),
            'by_city' => Customer::select('city', DB::raw('count(*) as count'))
                               ->groupBy('city')
                               ->orderBy('count', 'desc')
                               ->limit(20)
                               ->pluck('count', 'city')
                               ->toArray(),
            'by_region' => $this->groupByRegion(),
        ];
    }

    /**
     * تقسيم العملاء حسب الصناعة
     */
    public function segmentByIndustry(): array
    {
        return Customer::with('industry')
                      ->get()
                      ->groupBy('industry.name')
                      ->map(function ($customers, $industry) {
                          return [
                              'industry' => $industry,
                              'count' => $customers->count(),
                              'total_value' => $customers->sum('total_spent'),
                              'average_value' => $customers->avg('total_spent'),
                              'customer_ids' => $customers->pluck('id')->toArray(),
                          ];
                      })
                      ->sortByDesc('total_value')
                      ->toArray();
    }

    /**
     * تحليل تفضيلات العملاء
     */
    public function analyzeCustomerPreferences(): array
    {
        return [
            'communication_preferences' => $this->analyzeCommunicationPreferences(),
            'product_preferences' => $this->analyzeProductPreferences(),
            'channel_preferences' => $this->analyzeChannelPreferences(),
            'timing_preferences' => $this->analyzeTimingPreferences(),
            'content_preferences' => $this->analyzeContentPreferences(),
        ];
    }

    /**
     * إنشاء شرائح مخصصة بناءً على الذكاء الاصطناعي
     */
    public function createAIBasedSegments(): array
    {
        // تحليل البيانات باستخدام خوارزميات التعلم الآلي
        $customerData = $this->prepareCustomerDataForML();
        
        // تطبيق خوارزمية K-Means للتجميع
        $clusters = $this->performKMeansClustering($customerData);
        
        // إنشاء شرائح من النتائج
        $segments = [];
        foreach ($clusters as $clusterId => $customerIds) {
            $segment = $this->createAISegment($clusterId, $customerIds);
            $segments[] = $segment;
        }

        return $segments;
    }

    /**
     * تحديد العملاء عالي القيمة
     */
    protected function identifyHighValueCustomers(): array
    {
        return Customer::where('total_spent', '>=', 50000)
                      ->orWhere('lifetime_value', '>=', 100000)
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد المشترين المتكررين
     */
    protected function identifyFrequentBuyers(): array
    {
        return Customer::where('total_orders', '>=', 5)
                      ->whereHas('ecommerceOrders', function ($query) {
                          $query->where('created_at', '>=', now()->subMonths(6));
                      })
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد العملاء المعرضين للخطر
     */
    protected function identifyAtRiskCustomers(): array
    {
        return Customer::where('last_purchase_at', '<', now()->subMonths(6))
                      ->where('total_spent', '>', 1000)
                      ->whereHas('supportTickets', function ($query) {
                          $query->where('satisfaction_rating', '<', 3);
                      })
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد العملاء الجدد
     */
    protected function identifyNewCustomers(): array
    {
        return Customer::where('created_at', '>=', now()->subDays(30))
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد العملاء المخلصين
     */
    protected function identifyLoyalCustomers(): array
    {
        return Customer::where('created_at', '<=', now()->subYear())
                      ->where('total_orders', '>=', 10)
                      ->where('last_purchase_at', '>=', now()->subMonths(3))
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد العملاء الخاملين
     */
    protected function identifyDormantCustomers(): array
    {
        return Customer::where('last_purchase_at', '<', now()->subYear())
                      ->where('total_spent', '>', 0)
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد العملاء الحساسين للسعر
     */
    protected function identifyPriceSensitiveCustomers(): array
    {
        // العملاء الذين يشترون فقط عند وجود خصومات
        return Customer::whereHas('ecommerceOrders', function ($query) {
                          $query->where('discount_amount', '>', 0);
                      })
                      ->whereDoesntHave('ecommerceOrders', function ($query) {
                          $query->where('discount_amount', '=', 0);
                      })
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * تحديد المتبنين المبكرين
     */
    protected function identifyEarlyAdopters(): array
    {
        // العملاء الذين يشترون المنتجات الجديدة بسرعة
        return Customer::whereHas('ecommerceOrders.items.product', function ($query) {
                          $query->where('created_at', '>=', now()->subMonths(3));
                      })
                      ->pluck('id')
                      ->toArray();
    }

    /**
     * إنشاء شريحة سلوكية
     */
    protected function createBehavioralSegment(string $type, array $customerIds): CustomerSegment
    {
        $segmentNames = [
            'high_value_customers' => 'عملاء عالي القيمة',
            'frequent_buyers' => 'مشترين متكررين',
            'at_risk_customers' => 'عملاء معرضين للخطر',
            'new_customers' => 'عملاء جدد',
            'loyal_customers' => 'عملاء مخلصين',
            'dormant_customers' => 'عملاء خاملين',
            'price_sensitive' => 'حساسين للسعر',
            'early_adopters' => 'متبنين مبكرين',
        ];

        $segment = CustomerSegment::create([
            'name' => $segmentNames[$type] ?? $type,
            'description' => "شريحة تم إنشاؤها تلقائياً بناءً على السلوك: {$type}",
            'type' => 'behavioral',
            'is_dynamic' => false,
            'is_active' => true,
            'actual_size' => count($customerIds),
            'created_by' => 1, // System user
        ]);

        // إضافة العملاء للشريحة
        $segment->customers()->sync($customerIds);

        return $segment;
    }

    /**
     * تحديد مرحلة دورة الحياة
     */
    protected function determineLifecycleStage(Customer $customer): string
    {
        $daysSinceCreation = $customer->created_at->diffInDays(now());
        $totalSpent = $customer->total_spent;
        $lastPurchase = $customer->last_purchase_at;
        $totalOrders = $customer->total_orders;

        if ($totalSpent == 0) {
            return 'prospects';
        } elseif ($daysSinceCreation <= 90 && $totalOrders <= 2) {
            return 'new_customers';
        } elseif ($totalSpent >= 100000 || $customer->tier === 'vip') {
            return 'vip_customers';
        } elseif ($totalOrders >= 10 && $lastPurchase && $lastPurchase->diffInDays(now()) <= 180) {
            return 'loyal_customers';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) <= 180) {
            return 'active_customers';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 365) {
            return 'churned';
        } elseif ($lastPurchase && $lastPurchase->diffInDays(now()) > 180) {
            return 'at_risk';
        } else {
            return 'dormant';
        }
    }

    /**
     * حساب نقاط الحداثة
     */
    protected function calculateRecencyScore(int $days): int
    {
        if ($days <= 30) return 5;
        if ($days <= 90) return 4;
        if ($days <= 180) return 3;
        if ($days <= 365) return 2;
        return 1;
    }

    /**
     * حساب نقاط التكرار
     */
    protected function calculateFrequencyScore(int $orders): int
    {
        if ($orders >= 20) return 5;
        if ($orders >= 10) return 4;
        if ($orders >= 5) return 3;
        if ($orders >= 2) return 2;
        return 1;
    }

    /**
     * حساب نقاط القيمة النقدية
     */
    protected function calculateMonetaryScore(float $amount): int
    {
        if ($amount >= 100000) return 5;
        if ($amount >= 50000) return 4;
        if ($amount >= 10000) return 3;
        if ($amount >= 1000) return 2;
        return 1;
    }

    /**
     * تصنيف شرائح RFM
     */
    protected function categorizeRFMSegments(array $rfmData): array
    {
        $segments = [
            'champions' => [], // 555, 554, 544, 545, 454, 455, 445
            'loyal_customers' => [], // 543, 444, 435, 355, 354, 345, 344, 335
            'potential_loyalists' => [], // 512, 511, 422, 421, 412, 411, 311
            'new_customers' => [], // 512, 511, 422, 421, 412, 411, 311
            'promising' => [], // 512, 511, 422, 421, 412, 411, 311
            'need_attention' => [], // 512, 511, 422, 421, 412, 411, 311
            'about_to_sleep' => [], // 155, 154, 144, 214, 215, 115, 114
            'at_risk' => [], // 155, 154, 144, 214, 215, 115, 114
            'cannot_lose_them' => [], // 155, 154, 144, 214, 215, 115, 114
            'hibernating' => [], // 155, 154, 144, 214, 215, 115, 114
            'lost' => [], // 155, 154, 144, 214, 215, 115, 114
        ];

        foreach ($rfmData as $customerId => $data) {
            $rfmScore = $data['recency_score'] . $data['frequency_score'] . $data['monetary_score'];
            $segment = $this->getRFMSegment($rfmScore);
            $segments[$segment][] = $customerId;
        }

        return $segments;
    }

    /**
     * تحديد شريحة RFM
     */
    protected function getRFMSegment(string $rfmScore): string
    {
        $champions = ['555', '554', '544', '545', '454', '455', '445'];
        $loyalCustomers = ['543', '444', '435', '355', '354', '345', '344', '335'];
        $potentialLoyalists = ['553', '551', '552', '541', '542', '533', '532', '531', '452', '451'];
        $newCustomers = ['512', '511', '422', '421', '412', '411', '311'];
        $promising = ['525', '524', '523', '522', '521', '515', '514', '513', '425', '424', '413', '414', '415', '315', '314', '313'];
        $needAttention = ['535', '534', '443', '434', '343', '334', '325', '324'];
        $aboutToSleep = ['155', '154', '144', '214', '215', '115', '114'];
        $atRisk = ['255', '254', '245', '244', '253', '252', '243', '242', '235', '234', '225', '224', '153', '152', '145', '143', '142', '135', '134', '125', '124'];
        $cannotLoseThem = ['155', '154', '144', '214', '215', '115', '114'];
        $hibernating = ['332', '322', '231', '241', '251', '233', '232', '223', '222', '132', '123', '122', '212', '211'];
        $lost = ['155', '154', '144', '214', '215', '115', '114', '113', '112', '111'];

        if (in_array($rfmScore, $champions)) return 'champions';
        if (in_array($rfmScore, $loyalCustomers)) return 'loyal_customers';
        if (in_array($rfmScore, $potentialLoyalists)) return 'potential_loyalists';
        if (in_array($rfmScore, $newCustomers)) return 'new_customers';
        if (in_array($rfmScore, $promising)) return 'promising';
        if (in_array($rfmScore, $needAttention)) return 'need_attention';
        if (in_array($rfmScore, $aboutToSleep)) return 'about_to_sleep';
        if (in_array($rfmScore, $atRisk)) return 'at_risk';
        if (in_array($rfmScore, $cannotLoseThem)) return 'cannot_lose_them';
        if (in_array($rfmScore, $hibernating)) return 'hibernating';
        
        return 'lost';
    }

    // دوال مساعدة إضافية
    protected function applyRule($query, array $rule) { return $query; }
    protected function groupByRegion(): array { return []; }
    protected function analyzeCommunicationPreferences(): array { return []; }
    protected function analyzeProductPreferences(): array { return []; }
    protected function analyzeChannelPreferences(): array { return []; }
    protected function analyzeTimingPreferences(): array { return []; }
    protected function analyzeContentPreferences(): array { return []; }
    protected function prepareCustomerDataForML(): array { return []; }
    protected function performKMeansClustering(array $data): array { return []; }
    protected function createAISegment(int $clusterId, array $customerIds): CustomerSegment { return new CustomerSegment(); }
}
