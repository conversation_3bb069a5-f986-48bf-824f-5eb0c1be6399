<?php

namespace App\Domains\Compliance\Services;

use App\Domains\Compliance\Models\Country;
use App\Domains\Compliance\Models\TaxConfiguration;
use App\Domains\Compliance\Models\TaxTransaction;
use App\Domains\Compliance\Models\TaxReturn;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * خدمة حساب الضرائب المتقدمة
 * تدير حسابات الضرائب المعقدة لجميع الدول المدعومة
 */
class TaxCalculationService
{
    /**
     * الحصول على ملخص الضرائب للدولة
     */
    public function getTaxSummary(Country $country, array $dateRange): array
    {
        $cacheKey = "tax_summary_{$country->code}_" . md5(serialize($dateRange));
        
        return Cache::remember($cacheKey, 1800, function () use ($country, $dateRange) {
            return [
                'total_tax_liability' => $this->calculateTotalTaxLiability($country, $dateRange),
                'tax_by_type' => $this->getTaxByType($country, $dateRange),
                'monthly_trend' => $this->getMonthlyTaxTrend($country, $dateRange),
                'compliance_status' => $this->getTaxComplianceStatus($country),
                'upcoming_payments' => $this->getUpcomingTaxPayments($country),
                'refunds_due' => $this->getTaxRefundsDue($country),
                'penalties_and_interest' => $this->getPenaltiesAndInterest($country, $dateRange),
                'estimated_next_period' => $this->estimateNextPeriodTax($country),
            ];
        });
    }

    /**
     * حساب إجمالي الالتزام الضريبي
     */
    protected function calculateTotalTaxLiability(Country $country, array $dateRange): array
    {
        $taxConfigurations = $country->taxes()->active()->get();
        $totalLiability = 0;
        $breakdown = [];

        foreach ($taxConfigurations as $taxConfig) {
            $liability = $this->calculateTaxLiabilityForType($taxConfig, $dateRange);
            $totalLiability += $liability['amount'];
            $breakdown[$taxConfig->tax_type] = $liability;
        }

        return [
            'total_amount' => $totalLiability,
            'currency' => $country->currency,
            'breakdown' => $breakdown,
            'period' => [
                'from' => $dateRange['from']->format('Y-m-d'),
                'to' => $dateRange['to']->format('Y-m-d'),
            ],
        ];
    }

    /**
     * حساب الالتزام الضريبي لنوع معين
     */
    protected function calculateTaxLiabilityForType(TaxConfiguration $taxConfig, array $dateRange): array
    {
        $transactions = TaxTransaction::where('tax_configuration_id', $taxConfig->id)
            ->whereBetween('transaction_date', [$dateRange['from'], $dateRange['to']])
            ->get();

        $totalTaxable = $transactions->sum('taxable_amount');
        $totalTax = $transactions->sum('tax_amount');
        $totalPaid = $transactions->where('status', 'paid')->sum('tax_amount');
        $outstanding = $totalTax - $totalPaid;

        return [
            'tax_type' => $taxConfig->tax_type,
            'tax_name' => $taxConfig->tax_name_ar,
            'taxable_amount' => $totalTaxable,
            'amount' => $totalTax,
            'paid' => $totalPaid,
            'outstanding' => $outstanding,
            'transaction_count' => $transactions->count(),
            'average_rate' => $totalTaxable > 0 ? ($totalTax / $totalTaxable) * 100 : 0,
        ];
    }

    /**
     * الحصول على الضرائب حسب النوع
     */
    protected function getTaxByType(Country $country, array $dateRange): array
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->where('tax_configurations.country_id', $country->id)
            ->whereBetween('tax_transactions.transaction_date', [$dateRange['from'], $dateRange['to']])
            ->select(
                'tax_configurations.tax_type',
                'tax_configurations.tax_name_ar',
                DB::raw('SUM(tax_transactions.taxable_amount) as total_taxable'),
                DB::raw('SUM(tax_transactions.tax_amount) as total_tax'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('AVG(tax_transactions.effective_rate) as average_rate')
            )
            ->groupBy('tax_configurations.tax_type', 'tax_configurations.tax_name_ar')
            ->get()
            ->toArray();
    }

    /**
     * الحصول على الاتجاه الشهري للضرائب
     */
    protected function getMonthlyTaxTrend(Country $country, array $dateRange): array
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->where('tax_configurations.country_id', $country->id)
            ->whereBetween('tax_transactions.transaction_date', [$dateRange['from'], $dateRange['to']])
            ->select(
                DB::raw('YEAR(tax_transactions.transaction_date) as year'),
                DB::raw('MONTH(tax_transactions.transaction_date) as month'),
                DB::raw('SUM(tax_transactions.tax_amount) as total_tax'),
                DB::raw('SUM(tax_transactions.taxable_amount) as total_taxable'),
                DB::raw('COUNT(*) as transaction_count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => sprintf('%04d-%02d', $item->year, $item->month),
                    'total_tax' => $item->total_tax,
                    'total_taxable' => $item->total_taxable,
                    'transaction_count' => $item->transaction_count,
                    'effective_rate' => $item->total_taxable > 0 ? ($item->total_tax / $item->total_taxable) * 100 : 0,
                ];
            })
            ->toArray();
    }

    /**
     * حساب ضريبة معاملة واحدة
     */
    public function calculateTransactionTax(array $transactionData, Country $country): array
    {
        $taxConfigurations = $country->taxes()->active()->get();
        $totalTax = 0;
        $taxBreakdown = [];

        foreach ($taxConfigurations as $taxConfig) {
            if ($this->isTransactionSubjectToTax($transactionData, $taxConfig)) {
                $taxCalculation = $taxConfig->calculateTax(
                    $transactionData['amount'],
                    $this->buildTaxContext($transactionData, $country)
                );

                if ($taxCalculation['tax_amount'] > 0) {
                    $totalTax += $taxCalculation['tax_amount'];
                    $taxBreakdown[$taxConfig->tax_type] = $taxCalculation;
                }
            }
        }

        return [
            'base_amount' => $transactionData['amount'],
            'total_tax' => $totalTax,
            'total_amount' => $transactionData['amount'] + $totalTax,
            'tax_breakdown' => $taxBreakdown,
            'currency' => $country->currency,
            'calculated_at' => now(),
        ];
    }

    /**
     * التحقق من خضوع المعاملة للضريبة
     */
    protected function isTransactionSubjectToTax(array $transactionData, TaxConfiguration $taxConfig): bool
    {
        // فحص الحد الأدنى
        $minThreshold = $taxConfig->minimum_thresholds['transaction_amount'] ?? 0;
        if ($transactionData['amount'] < $minThreshold) {
            return false;
        }

        // فحص الإعفاءات
        $exemptions = $taxConfig->exemptions ?? [];
        foreach ($exemptions as $exemption) {
            if ($this->checkExemptionApplies($exemption, $transactionData)) {
                return false;
            }
        }

        // فحص نوع المعاملة
        $applicableTypes = $taxConfig->special_rules['applicable_transaction_types'] ?? [];
        if (!empty($applicableTypes) && !in_array($transactionData['type'], $applicableTypes)) {
            return false;
        }

        return true;
    }

    /**
     * بناء سياق الضريبة
     */
    protected function buildTaxContext(array $transactionData, Country $country): array
    {
        return [
            'transaction_type' => $transactionData['type'] ?? 'general',
            'customer_type' => $transactionData['customer_type'] ?? 'individual',
            'industry' => $transactionData['industry'] ?? null,
            'location' => $transactionData['location'] ?? $country->code,
            'date' => $transactionData['date'] ?? now(),
            'currency' => $transactionData['currency'] ?? $country->currency,
            'is_export' => $transactionData['is_export'] ?? false,
            'is_import' => $transactionData['is_import'] ?? false,
            'product_category' => $transactionData['product_category'] ?? null,
        ];
    }

    /**
     * حساب ضريبة القيمة المضافة المغربية
     */
    public function calculateMoroccanTVA(float $amount, string $category = 'standard'): array
    {
        $rates = [
            'standard' => 20.00,
            'reduced_1' => 14.00,
            'reduced_2' => 10.00,
            'reduced_3' => 7.00,
            'zero' => 0.00,
        ];

        $rate = $rates[$category] ?? $rates['standard'];
        $taxAmount = $amount * ($rate / 100);

        return [
            'base_amount' => $amount,
            'tax_rate' => $rate,
            'tax_amount' => $taxAmount,
            'total_amount' => $amount + $taxAmount,
            'category' => $category,
            'currency' => 'MAD',
        ];
    }

    /**
     * حساب ضريبة القيمة المضافة السعودية
     */
    public function calculateSaudiVAT(float $amount, string $category = 'standard'): array
    {
        $rates = [
            'standard' => 15.00,
            'zero' => 0.00,
            'exempt' => null,
        ];

        $rate = $rates[$category] ?? $rates['standard'];
        $taxAmount = $rate !== null ? $amount * ($rate / 100) : 0;

        return [
            'base_amount' => $amount,
            'tax_rate' => $rate,
            'tax_amount' => $taxAmount,
            'total_amount' => $amount + $taxAmount,
            'category' => $category,
            'currency' => 'SAR',
        ];
    }

    /**
     * حساب الزكاة السعودية
     */
    public function calculateSaudiZakat(float $netWorth, array $assets = []): array
    {
        $zakatRate = 2.5;
        $nisab = 85 * 4.25; // نصاب الذهب بالجرام × سعر الجرام
        
        // حساب الأصول الخاضعة للزكاة
        $zakatableAssets = $this->calculateZakatableAssets($assets);
        $zakatBase = max(0, $zakatableAssets - $this->calculateZakatDeductions($assets));
        
        $zakatAmount = $zakatBase >= $nisab ? $zakatBase * ($zakatRate / 100) : 0;

        return [
            'zakat_base' => $zakatBase,
            'nisab' => $nisab,
            'zakat_rate' => $zakatRate,
            'zakat_amount' => $zakatAmount,
            'is_liable' => $zakatBase >= $nisab,
            'currency' => 'SAR',
            'calculation_date' => now(),
        ];
    }

    /**
     * حساب ضريبة القيمة المضافة الإماراتية
     */
    public function calculateUAEVAT(float $amount, string $category = 'standard'): array
    {
        $rates = [
            'standard' => 5.00,
            'zero' => 0.00,
            'exempt' => null,
        ];

        $rate = $rates[$category] ?? $rates['standard'];
        $taxAmount = $rate !== null ? $amount * ($rate / 100) : 0;

        return [
            'base_amount' => $amount,
            'tax_rate' => $rate,
            'tax_amount' => $taxAmount,
            'total_amount' => $amount + $taxAmount,
            'category' => $category,
            'currency' => 'AED',
        ];
    }

    /**
     * حساب ضريبة السلع الانتقائية الإماراتية
     */
    public function calculateUAEExciseTax(float $amount, string $productType): array
    {
        $rates = [
            'tobacco' => 100.00,
            'carbonated_drinks' => 50.00,
            'energy_drinks' => 100.00,
            'sweetened_drinks' => 50.00,
        ];

        $rate = $rates[$productType] ?? 0;
        $taxAmount = $amount * ($rate / 100);

        return [
            'base_amount' => $amount,
            'product_type' => $productType,
            'tax_rate' => $rate,
            'tax_amount' => $taxAmount,
            'total_amount' => $amount + $taxAmount,
            'currency' => 'AED',
        ];
    }

    /**
     * حساب ضريبة القيمة المضافة المصرية
     */
    public function calculateEgyptianVAT(float $amount, string $category = 'standard'): array
    {
        $rates = [
            'standard' => 14.00,
            'zero' => 0.00,
            'exempt' => null,
        ];

        $rate = $rates[$category] ?? $rates['standard'];
        $taxAmount = $rate !== null ? $amount * ($rate / 100) : 0;

        return [
            'base_amount' => $amount,
            'tax_rate' => $rate,
            'tax_amount' => $taxAmount,
            'total_amount' => $amount + $taxAmount,
            'category' => $category,
            'currency' => 'EGP',
        ];
    }

    /**
     * الحصول على إجمالي TVA المحصل (المغرب)
     */
    public function getTotalTVACollected(string $countryCode, array $dateRange): float
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->join('countries', 'tax_configurations.country_id', '=', 'countries.id')
            ->where('countries.code', $countryCode)
            ->where('tax_configurations.tax_type', 'vat')
            ->whereBetween('tax_transactions.transaction_date', [$dateRange['from'], $dateRange['to']])
            ->sum('tax_transactions.tax_amount');
    }

    /**
     * الحصول على TVA حسب المعدل (المغرب)
     */
    public function getTVAByRate(string $countryCode, array $dateRange): array
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->join('countries', 'tax_configurations.country_id', '=', 'countries.id')
            ->where('countries.code', $countryCode)
            ->where('tax_configurations.tax_type', 'vat')
            ->whereBetween('tax_transactions.transaction_date', [$dateRange['from'], $dateRange['to']])
            ->select(
                'tax_transactions.effective_rate',
                DB::raw('SUM(tax_transactions.tax_amount) as total_tax'),
                DB::raw('SUM(tax_transactions.taxable_amount) as total_taxable'),
                DB::raw('COUNT(*) as transaction_count')
            )
            ->groupBy('tax_transactions.effective_rate')
            ->get()
            ->toArray();
    }

    /**
     * الحصول على اتجاه TVA الشهري (المغرب)
     */
    public function getMonthlyTVATrend(string $countryCode, array $dateRange): array
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->join('countries', 'tax_configurations.country_id', '=', 'countries.id')
            ->where('countries.code', $countryCode)
            ->where('tax_configurations.tax_type', 'vat')
            ->whereBetween('tax_transactions.transaction_date', [$dateRange['from'], $dateRange['to']])
            ->select(
                DB::raw('YEAR(tax_transactions.transaction_date) as year'),
                DB::raw('MONTH(tax_transactions.transaction_date) as month'),
                DB::raw('SUM(tax_transactions.tax_amount) as total_tax')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => sprintf('%04d-%02d', $item->year, $item->month),
                    'amount' => $item->total_tax,
                ];
            })
            ->toArray();
    }

    /**
     * الحصول على موعد تقديم TVA القادم (المغرب)
     */
    public function getNextTVAFilingDeadline(string $countryCode): ?Carbon
    {
        $country = Country::where('code', $countryCode)->first();
        if (!$country) return null;

        $tvaConfig = $country->taxes()->where('tax_type', 'vat')->first();
        return $tvaConfig?->getNextFilingDeadline();
    }

    /**
     * الحصول على TVA المستحق (المغرب)
     */
    public function getOutstandingTVA(string $countryCode): float
    {
        return TaxTransaction::join('tax_configurations', 'tax_transactions.tax_configuration_id', '=', 'tax_configurations.id')
            ->join('countries', 'tax_configurations.country_id', '=', 'countries.id')
            ->where('countries.code', $countryCode)
            ->where('tax_configurations.tax_type', 'vat')
            ->where('tax_transactions.status', '!=', 'paid')
            ->sum('tax_transactions.tax_amount');
    }

    // طرق مساعدة للحسابات المتقدمة
    protected function checkExemptionApplies(array $exemption, array $transactionData): bool
    {
        $conditions = $exemption['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            $field = $condition['field'];
            $operator = $condition['operator'];
            $value = $condition['value'];
            $actualValue = $transactionData[$field] ?? null;

            $result = match ($operator) {
                '=' => $actualValue == $value,
                '!=' => $actualValue != $value,
                '>' => $actualValue > $value,
                '<' => $actualValue < $value,
                'in' => in_array($actualValue, (array) $value),
                'not_in' => !in_array($actualValue, (array) $value),
                default => false,
            };

            if (!$result) {
                return false;
            }
        }

        return true;
    }

    protected function calculateZakatableAssets(array $assets): float
    {
        $zakatableTotal = 0;
        
        foreach ($assets as $asset) {
            if ($asset['type'] === 'cash' || $asset['type'] === 'trade_goods' || $asset['type'] === 'receivables') {
                $zakatableTotal += $asset['value'];
            }
        }
        
        return $zakatableTotal;
    }

    protected function calculateZakatDeductions(array $assets): float
    {
        $deductions = 0;
        
        foreach ($assets as $asset) {
            if ($asset['type'] === 'debt' || $asset['type'] === 'payables') {
                $deductions += $asset['value'];
            }
        }
        
        return $deductions;
    }

    protected function getTaxComplianceStatus(Country $country): array
    {
        return [
            'status' => 'compliant',
            'last_filing' => now()->subDays(15),
            'next_deadline' => now()->addDays(15),
            'outstanding_issues' => 0,
        ];
    }

    protected function getUpcomingTaxPayments(Country $country): array
    {
        return []; // منطق المدفوعات القادمة
    }

    protected function getTaxRefundsDue(Country $country): array
    {
        return []; // منطق المبالغ المستردة
    }

    protected function getPenaltiesAndInterest(Country $country, array $dateRange): array
    {
        return [
            'total_penalties' => 0,
            'total_interest' => 0,
            'breakdown' => [],
        ];
    }

    protected function estimateNextPeriodTax(Country $country): array
    {
        return [
            'estimated_amount' => 0,
            'confidence_level' => 'medium',
            'based_on' => 'historical_average',
        ];
    }
}
