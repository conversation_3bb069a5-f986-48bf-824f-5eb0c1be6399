<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * نموذج تكوين الضمان الاجتماعي
 * يدير أنظمة الضمان الاجتماعي والتأمينات لكل دولة
 */
class SocialSecurityConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'country_id',
        'system_name',
        'system_name_ar',
        'system_name_en',
        'system_code',
        'authority_name',
        'contribution_rates',
        'salary_brackets',
        'coverage_types',
        'benefit_types',
        'eligibility_criteria',
        'calculation_method',
        'contribution_caps',
        'minimum_contributions',
        'employer_obligations',
        'employee_obligations',
        'filing_frequency',
        'filing_deadlines',
        'payment_deadlines',
        'penalty_structure',
        'late_payment_interest',
        'registration_requirements',
        'reporting_requirements',
        'audit_requirements',
        'exemption_criteria',
        'special_categories',
        'seasonal_workers_rules',
        'foreign_workers_rules',
        'retirement_age',
        'disability_provisions',
        'survivor_benefits',
        'medical_coverage',
        'unemployment_benefits',
        'maternity_benefits',
        'api_integration_config',
        'electronic_filing_mandatory',
        'digital_certificate_required',
        'validation_rules',
        'compliance_requirements',
        'is_active',
        'effective_from',
        'last_updated_by_authority',
    ];

    protected $casts = [
        'contribution_rates' => 'array',
        'salary_brackets' => 'array',
        'coverage_types' => 'array',
        'benefit_types' => 'array',
        'eligibility_criteria' => 'array',
        'contribution_caps' => 'array',
        'minimum_contributions' => 'array',
        'employer_obligations' => 'array',
        'employee_obligations' => 'array',
        'filing_deadlines' => 'array',
        'payment_deadlines' => 'array',
        'penalty_structure' => 'array',
        'late_payment_interest' => 'array',
        'registration_requirements' => 'array',
        'reporting_requirements' => 'array',
        'audit_requirements' => 'array',
        'exemption_criteria' => 'array',
        'special_categories' => 'array',
        'seasonal_workers_rules' => 'array',
        'foreign_workers_rules' => 'array',
        'disability_provisions' => 'array',
        'survivor_benefits' => 'array',
        'medical_coverage' => 'array',
        'unemployment_benefits' => 'array',
        'maternity_benefits' => 'array',
        'api_integration_config' => 'array',
        'validation_rules' => 'array',
        'compliance_requirements' => 'array',
        'electronic_filing_mandatory' => 'boolean',
        'digital_certificate_required' => 'boolean',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'last_updated_by_authority' => 'datetime',
    ];

    /**
     * أنظمة الضمان الاجتماعي المدعومة
     */
    const SUPPORTED_SYSTEMS = [
        'MA_CNSS' => [
            'name' => 'الصندوق الوطني للضمان الاجتماعي',
            'code' => 'CNSS',
            'country' => 'MA',
            'total_rate' => 26.82,
            'employer_rate' => 20.48,
            'employee_rate' => 6.34,
        ],
        'MA_AMO' => [
            'name' => 'التأمين الإجباري الأساسي عن المرض',
            'code' => 'AMO',
            'country' => 'MA',
            'total_rate' => 5.50,
            'employer_rate' => 3.50,
            'employee_rate' => 2.00,
        ],
        'SA_GOSI' => [
            'name' => 'التأمينات الاجتماعية',
            'code' => 'GOSI',
            'country' => 'SA',
            'total_rate' => 22.00,
            'employer_rate' => 12.00,
            'employee_rate' => 10.00,
        ],
        'AE_SSA' => [
            'name' => 'هيئة الضمان الاجتماعي',
            'code' => 'SSA',
            'country' => 'AE',
            'total_rate' => 17.50,
            'employer_rate' => 12.50,
            'employee_rate' => 5.00,
        ],
        'EG_NOSI' => [
            'name' => 'الهيئة القومية للتأمين الاجتماعي',
            'code' => 'NOSI',
            'country' => 'EG',
            'total_rate' => 40.00,
            'employer_rate' => 26.00,
            'employee_rate' => 14.00,
        ],
    ];

    /**
     * أنواع التغطية
     */
    const COVERAGE_TYPES = [
        'retirement' => 'التقاعد',
        'disability' => 'العجز',
        'death' => 'الوفاة',
        'medical' => 'التأمين الصحي',
        'unemployment' => 'البطالة',
        'maternity' => 'الأمومة',
        'work_injury' => 'إصابات العمل',
        'family_allowance' => 'العلاوات العائلية',
    ];

    /**
     * طرق الحساب
     */
    const CALCULATION_METHODS = [
        'percentage_of_salary' => 'نسبة من الراتب',
        'fixed_amount' => 'مبلغ ثابت',
        'tiered_percentage' => 'نسبة متدرجة',
        'points_system' => 'نظام النقاط',
        'hybrid' => 'مختلط',
    ];

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع مساهمات الضمان الاجتماعي
     */
    public function contributions(): HasMany
    {
        return $this->hasMany(SocialSecurityContribution::class);
    }

    /**
     * العلاقة مع تقارير الضمان الاجتماعي
     */
    public function reports(): HasMany
    {
        return $this->hasMany(SocialSecurityReport::class);
    }

    /**
     * حساب مساهمات الضمان الاجتماعي
     */
    public function calculateContributions(float $salary, array $employeeData = []): array
    {
        $calculation = [
            'gross_salary' => $salary,
            'contributory_salary' => $this->calculateContributorySalary($salary),
            'employer_contribution' => 0,
            'employee_contribution' => 0,
            'total_contribution' => 0,
            'breakdown' => [],
            'exemptions_applied' => [],
            'caps_applied' => [],
        ];

        // تطبيق الإعفاءات
        if ($this->isEmployeeExempt($employeeData)) {
            $calculation['exemptions_applied'] = $this->getApplicableExemptions($employeeData);
            return $calculation;
        }

        // حساب المساهمات حسب النوع
        $contributorySalary = $calculation['contributory_salary'];
        $rates = $this->contribution_rates;

        foreach ($this->coverage_types as $coverageType) {
            if (isset($rates[$coverageType])) {
                $coverageCalculation = $this->calculateCoverageContribution(
                    $contributorySalary,
                    $rates[$coverageType],
                    $coverageType
                );

                $calculation['employer_contribution'] += $coverageCalculation['employer_amount'];
                $calculation['employee_contribution'] += $coverageCalculation['employee_amount'];
                $calculation['breakdown'][$coverageType] = $coverageCalculation;
            }
        }

        // تطبيق الحدود القصوى
        $calculation = $this->applyContributionCaps($calculation);

        $calculation['total_contribution'] = $calculation['employer_contribution'] + $calculation['employee_contribution'];

        return $calculation;
    }

    /**
     * حساب الراتب الخاضع للمساهمة
     */
    protected function calculateContributorySalary(float $salary): float
    {
        $minContributory = $this->minimum_contributions['salary'] ?? 0;
        $maxContributory = $this->contribution_caps['salary'] ?? PHP_FLOAT_MAX;

        return max($minContributory, min($salary, $maxContributory));
    }

    /**
     * حساب مساهمة تغطية معينة
     */
    protected function calculateCoverageContribution(float $salary, array $rates, string $coverageType): array
    {
        $employerRate = $rates['employer_rate'] ?? 0;
        $employeeRate = $rates['employee_rate'] ?? 0;

        $employerAmount = $salary * ($employerRate / 100);
        $employeeAmount = $salary * ($employeeRate / 100);

        return [
            'coverage_type' => $coverageType,
            'contributory_salary' => $salary,
            'employer_rate' => $employerRate,
            'employee_rate' => $employeeRate,
            'employer_amount' => $employerAmount,
            'employee_amount' => $employeeAmount,
            'total_amount' => $employerAmount + $employeeAmount,
        ];
    }

    /**
     * تطبيق الحدود القصوى للمساهمات
     */
    protected function applyContributionCaps(array $calculation): array
    {
        $caps = $this->contribution_caps;

        if (isset($caps['employer_monthly_max'])) {
            if ($calculation['employer_contribution'] > $caps['employer_monthly_max']) {
                $calculation['caps_applied'][] = 'employer_monthly_max';
                $calculation['employer_contribution'] = $caps['employer_monthly_max'];
            }
        }

        if (isset($caps['employee_monthly_max'])) {
            if ($calculation['employee_contribution'] > $caps['employee_monthly_max']) {
                $calculation['caps_applied'][] = 'employee_monthly_max';
                $calculation['employee_contribution'] = $caps['employee_monthly_max'];
            }
        }

        return $calculation;
    }

    /**
     * التحقق من إعفاء الموظف
     */
    protected function isEmployeeExempt(array $employeeData): bool
    {
        $exemptionCriteria = $this->exemption_criteria ?? [];

        foreach ($exemptionCriteria as $criterion) {
            if ($this->checkExemptionCriterion($criterion, $employeeData)) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص معيار الإعفاء
     */
    protected function checkExemptionCriterion(array $criterion, array $employeeData): bool
    {
        $field = $criterion['field'];
        $operator = $criterion['operator'];
        $value = $criterion['value'];
        $actualValue = $employeeData[$field] ?? null;

        return match ($operator) {
            '=' => $actualValue == $value,
            '!=' => $actualValue != $value,
            '>' => $actualValue > $value,
            '<' => $actualValue < $value,
            'in' => in_array($actualValue, (array) $value),
            'not_in' => !in_array($actualValue, (array) $value),
            default => false,
        };
    }

    /**
     * الحصول على الإعفاءات المطبقة
     */
    protected function getApplicableExemptions(array $employeeData): array
    {
        $exemptions = [];
        $exemptionCriteria = $this->exemption_criteria ?? [];

        foreach ($exemptionCriteria as $criterion) {
            if ($this->checkExemptionCriterion($criterion, $employeeData)) {
                $exemptions[] = $criterion;
            }
        }

        return $exemptions;
    }

    /**
     * الحصول على موعد التقديم القادم
     */
    public function getNextFilingDeadline(): ?Carbon
    {
        $deadlines = $this->filing_deadlines ?? [];
        $frequency = $this->filing_frequency;

        $now = now();
        $nextDeadline = null;

        switch ($frequency) {
            case 'monthly':
                $nextDeadline = $now->copy()->addMonth()->day($deadlines['day'] ?? 15);
                break;
            case 'quarterly':
                $nextQuarter = $now->copy()->addMonths(3 - ($now->month % 3));
                $nextDeadline = $nextQuarter->day($deadlines['day'] ?? 15);
                break;
            case 'annual':
                $nextDeadline = $now->copy()->addYear()->month($deadlines['month'] ?? 1)->day($deadlines['day'] ?? 31);
                break;
        }

        return $nextDeadline;
    }

    /**
     * التحقق من التأخير في التقديم
     */
    public function isFilingOverdue(): bool
    {
        $deadline = $this->getNextFilingDeadline();
        return $deadline && $deadline->isPast();
    }

    /**
     * حساب الغرامة على التأخير
     */
    public function calculateLatePenalty(float $contributionAmount, Carbon $dueDate): array
    {
        $penaltyStructure = $this->penalty_structure ?? [];
        $interestRates = $this->late_payment_interest ?? [];

        $daysLate = max(0, now()->diffInDays($dueDate));
        $penalty = 0;
        $interest = 0;

        if ($daysLate > 0) {
            // حساب الغرامة
            $penaltyRate = $penaltyStructure['rate'] ?? 0;
            $penaltyMin = $penaltyStructure['minimum'] ?? 0;
            $penaltyMax = $penaltyStructure['maximum'] ?? PHP_FLOAT_MAX;

            $penalty = max($penaltyMin, min($penaltyMax, $contributionAmount * ($penaltyRate / 100)));

            // حساب الفوائد
            $interestRate = $interestRates['annual_rate'] ?? 0;
            $dailyRate = $interestRate / 365 / 100;
            $interest = $contributionAmount * $dailyRate * $daysLate;
        }

        return [
            'penalty' => $penalty,
            'interest' => $interest,
            'total_additional' => $penalty + $interest,
            'days_late' => $daysLate,
            'original_amount' => $contributionAmount,
            'total_due' => $contributionAmount + $penalty + $interest,
        ];
    }

    /**
     * التحقق من أهلية الاستفادة
     */
    public function checkBenefitEligibility(array $employeeData, string $benefitType): array
    {
        $eligibilityCriteria = $this->eligibility_criteria[$benefitType] ?? [];
        $isEligible = true;
        $failedCriteria = [];

        foreach ($eligibilityCriteria as $criterion) {
            if (!$this->checkEligibilityCriterion($criterion, $employeeData)) {
                $isEligible = false;
                $failedCriteria[] = $criterion;
            }
        }

        return [
            'benefit_type' => $benefitType,
            'is_eligible' => $isEligible,
            'failed_criteria' => $failedCriteria,
            'benefit_details' => $this->benefit_types[$benefitType] ?? [],
        ];
    }

    /**
     * فحص معيار الأهلية
     */
    protected function checkEligibilityCriterion(array $criterion, array $employeeData): bool
    {
        $field = $criterion['field'];
        $operator = $criterion['operator'];
        $value = $criterion['value'];
        $actualValue = $employeeData[$field] ?? null;

        return match ($operator) {
            '=' => $actualValue == $value,
            '!=' => $actualValue != $value,
            '>' => $actualValue > $value,
            '>=' => $actualValue >= $value,
            '<' => $actualValue < $value,
            '<=' => $actualValue <= $value,
            'in' => in_array($actualValue, (array) $value),
            'not_in' => !in_array($actualValue, (array) $value),
            'between' => $actualValue >= $value[0] && $actualValue <= $value[1],
            default => false,
        };
    }

    /**
     * حساب مبلغ الاستفادة
     */
    public function calculateBenefitAmount(array $employeeData, string $benefitType): array
    {
        $benefitDetails = $this->benefit_types[$benefitType] ?? [];
        $calculationMethod = $benefitDetails['calculation_method'] ?? 'percentage_of_salary';

        $amount = match ($calculationMethod) {
            'percentage_of_salary' => $this->calculatePercentageOfSalary($employeeData, $benefitDetails),
            'fixed_amount' => $benefitDetails['amount'] ?? 0,
            'points_based' => $this->calculatePointsBased($employeeData, $benefitDetails),
            'years_of_service' => $this->calculateYearsOfService($employeeData, $benefitDetails),
            default => 0,
        };

        return [
            'benefit_type' => $benefitType,
            'calculation_method' => $calculationMethod,
            'amount' => $amount,
            'currency' => $this->country->currency,
            'calculated_at' => now(),
        ];
    }

    /**
     * حساب نسبة من الراتب
     */
    protected function calculatePercentageOfSalary(array $employeeData, array $benefitDetails): float
    {
        $salary = $employeeData['salary'] ?? 0;
        $percentage = $benefitDetails['percentage'] ?? 0;
        $maxAmount = $benefitDetails['max_amount'] ?? PHP_FLOAT_MAX;

        return min($salary * ($percentage / 100), $maxAmount);
    }

    /**
     * حساب بناءً على النقاط
     */
    protected function calculatePointsBased(array $employeeData, array $benefitDetails): float
    {
        $points = $employeeData['accumulated_points'] ?? 0;
        $pointValue = $benefitDetails['point_value'] ?? 0;

        return $points * $pointValue;
    }

    /**
     * حساب بناءً على سنوات الخدمة
     */
    protected function calculateYearsOfService(array $employeeData, array $benefitDetails): float
    {
        $yearsOfService = $employeeData['years_of_service'] ?? 0;
        $amountPerYear = $benefitDetails['amount_per_year'] ?? 0;
        $maxYears = $benefitDetails['max_years'] ?? PHP_FLOAT_MAX;

        return min($yearsOfService, $maxYears) * $amountPerYear;
    }

    /**
     * Scope للأنظمة النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للأنظمة حسب نوع التغطية
     */
    public function scopeWithCoverage($query, string $coverageType)
    {
        return $query->whereJsonContains('coverage_types', $coverageType);
    }

    /**
     * Scope للأنظمة المطلوب تقديمها
     */
    public function scopeDueForFiling($query)
    {
        return $query->active()
            ->where('electronic_filing_mandatory', true);
    }
}
