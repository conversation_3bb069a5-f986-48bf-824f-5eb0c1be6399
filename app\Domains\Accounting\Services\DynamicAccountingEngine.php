<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\Account;
use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\ChartOfAccounts;
use App\Domains\Accounting\Models\AccountMapping;
use App\Domains\Accounting\Models\AccountingPeriod;
use Illuminate\Support\Facades\DB;

/**
 * المحرك المحاسبي الديناميكي
 * يدعم معايير محاسبية متعددة مع التحويل التلقائي
 */
class DynamicAccountingEngine
{
    protected ChartOfAccounts $defaultChart;
    protected AccountingPeriod $currentPeriod;

    public function __construct()
    {
        $this->defaultChart = ChartOfAccounts::getDefault() ?? 
            throw new \Exception('لم يتم تعيين دليل حسابات افتراضي');
        
        $this->currentPeriod = AccountingPeriod::current() ?? 
            throw new \Exception('لم يتم تعيين فترة محاسبية حالية');
    }

    /**
     * إنشاء قيد محاسبي متعدد المعايير
     */
    public function createJournalEntry(array $entryData, array $standards = ['PCGM']): array
    {
        $entries = [];
        $entryNumber = JournalEntry::generateEntryNumber();

        DB::beginTransaction();
        try {
            foreach ($standards as $standard) {
                $chart = ChartOfAccounts::getByStandard($standard);
                if (!$chart) {
                    throw new \Exception("دليل الحسابات غير موجود للمعيار: {$standard}");
                }

                $standardEntries = $this->createEntriesForStandard($entryData, $chart, $entryNumber);
                $entries[$standard] = $standardEntries;
            }

            DB::commit();
            return $entries;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * إنشاء قيود لمعيار محدد
     */
    protected function createEntriesForStandard(array $entryData, ChartOfAccounts $chart, string $entryNumber): array
    {
        $entries = [];

        foreach ($entryData['lines'] as $line) {
            $account = $this->getAccountForStandard($line['account_code'], $chart);
            if (!$account) {
                throw new \Exception("الحساب غير موجود: {$line['account_code']} في المعيار {$chart->standard}");
            }

            $entry = JournalEntry::create([
                'entry_number' => $entryNumber,
                'reference_number' => $entryData['reference_number'] ?? null,
                'description' => $entryData['description'],
                'entry_date' => $entryData['entry_date'],
                'posting_date' => $entryData['posting_date'] ?? $entryData['entry_date'],
                'period_id' => $this->currentPeriod->id,
                'account_id' => $account->id,
                'debit_amount' => $line['debit_amount'] ?? 0,
                'credit_amount' => $line['credit_amount'] ?? 0,
                'currency' => $entryData['currency'] ?? 'MAD',
                'exchange_rate' => $entryData['exchange_rate'] ?? 1,
                'status' => 'DRAFT',
                'source_type' => $entryData['source_type'] ?? 'MANUAL',
                'source_id' => $entryData['source_id'] ?? null,
                'notes' => $line['notes'] ?? null,
                'metadata' => array_merge($entryData['metadata'] ?? [], [
                    'chart_standard' => $chart->standard,
                    'chart_id' => $chart->id,
                ]),
            ]);

            $entries[] = $entry;
        }

        // التحقق من توازن القيد
        if (!$this->isEntryBalanced($entryNumber)) {
            throw new \Exception('القيد غير متوازن');
        }

        return $entries;
    }

    /**
     * الحصول على حساب لمعيار محدد
     */
    protected function getAccountForStandard(string $accountCode, ChartOfAccounts $chart): ?Account
    {
        // البحث المباشر في الدليل
        $account = $chart->accounts()->where('code', $accountCode)->first();
        
        if ($account) {
            return $account;
        }

        // البحث عبر خريطة التحويل
        if ($chart->id !== $this->defaultChart->id) {
            $defaultAccount = $this->defaultChart->accounts()->where('code', $accountCode)->first();
            if ($defaultAccount) {
                $mapping = AccountMapping::where('source_chart_id', $this->defaultChart->id)
                    ->where('target_chart_id', $chart->id)
                    ->where('source_account_id', $defaultAccount->id)
                    ->where('is_active', true)
                    ->first();

                if ($mapping) {
                    return $mapping->targetAccount;
                }
            }
        }

        return null;
    }

    /**
     * التحقق من توازن القيد
     */
    protected function isEntryBalanced(string $entryNumber): bool
    {
        $totalDebits = JournalEntry::where('entry_number', $entryNumber)->sum('debit_amount');
        $totalCredits = JournalEntry::where('entry_number', $entryNumber)->sum('credit_amount');

        return abs($totalDebits - $totalCredits) < 0.01;
    }

    /**
     * تحويل قيد من معيار إلى آخر
     */
    public function convertEntry(string $entryNumber, string $fromStandard, string $toStandard): array
    {
        $sourceChart = ChartOfAccounts::getByStandard($fromStandard);
        $targetChart = ChartOfAccounts::getByStandard($toStandard);

        if (!$sourceChart || !$targetChart) {
            throw new \Exception('أحد المعايير غير موجود');
        }

        $sourceEntries = JournalEntry::where('entry_number', $entryNumber)
            ->whereHas('account', function ($q) use ($sourceChart) {
                $q->where('chart_id', $sourceChart->id);
            })
            ->get();

        $convertedEntries = [];
        $newEntryNumber = JournalEntry::generateEntryNumber();

        foreach ($sourceEntries as $sourceEntry) {
            $mapping = AccountMapping::where('source_chart_id', $sourceChart->id)
                ->where('target_chart_id', $targetChart->id)
                ->where('source_account_id', $sourceEntry->account_id)
                ->where('is_active', true)
                ->first();

            if (!$mapping) {
                throw new \Exception("لا توجد خريطة تحويل للحساب: {$sourceEntry->account->code}");
            }

            $convertedEntry = $sourceEntry->replicate();
            $convertedEntry->entry_number = $newEntryNumber;
            $convertedEntry->account_id = $mapping->target_account_id;
            $convertedEntry->debit_amount = $mapping->convertAmount($sourceEntry->debit_amount);
            $convertedEntry->credit_amount = $mapping->convertAmount($sourceEntry->credit_amount);
            $convertedEntry->metadata = array_merge($convertedEntry->metadata ?? [], [
                'converted_from' => $fromStandard,
                'original_entry_number' => $entryNumber,
                'mapping_id' => $mapping->id,
            ]);
            $convertedEntry->save();

            $convertedEntries[] = $convertedEntry;
        }

        return $convertedEntries;
    }

    /**
     * توليد تقرير متعدد المعايير
     */
    public function generateMultiStandardReport(string $reportType, array $parameters = []): array
    {
        $standards = $parameters['standards'] ?? ['PCGM', 'IFRS'];
        $report = [];

        foreach ($standards as $standard) {
            $chart = ChartOfAccounts::getByStandard($standard);
            if ($chart) {
                $report[$standard] = $this->generateReportForStandard($reportType, $chart, $parameters);
            }
        }

        return $report;
    }

    /**
     * توليد تقرير لمعيار محدد
     */
    protected function generateReportForStandard(string $reportType, ChartOfAccounts $chart, array $parameters): array
    {
        return match ($reportType) {
            'trial_balance' => $this->generateTrialBalance($chart, $parameters),
            'balance_sheet' => $this->generateBalanceSheet($chart, $parameters),
            'income_statement' => $this->generateIncomeStatement($chart, $parameters),
            'cash_flow' => $this->generateCashFlowStatement($chart, $parameters),
            default => throw new \Exception("نوع التقرير غير مدعوم: {$reportType}"),
        };
    }

    /**
     * توليد ميزان المراجعة
     */
    protected function generateTrialBalance(ChartOfAccounts $chart, array $parameters): array
    {
        $startDate = $parameters['start_date'] ?? $this->currentPeriod->start_date;
        $endDate = $parameters['end_date'] ?? $this->currentPeriod->end_date;

        $accounts = $chart->accounts()
            ->with(['journalEntries' => function ($q) use ($startDate, $endDate) {
                $q->whereBetween('entry_date', [$startDate, $endDate])
                  ->where('status', 'POSTED');
            }])
            ->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $debits = $account->journalEntries->sum('debit_amount');
            $credits = $account->journalEntries->sum('credit_amount');
            $balance = $account->calculateBalance();

            if ($debits > 0 || $credits > 0 || $balance != 0) {
                $trialBalance[] = [
                    'account_code' => $account->code,
                    'account_name' => $account->getLocalizedName(),
                    'account_type' => $account->account_type,
                    'debits' => $debits,
                    'credits' => $credits,
                    'balance' => $balance,
                ];

                $totalDebits += $debits;
                $totalCredits += $credits;
            }
        }

        return [
            'accounts' => $trialBalance,
            'totals' => [
                'total_debits' => $totalDebits,
                'total_credits' => $totalCredits,
                'difference' => $totalDebits - $totalCredits,
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
            'chart_info' => [
                'name' => $chart->name,
                'standard' => $chart->standard,
            ],
        ];
    }

    /**
     * توليد الميزانية العمومية
     */
    protected function generateBalanceSheet(ChartOfAccounts $chart, array $parameters): array
    {
        $asOfDate = $parameters['as_of_date'] ?? now();

        $assets = $this->getAccountBalancesByType($chart, 'ASSET', $asOfDate);
        $liabilities = $this->getAccountBalancesByType($chart, 'LIABILITY', $asOfDate);
        $equity = $this->getAccountBalancesByType($chart, 'EQUITY', $asOfDate);

        $totalAssets = collect($assets)->sum('balance');
        $totalLiabilities = collect($liabilities)->sum('balance');
        $totalEquity = collect($equity)->sum('balance');

        return [
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'totals' => [
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
                'total_liabilities_equity' => $totalLiabilities + $totalEquity,
                'difference' => $totalAssets - ($totalLiabilities + $totalEquity),
            ],
            'as_of_date' => $asOfDate,
            'chart_info' => [
                'name' => $chart->name,
                'standard' => $chart->standard,
            ],
        ];
    }

    /**
     * الحصول على أرصدة الحسابات حسب النوع
     */
    protected function getAccountBalancesByType(ChartOfAccounts $chart, string $accountType, $asOfDate): array
    {
        $accounts = $chart->accounts()
            ->where('account_type', $accountType)
            ->with(['journalEntries' => function ($q) use ($asOfDate) {
                $q->where('entry_date', '<=', $asOfDate)
                  ->where('status', 'POSTED');
            }])
            ->get();

        $balances = [];
        foreach ($accounts as $account) {
            $balance = $account->calculateBalance();
            if ($balance != 0) {
                $balances[] = [
                    'account_code' => $account->code,
                    'account_name' => $account->getLocalizedName(),
                    'balance' => $balance,
                ];
            }
        }

        return $balances;
    }

    /**
     * توليد قائمة الدخل
     */
    protected function generateIncomeStatement(ChartOfAccounts $chart, array $parameters): array
    {
        $startDate = $parameters['start_date'] ?? $this->currentPeriod->start_date;
        $endDate = $parameters['end_date'] ?? $this->currentPeriod->end_date;

        $revenues = $this->getAccountBalancesByTypeAndPeriod($chart, 'REVENUE', $startDate, $endDate);
        $expenses = $this->getAccountBalancesByTypeAndPeriod($chart, 'EXPENSE', $startDate, $endDate);
        $cogs = $this->getAccountBalancesByTypeAndPeriod($chart, 'COST_OF_GOODS_SOLD', $startDate, $endDate);

        $totalRevenues = collect($revenues)->sum('balance');
        $totalExpenses = collect($expenses)->sum('balance');
        $totalCogs = collect($cogs)->sum('balance');

        $grossProfit = $totalRevenues - $totalCogs;
        $netIncome = $grossProfit - $totalExpenses;

        return [
            'revenues' => $revenues,
            'cost_of_goods_sold' => $cogs,
            'expenses' => $expenses,
            'totals' => [
                'total_revenues' => $totalRevenues,
                'total_cogs' => $totalCogs,
                'gross_profit' => $grossProfit,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
            'chart_info' => [
                'name' => $chart->name,
                'standard' => $chart->standard,
            ],
        ];
    }

    /**
     * الحصول على أرصدة الحسابات حسب النوع والفترة
     */
    protected function getAccountBalancesByTypeAndPeriod(ChartOfAccounts $chart, string $accountType, $startDate, $endDate): array
    {
        $accounts = $chart->accounts()
            ->where('account_type', $accountType)
            ->with(['journalEntries' => function ($q) use ($startDate, $endDate) {
                $q->whereBetween('entry_date', [$startDate, $endDate])
                  ->where('status', 'POSTED');
            }])
            ->get();

        $balances = [];
        foreach ($accounts as $account) {
            $debits = $account->journalEntries->sum('debit_amount');
            $credits = $account->journalEntries->sum('credit_amount');
            
            // للإيرادات: الرصيد = الدائن - المدين
            // للمصروفات: الرصيد = المدين - الدائن
            $balance = $accountType === 'REVENUE' ? $credits - $debits : $debits - $credits;
            
            if ($balance != 0) {
                $balances[] = [
                    'account_code' => $account->code,
                    'account_name' => $account->getLocalizedName(),
                    'balance' => $balance,
                ];
            }
        }

        return $balances;
    }

    /**
     * توليد قائمة التدفقات النقدية
     */
    protected function generateCashFlowStatement(ChartOfAccounts $chart, array $parameters): array
    {
        // تنفيذ مبسط لقائمة التدفقات النقدية
        // يمكن تطويرها أكثر حسب المتطلبات
        
        return [
            'operating_activities' => [],
            'investing_activities' => [],
            'financing_activities' => [],
            'net_cash_flow' => 0,
        ];
    }
}
