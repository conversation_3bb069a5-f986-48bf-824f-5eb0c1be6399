// Dashboard JavaScript - Advanced Multi-Level Dashboard System

class DashboardManager {
    constructor() {
        this.widgets = new Map();
        this.layouts = new Map();
        this.currentLayout = null;
        this.isCustomizing = false;
        this.searchIndex = new Map();
        this.notifications = [];
        this.aiAssistant = null;
        
        this.init();
    }

    init() {
        this.initSidebar();
        this.initSearch();
        this.initNotifications();
        this.initWidgetSystem();
        this.initAIAssistant();
        this.initCommandMode();
        this.initKeyboardShortcuts();
        this.initRealTimeUpdates();
        this.buildSearchIndex();
    }

    // ========== Sidebar Management ==========
    initSidebar() {
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');

        sidebarToggle.addEventListener('click', () => {
            const isCollapsed = sidebar.classList.contains('sidebar-collapsed');
            
            if (isCollapsed) {
                sidebar.classList.remove('sidebar-collapsed');
                sidebar.classList.add('sidebar-expanded');
                mainContent.classList.remove('mr-80');
                mainContent.classList.add('mr-280');
                
                // إظهار النصوص
                document.querySelectorAll('.sidebar-text').forEach(el => {
                    el.style.display = 'block';
                });
            } else {
                sidebar.classList.remove('sidebar-expanded');
                sidebar.classList.add('sidebar-collapsed');
                mainContent.classList.remove('mr-280');
                mainContent.classList.add('mr-80');
                
                // إخفاء النصوص
                document.querySelectorAll('.sidebar-text').forEach(el => {
                    el.style.display = 'none';
                });
            }
        });
    }

    // ========== Global Search System ==========
    initSearch() {
        const searchInput = document.getElementById('global-search');
        const searchResults = document.getElementById('search-results');
        let searchTimeout;

        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.add('hidden');
            }
        });
    }

    buildSearchIndex() {
        // بناء فهرس البحث للبيانات المختلفة
        this.searchIndex.set('invoices', [
            { id: 'INV-2024-001', title: 'فاتورة شركة ABC', type: 'invoice', status: 'overdue' },
            { id: 'INV-2024-002', title: 'فاتورة مشروع XYZ', type: 'invoice', status: 'paid' },
        ]);

        this.searchIndex.set('projects', [
            { id: 'PRJ-001', title: 'مشروع تطوير الموقع', type: 'project', status: 'active' },
            { id: 'PRJ-002', title: 'مشروع التطبيق المحمول', type: 'project', status: 'completed' },
        ]);

        this.searchIndex.set('customers', [
            { id: 'CUST-001', title: 'شركة ABC للتجارة', type: 'customer', status: 'active' },
            { id: 'CUST-002', title: 'مؤسسة XYZ للخدمات', type: 'customer', status: 'active' },
        ]);

        this.searchIndex.set('employees', [
            { id: 'EMP-001', title: 'أحمد محمد - مطور', type: 'employee', status: 'active' },
            { id: 'EMP-002', title: 'فاطمة علي - محاسبة', type: 'employee', status: 'active' },
        ]);
    }

    performSearch(query) {
        const results = [];
        const searchResults = document.getElementById('search-results');

        // البحث في جميع الفهارس
        this.searchIndex.forEach((items, category) => {
            items.forEach(item => {
                if (item.title.toLowerCase().includes(query.toLowerCase()) ||
                    item.id.toLowerCase().includes(query.toLowerCase())) {
                    results.push(item);
                }
            });
        });

        // عرض النتائج
        if (results.length > 0) {
            searchResults.innerHTML = this.renderSearchResults(results);
            searchResults.classList.remove('hidden');
        } else {
            searchResults.innerHTML = '<div class="p-4 text-center text-gray-500">لا توجد نتائج</div>';
            searchResults.classList.remove('hidden');
        }
    }

    renderSearchResults(results) {
        const typeIcons = {
            'invoice': 'fas fa-file-invoice',
            'project': 'fas fa-project-diagram',
            'customer': 'fas fa-user-tie',
            'employee': 'fas fa-user'
        };

        const typeLabels = {
            'invoice': 'فاتورة',
            'project': 'مشروع',
            'customer': 'عميل',
            'employee': 'موظف'
        };

        return results.map(result => `
            <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
                 onclick="navigateToResult('${result.type}', '${result.id}')">
                <div class="flex items-center">
                    <i class="${typeIcons[result.type]} text-purple-600 mr-3"></i>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">${result.title}</p>
                        <p class="text-sm text-gray-500">${typeLabels[result.type]} - ${result.id}</p>
                    </div>
                    <span class="text-xs px-2 py-1 rounded-full ${this.getStatusColor(result.status)}">
                        ${this.getStatusLabel(result.status)}
                    </span>
                </div>
            </div>
        `).join('');
    }

    getStatusColor(status) {
        const colors = {
            'active': 'bg-green-100 text-green-800',
            'completed': 'bg-blue-100 text-blue-800',
            'overdue': 'bg-red-100 text-red-800',
            'paid': 'bg-green-100 text-green-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    getStatusLabel(status) {
        const labels = {
            'active': 'نشط',
            'completed': 'مكتمل',
            'overdue': 'متأخر',
            'paid': 'مدفوع'
        };
        return labels[status] || status;
    }

    // ========== Notification System ==========
    initNotifications() {
        const notificationsBtn = document.getElementById('notifications-btn');
        
        notificationsBtn.addEventListener('click', () => {
            this.showNotificationsPanel();
        });

        // تحديث الإشعارات كل دقيقة
        setInterval(() => {
            this.fetchNotifications();
        }, 60000);
    }

    showNotificationsPanel() {
        const modal = document.getElementById('notifications-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.renderNotifications();
        }
    }

    renderNotifications() {
        const container = document.getElementById('notifications-container');
        if (!container) return;

        const notifications = window.notifications.recent || [];
        
        container.innerHTML = notifications.map(notification => `
            <div class="p-4 border-b border-gray-200 hover:bg-gray-50 ${notification.is_critical ? 'border-l-4 border-red-500' : ''}">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="${this.getNotificationIcon(notification.type)} ${notification.is_critical ? 'text-red-500' : 'text-blue-500'}"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                        <p class="text-sm text-gray-600 mt-1">${notification.message}</p>
                        <p class="text-xs text-gray-400 mt-2">${this.formatTime(notification.created_at)}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <button class="text-gray-400 hover:text-gray-600" onclick="dismissNotification(${notification.id})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getNotificationIcon(type) {
        const icons = {
            'invoice_overdue': 'fas fa-exclamation-triangle',
            'project_milestone': 'fas fa-flag-checkered',
            'support_ticket': 'fas fa-headset',
            'system_alert': 'fas fa-bell'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    // ========== Widget System ==========
    initWidgetSystem() {
        this.initDragAndDrop();
        this.initWidgetCustomization();
        this.loadWidgetData();
    }

    initDragAndDrop() {
        const grid = document.getElementById('dashboard-grid');
        
        if (grid) {
            new Sortable(grid, {
                animation: 150,
                ghostClass: 'widget-dragging',
                onEnd: (evt) => {
                    this.saveLayoutChanges();
                }
            });
        }
    }

    initWidgetCustomization() {
        const customizeBtn = document.getElementById('customize-dashboard-btn');
        
        customizeBtn.addEventListener('click', () => {
            this.toggleCustomizationMode();
        });
    }

    toggleCustomizationMode() {
        this.isCustomizing = !this.isCustomizing;
        const widgets = document.querySelectorAll('.widget');
        
        if (this.isCustomizing) {
            widgets.forEach(widget => {
                widget.classList.add('customizing');
                this.addCustomizationControls(widget);
            });
            this.showCustomizationToolbar();
        } else {
            widgets.forEach(widget => {
                widget.classList.remove('customizing');
                this.removeCustomizationControls(widget);
            });
            this.hideCustomizationToolbar();
        }
    }

    addCustomizationControls(widget) {
        const controls = document.createElement('div');
        controls.className = 'widget-controls absolute top-2 left-2 bg-white rounded-lg shadow-lg p-2 flex space-x-2';
        controls.innerHTML = `
            <button class="text-blue-600 hover:text-blue-800" onclick="resizeWidget(this)">
                <i class="fas fa-expand-arrows-alt"></i>
            </button>
            <button class="text-green-600 hover:text-green-800" onclick="configureWidget(this)">
                <i class="fas fa-cog"></i>
            </button>
            <button class="text-red-600 hover:text-red-800" onclick="removeWidget(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        widget.style.position = 'relative';
        widget.appendChild(controls);
    }

    // ========== AI Assistant ==========
    initAIAssistant() {
        const aiBtn = document.getElementById('ai-assistant-btn');
        
        aiBtn.addEventListener('click', () => {
            this.showAIAssistant();
        });
    }

    showAIAssistant() {
        const modal = document.getElementById('ai-assistant-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.getElementById('ai-input').focus();
        }
    }

    processAIQuery(query) {
        // محاكاة معالجة الاستعلام بالذكاء الاصطناعي
        const responses = {
            'فواتير غير مدفوعة': 'يوجد 5 فواتير غير مدفوعة بقيمة إجمالية 125,000 ر.س',
            'إجمالي الدخل': 'إجمالي الدخل لهذا الربع هو 2,500,000 ر.س بزيادة 12.5% عن الربع السابق',
            'المشاريع المتأخرة': 'يوجد 3 مشاريع متأخرة عن الجدول الزمني المحدد'
        };

        return responses[query] || 'عذراً، لم أتمكن من فهم استفسارك. يرجى المحاولة مرة أخرى.';
    }

    // ========== Command Mode ==========
    initCommandMode() {
        const commandBtn = document.getElementById('command-mode-btn');
        
        commandBtn.addEventListener('click', () => {
            this.enterCommandMode();
        });
    }

    enterCommandMode() {
        const body = document.body;
        body.classList.add('command-mode');
        
        // إنشاء واجهة وضع القيادة
        const commandInterface = document.createElement('div');
        commandInterface.id = 'command-interface';
        commandInterface.className = 'command-mode bg-black text-white p-8';
        commandInterface.innerHTML = this.renderCommandModeInterface();
        
        body.appendChild(commandInterface);
        
        // إضافة مستمع للخروج من الوضع
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.exitCommandMode();
            }
        });
    }

    renderCommandModeInterface() {
        return `
            <div class="h-full flex flex-col">
                <div class="flex justify-between items-center mb-8">
                    <h1 class="text-4xl font-bold">وضع القيادة - حسابي AI</h1>
                    <button onclick="dashboardManager.exitCommandMode()" class="text-white hover:text-gray-300">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 flex-1">
                    <!-- KPIs الرئيسية -->
                    <div class="bg-gray-900 rounded-lg p-6">
                        <h2 class="text-2xl font-bold mb-6 text-green-400">مؤشرات الأداء</h2>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span>الإيرادات:</span>
                                <span class="text-green-400 font-bold">2.5M ر.س</span>
                            </div>
                            <div class="flex justify-between">
                                <span>صافي الربح:</span>
                                <span class="text-blue-400 font-bold">450K ر.س</span>
                            </div>
                            <div class="flex justify-between">
                                <span>المشاريع النشطة:</span>
                                <span class="text-purple-400 font-bold">12</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- خريطة النشاط -->
                    <div class="bg-gray-900 rounded-lg p-6">
                        <h2 class="text-2xl font-bold mb-6 text-blue-400">خريطة النشاط</h2>
                        <div id="activity-heatmap" class="h-64 bg-gray-800 rounded"></div>
                    </div>
                    
                    <!-- المهام العاجلة -->
                    <div class="bg-gray-900 rounded-lg p-6">
                        <h2 class="text-2xl font-bold mb-6 text-red-400">المهام العاجلة</h2>
                        <div class="space-y-3">
                            <div class="bg-red-900 p-3 rounded">
                                <p class="font-semibold">فاتورة متأخرة</p>
                                <p class="text-sm text-gray-300">INV-2024-001</p>
                            </div>
                            <div class="bg-yellow-900 p-3 rounded">
                                <p class="font-semibold">مراجعة مشروع</p>
                                <p class="text-sm text-gray-300">PRJ-001</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    exitCommandMode() {
        const commandInterface = document.getElementById('command-interface');
        if (commandInterface) {
            commandInterface.remove();
        }
        document.body.classList.remove('command-mode');
    }

    // ========== Keyboard Shortcuts ==========
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K للبحث
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('global-search').focus();
            }
            
            // Ctrl/Cmd + D لوضع القيادة
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.enterCommandMode();
            }
            
            // Ctrl/Cmd + N للإشعارات
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.showNotificationsPanel();
            }
        });
    }

    // ========== Real-time Updates ==========
    initRealTimeUpdates() {
        // محاكاة التحديثات الفورية
        setInterval(() => {
            this.updateWidgetData();
        }, 30000); // كل 30 ثانية
    }

    updateWidgetData() {
        // تحديث بيانات الودجات
        const widgets = document.querySelectorAll('.widget');
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget-type');
            if (widgetType) {
                this.refreshWidgetData(widgetType);
            }
        });
    }

    refreshWidgetData(widgetType) {
        // تحديث بيانات ودجت معين
        console.log(`Refreshing widget: ${widgetType}`);
    }

    // ========== Utility Functions ==========
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'الآن';
        if (diff < 3600000) return `${Math.floor(diff / 60000)} دقيقة`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)} ساعة`;
        return date.toLocaleDateString('ar-SA');
    }

    saveLayoutChanges() {
        // حفظ تغييرات التخطيط
        console.log('Saving layout changes...');
    }

    loadWidgetData() {
        // تحميل بيانات الودجات
        console.log('Loading widget data...');
    }
}

// تهيئة مدير لوحة التحكم
let dashboardManager;

document.addEventListener('DOMContentLoaded', function() {
    dashboardManager = new DashboardManager();
});

// دوال مساعدة عامة
function navigateToResult(type, id) {
    console.log(`Navigating to ${type}: ${id}`);
    // تنفيذ التنقل
}

function dismissNotification(id) {
    console.log(`Dismissing notification: ${id}`);
    // تنفيذ إخفاء الإشعار
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 left-4 z-50 p-4 rounded-lg text-white ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
    }
}
