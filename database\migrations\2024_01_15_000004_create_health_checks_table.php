<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('health_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_gateway_id')->constrained()->onDelete('cascade');
            $table->foreignId('api_endpoint_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('check_type'); // connectivity, authentication, response_time, upstream, dependencies
            $table->string('status'); // healthy, degraded, unhealthy
            $table->integer('response_time_ms')->nullable();
            $table->integer('status_code')->nullable();
            $table->text('response_body')->nullable();
            $table->text('error_message')->nullable();
            $table->json('check_config')->nullable();
            $table->json('metrics')->nullable();
            $table->json('alerts_triggered')->nullable();
            $table->timestamp('checked_at');
            $table->timestamp('next_check_at')->nullable();
            $table->timestamps();

            $table->index(['api_gateway_id', 'checked_at']);
            $table->index(['api_endpoint_id', 'checked_at']);
            $table->index(['status', 'checked_at']);
            $table->index(['check_type', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('health_checks');
    }
};
