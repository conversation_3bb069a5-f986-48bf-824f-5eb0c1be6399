/* Custom CSS for Hesabiai */

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', sans-serif;
}

/* Gradient Backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.gradient-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Smooth Scrolling */
.scroll-smooth {
    scroll-behavior: smooth;
}

/* Animations */
.animate-float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top.show {
    opacity: 1;
}

.back-to-top:hover {
    transform: translateY(-2px);
}

/* Step Indicators */
.step {
    display: none;
}

.step.active {
    display: block;
}

.step-indicator {
    background: #e5e7eb;
    transition: all 0.3s ease;
}

.step-indicator.active {
    background: #8b5cf6;
    transform: scale(1.1);
}

.step-indicator.completed {
    background: #10b981;
}

/* Form Enhancements */
.form-input {
    transition: all 0.3s ease;
}

.form-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(107, 114, 128, 0.3);
}

/* Pricing Cards */
.pricing-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.pricing-card.popular {
    border: 2px solid #8b5cf6;
    transform: scale(1.05);
}

.pricing-card.popular::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.service-card:hover {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Testimonial Cards */
.testimonial-card {
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #8b5cf6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Enhancements */
.modal-backdrop {
    backdrop-filter: blur(5px);
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .pricing-card.popular {
        transform: none;
    }
    
    .service-card {
        margin-bottom: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .card {
        background-color: #374151;
        border-color: #4b5563;
    }
    
    .dark-mode .text-gray-600 {
        color: #d1d5db;
    }
    
    .dark-mode .text-gray-800 {
        color: #f9fafb;
    }
}

/* RTL Support Enhancements */
[dir="rtl"] .animate-slide-in-right {
    animation: slideInLeft 0.5s ease-out;
}

[dir="rtl"] .animate-slide-in-left {
    animation: slideInRight 0.5s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #8b5cf6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #7c3aed;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}
