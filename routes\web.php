<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Pricing routes
Route::get('/pricing/compare', function () {
    return view('pricing.compare');
})->name('pricing.compare');

// Authentication routes
Route::get('/login', [App\Http\Controllers\Auth\AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [App\Http\Controllers\Auth\AuthController::class, 'login']);
Route::get('/register', [App\Http\Controllers\Auth\AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [App\Http\Controllers\Auth\AuthController::class, 'register']);
Route::post('/logout', [App\Http\Controllers\Auth\AuthController::class, 'logout'])->name('logout');

// Admin setup route
Route::get('/setup-admin', [App\Http\Controllers\Auth\AuthController::class, 'createAdminUser']);

// Dashboard routes (protected)
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::post('/dashboard/layout', [App\Http\Controllers\DashboardController::class, 'saveLayout'])->name('dashboard.layout.save');
    Route::get('/dashboard/widgets', [App\Http\Controllers\DashboardController::class, 'getAvailableWidgets'])->name('dashboard.widgets');
    Route::post('/dashboard/widget/add', [App\Http\Controllers\DashboardController::class, 'addWidget'])->name('dashboard.widget.add');
    Route::delete('/dashboard/widget/{id}', [App\Http\Controllers\DashboardController::class, 'removeWidget'])->name('dashboard.widget.remove');
    Route::put('/dashboard/widget/{id}', [App\Http\Controllers\DashboardController::class, 'updateWidget'])->name('dashboard.widget.update');

    // API routes for dashboard data
    Route::prefix('api/dashboard')->group(function () {
        Route::get('/kpis', [App\Http\Controllers\DashboardController::class, 'getKPIs'])->name('api.dashboard.kpis');
        Route::get('/notifications', [App\Http\Controllers\DashboardController::class, 'getNotifications'])->name('api.dashboard.notifications');
        Route::get('/search', [App\Http\Controllers\DashboardController::class, 'search'])->name('api.dashboard.search');
        Route::post('/ai-query', [App\Http\Controllers\DashboardController::class, 'processAIQuery'])->name('api.dashboard.ai');
    });
});

// تضمين طرق نظام إدارة المشاريع
require __DIR__.'/projects.php';
