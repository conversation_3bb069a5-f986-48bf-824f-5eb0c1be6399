<?php

namespace App\Domains\Compliance\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * مورد نشاط الامتثال
 */
class ComplianceActivityResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'activity_id' => $this->activity_id,
            'title' => $this->title,
            'description' => $this->description,
            'activity_type' => $this->activity_type,
            'activity_type_label' => $this->activity_type ? 
                \App\Domains\Compliance\Models\ComplianceActivity::ACTIVITY_TYPES[$this->activity_type] : null,
            'activity_category' => $this->activity_category,
            'activity_category_label' => $this->activity_category ? 
                \App\Domains\Compliance\Models\ComplianceActivity::ACTIVITY_CATEGORIES[$this->activity_category] : null,
            'status' => $this->status,
            'status_label' => $this->status ? 
                \App\Domains\Compliance\Models\ComplianceActivity::STATUSES[$this->status] : null,
            'priority' => $this->priority,
            'priority_label' => $this->priority ? 
                \App\Domains\Compliance\Models\ComplianceActivity::PRIORITIES[$this->priority] : null,
            'risk_level' => $this->risk_level,
            'risk_level_label' => $this->risk_level ? 
                \App\Domains\Compliance\Models\ComplianceActivity::RISK_LEVELS[$this->risk_level] : null,
            'automation_level' => $this->automation_level,
            'automation_level_label' => $this->automation_level ? 
                \App\Domains\Compliance\Models\ComplianceActivity::AUTOMATION_LEVELS[$this->automation_level] : null,
            
            // التواريخ والمواعيد
            'due_date' => $this->due_date?->format('Y-m-d H:i:s'),
            'completed_at' => $this->completed_at?->format('Y-m-d H:i:s'),
            'days_remaining' => $this->getDaysRemaining(),
            'days_overdue' => $this->getDaysOverdue(),
            'is_overdue' => $this->isOverdue(),
            
            // التأثير والنقاط
            'compliance_score_impact' => $this->calculateComplianceImpact(),
            'escalation_level' => $this->escalation_level,
            'notification_sent' => $this->notification_sent,
            
            // العلاقات
            'company' => $this->whenLoaded('company', function () {
                return [
                    'id' => $this->company->id,
                    'name' => $this->company->name,
                ];
            }),
            
            'country' => $this->whenLoaded('country', function () {
                return [
                    'id' => $this->country->id,
                    'name_ar' => $this->country->name_ar,
                    'code' => $this->country->code,
                ];
            }),
            
            'compliance_rule' => $this->whenLoaded('complianceRule', function () {
                return [
                    'id' => $this->complianceRule->id,
                    'rule_name_ar' => $this->complianceRule->rule_name_ar,
                    'rule_category' => $this->complianceRule->rule_category,
                ];
            }),
            
            'assigned_user' => $this->whenLoaded('assignedUser', function () {
                return [
                    'id' => $this->assignedUser->id,
                    'name' => $this->assignedUser->name,
                    'email' => $this->assignedUser->email,
                ];
            }),
            
            'reviewer' => $this->whenLoaded('reviewer', function () {
                return [
                    'id' => $this->reviewer->id,
                    'name' => $this->reviewer->name,
                ];
            }),
            
            'approver' => $this->whenLoaded('approver', function () {
                return [
                    'id' => $this->approver->id,
                    'name' => $this->approver->name,
                ];
            }),

            // البيانات المفصلة (فقط عند الطلب)
            $this->mergeWhen($request->get('include_details'), [
                'activity_data' => $this->activity_data,
                'resolution_notes' => $this->resolution_notes,
                'attachments' => $this->attachments,
                'metadata' => $this->metadata,
                'related_model_type' => $this->related_model_type,
                'related_model_id' => $this->related_model_id,
            ]),

            // السجل والتاريخ (فقط عند الطلب)
            $this->mergeWhen($request->get('include_history'), [
                'status_history' => $this->getStatusHistory(),
                'escalations' => $this->getEscalations(),
                'notes' => $this->getNotes(),
            ]),

            // التواريخ
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * الحصول على تاريخ الحالات
     */
    protected function getStatusHistory(): array
    {
        $metadata = $this->metadata ?? [];
        return $metadata['status_history'] ?? [];
    }

    /**
     * الحصول على التصعيدات
     */
    protected function getEscalations(): array
    {
        $metadata = $this->metadata ?? [];
        return $metadata['escalations'] ?? [];
    }

    /**
     * الحصول على الملاحظات
     */
    protected function getNotes(): array
    {
        $metadata = $this->metadata ?? [];
        return $metadata['notes'] ?? [];
    }
}
