<?php

namespace App\Domains\ECommerce\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب تحديث تكامل موجود
 */
class UpdateIntegrationRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return true; // يمكن تخصيص هذا حسب نظام الصلاحيات
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        $integrationId = $this->route('integration')->id ?? null;

        return [
            'name' => 'sometimes|required|string|max:255',
            'authentication_config' => 'sometimes|required|array',
            'sync_config' => 'sometimes|array',
            'webhook_config' => 'sometimes|array',
            'status' => 'sometimes|in:active,inactive,pending',
            'description' => 'sometimes|nullable|string|max:1000',
            
            // إعدادات المزامنة
            'sync_config.auto_sync' => 'sometimes|boolean',
            'sync_config.sync_interval' => 'sometimes|integer|min:5|max:1440',
            'sync_config.sync_products' => 'sometimes|boolean',
            'sync_config.sync_orders' => 'sometimes|boolean',
            'sync_config.sync_customers' => 'sometimes|boolean',
            'sync_config.sync_categories' => 'sometimes|boolean',
            'sync_config.sync_inventory' => 'sometimes|boolean',
            'sync_config.batch_size' => 'sometimes|integer|min:1|max:1000',
            
            // إعدادات Webhooks
            'webhook_config.enabled' => 'sometimes|boolean',
            'webhook_config.events' => 'sometimes|array',
            'webhook_config.events.*' => 'string',
            'webhook_config.secret' => 'sometimes|nullable|string',
            'webhook_config.url' => 'sometimes|nullable|url',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'name.required' => 'اسم التكامل مطلوب',
            'name.max' => 'اسم التكامل يجب ألا يتجاوز 255 حرف',
            'authentication_config.required' => 'إعدادات المصادقة مطلوبة',
            'status.in' => 'حالة التكامل يجب أن تكون: نشط، غير نشط، أو في الانتظار',
            'description.max' => 'الوصف يجب ألا يتجاوز 1000 حرف',
            'sync_config.sync_interval.min' => 'فترة المزامنة يجب أن تكون على الأقل 5 دقائق',
            'sync_config.sync_interval.max' => 'فترة المزامنة يجب ألا تتجاوز 1440 دقيقة (24 ساعة)',
            'sync_config.batch_size.min' => 'حجم الدفعة يجب أن يكون على الأقل 1',
            'sync_config.batch_size.max' => 'حجم الدفعة يجب ألا يتجاوز 1000',
            'webhook_config.url.url' => 'رابط Webhook يجب أن يكون صحيحاً',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'name' => 'اسم التكامل',
            'authentication_config' => 'إعدادات المصادقة',
            'sync_config' => 'إعدادات المزامنة',
            'webhook_config' => 'إعدادات Webhooks',
            'status' => 'الحالة',
            'description' => 'الوصف',
        ];
    }

    /**
     * التحقق من صحة البيانات بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $integration = $this->route('integration');
            
            // التحقق من إمكانية تحديث التكامل
            if ($integration && $integration->status === 'syncing') {
                $validator->errors()->add('status', 'لا يمكن تحديث التكامل أثناء المزامنة');
            }

            // التحقق من صحة إعدادات المصادقة إذا تم تحديثها
            if ($this->has('authentication_config') && $integration) {
                $this->validateAuthenticationConfig($validator, $integration);
            }
        });
    }

    /**
     * التحقق من إعدادات المصادقة
     */
    protected function validateAuthenticationConfig($validator, $integration): void
    {
        $config = $this->authentication_config;
        $platform = $integration->platform->slug;

        // التحقق من الحقول المطلوبة حسب المنصة
        $requiredFields = $this->getRequiredFieldsForPlatform($platform);
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                $validator->errors()->add(
                    "authentication_config.{$field}",
                    "الحقل {$field} مطلوب لمنصة {$platform}"
                );
            }
        }

        // التحقق من صحة البيانات حسب المنصة
        $this->validatePlatformSpecificConfig($validator, $platform, $config);
    }

    /**
     * الحصول على الحقول المطلوبة لكل منصة
     */
    protected function getRequiredFieldsForPlatform(string $platform): array
    {
        return match ($platform) {
            'shopify' => ['access_token', 'shop_domain'],
            'woocommerce' => ['consumer_key', 'consumer_secret', 'base_url'],
            'salla' => ['access_token'],
            'zid' => ['access_token', 'manager_token'],
            'youcan' => ['access_token', 'store_id'],
            'noon' => ['access_token', 'seller_id'],
            'jumia' => ['api_key', 'user_id', 'country'],
            'amazon' => ['access_token', 'marketplace_id', 'seller_id', 'region'],
            'ebay' => ['access_token', 'marketplace_id'],
            'magento' => ['access_token', 'base_url'],
            'bigcommerce' => ['access_token', 'store_hash'],
            default => ['access_token'],
        };
    }

    /**
     * التحقق من إعدادات المنصة المحددة
     */
    protected function validatePlatformSpecificConfig($validator, string $platform, array $config): void
    {
        switch ($platform) {
            case 'shopify':
                if (isset($config['shop_domain']) && !preg_match('/^[a-zA-Z0-9-]+\.myshopify\.com$/', $config['shop_domain'])) {
                    $validator->errors()->add('authentication_config.shop_domain', 'نطاق Shopify غير صحيح');
                }
                break;

            case 'woocommerce':
                if (isset($config['base_url']) && !filter_var($config['base_url'], FILTER_VALIDATE_URL)) {
                    $validator->errors()->add('authentication_config.base_url', 'رابط الموقع غير صحيح');
                }
                break;

            case 'amazon':
                if (isset($config['region']) && !in_array($config['region'], ['us-east-1', 'eu-west-1', 'us-west-2'])) {
                    $validator->errors()->add('authentication_config.region', 'المنطقة غير مدعومة');
                }
                break;

            case 'jumia':
                if (isset($config['country']) && !in_array($config['country'], ['eg', 'ma', 'ng', 'ke', 'ug'])) {
                    $validator->errors()->add('authentication_config.country', 'الدولة غير مدعومة في Jumia');
                }
                break;

            case 'ebay':
                if (isset($config['environment']) && !in_array($config['environment'], ['production', 'sandbox'])) {
                    $validator->errors()->add('authentication_config.environment', 'البيئة يجب أن تكون production أو sandbox');
                }
                break;
        }
    }
}
