<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if not exists
        $adminEmail = '<EMAIL>';
        
        if (!User::where('email', $adminEmail)->exists()) {
            User::create([
                'name' => 'Said Admin',
                'username' => 'said_admin',
                'email' => $adminEmail,
                'password' => Hash::make('Colorado2020@'),
                'account_type' => 'enterprise',
                'country' => 'SA',
                'city' => 'Riyadh',
                'currency' => 'ريال سعودي (SAR)',
                'company_name' => 'Hesabiai Company',
                'is_admin' => true,
                'subscription_plan' => 'enterprise',
                'email_verified_at' => now(),
                'is_active' => true,
            ]);

            $this->command->info('Admin user created successfully!');
        } else {
            $this->command->info('Admin user already exists.');
        }
    }
}
