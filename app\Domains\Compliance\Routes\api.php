<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Compliance\Controllers\ComplianceRuleController;
use App\Domains\Compliance\Controllers\ComplianceActivityController;
use App\Domains\Compliance\Controllers\ComplianceAlertController;
use App\Domains\Compliance\Controllers\GovernmentIntegrationController;
use App\Domains\Compliance\Controllers\CountryDashboardController;
use App\Domains\Compliance\Controllers\ComplianceDashboardController;
use App\Domains\Compliance\Controllers\ComplianceReportController;

/*
|--------------------------------------------------------------------------
| Compliance API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام الامتثال
|
*/

Route::prefix('compliance')->middleware(['auth:sanctum'])->group(function () {
    
    // ========== لوحات التحكم ==========
    Route::prefix('dashboards')->group(function () {
        // لوحة التحكم الرئيسية
        Route::get('/', [ComplianceDashboardController::class, 'index']);
        Route::get('/metrics', [ComplianceDashboardController::class, 'getMetrics']);
        Route::get('/summary', [ComplianceDashboardController::class, 'getSummary']);
        
        // لوحات التحكم المخصصة للدول
        Route::get('/countries/{country}', [CountryDashboardController::class, 'show']);
        Route::get('/morocco', [CountryDashboardController::class, 'moroccanDashboard']);
        Route::get('/saudi-arabia', [CountryDashboardController::class, 'saudiDashboard']);
        Route::get('/uae', [CountryDashboardController::class, 'uaeDashboard']);
        Route::get('/kuwait', [CountryDashboardController::class, 'kuwaitDashboard']);
        Route::get('/qatar', [CountryDashboardController::class, 'qatarDashboard']);
        Route::get('/jordan', [CountryDashboardController::class, 'jordanDashboard']);
        Route::get('/egypt', [CountryDashboardController::class, 'egyptDashboard']);
        Route::get('/tunisia', [CountryDashboardController::class, 'tunisiaDashboard']);
        Route::get('/algeria', [CountryDashboardController::class, 'algeriaDashboard']);
    });

    // ========== قواعد الامتثال ==========
    Route::prefix('rules')->group(function () {
        Route::get('/', [ComplianceRuleController::class, 'index']);
        Route::post('/', [ComplianceRuleController::class, 'store']);
        Route::get('/{rule}', [ComplianceRuleController::class, 'show']);
        Route::put('/{rule}', [ComplianceRuleController::class, 'update']);
        Route::delete('/{rule}', [ComplianceRuleController::class, 'destroy']);
        
        // عمليات خاصة
        Route::post('/{rule}/activate', [ComplianceRuleController::class, 'activate']);
        Route::post('/{rule}/deactivate', [ComplianceRuleController::class, 'deactivate']);
        Route::post('/{rule}/check-compliance', [ComplianceRuleController::class, 'checkCompliance']);
        Route::get('/{rule}/applicable-entities', [ComplianceRuleController::class, 'getApplicableEntities']);
        
        // التصفية والبحث
        Route::get('/category/{category}', [ComplianceRuleController::class, 'getByCategory']);
        Route::get('/country/{country}', [ComplianceRuleController::class, 'getByCountry']);
        Route::get('/search/{term}', [ComplianceRuleController::class, 'search']);
        Route::get('/statistics/overview', [ComplianceRuleController::class, 'getStatistics']);
    });

    // ========== أنشطة الامتثال ==========
    Route::prefix('activities')->group(function () {
        Route::get('/', [ComplianceActivityController::class, 'index']);
        Route::post('/', [ComplianceActivityController::class, 'store']);
        Route::get('/{activity}', [ComplianceActivityController::class, 'show']);
        Route::put('/{activity}', [ComplianceActivityController::class, 'update']);
        Route::delete('/{activity}', [ComplianceActivityController::class, 'destroy']);
        
        // إدارة الحالة
        Route::post('/{activity}/start', [ComplianceActivityController::class, 'start']);
        Route::post('/{activity}/complete', [ComplianceActivityController::class, 'complete']);
        Route::post('/{activity}/cancel', [ComplianceActivityController::class, 'cancel']);
        Route::post('/{activity}/escalate', [ComplianceActivityController::class, 'escalate']);
        Route::post('/{activity}/assign', [ComplianceActivityController::class, 'assign']);
        
        // المراجعة والاعتماد
        Route::post('/{activity}/review', [ComplianceActivityController::class, 'review']);
        Route::post('/{activity}/approve', [ComplianceActivityController::class, 'approve']);
        Route::post('/{activity}/reject', [ComplianceActivityController::class, 'reject']);
        
        // المرفقات والملاحظات
        Route::post('/{activity}/attachments', [ComplianceActivityController::class, 'addAttachment']);
        Route::delete('/{activity}/attachments/{attachment}', [ComplianceActivityController::class, 'removeAttachment']);
        Route::post('/{activity}/notes', [ComplianceActivityController::class, 'addNote']);
        
        // التصفية والبحث
        Route::get('/company/{company}', [ComplianceActivityController::class, 'getByCompany']);
        Route::get('/user/{user}', [ComplianceActivityController::class, 'getByUser']);
        Route::get('/status/{status}', [ComplianceActivityController::class, 'getByStatus']);
        Route::get('/overdue/list', [ComplianceActivityController::class, 'getOverdue']);
        Route::get('/due-today/list', [ComplianceActivityController::class, 'getDueToday']);
        Route::get('/statistics/overview', [ComplianceActivityController::class, 'getStatistics']);
    });

    // ========== تنبيهات الامتثال ==========
    Route::prefix('alerts')->group(function () {
        Route::get('/', [ComplianceAlertController::class, 'index']);
        Route::post('/', [ComplianceAlertController::class, 'store']);
        Route::get('/{alert}', [ComplianceAlertController::class, 'show']);
        Route::put('/{alert}', [ComplianceAlertController::class, 'update']);
        Route::delete('/{alert}', [ComplianceAlertController::class, 'destroy']);
        
        // إدارة التنبيهات
        Route::post('/{alert}/acknowledge', [ComplianceAlertController::class, 'acknowledge']);
        Route::post('/{alert}/resolve', [ComplianceAlertController::class, 'resolve']);
        Route::post('/{alert}/dismiss', [ComplianceAlertController::class, 'dismiss']);
        Route::post('/{alert}/escalate', [ComplianceAlertController::class, 'escalate']);
        Route::post('/{alert}/repeat', [ComplianceAlertController::class, 'repeat']);
        
        // التصفية والبحث
        Route::get('/active/list', [ComplianceAlertController::class, 'getActive']);
        Route::get('/critical/list', [ComplianceAlertController::class, 'getCritical']);
        Route::get('/company/{company}', [ComplianceAlertController::class, 'getByCompany']);
        Route::get('/type/{type}', [ComplianceAlertController::class, 'getByType']);
        Route::get('/statistics/overview', [ComplianceAlertController::class, 'getStatistics']);
    });

    // ========== التكاملات الحكومية ==========
    Route::prefix('integrations')->group(function () {
        Route::get('/', [GovernmentIntegrationController::class, 'index']);
        Route::post('/', [GovernmentIntegrationController::class, 'store']);
        Route::get('/{integration}', [GovernmentIntegrationController::class, 'show']);
        Route::put('/{integration}', [GovernmentIntegrationController::class, 'update']);
        Route::delete('/{integration}', [GovernmentIntegrationController::class, 'destroy']);
        
        // إدارة التكاملات
        Route::post('/{integration}/activate', [GovernmentIntegrationController::class, 'activate']);
        Route::post('/{integration}/deactivate', [GovernmentIntegrationController::class, 'deactivate']);
        Route::post('/{integration}/test', [GovernmentIntegrationController::class, 'test']);
        Route::get('/{integration}/health', [GovernmentIntegrationController::class, 'checkHealth']);
        
        // العمليات والإرسال
        Route::post('/{integration}/submit', [GovernmentIntegrationController::class, 'submit']);
        Route::get('/{integration}/operations', [GovernmentIntegrationController::class, 'getOperations']);
        Route::get('/{integration}/api-calls', [GovernmentIntegrationController::class, 'getApiCalls']);
        
        // التصفية والإحصائيات
        Route::get('/country/{country}', [GovernmentIntegrationController::class, 'getByCountry']);
        Route::get('/category/{category}', [GovernmentIntegrationController::class, 'getByCategory']);
        Route::get('/health/all', [GovernmentIntegrationController::class, 'checkAllHealth']);
        Route::get('/statistics/overview', [GovernmentIntegrationController::class, 'getStatistics']);
    });

    // ========== التقارير ==========
    Route::prefix('reports')->group(function () {
        Route::get('/compliance-summary', [ComplianceReportController::class, 'complianceSummary']);
        Route::get('/company/{company}/compliance', [ComplianceReportController::class, 'companyCompliance']);
        Route::get('/country/{country}/compliance', [ComplianceReportController::class, 'countryCompliance']);
        Route::get('/activities-report', [ComplianceReportController::class, 'activitiesReport']);
        Route::get('/alerts-report', [ComplianceReportController::class, 'alertsReport']);
        Route::get('/integrations-report', [ComplianceReportController::class, 'integrationsReport']);
        Route::get('/risk-assessment', [ComplianceReportController::class, 'riskAssessment']);
        Route::get('/automation-status', [ComplianceReportController::class, 'automationStatus']);
        
        // تصدير التقارير
        Route::post('/export/pdf', [ComplianceReportController::class, 'exportPDF']);
        Route::post('/export/excel', [ComplianceReportController::class, 'exportExcel']);
        Route::post('/export/csv', [ComplianceReportController::class, 'exportCSV']);
    });

    // ========== العمليات العامة ==========
    Route::prefix('operations')->group(function () {
        // فحص الامتثال
        Route::post('/check-compliance', [ComplianceDashboardController::class, 'checkCompliance']);
        Route::post('/company/{company}/check-compliance', [ComplianceDashboardController::class, 'checkCompanyCompliance']);
        
        // مراقبة المواعيد
        Route::post('/monitor-deadlines', [ComplianceDashboardController::class, 'monitorDeadlines']);
        Route::get('/upcoming-deadlines', [ComplianceDashboardController::class, 'getUpcomingDeadlines']);
        
        // التقديم للحكومة
        Route::post('/submit-to-government', [GovernmentIntegrationController::class, 'submitToGovernment']);
        Route::get('/submission-status/{submissionId}', [GovernmentIntegrationController::class, 'getSubmissionStatus']);
        
        // الأتمتة
        Route::post('/create-automatic-activities', [ComplianceActivityController::class, 'createAutomaticActivities']);
        Route::post('/process-automated-tasks', [ComplianceDashboardController::class, 'processAutomatedTasks']);
    });

    // ========== الإعدادات والتكوين ==========
    Route::prefix('settings')->group(function () {
        Route::get('/countries', [ComplianceDashboardController::class, 'getCountries']);
        Route::get('/rule-categories', [ComplianceRuleController::class, 'getCategories']);
        Route::get('/activity-types', [ComplianceActivityController::class, 'getActivityTypes']);
        Route::get('/alert-types', [ComplianceAlertController::class, 'getAlertTypes']);
        Route::get('/integration-types', [GovernmentIntegrationController::class, 'getIntegrationTypes']);
        
        // إعدادات الإشعارات
        Route::get('/notification-settings', [ComplianceDashboardController::class, 'getNotificationSettings']);
        Route::post('/notification-settings', [ComplianceDashboardController::class, 'updateNotificationSettings']);
    });
});
