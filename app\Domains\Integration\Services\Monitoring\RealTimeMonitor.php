<?php

namespace App\Domains\Integration\Services\Monitoring;

use App\Domains\Integration\Events\PerformanceThresholdExceeded;
use App\Domains\Integration\Events\SystemHealthDegraded;
use App\Domains\Integration\Events\AnomalyDetected;
use App\Domains\Integration\Services\Monitoring\Collectors\MetricsCollector;
use App\Domains\Integration\Services\Monitoring\Analyzers\PerformanceAnalyzer;
use App\Domains\Integration\Services\Monitoring\Analyzers\AnomalyDetector;
use App\Domains\Integration\Services\Monitoring\Alerting\AlertManager;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Advanced Real-Time Monitoring Service
 *
 * Features:
 * - Real-time metrics collection and analysis
 * - Performance monitoring with SLA tracking
 * - Anomaly detection using machine learning
 * - Predictive analytics for capacity planning
 * - Multi-dimensional alerting system
 * - Custom dashboards and visualizations
 * - Health scoring and trend analysis
 * - Resource utilization monitoring
 * - Business metrics tracking
 * - Compliance monitoring
 * - Distributed tracing correlation
 * - Auto-remediation triggers
 */
class RealTimeMonitor
{
    protected MetricsCollector $metricsCollector;
    protected PerformanceAnalyzer $performanceAnalyzer;
    protected AnomalyDetector $anomalyDetector;
    protected AlertManager $alertManager;
    protected array $config;
    protected array $activeMonitors;
    protected array $thresholds;

    public function __construct(
        MetricsCollector $metricsCollector,
        PerformanceAnalyzer $performanceAnalyzer,
        AnomalyDetector $anomalyDetector,
        AlertManager $alertManager
    ) {
        $this->metricsCollector = $metricsCollector;
        $this->performanceAnalyzer = $performanceAnalyzer;
        $this->anomalyDetector = $anomalyDetector;
        $this->alertManager = $alertManager;
        $this->config = config('integration.monitoring', []);
        $this->activeMonitors = [];
        $this->loadThresholds();
    }

    /**
     * Start monitoring a request
     */
    public function startRequestMonitoring(string $requestId, string $gatewayId): void
    {
        $monitoringContext = [
            'request_id' => $requestId,
            'gateway_id' => $gatewayId,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_cpu' => $this->getCpuUsage(),
            'metrics' => [],
            'checkpoints' => [],
        ];

        $this->activeMonitors[$requestId] = $monitoringContext;

        // Store in Redis for distributed monitoring
        Redis::setex(
            "monitor:request:{$requestId}",
            300, // 5 minutes TTL
            json_encode($monitoringContext)
        );

        // Start collecting baseline metrics
        $this->collectBaselineMetrics($requestId, $gatewayId);
    }

    /**
     * Add checkpoint during request processing
     */
    public function addCheckpoint(string $requestId, string $checkpoint, array $data = []): void
    {
        if (!isset($this->activeMonitors[$requestId])) {
            return;
        }

        $currentTime = microtime(true);
        $startTime = $this->activeMonitors[$requestId]['start_time'];

        $checkpointData = [
            'name' => $checkpoint,
            'timestamp' => $currentTime,
            'elapsed_time' => $currentTime - $startTime,
            'memory_usage' => memory_get_usage(true),
            'cpu_usage' => $this->getCpuUsage(),
            'data' => $data,
        ];

        $this->activeMonitors[$requestId]['checkpoints'][] = $checkpointData;

        // Update Redis
        Redis::setex(
            "monitor:request:{$requestId}",
            300,
            json_encode($this->activeMonitors[$requestId])
        );

        // Check for performance issues
        $this->checkPerformanceThresholds($requestId, $checkpointData);
    }

    /**
     * Finish monitoring a request
     */
    public function finishRequestMonitoring(string $requestId, float $totalTime): void
    {
        if (!isset($this->activeMonitors[$requestId])) {
            return;
        }

        $context = $this->activeMonitors[$requestId];
        $endTime = microtime(true);

        $finalMetrics = [
            'request_id' => $requestId,
            'gateway_id' => $context['gateway_id'],
            'total_time' => $totalTime,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_used' => memory_get_usage(true) - $context['start_memory'],
            'cpu_time' => $this->getCpuUsage() - $context['start_cpu'],
            'checkpoints' => $context['checkpoints'],
            'completed_at' => $endTime,
        ];

        // Store final metrics
        $this->metricsCollector->recordRequestMetrics($finalMetrics);

        // Analyze performance
        $this->performanceAnalyzer->analyzeRequest($finalMetrics);

        // Check for anomalies
        $this->detectAnomalies($finalMetrics);

        // Update aggregated metrics
        $this->updateAggregatedMetrics($finalMetrics);

        // Cleanup
        unset($this->activeMonitors[$requestId]);
        Redis::del("monitor:request:{$requestId}");
    }

    /**
     * Monitor system health continuously
     */
    public function monitorSystemHealth(): array
    {
        $healthMetrics = [
            'timestamp' => now(),
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'network_io' => $this->getNetworkIO(),
            'database_connections' => $this->getDatabaseConnections(),
            'redis_connections' => $this->getRedisConnections(),
            'queue_size' => $this->getQueueSize(),
            'active_requests' => count($this->activeMonitors),
            'error_rate' => $this->calculateErrorRate(),
            'response_time_p95' => $this->calculateResponseTimePercentile(95),
            'response_time_p99' => $this->calculateResponseTimePercentile(99),
        ];

        // Store metrics
        $this->storeSystemHealthMetrics($healthMetrics);

        // Check health thresholds
        $this->checkSystemHealthThresholds($healthMetrics);

        // Calculate health score
        $healthScore = $this->calculateHealthScore($healthMetrics);
        $healthMetrics['health_score'] = $healthScore;

        return $healthMetrics;
    }

    /**
     * Monitor business metrics
     */
    public function monitorBusinessMetrics(string $gatewayId): array
    {
        $businessMetrics = [
            'timestamp' => now(),
            'gateway_id' => $gatewayId,
            'total_requests_today' => $this->getTotalRequestsToday($gatewayId),
            'successful_requests_today' => $this->getSuccessfulRequestsToday($gatewayId),
            'failed_requests_today' => $this->getFailedRequestsToday($gatewayId),
            'revenue_impact' => $this->calculateRevenueImpact($gatewayId),
            'sla_compliance' => $this->calculateSlaCompliance($gatewayId),
            'customer_satisfaction' => $this->calculateCustomerSatisfaction($gatewayId),
            'api_adoption_rate' => $this->calculateApiAdoptionRate($gatewayId),
            'cost_per_request' => $this->calculateCostPerRequest($gatewayId),
        ];

        // Store business metrics
        $this->storeBusinessMetrics($businessMetrics);

        return $businessMetrics;
    }

    /**
     * Get real-time dashboard data
     */
    public function getDashboardData(string $gatewayId, string $timeRange = '1h'): array
    {
        $endTime = now();
        $startTime = $this->getStartTimeFromRange($timeRange);

        return [
            'overview' => $this->getOverviewMetrics($gatewayId, $startTime, $endTime),
            'performance' => $this->getPerformanceMetrics($gatewayId, $startTime, $endTime),
            'errors' => $this->getErrorMetrics($gatewayId, $startTime, $endTime),
            'traffic' => $this->getTrafficMetrics($gatewayId, $startTime, $endTime),
            'security' => $this->getSecurityMetrics($gatewayId, $startTime, $endTime),
            'business' => $this->getBusinessMetrics($gatewayId, $startTime, $endTime),
            'alerts' => $this->getActiveAlerts($gatewayId),
            'health_score' => $this->getCurrentHealthScore($gatewayId),
        ];
    }

    /**
     * Detect anomalies in metrics
     */
    protected function detectAnomalies(array $metrics): void
    {
        $anomalies = $this->anomalyDetector->detectAnomalies($metrics);

        foreach ($anomalies as $anomaly) {
            Event::dispatch(new AnomalyDetected(
                $metrics['request_id'],
                $anomaly['type'],
                $anomaly['severity'],
                $anomaly['details']
            ));

            // Trigger alert if severity is high
            if ($anomaly['severity'] === 'high' || $anomaly['severity'] === 'critical') {
                $this->alertManager->triggerAlert('anomaly_detected', $anomaly);
            }
        }
    }

    /**
     * Check performance thresholds
     */
    protected function checkPerformanceThresholds(string $requestId, array $checkpointData): void
    {
        $thresholds = $this->thresholds['performance'] ?? [];

        foreach ($thresholds as $metric => $threshold) {
            $value = $checkpointData[$metric] ?? null;

            if ($value !== null && $value > $threshold['critical']) {
                Event::dispatch(new PerformanceThresholdExceeded(
                    $requestId,
                    $metric,
                    $value,
                    $threshold['critical'],
                    'critical'
                ));

                $this->alertManager->triggerAlert('performance_threshold_exceeded', [
                    'request_id' => $requestId,
                    'metric' => $metric,
                    'value' => $value,
                    'threshold' => $threshold['critical'],
                    'severity' => 'critical',
                ]);
            } elseif ($value !== null && $value > $threshold['warning']) {
                $this->alertManager->triggerAlert('performance_threshold_exceeded', [
                    'request_id' => $requestId,
                    'metric' => $metric,
                    'value' => $value,
                    'threshold' => $threshold['warning'],
                    'severity' => 'warning',
                ]);
            }
        }
    }

    /**
     * Check system health thresholds
     */
    protected function checkSystemHealthThresholds(array $healthMetrics): void
    {
        $thresholds = $this->thresholds['system_health'] ?? [];

        foreach ($thresholds as $metric => $threshold) {
            $value = $healthMetrics[$metric] ?? null;

            if ($value !== null && $value > $threshold['critical']) {
                Event::dispatch(new SystemHealthDegraded(
                    $metric,
                    $value,
                    $threshold['critical'],
                    'critical'
                ));

                $this->alertManager->triggerAlert('system_health_degraded', [
                    'metric' => $metric,
                    'value' => $value,
                    'threshold' => $threshold['critical'],
                    'severity' => 'critical',
                ]);
            }
        }
    }

    /**
     * Calculate health score
     */
    protected function calculateHealthScore(array $metrics): float
    {
        $weights = $this->config['health_score_weights'] ?? [
            'cpu_usage' => 0.2,
            'memory_usage' => 0.2,
            'error_rate' => 0.3,
            'response_time_p95' => 0.2,
            'sla_compliance' => 0.1,
        ];

        $score = 100.0;

        foreach ($weights as $metric => $weight) {
            $value = $metrics[$metric] ?? 0;
            $penalty = $this->calculateMetricPenalty($metric, $value);
            $score -= $penalty * $weight * 100;
        }

        return max(0, min(100, $score));
    }

    /**
     * Calculate metric penalty for health score
     */
    protected function calculateMetricPenalty(string $metric, float $value): float
    {
        $thresholds = $this->thresholds['health_score'][$metric] ?? null;

        if (!$thresholds) {
            return 0;
        }

        if ($value <= $thresholds['good']) {
            return 0;
        } elseif ($value <= $thresholds['warning']) {
            return ($value - $thresholds['good']) / ($thresholds['warning'] - $thresholds['good']) * 0.3;
        } elseif ($value <= $thresholds['critical']) {
            return 0.3 + (($value - $thresholds['warning']) / ($thresholds['critical'] - $thresholds['warning']) * 0.7);
        } else {
            return 1.0;
        }
    }

    /**
     * Load monitoring thresholds
     */
    protected function loadThresholds(): void
    {
        $this->thresholds = Cache::remember('monitoring_thresholds', 300, function () {
            return config('integration.monitoring.thresholds', [
                'performance' => [
                    'elapsed_time' => ['warning' => 1.0, 'critical' => 5.0],
                    'memory_usage' => ['warning' => 100 * 1024 * 1024, 'critical' => 500 * 1024 * 1024],
                    'cpu_usage' => ['warning' => 70, 'critical' => 90],
                ],
                'system_health' => [
                    'cpu_usage' => ['warning' => 80, 'critical' => 95],
                    'memory_usage' => ['warning' => 80, 'critical' => 95],
                    'error_rate' => ['warning' => 5, 'critical' => 10],
                ],
                'health_score' => [
                    'cpu_usage' => ['good' => 50, 'warning' => 80, 'critical' => 95],
                    'memory_usage' => ['good' => 50, 'warning' => 80, 'critical' => 95],
                    'error_rate' => ['good' => 1, 'warning' => 5, 'critical' => 10],
                    'response_time_p95' => ['good' => 500, 'warning' => 1000, 'critical' => 5000],
                ],
            ]);
        });
    }

    /**
     * Collect baseline metrics
     */
    protected function collectBaselineMetrics(string $requestId, string $gatewayId): void
    {
        $baselineMetrics = [
            'request_id' => $requestId,
            'gateway_id' => $gatewayId,
            'timestamp' => now(),
            'system_load' => sys_getloadavg()[0] ?? 0,
            'memory_available' => $this->getAvailableMemory(),
            'active_connections' => $this->getActiveConnections(),
        ];

        Redis::setex(
            "baseline:request:{$requestId}",
            300,
            json_encode($baselineMetrics)
        );
    }

    // System metrics collection methods
    protected function getCpuUsage(): float
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0] ?? 0.0;
        }
        return 0.0;
    }

    protected function getMemoryUsage(): float
    {
        $memInfo = $this->getMemoryInfo();
        return ($memInfo['total'] - $memInfo['available']) / $memInfo['total'] * 100;
    }

    protected function getMemoryInfo(): array
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $memInfo = file_get_contents('/proc/meminfo');
            preg_match('/MemTotal:\s+(\d+)/', $memInfo, $total);
            preg_match('/MemAvailable:\s+(\d+)/', $memInfo, $available);

            return [
                'total' => ($total[1] ?? 0) * 1024,
                'available' => ($available[1] ?? 0) * 1024,
            ];
        }

        return ['total' => 0, 'available' => 0];
    }

    protected function getDiskUsage(): float
    {
        $total = disk_total_space('/');
        $free = disk_free_space('/');
        return $total > 0 ? (($total - $free) / $total) * 100 : 0;
    }

    protected function getNetworkIO(): array
    {
        // Simplified network I/O metrics
        return ['bytes_in' => 0, 'bytes_out' => 0];
    }

    protected function getDatabaseConnections(): int
    {
        // This would integrate with your database monitoring
        return 0;
    }

    protected function getRedisConnections(): int
    {
        try {
            $info = Redis::info();
            return (int) ($info['connected_clients'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getQueueSize(): int
    {
        // This would integrate with your queue monitoring
        return 0;
    }

    protected function calculateErrorRate(): float
    {
        // Calculate error rate from recent metrics
        return 0.0;
    }

    protected function calculateResponseTimePercentile(int $percentile): float
    {
        // Calculate response time percentile from recent metrics
        return 0.0;
    }

    protected function getAvailableMemory(): int
    {
        $memInfo = $this->getMemoryInfo();
        return $memInfo['available'];
    }

    protected function getActiveConnections(): int
    {
        return count($this->activeMonitors);
    }

    /**
     * Store system health metrics
     */
    protected function storeSystemHealthMetrics(array $metrics): void
    {
        $timestamp = now();
        $metricsData = [
            'timestamp' => $timestamp,
            'cpu_usage' => $metrics['cpu_usage'] ?? 0,
            'memory_usage' => $metrics['memory_usage'] ?? 0,
            'disk_usage' => $metrics['disk_usage'] ?? 0,
            'network_io' => $metrics['network_io'] ?? 0,
            'active_connections' => $metrics['active_connections'] ?? 0,
            'response_time' => $metrics['response_time'] ?? 0,
            'error_rate' => $metrics['error_rate'] ?? 0,
            'throughput' => $metrics['throughput'] ?? 0,
        ];

        // Store in time-series database or cache
        Cache::put(
            "system_health:" . $timestamp->format('Y-m-d-H-i'),
            $metricsData,
            3600
        );

        // Store aggregated hourly data
        $hourKey = "system_health_hourly:" . $timestamp->format('Y-m-d-H');
        $hourlyData = Cache::get($hourKey, []);
        $hourlyData[] = $metricsData;
        Cache::put($hourKey, $hourlyData, 86400);
    }

    /**
     * Store business metrics
     */
    protected function storeBusinessMetrics(array $metrics): void
    {
        $timestamp = now();
        $businessData = [
            'timestamp' => $timestamp,
            'total_requests' => $metrics['total_requests'] ?? 0,
            'successful_requests' => $metrics['successful_requests'] ?? 0,
            'failed_requests' => $metrics['failed_requests'] ?? 0,
            'unique_users' => $metrics['unique_users'] ?? 0,
            'revenue' => $metrics['revenue'] ?? 0,
            'conversions' => $metrics['conversions'] ?? 0,
            'api_calls' => $metrics['api_calls'] ?? 0,
        ];

        Cache::put(
            "business_metrics:" . $timestamp->format('Y-m-d-H-i'),
            $businessData,
            3600
        );

        // Store daily aggregates
        $dayKey = "business_metrics_daily:" . $timestamp->format('Y-m-d');
        $dailyData = Cache::get($dayKey, []);
        $dailyData[] = $businessData;
        Cache::put($dayKey, $dailyData, 86400 * 7);
    }

    /**
     * Update aggregated metrics
     */
    protected function updateAggregatedMetrics(array $metrics): void
    {
        $timestamp = now();
        $aggregateKey = "metrics_aggregate:" . $timestamp->format('Y-m-d-H');

        $currentAggregate = Cache::get($aggregateKey, [
            'total_requests' => 0,
            'total_errors' => 0,
            'total_response_time' => 0,
            'max_response_time' => 0,
            'min_response_time' => PHP_FLOAT_MAX,
            'unique_ips' => [],
            'endpoints_hit' => [],
        ]);

        // Update aggregates
        $currentAggregate['total_requests'] += $metrics['requests'] ?? 0;
        $currentAggregate['total_errors'] += $metrics['errors'] ?? 0;
        $currentAggregate['total_response_time'] += $metrics['response_time'] ?? 0;

        if (isset($metrics['response_time'])) {
            $currentAggregate['max_response_time'] = max(
                $currentAggregate['max_response_time'],
                $metrics['response_time']
            );
            $currentAggregate['min_response_time'] = min(
                $currentAggregate['min_response_time'],
                $metrics['response_time']
            );
        }

        if (isset($metrics['source_ip'])) {
            $currentAggregate['unique_ips'][$metrics['source_ip']] = true;
        }

        if (isset($metrics['endpoint_id'])) {
            $currentAggregate['endpoints_hit'][$metrics['endpoint_id']] = true;
        }

        Cache::put($aggregateKey, $currentAggregate, 3600);
    }
    /**
     * Get start time from range string
     */
    protected function getStartTimeFromRange(string $range): Carbon
    {
        return match ($range) {
            '1h' => now()->subHour(),
            '6h' => now()->subHours(6),
            '12h' => now()->subHours(12),
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subHour(),
        };
    }

    /**
     * Get overview metrics for gateway
     */
    protected function getOverviewMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "overview_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $metrics = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN error_message IS NULL THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as failed_requests,
                    AVG(processing_time) as avg_response_time,
                    MAX(processing_time) as max_response_time,
                    MIN(processing_time) as min_response_time,
                    COUNT(DISTINCT source_ip) as unique_ips,
                    COUNT(DISTINCT endpoint_id) as unique_endpoints
                ')
                ->first();

            if (!$metrics || $metrics->total_requests == 0) {
                return [
                    'total_requests' => 0,
                    'successful_requests' => 0,
                    'failed_requests' => 0,
                    'success_rate' => 100,
                    'error_rate' => 0,
                    'avg_response_time' => 0,
                    'max_response_time' => 0,
                    'min_response_time' => 0,
                    'unique_ips' => 0,
                    'unique_endpoints' => 0,
                ];
            }

            $successRate = ($metrics->successful_requests / $metrics->total_requests) * 100;
            $errorRate = ($metrics->failed_requests / $metrics->total_requests) * 100;

            return [
                'total_requests' => $metrics->total_requests,
                'successful_requests' => $metrics->successful_requests,
                'failed_requests' => $metrics->failed_requests,
                'success_rate' => round($successRate, 2),
                'error_rate' => round($errorRate, 2),
                'avg_response_time' => round($metrics->avg_response_time, 3),
                'max_response_time' => round($metrics->max_response_time, 3),
                'min_response_time' => round($metrics->min_response_time, 3),
                'unique_ips' => $metrics->unique_ips,
                'unique_endpoints' => $metrics->unique_endpoints,
            ];
        });
    }

    /**
     * Get performance metrics for gateway
     */
    protected function getPerformanceMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "performance_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $metrics = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    AVG(processing_time) as avg_response_time,
                    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY processing_time) as median_response_time,
                    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY processing_time) as p95_response_time,
                    PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY processing_time) as p99_response_time,
                    COUNT(*) / EXTRACT(EPOCH FROM (? - ?)) * 60 as requests_per_minute,
                    AVG(CASE WHEN JSON_EXTRACT(metrics, "$.memory_usage") IS NOT NULL
                        THEN JSON_EXTRACT(metrics, "$.memory_usage") ELSE 0 END) as avg_memory_usage,
                    AVG(CASE WHEN JSON_EXTRACT(metrics, "$.cpu_usage") IS NOT NULL
                        THEN JSON_EXTRACT(metrics, "$.cpu_usage") ELSE 0 END) as avg_cpu_usage
                ')
                ->addBinding($end)
                ->addBinding($start)
                ->first();

            return [
                'avg_response_time' => round($metrics->avg_response_time ?? 0, 3),
                'median_response_time' => round($metrics->median_response_time ?? 0, 3),
                'p95_response_time' => round($metrics->p95_response_time ?? 0, 3),
                'p99_response_time' => round($metrics->p99_response_time ?? 0, 3),
                'requests_per_minute' => round($metrics->requests_per_minute ?? 0, 2),
                'avg_memory_usage' => round(($metrics->avg_memory_usage ?? 0) / 1024 / 1024, 2), // MB
                'avg_cpu_usage' => round($metrics->avg_cpu_usage ?? 0, 2),
            ];
        });
    }

    /**
     * Get error metrics for gateway
     */
    protected function getErrorMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "error_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $errorStats = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->whereNotNull('error_message')
                ->selectRaw('
                    COUNT(*) as total_errors,
                    COUNT(DISTINCT error_message) as unique_errors,
                    error_message,
                    COUNT(*) as error_count
                ')
                ->groupBy('error_message')
                ->orderBy('error_count', 'desc')
                ->limit(10)
                ->get();

            $topErrors = $errorStats->map(function ($error) {
                return [
                    'message' => $error->error_message,
                    'count' => $error->error_count,
                ];
            })->toArray();

            $totalErrors = $errorStats->sum('error_count');

            return [
                'total_errors' => $totalErrors,
                'unique_errors' => $errorStats->count(),
                'top_errors' => $topErrors,
                'error_rate_trend' => $this->getErrorRateTrend($gatewayId, $start, $end),
            ];
        });
    }

    /**
     * Get error rate trend over time
     */
    protected function getErrorRateTrend(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $intervals = [];
        $current = $start->copy();
        $intervalMinutes = max(1, $start->diffInMinutes($end) / 20); // 20 data points

        while ($current < $end) {
            $intervalEnd = $current->copy()->addMinutes($intervalMinutes);

            $stats = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$current, $intervalEnd])
                ->selectRaw('
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count
                ')
                ->first();

            $errorRate = $stats->total_requests > 0 ?
                ($stats->error_count / $stats->total_requests) * 100 : 0;

            $intervals[] = [
                'timestamp' => $current->toISOString(),
                'error_rate' => round($errorRate, 2),
                'total_requests' => $stats->total_requests,
                'error_count' => $stats->error_count,
            ];

            $current = $intervalEnd;
        }

        return $intervals;
    }

    /**
     * Get traffic metrics for gateway
     */
    protected function getTrafficMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "traffic_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $hourlyStats = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    DATE_FORMAT(processed_at, "%Y-%m-%d %H:00:00") as hour,
                    COUNT(*) as requests,
                    AVG(processing_time) as avg_response_time,
                    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as errors
                ')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();

            $peakHour = $hourlyStats->sortByDesc('requests')->first();
            $totalRequests = $hourlyStats->sum('requests');
            $avgResponseTime = $hourlyStats->avg('avg_response_time');

            return [
                'total_requests' => $totalRequests,
                'avg_response_time' => round($avgResponseTime, 3),
                'peak_hour' => $peakHour ? [
                    'hour' => $peakHour->hour,
                    'requests' => $peakHour->requests,
                ] : null,
                'hourly_breakdown' => $hourlyStats->toArray(),
                'requests_per_second' => $totalRequests / $start->diffInSeconds($end),
            ];
        });
    }

    /**
     * Get security metrics for gateway
     */
    protected function getSecurityMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "security_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $securityEvents = DB::table('security_audit_log')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('detected_at', [$start, $end])
                ->selectRaw('
                    threat_type,
                    severity,
                    COUNT(*) as count,
                    COUNT(DISTINCT source_ip) as unique_ips
                ')
                ->groupBy('threat_type', 'severity')
                ->get();

            $blockedIps = DB::table('security_audit_log')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('detected_at', [$start, $end])
                ->distinct('source_ip')
                ->count();

            $threatsByType = $securityEvents->groupBy('threat_type')->map(function ($threats) {
                return $threats->sum('count');
            });

            return [
                'total_threats' => $securityEvents->sum('count'),
                'blocked_ips' => $blockedIps,
                'threats_by_type' => $threatsByType->toArray(),
                'threats_by_severity' => $securityEvents->groupBy('severity')->map(function ($threats) {
                    return $threats->sum('count');
                })->toArray(),
                'security_score' => $this->calculateSecurityScore($gatewayId, $start, $end),
            ];
        });
    }

    /**
     * Get business metrics for gateway
     */
    protected function getBusinessMetrics(string $gatewayId, Carbon $start, Carbon $end): array
    {
        $cacheKey = "business_metrics:{$gatewayId}:" . $start->format('Y-m-d-H') . ":" . $end->format('Y-m-d-H');

        return Cache::remember($cacheKey, 300, function () use ($gatewayId, $start, $end) {
            $apiUsage = DB::table('api_request_logs')
                ->where('gateway_id', $gatewayId)
                ->whereBetween('processed_at', [$start, $end])
                ->selectRaw('
                    COUNT(DISTINCT api_key_id) as active_api_keys,
                    COUNT(DISTINCT source_ip) as unique_users,
                    COUNT(DISTINCT endpoint_id) as endpoints_used,
                    SUM(request_size + response_size) as total_bandwidth
                ')
                ->first();

            return [
                'active_api_keys' => $apiUsage->active_api_keys ?? 0,
                'unique_users' => $apiUsage->unique_users ?? 0,
                'endpoints_used' => $apiUsage->endpoints_used ?? 0,
                'total_bandwidth' => $apiUsage->total_bandwidth ?? 0,
                'revenue_impact' => $this->calculateRevenueImpact($gatewayId),
                'sla_compliance' => $this->calculateSlaCompliance($gatewayId),
                'api_adoption_rate' => $this->calculateApiAdoptionRate($gatewayId),
                'cost_per_request' => $this->calculateCostPerRequest($gatewayId),
            ];
        });
    }

    /**
     * Get active alerts for gateway
     */
    protected function getActiveAlerts(string $gatewayId): array
    {
        $alerts = [];

        // Check performance alerts
        $avgResponseTime = $this->getAverageResponseTime($gatewayId, now()->subMinutes(5));
        if ($avgResponseTime > 2000) { // 2 seconds
            $alerts[] = [
                'type' => 'performance',
                'severity' => 'warning',
                'message' => 'High response time detected',
                'value' => $avgResponseTime,
                'threshold' => 2000,
            ];
        }

        // Check error rate alerts
        $errorRate = $this->getErrorRate($gatewayId, now()->subMinutes(5));
        if ($errorRate > 5) { // 5%
            $alerts[] = [
                'type' => 'error_rate',
                'severity' => 'critical',
                'message' => 'High error rate detected',
                'value' => $errorRate,
                'threshold' => 5,
            ];
        }

        // Check security alerts
        $securityThreats = $this->getRecentSecurityThreats($gatewayId);
        if ($securityThreats > 0) {
            $alerts[] = [
                'type' => 'security',
                'severity' => 'high',
                'message' => 'Security threats detected',
                'value' => $securityThreats,
                'threshold' => 0,
            ];
        }

        return $alerts;
    }

    /**
     * Get current health score for gateway
     */
    protected function getCurrentHealthScore(string $gatewayId): float
    {
        $weights = [
            'response_time' => 0.3,
            'error_rate' => 0.3,
            'availability' => 0.2,
            'security' => 0.2,
        ];

        $scores = [
            'response_time' => $this->calculateResponseTimeScore($gatewayId),
            'error_rate' => $this->calculateErrorRateScore($gatewayId),
            'availability' => $this->calculateAvailabilityScore($gatewayId),
            'security' => $this->calculateSecurityScore($gatewayId, now()->subHour(), now()),
        ];

        $totalScore = 0;
        foreach ($weights as $metric => $weight) {
            $totalScore += $scores[$metric] * $weight;
        }

        return round($totalScore, 1);
    }

    /**
     * Get total requests today for gateway
     */
    protected function getTotalRequestsToday(string $gatewayId): int
    {
        return (int) DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereDate('processed_at', today())
            ->count();
    }

    /**
     * Get successful requests today for gateway
     */
    protected function getSuccessfulRequestsToday(string $gatewayId): int
    {
        return (int) DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereDate('processed_at', today())
            ->whereNull('error_message')
            ->count();
    }

    /**
     * Get failed requests today for gateway
     */
    protected function getFailedRequestsToday(string $gatewayId): int
    {
        return (int) DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereDate('processed_at', today())
            ->whereNotNull('error_message')
            ->count();
    }

    // Helper methods for calculations
    protected function calculateRevenueImpact(string $gatewayId): float
    {
        // Placeholder calculation
        $requests = $this->getTotalRequestsToday($gatewayId);
        $costPerRequest = 0.001; // $0.001 per request
        return $requests * $costPerRequest;
    }

    protected function calculateSlaCompliance(string $gatewayId): float
    {
        $total = $this->getTotalRequestsToday($gatewayId);
        $successful = $this->getSuccessfulRequestsToday($gatewayId);
        return $total > 0 ? ($successful / $total) * 100 : 100.0;
    }

    protected function calculateApiAdoptionRate(string $gatewayId): float
    {
        $activeKeys = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->whereDate('processed_at', today())
            ->distinct('api_key_id')
            ->count();

        $totalKeys = DB::table('api_keys')
            ->where('gateway_id', $gatewayId)
            ->where('is_active', true)
            ->count();

        return $totalKeys > 0 ? ($activeKeys / $totalKeys) * 100 : 0.0;
    }

    protected function calculateCostPerRequest(string $gatewayId): float
    {
        // Simplified cost calculation
        return 0.001; // $0.001 per request
    }

    protected function getAverageResponseTime(string $gatewayId, Carbon $since): float
    {
        return (float) DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->where('processed_at', '>', $since)
            ->avg('processing_time') ?? 0;
    }

    protected function getErrorRate(string $gatewayId, Carbon $since): float
    {
        $total = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->where('processed_at', '>', $since)
            ->count();

        $errors = DB::table('api_request_logs')
            ->where('gateway_id', $gatewayId)
            ->where('processed_at', '>', $since)
            ->whereNotNull('error_message')
            ->count();

        return $total > 0 ? ($errors / $total) * 100 : 0;
    }

    protected function getRecentSecurityThreats(string $gatewayId): int
    {
        return (int) DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->where('detected_at', '>', now()->subMinutes(5))
            ->count();
    }

    protected function calculateResponseTimeScore(string $gatewayId): float
    {
        $avgTime = $this->getAverageResponseTime($gatewayId, now()->subMinutes(5));
        $maxTime = 2000; // 2 seconds
        return max(0, 100 - ($avgTime / $maxTime) * 100);
    }

    protected function calculateErrorRateScore(string $gatewayId): float
    {
        $errorRate = $this->getErrorRate($gatewayId, now()->subMinutes(5));
        $maxErrorRate = 5; // 5%
        return max(0, 100 - ($errorRate / $maxErrorRate) * 100);
    }

    protected function calculateAvailabilityScore(string $gatewayId): float
    {
        // Simplified availability calculation
        return 99.9; // Assume 99.9% availability
    }

    protected function calculateSecurityScore(string $gatewayId, Carbon $start, Carbon $end): float
    {
        $threats = DB::table('security_audit_log')
            ->where('gateway_id', $gatewayId)
            ->whereBetween('detected_at', [$start, $end])
            ->count();

        // Score decreases with more threats
        return max(0, 100 - ($threats * 5)); // -5 points per threat
    }
}
