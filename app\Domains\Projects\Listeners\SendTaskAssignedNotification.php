<?php

namespace App\Domains\Projects\Listeners;

use App\Domains\Projects\Events\TaskAssigned;
use App\Domains\Projects\Notifications\TaskAssignedNotification;
use App\Domains\Projects\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * مستمع إرسال إشعار تعيين المهمة
 */
class SendTaskAssignedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * إنشاء مستمع الحدث
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * معالجة الحدث
     */
    public function handle(TaskAssigned $event): void
    {
        $task = $event->task;
        $newAssigneeId = $event->newAssigneeId;
        $oldAssigneeId = $event->oldAssigneeId;

        // إرسال إشعار للمكلف الجديد
        if ($newAssigneeId) {
            $newAssignee = \App\Models\User::find($newAssigneeId);
            if ($newAssignee) {
                $newAssignee->notify(
                    new TaskAssignedNotification($task, 'assigned_to_you')
                );
            }
        }

        // إرسال إشعار للمكلف السابق إذا كان موجوداً
        if ($oldAssigneeId && $oldAssigneeId !== $newAssigneeId) {
            $oldAssignee = \App\Models\User::find($oldAssigneeId);
            if ($oldAssignee) {
                $oldAssignee->notify(
                    new TaskAssignedNotification($task, 'unassigned_from_you')
                );
            }
        }

        // إرسال إشعار لمدير المشروع إذا لم يكن هو من قام بالتعيين
        if ($task->project->project_manager_id && 
            $task->project->project_manager_id !== auth()->id()) {
            $task->project->projectManager->notify(
                new TaskAssignedNotification($task, 'task_assigned_in_project')
            );
        }

        // إرسال إشعار للمتابعين
        foreach ($task->watchers as $watcher) {
            if ($watcher->id !== $newAssigneeId && $watcher->id !== auth()->id()) {
                $watcher->notify(
                    new TaskAssignedNotification($task, 'task_assigned_watched')
                );
            }
        }

        // إرسال إشعارات خارجية
        $this->sendExternalNotifications($task, $newAssigneeId, $oldAssigneeId);

        // تسجيل النشاط
        $this->logActivity($task, $newAssigneeId, $oldAssigneeId);
    }

    /**
     * إرسال إشعارات خارجية
     */
    protected function sendExternalNotifications($task, $newAssigneeId, $oldAssigneeId): void
    {
        $newAssignee = $newAssigneeId ? \App\Models\User::find($newAssigneeId) : null;
        $oldAssignee = $oldAssigneeId ? \App\Models\User::find($oldAssigneeId) : null;

        // إرسال إشعار Slack
        if ($task->project->notification_settings['slack_notifications'] ?? false) {
            $message = $newAssignee 
                ? "📋 تم تعيين المهمة '{$task->title}' لـ {$newAssignee->name}"
                : "📋 تم إلغاء تعيين المهمة '{$task->title}'";

            $this->notificationService->sendSlackNotification([
                'channel' => '#tasks',
                'message' => $message,
                'task' => $task,
                'assignee' => $newAssignee,
                'type' => 'task_assigned',
            ]);
        }

        // إرسال بريد إلكتروني
        if ($task->project->notification_settings['email_notifications'] ?? true) {
            $recipients = [];
            
            if ($newAssignee && $newAssignee->email) {
                $recipients[] = $newAssignee->email;
            }
            
            if ($task->project->projectManager && $task->project->projectManager->email) {
                $recipients[] = $task->project->projectManager->email;
            }

            if (!empty($recipients)) {
                $this->notificationService->sendEmailNotification([
                    'template' => 'task-assigned',
                    'recipients' => array_unique($recipients),
                    'data' => [
                        'task' => $task,
                        'project' => $task->project,
                        'new_assignee' => $newAssignee,
                        'old_assignee' => $oldAssignee,
                        'assigned_by' => auth()->user(),
                    ],
                ]);
            }
        }

        // إرسال إشعار Teams
        if ($task->project->notification_settings['teams_notifications'] ?? false) {
            $message = $newAssignee 
                ? "تم تعيين المهمة '{$task->title}' لـ {$newAssignee->name}"
                : "تم إلغاء تعيين المهمة '{$task->title}'";

            $this->notificationService->sendTeamsNotification([
                'webhook_url' => config('services.teams.webhook_url'),
                'title' => 'تعيين مهمة',
                'message' => $message,
                'task' => $task,
                'assignee' => $newAssignee,
            ]);
        }
    }

    /**
     * تسجيل النشاط
     */
    protected function logActivity($task, $newAssigneeId, $oldAssigneeId): void
    {
        $newAssignee = $newAssigneeId ? \App\Models\User::find($newAssigneeId) : null;
        $oldAssignee = $oldAssigneeId ? \App\Models\User::find($oldAssigneeId) : null;

        $description = 'تم تعيين المهمة';
        if ($newAssignee && $oldAssignee) {
            $description = "تم تغيير تعيين المهمة من {$oldAssignee->name} إلى {$newAssignee->name}";
        } elseif ($newAssignee) {
            $description = "تم تعيين المهمة لـ {$newAssignee->name}";
        } elseif ($oldAssignee) {
            $description = "تم إلغاء تعيين المهمة من {$oldAssignee->name}";
        }

        activity()
            ->performedOn($task)
            ->causedBy(auth()->user())
            ->withProperties([
                'task_title' => $task->title,
                'task_number' => $task->task_number,
                'project_name' => $task->project->name,
                'new_assignee' => $newAssignee?->name,
                'old_assignee' => $oldAssignee?->name,
                'new_assignee_id' => $newAssigneeId,
                'old_assignee_id' => $oldAssigneeId,
            ])
            ->log($description);
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(TaskAssigned $event, $exception): void
    {
        \Log::error('فشل في إرسال إشعار تعيين المهمة', [
            'task_id' => $event->task->id,
            'task_title' => $event->task->title,
            'new_assignee_id' => $event->newAssigneeId,
            'old_assignee_id' => $event->oldAssigneeId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // إرسال إشعار للمدراء بالفشل
        $admins = \App\Models\User::role('admin')->get();
        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\SystemErrorNotification([
                'type' => 'notification_failure',
                'message' => 'فشل في إرسال إشعار تعيين المهمة',
                'task_id' => $event->task->id,
                'error' => $exception->getMessage(),
            ]));
        }
    }
}
