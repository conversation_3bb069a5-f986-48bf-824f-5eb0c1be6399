<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التحقق من صحة البيانات للتجارة الإلكترونية
 * تتحقق من صحة البيانات المحولة قبل الحفظ
 */
class ECommerceValidationService
{
    /**
     * التحقق من صحة بيانات المنتج
     */
    public function validateProduct(array $data, ECommerceIntegration $integration = null): array
    {
        $rules = $this->getProductValidationRules($integration);
        $messages = $this->getProductValidationMessages();

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->toArray(),
                'failed_rules' => $validator->failed(),
            ];
        }

        // التحقق من القواعد المخصصة
        $customValidation = $this->validateProductCustomRules($data, $integration);
        
        if (!$customValidation['valid']) {
            return $customValidation;
        }

        return [
            'valid' => true,
            'errors' => [],
            'warnings' => $this->getProductWarnings($data, $integration),
        ];
    }

    /**
     * التحقق من صحة بيانات الطلب
     */
    public function validateOrder(array $data, ECommerceIntegration $integration = null): array
    {
        $rules = $this->getOrderValidationRules($integration);
        $messages = $this->getOrderValidationMessages();

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->toArray(),
                'failed_rules' => $validator->failed(),
            ];
        }

        // التحقق من القواعد المخصصة
        $customValidation = $this->validateOrderCustomRules($data, $integration);
        
        if (!$customValidation['valid']) {
            return $customValidation;
        }

        return [
            'valid' => true,
            'errors' => [],
            'warnings' => $this->getOrderWarnings($data, $integration),
        ];
    }

    /**
     * التحقق من صحة بيانات العميل
     */
    public function validateCustomer(array $data, ECommerceIntegration $integration = null): array
    {
        $rules = $this->getCustomerValidationRules($integration);
        $messages = $this->getCustomerValidationMessages();

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->toArray(),
                'failed_rules' => $validator->failed(),
            ];
        }

        // التحقق من القواعد المخصصة
        $customValidation = $this->validateCustomerCustomRules($data, $integration);
        
        if (!$customValidation['valid']) {
            return $customValidation;
        }

        return [
            'valid' => true,
            'errors' => [],
            'warnings' => $this->getCustomerWarnings($data, $integration),
        ];
    }

    /**
     * التحقق من صحة بيانات عنصر الطلب
     */
    public function validateOrderItem(array $data, ECommerceIntegration $integration = null): array
    {
        $rules = $this->getOrderItemValidationRules($integration);
        $messages = $this->getOrderItemValidationMessages();

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->toArray(),
                'failed_rules' => $validator->failed(),
            ];
        }

        return [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
        ];
    }

    /**
     * الحصول على قواعد التحقق للمنتج
     */
    protected function getProductValidationRules(ECommerceIntegration $integration = null): array
    {
        $baseRules = [
            'external_id' => 'required|string|max:255',
            'name' => 'required|string|max:500',
            'description' => 'nullable|string',
            'sku' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'regular_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'inventory_quantity' => 'nullable|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'nullable|string|in:kg,g,lb,oz',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.height' => 'nullable|numeric|min:0',
            'images' => 'nullable|array',
            'images.*' => 'nullable|url',
            'featured_image' => 'nullable|url',
            'gallery_images' => 'nullable|array',
            'gallery_images.*' => 'nullable|url',
            'categories' => 'nullable|array',
            'tags' => 'nullable|array',
            'attributes' => 'nullable|array',
            'variations' => 'nullable|array',
            'options' => 'nullable|array',
            'meta_data' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'vendor' => 'nullable|string|max:255',
            'brand' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'product_type' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,inactive,draft,archived',
            'visibility' => 'nullable|string|in:visible,hidden,catalog,search',
            'featured' => 'nullable|boolean',
            'virtual' => 'nullable|boolean',
            'downloadable' => 'nullable|boolean',
            'manage_stock' => 'nullable|boolean',
            'in_stock' => 'nullable|boolean',
            'backorders_allowed' => 'nullable|boolean',
            'sold_individually' => 'nullable|boolean',
            'reviews_allowed' => 'nullable|boolean',
            'rating_average' => 'nullable|numeric|min:0|max:5',
            'rating_count' => 'nullable|integer|min:0',
            'purchase_note' => 'nullable|string',
            'shipping_class' => 'nullable|string|max:255',
            'tax_class' => 'nullable|string|max:255',
            'tax_status' => 'nullable|string|in:taxable,shipping,none',
            'upsell_ids' => 'nullable|array',
            'cross_sell_ids' => 'nullable|array',
            'parent_id' => 'nullable|integer',
            'grouped_products' => 'nullable|array',
            'external_url' => 'nullable|url',
            'button_text' => 'nullable|string|max:255',
            'menu_order' => 'nullable|integer',
            'date_on_sale_from' => 'nullable|date',
            'date_on_sale_to' => 'nullable|date',
            'created_at' => 'nullable|date',
            'updated_at' => 'nullable|date',
        ];

        // إضافة القواعد المخصصة من التكامل
        if ($integration && isset($integration->validation_rules['products'])) {
            $customRules = $integration->validation_rules['products'];
            $baseRules = array_merge($baseRules, $customRules);
        }

        return $baseRules;
    }

    /**
     * الحصول على قواعد التحقق للطلب
     */
    protected function getOrderValidationRules(ECommerceIntegration $integration = null): array
    {
        $baseRules = [
            'external_id' => 'required|string|max:255',
            'order_number' => 'nullable|string|max:255',
            'order_key' => 'nullable|string|max:255',
            'parent_id' => 'nullable|integer',
            'status' => 'nullable|string|max:100',
            'financial_status' => 'nullable|string|max:100',
            'fulfillment_status' => 'nullable|string|max:100',
            'payment_status' => 'nullable|string|max:100',
            'shipping_status' => 'nullable|string|max:100',
            'order_date' => 'nullable|date',
            'created_via' => 'nullable|string|max:255',
            'version' => 'nullable|string|max:50',
            'currency' => 'required|string|size:3',
            'prices_include_tax' => 'nullable|boolean',
            'customer_ip_address' => 'nullable|ip',
            'customer_user_agent' => 'nullable|string',
            'customer_note' => 'nullable|string',
            'billing_address' => 'nullable|array',
            'billing_address.first_name' => 'nullable|string|max:255',
            'billing_address.last_name' => 'nullable|string|max:255',
            'billing_address.company' => 'nullable|string|max:255',
            'billing_address.address_1' => 'nullable|string|max:255',
            'billing_address.address_2' => 'nullable|string|max:255',
            'billing_address.city' => 'nullable|string|max:255',
            'billing_address.state' => 'nullable|string|max:255',
            'billing_address.postcode' => 'nullable|string|max:20',
            'billing_address.country' => 'nullable|string|size:2',
            'billing_address.email' => 'nullable|email',
            'billing_address.phone' => 'nullable|string|max:20',
            'shipping_address' => 'nullable|array',
            'shipping_address.first_name' => 'nullable|string|max:255',
            'shipping_address.last_name' => 'nullable|string|max:255',
            'shipping_address.company' => 'nullable|string|max:255',
            'shipping_address.address_1' => 'nullable|string|max:255',
            'shipping_address.address_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'nullable|string|max:255',
            'shipping_address.state' => 'nullable|string|max:255',
            'shipping_address.postcode' => 'nullable|string|max:20',
            'shipping_address.country' => 'nullable|string|size:2',
            'line_items' => 'nullable|array',
            'line_items.*.external_id' => 'required|string',
            'line_items.*.name' => 'required|string',
            'line_items.*.quantity' => 'required|integer|min:1',
            'line_items.*.price' => 'required|numeric|min:0',
            'shipping_lines' => 'nullable|array',
            'tax_lines' => 'nullable|array',
            'fee_lines' => 'nullable|array',
            'coupon_lines' => 'nullable|array',
            'refunds' => 'nullable|array',
            'payment_method' => 'nullable|string|max:255',
            'payment_method_title' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'date_paid' => 'nullable|date',
            'date_completed' => 'nullable|date',
            'cart_hash' => 'nullable|string|max:255',
            'meta_data' => 'nullable|array',
            'subtotal' => 'nullable|numeric|min:0',
            'subtotal_tax' => 'nullable|numeric|min:0',
            'shipping_total' => 'nullable|numeric|min:0',
            'shipping_tax' => 'nullable|numeric|min:0',
            'discount_total' => 'nullable|numeric|min:0',
            'discount_tax' => 'nullable|numeric|min:0',
            'cart_tax' => 'nullable|numeric|min:0',
            'total' => 'required|numeric|min:0',
            'total_tax' => 'nullable|numeric|min:0',
            'total_discount' => 'nullable|numeric|min:0',
            'total_shipping' => 'nullable|numeric|min:0',
            'total_fees' => 'nullable|numeric|min:0',
            'total_refunded' => 'nullable|numeric|min:0',
            'total_paid' => 'nullable|numeric|min:0',
            'total_due' => 'nullable|numeric|min:0',
            'weight_total' => 'nullable|numeric|min:0',
            'item_count' => 'nullable|integer|min:0',
            'quantity_total' => 'nullable|integer|min:0',
            'needs_payment' => 'nullable|boolean',
            'needs_processing' => 'nullable|boolean',
            'downloadable' => 'nullable|boolean',
            'virtual' => 'nullable|boolean',
            'gift_message' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'delivery_instructions' => 'nullable|string',
            'gift_wrap' => 'nullable|boolean',
            'gift_wrap_message' => 'nullable|string',
            'priority' => 'nullable|integer|min:1|max:10',
            'urgency' => 'nullable|string|in:low,normal,high,urgent',
            'source' => 'nullable|string|max:255',
            'channel' => 'nullable|string|max:255',
            'campaign' => 'nullable|string|max:255',
            'medium' => 'nullable|string|max:255',
            'referrer' => 'nullable|url',
            'landing_page' => 'nullable|url',
            'utm_source' => 'nullable|string|max:255',
            'utm_medium' => 'nullable|string|max:255',
            'utm_campaign' => 'nullable|string|max:255',
            'utm_term' => 'nullable|string|max:255',
            'utm_content' => 'nullable|string|max:255',
            'affiliate_id' => 'nullable|string|max:255',
            'affiliate_code' => 'nullable|string|max:255',
            'coupon_code' => 'nullable|string|max:255',
            'discount_code' => 'nullable|string|max:255',
            'promo_code' => 'nullable|string|max:255',
            'voucher_code' => 'nullable|string|max:255',
            'gift_card_code' => 'nullable|string|max:255',
            'loyalty_points_used' => 'nullable|integer|min:0',
            'loyalty_points_earned' => 'nullable|integer|min:0',
            'reward_points_used' => 'nullable|integer|min:0',
            'reward_points_earned' => 'nullable|integer|min:0',
            'cashback_amount' => 'nullable|numeric|min:0',
            'cashback_percentage' => 'nullable|numeric|min:0|max:100',
            'commission_amount' => 'nullable|numeric|min:0',
            'commission_percentage' => 'nullable|numeric|min:0|max:100',
            'margin_amount' => 'nullable|numeric',
            'margin_percentage' => 'nullable|numeric',
            'profit_amount' => 'nullable|numeric',
            'profit_percentage' => 'nullable|numeric',
            'cost_amount' => 'nullable|numeric|min:0',
            'cost_percentage' => 'nullable|numeric|min:0|max:100',
            'markup_amount' => 'nullable|numeric|min:0',
            'markup_percentage' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'tax_amount' => 'nullable|numeric|min:0',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'shipping_amount' => 'nullable|numeric|min:0',
            'shipping_percentage' => 'nullable|numeric|min:0|max:100',
            'handling_amount' => 'nullable|numeric|min:0',
            'handling_percentage' => 'nullable|numeric|min:0|max:100',
            'insurance_amount' => 'nullable|numeric|min:0',
            'insurance_percentage' => 'nullable|numeric|min:0|max:100',
            'packaging_amount' => 'nullable|numeric|min:0',
            'packaging_percentage' => 'nullable|numeric|min:0|max:100',
            'processing_amount' => 'nullable|numeric|min:0',
            'processing_percentage' => 'nullable|numeric|min:0|max:100',
            'service_amount' => 'nullable|numeric|min:0',
            'service_percentage' => 'nullable|numeric|min:0|max:100',
            'convenience_amount' => 'nullable|numeric|min:0',
            'convenience_percentage' => 'nullable|numeric|min:0|max:100',
            'surcharge_amount' => 'nullable|numeric|min:0',
            'surcharge_percentage' => 'nullable|numeric|min:0|max:100',
            'adjustment_amount' => 'nullable|numeric',
            'adjustment_percentage' => 'nullable|numeric',
            'rounding_amount' => 'nullable|numeric',
            'rounding_percentage' => 'nullable|numeric',
            'exchange_rate' => 'nullable|numeric|min:0',
            'base_currency' => 'nullable|string|size:3',
            'order_currency' => 'nullable|string|size:3',
            'customer_currency' => 'nullable|string|size:3',
            'display_currency' => 'nullable|string|size:3',
            'reporting_currency' => 'nullable|string|size:3',
            'accounting_currency' => 'nullable|string|size:3',
            'settlement_currency' => 'nullable|string|size:3',
            'payment_currency' => 'nullable|string|size:3',
            'refund_currency' => 'nullable|string|size:3',
            'chargeback_currency' => 'nullable|string|size:3',
            'dispute_currency' => 'nullable|string|size:3',
            'fraud_score' => 'nullable|numeric|min:0|max:100',
            'risk_score' => 'nullable|numeric|min:0|max:100',
            'trust_score' => 'nullable|numeric|min:0|max:100',
            'quality_score' => 'nullable|numeric|min:0|max:100',
            'satisfaction_score' => 'nullable|numeric|min:0|max:100',
            'nps_score' => 'nullable|numeric|min:-100|max:100',
            'csat_score' => 'nullable|numeric|min:0|max:100',
            'ces_score' => 'nullable|numeric|min:0|max:100',
            'fcr_score' => 'nullable|numeric|min:0|max:100',
            'resolution_score' => 'nullable|numeric|min:0|max:100',
            'escalation_score' => 'nullable|numeric|min:0|max:100',
            'complaint_score' => 'nullable|numeric|min:0|max:100',
            'compliment_score' => 'nullable|numeric|min:0|max:100',
            'feedback_score' => 'nullable|numeric|min:0|max:100',
            'review_score' => 'nullable|numeric|min:0|max:100',
            'rating_score' => 'nullable|numeric|min:0|max:100',
            'recommendation_score' => 'nullable|numeric|min:0|max:100',
            'referral_score' => 'nullable|numeric|min:0|max:100',
            'loyalty_score' => 'nullable|numeric|min:0|max:100',
            'retention_score' => 'nullable|numeric|min:0|max:100',
            'churn_score' => 'nullable|numeric|min:0|max:100',
            'lifetime_value' => 'nullable|numeric|min:0',
            'customer_value' => 'nullable|numeric|min:0',
            'order_value' => 'nullable|numeric|min:0',
            'average_order_value' => 'nullable|numeric|min:0',
            'frequency_score' => 'nullable|numeric|min:0|max:100',
            'recency_score' => 'nullable|numeric|min:0|max:100',
            'monetary_score' => 'nullable|numeric|min:0|max:100',
            'rfm_score' => 'nullable|numeric|min:0|max:100',
            'clv_score' => 'nullable|numeric|min:0|max:100',
            'ltv_score' => 'nullable|numeric|min:0|max:100',
            'created_at' => 'nullable|date',
            'updated_at' => 'nullable|date',
        ];

        // إضافة القواعد المخصصة من التكامل
        if ($integration && isset($integration->validation_rules['orders'])) {
            $customRules = $integration->validation_rules['orders'];
            $baseRules = array_merge($baseRules, $customRules);
        }

        return $baseRules;
    }

    /**
     * الحصول على قواعد التحقق للعميل
     */
    protected function getCustomerValidationRules(ECommerceIntegration $integration = null): array
    {
        $baseRules = [
            'external_id' => 'required|string|max:255',
            'username' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'display_name' => 'nullable|string|max:255',
            'avatar_url' => 'nullable|url',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|string|in:male,female,other',
            'language' => 'nullable|string|max:10',
            'locale' => 'nullable|string|max:10',
            'timezone' => 'nullable|string|max:50',
            'currency' => 'nullable|string|size:3',
            'country' => 'nullable|string|size:2',
            'state' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'billing_address' => 'nullable|array',
            'shipping_address' => 'nullable|array',
            'addresses' => 'nullable|array',
            'role' => 'nullable|string|max:100',
            'customer_group' => 'nullable|string|max:100',
            'customer_type' => 'nullable|string|max:100',
            'customer_segment' => 'nullable|string|max:100',
            'customer_tier' => 'nullable|string|max:100',
            'customer_status' => 'nullable|string|max:100',
            'account_status' => 'nullable|string|max:100',
            'email_verified' => 'nullable|boolean',
            'phone_verified' => 'nullable|boolean',
            'identity_verified' => 'nullable|boolean',
            'kyc_status' => 'nullable|string|max:100',
            'kyc_level' => 'nullable|integer|min:0|max:10',
            'risk_level' => 'nullable|string|in:low,medium,high',
            'trust_score' => 'nullable|numeric|min:0|max:100',
            'credit_score' => 'nullable|numeric|min:0|max:1000',
            'loyalty_points' => 'nullable|integer|min:0',
            'reward_points' => 'nullable|integer|min:0',
            'store_credit' => 'nullable|numeric|min:0',
            'wallet_balance' => 'nullable|numeric|min:0',
            'total_spent' => 'nullable|numeric|min:0',
            'total_orders' => 'nullable|integer|min:0',
            'total_items' => 'nullable|integer|min:0',
            'average_order_value' => 'nullable|numeric|min:0',
            'lifetime_value' => 'nullable|numeric|min:0',
            'first_order_date' => 'nullable|date',
            'last_order_date' => 'nullable|date',
            'last_login_date' => 'nullable|date',
            'registration_date' => 'nullable|date',
            'preferences' => 'nullable|array',
            'interests' => 'nullable|array',
            'tags' => 'nullable|array',
            'notes' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'marketing_consent' => 'nullable|boolean',
            'email_marketing' => 'nullable|boolean',
            'sms_marketing' => 'nullable|boolean',
            'push_notifications' => 'nullable|boolean',
            'newsletter_subscription' => 'nullable|boolean',
            'promotional_emails' => 'nullable|boolean',
            'order_updates' => 'nullable|boolean',
            'shipping_updates' => 'nullable|boolean',
            'product_recommendations' => 'nullable|boolean',
            'price_alerts' => 'nullable|boolean',
            'stock_alerts' => 'nullable|boolean',
            'wishlist_updates' => 'nullable|boolean',
            'review_reminders' => 'nullable|boolean',
            'birthday_offers' => 'nullable|boolean',
            'anniversary_offers' => 'nullable|boolean',
            'seasonal_offers' => 'nullable|boolean',
            'personalized_offers' => 'nullable|boolean',
            'exclusive_offers' => 'nullable|boolean',
            'vip_offers' => 'nullable|boolean',
            'early_access' => 'nullable|boolean',
            'beta_access' => 'nullable|boolean',
            'preview_access' => 'nullable|boolean',
            'member_benefits' => 'nullable|boolean',
            'loyalty_benefits' => 'nullable|boolean',
            'tier_benefits' => 'nullable|boolean',
            'group_benefits' => 'nullable|boolean',
            'referral_code' => 'nullable|string|max:255',
            'referred_by' => 'nullable|string|max:255',
            'referral_count' => 'nullable|integer|min:0',
            'referral_earnings' => 'nullable|numeric|min:0',
            'affiliate_id' => 'nullable|string|max:255',
            'affiliate_code' => 'nullable|string|max:255',
            'affiliate_earnings' => 'nullable|numeric|min:0',
            'social_profiles' => 'nullable|array',
            'social_connections' => 'nullable|array',
            'social_sharing' => 'nullable|boolean',
            'social_login' => 'nullable|boolean',
            'oauth_providers' => 'nullable|array',
            'login_methods' => 'nullable|array',
            'security_settings' => 'nullable|array',
            'privacy_settings' => 'nullable|array',
            'communication_settings' => 'nullable|array',
            'notification_settings' => 'nullable|array',
            'display_settings' => 'nullable|array',
            'accessibility_settings' => 'nullable|array',
            'device_info' => 'nullable|array',
            'browser_info' => 'nullable|array',
            'session_info' => 'nullable|array',
            'location_info' => 'nullable|array',
            'ip_address' => 'nullable|ip',
            'user_agent' => 'nullable|string',
            'last_seen_ip' => 'nullable|ip',
            'last_seen_location' => 'nullable|string|max:255',
            'last_seen_device' => 'nullable|string|max:255',
            'last_seen_browser' => 'nullable|string|max:255',
            'login_count' => 'nullable|integer|min:0',
            'session_count' => 'nullable|integer|min:0',
            'page_views' => 'nullable|integer|min:0',
            'time_spent' => 'nullable|integer|min:0',
            'bounce_rate' => 'nullable|numeric|min:0|max:100',
            'conversion_rate' => 'nullable|numeric|min:0|max:100',
            'engagement_score' => 'nullable|numeric|min:0|max:100',
            'activity_score' => 'nullable|numeric|min:0|max:100',
            'interaction_score' => 'nullable|numeric|min:0|max:100',
            'participation_score' => 'nullable|numeric|min:0|max:100',
            'contribution_score' => 'nullable|numeric|min:0|max:100',
            'influence_score' => 'nullable|numeric|min:0|max:100',
            'advocacy_score' => 'nullable|numeric|min:0|max:100',
            'satisfaction_score' => 'nullable|numeric|min:0|max:100',
            'nps_score' => 'nullable|numeric|min:-100|max:100',
            'csat_score' => 'nullable|numeric|min:0|max:100',
            'ces_score' => 'nullable|numeric|min:0|max:100',
            'quality_score' => 'nullable|numeric|min:0|max:100',
            'service_score' => 'nullable|numeric|min:0|max:100',
            'support_score' => 'nullable|numeric|min:0|max:100',
            'experience_score' => 'nullable|numeric|min:0|max:100',
            'journey_score' => 'nullable|numeric|min:0|max:100',
            'touchpoint_score' => 'nullable|numeric|min:0|max:100',
            'channel_score' => 'nullable|numeric|min:0|max:100',
            'platform_score' => 'nullable|numeric|min:0|max:100',
            'device_score' => 'nullable|numeric|min:0|max:100',
            'browser_score' => 'nullable|numeric|min:0|max:100',
            'mobile_score' => 'nullable|numeric|min:0|max:100',
            'desktop_score' => 'nullable|numeric|min:0|max:100',
            'tablet_score' => 'nullable|numeric|min:0|max:100',
            'app_score' => 'nullable|numeric|min:0|max:100',
            'web_score' => 'nullable|numeric|min:0|max:100',
            'email_score' => 'nullable|numeric|min:0|max:100',
            'sms_score' => 'nullable|numeric|min:0|max:100',
            'push_score' => 'nullable|numeric|min:0|max:100',
            'social_score' => 'nullable|numeric|min:0|max:100',
            'search_score' => 'nullable|numeric|min:0|max:100',
            'organic_score' => 'nullable|numeric|min:0|max:100',
            'paid_score' => 'nullable|numeric|min:0|max:100',
            'direct_score' => 'nullable|numeric|min:0|max:100',
            'referral_score' => 'nullable|numeric|min:0|max:100',
            'affiliate_score' => 'nullable|numeric|min:0|max:100',
            'partner_score' => 'nullable|numeric|min:0|max:100',
            'influencer_score' => 'nullable|numeric|min:0|max:100',
            'brand_score' => 'nullable|numeric|min:0|max:100',
            'product_score' => 'nullable|numeric|min:0|max:100',
            'category_score' => 'nullable|numeric|min:0|max:100',
            'price_score' => 'nullable|numeric|min:0|max:100',
            'value_score' => 'nullable|numeric|min:0|max:100',
            'created_at' => 'nullable|date',
            'updated_at' => 'nullable|date',
        ];

        // إضافة القواعد المخصصة من التكامل
        if ($integration && isset($integration->validation_rules['customers'])) {
            $customRules = $integration->validation_rules['customers'];
            $baseRules = array_merge($baseRules, $customRules);
        }

        return $baseRules;
    }

    /**
     * الحصول على قواعد التحقق لعنصر الطلب
     */
    protected function getOrderItemValidationRules(ECommerceIntegration $integration = null): array
    {
        $baseRules = [
            'external_id' => 'required|string|max:255',
            'external_product_id' => 'nullable|string|max:255',
            'external_variant_id' => 'nullable|string|max:255',
            'name' => 'required|string|max:500',
            'sku' => 'nullable|string|max:255',
            'product_name' => 'nullable|string|max:500',
            'variant_name' => 'nullable|string|max:500',
            'product_url' => 'nullable|url',
            'image_url' => 'nullable|url',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'regular_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'unit_price' => 'nullable|numeric|min:0',
            'total_price' => 'nullable|numeric|min:0',
            'subtotal' => 'nullable|numeric|min:0',
            'subtotal_tax' => 'nullable|numeric|min:0',
            'total' => 'nullable|numeric|min:0',
            'total_tax' => 'nullable|numeric|min:0',
            'tax_class' => 'nullable|string|max:255',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'coupon_discount' => 'nullable|numeric|min:0',
            'line_discount' => 'nullable|numeric|min:0',
            'item_discount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'nullable|string|in:kg,g,lb,oz',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.height' => 'nullable|numeric|min:0',
            'volume' => 'nullable|numeric|min:0',
            'volume_unit' => 'nullable|string|max:10',
            'attributes' => 'nullable|array',
            'variations' => 'nullable|array',
            'options' => 'nullable|array',
            'customizations' => 'nullable|array',
            'personalizations' => 'nullable|array',
            'gift_message' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'notes' => 'nullable|string',
            'meta_data' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'category' => 'nullable|string|max:255',
            'brand' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:255',
            'status' => 'nullable|string|max:100',
            'fulfillment_status' => 'nullable|string|max:100',
            'shipping_status' => 'nullable|string|max:100',
            'delivery_status' => 'nullable|string|max:100',
            'return_status' => 'nullable|string|max:100',
            'refund_status' => 'nullable|string|max:100',
            'warranty_status' => 'nullable|string|max:100',
            'service_status' => 'nullable|string|max:100',
            'support_status' => 'nullable|string|max:100',
            'quality_status' => 'nullable|string|max:100',
            'condition' => 'nullable|string|max:100',
            'grade' => 'nullable|string|max:100',
            'rating' => 'nullable|numeric|min:0|max:5',
            'review' => 'nullable|string',
            'feedback' => 'nullable|string',
            'satisfaction_score' => 'nullable|numeric|min:0|max:100',
            'quality_score' => 'nullable|numeric|min:0|max:100',
            'value_score' => 'nullable|numeric|min:0|max:100',
            'service_score' => 'nullable|numeric|min:0|max:100',
            'delivery_score' => 'nullable|numeric|min:0|max:100',
            'packaging_score' => 'nullable|numeric|min:0|max:100',
            'presentation_score' => 'nullable|numeric|min:0|max:100',
            'appearance_score' => 'nullable|numeric|min:0|max:100',
            'functionality_score' => 'nullable|numeric|min:0|max:100',
            'performance_score' => 'nullable|numeric|min:0|max:100',
            'reliability_score' => 'nullable|numeric|min:0|max:100',
            'durability_score' => 'nullable|numeric|min:0|max:100',
            'usability_score' => 'nullable|numeric|min:0|max:100',
            'accessibility_score' => 'nullable|numeric|min:0|max:100',
            'compatibility_score' => 'nullable|numeric|min:0|max:100',
        ];

        // إضافة القواعد المخصصة من التكامل
        if ($integration && isset($integration->validation_rules['order_items'])) {
            $customRules = $integration->validation_rules['order_items'];
            $baseRules = array_merge($baseRules, $customRules);
        }

        return $baseRules;
    }

    /**
     * الحصول على رسائل التحقق للمنتج
     */
    protected function getProductValidationMessages(): array
    {
        return [
            'external_id.required' => 'معرف المنتج الخارجي مطلوب',
            'name.required' => 'اسم المنتج مطلوب',
            'name.max' => 'اسم المنتج يجب ألا يتجاوز 500 حرف',
            'price.numeric' => 'سعر المنتج يجب أن يكون رقماً',
            'price.min' => 'سعر المنتج يجب أن يكون أكبر من أو يساوي صفر',
            'inventory_quantity.integer' => 'كمية المخزون يجب أن تكون رقماً صحيحاً',
            'inventory_quantity.min' => 'كمية المخزون يجب أن تكون أكبر من أو تساوي صفر',
            'weight.numeric' => 'وزن المنتج يجب أن يكون رقماً',
            'weight.min' => 'وزن المنتج يجب أن يكون أكبر من أو يساوي صفر',
            'weight_unit.in' => 'وحدة الوزن يجب أن تكون إحدى القيم: kg, g, lb, oz',
            'featured_image.url' => 'رابط الصورة المميزة يجب أن يكون رابطاً صحيحاً',
            'status.in' => 'حالة المنتج يجب أن تكون إحدى القيم: active, inactive, draft, archived',
            'visibility.in' => 'رؤية المنتج يجب أن تكون إحدى القيم: visible, hidden, catalog, search',
        ];
    }

    /**
     * الحصول على رسائل التحقق للطلب
     */
    protected function getOrderValidationMessages(): array
    {
        return [
            'external_id.required' => 'معرف الطلب الخارجي مطلوب',
            'currency.required' => 'عملة الطلب مطلوبة',
            'currency.size' => 'عملة الطلب يجب أن تكون 3 أحرف',
            'total.required' => 'إجمالي الطلب مطلوب',
            'total.numeric' => 'إجمالي الطلب يجب أن يكون رقماً',
            'total.min' => 'إجمالي الطلب يجب أن يكون أكبر من أو يساوي صفر',
            'customer_ip_address.ip' => 'عنوان IP للعميل يجب أن يكون عنوان IP صحيحاً',
            'billing_address.email.email' => 'البريد الإلكتروني في عنوان الفوترة يجب أن يكون بريداً إلكترونياً صحيحاً',
            'billing_address.country.size' => 'رمز البلد في عنوان الفوترة يجب أن يكون حرفين',
            'shipping_address.country.size' => 'رمز البلد في عنوان الشحن يجب أن يكون حرفين',
            'line_items.*.external_id.required' => 'معرف عنصر الطلب الخارجي مطلوب',
            'line_items.*.name.required' => 'اسم عنصر الطلب مطلوب',
            'line_items.*.quantity.required' => 'كمية عنصر الطلب مطلوبة',
            'line_items.*.quantity.min' => 'كمية عنصر الطلب يجب أن تكون أكبر من صفر',
            'line_items.*.price.required' => 'سعر عنصر الطلب مطلوب',
            'line_items.*.price.min' => 'سعر عنصر الطلب يجب أن يكون أكبر من أو يساوي صفر',
        ];
    }

    /**
     * الحصول على رسائل التحقق للعميل
     */
    protected function getCustomerValidationMessages(): array
    {
        return [
            'external_id.required' => 'معرف العميل الخارجي مطلوب',
            'email.required' => 'البريد الإلكتروني للعميل مطلوب',
            'email.email' => 'البريد الإلكتروني للعميل يجب أن يكون بريداً إلكترونياً صحيحاً',
            'email.max' => 'البريد الإلكتروني للعميل يجب ألا يتجاوز 255 حرف',
            'phone.max' => 'رقم الهاتف يجب ألا يتجاوز 20 حرف',
            'first_name.max' => 'الاسم الأول يجب ألا يتجاوز 255 حرف',
            'last_name.max' => 'الاسم الأخير يجب ألا يتجاوز 255 حرف',
            'avatar_url.url' => 'رابط الصورة الشخصية يجب أن يكون رابطاً صحيحاً',
            'date_of_birth.date' => 'تاريخ الميلاد يجب أن يكون تاريخاً صحيحاً',
            'gender.in' => 'الجنس يجب أن يكون إحدى القيم: male, female, other',
            'currency.size' => 'العملة يجب أن تكون 3 أحرف',
            'country.size' => 'رمز البلد يجب أن يكون حرفين',
            'risk_level.in' => 'مستوى المخاطر يجب أن يكون إحدى القيم: low, medium, high',
            'trust_score.min' => 'نقاط الثقة يجب أن تكون أكبر من أو تساوي صفر',
            'trust_score.max' => 'نقاط الثقة يجب أن تكون أقل من أو تساوي 100',
            'credit_score.min' => 'النقاط الائتمانية يجب أن تكون أكبر من أو تساوي صفر',
            'credit_score.max' => 'النقاط الائتمانية يجب أن تكون أقل من أو تساوي 1000',
        ];
    }

    /**
     * الحصول على رسائل التحقق لعنصر الطلب
     */
    protected function getOrderItemValidationMessages(): array
    {
        return [
            'external_id.required' => 'معرف عنصر الطلب الخارجي مطلوب',
            'name.required' => 'اسم عنصر الطلب مطلوب',
            'name.max' => 'اسم عنصر الطلب يجب ألا يتجاوز 500 حرف',
            'quantity.required' => 'كمية عنصر الطلب مطلوبة',
            'quantity.integer' => 'كمية عنصر الطلب يجب أن تكون رقماً صحيحاً',
            'quantity.min' => 'كمية عنصر الطلب يجب أن تكون أكبر من صفر',
            'price.required' => 'سعر عنصر الطلب مطلوب',
            'price.numeric' => 'سعر عنصر الطلب يجب أن يكون رقماً',
            'price.min' => 'سعر عنصر الطلب يجب أن يكون أكبر من أو يساوي صفر',
            'product_url.url' => 'رابط المنتج يجب أن يكون رابطاً صحيحاً',
            'image_url.url' => 'رابط صورة المنتج يجب أن يكون رابطاً صحيحاً',
            'currency.size' => 'العملة يجب أن تكون 3 أحرف',
            'weight.numeric' => 'الوزن يجب أن يكون رقماً',
            'weight.min' => 'الوزن يجب أن يكون أكبر من أو يساوي صفر',
            'weight_unit.in' => 'وحدة الوزن يجب أن تكون إحدى القيم: kg, g, lb, oz',
            'rating.numeric' => 'التقييم يجب أن يكون رقماً',
            'rating.min' => 'التقييم يجب أن يكون أكبر من أو يساوي صفر',
            'rating.max' => 'التقييم يجب أن يكون أقل من أو يساوي 5',
        ];
    }

    /**
     * التحقق من القواعد المخصصة للمنتج
     */
    protected function validateProductCustomRules(array $data, ECommerceIntegration $integration = null): array
    {
        $errors = [];

        // التحقق من أن سعر البيع أقل من السعر العادي
        if (isset($data['sale_price']) && isset($data['regular_price'])) {
            if ($data['sale_price'] > $data['regular_price']) {
                $errors['sale_price'] = ['سعر البيع يجب أن يكون أقل من السعر العادي'];
            }
        }

        // التحقق من أن سعر المقارنة أكبر من السعر العادي
        if (isset($data['compare_price']) && isset($data['regular_price'])) {
            if ($data['compare_price'] < $data['regular_price']) {
                $errors['compare_price'] = ['سعر المقارنة يجب أن يكون أكبر من السعر العادي'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * التحقق من القواعد المخصصة للطلب
     */
    protected function validateOrderCustomRules(array $data, ECommerceIntegration $integration = null): array
    {
        $errors = [];

        // التحقق من أن إجمالي الطلب يساوي مجموع العناصر
        if (isset($data['total']) && isset($data['line_items'])) {
            $itemsTotal = 0;
            foreach ($data['line_items'] as $item) {
                if (isset($item['total'])) {
                    $itemsTotal += $item['total'];
                } elseif (isset($item['price']) && isset($item['quantity'])) {
                    $itemsTotal += $item['price'] * $item['quantity'];
                }
            }

            $calculatedTotal = $itemsTotal + ($data['shipping_total'] ?? 0) + ($data['total_tax'] ?? 0) - ($data['discount_total'] ?? 0);
            
            if (abs($data['total'] - $calculatedTotal) > 0.01) {
                $errors['total'] = ['إجمالي الطلب لا يتطابق مع مجموع العناصر والرسوم'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * التحقق من القواعد المخصصة للعميل
     */
    protected function validateCustomerCustomRules(array $data, ECommerceIntegration $integration = null): array
    {
        $errors = [];

        // التحقق من أن تاريخ الميلاد منطقي
        if (isset($data['date_of_birth'])) {
            $birthDate = \Carbon\Carbon::parse($data['date_of_birth']);
            $age = $birthDate->diffInYears(now());
            
            if ($age > 120) {
                $errors['date_of_birth'] = ['تاريخ الميلاد غير منطقي'];
            }
            
            if ($birthDate->isFuture()) {
                $errors['date_of_birth'] = ['تاريخ الميلاد لا يمكن أن يكون في المستقبل'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * الحصول على تحذيرات المنتج
     */
    protected function getProductWarnings(array $data, ECommerceIntegration $integration = null): array
    {
        $warnings = [];

        // تحذير إذا لم يكن هناك وصف
        if (empty($data['description'])) {
            $warnings[] = 'المنتج لا يحتوي على وصف';
        }

        // تحذير إذا لم يكن هناك صورة
        if (empty($data['featured_image']) && empty($data['images'])) {
            $warnings[] = 'المنتج لا يحتوي على صور';
        }

        // تحذير إذا كان السعر صفر
        if (isset($data['price']) && $data['price'] == 0) {
            $warnings[] = 'سعر المنتج صفر';
        }

        // تحذير إذا لم يكن هناك SKU
        if (empty($data['sku'])) {
            $warnings[] = 'المنتج لا يحتوي على رمز SKU';
        }

        return $warnings;
    }

    /**
     * الحصول على تحذيرات الطلب
     */
    protected function getOrderWarnings(array $data, ECommerceIntegration $integration = null): array
    {
        $warnings = [];

        // تحذير إذا لم يكن هناك عنوان شحن
        if (empty($data['shipping_address'])) {
            $warnings[] = 'الطلب لا يحتوي على عنوان شحن';
        }

        // تحذير إذا لم يكن هناك عنوان فوترة
        if (empty($data['billing_address'])) {
            $warnings[] = 'الطلب لا يحتوي على عنوان فوترة';
        }

        // تحذير إذا كان الطلب بدون عناصر
        if (empty($data['line_items'])) {
            $warnings[] = 'الطلب لا يحتوي على عناصر';
        }

        return $warnings;
    }

    /**
     * الحصول على تحذيرات العميل
     */
    protected function getCustomerWarnings(array $data, ECommerceIntegration $integration = null): array
    {
        $warnings = [];

        // تحذير إذا لم يكن هناك اسم
        if (empty($data['first_name']) && empty($data['last_name'])) {
            $warnings[] = 'العميل لا يحتوي على اسم';
        }

        // تحذير إذا لم يكن هناك رقم هاتف
        if (empty($data['phone'])) {
            $warnings[] = 'العميل لا يحتوي على رقم هاتف';
        }

        // تحذير إذا لم يتم التحقق من البريد الإلكتروني
        if (isset($data['email_verified']) && !$data['email_verified']) {
            $warnings[] = 'لم يتم التحقق من البريد الإلكتروني للعميل';
        }

        return $warnings;
    }
}
