<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج تقييم الأداء - نظام إدارة الأداء المتقدم
 * يدعم OKR، KPI، تقييم 360 درجة
 */
class PerformanceReview extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'employee_id',
        'reviewer_id',
        'review_period_id',
        'review_type',
        'review_cycle',
        'status',
        'start_date',
        'end_date',
        'due_date',
        'completed_date',
        
        // Overall Scores
        'overall_score',
        'manager_score',
        'self_score',
        'peer_average_score',
        'subordinate_average_score',
        'customer_score',
        
        // Detailed Ratings
        'competency_scores',
        'goal_achievements',
        'kpi_scores',
        'okr_scores',
        
        // Qualitative Assessment
        'strengths',
        'areas_for_improvement',
        'achievements',
        'challenges_faced',
        'manager_comments',
        'employee_comments',
        'hr_comments',
        
        // Development Planning
        'development_goals',
        'training_recommendations',
        'career_aspirations',
        'promotion_readiness',
        'succession_planning_notes',
        
        // 360 Feedback
        'peer_feedback',
        'subordinate_feedback',
        'customer_feedback',
        'feedback_summary',
        
        // Action Items
        'action_items',
        'follow_up_date',
        'next_review_date',
        
        // Calibration
        'calibration_session_id',
        'calibrated_score',
        'calibration_notes',
        
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'due_date' => 'date',
        'completed_date' => 'date',
        'follow_up_date' => 'date',
        'next_review_date' => 'date',
        'overall_score' => 'decimal:2',
        'manager_score' => 'decimal:2',
        'self_score' => 'decimal:2',
        'peer_average_score' => 'decimal:2',
        'subordinate_average_score' => 'decimal:2',
        'customer_score' => 'decimal:2',
        'calibrated_score' => 'decimal:2',
        'competency_scores' => 'array',
        'goal_achievements' => 'array',
        'kpi_scores' => 'array',
        'okr_scores' => 'array',
        'strengths' => 'array',
        'areas_for_improvement' => 'array',
        'achievements' => 'array',
        'challenges_faced' => 'array',
        'development_goals' => 'array',
        'training_recommendations' => 'array',
        'career_aspirations' => 'array',
        'peer_feedback' => 'array',
        'subordinate_feedback' => 'array',
        'customer_feedback' => 'array',
        'action_items' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع التقييم
     */
    const REVIEW_TYPES = [
        'ANNUAL' => 'سنوي',
        'SEMI_ANNUAL' => 'نصف سنوي',
        'QUARTERLY' => 'ربع سنوي',
        'PROBATION' => 'فترة التجربة',
        'PROJECT_BASED' => 'على أساس المشروع',
        'PROMOTION' => 'ترقية',
        'DISCIPLINARY' => 'تأديبي',
        'EXIT' => 'مغادرة',
        'SPOT' => 'فوري',
    ];

    /**
     * دورات التقييم
     */
    const REVIEW_CYCLES = [
        'Q1' => 'الربع الأول',
        'Q2' => 'الربع الثاني',
        'Q3' => 'الربع الثالث',
        'Q4' => 'الربع الرابع',
        'H1' => 'النصف الأول',
        'H2' => 'النصف الثاني',
        'ANNUAL' => 'سنوي',
    ];

    /**
     * حالات التقييم
     */
    const STATUSES = [
        'NOT_STARTED' => 'لم يبدأ',
        'SELF_ASSESSMENT' => 'تقييم ذاتي',
        'MANAGER_REVIEW' => 'مراجعة المدير',
        'PEER_FEEDBACK' => 'تقييم الزملاء',
        'SUBORDINATE_FEEDBACK' => 'تقييم المرؤوسين',
        'CUSTOMER_FEEDBACK' => 'تقييم العملاء',
        'CALIBRATION' => 'معايرة',
        'COMPLETED' => 'مكتمل',
        'OVERDUE' => 'متأخر',
        'CANCELLED' => 'ملغي',
    ];

    /**
     * مقاييس الأداء
     */
    const PERFORMANCE_RATINGS = [
        'EXCEPTIONAL' => ['value' => 5, 'label' => 'استثنائي', 'description' => 'يتجاوز التوقعات بشكل كبير'],
        'EXCEEDS' => ['value' => 4, 'label' => 'يتجاوز التوقعات', 'description' => 'أداء أعلى من المطلوب'],
        'MEETS' => ['value' => 3, 'label' => 'يلبي التوقعات', 'description' => 'أداء مطابق للمعايير'],
        'BELOW' => ['value' => 2, 'label' => 'أقل من التوقعات', 'description' => 'يحتاج تحسين'],
        'UNSATISFACTORY' => ['value' => 1, 'label' => 'غير مرضي', 'description' => 'أداء غير مقبول'],
    ];

    /**
     * الموظف المُقيَّم
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * المُقيِّم (المدير)
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'reviewer_id');
    }

    /**
     * فترة التقييم
     */
    public function reviewPeriod(): BelongsTo
    {
        return $this->belongsTo(ReviewPeriod::class);
    }

    /**
     * الأهداف المرتبطة
     */
    public function goals(): HasMany
    {
        return $this->hasMany(EmployeeGoal::class, 'review_id');
    }

    /**
     * تقييمات الزملاء
     */
    public function peerReviews(): HasMany
    {
        return $this->hasMany(PeerReview::class);
    }

    /**
     * جلسة المعايرة
     */
    public function calibrationSession(): BelongsTo
    {
        return $this->belongsTo(CalibrationSession::class);
    }

    /**
     * بدء التقييم الذاتي
     */
    public function startSelfAssessment(): bool
    {
        if ($this->status !== 'NOT_STARTED') {
            return false;
        }

        $this->update(['status' => 'SELF_ASSESSMENT']);
        
        // إرسال إشعار للموظف
        $this->employee->notify(new \App\Notifications\SelfAssessmentStarted($this));
        
        return true;
    }

    /**
     * إكمال التقييم الذاتي
     */
    public function completeSelfAssessment(array $selfAssessmentData): bool
    {
        if ($this->status !== 'SELF_ASSESSMENT') {
            return false;
        }

        $this->update([
            'self_score' => $selfAssessmentData['overall_score'],
            'employee_comments' => $selfAssessmentData['comments'],
            'career_aspirations' => $selfAssessmentData['career_aspirations'] ?? [],
            'status' => 'MANAGER_REVIEW',
        ]);

        // إرسال إشعار للمدير
        $this->reviewer->notify(new \App\Notifications\ManagerReviewRequired($this));

        return true;
    }

    /**
     * إكمال مراجعة المدير
     */
    public function completeManagerReview(array $managerReviewData): bool
    {
        if ($this->status !== 'MANAGER_REVIEW') {
            return false;
        }

        $this->update([
            'manager_score' => $managerReviewData['overall_score'],
            'competency_scores' => $managerReviewData['competency_scores'],
            'goal_achievements' => $managerReviewData['goal_achievements'],
            'strengths' => $managerReviewData['strengths'],
            'areas_for_improvement' => $managerReviewData['areas_for_improvement'],
            'manager_comments' => $managerReviewData['comments'],
            'development_goals' => $managerReviewData['development_goals'],
            'training_recommendations' => $managerReviewData['training_recommendations'],
            'status' => $this->requiresPeerFeedback() ? 'PEER_FEEDBACK' : 'COMPLETED',
        ]);

        if ($this->requiresPeerFeedback()) {
            $this->initiatePeerFeedback();
        } else {
            $this->calculateFinalScore();
        }

        return true;
    }

    /**
     * التحقق من الحاجة لتقييم الزملاء
     */
    protected function requiresPeerFeedback(): bool
    {
        // تحديد ما إذا كان الموظف يحتاج تقييم 360 درجة
        return $this->employee->position->is_management || 
               $this->review_type === 'ANNUAL' ||
               $this->employee->position->level === 'SENIOR';
    }

    /**
     * بدء تقييم الزملاء
     */
    protected function initiatePeerFeedback(): void
    {
        // الحصول على قائمة الزملاء للتقييم
        $peers = $this->getPeersForFeedback();
        
        foreach ($peers as $peer) {
            PeerReview::create([
                'performance_review_id' => $this->id,
                'reviewer_id' => $peer->id,
                'reviewee_id' => $this->employee_id,
                'status' => 'PENDING',
                'due_date' => now()->addDays(7),
            ]);

            // إرسال إشعار للزميل
            $peer->notify(new \App\Notifications\PeerReviewRequest($this));
        }
    }

    /**
     * الحصول على الزملاء للتقييم
     */
    protected function getPeersForFeedback(): \Illuminate\Database\Eloquent\Collection
    {
        return Employee::where('department_id', $this->employee->department_id)
            ->where('id', '!=', $this->employee_id)
            ->where('id', '!=', $this->reviewer_id)
            ->active()
            ->limit(5) // حد أقصى 5 زملاء
            ->get();
    }

    /**
     * حساب النقاط النهائية
     */
    public function calculateFinalScore(): void
    {
        $weights = $this->getScoreWeights();
        $totalScore = 0;
        $totalWeight = 0;

        // نقاط المدير
        if ($this->manager_score && isset($weights['manager'])) {
            $totalScore += $this->manager_score * $weights['manager'];
            $totalWeight += $weights['manager'];
        }

        // نقاط التقييم الذاتي
        if ($this->self_score && isset($weights['self'])) {
            $totalScore += $this->self_score * $weights['self'];
            $totalWeight += $weights['self'];
        }

        // نقاط الزملاء
        if ($this->peer_average_score && isset($weights['peer'])) {
            $totalScore += $this->peer_average_score * $weights['peer'];
            $totalWeight += $weights['peer'];
        }

        // نقاط المرؤوسين
        if ($this->subordinate_average_score && isset($weights['subordinate'])) {
            $totalScore += $this->subordinate_average_score * $weights['subordinate'];
            $totalWeight += $weights['subordinate'];
        }

        // نقاط العملاء
        if ($this->customer_score && isset($weights['customer'])) {
            $totalScore += $this->customer_score * $weights['customer'];
            $totalWeight += $weights['customer'];
        }

        $this->overall_score = $totalWeight > 0 ? $totalScore / $totalWeight : 0;
        $this->save();
    }

    /**
     * الحصول على أوزان النقاط
     */
    protected function getScoreWeights(): array
    {
        // يمكن تخصيص الأوزان حسب المنصب ونوع التقييم
        return [
            'manager' => 0.6,      // 60% للمدير
            'self' => 0.1,         // 10% للتقييم الذاتي
            'peer' => 0.2,         // 20% للزملاء
            'subordinate' => 0.1,  // 10% للمرؤوسين
            'customer' => 0.0,     // 0% للعملاء (حسب الحاجة)
        ];
    }

    /**
     * إكمال التقييم
     */
    public function complete(): bool
    {
        if ($this->status === 'COMPLETED') {
            return false;
        }

        $this->update([
            'status' => 'COMPLETED',
            'completed_date' => now(),
            'next_review_date' => $this->calculateNextReviewDate(),
        ]);

        // إرسال إشعار للموظف
        $this->employee->notify(new \App\Notifications\PerformanceReviewCompleted($this));

        // تحديث سجل الأداء في ملف الموظف
        $this->updateEmployeePerformanceRecord();

        return true;
    }

    /**
     * حساب تاريخ التقييم التالي
     */
    protected function calculateNextReviewDate(): \Carbon\Carbon
    {
        return match ($this->review_type) {
            'QUARTERLY' => $this->end_date->addMonths(3),
            'SEMI_ANNUAL' => $this->end_date->addMonths(6),
            'ANNUAL' => $this->end_date->addYear(),
            default => $this->end_date->addYear(),
        };
    }

    /**
     * تحديث سجل أداء الموظف
     */
    protected function updateEmployeePerformanceRecord(): void
    {
        $performanceHistory = $this->employee->metadata['performance_history'] ?? [];
        
        $performanceHistory[] = [
            'review_id' => $this->id,
            'review_type' => $this->review_type,
            'review_period' => $this->review_cycle,
            'overall_score' => $this->overall_score,
            'completed_date' => $this->completed_date,
            'promotion_readiness' => $this->promotion_readiness,
        ];

        $this->employee->update([
            'metadata' => array_merge($this->employee->metadata ?? [], [
                'performance_history' => $performanceHistory,
                'last_review_score' => $this->overall_score,
                'last_review_date' => $this->completed_date,
            ])
        ]);
    }

    /**
     * إنشاء خطة التطوير
     */
    public function createDevelopmentPlan(): array
    {
        $plan = [];

        // تحليل نقاط الضعف
        foreach ($this->areas_for_improvement as $area) {
            $plan[] = [
                'area' => $area,
                'recommended_actions' => $this->getRecommendedActions($area),
                'timeline' => '3-6 months',
                'priority' => 'HIGH',
            ];
        }

        // إضافة التدريبات المقترحة
        foreach ($this->training_recommendations as $training) {
            $plan[] = [
                'type' => 'TRAINING',
                'description' => $training,
                'timeline' => '1-3 months',
                'priority' => 'MEDIUM',
            ];
        }

        return $plan;
    }

    /**
     * الحصول على الإجراءات المقترحة
     */
    protected function getRecommendedActions(string $area): array
    {
        $actionMap = [
            'communication' => ['حضور دورة تدريبية في التواصل', 'ممارسة العروض التقديمية'],
            'leadership' => ['برنامج تطوير القيادة', 'توجيه فريق صغير'],
            'technical_skills' => ['دورات تقنية متخصصة', 'مشاريع تطبيقية'],
            'time_management' => ['استخدام أدوات إدارة الوقت', 'تحديد الأولويات'],
        ];

        return $actionMap[strtolower($area)] ?? ['خطة تطوير مخصصة'];
    }

    /**
     * الحصول على تقرير الأداء
     */
    public function getPerformanceReport(): array
    {
        return [
            'employee_info' => [
                'name' => $this->employee->full_name,
                'position' => $this->employee->position->title,
                'department' => $this->employee->department->name,
            ],
            'review_details' => [
                'type' => $this->review_type,
                'period' => $this->review_cycle,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
            ],
            'scores' => [
                'overall' => $this->overall_score,
                'manager' => $this->manager_score,
                'self' => $this->self_score,
                'peer_average' => $this->peer_average_score,
                'subordinate_average' => $this->subordinate_average_score,
            ],
            'competencies' => $this->competency_scores,
            'goals' => $this->goal_achievements,
            'feedback' => [
                'strengths' => $this->strengths,
                'areas_for_improvement' => $this->areas_for_improvement,
                'manager_comments' => $this->manager_comments,
                'employee_comments' => $this->employee_comments,
            ],
            'development' => [
                'goals' => $this->development_goals,
                'training_recommendations' => $this->training_recommendations,
                'career_aspirations' => $this->career_aspirations,
            ],
        ];
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * نطاق للتقييمات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
    }

    /**
     * نطاق حسب نوع التقييم
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('review_type', $type);
    }

    /**
     * نطاق للفترة
     */
    public function scopeForPeriod($query, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate]);
    }

    /**
     * نطاق للموظف
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * نطاق للمدير
     */
    public function scopeForReviewer($query, $reviewerId)
    {
        return $query->where('reviewer_id', $reviewerId);
    }

    /**
     * نطاق حسب النقاط
     */
    public function scopeByScoreRange($query, float $minScore, float $maxScore = null)
    {
        $query->where('overall_score', '>=', $minScore);
        
        if ($maxScore) {
            $query->where('overall_score', '<=', $maxScore);
        }
        
        return $query;
    }
}
