<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Domains\HR\Models\TrainingProgram;

/**
 * إشعار دعوة التدريب
 */
class TrainingInvitation extends Notification implements ShouldQueue
{
    use Queueable;

    protected TrainingProgram $trainingProgram;

    /**
     * Create a new notification instance.
     */
    public function __construct(TrainingProgram $trainingProgram)
    {
        $this->trainingProgram = $trainingProgram;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('دعوة للمشاركة في برنامج تدريبي')
            ->greeting("مرحباً {$notifiable->name}")
            ->line("تم دعوتك للمشاركة في البرنامج التدريبي: {$this->trainingProgram->title}")
            ->line("وصف البرنامج: {$this->trainingProgram->description}")
            ->line("تاريخ البداية: {$this->trainingProgram->start_date->format('Y-m-d')}")
            ->line("تاريخ النهاية: {$this->trainingProgram->end_date->format('Y-m-d')}")
            ->line("المدة: {$this->trainingProgram->duration_hours} ساعة")
            ->line("المدرب: " . ($this->trainingProgram->instructor->name ?? 'غير محدد'))
            ->action('عرض تفاصيل البرنامج', route('training.programs.show', $this->trainingProgram->id))
            ->line('يرجى تأكيد مشاركتك في أقرب وقت ممكن.')
            ->salutation('مع أطيب التحيات، فريق الموارد البشرية');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'training_invitation',
            'training_program_id' => $this->trainingProgram->id,
            'training_program_title' => $this->trainingProgram->title,
            'start_date' => $this->trainingProgram->start_date->toDateString(),
            'end_date' => $this->trainingProgram->end_date->toDateString(),
            'duration_hours' => $this->trainingProgram->duration_hours,
            'instructor_name' => $this->trainingProgram->instructor->name ?? null,
            'message' => "تم دعوتك للمشاركة في البرنامج التدريبي: {$this->trainingProgram->title}",
            'action_url' => route('training.programs.show', $this->trainingProgram->id),
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'training_invitation';
    }
}
