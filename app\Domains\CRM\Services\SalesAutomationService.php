<?php

namespace App\Domains\CRM\Services;

use App\Domains\CRM\Models\Opportunity;
use App\Domains\CRM\Models\Customer;
use App\Domains\CRM\Models\CustomerInteraction;
use App\Domains\CRM\Models\CustomerTask;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة أتمتة المبيعات - Sales Automation Service
 * أتمتة دورة المبيعات والمهام والمتابعة
 */
class SalesAutomationService
{
    /**
     * تنفيذ الأتمتة الذكية للمبيعات
     */
    public function executeSmartAutomation(array $criteria = []): array
    {
        $results = [];

        // أتمتة متابعة الفرص
        $results['opportunity_follow_ups'] = $this->automateOpportunityFollowUps();

        // أتمتة تحديث مراحل الفرص
        $results['stage_updates'] = $this->automateStageUpdates();

        // أتمتة إنشاء المهام
        $results['task_creation'] = $this->automateTaskCreation();

        // أتمتة التذكيرات
        $results['reminders'] = $this->automateReminders();

        // أتمتة التصعيد
        $results['escalations'] = $this->automateEscalations();

        // أتمتة توزيع العملاء المحتملين
        $results['lead_distribution'] = $this->automateLeadDistribution();

        // أتمتة تحديث النقاط والاحتمالية
        $results['scoring_updates'] = $this->automateScoringUpdates();

        return $results;
    }

    /**
     * أتمتة متابعة الفرص التجارية
     */
    public function automateOpportunityFollowUps(): array
    {
        $opportunities = Opportunity::open()
            ->where('next_action_date', '<=', now())
            ->orWhere('last_activity_at', '<', now()->subDays(7))
            ->with(['customer', 'assignedSalesRep'])
            ->get();

        $followUps = [];

        foreach ($opportunities as $opportunity) {
            $followUp = $this->createAutomaticFollowUp($opportunity);
            if ($followUp) {
                $followUps[] = $followUp;
            }
        }

        return [
            'total_opportunities' => $opportunities->count(),
            'follow_ups_created' => count($followUps),
            'follow_ups' => $followUps,
        ];
    }

    /**
     * أتمتة تحديث مراحل الفرص
     */
    public function automateStageUpdates(): array
    {
        $updates = [];

        // الفرص التي تحتاج تحديث تلقائي للمرحلة
        $opportunities = Opportunity::open()
            ->where('days_in_stage', '>', 30)
            ->with(['activities', 'customer'])
            ->get();

        foreach ($opportunities as $opportunity) {
            $suggestedStage = $this->suggestNextStage($opportunity);
            
            if ($suggestedStage && $suggestedStage !== $opportunity->stage) {
                $update = $this->createStageUpdateSuggestion($opportunity, $suggestedStage);
                $updates[] = $update;
            }
        }

        return [
            'opportunities_reviewed' => $opportunities->count(),
            'stage_updates_suggested' => count($updates),
            'updates' => $updates,
        ];
    }

    /**
     * أتمتة إنشاء المهام
     */
    public function automateTaskCreation(): array
    {
        $tasks = [];

        // إنشاء مهام للعملاء الجدد
        $tasks = array_merge($tasks, $this->createNewCustomerTasks());

        // إنشاء مهام للفرص الجديدة
        $tasks = array_merge($tasks, $this->createNewOpportunityTasks());

        // إنشاء مهام المتابعة
        $tasks = array_merge($tasks, $this->createFollowUpTasks());

        // إنشاء مهام التجديد
        $tasks = array_merge($tasks, $this->createRenewalTasks());

        return [
            'total_tasks_created' => count($tasks),
            'tasks' => $tasks,
        ];
    }

    /**
     * أتمتة التذكيرات
     */
    public function automateReminders(): array
    {
        $reminders = [];

        // تذكيرات المواعيد
        $reminders = array_merge($reminders, $this->createAppointmentReminders());

        // تذكيرات المهام المتأخرة
        $reminders = array_merge($reminders, $this->createOverdueTaskReminders());

        // تذكيرات الفرص المتأخرة
        $reminders = array_merge($reminders, $this->createOverdueOpportunityReminders());

        // تذكيرات المتابعة
        $reminders = array_merge($reminders, $this->createFollowUpReminders());

        return [
            'total_reminders' => count($reminders),
            'reminders' => $reminders,
        ];
    }

    /**
     * أتمتة التصعيد
     */
    public function automateEscalations(): array
    {
        $escalations = [];

        // تصعيد الفرص المتأخرة
        $overdueOpportunities = Opportunity::overdue()
            ->where('value', '>=', 50000)
            ->with(['assignedSalesRep', 'customer'])
            ->get();

        foreach ($overdueOpportunities as $opportunity) {
            $escalation = $this->createEscalation($opportunity, 'overdue_high_value');
            $escalations[] = $escalation;
        }

        // تصعيد العملاء المعرضين للخطر
        $atRiskCustomers = Customer::notContactedSince(60)
            ->where('tier', 'vip')
            ->get();

        foreach ($atRiskCustomers as $customer) {
            $escalation = $this->createCustomerEscalation($customer, 'at_risk_vip');
            $escalations[] = $escalation;
        }

        return [
            'total_escalations' => count($escalations),
            'escalations' => $escalations,
        ];
    }

    /**
     * أتمتة توزيع العملاء المحتملين
     */
    public function automateLeadDistribution(): array
    {
        $newLeads = Customer::where('status', 'lead')
            ->whereNull('assigned_to')
            ->with(['industry'])
            ->get();

        $distributions = [];

        foreach ($newLeads as $lead) {
            $assignedRep = $this->findBestSalesRep($lead);
            
            if ($assignedRep) {
                $lead->update(['assigned_to' => $assignedRep->id]);
                
                // إنشاء مهمة متابعة
                $this->createLeadFollowUpTask($lead, $assignedRep);
                
                $distributions[] = [
                    'lead_id' => $lead->id,
                    'lead_name' => $lead->full_name,
                    'assigned_to' => $assignedRep->name,
                    'reason' => $this->getAssignmentReason($lead, $assignedRep),
                ];
            }
        }

        return [
            'total_leads' => $newLeads->count(),
            'distributed_leads' => count($distributions),
            'distributions' => $distributions,
        ];
    }

    /**
     * أتمتة تحديث النقاط والاحتمالية
     */
    public function automateScoringUpdates(): array
    {
        $opportunities = Opportunity::open()
            ->where('updated_at', '<', now()->subHours(24))
            ->get();

        $updates = [];

        foreach ($opportunities as $opportunity) {
            $oldScore = $opportunity->score;
            $oldProbability = $opportunity->probability;

            $opportunity->updateScore();

            if ($opportunity->score !== $oldScore || $opportunity->probability !== $oldProbability) {
                $updates[] = [
                    'opportunity_id' => $opportunity->id,
                    'opportunity_title' => $opportunity->title,
                    'old_score' => $oldScore,
                    'new_score' => $opportunity->score,
                    'old_probability' => $oldProbability,
                    'new_probability' => $opportunity->probability,
                ];
            }
        }

        return [
            'opportunities_updated' => count($updates),
            'updates' => $updates,
        ];
    }

    /**
     * إنشاء متابعة تلقائية للفرصة
     */
    protected function createAutomaticFollowUp(Opportunity $opportunity): ?array
    {
        $daysSinceLastActivity = $opportunity->last_activity_at ? 
            $opportunity->last_activity_at->diffInDays(now()) : 
            $opportunity->created_at->diffInDays(now());

        if ($daysSinceLastActivity >= 7) {
            $followUpType = $this->determineFollowUpType($opportunity);
            
            $interaction = CustomerInteraction::create([
                'customer_id' => $opportunity->customer_id,
                'opportunity_id' => $opportunity->id,
                'user_id' => $opportunity->assigned_to,
                'type' => $followUpType,
                'direction' => 'outbound',
                'subject' => "متابعة تلقائية - {$opportunity->title}",
                'description' => $this->generateFollowUpMessage($opportunity),
                'status' => 'scheduled',
                'scheduled_at' => now()->addHours(2),
                'priority' => $this->determineFollowUpPriority($opportunity),
            ]);

            return [
                'opportunity_id' => $opportunity->id,
                'interaction_id' => $interaction->id,
                'type' => $followUpType,
                'scheduled_at' => $interaction->scheduled_at,
            ];
        }

        return null;
    }

    /**
     * اقتراح المرحلة التالية
     */
    protected function suggestNextStage(Opportunity $opportunity): ?string
    {
        $currentStage = $opportunity->stage;
        $daysInStage = $opportunity->days_in_current_stage;
        $recentActivities = $opportunity->activities()
                                      ->where('occurred_at', '>=', now()->subDays(14))
                                      ->get();

        // منطق اقتراح المرحلة بناءً على الأنشطة والوقت
        switch ($currentStage) {
            case 'lead':
                if ($recentActivities->where('type', 'call')->count() >= 2) {
                    return 'qualified';
                }
                break;

            case 'qualified':
                if ($recentActivities->where('type', 'meeting')->count() >= 1) {
                    return 'proposal';
                }
                break;

            case 'proposal':
                if ($recentActivities->where('type', 'proposal')->count() >= 1 && $daysInStage >= 14) {
                    return 'negotiation';
                }
                break;

            case 'negotiation':
                if ($recentActivities->where('outcome', 'successful')->count() >= 2) {
                    return 'contract';
                }
                break;
        }

        return null;
    }

    /**
     * إنشاء مهام للعملاء الجدد
     */
    protected function createNewCustomerTasks(): array
    {
        $newCustomers = Customer::where('created_at', '>=', now()->subDays(1))
            ->whereDoesntHave('tasks')
            ->get();

        $tasks = [];

        foreach ($newCustomers as $customer) {
            $task = CustomerTask::create([
                'customer_id' => $customer->id,
                'assigned_to' => $customer->assigned_to,
                'title' => 'مرحباً بالعميل الجديد',
                'description' => 'الاتصال بالعميل الجديد والترحيب به',
                'type' => 'welcome_call',
                'priority' => 'high',
                'due_date' => now()->addDays(1),
                'status' => 'pending',
            ]);

            $tasks[] = $task;
        }

        return $tasks;
    }

    /**
     * إنشاء مهام للفرص الجديدة
     */
    protected function createNewOpportunityTasks(): array
    {
        $newOpportunities = Opportunity::where('created_at', '>=', now()->subDays(1))
            ->whereDoesntHave('tasks')
            ->get();

        $tasks = [];

        foreach ($newOpportunities as $opportunity) {
            $task = OpportunityTask::create([
                'opportunity_id' => $opportunity->id,
                'assigned_to' => $opportunity->assigned_to,
                'title' => 'تأهيل الفرصة الجديدة',
                'description' => 'تأهيل الفرصة وتحديد الاحتياجات',
                'type' => 'qualification',
                'priority' => 'medium',
                'due_date' => now()->addDays(2),
                'status' => 'pending',
            ]);

            $tasks[] = $task;
        }

        return $tasks;
    }

    /**
     * العثور على أفضل مندوب مبيعات
     */
    protected function findBestSalesRep(Customer $lead): ?\App\Domains\HR\Models\Employee
    {
        $salesReps = \App\Domains\HR\Models\Employee::where('department', 'sales')
            ->where('is_active', true)
            ->get();

        $bestRep = null;
        $bestScore = 0;

        foreach ($salesReps as $rep) {
            $score = $this->calculateAssignmentScore($lead, $rep);
            
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestRep = $rep;
            }
        }

        return $bestRep;
    }

    /**
     * حساب نقاط التعيين
     */
    protected function calculateAssignmentScore(Customer $lead, \App\Domains\HR\Models\Employee $rep): float
    {
        $score = 0;

        // نقاط بناءً على عبء العمل
        $currentLoad = Customer::where('assigned_to', $rep->id)->count();
        $maxLoad = $rep->max_customers ?? 50;
        $loadScore = max(0, (1 - ($currentLoad / $maxLoad)) * 40);
        $score += $loadScore;

        // نقاط بناءً على التخصص الصناعي
        if ($lead->industry_id && in_array($lead->industry_id, $rep->specializations ?? [])) {
            $score += 30;
        }

        // نقاط بناءً على الموقع الجغرافي
        if ($lead->country_code === $rep->country_code) {
            $score += 20;
        }

        // نقاط بناءً على اللغة
        if (in_array($lead->language, $rep->languages ?? [])) {
            $score += 10;
        }

        return $score;
    }

    /**
     * تحديد نوع المتابعة
     */
    protected function determineFollowUpType(Opportunity $opportunity): string
    {
        $lastActivity = $opportunity->last_activity;
        
        if (!$lastActivity) {
            return 'call';
        }

        return match ($lastActivity->type) {
            'email' => 'call',
            'call' => 'email',
            'meeting' => 'call',
            default => 'email',
        };
    }

    /**
     * توليد رسالة المتابعة
     */
    protected function generateFollowUpMessage(Opportunity $opportunity): string
    {
        $templates = [
            'call' => "مرحباً {customer_name}، أردت المتابعة معك بخصوص {opportunity_title}. هل يمكننا تحديد موعد للمناقشة؟",
            'email' => "عزيزي {customer_name}، أتابع معك بخصوص {opportunity_title}. أرجو التواصل معي لمناقشة التفاصيل.",
        ];

        $template = $templates[$this->determineFollowUpType($opportunity)] ?? $templates['email'];

        return str_replace(
            ['{customer_name}', '{opportunity_title}'],
            [$opportunity->customer->full_name, $opportunity->title],
            $template
        );
    }

    /**
     * تحديد أولوية المتابعة
     */
    protected function determineFollowUpPriority(Opportunity $opportunity): string
    {
        if ($opportunity->value >= 100000) return 'urgent';
        if ($opportunity->value >= 50000) return 'high';
        if ($opportunity->temperature === 'hot') return 'high';
        return 'medium';
    }

    // دوال مساعدة إضافية
    protected function createStageUpdateSuggestion(Opportunity $opportunity, string $suggestedStage): array { return []; }
    protected function createFollowUpTasks(): array { return []; }
    protected function createRenewalTasks(): array { return []; }
    protected function createAppointmentReminders(): array { return []; }
    protected function createOverdueTaskReminders(): array { return []; }
    protected function createOverdueOpportunityReminders(): array { return []; }
    protected function createFollowUpReminders(): array { return []; }
    protected function createEscalation(Opportunity $opportunity, string $reason): array { return []; }
    protected function createCustomerEscalation(Customer $customer, string $reason): array { return []; }
    protected function createLeadFollowUpTask(Customer $lead, \App\Domains\HR\Models\Employee $rep): void { }
    protected function getAssignmentReason(Customer $lead, \App\Domains\HR\Models\Employee $rep): string { return ''; }
}
