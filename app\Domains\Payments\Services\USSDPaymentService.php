<?php

namespace App\Domains\Payments\Services;

use App\Domains\Payments\Models\PaymentTransaction;
use App\Domains\Payments\Models\DigitalWallet;
use App\Domains\Payments\Models\USSDSession;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * خدمة مدفوعات USSD و SMS
 * تدير المدفوعات عبر الهاتف للمناطق ذات الإنترنت الضعيف
 */
class USSDPaymentService
{
    /**
     * قوائم USSD المدعومة
     */
    const USSD_MENUS = [
        'main' => [
            'title' => 'مرحباً بك في Hesabiai',
            'options' => [
                '1' => 'رصيد المحفظة',
                '2' => 'شحن المحفظة',
                '3' => 'تحويل أموال',
                '4' => 'دفع فاتورة',
                '5' => 'تاريخ المعاملات',
                '0' => 'خروج',
            ],
        ],
        'balance' => [
            'title' => 'رصيد المحفظة',
            'options' => [
                '1' => 'عرض الرصيد',
                '2' => 'تفاصيل المحفظة',
                '9' => 'القائمة الرئيسية',
                '0' => 'خروج',
            ],
        ],
        'topup' => [
            'title' => 'شحن المحفظة',
            'options' => [
                '1' => 'شحن 10 ريال',
                '2' => 'شحن 25 ريال',
                '3' => 'شحن 50 ريال',
                '4' => 'شحن 100 ريال',
                '5' => 'مبلغ مخصص',
                '9' => 'القائمة الرئيسية',
                '0' => 'خروج',
            ],
        ],
        'transfer' => [
            'title' => 'تحويل أموال',
            'options' => [
                '1' => 'تحويل برقم الهاتف',
                '2' => 'تحويل برقم المحفظة',
                '3' => 'تحويل سريع',
                '9' => 'القائمة الرئيسية',
                '0' => 'خروج',
            ],
        ],
    ];

    /**
     * رموز USSD للدول المختلفة
     */
    const USSD_CODES = [
        'SA' => '*123#', // السعودية
        'AE' => '*124#', // الإمارات
        'EG' => '*125#', // مصر
        'MA' => '*126#', // المغرب
        'KW' => '*127#', // الكويت
        'QA' => '*128#', // قطر
    ];

    /**
     * معالجة طلب USSD
     */
    public function handleUSSDRequest(string $phoneNumber, string $input, string $sessionId = null): array
    {
        try {
            // الحصول على أو إنشاء جلسة USSD
            $session = $this->getOrCreateSession($phoneNumber, $sessionId);
            
            // معالجة الإدخال
            $response = $this->processUSSDInput($session, $input);
            
            // تحديث الجلسة
            $this->updateSession($session, $input, $response);
            
            return [
                'success' => true,
                'response' => $response,
                'session_id' => $session->session_id,
                'continue' => $response['continue'] ?? false,
            ];
            
        } catch (\Exception $e) {
            Log::error('فشل في معالجة طلب USSD', [
                'phone' => $phoneNumber,
                'input' => $input,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'response' => [
                    'message' => 'حدث خطأ. يرجى المحاولة مرة أخرى.',
                    'continue' => false,
                ],
            ];
        }
    }

    /**
     * معالجة إدخال USSD
     */
    protected function processUSSDInput(USSDSession $session, string $input): array
    {
        $currentMenu = $session->current_menu ?? 'main';
        $step = $session->step ?? 0;
        
        // إذا كان الإدخال فارغ، عرض القائمة الحالية
        if (empty($input) || $input === '*123#') {
            return $this->showMenu('main', $session);
        }
        
        // معالجة الإدخال حسب القائمة الحالية
        return match ($currentMenu) {
            'main' => $this->handleMainMenu($session, $input),
            'balance' => $this->handleBalanceMenu($session, $input),
            'topup' => $this->handleTopupMenu($session, $input),
            'transfer' => $this->handleTransferMenu($session, $input),
            'custom_amount' => $this->handleCustomAmount($session, $input),
            'transfer_phone' => $this->handleTransferPhone($session, $input),
            'transfer_amount' => $this->handleTransferAmount($session, $input),
            'confirm_transaction' => $this->handleTransactionConfirmation($session, $input),
            default => $this->showMenu('main', $session),
        };
    }

    /**
     * معالجة القائمة الرئيسية
     */
    protected function handleMainMenu(USSDSession $session, string $input): array
    {
        return match ($input) {
            '1' => $this->showMenu('balance', $session),
            '2' => $this->showMenu('topup', $session),
            '3' => $this->showMenu('transfer', $session),
            '4' => $this->handleBillPayment($session),
            '5' => $this->showTransactionHistory($session),
            '0' => $this->endSession($session),
            default => $this->showInvalidOption($session, 'main'),
        };
    }

    /**
     * معالجة قائمة الرصيد
     */
    protected function handleBalanceMenu(USSDSession $session, string $input): array
    {
        return match ($input) {
            '1' => $this->showBalance($session),
            '2' => $this->showWalletDetails($session),
            '9' => $this->showMenu('main', $session),
            '0' => $this->endSession($session),
            default => $this->showInvalidOption($session, 'balance'),
        };
    }

    /**
     * معالجة قائمة الشحن
     */
    protected function handleTopupMenu(USSDSession $session, string $input): array
    {
        $amounts = ['1' => 10, '2' => 25, '3' => 50, '4' => 100];
        
        if (isset($amounts[$input])) {
            return $this->processTopup($session, $amounts[$input]);
        }
        
        return match ($input) {
            '5' => $this->requestCustomAmount($session),
            '9' => $this->showMenu('main', $session),
            '0' => $this->endSession($session),
            default => $this->showInvalidOption($session, 'topup'),
        };
    }

    /**
     * عرض القائمة
     */
    protected function showMenu(string $menuName, USSDSession $session): array
    {
        $menu = self::USSD_MENUS[$menuName] ?? self::USSD_MENUS['main'];
        
        $message = $menu['title'] . "\n\n";
        foreach ($menu['options'] as $key => $option) {
            $message .= "{$key}. {$option}\n";
        }
        
        $session->update(['current_menu' => $menuName, 'step' => 0]);
        
        return [
            'message' => $message,
            'continue' => true,
        ];
    }

    /**
     * عرض الرصيد
     */
    protected function showBalance(USSDSession $session): array
    {
        $user = $session->user;
        if (!$user) {
            return $this->requestRegistration($session);
        }
        
        $wallet = $user->digitalWallets()->primary()->first();
        if (!$wallet) {
            return [
                'message' => "لا توجد محفظة مسجلة.\n\n9. القائمة الرئيسية\n0. خروج",
                'continue' => true,
            ];
        }
        
        $balance = number_format($wallet->available_balance, 2);
        $currency = $wallet->currency;
        
        return [
            'message' => "رصيدك الحالي:\n{$balance} {$currency}\n\nالرصيد المتاح: {$balance} {$currency}\n\n9. القائمة الرئيسية\n0. خروج",
            'continue' => true,
        ];
    }

    /**
     * معالجة الشحن
     */
    protected function processTopup(USSDSession $session, float $amount): array
    {
        $user = $session->user;
        if (!$user) {
            return $this->requestRegistration($session);
        }
        
        // حفظ مبلغ الشحن في الجلسة
        $session->update([
            'current_menu' => 'confirm_transaction',
            'transaction_data' => [
                'type' => 'topup',
                'amount' => $amount,
                'currency' => 'SAR',
            ],
        ]);
        
        return [
            'message' => "تأكيد شحن المحفظة:\nالمبلغ: {$amount} ريال\nرسوم الخدمة: 1 ريال\nالإجمالي: " . ($amount + 1) . " ريال\n\n1. تأكيد\n2. إلغاء\n9. القائمة الرئيسية",
            'continue' => true,
        ];
    }

    /**
     * معالجة تأكيد المعاملة
     */
    protected function handleTransactionConfirmation(USSDSession $session, string $input): array
    {
        if ($input === '1') {
            return $this->executeTransaction($session);
        } elseif ($input === '2') {
            return $this->showMenu('main', $session);
        } elseif ($input === '9') {
            return $this->showMenu('main', $session);
        }
        
        return [
            'message' => "خيار غير صحيح.\n\n1. تأكيد\n2. إلغاء\n9. القائمة الرئيسية",
            'continue' => true,
        ];
    }

    /**
     * تنفيذ المعاملة
     */
    protected function executeTransaction(USSDSession $session): array
    {
        $transactionData = $session->transaction_data;
        $user = $session->user;
        
        try {
            switch ($transactionData['type']) {
                case 'topup':
                    return $this->executeTopup($user, $transactionData);
                case 'transfer':
                    return $this->executeTransfer($user, $transactionData);
                default:
                    throw new \Exception('نوع معاملة غير مدعوم');
            }
        } catch (\Exception $e) {
            return [
                'message' => "فشل في تنفيذ المعاملة:\n{$e->getMessage()}\n\n9. القائمة الرئيسية\n0. خروج",
                'continue' => true,
            ];
        }
    }

    /**
     * تنفيذ الشحن
     */
    protected function executeTopup(User $user, array $transactionData): array
    {
        // في التطبيق الحقيقي، سيتم الشحن عبر بوابة دفع
        // هنا نحاكي العملية
        
        $wallet = $user->digitalWallets()->primary()->first();
        if (!$wallet) {
            throw new \Exception('لا توجد محفظة مسجلة');
        }
        
        $amount = $transactionData['amount'];
        
        // إضافة الرصيد (محاكاة)
        $wallet->addBalance($amount, 'شحن عبر USSD', [
            'method' => 'ussd',
            'session_id' => session()->getId(),
        ]);
        
        $newBalance = number_format($wallet->fresh()->available_balance, 2);
        
        return [
            'message' => "تم شحن المحفظة بنجاح!\nالمبلغ المضاف: {$amount} ريال\nرصيدك الجديد: {$newBalance} ريال\n\nشكراً لاستخدام Hesabiai",
            'continue' => false,
        ];
    }

    /**
     * معالجة دفع SMS
     */
    public function handleSMSPayment(string $phoneNumber, string $message): array
    {
        try {
            // تحليل رسالة SMS
            $command = $this->parseSMSCommand($message);
            
            if (!$command) {
                return $this->sendSMSHelp($phoneNumber);
            }
            
            // العثور على المستخدم
            $user = User::where('phone', $phoneNumber)->first();
            if (!$user) {
                return $this->sendSMSRegistrationPrompt($phoneNumber);
            }
            
            // تنفيذ الأمر
            return $this->executeSMSCommand($user, $command);
            
        } catch (\Exception $e) {
            Log::error('فشل في معالجة دفع SMS', [
                'phone' => $phoneNumber,
                'message' => $message,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'response' => 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * تحليل أمر SMS
     */
    protected function parseSMSCommand(string $message): ?array
    {
        $message = trim(strtoupper($message));
        
        // أوامر الرصيد
        if (preg_match('/^(BALANCE|رصيد)$/', $message)) {
            return ['type' => 'balance'];
        }
        
        // أوامر الشحن
        if (preg_match('/^(TOPUP|شحن)\s+(\d+)$/', $message, $matches)) {
            return [
                'type' => 'topup',
                'amount' => (float) $matches[2],
            ];
        }
        
        // أوامر التحويل
        if (preg_match('/^(TRANSFER|تحويل)\s+(\d+)\s+(\d+)$/', $message, $matches)) {
            return [
                'type' => 'transfer',
                'phone' => $matches[2],
                'amount' => (float) $matches[3],
            ];
        }
        
        // أوامر المساعدة
        if (preg_match('/^(HELP|مساعدة)$/', $message)) {
            return ['type' => 'help'];
        }
        
        return null;
    }

    /**
     * تنفيذ أمر SMS
     */
    protected function executeSMSCommand(User $user, array $command): array
    {
        switch ($command['type']) {
            case 'balance':
                return $this->getSMSBalance($user);
                
            case 'topup':
                return $this->processSMSTopup($user, $command['amount']);
                
            case 'transfer':
                return $this->processSMSTransfer($user, $command['phone'], $command['amount']);
                
            case 'help':
                return $this->sendSMSHelp($user->phone);
                
            default:
                return [
                    'success' => false,
                    'response' => 'أمر غير مدعوم. أرسل HELP للمساعدة.',
                ];
        }
    }

    /**
     * الحصول على الرصيد عبر SMS
     */
    protected function getSMSBalance(User $user): array
    {
        $wallet = $user->digitalWallets()->primary()->first();
        
        if (!$wallet) {
            return [
                'success' => false,
                'response' => 'لا توجد محفظة مسجلة. يرجى زيارة التطبيق لإنشاء محفظة.',
            ];
        }
        
        $balance = number_format($wallet->available_balance, 2);
        $currency = $wallet->currency;
        
        return [
            'success' => true,
            'response' => "رصيدك الحالي: {$balance} {$currency}\nآخر تحديث: " . now()->format('Y-m-d H:i'),
        ];
    }

    /**
     * الحصول على أو إنشاء جلسة USSD
     */
    protected function getOrCreateSession(string $phoneNumber, ?string $sessionId): USSDSession
    {
        if ($sessionId) {
            $session = USSDSession::where('session_id', $sessionId)->first();
            if ($session) {
                return $session;
            }
        }
        
        // البحث عن المستخدم
        $user = User::where('phone', $phoneNumber)->first();
        
        return USSDSession::create([
            'session_id' => Str::random(16),
            'phone_number' => $phoneNumber,
            'user_id' => $user?->id,
            'current_menu' => 'main',
            'step' => 0,
            'started_at' => now(),
            'last_activity_at' => now(),
        ]);
    }

    /**
     * تحديث الجلسة
     */
    protected function updateSession(USSDSession $session, string $input, array $response): void
    {
        $session->update([
            'last_input' => $input,
            'last_response' => $response['message'] ?? '',
            'last_activity_at' => now(),
            'interaction_count' => $session->interaction_count + 1,
        ]);
        
        // إنهاء الجلسة إذا لم تعد مستمرة
        if (!($response['continue'] ?? false)) {
            $session->update([
                'status' => 'completed',
                'ended_at' => now(),
            ]);
        }
    }

    /**
     * طلب التسجيل
     */
    protected function requestRegistration(USSDSession $session): array
    {
        return [
            'message' => "مرحباً بك في Hesabiai!\nيرجى تسجيل حساب جديد عبر التطبيق أو الموقع الإلكتروني.\n\nشكراً لك!",
            'continue' => false,
        ];
    }

    /**
     * إنهاء الجلسة
     */
    protected function endSession(USSDSession $session): array
    {
        $session->update([
            'status' => 'completed',
            'ended_at' => now(),
        ]);
        
        return [
            'message' => "شكراً لاستخدام Hesabiai!\nنتطلع لخدمتك مرة أخرى.",
            'continue' => false,
        ];
    }

    /**
     * عرض خيار غير صحيح
     */
    protected function showInvalidOption(USSDSession $session, string $menu): array
    {
        return [
            'message' => "خيار غير صحيح. يرجى اختيار رقم من القائمة.\n\n" . $this->showMenu($menu, $session)['message'],
            'continue' => true,
        ];
    }

    /**
     * إرسال مساعدة SMS
     */
    protected function sendSMSHelp(string $phoneNumber): array
    {
        $helpText = "أوامر Hesabiai SMS:\n";
        $helpText .= "BALANCE - عرض الرصيد\n";
        $helpText .= "TOPUP [مبلغ] - شحن المحفظة\n";
        $helpText .= "TRANSFER [هاتف] [مبلغ] - تحويل أموال\n";
        $helpText .= "HELP - عرض المساعدة";
        
        return [
            'success' => true,
            'response' => $helpText,
        ];
    }

    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanupExpiredSessions(): int
    {
        $expiredSessions = USSDSession::where('last_activity_at', '<', now()->subMinutes(30))
            ->where('status', 'active')
            ->get();
        
        foreach ($expiredSessions as $session) {
            $session->update([
                'status' => 'expired',
                'ended_at' => now(),
            ]);
        }
        
        return $expiredSessions->count();
    }
}
