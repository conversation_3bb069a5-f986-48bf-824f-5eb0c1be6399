<?php

namespace App\Domains\Taxation\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب إنشاء قاعدة ضريبية جديدة
 * تحقق شامل من البيانات مع دعم القواعد المعقدة
 */
class StoreTaxRuleRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create-tax-rules');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'tax_system_id' => 'required|exists:tax_systems,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:2000',
            'code' => [
                'nullable',
                'string',
                'max:50',
                'unique:tax_rules,code',
                'regex:/^[A-Z0-9_-]+$/'
            ],

            // Rule Type and Category
            'type' => [
                'required',
                'string',
                Rule::in(['VAT', 'CORPORATE_TAX', 'WITHHOLDING_TAX', 'EXCISE_TAX', 'CUSTOMS_DUTY', 'SALES_TAX', 'SERVICE_TAX', 'OTHER'])
            ],
            'category' => 'nullable|string|max:100',
            'subcategory' => 'nullable|string|max:100',

            // Tax Calculation
            'calculation_method' => [
                'required',
                'string',
                Rule::in(['PERCENTAGE', 'FIXED_AMOUNT', 'TIERED', 'PROGRESSIVE', 'FORMULA', 'LOOKUP_TABLE'])
            ],
            'rate' => 'nullable|numeric|between:0,100',
            'fixed_amount' => 'nullable|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_amount' => 'nullable|numeric|min:0',

            // Tiered Rates (for progressive taxation)
            'tiers' => 'nullable|array',
            'tiers.*.from_amount' => 'required_with:tiers|numeric|min:0',
            'tiers.*.to_amount' => 'nullable|numeric|gt:tiers.*.from_amount',
            'tiers.*.rate' => 'required_with:tiers|numeric|between:0,100',
            'tiers.*.fixed_amount' => 'nullable|numeric|min:0',

            // Formula (for complex calculations)
            'formula' => 'nullable|string|max:1000',
            'formula_variables' => 'nullable|array',

            // Lookup Table (for specific rates)
            'lookup_table' => 'nullable|array',
            'lookup_table.*.key' => 'required_with:lookup_table|string|max:100',
            'lookup_table.*.value' => 'required_with:lookup_table|numeric',
            'lookup_table.*.rate' => 'required_with:lookup_table|numeric|between:0,100',

            // Effective Dates
            'effective_from' => 'required|date',
            'effective_to' => 'nullable|date|after:effective_from',

            // Status and Priority
            'is_active' => 'boolean',
            'priority' => 'nullable|integer|between:1,100',

            // Conditions and Criteria
            'conditions' => 'nullable|array',
            'conditions.*.field' => 'required_with:conditions|string|max:100',
            'conditions.*.operator' => [
                'required_with:conditions',
                'string',
                Rule::in(['=', '!=', '>', '>=', '<', '<=', 'IN', 'NOT_IN', 'CONTAINS', 'NOT_CONTAINS', 'STARTS_WITH', 'ENDS_WITH'])
            ],
            'conditions.*.value' => 'required_with:conditions',
            'conditions.*.logical_operator' => [
                'nullable',
                'string',
                Rule::in(['AND', 'OR'])
            ],

            // Exemptions and Exclusions
            'exemptions' => 'nullable|array',
            'exemptions.*.type' => [
                'required_with:exemptions',
                'string',
                Rule::in(['FULL_EXEMPTION', 'PARTIAL_EXEMPTION', 'THRESHOLD_EXEMPTION', 'CONDITIONAL_EXEMPTION'])
            ],
            'exemptions.*.conditions' => 'nullable|array',
            'exemptions.*.exemption_rate' => 'nullable|numeric|between:0,100',
            'exemptions.*.threshold_amount' => 'nullable|numeric|min:0',

            // Reverse Charge
            'reverse_charge_applicable' => 'boolean',
            'reverse_charge_conditions' => 'nullable|array',

            // Rounding Rules
            'rounding_method' => [
                'nullable',
                'string',
                Rule::in(['ROUND', 'ROUND_UP', 'ROUND_DOWN', 'NO_ROUNDING'])
            ],
            'rounding_precision' => 'nullable|integer|between:0,6',

            // Geographic Scope
            'geographic_scope' => 'nullable|array',
            'geographic_scope.countries' => 'nullable|array',
            'geographic_scope.countries.*' => 'string|size:2',
            'geographic_scope.regions' => 'nullable|array',
            'geographic_scope.cities' => 'nullable|array',

            // Industry/Sector Specific
            'applicable_industries' => 'nullable|array',
            'applicable_industries.*' => 'string|max:100',
            'excluded_industries' => 'nullable|array',
            'excluded_industries.*' => 'string|max:100',

            // Product/Service Categories
            'applicable_products' => 'nullable|array',
            'applicable_products.*' => 'string|max:100',
            'excluded_products' => 'nullable|array',
            'excluded_products.*' => 'string|max:100',

            // Transaction Types
            'applicable_transaction_types' => 'nullable|array',
            'applicable_transaction_types.*' => [
                'string',
                Rule::in(['SALE', 'PURCHASE', 'IMPORT', 'EXPORT', 'SERVICE', 'RENTAL', 'COMMISSION', 'OTHER'])
            ],

            // Compliance and Reporting
            'requires_documentation' => 'boolean',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'string|max:100',
            'reporting_frequency' => [
                'nullable',
                'string',
                Rule::in(['MONTHLY', 'QUARTERLY', 'ANNUALLY', 'ON_TRANSACTION'])
            ],

            // Integration Settings
            'authority_code' => 'nullable|string|max:50',
            'external_reference' => 'nullable|string|max:100',
            'sync_with_authority' => 'boolean',

            // Advanced Features
            'supports_installments' => 'boolean',
            'installment_rules' => 'nullable|array',
            'penalty_applicable' => 'boolean',
            'penalty_rate' => 'nullable|numeric|between:0,100',
            'interest_applicable' => 'boolean',
            'interest_rate' => 'nullable|numeric|between:0,100',

            // Custom Fields and Metadata
            'custom_fields' => 'nullable|array',
            'metadata' => 'nullable|array',
            'notes' => 'nullable|string|max:2000',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'tax_system_id.required' => 'النظام الضريبي مطلوب',
            'tax_system_id.exists' => 'النظام الضريبي المحدد غير موجود',
            'name.required' => 'اسم القاعدة الضريبية مطلوب',
            'name.max' => 'اسم القاعدة الضريبية لا يجب أن يتجاوز 255 حرف',
            'name_ar.required' => 'الاسم العربي مطلوب',
            'code.unique' => 'رمز القاعدة الضريبية مستخدم مسبقاً',
            'code.regex' => 'رمز القاعدة الضريبية يجب أن يحتوي على أحرف كبيرة وأرقام وشرطات فقط',

            // Rule Type and Category
            'type.required' => 'نوع القاعدة الضريبية مطلوب',
            'type.in' => 'نوع القاعدة الضريبية المحدد غير صحيح',

            // Tax Calculation
            'calculation_method.required' => 'طريقة الحساب مطلوبة',
            'calculation_method.in' => 'طريقة الحساب المحددة غير صحيحة',
            'rate.between' => 'معدل الضريبة يجب أن يكون بين 0 و 100',
            'fixed_amount.min' => 'المبلغ الثابت لا يمكن أن يكون سالباً',
            'minimum_amount.min' => 'الحد الأدنى لا يمكن أن يكون سالباً',
            'maximum_amount.min' => 'الحد الأقصى لا يمكن أن يكون سالباً',

            // Tiered Rates
            'tiers.*.from_amount.required_with' => 'مبلغ البداية مطلوب للشريحة',
            'tiers.*.to_amount.gt' => 'مبلغ النهاية يجب أن يكون أكبر من مبلغ البداية',
            'tiers.*.rate.required_with' => 'معدل الشريحة مطلوب',
            'tiers.*.rate.between' => 'معدل الشريحة يجب أن يكون بين 0 و 100',

            // Effective Dates
            'effective_from.required' => 'تاريخ بداية السريان مطلوب',
            'effective_to.after' => 'تاريخ انتهاء السريان يجب أن يكون بعد تاريخ البداية',

            // Conditions
            'conditions.*.field.required_with' => 'حقل الشرط مطلوب',
            'conditions.*.operator.required_with' => 'عامل المقارنة مطلوب',
            'conditions.*.operator.in' => 'عامل المقارنة المحدد غير صحيح',
            'conditions.*.value.required_with' => 'قيمة الشرط مطلوبة',

            // Exemptions
            'exemptions.*.type.required_with' => 'نوع الإعفاء مطلوب',
            'exemptions.*.type.in' => 'نوع الإعفاء المحدد غير صحيح',
            'exemptions.*.exemption_rate.between' => 'معدل الإعفاء يجب أن يكون بين 0 و 100',

            // Rounding
            'rounding_method.in' => 'طريقة التقريب المحددة غير صحيحة',
            'rounding_precision.between' => 'دقة التقريب يجب أن تكون بين 0 و 6',

            // Geographic Scope
            'geographic_scope.countries.*.size' => 'رمز الدولة يجب أن يكون حرفين',

            // Transaction Types
            'applicable_transaction_types.*.in' => 'نوع المعاملة المحدد غير صحيح',

            // Reporting
            'reporting_frequency.in' => 'تكرار التقرير المحدد غير صحيح',

            // Penalties and Interest
            'penalty_rate.between' => 'معدل الغرامة يجب أن يكون بين 0 و 100',
            'interest_rate.between' => 'معدل الفائدة يجب أن يكون بين 0 و 100',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper(trim($this->code))
            ]);
        }

        // إنشاء رمز تلقائي إذا لم يتم تقديمه
        if (!$this->has('code') || empty($this->code)) {
            $this->merge([
                'code' => $this->generateRuleCode()
            ]);
        }

        // تعيين القيم الافتراضية
        $this->merge([
            'is_active' => $this->is_active ?? true,
            'priority' => $this->priority ?? 50,
            'reverse_charge_applicable' => $this->reverse_charge_applicable ?? false,
            'requires_documentation' => $this->requires_documentation ?? false,
            'sync_with_authority' => $this->sync_with_authority ?? true,
            'supports_installments' => $this->supports_installments ?? false,
            'penalty_applicable' => $this->penalty_applicable ?? false,
            'interest_applicable' => $this->interest_applicable ?? false,
            'rounding_method' => $this->rounding_method ?? 'ROUND',
            'rounding_precision' => $this->rounding_precision ?? 2,
        ]);
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من طريقة الحساب والمعاملات المطلوبة
            $this->validateCalculationMethod($validator);

            // التحقق من صحة الشروط
            $this->validateConditions($validator);

            // التحقق من صحة الشرائح
            $this->validateTiers($validator);

            // التحقق من صحة الصيغة
            $this->validateFormula($validator);

            // التحقق من صحة جدول البحث
            $this->validateLookupTable($validator);

            // التحقق من صحة الإعفاءات
            $this->validateExemptions($validator);

            // التحقق من النطاق الجغرافي
            $this->validateGeographicScope($validator);

            // التحقق من تضارب القواعد
            $this->validateRuleConflicts($validator);
        });
    }

    /**
     * التحقق من طريقة الحساب
     */
    protected function validateCalculationMethod($validator): void
    {
        switch ($this->calculation_method) {
            case 'PERCENTAGE':
                if (!$this->rate) {
                    $validator->errors()->add('rate', 'معدل الضريبة مطلوب لطريقة النسبة المئوية');
                }
                break;

            case 'FIXED_AMOUNT':
                if (!$this->fixed_amount) {
                    $validator->errors()->add('fixed_amount', 'المبلغ الثابت مطلوب لطريقة المبلغ الثابت');
                }
                break;

            case 'TIERED':
            case 'PROGRESSIVE':
                if (!$this->tiers || empty($this->tiers)) {
                    $validator->errors()->add('tiers', 'الشرائح مطلوبة لطريقة الحساب المتدرج');
                }
                break;

            case 'FORMULA':
                if (!$this->formula) {
                    $validator->errors()->add('formula', 'الصيغة مطلوبة لطريقة الحساب بالصيغة');
                }
                break;

            case 'LOOKUP_TABLE':
                if (!$this->lookup_table || empty($this->lookup_table)) {
                    $validator->errors()->add('lookup_table', 'جدول البحث مطلوب لطريقة جدول البحث');
                }
                break;
        }
    }

    /**
     * التحقق من صحة الشروط
     */
    protected function validateConditions($validator): void
    {
        if ($this->conditions) {
            foreach ($this->conditions as $index => $condition) {
                if (!isset($condition['field']) || !isset($condition['operator']) || !isset($condition['value'])) {
                    $validator->errors()->add("conditions.{$index}", 'شرط غير مكتمل');
                    continue;
                }

                // التحقق من صحة العامل مع نوع القيمة
                if (in_array($condition['operator'], ['IN', 'NOT_IN']) && !is_array($condition['value'])) {
                    $validator->errors()->add("conditions.{$index}.value", 'القيمة يجب أن تكون مصفوفة للعوامل IN و NOT_IN');
                }
            }
        }
    }

    /**
     * التحقق من صحة الشرائح
     */
    protected function validateTiers($validator): void
    {
        if ($this->tiers) {
            $previousTo = 0;
            foreach ($this->tiers as $index => $tier) {
                // التحقق من التسلسل
                if ($tier['from_amount'] < $previousTo) {
                    $validator->errors()->add("tiers.{$index}.from_amount", 'مبلغ البداية يجب أن يكون متسلسلاً');
                }

                if (isset($tier['to_amount'])) {
                    $previousTo = $tier['to_amount'];
                }

                // التحقق من وجود معدل أو مبلغ ثابت
                if (!isset($tier['rate']) && !isset($tier['fixed_amount'])) {
                    $validator->errors()->add("tiers.{$index}", 'يجب تحديد معدل أو مبلغ ثابت للشريحة');
                }
            }
        }
    }

    /**
     * التحقق من صحة الصيغة
     */
    protected function validateFormula($validator): void
    {
        if ($this->formula) {
            // التحقق من صحة الصيغة الرياضية
            $allowedFunctions = ['abs', 'round', 'ceil', 'floor', 'min', 'max', 'pow', 'sqrt'];
            $allowedOperators = ['+', '-', '*', '/', '(', ')', '%'];
            
            // تحقق بسيط من الصيغة
            $formula = preg_replace('/\s+/', '', $this->formula);
            $invalidChars = preg_replace('/[0-9\.\+\-\*\/\(\)\%a-zA-Z_]/', '', $formula);
            
            if (!empty($invalidChars)) {
                $validator->errors()->add('formula', 'الصيغة تحتوي على رموز غير مسموحة');
            }
        }
    }

    /**
     * التحقق من صحة جدول البحث
     */
    protected function validateLookupTable($validator): void
    {
        if ($this->lookup_table) {
            $keys = [];
            foreach ($this->lookup_table as $index => $entry) {
                if (in_array($entry['key'], $keys)) {
                    $validator->errors()->add("lookup_table.{$index}.key", 'مفتاح مكرر في جدول البحث');
                }
                $keys[] = $entry['key'];
            }
        }
    }

    /**
     * التحقق من صحة الإعفاءات
     */
    protected function validateExemptions($validator): void
    {
        if ($this->exemptions) {
            foreach ($this->exemptions as $index => $exemption) {
                if ($exemption['type'] === 'PARTIAL_EXEMPTION' && !isset($exemption['exemption_rate'])) {
                    $validator->errors()->add("exemptions.{$index}.exemption_rate", 'معدل الإعفاء مطلوب للإعفاء الجزئي');
                }

                if ($exemption['type'] === 'THRESHOLD_EXEMPTION' && !isset($exemption['threshold_amount'])) {
                    $validator->errors()->add("exemptions.{$index}.threshold_amount", 'مبلغ الحد مطلوب لإعفاء الحد');
                }
            }
        }
    }

    /**
     * التحقق من النطاق الجغرافي
     */
    protected function validateGeographicScope($validator): void
    {
        if ($this->geographic_scope && isset($this->geographic_scope['countries'])) {
            $validCountries = config('taxation.supported_countries', []);
            if (!empty($validCountries)) {
                foreach ($this->geographic_scope['countries'] as $index => $country) {
                    if (!in_array($country, $validCountries)) {
                        $validator->errors()->add("geographic_scope.countries.{$index}", 'رمز الدولة غير مدعوم');
                    }
                }
            }
        }
    }

    /**
     * التحقق من تضارب القواعد
     */
    protected function validateRuleConflicts($validator): void
    {
        // التحقق من وجود قواعد متضاربة في نفس النظام الضريبي
        $conflictingRules = \App\Domains\Taxation\Models\TaxRule::where('tax_system_id', $this->tax_system_id)
            ->where('type', $this->type)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->where('effective_from', '<=', $this->effective_to ?? '9999-12-31')
                      ->where(function ($q) {
                          $q->whereNull('effective_to')
                            ->orWhere('effective_to', '>=', $this->effective_from);
                      });
            })
            ->exists();

        if ($conflictingRules) {
            $validator->errors()->add('type', 'توجد قاعدة ضريبية متضاربة من نفس النوع في نفس الفترة');
        }
    }

    /**
     * إنشاء رمز القاعدة
     */
    protected function generateRuleCode(): string
    {
        $type = $this->type ?? 'TAX';
        $timestamp = now()->format('YmdHis');
        $random = strtoupper(substr(md5(uniqid()), 0, 4));
        
        return "{$type}_{$timestamp}_{$random}";
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }
}
