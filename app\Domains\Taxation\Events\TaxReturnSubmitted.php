<?php

namespace App\Domains\Taxation\Events;

use App\Domains\Taxation\Models\TaxReturn;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث تقديم الإقرار الضريبي
 * يتم إطلاقه عند تقديم إقرار ضريبي للهيئة الضريبية
 */
class TaxReturnSubmitted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public TaxReturn $taxReturn;
    public array $submissionResult;
    public array $metadata;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(TaxReturn $taxReturn, array $submissionResult = [], array $metadata = [])
    {
        $this->taxReturn = $taxReturn;
        $this->submissionResult = $submissionResult;
        $this->metadata = array_merge([
            'submitted_at' => now(),
            'submitted_by' => auth()->user()?->name ?? 'System',
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'submission_method' => 'web_interface',
        ], $metadata);
    }

    /**
     * الحصول على القنوات التي يجب بث الحدث عليها
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('tax-returns'),
            new PrivateChannel('tax-returns.' . $this->taxReturn->company_id),
            new PrivateChannel('user.' . $this->taxReturn->submitted_by),
            new Channel('tax-system.' . $this->taxReturn->tax_system_id),
            new PrivateChannel('compliance.alerts'),
        ];
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'tax-return.submitted';
    }

    /**
     * البيانات التي يتم بثها مع الحدث
     */
    public function broadcastWith(): array
    {
        return [
            'tax_return' => [
                'id' => $this->taxReturn->id,
                'uuid' => $this->taxReturn->uuid,
                'return_number' => $this->taxReturn->return_number,
                'type' => $this->taxReturn->type,
                'status' => $this->taxReturn->status,
                'company_id' => $this->taxReturn->company_id,
                'company_name' => $this->taxReturn->company?->name,
                'tax_system_id' => $this->taxReturn->tax_system_id,
                'tax_system_name' => $this->taxReturn->taxSystem?->name,
                'submission_reference' => $this->taxReturn->submission_reference,
                'authority_status' => $this->taxReturn->authority_status,
                'total_tax_amount' => $this->taxReturn->total_tax_amount,
                'currency' => $this->taxReturn->taxSystem?->currency,
                'submitted_at' => $this->taxReturn->submitted_at?->format('Y-m-d H:i:s'),
                'due_date' => $this->taxReturn->due_date?->format('Y-m-d'),
                'payment_due_date' => $this->taxReturn->payment_due_date?->format('Y-m-d'),
            ],
            'submission_result' => $this->submissionResult,
            'metadata' => $this->metadata,
            'message' => $this->getSubmissionMessage(),
            'notification_type' => $this->getNotificationType(),
            'next_actions' => $this->getNextActions(),
            'compliance_impact' => $this->getComplianceImpact(),
        ];
    }

    /**
     * تحديد ما إذا كان يجب بث الحدث
     */
    public function shouldBroadcast(): bool
    {
        // بث الحدث دائماً للتقديمات المهمة
        return true;
    }

    /**
     * الحصول على رسالة التقديم
     */
    protected function getSubmissionMessage(): string
    {
        $isSuccessful = $this->isSubmissionSuccessful();
        
        if ($isSuccessful) {
            return "تم تقديم الإقرار الضريبي رقم {$this->taxReturn->return_number} بنجاح";
        } else {
            return "فشل في تقديم الإقرار الضريبي رقم {$this->taxReturn->return_number}";
        }
    }

    /**
     * تحديد نوع الإشعار
     */
    protected function getNotificationType(): string
    {
        if ($this->isSubmissionSuccessful()) {
            return 'success';
        } else {
            return 'error';
        }
    }

    /**
     * التحقق من نجاح التقديم
     */
    protected function isSubmissionSuccessful(): bool
    {
        return $this->taxReturn->status === 'SUBMITTED' && 
               ($this->submissionResult['success'] ?? false);
    }

    /**
     * الحصول على الإجراءات التالية
     */
    protected function getNextActions(): array
    {
        $actions = [];

        if ($this->isSubmissionSuccessful()) {
            // إجراءات ما بعد التقديم الناجح
            $actions[] = [
                'type' => 'monitor_status',
                'title' => 'متابعة الحالة',
                'description' => 'تابع حالة الإقرار لدى الهيئة الضريبية',
                'priority' => 'medium',
                'estimated_time' => '1-3 أيام عمل',
            ];

            if ($this->taxReturn->total_tax_amount > 0) {
                $actions[] = [
                    'type' => 'prepare_payment',
                    'title' => 'تحضير الدفع',
                    'description' => 'حضر لدفع الضرائب المستحقة',
                    'priority' => 'high',
                    'amount' => $this->taxReturn->total_tax_amount,
                    'due_date' => $this->taxReturn->payment_due_date?->format('Y-m-d'),
                ];
            }

            $actions[] = [
                'type' => 'download_receipt',
                'title' => 'تحميل الإيصال',
                'description' => 'احتفظ بنسخة من إيصال التقديم',
                'priority' => 'low',
            ];
        } else {
            // إجراءات في حالة فشل التقديم
            $actions[] = [
                'type' => 'review_errors',
                'title' => 'مراجعة الأخطاء',
                'description' => 'راجع الأخطاء وصححها',
                'priority' => 'urgent',
                'errors' => $this->submissionResult['errors'] ?? [],
            ];

            $actions[] = [
                'type' => 'retry_submission',
                'title' => 'إعادة المحاولة',
                'description' => 'أعد تقديم الإقرار بعد التصحيح',
                'priority' => 'high',
            ];

            if ($this->taxReturn->due_date && $this->taxReturn->due_date->diffInDays(now()) <= 1) {
                $actions[] = [
                    'type' => 'urgent_contact',
                    'title' => 'اتصال عاجل',
                    'description' => 'اتصل بالدعم الفني فوراً',
                    'priority' => 'critical',
                ];
            }
        }

        return $actions;
    }

    /**
     * الحصول على تأثير الامتثال
     */
    protected function getComplianceImpact(): array
    {
        $impact = [
            'compliance_status' => 'maintained',
            'risk_level' => 'low',
            'penalties_avoided' => 0,
            'recommendations' => [],
        ];

        if ($this->isSubmissionSuccessful()) {
            // تقديم ناجح
            if ($this->taxReturn->due_date && $this->taxReturn->submitted_at <= $this->taxReturn->due_date) {
                $impact['compliance_status'] = 'improved';
                $impact['penalties_avoided'] = $this->calculateAvoidedPenalties();
                $impact['recommendations'][] = 'تم تجنب الغرامات بالتقديم في الوقت المحدد';
            }
        } else {
            // فشل في التقديم
            $impact['compliance_status'] = 'at_risk';
            $impact['risk_level'] = $this->calculateRiskLevel();
            $impact['recommendations'][] = 'يجب إعادة التقديم فوراً لتجنب الغرامات';
            
            if ($this->taxReturn->due_date && now() > $this->taxReturn->due_date) {
                $impact['compliance_status'] = 'violated';
                $impact['risk_level'] = 'critical';
                $impact['recommendations'][] = 'تم تجاوز الموعد النهائي - قد تطبق غرامات';
            }
        }

        return $impact;
    }

    /**
     * حساب الغرامات المتجنبة
     */
    protected function calculateAvoidedPenalties(): float
    {
        if (!$this->taxReturn->due_date || $this->taxReturn->submitted_at > $this->taxReturn->due_date) {
            return 0;
        }

        $penaltyRate = $this->taxReturn->taxSystem->compliance_settings['penalty_rate'] ?? 5;
        return ($this->taxReturn->total_tax_amount * $penaltyRate / 100);
    }

    /**
     * حساب مستوى المخاطر
     */
    protected function calculateRiskLevel(): string
    {
        if (!$this->taxReturn->due_date) {
            return 'medium';
        }

        $daysOverdue = now()->diffInDays($this->taxReturn->due_date, false);

        if ($daysOverdue > 0) {
            return 'critical';
        } elseif ($daysOverdue > -1) {
            return 'high';
        } elseif ($daysOverdue > -7) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * الحصول على معلومات إضافية للحدث
     */
    public function getEventInfo(): array
    {
        return [
            'event_type' => 'tax_return_submitted',
            'event_category' => 'taxation',
            'severity' => $this->getEventSeverity(),
            'affects_compliance' => true,
            'submission_successful' => $this->isSubmissionSuccessful(),
            'authority_reference' => $this->submissionResult['reference'] ?? null,
            'processing_time' => $this->submissionResult['processing_time'] ?? null,
            'validation_errors' => $this->submissionResult['errors'] ?? [],
            'warnings' => $this->submissionResult['warnings'] ?? [],
        ];
    }

    /**
     * تحديد شدة الحدث
     */
    protected function getEventSeverity(): string
    {
        if (!$this->isSubmissionSuccessful()) {
            if ($this->taxReturn->due_date && now() > $this->taxReturn->due_date) {
                return 'critical';
            }
            return 'high';
        }

        if ($this->taxReturn->total_tax_amount > 100000) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * الحصول على تفاصيل التقديم
     */
    public function getSubmissionDetails(): array
    {
        return [
            'submission_reference' => $this->taxReturn->submission_reference,
            'authority_status' => $this->taxReturn->authority_status,
            'submission_method' => $this->metadata['submission_method'],
            'processing_time' => $this->submissionResult['processing_time'] ?? null,
            'file_size' => $this->submissionResult['file_size'] ?? null,
            'validation_passed' => $this->submissionResult['validation_passed'] ?? false,
            'digital_signature_verified' => $this->submissionResult['digital_signature_verified'] ?? false,
            'authority_response_code' => $this->submissionResult['response_code'] ?? null,
            'authority_message' => $this->submissionResult['message'] ?? null,
        ];
    }

    /**
     * تحويل الحدث إلى مصفوفة للتسجيل
     */
    public function toArray(): array
    {
        return [
            'event' => 'TaxReturnSubmitted',
            'tax_return_id' => $this->taxReturn->id,
            'return_number' => $this->taxReturn->return_number,
            'type' => $this->taxReturn->type,
            'company_id' => $this->taxReturn->company_id,
            'tax_system_id' => $this->taxReturn->tax_system_id,
            'total_amount' => $this->taxReturn->total_tax_amount,
            'submission_successful' => $this->isSubmissionSuccessful(),
            'submission_reference' => $this->taxReturn->submission_reference,
            'authority_status' => $this->taxReturn->authority_status,
            'submission_result' => $this->submissionResult,
            'metadata' => $this->metadata,
            'event_info' => $this->getEventInfo(),
            'compliance_impact' => $this->getComplianceImpact(),
            'submission_details' => $this->getSubmissionDetails(),
            'timestamp' => now()->toISOString(),
        ];
    }
}
