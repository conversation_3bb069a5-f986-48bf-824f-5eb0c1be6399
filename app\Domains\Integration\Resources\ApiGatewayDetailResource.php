<?php

namespace App\Domains\Integration\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Detailed API Gateway Resource
 * 
 * Provides comprehensive gateway information including
 * real-time metrics, health status, and configuration details
 */
class ApiGatewayDetailResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'gateway_id' => $this->gateway_id,
            'name' => $this->name,
            'description' => $this->description,
            'base_url' => $this->base_url,
            'environment' => $this->environment,
            'status' => $this->status,
            'health_status' => $this->health_status,
            'version' => $this->version,
            'gateway_type' => $this->gateway_type,
            
            // Configuration
            'configuration' => [
                'load_balancer' => $this->load_balancer_config,
                'circuit_breaker' => $this->circuit_breaker_config,
                'rate_limiting' => $this->rate_limiting_config,
                'caching' => $this->caching_config,
                'security' => $this->security_config,
                'monitoring' => $this->monitoring_config,
                'auto_scaling' => $this->auto_scaling_config,
            ],
            
            // Features
            'features' => [
                'load_balancing_enabled' => $this->load_balancing_enabled,
                'circuit_breaker_enabled' => $this->circuit_breaker_enabled,
                'rate_limiting_enabled' => $this->rate_limiting_enabled,
                'caching_enabled' => $this->caching_enabled,
                'monitoring_enabled' => $this->monitoring_enabled,
                'auto_scaling_enabled' => $this->auto_scaling_enabled,
                'security_enabled' => $this->security_enabled,
                'logging_enabled' => $this->logging_enabled,
                'tracing_enabled' => $this->tracing_enabled,
            ],
            
            // Statistics
            'statistics' => [
                'total_requests' => $this->total_requests,
                'successful_requests' => $this->successful_requests,
                'failed_requests' => $this->failed_requests,
                'cached_requests' => $this->cached_requests,
                'rate_limited_requests' => $this->rate_limited_requests,
                'circuit_breaker_trips' => $this->circuit_breaker_trips,
                'average_response_time' => $this->average_response_time,
                'p95_response_time' => $this->p95_response_time,
                'p99_response_time' => $this->p99_response_time,
                'error_rate' => $this->error_rate,
                'cache_hit_ratio' => $this->cache_hit_ratio,
                'uptime_percentage' => $this->uptime_percentage,
                'data_transfer_in' => $this->data_transfer_in,
                'data_transfer_out' => $this->data_transfer_out,
            ],
            
            // Performance Metrics
            'performance' => [
                'cpu_usage' => $this->cpu_usage,
                'memory_usage' => $this->memory_usage,
                'network_io' => $this->network_io,
                'disk_io' => $this->disk_io,
                'concurrent_connections' => $this->concurrent_connections,
                'queue_depth' => $this->queue_depth,
                'health_score' => $this->health_score,
                'performance_score' => $this->performance_score,
                'reliability_score' => $this->reliability_score,
                'security_score' => $this->security_score,
            ],
            
            // SLA and Compliance
            'sla' => [
                'availability_sla' => $this->availability_sla,
                'performance_sla' => $this->performance_sla,
                'current_availability' => $this->uptime_percentage,
                'sla_compliance' => $this->calculateSlaCompliance(),
            ],
            
            // Cost Information
            'cost' => [
                'cost_per_request' => $this->cost_per_request,
                'monthly_cost_estimate' => $this->calculateMonthlyCost(),
                'cost_optimization_score' => $this->calculateCostOptimizationScore(),
            ],
            
            // Related Resources
            'endpoints' => ApiEndpointResource::collection($this->whenLoaded('endpoints')),
            'api_keys' => ApiKeyResource::collection($this->whenLoaded('apiKeys')),
            'recent_deployments' => DeploymentResource::collection($this->whenLoaded('deployments')),
            'scaling_events' => ScalingEventResource::collection($this->whenLoaded('scalingEvents')),
            'health_checks' => HealthCheckResource::collection($this->whenLoaded('healthChecks')),
            'security_incidents' => SecurityIncidentResource::collection($this->whenLoaded('securityIncidents')),
            
            // Metadata
            'metadata' => $this->metadata,
            'annotations' => $this->annotations,
            'labels' => $this->labels,
            'tags' => $this->tags,
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'last_request_at' => $this->last_request_at,
            'last_health_check' => $this->last_health_check,
            'last_deployment' => $this->last_deployment,
            
            // User Information
            'created_by' => $this->whenLoaded('creator', fn() => [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
                'email' => $this->creator->email,
            ]),
            'updated_by' => $this->whenLoaded('updater', fn() => [
                'id' => $this->updater->id,
                'name' => $this->updater->name,
                'email' => $this->updater->email,
            ]),
        ];
    }

    /**
     * Calculate SLA compliance percentage
     */
    protected function calculateSlaCompliance(): float
    {
        $availabilitySla = $this->availability_sla ?? 99.9;
        $performanceSla = $this->performance_sla ?? 1000; // ms
        
        $availabilityCompliance = min(100, ($this->uptime_percentage / $availabilitySla) * 100);
        $performanceCompliance = $this->average_response_time <= $performanceSla ? 100 : 
            max(0, 100 - (($this->average_response_time - $performanceSla) / $performanceSla * 100));
        
        return ($availabilityCompliance + $performanceCompliance) / 2;
    }

    /**
     * Calculate estimated monthly cost
     */
    protected function calculateMonthlyCost(): float
    {
        $requestsPerMonth = $this->total_requests * 30; // Rough estimate
        $costPerRequest = $this->cost_per_request ?? 0.001;
        
        return $requestsPerMonth * $costPerRequest;
    }

    /**
     * Calculate cost optimization score
     */
    protected function calculateCostOptimizationScore(): float
    {
        // This would implement a complex algorithm to score cost optimization
        // based on resource utilization, scaling efficiency, etc.
        return 85.5; // Placeholder
    }
}
