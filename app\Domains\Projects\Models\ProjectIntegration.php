<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج تكامل المشروع - Project Integration
 * يدير التكاملات مع الأدوات والمنصات الخارجية
 */
class ProjectIntegration extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'project_id',
        'platform',
        'integration_type',
        'credentials',
        'settings',
        'webhook_url',
        'webhook_secret',
        'is_active',
        'last_sync_at',
        'sync_status',
        'sync_errors',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'credentials' => 'encrypted:array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'last_sync_at' => 'datetime',
        'sync_errors' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * التحقق من حالة التكامل
     */
    public function getIsHealthyAttribute(): bool
    {
        return $this->is_active && 
               $this->sync_status === 'SUCCESS' && 
               empty($this->sync_errors);
    }

    /**
     * الحصول على آخر خطأ مزامنة
     */
    public function getLastSyncErrorAttribute(): ?string
    {
        return !empty($this->sync_errors) ? end($this->sync_errors)['message'] ?? null : null;
    }

    /**
     * تحديث حالة المزامنة
     */
    public function updateSyncStatus(string $status, array $errors = []): void
    {
        $this->update([
            'sync_status' => $status,
            'sync_errors' => $errors,
            'last_sync_at' => now(),
        ]);
    }

    /**
     * تفعيل التكامل
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * إلغاء تفعيل التكامل
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * فلترة التكاملات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب المنصة
     */
    public function scopeForPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * فلترة التكاملات الصحية
     */
    public function scopeHealthy($query)
    {
        return $query->where('is_active', true)
                    ->where('sync_status', 'SUCCESS');
    }
}
