<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Taxation\Controllers\TaxRateController;
use App\Domains\Taxation\Controllers\VATController;
use App\Domains\Taxation\Controllers\TaxReturnController;
use App\Domains\Taxation\Controllers\EInvoiceController;
use App\Domains\Taxation\Controllers\WithholdingTaxController;
use App\Domains\Taxation\Controllers\ExciseTaxController;
use App\Domains\Taxation\Controllers\TaxComplianceController;
use App\Domains\Taxation\Controllers\TaxReportController;
use App\Domains\Taxation\Controllers\TaxAnalyticsController;
// New Advanced Controllers
use App\Domains\Taxation\Controllers\TaxSystemController;
use App\Domains\Taxation\Controllers\TaxRuleController;

/*
|--------------------------------------------------------------------------
| Taxation API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام الضرائب
|
*/

// Tax Rates Management
Route::apiResource('tax-rates', TaxRateController::class);
Route::prefix('tax-rates')->group(function () {
    Route::get('by-country/{country}', [TaxRateController::class, 'getByCountry']);
    Route::get('by-category/{category}', [TaxRateController::class, 'getByCategory']);
    Route::get('current', [TaxRateController::class, 'getCurrentRates']);
    Route::get('historical/{date}', [TaxRateController::class, 'getHistoricalRates']);
    Route::post('calculate', [TaxRateController::class, 'calculateTax']);
    Route::post('bulk-update', [TaxRateController::class, 'bulkUpdateRates']);
    Route::get('effective-dates', [TaxRateController::class, 'getEffectiveDates']);
    Route::post('schedule-change', [TaxRateController::class, 'scheduleRateChange']);
    Route::get('upcoming-changes', [TaxRateController::class, 'getUpcomingChanges']);
    Route::post('import', [TaxRateController::class, 'importRates']);
    Route::get('export', [TaxRateController::class, 'exportRates']);
});

// VAT Management
Route::apiResource('vat', VATController::class);
Route::prefix('vat')->group(function () {
    Route::get('registration-status', [VATController::class, 'getRegistrationStatus']);
    Route::post('register', [VATController::class, 'registerForVAT']);
    Route::post('deregister', [VATController::class, 'deregisterFromVAT']);
    Route::get('threshold-check', [VATController::class, 'checkThreshold']);
    Route::get('returns', [VATController::class, 'getVATReturns']);
    Route::post('returns', [VATController::class, 'createVATReturn']);
    Route::get('returns/{return}', [VATController::class, 'getVATReturn']);
    Route::put('returns/{return}', [VATController::class, 'updateVATReturn']);
    Route::post('returns/{return}/submit', [VATController::class, 'submitVATReturn']);
    Route::post('returns/{return}/amend', [VATController::class, 'amendVATReturn']);
    Route::get('returns/{return}/pdf', [VATController::class, 'generateVATReturnPDF']);
    Route::get('input-tax-recovery', [VATController::class, 'getInputTaxRecovery']);
    Route::post('input-tax-recovery', [VATController::class, 'claimInputTaxRecovery']);
    Route::get('reverse-charge', [VATController::class, 'getReverseChargeTransactions']);
    Route::post('reverse-charge', [VATController::class, 'processReverseCharge']);
    Route::get('zero-rated-supplies', [VATController::class, 'getZeroRatedSupplies']);
    Route::get('exempt-supplies', [VATController::class, 'getExemptSupplies']);
    Route::get('partial-exemption', [VATController::class, 'getPartialExemptionCalculation']);
    Route::post('partial-exemption', [VATController::class, 'calculatePartialExemption']);
    Route::get('audit-trail', [VATController::class, 'getVATAuditTrail']);
});

// E-Invoicing
Route::apiResource('e-invoices', EInvoiceController::class);
Route::prefix('e-invoices')->group(function () {
    Route::post('{invoice}/generate', [EInvoiceController::class, 'generateEInvoice']);
    Route::post('{invoice}/submit', [EInvoiceController::class, 'submitToZATCA']);
    Route::get('{invoice}/status', [EInvoiceController::class, 'getSubmissionStatus']);
    Route::get('{invoice}/qr-code', [EInvoiceController::class, 'generateQRCode']);
    Route::get('{invoice}/xml', [EInvoiceController::class, 'getXMLContent']);
    Route::post('{invoice}/validate', [EInvoiceController::class, 'validateEInvoice']);
    Route::post('{invoice}/sign', [EInvoiceController::class, 'signEInvoice']);
    Route::get('{invoice}/verification', [EInvoiceController::class, 'verifySignature']);
    Route::post('batch-submit', [EInvoiceController::class, 'batchSubmit']);
    Route::get('submission-log', [EInvoiceController::class, 'getSubmissionLog']);
    Route::get('failed-submissions', [EInvoiceController::class, 'getFailedSubmissions']);
    Route::post('retry-failed', [EInvoiceController::class, 'retryFailedSubmissions']);
    Route::get('compliance-status', [EInvoiceController::class, 'getComplianceStatus']);
    Route::get('zatca-updates', [EInvoiceController::class, 'getZATCAUpdates']);
    Route::post('test-connection', [EInvoiceController::class, 'testZATCAConnection']);
    Route::get('certificates', [EInvoiceController::class, 'getCertificates']);
    Route::post('certificates/renew', [EInvoiceController::class, 'renewCertificate']);
});

// Tax Returns
Route::apiResource('tax-returns', TaxReturnController::class);
Route::prefix('tax-returns')->group(function () {
    Route::get('periods', [TaxReturnController::class, 'getTaxPeriods']);
    Route::get('period/{period}', [TaxReturnController::class, 'getReturnByPeriod']);
    Route::post('auto-generate/{period}', [TaxReturnController::class, 'autoGenerateReturn']);
    Route::post('{return}/calculate', [TaxReturnController::class, 'calculateTaxLiability']);
    Route::post('{return}/validate', [TaxReturnController::class, 'validateReturn']);
    Route::post('{return}/submit', [TaxReturnController::class, 'submitReturn']);
    Route::post('{return}/amend', [TaxReturnController::class, 'amendReturn']);
    Route::get('{return}/pdf', [TaxReturnController::class, 'generatePDF']);
    Route::get('{return}/supporting-documents', [TaxReturnController::class, 'getSupportingDocuments']);
    Route::post('{return}/supporting-documents', [TaxReturnController::class, 'uploadSupportingDocument']);
    Route::get('pending-submission', [TaxReturnController::class, 'getPendingSubmissions']);
    Route::get('overdue', [TaxReturnController::class, 'getOverdueReturns']);
    Route::get('submitted', [TaxReturnController::class, 'getSubmittedReturns']);
    Route::get('amendments', [TaxReturnController::class, 'getAmendments']);
    Route::get('deadlines', [TaxReturnController::class, 'getUpcomingDeadlines']);
    Route::post('bulk-submit', [TaxReturnController::class, 'bulkSubmitReturns']);
});

// Withholding Tax
Route::apiResource('withholding-tax', WithholdingTaxController::class);
Route::prefix('withholding-tax')->group(function () {
    Route::get('transactions', [WithholdingTaxController::class, 'getTransactions']);
    Route::post('transactions', [WithholdingTaxController::class, 'recordTransaction']);
    Route::put('transactions/{transaction}', [WithholdingTaxController::class, 'updateTransaction']);
    Route::delete('transactions/{transaction}', [WithholdingTaxController::class, 'deleteTransaction']);
    Route::post('calculate', [WithholdingTaxController::class, 'calculateWithholdingTax']);
    Route::get('certificates', [WithholdingTaxController::class, 'getCertificates']);
    Route::post('certificates', [WithholdingTaxController::class, 'generateCertificate']);
    Route::get('certificates/{certificate}/pdf', [WithholdingTaxController::class, 'downloadCertificate']);
    Route::get('monthly-returns', [WithholdingTaxController::class, 'getMonthlyReturns']);
    Route::post('monthly-returns', [WithholdingTaxController::class, 'createMonthlyReturn']);
    Route::get('annual-returns', [WithholdingTaxController::class, 'getAnnualReturns']);
    Route::post('annual-returns', [WithholdingTaxController::class, 'createAnnualReturn']);
    Route::get('suppliers', [WithholdingTaxController::class, 'getSuppliers']);
    Route::post('suppliers/{supplier}/exempt', [WithholdingTaxController::class, 'exemptSupplier']);
    Route::get('exemptions', [WithholdingTaxController::class, 'getExemptions']);
    Route::get('rates-by-service', [WithholdingTaxController::class, 'getRatesByService']);
});

// Excise Tax
Route::apiResource('excise-tax', ExciseTaxController::class);
Route::prefix('excise-tax')->group(function () {
    Route::get('products', [ExciseTaxController::class, 'getTaxableProducts']);
    Route::post('products', [ExciseTaxController::class, 'addTaxableProduct']);
    Route::put('products/{product}', [ExciseTaxController::class, 'updateTaxableProduct']);
    Route::delete('products/{product}', [ExciseTaxController::class, 'removeTaxableProduct']);
    Route::post('calculate', [ExciseTaxController::class, 'calculateExciseTax']);
    Route::get('warehouse-tracking', [ExciseTaxController::class, 'getWarehouseTracking']);
    Route::post('warehouse-tracking', [ExciseTaxController::class, 'recordWarehouseMovement']);
    Route::get('monthly-returns', [ExciseTaxController::class, 'getMonthlyReturns']);
    Route::post('monthly-returns', [ExciseTaxController::class, 'createMonthlyReturn']);
    Route::get('registration-status', [ExciseTaxController::class, 'getRegistrationStatus']);
    Route::post('register', [ExciseTaxController::class, 'registerForExciseTax']);
    Route::get('rates', [ExciseTaxController::class, 'getExciseTaxRates']);
    Route::get('exemptions', [ExciseTaxController::class, 'getExemptions']);
    Route::get('compliance-check', [ExciseTaxController::class, 'checkCompliance']);
});

// Tax Compliance
Route::prefix('compliance')->group(function () {
    Route::get('status', [TaxComplianceController::class, 'getComplianceStatus']);
    Route::get('checklist', [TaxComplianceController::class, 'getComplianceChecklist']);
    Route::post('run-checks', [TaxComplianceController::class, 'runComplianceChecks']);
    Route::get('violations', [TaxComplianceController::class, 'getViolations']);
    Route::post('violations/{violation}/resolve', [TaxComplianceController::class, 'resolveViolation']);
    Route::get('penalties', [TaxComplianceController::class, 'getPenalties']);
    Route::post('penalties/calculate', [TaxComplianceController::class, 'calculatePenalties']);
    Route::get('deadlines', [TaxComplianceController::class, 'getUpcomingDeadlines']);
    Route::get('alerts', [TaxComplianceController::class, 'getComplianceAlerts']);
    Route::post('alerts/acknowledge', [TaxComplianceController::class, 'acknowledgeAlert']);
    Route::get('audit-trail', [TaxComplianceController::class, 'getAuditTrail']);
    Route::get('risk-assessment', [TaxComplianceController::class, 'getRiskAssessment']);
    Route::post('risk-assessment/update', [TaxComplianceController::class, 'updateRiskAssessment']);
    Route::get('documentation', [TaxComplianceController::class, 'getRequiredDocumentation']);
    Route::post('documentation/upload', [TaxComplianceController::class, 'uploadDocument']);
    Route::get('training-requirements', [TaxComplianceController::class, 'getTrainingRequirements']);
    Route::post('training/complete', [TaxComplianceController::class, 'completeTraining']);
});

// Tax Reports
Route::prefix('reports')->group(function () {
    Route::get('/', [TaxReportController::class, 'getAvailableReports']);
    Route::get('vat-summary', [TaxReportController::class, 'getVATSummaryReport']);
    Route::get('withholding-tax-summary', [TaxReportController::class, 'getWithholdingTaxSummary']);
    Route::get('excise-tax-summary', [TaxReportController::class, 'getExciseTaxSummary']);
    Route::get('tax-liability', [TaxReportController::class, 'getTaxLiabilityReport']);
    Route::get('compliance-report', [TaxReportController::class, 'getComplianceReport']);
    Route::get('audit-report', [TaxReportController::class, 'getAuditReport']);
    Route::get('penalty-report', [TaxReportController::class, 'getPenaltyReport']);
    Route::get('payment-history', [TaxReportController::class, 'getPaymentHistoryReport']);
    Route::get('refund-status', [TaxReportController::class, 'getRefundStatusReport']);
    Route::get('e-invoicing-summary', [TaxReportController::class, 'getEInvoicingSummary']);
    Route::get('custom', [TaxReportController::class, 'generateCustomReport']);
    Route::post('schedule', [TaxReportController::class, 'scheduleReport']);
    Route::get('scheduled', [TaxReportController::class, 'getScheduledReports']);
    Route::get('export/{report}', [TaxReportController::class, 'exportReport']);
});

// Tax Analytics
Route::prefix('analytics')->group(function () {
    Route::get('dashboard', [TaxAnalyticsController::class, 'getDashboard']);
    Route::get('tax-burden-analysis', [TaxAnalyticsController::class, 'getTaxBurdenAnalysis']);
    Route::get('compliance-trends', [TaxAnalyticsController::class, 'getComplianceTrends']);
    Route::get('penalty-analysis', [TaxAnalyticsController::class, 'getPenaltyAnalysis']);
    Route::get('refund-analysis', [TaxAnalyticsController::class, 'getRefundAnalysis']);
    Route::get('efficiency-metrics', [TaxAnalyticsController::class, 'getEfficiencyMetrics']);
    Route::get('cost-benefit-analysis', [TaxAnalyticsController::class, 'getCostBenefitAnalysis']);
    Route::get('risk-metrics', [TaxAnalyticsController::class, 'getRiskMetrics']);
    Route::get('benchmarking', [TaxAnalyticsController::class, 'getBenchmarkingData']);
    Route::get('predictive-insights', [TaxAnalyticsController::class, 'getPredictiveInsights']);
    Route::get('optimization-recommendations', [TaxAnalyticsController::class, 'getOptimizationRecommendations']);
    Route::get('seasonal-analysis', [TaxAnalyticsController::class, 'getSeasonalAnalysis']);
    Route::get('industry-comparison', [TaxAnalyticsController::class, 'getIndustryComparison']);
});

// Tax Authority Integration
Route::prefix('authorities')->group(function () {
    Route::get('zatca/status', [TaxComplianceController::class, 'getZATCAStatus']);
    Route::post('zatca/sync', [TaxComplianceController::class, 'syncWithZATCA']);
    Route::get('zatca/updates', [TaxComplianceController::class, 'getZATCAUpdates']);
    Route::post('zatca/test-connection', [TaxComplianceController::class, 'testZATCAConnection']);
    Route::get('gazt/status', [TaxComplianceController::class, 'getGAZTStatus']);
    Route::post('gazt/sync', [TaxComplianceController::class, 'syncWithGAZT']);
    Route::get('mol/status', [TaxComplianceController::class, 'getMOLStatus']);
    Route::post('mol/sync', [TaxComplianceController::class, 'syncWithMOL']);
    Route::get('notifications', [TaxComplianceController::class, 'getAuthorityNotifications']);
    Route::post('notifications/{notification}/acknowledge', [TaxComplianceController::class, 'acknowledgeNotification']);
});

// Tax Optimization
Route::prefix('optimization')->group(function () {
    Route::get('opportunities', [TaxAnalyticsController::class, 'getOptimizationOpportunities']);
    Route::post('analyze-scenario', [TaxAnalyticsController::class, 'analyzeScenario']);
    Route::get('tax-planning', [TaxAnalyticsController::class, 'getTaxPlanningRecommendations']);
    Route::post('simulate-changes', [TaxAnalyticsController::class, 'simulateChanges']);
    Route::get('savings-potential', [TaxAnalyticsController::class, 'getSavingsPotential']);
    Route::get('restructuring-options', [TaxAnalyticsController::class, 'getRestructuringOptions']);
    Route::get('incentives', [TaxAnalyticsController::class, 'getAvailableIncentives']);
    Route::post('incentives/apply', [TaxAnalyticsController::class, 'applyForIncentive']);
});

// Import/Export
Route::prefix('import-export')->group(function () {
    Route::post('import-tax-data', [TaxRateController::class, 'importTaxData']);
    Route::post('import-transactions', [VATController::class, 'importTransactions']);
    Route::get('export-tax-data', [TaxRateController::class, 'exportTaxData']);
    Route::get('export-returns', [TaxReturnController::class, 'exportReturns']);
    Route::get('export-compliance-data', [TaxComplianceController::class, 'exportComplianceData']);
    Route::post('backup-data', [TaxAnalyticsController::class, 'backupTaxData']);
    Route::post('restore-data', [TaxAnalyticsController::class, 'restoreTaxData']);
});

// System Administration
Route::prefix('admin')->group(function () {
    Route::get('system-health', [TaxAnalyticsController::class, 'getSystemHealth']);
    Route::get('integration-status', [TaxComplianceController::class, 'getIntegrationStatus']);
    Route::post('refresh-cache', [TaxAnalyticsController::class, 'refreshCache']);
    Route::get('audit-logs', [TaxComplianceController::class, 'getAuditLogs']);
    Route::get('user-activity', [TaxAnalyticsController::class, 'getUserActivity']);
    Route::post('maintenance-mode', [TaxAnalyticsController::class, 'toggleMaintenanceMode']);
    Route::get('performance-metrics', [TaxAnalyticsController::class, 'getPerformanceMetrics']);
});

// ================================
// Advanced Tax Systems Management
// ================================
Route::middleware(['auth:sanctum'])->prefix('tax-systems')->group(function () {
    // CRUD Operations
    Route::get('/', [TaxSystemController::class, 'index'])->name('tax-systems.index');
    Route::post('/', [TaxSystemController::class, 'store'])->name('tax-systems.store');
    Route::get('/{id}', [TaxSystemController::class, 'show'])->name('tax-systems.show');
    Route::put('/{id}', [TaxSystemController::class, 'update'])->name('tax-systems.update');
    Route::delete('/{id}', [TaxSystemController::class, 'destroy'])->name('tax-systems.destroy');

    // Status Management
    Route::post('/{id}/set-default', [TaxSystemController::class, 'setAsDefault'])->name('tax-systems.set-default');
    Route::post('/{id}/toggle-status', [TaxSystemController::class, 'toggleStatus'])->name('tax-systems.toggle-status');

    // Integration & Testing
    Route::post('/{id}/test-connection', [TaxSystemController::class, 'testConnection'])->name('tax-systems.test-connection');
    Route::post('/{id}/sync-authority', [TaxSystemController::class, 'syncWithAuthority'])->name('tax-systems.sync-authority');

    // Configuration
    Route::get('/{id}/configuration', [TaxSystemController::class, 'getConfiguration'])->name('tax-systems.configuration');
    Route::post('/{id}/configuration', [TaxSystemController::class, 'updateConfiguration'])->name('tax-systems.update-configuration');

    // Statistics & Analytics
    Route::get('/{id}/statistics', [TaxSystemController::class, 'getStatistics'])->name('tax-systems.statistics');
    Route::get('/{id}/health-check', [TaxSystemController::class, 'healthCheck'])->name('tax-systems.health-check');

    // Import/Export
    Route::post('/{id}/export', [TaxSystemController::class, 'export'])->name('tax-systems.export');
    Route::post('/import', [TaxSystemController::class, 'import'])->name('tax-systems.import');
});

// ================================
// Advanced Tax Rules Management
// ================================
Route::middleware(['auth:sanctum'])->prefix('tax-rules')->group(function () {
    // CRUD Operations
    Route::get('/', [TaxRuleController::class, 'index'])->name('tax-rules.index');
    Route::post('/', [TaxRuleController::class, 'store'])->name('tax-rules.store');
    Route::get('/{id}', [TaxRuleController::class, 'show'])->name('tax-rules.show');
    Route::put('/{id}', [TaxRuleController::class, 'update'])->name('tax-rules.update');
    Route::delete('/{id}', [TaxRuleController::class, 'destroy'])->name('tax-rules.destroy');

    // Rule Management
    Route::post('/{id}/toggle-status', [TaxRuleController::class, 'toggleStatus'])->name('tax-rules.toggle-status');
    Route::post('/{id}/duplicate', [TaxRuleController::class, 'duplicate'])->name('tax-rules.duplicate');
    Route::post('/{id}/test', [TaxRuleController::class, 'testRule'])->name('tax-rules.test');

    // Validation & Testing
    Route::post('/{id}/validate', [TaxRuleController::class, 'validateRule'])->name('tax-rules.validate');
    Route::post('/bulk-validate', [TaxRuleController::class, 'bulkValidate'])->name('tax-rules.bulk-validate');

    // Bulk Operations
    Route::post('/bulk-update', [TaxRuleController::class, 'bulkUpdate'])->name('tax-rules.bulk-update');
    Route::post('/bulk-delete', [TaxRuleController::class, 'bulkDelete'])->name('tax-rules.bulk-delete');
    Route::post('/bulk-activate', [TaxRuleController::class, 'bulkActivate'])->name('tax-rules.bulk-activate');
    Route::post('/bulk-deactivate', [TaxRuleController::class, 'bulkDeactivate'])->name('tax-rules.bulk-deactivate');

    // Templates & Presets
    Route::get('/templates', [TaxRuleController::class, 'getTemplates'])->name('tax-rules.templates');
    Route::post('/from-template', [TaxRuleController::class, 'createFromTemplate'])->name('tax-rules.from-template');

    // Analytics
    Route::get('/{id}/usage-statistics', [TaxRuleController::class, 'getUsageStatistics'])->name('tax-rules.usage-statistics');
    Route::get('/{id}/performance-metrics', [TaxRuleController::class, 'getPerformanceMetrics'])->name('tax-rules.performance-metrics');
});
