<?php

namespace App\Domains\CRM\Console\Commands;

use Illuminate\Console\Command;
use App\Domains\CRM\Services\SalesAutomationService;
use App\Domains\CRM\Services\MarketingAutomationService;
use App\Domains\CRM\Services\CustomerSegmentationService;
use App\Domains\CRM\Services\CRMIntegrationService;

/**
 * أمر تشغيل الأتمتة
 * Run Automation Command
 */
class RunAutomationCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'crm:run-automation 
                            {--type=all : نوع الأتمتة (sales, marketing, segmentation, integration, all)}
                            {--force : تشغيل إجباري حتى لو تم التشغيل مؤخراً}';

    /**
     * The console command description.
     */
    protected $description = 'تشغيل أتمتة CRM (المبيعات، التسويق، التقسيم، التكامل)';

    /**
     * خدمة أتمتة المبيعات
     */
    protected SalesAutomationService $salesAutomation;

    /**
     * خدمة أتمتة التسويق
     */
    protected MarketingAutomationService $marketingAutomation;

    /**
     * خدمة تقسيم العملاء
     */
    protected CustomerSegmentationService $segmentationService;

    /**
     * خدمة التكامل
     */
    protected CRMIntegrationService $integrationService;

    /**
     * Create a new command instance.
     */
    public function __construct(
        SalesAutomationService $salesAutomation,
        MarketingAutomationService $marketingAutomation,
        CustomerSegmentationService $segmentationService,
        CRMIntegrationService $integrationService
    ) {
        parent::__construct();
        
        $this->salesAutomation = $salesAutomation;
        $this->marketingAutomation = $marketingAutomation;
        $this->segmentationService = $segmentationService;
        $this->integrationService = $integrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $force = $this->option('force');

        $this->info('🚀 بدء تشغيل أتمتة CRM...');

        try {
            switch ($type) {
                case 'sales':
                    $this->runSalesAutomation($force);
                    break;
                    
                case 'marketing':
                    $this->runMarketingAutomation($force);
                    break;
                    
                case 'segmentation':
                    $this->runSegmentationAutomation($force);
                    break;
                    
                case 'integration':
                    $this->runIntegrationAutomation($force);
                    break;
                    
                case 'all':
                default:
                    $this->runAllAutomation($force);
                    break;
            }

            $this->info('✅ تم تشغيل الأتمتة بنجاح!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ خطأ في تشغيل الأتمتة: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * تشغيل أتمتة المبيعات
     */
    protected function runSalesAutomation(bool $force): void
    {
        $this->info('📈 تشغيل أتمتة المبيعات...');

        $bar = $this->output->createProgressBar(5);
        $bar->start();

        // تنفيذ المهام التلقائية
        $this->salesAutomation->executeAutomatedTasks();
        $bar->advance();

        // تحديث نقاط الفرص
        $this->salesAutomation->updateOpportunityScores();
        $bar->advance();

        // توزيع العملاء المحتملين
        $this->salesAutomation->distributeLeads();
        $bar->advance();

        // متابعة الفرص المتأخرة
        $this->salesAutomation->followUpOverdueOpportunities();
        $bar->advance();

        // تصعيد الفرص المهمة
        $this->salesAutomation->escalateImportantOpportunities();
        $bar->advance();

        $bar->finish();
        $this->newLine();
        $this->info('✅ تم تشغيل أتمتة المبيعات');
    }

    /**
     * تشغيل أتمتة التسويق
     */
    protected function runMarketingAutomation(bool $force): void
    {
        $this->info('📧 تشغيل أتمتة التسويق...');

        $bar = $this->output->createProgressBar(4);
        $bar->start();

        // تنفيذ الحملات التلقائية
        $this->marketingAutomation->executeAutomatedCampaigns();
        $bar->advance();

        // إرسال حملات المتابعة
        $this->marketingAutomation->sendFollowUpCampaigns();
        $bar->advance();

        // حملات العملاء غير النشطين
        $this->marketingAutomation->sendInactiveCampaigns();
        $bar->advance();

        // تحليل أداء الحملات
        $this->marketingAutomation->analyzeCampaignPerformance();
        $bar->advance();

        $bar->finish();
        $this->newLine();
        $this->info('✅ تم تشغيل أتمتة التسويق');
    }

    /**
     * تشغيل أتمتة التقسيم
     */
    protected function runSegmentationAutomation(bool $force): void
    {
        $this->info('🎯 تشغيل أتمتة التقسيم...');

        $bar = $this->output->createProgressBar(3);
        $bar->start();

        // تحديث الشرائح الديناميكية
        $results = $this->segmentationService->updateAllDynamicSegments();
        $bar->advance();

        // إنشاء شرائح سلوكية تلقائية
        $this->segmentationService->createAutomaticBehavioralSegments();
        $bar->advance();

        // تحليل أداء الشرائح
        $this->segmentationService->analyzeSegmentPerformance();
        $bar->advance();

        $bar->finish();
        $this->newLine();

        // عرض نتائج التحديث
        $this->table(
            ['الشريحة', 'الحجم السابق', 'الحجم الجديد', 'التغيير'],
            collect($results)->map(function ($result) {
                return [
                    $result['segment_name'],
                    $result['old_size'],
                    $result['new_size'],
                    $result['change'] >= 0 ? "+{$result['change']}" : $result['change']
                ];
            })->toArray()
        );

        $this->info('✅ تم تشغيل أتمتة التقسيم');
    }

    /**
     * تشغيل أتمتة التكامل
     */
    protected function runIntegrationAutomation(bool $force): void
    {
        $this->info('🔗 تشغيل أتمتة التكامل...');

        $bar = $this->output->createProgressBar(6);
        $bar->start();

        // مزامنة مع المحاسبة
        $this->integrationService->syncWithAccounting();
        $bar->advance();

        // مزامنة مع المشاريع
        $this->integrationService->syncWithProjects();
        $bar->advance();

        // مزامنة مع الدعم الفني
        $this->integrationService->syncWithSupport();
        $bar->advance();

        // مزامنة مع التجارة الإلكترونية
        $this->integrationService->syncWithEcommerce();
        $bar->advance();

        // مزامنة مع البريد
        $this->integrationService->syncWithEmailSystem();
        $bar->advance();

        // مزامنة مع الأنظمة الخارجية
        $this->integrationService->syncWithExternalEcommerce();
        $bar->advance();

        $bar->finish();
        $this->newLine();
        $this->info('✅ تم تشغيل أتمتة التكامل');
    }

    /**
     * تشغيل جميع أنواع الأتمتة
     */
    protected function runAllAutomation(bool $force): void
    {
        $this->info('🔄 تشغيل جميع أنواع الأتمتة...');

        $this->runSalesAutomation($force);
        $this->newLine();

        $this->runMarketingAutomation($force);
        $this->newLine();

        $this->runSegmentationAutomation($force);
        $this->newLine();

        $this->runIntegrationAutomation($force);
        $this->newLine();

        $this->info('🎉 تم تشغيل جميع أنواع الأتمتة بنجاح!');
    }
}
