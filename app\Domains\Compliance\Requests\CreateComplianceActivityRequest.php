<?php

namespace App\Domains\Compliance\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب إنشاء نشاط امتثال
 */
class CreateComplianceActivityRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Domains\Compliance\Models\ComplianceActivity::class);
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            'company_id' => ['required', 'exists:companies,id'],
            'country_id' => ['required', 'exists:countries,id'],
            'compliance_rule_id' => ['nullable', 'exists:compliance_rules,id'],
            'related_model_type' => ['nullable', 'string', 'max:255'],
            'related_model_id' => ['nullable', 'integer'],
            'activity_type' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceActivity::ACTIVITY_TYPES))],
            'activity_category' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceActivity::ACTIVITY_CATEGORIES))],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'priority' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceActivity::PRIORITIES))],
            'risk_level' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceActivity::RISK_LEVELS))],
            'due_date' => ['nullable', 'date', 'after:now'],
            'assigned_to' => ['nullable', 'exists:users,id'],
            'activity_data' => ['nullable', 'array'],
            'automation_level' => ['required', Rule::in(array_keys(\App\Domains\Compliance\Models\ComplianceActivity::AUTOMATION_LEVELS))],
            'send_notification' => ['boolean'],
            'metadata' => ['nullable', 'array'],
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'company_id.required' => 'الشركة مطلوبة',
            'company_id.exists' => 'الشركة المحددة غير موجودة',
            'country_id.required' => 'الدولة مطلوبة',
            'country_id.exists' => 'الدولة المحددة غير موجودة',
            'compliance_rule_id.exists' => 'قاعدة الامتثال المحددة غير موجودة',
            'activity_type.required' => 'نوع النشاط مطلوب',
            'activity_type.in' => 'نوع النشاط غير صحيح',
            'activity_category.required' => 'فئة النشاط مطلوبة',
            'activity_category.in' => 'فئة النشاط غير صحيحة',
            'title.required' => 'عنوان النشاط مطلوب',
            'title.max' => 'عنوان النشاط يجب ألا يتجاوز 255 حرف',
            'description.required' => 'وصف النشاط مطلوب',
            'priority.required' => 'الأولوية مطلوبة',
            'priority.in' => 'الأولوية غير صحيحة',
            'risk_level.required' => 'مستوى المخاطر مطلوب',
            'risk_level.in' => 'مستوى المخاطر غير صحيح',
            'due_date.date' => 'تاريخ الاستحقاق يجب أن يكون تاريخ صحيح',
            'due_date.after' => 'تاريخ الاستحقاق يجب أن يكون في المستقبل',
            'assigned_to.exists' => 'المستخدم المُعيَّن غير موجود',
            'automation_level.required' => 'مستوى الأتمتة مطلوب',
            'automation_level.in' => 'مستوى الأتمتة غير صحيح',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تعيين القيم الافتراضية
        $this->merge([
            'send_notification' => $this->boolean('send_notification', true),
        ]);

        // تحويل البيانات JSON إذا كانت نصوص
        if ($this->has('activity_data') && is_string($this->activity_data)) {
            $this->merge([
                'activity_data' => json_decode($this->activity_data, true) ?: []
            ]);
        }

        if ($this->has('metadata') && is_string($this->metadata)) {
            $this->merge([
                'metadata' => json_decode($this->metadata, true) ?: []
            ]);
        }
    }

    /**
     * الحصول على البيانات المحققة مع معالجة إضافية
     */
    public function getValidatedData(): array
    {
        $data = $this->validated();

        // إضافة معرف المستخدم الحالي
        $data['user_id'] = $this->user()->id;

        return $data;
    }

    /**
     * قواعد إضافية للتحقق بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من أن المستخدم يمكنه الوصول للشركة
            if ($this->company_id && !$this->user()->can('access-company', $this->company_id)) {
                $validator->errors()->add('company_id', 'ليس لديك صلاحية للوصول لهذه الشركة');
            }

            // التحقق من أن المستخدم يمكنه الوصول للدولة
            if ($this->country_id) {
                $country = \App\Domains\Compliance\Models\Country::find($this->country_id);
                if ($country && !$this->user()->can('access-country', $country->code)) {
                    $validator->errors()->add('country_id', 'ليس لديك صلاحية للوصول لهذه الدولة');
                }
            }

            // التحقق من صحة النموذج المرتبط
            if ($this->related_model_type && $this->related_model_id) {
                if (!class_exists($this->related_model_type)) {
                    $validator->errors()->add('related_model_type', 'نوع النموذج المرتبط غير صحيح');
                } else {
                    $model = $this->related_model_type::find($this->related_model_id);
                    if (!$model) {
                        $validator->errors()->add('related_model_id', 'النموذج المرتبط غير موجود');
                    }
                }
            }
        });
    }
}
