<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Accounting\Models\JournalEntry;
use App\Domains\Accounting\Models\JournalEntryLine;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use App\Domains\Accounting\Requests\StoreJournalEntryRequest;
use App\Domains\Accounting\Requests\UpdateJournalEntryRequest;
use App\Domains\Accounting\Resources\JournalEntryResource;
use App\Domains\Accounting\Resources\JournalEntryCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * Journal Entry Controller
 * تحكم في قيود اليومية
 */
class JournalEntryController extends Controller
{
    protected AdvancedAccountingEngine $accountingEngine;

    public function __construct(AdvancedAccountingEngine $accountingEngine)
    {
        $this->accountingEngine = $accountingEngine;
        $this->middleware('auth');
    }

    /**
     * عرض قائمة قيود اليومية
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', JournalEntry::class);

        $query = JournalEntry::with(['lines.account', 'creator', 'approver'])
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('entry_number', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%")
                          ->orWhere('reference', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            })
            ->when($request->date_from, function ($q, $dateFrom) {
                $q->where('entry_date', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($q, $dateTo) {
                $q->where('entry_date', '<=', $dateTo);
            })
            ->when($request->source_type, function ($q, $sourceType) {
                $q->where('source_type', $sourceType);
            })
            ->when($request->requires_review, function ($q) {
                $q->where('requires_review', true);
            });

        $entries = $query->orderBy('entry_date', 'desc')
                        ->orderBy('entry_number', 'desc')
                        ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new JournalEntryCollection($entries),
            'message' => 'تم جلب قيود اليومية بنجاح'
        ]);
    }

    /**
     * إنشاء قيد يومية جديد
     */
    public function store(StoreJournalEntryRequest $request): JsonResponse
    {
        $this->authorize('create', JournalEntry::class);

        try {
            $entry = $this->accountingEngine->createJournalEntry($request->validated());

            return response()->json([
                'success' => true,
                'data' => new JournalEntryResource($entry->load(['lines.account', 'creator'])),
                'message' => 'تم إنشاء قيد اليومية بنجاح'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء قيد اليومية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل قيد يومية محدد
     */
    public function show(JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('view', $journalEntry);

        $journalEntry->load([
            'lines.account',
            'creator',
            'updater',
            'approver',
            'reversingEntry',
            'reversedEntry'
        ]);

        return response()->json([
            'success' => true,
            'data' => new JournalEntryResource($journalEntry),
            'message' => 'تم جلب تفاصيل قيد اليومية بنجاح'
        ]);
    }

    /**
     * تحديث قيد يومية
     */
    public function update(UpdateJournalEntryRequest $request, JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('update', $journalEntry);

        if ($journalEntry->status === 'POSTED') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل قيد مرحل'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // حذف الخطوط القديمة
            $journalEntry->lines()->delete();

            // تحديث بيانات القيد
            $journalEntry->update([
                'entry_date' => $request->entry_date,
                'description' => $request->description,
                'reference' => $request->reference,
                'updated_by' => auth()->id(),
            ]);

            $totalDebits = 0;
            $totalCredits = 0;

            // إنشاء الخطوط الجديدة
            foreach ($request->lines as $lineData) {
                $line = $journalEntry->lines()->create([
                    'account_id' => $lineData['account_id'],
                    'debit_amount' => $lineData['debit_amount'] ?? 0,
                    'credit_amount' => $lineData['credit_amount'] ?? 0,
                    'description' => $lineData['description'] ?? $journalEntry->description,
                    'cost_center_id' => $lineData['cost_center_id'] ?? null,
                    'project_id' => $lineData['project_id'] ?? null,
                ]);

                $totalDebits += $line->debit_amount;
                $totalCredits += $line->credit_amount;
            }

            // التحقق من توازن القيد
            if (abs($totalDebits - $totalCredits) > 0.01) {
                throw new \Exception('القيد غير متوازن: المدين = ' . $totalDebits . '، الدائن = ' . $totalCredits);
            }

            $journalEntry->update([
                'total_debit' => $totalDebits,
                'total_credit' => $totalCredits,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new JournalEntryResource($journalEntry->load(['lines.account'])),
                'message' => 'تم تحديث قيد اليومية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث قيد اليومية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف قيد يومية
     */
    public function destroy(JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('delete', $journalEntry);

        if ($journalEntry->status === 'POSTED') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف قيد مرحل'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // حذف الخطوط
            $journalEntry->lines()->delete();
            
            // حذف القيد
            $journalEntry->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف قيد اليومية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف قيد اليومية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * ترحيل قيد يومية
     */
    public function postEntry(JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('post', $journalEntry);

        if ($journalEntry->status === 'POSTED') {
            return response()->json([
                'success' => false,
                'message' => 'القيد مرحل مسبقاً'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // ترحيل القيد
            $journalEntry->update([
                'status' => 'POSTED',
                'posted_at' => now(),
                'posted_by' => auth()->id(),
            ]);

            // تحديث أرصدة الحسابات
            foreach ($journalEntry->lines as $line) {
                $line->account->updateCurrentBalance();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new JournalEntryResource($journalEntry->fresh(['lines.account'])),
                'message' => 'تم ترحيل قيد اليومية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء ترحيل قيد اليومية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عكس قيد يومية
     */
    public function reverseEntry(Request $request, JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('reverse', $journalEntry);

        if ($journalEntry->status !== 'POSTED') {
            return response()->json([
                'success' => false,
                'message' => 'يمكن عكس القيود المرحلة فقط'
            ], 422);
        }

        if ($journalEntry->is_reversed) {
            return response()->json([
                'success' => false,
                'message' => 'القيد معكوس مسبقاً'
            ], 422);
        }

        $request->validate([
            'reversal_reason' => 'required|string|max:255',
            'reversal_date' => 'required|date',
        ]);

        DB::beginTransaction();
        try {
            // إنشاء قيد العكس
            $reversalData = [
                'entry_date' => $request->reversal_date,
                'description' => 'عكس قيد: ' . $journalEntry->description,
                'reference' => 'REV-' . $journalEntry->entry_number,
                'source_type' => 'REVERSAL',
                'source_id' => $journalEntry->id,
                'lines' => [],
            ];

            // عكس الخطوط
            foreach ($journalEntry->lines as $line) {
                $reversalData['lines'][] = [
                    'account_id' => $line->account_id,
                    'debit_amount' => $line->credit_amount, // عكس المبالغ
                    'credit_amount' => $line->debit_amount,
                    'description' => 'عكس: ' . $line->description,
                ];
            }

            $reversalEntry = $this->accountingEngine->createJournalEntry($reversalData);

            // ترحيل قيد العكس تلقائياً
            $reversalEntry->update([
                'status' => 'POSTED',
                'posted_at' => now(),
                'posted_by' => auth()->id(),
            ]);

            // تحديث القيد الأصلي
            $journalEntry->update([
                'is_reversed' => true,
                'reversed_at' => now(),
                'reversed_by' => auth()->id(),
                'reversal_reason' => $request->reversal_reason,
                'reversing_entry_id' => $reversalEntry->id,
            ]);

            // تحديث أرصدة الحسابات
            foreach ($reversalEntry->lines as $line) {
                $line->account->updateCurrentBalance();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'original_entry' => new JournalEntryResource($journalEntry->fresh()),
                    'reversal_entry' => new JournalEntryResource($reversalEntry->load(['lines.account'])),
                ],
                'message' => 'تم عكس قيد اليومية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء عكس قيد اليومية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اعتماد قيد يومية
     */
    public function approveEntry(JournalEntry $journalEntry): JsonResponse
    {
        $this->authorize('approve', $journalEntry);

        if ($journalEntry->status === 'APPROVED') {
            return response()->json([
                'success' => false,
                'message' => 'القيد معتمد مسبقاً'
            ], 422);
        }

        $journalEntry->update([
            'status' => 'APPROVED',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
            'requires_review' => false,
        ]);

        return response()->json([
            'success' => true,
            'data' => new JournalEntryResource($journalEntry->fresh(['lines.account', 'approver'])),
            'message' => 'تم اعتماد قيد اليومية بنجاح'
        ]);
    }

    /**
     * الحصول على القيود غير المرحلة
     */
    public function getUnpostedEntries(Request $request): JsonResponse
    {
        $this->authorize('viewAny', JournalEntry::class);

        $entries = JournalEntry::with(['lines.account', 'creator'])
            ->where('status', 'DRAFT')
            ->orWhere('status', 'APPROVED')
            ->orderBy('entry_date')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new JournalEntryCollection($entries),
            'message' => 'تم جلب القيود غير المرحلة بنجاح'
        ]);
    }

    /**
     * الحصول على القيود في انتظار الاعتماد
     */
    public function getPendingApprovalEntries(Request $request): JsonResponse
    {
        $this->authorize('viewAny', JournalEntry::class);

        $entries = JournalEntry::with(['lines.account', 'creator'])
            ->where('status', 'DRAFT')
            ->where('requires_review', true)
            ->orderBy('created_at')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new JournalEntryCollection($entries),
            'message' => 'تم جلب القيود في انتظار الاعتماد بنجاح'
        ]);
    }

    /**
     * إنشاء قيود تلقائية
     */
    public function autoGenerateEntries(Request $request): JsonResponse
    {
        $this->authorize('create', JournalEntry::class);

        $request->validate([
            'source_type' => 'required|string',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        try {
            $generatedEntries = $this->accountingEngine->autoGenerateEntries(
                $request->source_type,
                $request->date_from,
                $request->date_to
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'generated_count' => count($generatedEntries),
                    'entries' => JournalEntryResource::collection($generatedEntries),
                ],
                'message' => 'تم إنشاء القيود التلقائية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء القيود التلقائية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على قوالب القيود
     */
    public function getEntryTemplates(): JsonResponse
    {
        $this->authorize('viewAny', JournalEntry::class);

        $templates = $this->accountingEngine->getEntryTemplates();

        return response()->json([
            'success' => true,
            'data' => $templates,
            'message' => 'تم جلب قوالب القيود بنجاح'
        ]);
    }

    /**
     * إنشاء قيد من قالب
     */
    public function createFromTemplate(Request $request): JsonResponse
    {
        $this->authorize('create', JournalEntry::class);

        $request->validate([
            'template_id' => 'required|integer',
            'entry_date' => 'required|date',
            'variables' => 'array',
        ]);

        try {
            $entry = $this->accountingEngine->createEntryFromTemplate(
                $request->template_id,
                $request->entry_date,
                $request->variables ?? []
            );

            return response()->json([
                'success' => true,
                'data' => new JournalEntryResource($entry->load(['lines.account'])),
                'message' => 'تم إنشاء القيد من القالب بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء القيد من القالب: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على مسار المراجعة
     */
    public function getAuditTrail(Request $request): JsonResponse
    {
        $this->authorize('audit', JournalEntry::class);

        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $auditTrail = $this->accountingEngine->getAuditTrail($dateFrom, $dateTo);

        return response()->json([
            'success' => true,
            'data' => $auditTrail,
            'message' => 'تم جلب مسار المراجعة بنجاح'
        ]);
    }
}
