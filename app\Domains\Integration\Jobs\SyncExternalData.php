<?php

namespace App\Domains\Integration\Jobs;

use App\Domains\Integration\Models\ExternalIntegration;
use App\Domains\Integration\Services\ExternalIntegrationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Sync External Data Job
 * 
 * Handles synchronization of data between external systems
 * with comprehensive error handling and retry logic
 */
class SyncExternalData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public ExternalIntegration $integration;
    public string $syncType;
    public array $syncOptions;
    public ?string $resourceType;
    public ?array $filters;
    public int $tries = 3;
    public int $maxExceptions = 3;
    public int $timeout = 1800; // 30 minutes
    public int $retryAfter = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        ExternalIntegration $integration,
        string $syncType = 'full',
        array $syncOptions = [],
        ?string $resourceType = null,
        ?array $filters = null
    ) {
        $this->integration = $integration;
        $this->syncType = $syncType;
        $this->syncOptions = $syncOptions;
        $this->resourceType = $resourceType;
        $this->filters = $filters;

        // Set queue based on integration priority
        $this->onQueue($this->determineQueue());
        
        // Set delay if specified
        if (isset($syncOptions['delay_seconds'])) {
            $this->delay(now()->addSeconds($syncOptions['delay_seconds']));
        }
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping("sync_{$this->integration->id}_{$this->syncType}"),
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(ExternalIntegrationService $integrationService): void
    {
        $startTime = microtime(true);
        $syncId = uniqid('sync_');
        
        try {
            Log::info('Starting external data sync', [
                'sync_id' => $syncId,
                'integration_id' => $this->integration->id,
                'integration_name' => $this->integration->name,
                'sync_type' => $this->syncType,
                'resource_type' => $this->resourceType,
                'attempt' => $this->attempts(),
            ]);

            // Update sync status
            $this->updateSyncStatus($syncId, 'running');

            // Validate integration
            $this->validateIntegration();

            // Perform health check
            $this->performHealthCheck($integrationService);

            // Execute sync based on type
            $result = $this->executeSyncByType($integrationService, $syncId);

            $processingTime = microtime(true) - $startTime;

            // Update sync status
            $this->updateSyncStatus($syncId, 'completed', $result);

            // Update integration last sync
            $this->integration->update([
                'last_sync_at' => now(),
                'last_sync_status' => 'success',
                'last_sync_result' => $result,
                'sync_error_count' => 0,
            ]);

            Log::info('External data sync completed successfully', [
                'sync_id' => $syncId,
                'integration_id' => $this->integration->id,
                'processing_time' => $processingTime,
                'records_processed' => $result['records_processed'] ?? 0,
                'records_created' => $result['records_created'] ?? 0,
                'records_updated' => $result['records_updated'] ?? 0,
                'records_failed' => $result['records_failed'] ?? 0,
            ]);

        } catch (\Exception $e) {
            $this->handleSyncFailure($e, $syncId, microtime(true) - $startTime);
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('External data sync failed permanently', [
            'integration_id' => $this->integration->id,
            'integration_name' => $this->integration->name,
            'sync_type' => $this->syncType,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update integration status
        $this->integration->update([
            'last_sync_at' => now(),
            'last_sync_status' => 'failed',
            'last_sync_error' => $exception->getMessage(),
            'sync_error_count' => $this->integration->sync_error_count + 1,
        ]);

        // Disable integration if too many failures
        if ($this->integration->sync_error_count >= 5) {
            $this->integration->update([
                'is_active' => false,
                'status' => 'disabled_due_to_errors',
            ]);

            Log::warning('Integration disabled due to repeated sync failures', [
                'integration_id' => $this->integration->id,
                'error_count' => $this->integration->sync_error_count,
            ]);
        }

        // Clean up any resources
        $this->cleanup();
    }

    /**
     * Validate integration before sync
     */
    protected function validateIntegration(): void
    {
        if (!$this->integration->is_active) {
            throw new \Exception("Integration {$this->integration->name} is not active");
        }

        if ($this->integration->status === 'maintenance') {
            throw new \Exception("Integration {$this->integration->name} is in maintenance mode");
        }

        // Check credentials
        if (empty($this->integration->credentials)) {
            throw new \Exception("Integration {$this->integration->name} has no credentials configured");
        }

        // Check if sync is already running
        if ($this->isSyncAlreadyRunning()) {
            throw new \Exception("Sync is already running for integration {$this->integration->name}");
        }
    }

    /**
     * Perform health check
     */
    protected function performHealthCheck(ExternalIntegrationService $integrationService): void
    {
        $healthResult = $integrationService->performHealthCheck($this->integration);
        
        if (!$healthResult['healthy']) {
            throw new \Exception("Health check failed: {$healthResult['error']}");
        }
    }

    /**
     * Execute sync based on type
     */
    protected function executeSyncByType(ExternalIntegrationService $integrationService, string $syncId): array
    {
        return match ($this->syncType) {
            'full' => $this->executeFullSync($integrationService, $syncId),
            'incremental' => $this->executeIncrementalSync($integrationService, $syncId),
            'delta' => $this->executeDeltaSync($integrationService, $syncId),
            'resource' => $this->executeResourceSync($integrationService, $syncId),
            'custom' => $this->executeCustomSync($integrationService, $syncId),
            default => throw new \Exception("Unknown sync type: {$this->syncType}"),
        };
    }

    /**
     * Execute full sync
     */
    protected function executeFullSync(ExternalIntegrationService $integrationService, string $syncId): array
    {
        Log::info('Executing full sync', ['sync_id' => $syncId]);
        
        $result = [
            'sync_type' => 'full',
            'records_processed' => 0,
            'records_created' => 0,
            'records_updated' => 0,
            'records_failed' => 0,
            'errors' => [],
        ];

        // Get all resources to sync
        $resources = $this->getResourcesToSync();
        
        foreach ($resources as $resource) {
            try {
                $resourceResult = $integrationService->syncResource(
                    $this->integration,
                    $resource,
                    $this->syncOptions
                );
                
                $result['records_processed'] += $resourceResult['processed'] ?? 0;
                $result['records_created'] += $resourceResult['created'] ?? 0;
                $result['records_updated'] += $resourceResult['updated'] ?? 0;
                
            } catch (\Exception $e) {
                $result['records_failed']++;
                $result['errors'][] = [
                    'resource' => $resource,
                    'error' => $e->getMessage(),
                ];
                
                Log::warning('Resource sync failed', [
                    'sync_id' => $syncId,
                    'resource' => $resource,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $result;
    }

    /**
     * Execute incremental sync
     */
    protected function executeIncrementalSync(ExternalIntegrationService $integrationService, string $syncId): array
    {
        Log::info('Executing incremental sync', ['sync_id' => $syncId]);
        
        $lastSyncAt = $this->integration->last_sync_at ?? now()->subDays(7);
        
        return $integrationService->syncIncrementalData(
            $this->integration,
            $lastSyncAt,
            $this->syncOptions
        );
    }

    /**
     * Execute delta sync
     */
    protected function executeDeltaSync(ExternalIntegrationService $integrationService, string $syncId): array
    {
        Log::info('Executing delta sync', ['sync_id' => $syncId]);
        
        return $integrationService->syncDeltaChanges(
            $this->integration,
            $this->syncOptions
        );
    }

    /**
     * Execute resource-specific sync
     */
    protected function executeResourceSync(ExternalIntegrationService $integrationService, string $syncId): array
    {
        if (!$this->resourceType) {
            throw new \Exception('Resource type is required for resource sync');
        }

        Log::info('Executing resource sync', [
            'sync_id' => $syncId,
            'resource_type' => $this->resourceType,
        ]);
        
        return $integrationService->syncSpecificResource(
            $this->integration,
            $this->resourceType,
            $this->filters ?? [],
            $this->syncOptions
        );
    }

    /**
     * Execute custom sync
     */
    protected function executeCustomSync(ExternalIntegrationService $integrationService, string $syncId): array
    {
        Log::info('Executing custom sync', ['sync_id' => $syncId]);
        
        return $integrationService->executeCustomSync(
            $this->integration,
            $this->syncOptions
        );
    }

    /**
     * Determine queue based on integration priority
     */
    protected function determineQueue(): string
    {
        $priority = $this->integration->priority ?? 'normal';
        
        return match ($priority) {
            'critical' => 'sync-critical',
            'high' => 'sync-high',
            'low' => 'sync-low',
            default => 'sync-normal',
        };
    }

    /**
     * Update sync status in cache
     */
    protected function updateSyncStatus(string $syncId, string $status, ?array $result = null): void
    {
        $statusData = [
            'sync_id' => $syncId,
            'integration_id' => $this->integration->id,
            'status' => $status,
            'sync_type' => $this->syncType,
            'resource_type' => $this->resourceType,
            'updated_at' => now()->toISOString(),
            'attempt' => $this->attempts(),
        ];

        if ($result) {
            $statusData['result'] = $result;
        }

        Cache::put("sync_status:{$syncId}", $statusData, 3600);
        Cache::put("integration_sync:{$this->integration->id}", $statusData, 3600);
    }

    /**
     * Check if sync is already running
     */
    protected function isSyncAlreadyRunning(): bool
    {
        $syncStatus = Cache::get("integration_sync:{$this->integration->id}");
        return $syncStatus && $syncStatus['status'] === 'running';
    }

    /**
     * Get resources to sync
     */
    protected function getResourcesToSync(): array
    {
        $config = $this->integration->sync_config ?? [];
        return $config['resources'] ?? ['default'];
    }

    /**
     * Handle sync failure
     */
    protected function handleSyncFailure(\Exception $e, string $syncId, float $processingTime): void
    {
        Log::warning('External data sync failed', [
            'sync_id' => $syncId,
            'integration_id' => $this->integration->id,
            'error' => $e->getMessage(),
            'attempt' => $this->attempts(),
            'processing_time' => $processingTime,
        ]);

        // Update sync status
        $this->updateSyncStatus($syncId, 'failed', ['error' => $e->getMessage()]);

        // Update integration
        $this->integration->update([
            'last_sync_at' => now(),
            'last_sync_status' => 'failed',
            'last_sync_error' => $e->getMessage(),
        ]);

        // Re-throw to trigger retry mechanism
        throw $e;
    }

    /**
     * Cleanup resources
     */
    protected function cleanup(): void
    {
        // Remove temporary cache entries
        Cache::forget("sync_lock:{$this->integration->id}");
        
        // Clean up any temporary files or resources
        // Implementation depends on specific requirements
    }
}
