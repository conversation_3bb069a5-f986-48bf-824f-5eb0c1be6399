<!-- Notifications Modal -->
<div id="notifications-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-bell text-2xl mr-3"></i>
                    <div>
                        <h2 class="text-xl font-bold">مركز الإشعارات</h2>
                        <p class="text-purple-100 text-sm">{{ $notifications['unread_count'] ?? 0 }} إشعار غير مقروء</p>
                    </div>
                </div>
                <button onclick="hideModal('notifications-modal')" class="text-white hover:text-gray-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Notification Filters -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center space-x-4 space-x-reverse">
                <button class="notification-filter active bg-purple-100 text-purple-800 px-4 py-2 rounded-lg text-sm font-medium" data-filter="all">
                    الكل
                </button>
                <button class="notification-filter text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg text-sm font-medium" data-filter="critical">
                    عاجل
                </button>
                <button class="notification-filter text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg text-sm font-medium" data-filter="financial">
                    مالي
                </button>
                <button class="notification-filter text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg text-sm font-medium" data-filter="projects">
                    مشاريع
                </button>
                <button class="notification-filter text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg text-sm font-medium" data-filter="support">
                    دعم فني
                </button>
                
                <div class="mr-auto flex items-center space-x-2 space-x-reverse">
                    <button class="text-gray-600 hover:text-purple-600 text-sm" onclick="markAllAsRead()">
                        <i class="fas fa-check-double mr-1"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button class="text-gray-600 hover:text-purple-600 text-sm" onclick="clearAllNotifications()">
                        <i class="fas fa-trash mr-1"></i>
                        مسح الكل
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div id="notifications-container" class="overflow-y-auto max-h-96">
            <!-- Critical Notifications -->
            <div class="notification-item critical" data-category="critical financial">
                <div class="p-4 border-b border-gray-200 hover:bg-red-50 border-r-4 border-red-500">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-red-600"></i>
                            </div>
                        </div>
                        <div class="mr-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">فاتورة متأخرة - عاجل</p>
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">عاجل</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">فاتورة رقم INV-2024-001 من شركة ABC متأخرة بـ 5 أيام (125,000 ر.س)</p>
                            <div class="flex items-center justify-between mt-3">
                                <p class="text-xs text-gray-400">منذ ساعتين</p>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        عرض الفاتورة
                                    </button>
                                    <button class="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">
                                        إرسال تذكير
                                    </button>
                                    <button class="text-xs text-gray-500 hover:text-gray-700" onclick="dismissNotification(1)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Notification -->
            <div class="notification-item" data-category="projects">
                <div class="p-4 border-b border-gray-200 hover:bg-gray-50">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-flag-checkered text-green-600"></i>
                            </div>
                        </div>
                        <div class="mr-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">إنجاز مرحلة مشروع</p>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">مكتمل</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">تم إنجاز المرحلة الثانية من مشروع تطوير الموقع بنجاح</p>
                            <div class="flex items-center justify-between mt-3">
                                <p class="text-xs text-gray-400">منذ 4 ساعات</p>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        عرض المشروع
                                    </button>
                                    <button class="text-xs text-gray-500 hover:text-gray-700" onclick="dismissNotification(2)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Ticket Notification -->
            <div class="notification-item critical" data-category="critical support">
                <div class="p-4 border-b border-gray-200 hover:bg-yellow-50 border-r-4 border-yellow-500">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-headset text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="mr-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">تذكرة دعم جديدة - أولوية عالية</p>
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">عالية</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">تذكرة دعم عاجلة من العميل ABC Company - مشكلة في النظام</p>
                            <div class="flex items-center justify-between mt-3">
                                <p class="text-xs text-gray-400">منذ 6 ساعات</p>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        عرض التذكرة
                                    </button>
                                    <button class="text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">
                                        تعيين مهندس
                                    </button>
                                    <button class="text-xs text-gray-500 hover:text-gray-700" onclick="dismissNotification(3)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Notification -->
            <div class="notification-item" data-category="financial">
                <div class="p-4 border-b border-gray-200 hover:bg-gray-50">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-blue-600"></i>
                            </div>
                        </div>
                        <div class="mr-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">دفعة جديدة مستلمة</p>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">مالي</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">تم استلام دفعة بقيمة 75,000 ر.س من شركة XYZ</p>
                            <div class="flex items-center justify-between mt-3">
                                <p class="text-xs text-gray-400">منذ 8 ساعات</p>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        عرض التفاصيل
                                    </button>
                                    <button class="text-xs text-gray-500 hover:text-gray-700" onclick="dismissNotification(4)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Notification -->
            <div class="notification-item" data-category="system">
                <div class="p-4 border-b border-gray-200 hover:bg-gray-50">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-cog text-purple-600"></i>
                            </div>
                        </div>
                        <div class="mr-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">تحديث النظام</p>
                                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">نظام</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">تم تحديث النظام إلى الإصدار 2.1.5 بنجاح</p>
                            <div class="flex items-center justify-between mt-3">
                                <p class="text-xs text-gray-400">منذ يوم واحد</p>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                        عرض التغييرات
                                    </button>
                                    <button class="text-xs text-gray-500 hover:text-gray-700" onclick="dismissNotification(5)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="p-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-purple-600 mr-2">
                        <span class="text-sm text-gray-600">تفعيل الإشعارات الصوتية</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-purple-600 mr-2" checked>
                        <span class="text-sm text-gray-600">إشعارات البريد الإلكتروني</span>
                    </label>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition duration-300 text-sm">
                        إعدادات الإشعارات
                    </button>
                    <button onclick="hideModal('notifications-modal')" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-300 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Notification Modal JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل فلاتر الإشعارات
    const filterButtons = document.querySelectorAll('.notification-filter');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-purple-100', 'text-purple-800');
                btn.classList.add('text-gray-600', 'hover:bg-gray-100');
            });
            
            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active', 'bg-purple-100', 'text-purple-800');
            this.classList.remove('text-gray-600', 'hover:bg-gray-100');
            
            // تطبيق الفلتر
            const filter = this.getAttribute('data-filter');
            filterNotifications(filter);
        });
    });
});

function filterNotifications(filter) {
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(notification => {
        if (filter === 'all') {
            notification.style.display = 'block';
        } else {
            const categories = notification.getAttribute('data-category');
            if (categories && categories.includes(filter)) {
                notification.style.display = 'block';
            } else {
                notification.style.display = 'none';
            }
        }
    });
}

function markAllAsRead() {
    const notifications = document.querySelectorAll('.notification-item');
    notifications.forEach(notification => {
        notification.classList.add('read');
        notification.style.opacity = '0.7';
    });
    
    // تحديث عداد الإشعارات
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.style.display = 'none';
    }
    
    showNotification('تم تحديد جميع الإشعارات كمقروءة', 'success');
}

function clearAllNotifications() {
    if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
        const container = document.getElementById('notifications-container');
        container.innerHTML = '<div class="p-8 text-center text-gray-500">لا توجد إشعارات</div>';
        
        // تحديث عداد الإشعارات
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.style.display = 'none';
        }
        
        showNotification('تم حذف جميع الإشعارات', 'info');
    }
}

function dismissNotification(id) {
    const notification = document.querySelector(`[onclick="dismissNotification(${id})"]`).closest('.notification-item');
    if (notification) {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
}

// تحديث الإشعارات كل دقيقة
setInterval(() => {
    // محاكاة تحديث الإشعارات
    console.log('Checking for new notifications...');
}, 60000);
</script>

<style>
@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.notification-item.read {
    opacity: 0.7;
}

.notification-item:hover {
    transform: translateX(-2px);
    transition: transform 0.2s ease;
}
</style>
