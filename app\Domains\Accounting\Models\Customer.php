<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج العميل
 * يمثل العملاء في النظام المحاسبي
 */
class Customer extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'customer_code',
        'name',
        'name_ar',
        'email',
        'phone',
        'mobile',
        'website',
        'tax_number',
        'ice_number',
        'rc_number',
        'if_number',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'customer_type',
        'payment_terms',
        'credit_limit',
        'currency',
        'language',
        'is_active',
        'notes',
        'contact_person',
        'contact_title',
        'contact_email',
        'contact_phone',
        'bank_name',
        'bank_account',
        'iban',
        'swift_code',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'credit_limit' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الفواتير
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * العلاقة مع القيود المحاسبية
     */
    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class, 'source_id')
                    ->where('source_type', 'CUSTOMER');
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Payment::class);
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * الحصول على العنوان الكامل
     */
    public function getFullAddressAttribute(): string
    {
        $address = [];
        
        if ($this->address_line_1) $address[] = $this->address_line_1;
        if ($this->address_line_2) $address[] = $this->address_line_2;
        if ($this->city) $address[] = $this->city;
        if ($this->state) $address[] = $this->state;
        if ($this->postal_code) $address[] = $this->postal_code;
        if ($this->country) $address[] = $this->country;

        return implode(', ', $address);
    }

    /**
     * الحصول على إجمالي المبيعات
     */
    public function getTotalSalesAttribute(): float
    {
        return $this->invoices()
                    ->where('status', 'PAID')
                    ->sum('total_amount');
    }

    /**
     * الحصول على الرصيد المستحق
     */
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->invoices()
                    ->whereIn('status', ['SENT', 'OVERDUE'])
                    ->sum('total_amount');
    }

    /**
     * الحصول على عدد الفواتير
     */
    public function getInvoicesCountAttribute(): int
    {
        return $this->invoices()->count();
    }

    /**
     * الحصول على آخر فاتورة
     */
    public function getLastInvoiceAttribute(): ?Invoice
    {
        return $this->invoices()
                    ->orderBy('invoice_date', 'desc')
                    ->first();
    }

    /**
     * الحصول على متوسط قيمة الفاتورة
     */
    public function getAverageInvoiceValueAttribute(): float
    {
        $invoicesCount = $this->invoices_count;
        return $invoicesCount > 0 ? $this->total_sales / $invoicesCount : 0;
    }

    /**
     * التحقق من تجاوز الحد الائتماني
     */
    public function isOverCreditLimit(): bool
    {
        if (!$this->credit_limit) {
            return false;
        }

        return $this->outstanding_balance > $this->credit_limit;
    }

    /**
     * الحصول على حالة الائتمان
     */
    public function getCreditStatusAttribute(): string
    {
        if (!$this->credit_limit) {
            return 'NO_LIMIT';
        }

        $utilizationPercentage = ($this->outstanding_balance / $this->credit_limit) * 100;

        if ($utilizationPercentage >= 100) {
            return 'OVER_LIMIT';
        } elseif ($utilizationPercentage >= 80) {
            return 'NEAR_LIMIT';
        } else {
            return 'WITHIN_LIMIT';
        }
    }

    /**
     * الحصول على أيام الدفع المتوسطة
     */
    public function getAveragePaymentDaysAttribute(): float
    {
        $paidInvoices = $this->invoices()
                             ->where('status', 'PAID')
                             ->whereNotNull('paid_at')
                             ->get();

        if ($paidInvoices->isEmpty()) {
            return 0;
        }

        $totalDays = $paidInvoices->sum(function ($invoice) {
            return $invoice->invoice_date->diffInDays($invoice->paid_at);
        });

        return $totalDays / $paidInvoices->count();
    }

    /**
     * توليد رمز العميل
     */
    public static function generateCustomerCode(): string
    {
        $lastCustomer = self::orderBy('customer_code', 'desc')->first();
        
        if (!$lastCustomer) {
            return 'CUST-000001';
        }

        $lastNumber = (int) substr($lastCustomer->customer_code, -6);
        $newNumber = $lastNumber + 1;

        return 'CUST-' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * البحث في العملاء
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('name_ar', 'LIKE', "%{$search}%")
              ->orWhere('customer_code', 'LIKE', "%{$search}%")
              ->orWhere('email', 'LIKE', "%{$search}%")
              ->orWhere('phone', 'LIKE', "%{$search}%")
              ->orWhere('tax_number', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة العملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('customer_type', $type);
    }

    /**
     * فلترة حسب البلد
     */
    public function scopeFromCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * فلترة العملاء المتجاوزين للحد الائتماني
     */
    public function scopeOverCreditLimit($query)
    {
        return $query->whereNotNull('credit_limit')
                    ->whereHas('invoices', function ($q) {
                        $q->whereIn('status', ['SENT', 'OVERDUE'])
                          ->havingRaw('SUM(total_amount) > customers.credit_limit');
                    });
    }

    /**
     * فلترة العملاء مع فواتير متأخرة
     */
    public function scopeWithOverdueInvoices($query)
    {
        return $query->whereHas('invoices', function ($q) {
            $q->where('status', 'OVERDUE');
        });
    }

    /**
     * الحصول على أفضل العملاء
     */
    public function scopeTopCustomers($query, int $limit = 10)
    {
        return $query->withSum(['invoices as total_sales' => function ($q) {
                        $q->where('status', 'PAID');
                    }], 'total_amount')
                    ->orderBy('total_sales', 'desc')
                    ->limit($limit);
    }

    /**
     * تحديث الإحصائيات
     */
    public function updateStatistics(): void
    {
        // يمكن إضافة منطق تحديث الإحصائيات هنا
        $this->touch();
    }

    /**
     * إنشاء فاتورة جديدة
     */
    public function createInvoice(array $invoiceData): Invoice
    {
        $invoiceData['customer_id'] = $this->id;
        $invoiceData['currency'] = $invoiceData['currency'] ?? $this->currency ?? 'MAD';
        $invoiceData['payment_terms'] = $invoiceData['payment_terms'] ?? $this->payment_terms ?? 'Net 30';

        return Invoice::create($invoiceData);
    }

    /**
     * تصدير بيانات العميل
     */
    public function toExportArray(): array
    {
        return [
            'customer_code' => $this->customer_code,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'country' => $this->country,
            'total_sales' => $this->total_sales,
            'outstanding_balance' => $this->outstanding_balance,
            'invoices_count' => $this->invoices_count,
            'average_payment_days' => $this->average_payment_days,
            'credit_status' => $this->credit_status,
        ];
    }

    /**
     * الحصول على تقرير العميل
     */
    public function getCustomerReport(array $dateRange = []): array
    {
        $startDate = $dateRange['start_date'] ?? now()->startOfYear();
        $endDate = $dateRange['end_date'] ?? now();

        $invoices = $this->invoices()
                         ->whereBetween('invoice_date', [$startDate, $endDate])
                         ->get();

        return [
            'customer_info' => [
                'name' => $this->name,
                'code' => $this->customer_code,
                'email' => $this->email,
                'phone' => $this->phone,
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
            'summary' => [
                'total_invoices' => $invoices->count(),
                'total_amount' => $invoices->sum('total_amount'),
                'paid_amount' => $invoices->where('status', 'PAID')->sum('total_amount'),
                'outstanding_amount' => $invoices->whereIn('status', ['SENT', 'OVERDUE'])->sum('total_amount'),
            ],
            'invoices' => $invoices->map(function ($invoice) {
                return [
                    'invoice_number' => $invoice->invoice_number,
                    'date' => $invoice->invoice_date,
                    'amount' => $invoice->total_amount,
                    'status' => $invoice->status,
                ];
            }),
        ];
    }
}
