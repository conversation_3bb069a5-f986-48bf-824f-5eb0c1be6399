<?php

namespace App\Domains\Integration\Services\Transformation\Processors;

use App\Domains\Integration\Contracts\ProcessorInterface;
use App\Domains\Integration\Exceptions\TransformationException;
use Illuminate\Support\Facades\Log;

/**
 * CSV Data Processor
 * Handles CSV data transformation, validation, and processing
 */
class CsvProcessor implements ProcessorInterface
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'delimiter' => ',',
            'enclosure' => '"',
            'escape' => '\\',
            'has_header' => true,
            'encoding' => 'UTF-8',
            'skip_empty_lines' => true,
            'trim_values' => true,
        ], $config);
    }

    /**
     * Process CSV data
     */
    public function process(mixed $data, array $options = []): array
    {
        try {
            if (is_array($data)) {
                return $data;
            }

            if (is_string($data)) {
                return $this->csvToArray($data, $options);
            }

            throw new TransformationException('Invalid data type for CSV processing');
        } catch (\Exception $e) {
            Log::error('CSV processing failed', [
                'error' => $e->getMessage(),
                'data_type' => gettype($data),
            ]);
            throw new TransformationException('CSV processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Convert CSV string to array
     */
    public function csvToArray(string $csv, array $options = []): array
    {
        $delimiter = $options['delimiter'] ?? $this->config['delimiter'];
        $enclosure = $options['enclosure'] ?? $this->config['enclosure'];
        $escape = $options['escape'] ?? $this->config['escape'];
        $hasHeader = $options['has_header'] ?? $this->config['has_header'];
        $skipEmpty = $options['skip_empty_lines'] ?? $this->config['skip_empty_lines'];
        $trimValues = $options['trim_values'] ?? $this->config['trim_values'];

        // Convert encoding if needed
        $csv = $this->convertEncoding($csv, $options['encoding'] ?? $this->config['encoding']);

        // Split into lines
        $lines = str_getcsv($csv, "\n");
        $result = [];
        $headers = [];

        foreach ($lines as $index => $line) {
            if ($skipEmpty && empty(trim($line))) {
                continue;
            }

            $row = str_getcsv($line, $delimiter, $enclosure, $escape);

            if ($trimValues) {
                $row = array_map('trim', $row);
            }

            if ($hasHeader && $index === 0) {
                $headers = $row;
                continue;
            }

            if ($hasHeader && !empty($headers)) {
                // Create associative array using headers
                $associativeRow = [];
                foreach ($headers as $headerIndex => $header) {
                    $associativeRow[$header] = $row[$headerIndex] ?? null;
                }
                $result[] = $associativeRow;
            } else {
                // Use numeric indices
                $result[] = $row;
            }
        }

        return $result;
    }

    /**
     * Convert array to CSV string
     */
    public function arrayToCsv(array $data, array $options = []): string
    {
        if (empty($data)) {
            return '';
        }

        $delimiter = $options['delimiter'] ?? $this->config['delimiter'];
        $enclosure = $options['enclosure'] ?? $this->config['enclosure'];
        $escape = $options['escape'] ?? $this->config['escape'];
        $includeHeader = $options['include_header'] ?? $this->config['has_header'];

        $output = fopen('php://temp', 'r+');
        
        // Write header if needed
        if ($includeHeader && is_array($data[0])) {
            $headers = array_keys($data[0]);
            fputcsv($output, $headers, $delimiter, $enclosure, $escape);
        }

        // Write data rows
        foreach ($data as $row) {
            if (is_array($row)) {
                fputcsv($output, $row, $delimiter, $enclosure, $escape);
            } else {
                fputcsv($output, [$row], $delimiter, $enclosure, $escape);
            }
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }

    /**
     * Validate CSV data
     */
    public function validate(mixed $data, array $schema = []): bool
    {
        try {
            if (is_string($data)) {
                $array = $this->csvToArray($data);
                
                // Validate structure if schema provided
                if (!empty($schema)) {
                    return $this->validateSchema($array, $schema);
                }
                
                return true;
            }
            
            return is_array($data);
        } catch (\Exception $e) {
            Log::error('CSV validation failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Validate CSV data against schema
     */
    protected function validateSchema(array $data, array $schema): bool
    {
        if (empty($data)) {
            return true;
        }

        $requiredColumns = $schema['required_columns'] ?? [];
        $columnTypes = $schema['column_types'] ?? [];
        $minRows = $schema['min_rows'] ?? 0;
        $maxRows = $schema['max_rows'] ?? PHP_INT_MAX;

        // Check row count
        if (count($data) < $minRows || count($data) > $maxRows) {
            return false;
        }

        $firstRow = $data[0];
        
        // Check required columns
        foreach ($requiredColumns as $column) {
            if (!array_key_exists($column, $firstRow)) {
                return false;
            }
        }

        // Check column types
        foreach ($data as $row) {
            foreach ($columnTypes as $column => $type) {
                if (isset($row[$column]) && !$this->validateColumnType($row[$column], $type)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate column type
     */
    protected function validateColumnType(mixed $value, string $type): bool
    {
        switch ($type) {
            case 'string':
                return is_string($value);
            case 'integer':
                return is_numeric($value) && (int)$value == $value;
            case 'float':
                return is_numeric($value);
            case 'boolean':
                return in_array(strtolower($value), ['true', 'false', '1', '0', 'yes', 'no']);
            case 'date':
                return strtotime($value) !== false;
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            default:
                return true;
        }
    }

    /**
     * Transform CSV data using mapping rules
     */
    public function transform(array $data, array $mapping): array
    {
        $result = [];
        
        foreach ($data as $row) {
            $transformedRow = [];
            
            foreach ($mapping as $targetKey => $sourceKey) {
                if (is_array($sourceKey)) {
                    // Complex mapping with transformation rules
                    $transformedRow[$targetKey] = $this->applyTransformationRule($row, $sourceKey);
                } else {
                    // Simple key mapping
                    $transformedRow[$targetKey] = $row[$sourceKey] ?? null;
                }
            }
            
            $result[] = $transformedRow;
        }
        
        return $result;
    }

    /**
     * Apply transformation rule
     */
    protected function applyTransformationRule(array $row, array $rule): mixed
    {
        $source = $rule['source'] ?? null;
        $default = $rule['default'] ?? null;
        $transform = $rule['transform'] ?? null;
        
        $value = $source ? ($row[$source] ?? $default) : $default;
        
        if ($transform && is_callable($transform)) {
            $value = $transform($value);
        }
        
        return $value;
    }

    /**
     * Convert encoding
     */
    protected function convertEncoding(string $data, string $targetEncoding): string
    {
        $currentEncoding = mb_detect_encoding($data, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
        
        if ($currentEncoding && $currentEncoding !== $targetEncoding) {
            return mb_convert_encoding($data, $targetEncoding, $currentEncoding);
        }
        
        return $data;
    }

    /**
     * Get supported formats
     */
    public function getSupportedFormats(): array
    {
        return ['csv', 'text/csv', 'application/csv'];
    }

    /**
     * Check if format is supported
     */
    public function supports(string $format): bool
    {
        return in_array(strtolower($format), $this->getSupportedFormats());
    }

    /**
     * Filter CSV data
     */
    public function filter(array $data, callable $callback): array
    {
        return array_filter($data, $callback);
    }

    /**
     * Sort CSV data
     */
    public function sort(array $data, string $column, string $direction = 'asc'): array
    {
        usort($data, function ($a, $b) use ($column, $direction) {
            $valueA = $a[$column] ?? '';
            $valueB = $b[$column] ?? '';
            
            $comparison = strcmp($valueA, $valueB);
            
            return $direction === 'desc' ? -$comparison : $comparison;
        });
        
        return $data;
    }
}
