<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج مشاهدة الملف - File View
 */
class FileView extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'file_id',
        'user_id',
        'viewed_at',
        'ip_address',
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
    ];

    public function file(): BelongsTo
    {
        return $this->belongsTo(ProjectFile::class, 'file_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'user_id');
    }
}
