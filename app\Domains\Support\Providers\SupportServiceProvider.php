<?php

namespace App\Domains\Support\Providers;

use Illuminate\Support\ServiceProvider;

class SupportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل التكوينات
        $this->mergeConfigFrom(
            config_path('support.php'),
            'support'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل الطرق
        $this->loadRoutesFrom(__DIR__ . '/../Routes/web.php');
        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');

        // تحميل المشاهدات
        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'support');

        // تحميل الترجمات
        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'support');

        // تحميل Migrations
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        // نشر الملفات
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/vendor/support'),
            ], 'support-views');

            $this->publishes([
                __DIR__ . '/../Resources/lang' => resource_path('lang/vendor/support'),
            ], 'support-lang');
        }
    }
}
