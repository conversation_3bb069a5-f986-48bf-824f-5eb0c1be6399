<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceWebhook;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث استقبال Webhook
 * يتم إطلاقه عند استقبال webhook من المنصة
 */
class WebhookReceived
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceWebhook $webhook;
    public ECommerceIntegration $integration;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(ECommerceWebhook $webhook, ECommerceIntegration $integration)
    {
        $this->webhook = $webhook;
        $this->integration = $integration;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'webhook_id' => $this->webhook->id,
            'external_webhook_id' => $this->webhook->webhook_id,
            'event_type' => $this->webhook->event_type,
            'event_name' => $this->webhook->event_name,
            'topic' => $this->webhook->topic,
            'resource' => $this->webhook->resource,
            'resource_id' => $this->webhook->resource_id,
            'action' => $this->webhook->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'received_at' => $this->webhook->received_at,
            'ip_address' => $this->webhook->ip_address,
            'user_agent' => $this->webhook->user_agent,
            'delivery_id' => $this->webhook->delivery_id,
            'attempt_number' => $this->webhook->attempt_number,
            'payload_size' => strlen(json_encode($this->webhook->payload)),
        ];
    }

    /**
     * الحصول على نوع الحدث
     */
    public function getEventType(): string
    {
        return $this->webhook->event_type;
    }

    /**
     * الحصول على المورد
     */
    public function getResource(): string
    {
        return $this->webhook->resource ?? '';
    }

    /**
     * الحصول على الإجراء
     */
    public function getAction(): string
    {
        return $this->webhook->action ?? '';
    }

    /**
     * الحصول على البيانات المرسلة
     */
    public function getPayload(): array
    {
        return $this->webhook->getPayload();
    }

    /**
     * تحديد ما إذا كان webhook متعلق بالمنتجات
     */
    public function isProductWebhook(): bool
    {
        return str_contains($this->getEventType(), 'product') ||
               $this->getResource() === 'product';
    }

    /**
     * تحديد ما إذا كان webhook متعلق بالطلبات
     */
    public function isOrderWebhook(): bool
    {
        return str_contains($this->getEventType(), 'order') ||
               $this->getResource() === 'order';
    }

    /**
     * تحديد ما إذا كان webhook متعلق بالعملاء
     */
    public function isCustomerWebhook(): bool
    {
        return str_contains($this->getEventType(), 'customer') ||
               $this->getResource() === 'customer';
    }

    /**
     * تحديد ما إذا كان webhook متعلق بالمخزون
     */
    public function isInventoryWebhook(): bool
    {
        return str_contains($this->getEventType(), 'inventory') ||
               $this->getResource() === 'inventory';
    }
}
