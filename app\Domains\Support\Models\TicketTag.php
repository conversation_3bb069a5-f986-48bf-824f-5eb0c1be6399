<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج وسم التذكرة - Ticket Tag
 */
class TicketTag extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'color',
        'description',
        'is_system',
        'usage_count',
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'usage_count' => 'integer',
    ];

    public function tickets(): BelongsToMany
    {
        return $this->belongsToMany(Ticket::class, 'ticket_tag_pivot');
    }

    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    public function decrementUsage(): void
    {
        $this->decrement('usage_count');
    }

    public function scopePopular($query, int $limit = 10)
    {
        return $query->orderBy('usage_count', 'desc')->limit($limit);
    }

    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }
}
