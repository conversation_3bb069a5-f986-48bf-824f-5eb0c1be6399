<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قسم الدعم - Support Department
 */
class Department extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'description',
        'email',
        'phone',
        'is_active',
        'working_hours',
        'languages',
        'specializations',
        'auto_assignment_enabled',
        'max_tickets_per_agent',
        'escalation_department_id',
        'sla_settings',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'working_hours' => 'array',
        'languages' => 'array',
        'specializations' => 'array',
        'auto_assignment_enabled' => 'boolean',
        'max_tickets_per_agent' => 'integer',
        'sla_settings' => 'array',
    ];

    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    public function categories(): HasMany
    {
        return $this->hasMany(TicketCategory::class);
    }

    public function agents(): BelongsToMany
    {
        return $this->belongsToMany(\App\Domains\HR\Models\Employee::class, 'department_agents');
    }

    public function chats(): HasMany
    {
        return $this->hasMany(LiveChat::class);
    }
}
