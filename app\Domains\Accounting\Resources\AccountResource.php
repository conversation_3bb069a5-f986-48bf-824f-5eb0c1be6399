<?php

namespace App\Domains\Accounting\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Account Resource
 * مورد الحساب المحاسبي
 */
class AccountResource extends JsonResource
{
    /**
     * تحويل المورد إلى مصفوفة
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'account_code' => $this->account_code,
            'account_name' => $this->account_name,
            'account_name_en' => $this->account_name_en,
            'account_type' => $this->account_type,
            'account_type_label' => $this->getAccountTypeLabel(),
            'account_category' => $this->account_category,
            'account_category_label' => $this->getAccountCategoryLabel(),
            'description' => $this->description,
            'level' => $this->level,
            'is_active' => $this->is_active,
            'is_system_account' => $this->is_system_account,
            'allow_manual_entry' => $this->allow_manual_entry,
            'require_cost_center' => $this->require_cost_center,
            'require_project' => $this->require_project,
            'is_debit_nature' => $this->is_debit_nature,
            'is_credit_nature' => $this->is_credit_nature,
            
            // الأرصدة
            'opening_balance' => $this->opening_balance,
            'current_balance' => $this->current_balance,
            'debit_balance' => $this->is_debit_nature && $this->current_balance > 0 ? $this->current_balance : 0,
            'credit_balance' => $this->is_credit_nature && $this->current_balance > 0 ? $this->current_balance : 0,
            
            // العملة
            'currency_code' => $this->currency_code,
            'currency_symbol' => $this->getCurrencySymbol(),
            
            // البيانات البنكية
            'bank_account_number' => $this->bank_account_number,
            'iban' => $this->iban,
            'swift_code' => $this->swift_code,
            'bank_name' => $this->bank_name,
            'reconciliation_enabled' => $this->reconciliation_enabled,
            'auto_reconciliation' => $this->auto_reconciliation,
            
            // العلاقات
            'parent_account' => $this->whenLoaded('parentAccount', function () {
                return [
                    'id' => $this->parentAccount->id,
                    'account_code' => $this->parentAccount->account_code,
                    'account_name' => $this->parentAccount->account_name,
                ];
            }),
            
            'sub_accounts' => $this->whenLoaded('subAccounts', function () {
                return $this->subAccounts->map(function ($subAccount) {
                    return [
                        'id' => $subAccount->id,
                        'account_code' => $subAccount->account_code,
                        'account_name' => $subAccount->account_name,
                        'current_balance' => $subAccount->current_balance,
                        'is_active' => $subAccount->is_active,
                        'sub_accounts_count' => $subAccount->subAccounts()->count(),
                    ];
                });
            }),
            
            'tax_account' => $this->whenLoaded('taxAccount', function () {
                return [
                    'id' => $this->taxAccount->id,
                    'account_code' => $this->taxAccount->account_code,
                    'account_name' => $this->taxAccount->account_name,
                ];
            }),
            
            // الإحصائيات
            'statistics' => $this->when($request->include_statistics, function () {
                return [
                    'transactions_count' => $this->journalEntryDetails()->count(),
                    'last_transaction_date' => $this->getLastTransactionDate(),
                    'ytd_debit_total' => $this->getYearToDateDebitTotal(),
                    'ytd_credit_total' => $this->getYearToDateCreditTotal(),
                    'monthly_activity' => $this->getMonthlyActivity(),
                ];
            }),
            
            // بيانات التدقيق
            'audit_info' => [
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
                'created_by' => $this->whenLoaded('creator', function () {
                    return [
                        'id' => $this->creator->id,
                        'name' => $this->creator->name,
                    ];
                }),
                'updated_by' => $this->whenLoaded('updater', function () {
                    return [
                        'id' => $this->updater->id,
                        'name' => $this->updater->name,
                    ];
                }),
            ],
            
            // الروابط
            'links' => [
                'self' => route('api.accounting.accounts.show', $this->id),
                'edit' => route('api.accounting.accounts.edit', $this->id),
                'history' => route('api.accounting.accounts.history', $this->id),
                'balance' => route('api.accounting.accounts.balance', $this->id),
            ],
            
            // الأذونات
            'permissions' => $this->when(auth()->check(), function () {
                return [
                    'can_view' => auth()->user()->can('view', $this->resource),
                    'can_edit' => auth()->user()->can('update', $this->resource),
                    'can_delete' => auth()->user()->can('delete', $this->resource),
                    'can_adjust_balance' => auth()->user()->can('adjustBalance', $this->resource),
                    'can_view_history' => auth()->user()->can('viewHistory', $this->resource),
                ];
            }),
        ];
    }

    /**
     * الحصول على تسمية نوع الحساب
     */
    protected function getAccountTypeLabel(): string
    {
        return match ($this->account_type) {
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
            default => $this->account_type,
        };
    }

    /**
     * الحصول على تسمية فئة الحساب
     */
    protected function getAccountCategoryLabel(): string
    {
        return match ($this->account_category) {
            'current_assets' => 'أصول متداولة',
            'fixed_assets' => 'أصول ثابتة',
            'intangible_assets' => 'أصول غير ملموسة',
            'current_liabilities' => 'خصوم متداولة',
            'long_term_liabilities' => 'خصوم طويلة الأجل',
            'capital' => 'رأس المال',
            'retained_earnings' => 'أرباح محتجزة',
            'operating_revenue' => 'إيرادات تشغيلية',
            'other_revenue' => 'إيرادات أخرى',
            'cost_of_goods_sold' => 'تكلفة البضاعة المباعة',
            'operating_expenses' => 'مصروفات تشغيلية',
            'administrative_expenses' => 'مصروفات إدارية',
            'financial_expenses' => 'مصروفات مالية',
            'other_expenses' => 'مصروفات أخرى',
            default => $this->account_category,
        };
    }

    /**
     * الحصول على رمز العملة
     */
    protected function getCurrencySymbol(): string
    {
        return match ($this->currency_code) {
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'AED' => 'د.إ',
            'EGP' => 'ج.م',
            default => $this->currency_code,
        };
    }

    /**
     * الحصول على تاريخ آخر معاملة
     */
    protected function getLastTransactionDate(): ?string
    {
        $lastTransaction = $this->journalEntryDetails()
            ->whereHas('journalEntry', function ($query) {
                $query->where('status', 'POSTED');
            })
            ->orderBy('created_at', 'desc')
            ->first();

        return $lastTransaction ? $lastTransaction->created_at->format('Y-m-d') : null;
    }

    /**
     * الحصول على إجمالي المدين من بداية السنة
     */
    protected function getYearToDateDebitTotal(): float
    {
        return $this->journalEntryDetails()
            ->whereHas('journalEntry', function ($query) {
                $query->where('status', 'POSTED')
                      ->whereYear('entry_date', now()->year);
            })
            ->where('entry_type', 'debit')
            ->sum('amount');
    }

    /**
     * الحصول على إجمالي الدائن من بداية السنة
     */
    protected function getYearToDateCreditTotal(): float
    {
        return $this->journalEntryDetails()
            ->whereHas('journalEntry', function ($query) {
                $query->where('status', 'POSTED')
                      ->whereYear('entry_date', now()->year);
            })
            ->where('entry_type', 'credit')
            ->sum('amount');
    }

    /**
     * الحصول على النشاط الشهري
     */
    protected function getMonthlyActivity(): array
    {
        $monthlyData = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->format('Y-m');
            
            $debits = $this->journalEntryDetails()
                ->whereHas('journalEntry', function ($query) use ($date) {
                    $query->where('status', 'POSTED')
                          ->whereYear('entry_date', $date->year)
                          ->whereMonth('entry_date', $date->month);
                })
                ->where('entry_type', 'debit')
                ->sum('amount');
                
            $credits = $this->journalEntryDetails()
                ->whereHas('journalEntry', function ($query) use ($date) {
                    $query->where('status', 'POSTED')
                          ->whereYear('entry_date', $date->year)
                          ->whereMonth('entry_date', $date->month);
                })
                ->where('entry_type', 'credit')
                ->sum('amount');
            
            $monthlyData[] = [
                'month' => $month,
                'month_name' => $date->translatedFormat('F Y'),
                'debits' => $debits,
                'credits' => $credits,
                'net_change' => $this->is_debit_nature ? ($debits - $credits) : ($credits - $debits),
            ];
        }
        
        return $monthlyData;
    }

    /**
     * البيانات الإضافية مع الاستجابة
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'account_types' => [
                    'asset' => 'أصول',
                    'liability' => 'خصوم',
                    'equity' => 'حقوق ملكية',
                    'revenue' => 'إيرادات',
                    'expense' => 'مصروفات',
                ],
                'account_categories' => [
                    'current_assets' => 'أصول متداولة',
                    'fixed_assets' => 'أصول ثابتة',
                    'intangible_assets' => 'أصول غير ملموسة',
                    'current_liabilities' => 'خصوم متداولة',
                    'long_term_liabilities' => 'خصوم طويلة الأجل',
                    'capital' => 'رأس المال',
                    'retained_earnings' => 'أرباح محتجزة',
                    'operating_revenue' => 'إيرادات تشغيلية',
                    'other_revenue' => 'إيرادات أخرى',
                    'cost_of_goods_sold' => 'تكلفة البضاعة المباعة',
                    'operating_expenses' => 'مصروفات تشغيلية',
                    'administrative_expenses' => 'مصروفات إدارية',
                    'financial_expenses' => 'مصروفات مالية',
                    'other_expenses' => 'مصروفات أخرى',
                ],
                'supported_currencies' => [
                    'SAR' => 'ريال سعودي',
                    'USD' => 'دولار أمريكي',
                    'EUR' => 'يورو',
                    'GBP' => 'جنيه إسترليني',
                    'AED' => 'درهم إماراتي',
                    'EGP' => 'جنيه مصري',
                ],
            ],
        ];
    }
}
