<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Illuminate\Notifications\Notifiable;

/**
 * نموذج المرشح - نظام التوظيف الذكي المتقدم
 */
class Candidate extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, Notifiable;

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'first_name_ar',
        'middle_name_ar',
        'last_name_ar',
        'email',
        'phone',
        'mobile',
        'date_of_birth',
        'gender',
        'nationality',
        'national_id',
        'passport_number',
        'current_location',
        'willing_to_relocate',
        'visa_status',
        'linkedin_profile',
        'portfolio_url',
        'github_profile',
        
        // Resume & CV
        'resume_file_path',
        'resume_text',
        'cv_parsed_data',
        
        // Professional Info
        'current_position',
        'current_company',
        'current_salary',
        'expected_salary',
        'notice_period',
        'total_experience_years',
        'industry_experience',
        'skills',
        'languages',
        'certifications',
        'achievements',
        
        // Education
        'education',
        'highest_degree',
        'university',
        'graduation_year',
        'gpa',
        
        // Work History
        'work_history',
        
        // AI Analysis
        'ai_profile_score',
        'skills_extracted',
        'personality_traits',
        'career_level',
        'specializations',
        
        // Application Tracking
        'source',
        'referrer_id',
        'status',
        'blacklisted',
        'blacklist_reason',
        'notes',
        'tags',
        
        // Employee Link
        'employee_id',
        
        'metadata',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'willing_to_relocate' => 'boolean',
        'current_salary' => 'decimal:2',
        'expected_salary' => 'decimal:2',
        'total_experience_years' => 'decimal:1',
        'cv_parsed_data' => 'array',
        'skills' => 'array',
        'languages' => 'array',
        'certifications' => 'array',
        'achievements' => 'array',
        'education' => 'array',
        'work_history' => 'array',
        'skills_extracted' => 'array',
        'personality_traits' => 'array',
        'specializations' => 'array',
        'blacklisted' => 'boolean',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * حالات المرشح
     */
    const STATUSES = [
        'NEW' => 'جديد',
        'ACTIVE' => 'نشط',
        'HIRED' => 'تم توظيفه',
        'REJECTED' => 'مرفوض',
        'WITHDRAWN' => 'انسحب',
        'BLACKLISTED' => 'في القائمة السوداء',
        'ON_HOLD' => 'معلق',
    ];

    /**
     * مصادر المرشحين
     */
    const SOURCES = [
        'WEBSITE' => 'موقع الشركة',
        'LINKEDIN' => 'لينكدإن',
        'BAYT' => 'بيت.كوم',
        'AKHTABOOT' => 'اختبوط',
        'EMPLOI_MA' => 'Emploi.ma',
        'APEC' => 'APEC',
        'REFERRAL' => 'إحالة',
        'RECRUITMENT_AGENCY' => 'وكالة توظيف',
        'UNIVERSITY' => 'جامعة',
        'SOCIAL_MEDIA' => 'وسائل التواصل',
        'WALK_IN' => 'زيارة مباشرة',
        'EMAIL' => 'بريد إلكتروني',
        'OTHER' => 'أخرى',
    ];

    /**
     * مستويات الخبرة
     */
    const CAREER_LEVELS = [
        'ENTRY' => 'مبتدئ',
        'JUNIOR' => 'مبتدئ متقدم',
        'MID' => 'متوسط',
        'SENIOR' => 'كبير',
        'LEAD' => 'قائد فريق',
        'MANAGER' => 'مدير',
        'DIRECTOR' => 'مدير عام',
        'EXECUTIVE' => 'تنفيذي',
    ];

    /**
     * طلبات التوظيف
     */
    public function applications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * المقابلات
     */
    public function interviews(): HasMany
    {
        return $this->hasMany(Interview::class);
    }

    /**
     * التقييمات
     */
    public function assessments(): HasMany
    {
        return $this->hasMany(CandidateAssessment::class);
    }

    /**
     * الشخص الذي أحال المرشح
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'referrer_id');
    }

    /**
     * الموظف المرتبط (إذا تم توظيفه)
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * المستندات
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(\App\Domains\Shared\Models\Document::class, 'documentable');
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->middle_name} {$this->last_name}");
    }

    /**
     * الحصول على الاسم الكامل بالعربية
     */
    public function getFullNameArAttribute(): string
    {
        return trim("{$this->first_name_ar} {$this->middle_name_ar} {$this->last_name_ar}");
    }

    /**
     * الحصول على العمر
     */
    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * تحليل السيرة الذاتية بالذكاء الاصطناعي
     */
    public function analyzeResume(): void
    {
        $aiService = app(\App\Domains\HR\Services\AIRecruitmentService::class);
        $analysis = $aiService->analyzeResume($this->resume_text);

        $this->update([
            'skills_extracted' => $analysis['skills'],
            'personality_traits' => $analysis['personality_traits'],
            'career_level' => $analysis['career_level'],
            'specializations' => $analysis['specializations'],
            'ai_profile_score' => $analysis['profile_score'],
            'cv_parsed_data' => $analysis['parsed_data'],
        ]);
    }

    /**
     * استخراج البيانات من السيرة الذاتية
     */
    public function parseResumeData(): array
    {
        if (!$this->resume_text) {
            return [];
        }

        $parser = app(\App\Domains\HR\Services\ResumeParsingService::class);
        return $parser->parseResume($this->resume_text);
    }

    /**
     * حساب نقاط التطابق مع وظيفة
     */
    public function calculateMatchScore(JobPosting $jobPosting): float
    {
        $aiService = app(\App\Domains\HR\Services\AIRecruitmentService::class);
        
        // إنشاء طلب توظيف مؤقت للحساب
        $tempApplication = new JobApplication([
            'candidate_id' => $this->id,
            'job_posting_id' => $jobPosting->id,
        ]);
        $tempApplication->candidate = $this;
        $tempApplication->jobPosting = $jobPosting;

        $scoring = $aiService->scoreCandidate($tempApplication);
        return $scoring['overall_score'];
    }

    /**
     * إضافة للقائمة السوداء
     */
    public function blacklist(string $reason): bool
    {
        return $this->update([
            'blacklisted' => true,
            'blacklist_reason' => $reason,
            'status' => 'BLACKLISTED',
        ]);
    }

    /**
     * إزالة من القائمة السوداء
     */
    public function removeFromBlacklist(): bool
    {
        return $this->update([
            'blacklisted' => false,
            'blacklist_reason' => null,
            'status' => 'ACTIVE',
        ]);
    }

    /**
     * إضافة علامة
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    /**
     * إزالة علامة
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        $this->update(['tags' => array_values($tags)]);
    }

    /**
     * الحصول على آخر طلب توظيف
     */
    public function getLatestApplicationAttribute()
    {
        return $this->applications()->latest()->first();
    }

    /**
     * الحصول على عدد طلبات التوظيف
     */
    public function getApplicationsCountAttribute(): int
    {
        return $this->applications()->count();
    }

    /**
     * التحقق من توفر المرشح
     */
    public function isAvailable(): bool
    {
        return in_array($this->status, ['NEW', 'ACTIVE']) && !$this->blacklisted;
    }

    /**
     * التحقق من كون المرشح موظف حالياً
     */
    public function isCurrentEmployee(): bool
    {
        return $this->status === 'HIRED' && $this->employee_id;
    }

    /**
     * تحديث معلومات الخبرة
     */
    public function updateExperienceInfo(): void
    {
        $workHistory = $this->work_history ?? [];
        $totalYears = 0;
        $industries = [];

        foreach ($workHistory as $job) {
            $startDate = \Carbon\Carbon::parse($job['start_date'] ?? now());
            $endDate = $job['end_date'] ? \Carbon\Carbon::parse($job['end_date']) : now();
            
            $years = $startDate->diffInYears($endDate, true);
            $totalYears += $years;
            
            if (isset($job['industry'])) {
                $industries[] = $job['industry'];
            }
        }

        $this->update([
            'total_experience_years' => $totalYears,
            'industry_experience' => array_unique($industries),
        ]);
    }

    /**
     * إنشاء ملف تعريفي شامل
     */
    public function generateProfile(): array
    {
        return [
            'personal_info' => [
                'name' => $this->full_name,
                'email' => $this->email,
                'phone' => $this->phone,
                'age' => $this->age,
                'nationality' => $this->nationality,
                'location' => $this->current_location,
            ],
            'professional_info' => [
                'current_position' => $this->current_position,
                'current_company' => $this->current_company,
                'experience_years' => $this->total_experience_years,
                'career_level' => $this->career_level,
                'current_salary' => $this->current_salary,
                'expected_salary' => $this->expected_salary,
            ],
            'skills_and_qualifications' => [
                'skills' => $this->skills,
                'languages' => $this->languages,
                'certifications' => $this->certifications,
                'education' => $this->education,
                'highest_degree' => $this->highest_degree,
            ],
            'ai_insights' => [
                'profile_score' => $this->ai_profile_score,
                'personality_traits' => $this->personality_traits,
                'specializations' => $this->specializations,
                'extracted_skills' => $this->skills_extracted,
            ],
            'application_history' => [
                'total_applications' => $this->applications_count,
                'latest_application' => $this->latest_application,
                'source' => $this->source,
            ],
        ];
    }

    /**
     * نطاق للمرشحين المتاحين
     */
    public function scopeAvailable($query)
    {
        return $query->whereIn('status', ['NEW', 'ACTIVE'])
            ->where('blacklisted', false);
    }

    /**
     * نطاق حسب المصدر
     */
    public function scopeBySource($query, string $source)
    {
        return $query->where('source', $source);
    }

    /**
     * نطاق حسب مستوى الخبرة
     */
    public function scopeByCareerLevel($query, string $level)
    {
        return $query->where('career_level', $level);
    }

    /**
     * نطاق حسب المهارات
     */
    public function scopeWithSkill($query, string $skill)
    {
        return $query->whereJsonContains('skills', $skill)
            ->orWhereJsonContains('skills_extracted', $skill);
    }

    /**
     * نطاق حسب سنوات الخبرة
     */
    public function scopeByExperience($query, float $minYears, float $maxYears = null)
    {
        $query->where('total_experience_years', '>=', $minYears);
        
        if ($maxYears) {
            $query->where('total_experience_years', '<=', $maxYears);
        }
        
        return $query;
    }

    /**
     * نطاق حسب نطاق الراتب المتوقع
     */
    public function scopeBySalaryRange($query, float $minSalary, float $maxSalary = null)
    {
        $query->where('expected_salary', '>=', $minSalary);
        
        if ($maxSalary) {
            $query->where('expected_salary', '<=', $maxSalary);
        }
        
        return $query;
    }

    /**
     * نطاق للبحث النصي
     */
    public function scopeSearch($query, string $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('first_name', 'LIKE', "%{$term}%")
              ->orWhere('last_name', 'LIKE', "%{$term}%")
              ->orWhere('email', 'LIKE', "%{$term}%")
              ->orWhere('current_position', 'LIKE', "%{$term}%")
              ->orWhere('current_company', 'LIKE', "%{$term}%")
              ->orWhere('resume_text', 'LIKE', "%{$term}%");
        });
    }

    /**
     * نطاق للمرشحين عالي الجودة
     */
    public function scopeHighQuality($query, float $minScore = 80)
    {
        return $query->where('ai_profile_score', '>=', $minScore);
    }

    /**
     * نطاق حسب الجنسية
     */
    public function scopeByNationality($query, string $nationality)
    {
        return $query->where('nationality', $nationality);
    }

    /**
     * نطاق للمرشحين المستعدين للانتقال
     */
    public function scopeWillingToRelocate($query)
    {
        return $query->where('willing_to_relocate', true);
    }
}
