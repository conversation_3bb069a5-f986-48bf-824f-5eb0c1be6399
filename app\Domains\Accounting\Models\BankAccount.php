<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الحساب البنكي
 * يمثل الحسابات البنكية للشركة
 */
class BankAccount extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'account_id',
        'bank_name',
        'account_number',
        'account_name',
        'iban',
        'swift_code',
        'currency',
        'account_type',
        'current_balance',
        'available_balance',
        'is_active',
        'is_default',
        'bank_code',
        'branch_code',
        'branch_name',
        'contact_person',
        'contact_phone',
        'contact_email',
        'api_credentials',
        'last_sync_at',
        'sync_enabled',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'current_balance' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'sync_enabled' => 'boolean',
        'api_credentials' => 'encrypted:array',
        'last_sync_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الحساب المحاسبي
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * العلاقة مع المعاملات البنكية
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(BankTransaction::class);
    }

    /**
     * الحصول على الاسم الكامل للحساب
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->bank_name} - {$this->account_name} ({$this->account_number})";
    }

    /**
     * الحصول على رقم الحساب المقنع
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        $length = strlen($this->account_number);
        if ($length <= 4) {
            return $this->account_number;
        }
        
        return str_repeat('*', $length - 4) . substr($this->account_number, -4);
    }

    /**
     * الحصول على آخر معاملة
     */
    public function getLastTransactionAttribute(): ?BankTransaction
    {
        return $this->transactions()
                    ->orderBy('transaction_date', 'desc')
                    ->orderBy('id', 'desc')
                    ->first();
    }

    /**
     * الحصول على عدد المعاملات غير المطابقة
     */
    public function getUnmatchedTransactionsCountAttribute(): int
    {
        return $this->transactions()->unmatched()->count();
    }

    /**
     * الحصول على الرصيد المتاح
     */
    public function getAvailableBalanceAttribute(): float
    {
        return $this->attributes['available_balance'] ?? $this->current_balance;
    }

    /**
     * تحديث الرصيد الحالي
     */
    public function updateCurrentBalance(): void
    {
        $lastTransaction = $this->last_transaction;
        
        if ($lastTransaction) {
            $this->update(['current_balance' => $lastTransaction->balance_after]);
        }
    }

    /**
     * مزامنة المعاملات من البنك
     */
    public function syncTransactions(): array
    {
        if (!$this->sync_enabled || !$this->api_credentials) {
            return ['success' => false, 'message' => 'المزامنة غير مفعلة أو بيانات الاتصال مفقودة'];
        }

        try {
            $bankService = $this->getBankService();
            $transactions = $bankService->getTransactions($this->last_sync_at);
            
            $imported = 0;
            foreach ($transactions as $transactionData) {
                $existing = $this->transactions()
                                ->where('bank_reference', $transactionData['reference'])
                                ->first();
                
                if (!$existing) {
                    $this->transactions()->create([
                        'transaction_date' => $transactionData['date'],
                        'description' => $transactionData['description'],
                        'reference' => $transactionData['reference'],
                        'amount' => $transactionData['amount'],
                        'balance_after' => $transactionData['balance'],
                        'bank_reference' => $transactionData['reference'],
                        'counterparty_name' => $transactionData['counterparty'] ?? null,
                        'currency' => $this->currency,
                        'status' => 'UNMATCHED',
                        'imported_at' => now(),
                    ]);
                    $imported++;
                }
            }

            $this->update(['last_sync_at' => now()]);
            $this->updateCurrentBalance();

            return [
                'success' => true,
                'imported' => $imported,
                'message' => "تم استيراد {$imported} معاملة جديدة"
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في المزامنة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على خدمة البنك
     */
    protected function getBankService()
    {
        switch (strtolower($this->bank_name)) {
            case 'attijariwafa bank':
                return new \App\Services\Banking\AttijariwafaBankService($this->api_credentials);
            case 'banque populaire':
                return new \App\Services\Banking\BanquePopulaireService($this->api_credentials);
            case 'al rajhi bank':
                return new \App\Services\Banking\AlRajhiBankService($this->api_credentials);
            default:
                throw new \Exception('خدمة البنك غير مدعومة');
        }
    }

    /**
     * إنشاء معاملة يدوية
     */
    public function createManualTransaction(array $transactionData): BankTransaction
    {
        $transaction = $this->transactions()->create([
            'transaction_date' => $transactionData['date'],
            'description' => $transactionData['description'],
            'reference' => $transactionData['reference'] ?? 'MANUAL-' . time(),
            'amount' => $transactionData['amount'],
            'transaction_type' => 'MANUAL',
            'currency' => $this->currency,
            'status' => 'UNMATCHED',
            'imported_at' => now(),
        ]);

        $transaction->updateBalance();
        
        return $transaction;
    }

    /**
     * الحصول على كشف حساب
     */
    public function getStatement(array $dateRange): array
    {
        $startDate = $dateRange['start_date'] ?? now()->startOfMonth();
        $endDate = $dateRange['end_date'] ?? now()->endOfMonth();

        $transactions = $this->transactions()
                             ->forPeriod($startDate, $endDate)
                             ->orderByDate()
                             ->get();

        $openingBalance = $this->getBalanceAsOf($startDate);
        $closingBalance = $this->getBalanceAsOf($endDate);

        return [
            'account_info' => [
                'bank_name' => $this->bank_name,
                'account_name' => $this->account_name,
                'account_number' => $this->masked_account_number,
                'currency' => $this->currency,
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
            'balances' => [
                'opening_balance' => $openingBalance,
                'closing_balance' => $closingBalance,
                'net_change' => $closingBalance - $openingBalance,
            ],
            'transactions' => $transactions->map(function ($transaction) {
                return [
                    'date' => $transaction->transaction_date,
                    'description' => $transaction->description,
                    'reference' => $transaction->reference,
                    'debit' => $transaction->isDebit() ? $transaction->absolute_amount : null,
                    'credit' => $transaction->isCredit() ? $transaction->absolute_amount : null,
                    'balance' => $transaction->balance_after,
                    'status' => $transaction->status,
                ];
            }),
            'summary' => [
                'total_transactions' => $transactions->count(),
                'total_debits' => $transactions->where('amount', '<', 0)->sum('amount'),
                'total_credits' => $transactions->where('amount', '>', 0)->sum('amount'),
            ],
        ];
    }

    /**
     * الحصول على الرصيد في تاريخ معين
     */
    public function getBalanceAsOf(\Carbon\Carbon $date): float
    {
        $lastTransaction = $this->transactions()
                               ->where('transaction_date', '<=', $date)
                               ->orderBy('transaction_date', 'desc')
                               ->orderBy('id', 'desc')
                               ->first();

        return $lastTransaction ? $lastTransaction->balance_after : 0;
    }

    /**
     * التحقق من صحة بيانات الحساب
     */
    public function validateAccountDetails(): array
    {
        $errors = [];

        // التحقق من IBAN
        if ($this->iban && !$this->isValidIBAN($this->iban)) {
            $errors[] = 'رقم IBAN غير صحيح';
        }

        // التحقق من SWIFT Code
        if ($this->swift_code && !$this->isValidSWIFT($this->swift_code)) {
            $errors[] = 'رمز SWIFT غير صحيح';
        }

        // التحقق من رقم الحساب
        if (!$this->account_number || strlen($this->account_number) < 8) {
            $errors[] = 'رقم الحساب غير صحيح';
        }

        return $errors;
    }

    /**
     * التحقق من صحة IBAN
     */
    protected function isValidIBAN(string $iban): bool
    {
        // إزالة المسافات وتحويل للأحرف الكبيرة
        $iban = strtoupper(str_replace(' ', '', $iban));
        
        // التحقق من الطول (يختلف حسب البلد)
        if (strlen($iban) < 15 || strlen($iban) > 34) {
            return false;
        }

        // التحقق من البداية بحرفين
        if (!preg_match('/^[A-Z]{2}/', $iban)) {
            return false;
        }

        // يمكن إضافة المزيد من التحقق هنا
        return true;
    }

    /**
     * التحقق من صحة SWIFT Code
     */
    protected function isValidSWIFT(string $swift): bool
    {
        $swift = strtoupper(str_replace(' ', '', $swift));
        
        // SWIFT Code يجب أن يكون 8 أو 11 حرف
        return preg_match('/^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/', $swift);
    }

    /**
     * البحث في الحسابات البنكية
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('bank_name', 'LIKE', "%{$search}%")
              ->orWhere('account_name', 'LIKE', "%{$search}%")
              ->orWhere('account_number', 'LIKE', "%{$search}%")
              ->orWhere('iban', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة الحسابات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب العملة
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * فلترة حسب البنك
     */
    public function scopeForBank($query, string $bankName)
    {
        return $query->where('bank_name', 'LIKE', "%{$bankName}%");
    }

    /**
     * الحصول على الحساب الافتراضي
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * الحسابات المفعلة للمزامنة
     */
    public function scopeSyncEnabled($query)
    {
        return $query->where('sync_enabled', true);
    }
}
