<?php

namespace App\Domains\Accounting\Events;

use App\Domains\Accounting\Models\JournalEntry;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Journal Entry Created Event
 * حدث إنشاء قيد يومية جديد
 */
class JournalEntryCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public JournalEntry $journalEntry;
    public ?int $userId;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(JournalEntry $journalEntry, ?int $userId = null)
    {
        $this->journalEntry = $journalEntry;
        $this->userId = $userId ?? auth()->id();
    }

    /**
     * الحصول على البيانات للبث
     */
    public function broadcastWith(): array
    {
        return [
            'journal_entry' => [
                'id' => $this->journalEntry->id,
                'entry_number' => $this->journalEntry->entry_number,
                'entry_date' => $this->journalEntry->entry_date,
                'description' => $this->journalEntry->description,
                'total_debit' => $this->journalEntry->total_debit,
                'total_credit' => $this->journalEntry->total_credit,
                'status' => $this->journalEntry->status,
                'source_type' => $this->journalEntry->source_type,
            ],
            'user_id' => $this->userId,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * اسم القناة للبث
     */
    public function broadcastOn(): array
    {
        return [
            'accounting.journal-entries',
            "user.{$this->userId}",
        ];
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'journal-entry.created';
    }
}
