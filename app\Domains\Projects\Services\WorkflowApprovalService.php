<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\ProjectApproval;
use App\Domains\Projects\Models\WorkflowStep;
use App\Domains\Projects\Models\ApprovalWorkflow;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة سير العمل والموافقات - Advanced Workflow & Approval System
 * تدعم سير العمل المعقد والموافقات متعددة المستويات
 */
class WorkflowApprovalService
{
    protected array $workflowTypes = [
        'PROJECT_APPROVAL',
        'DOCUMENT_APPROVAL',
        'BUDGET_APPROVAL',
        'MILESTONE_APPROVAL',
        'DELIVERABLE_APPROVAL',
        'CHANGE_REQUEST_APPROVAL'
    ];

    protected array $approvalStatuses = [
        'PENDING',
        'APPROVED',
        'REJECTED',
        'CANCELLED',
        'ESCALATED'
    ];

    /**
     * إنشاء سير عمل موافقة جديد
     */
    public function createApprovalWorkflow(array $workflowData): ApprovalWorkflow
    {
        try {
            DB::beginTransaction();

            $workflow = ApprovalWorkflow::create([
                'name' => $workflowData['name'],
                'description' => $workflowData['description'],
                'type' => $workflowData['type'],
                'project_id' => $workflowData['project_id'] ?? null,
                'is_active' => true,
                'auto_start' => $workflowData['auto_start'] ?? false,
                'parallel_approval' => $workflowData['parallel_approval'] ?? false,
                'escalation_enabled' => $workflowData['escalation_enabled'] ?? true,
                'escalation_hours' => $workflowData['escalation_hours'] ?? 24,
                'settings' => $workflowData['settings'] ?? [],
                'created_by' => auth()->id(),
            ]);

            // إنشاء خطوات سير العمل
            foreach ($workflowData['steps'] as $index => $stepData) {
                $this->createWorkflowStep($workflow, $stepData, $index + 1);
            }

            DB::commit();

            return $workflow;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في إنشاء سير العمل', [
                'workflow_data' => $workflowData,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * بدء عملية موافقة
     */
    public function startApprovalProcess(
        int $workflowId,
        int $requesterId,
        string $subject,
        array $data = []
    ): ProjectApproval {
        try {
            DB::beginTransaction();

            $workflow = ApprovalWorkflow::findOrFail($workflowId);

            $approval = ProjectApproval::create([
                'workflow_id' => $workflowId,
                'project_id' => $workflow->project_id,
                'requester_id' => $requesterId,
                'subject' => $subject,
                'description' => $data['description'] ?? null,
                'priority' => $data['priority'] ?? 'MEDIUM',
                'status' => 'PENDING',
                'current_step' => 1,
                'data' => $data,
                'due_date' => $data['due_date'] ?? now()->addDays(7),
                'metadata' => [
                    'started_at' => now(),
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ],
            ]);

            // بدء الخطوة الأولى
            $this->processNextStep($approval);

            DB::commit();

            return $approval;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * معالجة موافقة/رفض
     */
    public function processApproval(
        int $approvalId,
        int $approverId,
        string $action,
        string $comments = null,
        array $attachments = []
    ): ProjectApproval {
        try {
            DB::beginTransaction();

            $approval = ProjectApproval::findOrFail($approvalId);

            // التحقق من صلاحية الموافقة
            $this->validateApprovalPermission($approval, $approverId);

            // تسجيل الإجراء
            $approval->approvalSteps()->create([
                'step_number' => $approval->current_step,
                'approver_id' => $approverId,
                'action' => $action,
                'comments' => $comments,
                'attachments' => $attachments,
                'processed_at' => now(),
                'ip_address' => request()->ip(),
            ]);

            if ($action === 'APPROVED') {
                $this->handleApproval($approval);
            } elseif ($action === 'REJECTED') {
                $this->handleRejection($approval, $comments);
            } elseif ($action === 'ESCALATED') {
                $this->handleEscalation($approval, $comments);
            }

            DB::commit();

            // إرسال الإشعارات
            $this->sendApprovalNotifications($approval, $action, $approverId);

            return $approval;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * معالجة الموافقة
     */
    protected function handleApproval(ProjectApproval $approval): void
    {
        $workflow = $approval->workflow;
        $nextStep = $approval->current_step + 1;

        // التحقق من وجود خطوات أخرى
        if ($workflow->steps()->where('step_number', $nextStep)->exists()) {
            $approval->update(['current_step' => $nextStep]);
            $this->processNextStep($approval);
        } else {
            // اكتمال سير العمل
            $approval->update([
                'status' => 'APPROVED',
                'completed_at' => now(),
            ]);

            $this->executeApprovalActions($approval);
        }
    }

    /**
     * معالجة الرفض
     */
    protected function handleRejection(ProjectApproval $approval, string $reason = null): void
    {
        $approval->update([
            'status' => 'REJECTED',
            'completed_at' => now(),
            'rejection_reason' => $reason,
        ]);

        $this->executeRejectionActions($approval);
    }

    /**
     * معالجة التصعيد
     */
    protected function handleEscalation(ProjectApproval $approval, string $reason = null): void
    {
        $workflow = $approval->workflow;
        $currentStep = $workflow->steps()->where('step_number', $approval->current_step)->first();

        if ($currentStep && $currentStep->escalation_approver_id) {
            // تصعيد للموافق المحدد
            $approval->update([
                'status' => 'ESCALATED',
                'escalated_to' => $currentStep->escalation_approver_id,
                'escalation_reason' => $reason,
                'escalated_at' => now(),
            ]);

            // إشعار الموافق المصعد إليه
            $escalationApprover = \App\Domains\HR\Models\Employee::find($currentStep->escalation_approver_id);
            $escalationApprover?->notify(
                new \App\Notifications\ApprovalEscalatedNotification($approval)
            );
        }
    }

    /**
     * معالجة الخطوة التالية
     */
    protected function processNextStep(ProjectApproval $approval): void
    {
        $workflow = $approval->workflow;
        $currentStep = $workflow->steps()->where('step_number', $approval->current_step)->first();

        if (!$currentStep) {
            return;
        }

        // تحديد الموافقين للخطوة الحالية
        $approvers = $this->getStepApprovers($currentStep);

        // إرسال إشعارات الموافقة
        foreach ($approvers as $approver) {
            $approver->notify(new \App\Notifications\ApprovalRequestNotification($approval));
        }

        // جدولة التصعيد التلقائي
        if ($workflow->escalation_enabled && $workflow->escalation_hours > 0) {
            $this->scheduleAutoEscalation($approval, $workflow->escalation_hours);
        }
    }

    /**
     * الحصول على موافقي الخطوة
     */
    protected function getStepApprovers(WorkflowStep $step): array
    {
        $approvers = [];

        // موافق محدد
        if ($step->approver_id) {
            $approvers[] = \App\Domains\HR\Models\Employee::find($step->approver_id);
        }

        // مجموعة موافقين
        if ($step->approver_group_id) {
            $group = \App\Domains\HR\Models\EmployeeGroup::find($step->approver_group_id);
            $approvers = array_merge($approvers, $group->members->toArray());
        }

        // موافق بناءً على الدور
        if ($step->approver_role) {
            $roleApprovers = \App\Domains\HR\Models\Employee::whereHas('roles', function ($query) use ($step) {
                $query->where('name', $step->approver_role);
            })->get();
            $approvers = array_merge($approvers, $roleApprovers->toArray());
        }

        return array_filter($approvers);
    }

    /**
     * تنفيذ إجراءات الموافقة
     */
    protected function executeApprovalActions(ProjectApproval $approval): void
    {
        $workflow = $approval->workflow;
        $actions = $workflow->settings['approval_actions'] ?? [];

        foreach ($actions as $action) {
            $this->executeAction($approval, $action);
        }

        // إشعار مقدم الطلب
        $approval->requester->notify(
            new \App\Notifications\ApprovalCompletedNotification($approval, 'APPROVED')
        );
    }

    /**
     * تنفيذ إجراءات الرفض
     */
    protected function executeRejectionActions(ProjectApproval $approval): void
    {
        $workflow = $approval->workflow;
        $actions = $workflow->settings['rejection_actions'] ?? [];

        foreach ($actions as $action) {
            $this->executeAction($approval, $action);
        }

        // إشعار مقدم الطلب
        $approval->requester->notify(
            new \App\Notifications\ApprovalCompletedNotification($approval, 'REJECTED')
        );
    }

    /**
     * تنفيذ إجراء محدد
     */
    protected function executeAction(ProjectApproval $approval, array $action): void
    {
        match ($action['type']) {
            'UPDATE_PROJECT_STATUS' => $this->updateProjectStatus($approval, $action['value']),
            'CREATE_TASK' => $this->createTask($approval, $action['data']),
            'SEND_EMAIL' => $this->sendEmail($approval, $action['template']),
            'UPDATE_BUDGET' => $this->updateBudget($approval, $action['amount']),
            'ASSIGN_RESOURCES' => $this->assignResources($approval, $action['resources']),
            default => Log::warning('إجراء غير معروف في سير العمل', ['action' => $action]),
        };
    }

    /**
     * إنشاء خطوة سير عمل
     */
    protected function createWorkflowStep(ApprovalWorkflow $workflow, array $stepData, int $stepNumber): WorkflowStep
    {
        return $workflow->steps()->create([
            'step_number' => $stepNumber,
            'name' => $stepData['name'],
            'description' => $stepData['description'] ?? null,
            'approver_id' => $stepData['approver_id'] ?? null,
            'approver_group_id' => $stepData['approver_group_id'] ?? null,
            'approver_role' => $stepData['approver_role'] ?? null,
            'escalation_approver_id' => $stepData['escalation_approver_id'] ?? null,
            'is_required' => $stepData['is_required'] ?? true,
            'timeout_hours' => $stepData['timeout_hours'] ?? 24,
            'conditions' => $stepData['conditions'] ?? [],
            'settings' => $stepData['settings'] ?? [],
        ]);
    }

    /**
     * التحقق من صلاحية الموافقة
     */
    protected function validateApprovalPermission(ProjectApproval $approval, int $approverId): void
    {
        $workflow = $approval->workflow;
        $currentStep = $workflow->steps()->where('step_number', $approval->current_step)->first();

        if (!$currentStep) {
            throw new \UnauthorizedHttpException('خطوة سير العمل غير موجودة');
        }

        $approvers = $this->getStepApprovers($currentStep);
        $approverIds = collect($approvers)->pluck('id')->toArray();

        if (!in_array($approverId, $approverIds) && $approval->escalated_to !== $approverId) {
            throw new \UnauthorizedHttpException('لا تملك صلاحية الموافقة على هذا الطلب');
        }
    }

    /**
     * جدولة التصعيد التلقائي
     */
    protected function scheduleAutoEscalation(ProjectApproval $approval, int $hours): void
    {
        // يمكن استخدام Laravel Scheduler أو Queue Jobs
        \App\Jobs\AutoEscalateApprovalJob::dispatch($approval)
            ->delay(now()->addHours($hours));
    }

    /**
     * إرسال إشعارات الموافقة
     */
    protected function sendApprovalNotifications(ProjectApproval $approval, string $action, int $approverId): void
    {
        // إشعار مقدم الطلب
        $approval->requester->notify(
            new \App\Notifications\ApprovalStatusUpdateNotification($approval, $action)
        );

        // إشعار مدير المشروع
        if ($approval->project && $approval->project->project_manager_id !== $approverId) {
            $approval->project->projectManager->notify(
                new \App\Notifications\ApprovalStatusUpdateNotification($approval, $action)
            );
        }
    }

    // دوال مساعدة للإجراءات
    protected function updateProjectStatus(ProjectApproval $approval, string $status): void
    {
        if ($approval->project) {
            $approval->project->update(['status' => $status]);
        }
    }

    protected function createTask(ProjectApproval $approval, array $taskData): void
    {
        if ($approval->project) {
            $approval->project->tasks()->create($taskData);
        }
    }

    protected function sendEmail(ProjectApproval $approval, string $template): void
    {
        // تنفيذ إرسال البريد الإلكتروني
    }

    protected function updateBudget(ProjectApproval $approval, float $amount): void
    {
        if ($approval->project) {
            $approval->project->increment('budget', $amount);
        }
    }

    protected function assignResources(ProjectApproval $approval, array $resources): void
    {
        // تنفيذ تعيين الموارد
    }
}
