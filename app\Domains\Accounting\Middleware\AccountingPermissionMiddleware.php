<?php

namespace App\Domains\Accounting\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * Accounting Permission Middleware
 * وسطية أذونات المحاسبة
 */
class AccountingPermissionMiddleware
{
    /**
     * معالجة الطلب الوارد
     */
    public function handle(Request $request, Closure $next, string $permission = null): Response
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            return $this->unauthorizedResponse('يجب تسجيل الدخول للوصول لهذه الصفحة');
        }

        $user = Auth::user();

        // التحقق من تفعيل نظام المحاسبة
        if (!config('accounting.enabled', true)) {
            return $this->forbiddenResponse('نظام المحاسبة غير مفعل حالياً');
        }

        // التحقق من الأذونات العامة للمحاسبة
        if (!$user->can('access-accounting')) {
            Log::warning('محاولة وصول غير مصرح بها لنظام المحاسبة', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'ip' => $request->ip(),
                'route' => $request->route()?->getName(),
                'url' => $request->fullUrl(),
            ]);

            return $this->forbiddenResponse('ليس لديك صلاحية للوصول لنظام المحاسبة');
        }

        // التحقق من الإذن المحدد إذا تم تمريره
        if ($permission && !$user->can($permission)) {
            Log::warning('محاولة وصول غير مصرح بها لإذن محدد', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'permission' => $permission,
                'ip' => $request->ip(),
                'route' => $request->route()?->getName(),
                'url' => $request->fullUrl(),
            ]);

            return $this->forbiddenResponse('ليس لديك صلاحية لتنفيذ هذا الإجراء');
        }

        // التحقق من قيود الوقت (إذا كانت مفعلة)
        if (config('accounting.security.time_restrictions.enabled', false)) {
            if (!$this->checkTimeRestrictions($user)) {
                return $this->forbiddenResponse('الوصول لنظام المحاسبة غير مسموح في هذا الوقت');
            }
        }

        // التحقق من قيود IP (إذا كانت مفعلة)
        if (config('accounting.security.ip_whitelist_enabled', false)) {
            if (!$this->checkIpRestrictions($request->ip())) {
                Log::warning('محاولة وصول من IP غير مصرح به', [
                    'user_id' => $user->id,
                    'ip' => $request->ip(),
                    'route' => $request->route()?->getName(),
                ]);

                return $this->forbiddenResponse('الوصول من هذا العنوان غير مسموح');
            }
        }

        // التحقق من المصادقة الثنائية (إذا كانت مطلوبة)
        if (config('accounting.security.two_factor_auth_required', false)) {
            if (!$this->checkTwoFactorAuth($user)) {
                return $this->forbiddenResponse('يجب تفعيل المصادقة الثنائية للوصول لنظام المحاسبة');
            }
        }

        // تسجيل الوصول الناجح
        $this->logSuccessfulAccess($user, $request, $permission);

        return $next($request);
    }

    /**
     * التحقق من قيود الوقت
     */
    protected function checkTimeRestrictions($user): bool
    {
        $restrictions = config('accounting.security.time_restrictions', []);
        
        if (empty($restrictions['allowed_hours'])) {
            return true;
        }

        $currentHour = now()->hour;
        $allowedHours = $restrictions['allowed_hours'];

        // التحقق من الساعات المسموحة
        if (!in_array($currentHour, $allowedHours)) {
            return false;
        }

        // التحقق من الأيام المسموحة
        if (!empty($restrictions['allowed_days'])) {
            $currentDay = now()->dayOfWeek; // 0 = Sunday, 6 = Saturday
            if (!in_array($currentDay, $restrictions['allowed_days'])) {
                return false;
            }
        }

        // التحقق من استثناءات المستخدمين
        if (!empty($restrictions['exempt_users'])) {
            if (in_array($user->id, $restrictions['exempt_users'])) {
                return true;
            }
        }

        // التحقق من استثناءات الأدوار
        if (!empty($restrictions['exempt_roles'])) {
            foreach ($restrictions['exempt_roles'] as $role) {
                if ($user->hasRole($role)) {
                    return true;
                }
            }
        }

        return true;
    }

    /**
     * التحقق من قيود IP
     */
    protected function checkIpRestrictions(string $ip): bool
    {
        $whitelist = config('accounting.security.ip_whitelist', []);
        
        if (empty($whitelist)) {
            return true;
        }

        // التحقق من IP المحدد
        if (in_array($ip, $whitelist)) {
            return true;
        }

        // التحقق من نطاقات IP
        foreach ($whitelist as $allowedIp) {
            if (str_contains($allowedIp, '/')) {
                // CIDR notation
                if ($this->ipInRange($ip, $allowedIp)) {
                    return true;
                }
            } elseif (str_contains($allowedIp, '*')) {
                // Wildcard notation
                if ($this->ipMatchesWildcard($ip, $allowedIp)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * التحقق من وجود IP في نطاق CIDR
     */
    protected function ipInRange(string $ip, string $cidr): bool
    {
        list($subnet, $mask) = explode('/', $cidr);
        
        if ((ip2long($ip) & ~((1 << (32 - $mask)) - 1)) == ip2long($subnet)) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من مطابقة IP مع نمط wildcard
     */
    protected function ipMatchesWildcard(string $ip, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/', $ip);
    }

    /**
     * التحقق من المصادقة الثنائية
     */
    protected function checkTwoFactorAuth($user): bool
    {
        // التحقق من تفعيل المصادقة الثنائية للمستخدم
        if (method_exists($user, 'hasTwoFactorEnabled')) {
            return $user->hasTwoFactorEnabled();
        }

        // إذا لم تكن المصادقة الثنائية مطبقة، نعتبرها مفعلة
        return true;
    }

    /**
     * تسجيل الوصول الناجح
     */
    protected function logSuccessfulAccess($user, Request $request, ?string $permission): void
    {
        Log::info('وصول ناجح لنظام المحاسبة', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'permission' => $permission,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * استجابة عدم التصريح
     */
    protected function unauthorizedResponse(string $message): Response
    {
        if (request()->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
                'error_code' => 'UNAUTHORIZED',
            ], 401);
        }

        return response()->view('errors.401', [
            'message' => $message
        ], 401);
    }

    /**
     * استجابة عدم السماح
     */
    protected function forbiddenResponse(string $message): Response
    {
        if (request()->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
                'error_code' => 'FORBIDDEN',
            ], 403);
        }

        return response()->view('errors.403', [
            'message' => $message
        ], 403);
    }
}
