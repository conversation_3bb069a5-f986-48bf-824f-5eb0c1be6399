<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج استبيان المشروع - Project Survey
 */
class ProjectSurvey extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'type',
        'questions',
        'is_anonymous',
        'is_active',
        'expires_at',
        'created_by',
    ];

    protected $casts = [
        'questions' => 'array',
        'is_anonymous' => 'boolean',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(SurveyResponse::class, 'survey_id');
    }
}
