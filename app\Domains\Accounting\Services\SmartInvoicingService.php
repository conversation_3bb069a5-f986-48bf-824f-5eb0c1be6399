<?php

namespace App\Domains\Accounting\Services;

use App\Domains\Accounting\Models\Invoice;
use App\Domains\Accounting\Models\InvoiceItem;
use App\Domains\Accounting\Models\Customer;
use App\Domains\Accounting\Models\TaxRate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * خدمة الفوترة الذكية
 * تدعم الفوترة الإلكترونية والتوقيع الرقمي والامتثال الضريبي
 */
class SmartInvoicingService
{
    protected array $eInvoiceProviders = [];
    protected array $digitalSignatureProviders = [];
    protected array $taxCompliance = [];

    public function __construct()
    {
        $this->loadEInvoiceProviders();
        $this->loadDigitalSignatureProviders();
        $this->loadTaxCompliance();
    }

    /**
     * إنشاء فاتورة ذكية
     */
    public function createSmartInvoice(array $invoiceData): Invoice
    {
        try {
            // إنشاء الفاتورة الأساسية
            $invoice = $this->createBaseInvoice($invoiceData);
            
            // حساب الضرائب تلقائياً
            $this->calculateTaxes($invoice);
            
            // تطبيق قواعد الامتثال الضريبي
            $this->applyTaxCompliance($invoice);
            
            // توليد رقم فاتورة ذكي
            $this->generateSmartInvoiceNumber($invoice);
            
            // إنشاء QR Code
            $this->generateQRCode($invoice);
            
            // التوقيع الرقمي
            if ($invoiceData['digital_signature'] ?? false) {
                $this->applyDigitalSignature($invoice);
            }
            
            // إرسال للنظام الضريبي (إذا مطلوب)
            if ($invoiceData['submit_to_tax_authority'] ?? false) {
                $this->submitToTaxAuthority($invoice);
            }

            return $invoice;

        } catch (\Exception $e) {
            Log::error('خطأ في إنشاء الفاتورة الذكية', [
                'error' => $e->getMessage(),
                'data' => $invoiceData,
            ]);
            throw $e;
        }
    }

    /**
     * إنشاء الفاتورة الأساسية
     */
    protected function createBaseInvoice(array $data): Invoice
    {
        $invoice = Invoice::create([
            'customer_id' => $data['customer_id'],
            'invoice_date' => $data['invoice_date'] ?? now(),
            'due_date' => $data['due_date'] ?? now()->addDays(30),
            'currency' => $data['currency'] ?? 'MAD',
            'exchange_rate' => $data['exchange_rate'] ?? 1.0,
            'payment_terms' => $data['payment_terms'] ?? 'Net 30',
            'notes' => $data['notes'] ?? null,
            'status' => 'DRAFT',
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'metadata' => $data['metadata'] ?? [],
        ]);

        // إضافة بنود الفاتورة
        foreach ($data['items'] as $itemData) {
            $this->addInvoiceItem($invoice, $itemData);
        }

        // إعادة حساب المجاميع
        $this->recalculateInvoiceTotals($invoice);

        return $invoice;
    }

    /**
     * إضافة بند للفاتورة
     */
    protected function addInvoiceItem(Invoice $invoice, array $itemData): InvoiceItem
    {
        return $invoice->items()->create([
            'description' => $itemData['description'],
            'quantity' => $itemData['quantity'],
            'unit_price' => $itemData['unit_price'],
            'line_total' => $itemData['quantity'] * $itemData['unit_price'],
            'tax_rate_id' => $itemData['tax_rate_id'] ?? null,
            'tax_amount' => 0, // سيتم حسابه لاحقاً
            'discount_percentage' => $itemData['discount_percentage'] ?? 0,
            'discount_amount' => $itemData['discount_amount'] ?? 0,
            'product_code' => $itemData['product_code'] ?? null,
            'unit_of_measure' => $itemData['unit_of_measure'] ?? 'PCS',
        ]);
    }

    /**
     * حساب الضرائب تلقائياً
     */
    protected function calculateTaxes(Invoice $invoice): void
    {
        $totalTax = 0;

        foreach ($invoice->items as $item) {
            if ($item->tax_rate_id) {
                $taxRate = TaxRate::find($item->tax_rate_id);
                if ($taxRate) {
                    $taxableAmount = $item->line_total - $item->discount_amount;
                    $itemTax = $taxableAmount * ($taxRate->rate / 100);
                    
                    $item->update(['tax_amount' => $itemTax]);
                    $totalTax += $itemTax;
                }
            }
        }

        $invoice->update(['tax_amount' => $totalTax]);
    }

    /**
     * إعادة حساب مجاميع الفاتورة
     */
    protected function recalculateInvoiceTotals(Invoice $invoice): void
    {
        $subtotal = $invoice->items->sum('line_total');
        $totalDiscount = $invoice->items->sum('discount_amount');
        $totalTax = $invoice->items->sum('tax_amount');
        
        $invoice->update([
            'subtotal' => $subtotal,
            'total_discount' => $totalDiscount,
            'tax_amount' => $totalTax,
            'total_amount' => $subtotal - $totalDiscount + $totalTax,
        ]);
    }

    /**
     * تطبيق قواعد الامتثال الضريبي
     */
    protected function applyTaxCompliance(Invoice $invoice): void
    {
        $country = $invoice->customer->country ?? 'MA';
        $compliance = $this->taxCompliance[$country] ?? [];

        // تطبيق قواعد الامتثال حسب الدولة
        switch ($country) {
            case 'MA': // المغرب
                $this->applyMoroccanTaxCompliance($invoice, $compliance);
                break;
            case 'SA': // السعودية
                $this->applySaudiTaxCompliance($invoice, $compliance);
                break;
            case 'AE': // الإمارات
                $this->applyUAETaxCompliance($invoice, $compliance);
                break;
        }
    }

    /**
     * تطبيق قواعد الامتثال الضريبي المغربي
     */
    protected function applyMoroccanTaxCompliance(Invoice $invoice, array $compliance): void
    {
        // التحقق من وجود TVA
        if ($invoice->total_amount > 1000 && $invoice->tax_amount == 0) {
            Log::warning('فاتورة كبيرة بدون TVA', ['invoice_id' => $invoice->id]);
        }

        // إضافة معلومات الامتثال
        $invoice->update([
            'tax_compliance_data' => [
                'tva_applicable' => $invoice->tax_amount > 0,
                'ice_number' => config('company.ice_number'),
                'rc_number' => config('company.rc_number'),
                'if_number' => config('company.if_number'),
                'cnss_number' => config('company.cnss_number'),
            ]
        ]);
    }

    /**
     * تطبيق قواعد الامتثال الضريبي السعودي
     */
    protected function applySaudiTaxCompliance(Invoice $invoice, array $compliance): void
    {
        // تطبيق ضريبة القيمة المضافة السعودية (15%)
        $vatRate = 15;
        
        $invoice->update([
            'tax_compliance_data' => [
                'vat_number' => config('company.saudi_vat_number'),
                'cr_number' => config('company.saudi_cr_number'),
                'vat_rate' => $vatRate,
                'zatca_compliant' => true,
            ]
        ]);
    }

    /**
     * تطبيق قواعد الامتثال الضريبي الإماراتي
     */
    protected function applyUAETaxCompliance(Invoice $invoice, array $compliance): void
    {
        // تطبيق ضريبة القيمة المضافة الإماراتية (5%)
        $vatRate = 5;
        
        $invoice->update([
            'tax_compliance_data' => [
                'trn_number' => config('company.uae_trn_number'),
                'trade_license' => config('company.uae_trade_license'),
                'vat_rate' => $vatRate,
                'fta_compliant' => true,
            ]
        ]);
    }

    /**
     * توليد رقم فاتورة ذكي
     */
    protected function generateSmartInvoiceNumber(Invoice $invoice): void
    {
        $year = $invoice->invoice_date->year;
        $month = $invoice->invoice_date->format('m');
        $country = $invoice->customer->country ?? 'MA';
        
        // العداد التسلسلي للسنة
        $sequence = Invoice::whereYear('invoice_date', $year)->count() + 1;
        
        // تنسيق الرقم حسب الدولة
        $invoiceNumber = match ($country) {
            'MA' => "FAC-{$year}-{$month}-" . str_pad($sequence, 6, '0', STR_PAD_LEFT),
            'SA' => "INV-SA-{$year}-" . str_pad($sequence, 8, '0', STR_PAD_LEFT),
            'AE' => "INV-AE-{$year}-" . str_pad($sequence, 8, '0', STR_PAD_LEFT),
            default => "INV-{$year}-{$month}-" . str_pad($sequence, 6, '0', STR_PAD_LEFT),
        };

        $invoice->update(['invoice_number' => $invoiceNumber]);
    }

    /**
     * توليد QR Code
     */
    protected function generateQRCode(Invoice $invoice): void
    {
        $qrData = [
            'invoice_number' => $invoice->invoice_number,
            'customer_name' => $invoice->customer->name,
            'total_amount' => $invoice->total_amount,
            'currency' => $invoice->currency,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            'verification_url' => route('invoice.verify', $invoice->uuid),
        ];

        // إضافة بيانات الامتثال الضريبي للـ QR
        if ($invoice->tax_compliance_data) {
            $qrData = array_merge($qrData, $invoice->tax_compliance_data);
        }

        $qrCodeData = base64_encode(json_encode($qrData));
        
        // توليد صورة QR Code
        $qrCodeImage = QrCode::format('png')
            ->size(200)
            ->generate($qrCodeData);

        // حفظ QR Code
        $qrPath = "invoices/{$invoice->id}/qr_code.png";
        Storage::disk('public')->put($qrPath, $qrCodeImage);

        $invoice->update([
            'qr_code_data' => $qrCodeData,
            'qr_code_path' => $qrPath,
        ]);
    }

    /**
     * تطبيق التوقيع الرقمي
     */
    protected function applyDigitalSignature(Invoice $invoice): void
    {
        $provider = $this->digitalSignatureProviders['default'] ?? null;
        
        if (!$provider) {
            throw new \Exception('مقدم التوقيع الرقمي غير متوفر');
        }

        // إنشاء hash للفاتورة
        $invoiceHash = $this->generateInvoiceHash($invoice);
        
        // تطبيق التوقيع الرقمي
        $signature = $this->signWithProvider($invoiceHash, $provider);
        
        $invoice->update([
            'digital_signature' => $signature,
            'signature_algorithm' => $provider['algorithm'],
            'signature_timestamp' => now(),
            'is_digitally_signed' => true,
        ]);
    }

    /**
     * توليد hash للفاتورة
     */
    protected function generateInvoiceHash(Invoice $invoice): string
    {
        $data = [
            'invoice_number' => $invoice->invoice_number,
            'customer_id' => $invoice->customer_id,
            'total_amount' => $invoice->total_amount,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d H:i:s'),
            'items' => $invoice->items->map(function ($item) {
                return [
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'line_total' => $item->line_total,
                ];
            })->toArray(),
        ];

        return hash('sha256', json_encode($data));
    }

    /**
     * التوقيع باستخدام مقدم الخدمة
     */
    protected function signWithProvider(string $hash, array $provider): string
    {
        // تطبيق مبسط للتوقيع الرقمي
        // في التطبيق الحقيقي يتم استخدام شهادات رقمية حقيقية
        
        $privateKey = $provider['private_key'];
        $signature = hash_hmac('sha256', $hash, $privateKey);
        
        return base64_encode($signature);
    }

    /**
     * إرسال للسلطة الضريبية
     */
    protected function submitToTaxAuthority(Invoice $invoice): void
    {
        $country = $invoice->customer->country ?? 'MA';
        
        switch ($country) {
            case 'MA':
                $this->submitToMoroccanTaxAuthority($invoice);
                break;
            case 'SA':
                $this->submitToZATCA($invoice);
                break;
            case 'AE':
                $this->submitToFTA($invoice);
                break;
        }
    }

    /**
     * إرسال للسلطة الضريبية المغربية
     */
    protected function submitToMoroccanTaxAuthority(Invoice $invoice): void
    {
        $endpoint = config('tax.morocco.endpoint');
        $apiKey = config('tax.morocco.api_key');
        
        if (!$endpoint || !$apiKey) {
            Log::warning('إعدادات السلطة الضريبية المغربية غير مكتملة');
            return;
        }

        $payload = [
            'invoice_number' => $invoice->invoice_number,
            'customer_ice' => $invoice->customer->ice_number,
            'total_amount' => $invoice->total_amount,
            'tax_amount' => $invoice->tax_amount,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            'items' => $invoice->items->map(function ($item) {
                return [
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'tax_amount' => $item->tax_amount,
                ];
            })->toArray(),
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
            ])->post($endpoint, $payload);

            if ($response->successful()) {
                $invoice->update([
                    'tax_authority_status' => 'SUBMITTED',
                    'tax_authority_reference' => $response->json('reference'),
                    'submitted_to_tax_authority_at' => now(),
                ]);
            } else {
                Log::error('فشل في إرسال الفاتورة للسلطة الضريبية', [
                    'invoice_id' => $invoice->id,
                    'response' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('خطأ في إرسال الفاتورة للسلطة الضريبية', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * إرسال لـ ZATCA (السعودية)
     */
    protected function submitToZATCA(Invoice $invoice): void
    {
        // تطبيق إرسال لـ ZATCA
        Log::info('إرسال فاتورة لـ ZATCA', ['invoice_id' => $invoice->id]);
    }

    /**
     * إرسال لـ FTA (الإمارات)
     */
    protected function submitToFTA(Invoice $invoice): void
    {
        // تطبيق إرسال لـ FTA
        Log::info('إرسال فاتورة لـ FTA', ['invoice_id' => $invoice->id]);
    }

    /**
     * إنشاء فاتورة متكررة
     */
    public function createRecurringInvoice(array $recurringData): array
    {
        $baseInvoice = Invoice::find($recurringData['base_invoice_id']);
        $frequency = $recurringData['frequency']; // monthly, quarterly, yearly
        $occurrences = $recurringData['occurrences'] ?? 12;
        
        $createdInvoices = [];
        $nextDate = \Carbon\Carbon::parse($recurringData['start_date']);

        for ($i = 0; $i < $occurrences; $i++) {
            $invoiceData = [
                'customer_id' => $baseInvoice->customer_id,
                'invoice_date' => $nextDate->copy(),
                'due_date' => $nextDate->copy()->addDays(30),
                'currency' => $baseInvoice->currency,
                'payment_terms' => $baseInvoice->payment_terms,
                'notes' => $baseInvoice->notes . ' (فاتورة متكررة)',
                'items' => $baseInvoice->items->map(function ($item) {
                    return [
                        'description' => $item->description,
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'tax_rate_id' => $item->tax_rate_id,
                    ];
                })->toArray(),
            ];

            $invoice = $this->createSmartInvoice($invoiceData);
            $createdInvoices[] = $invoice;

            // حساب التاريخ التالي
            $nextDate = match ($frequency) {
                'monthly' => $nextDate->addMonth(),
                'quarterly' => $nextDate->addMonths(3),
                'yearly' => $nextDate->addYear(),
                default => $nextDate->addMonth(),
            };
        }

        return $createdInvoices;
    }

    /**
     * إرسال الفاتورة
     */
    public function sendInvoice(Invoice $invoice, array $options = []): bool
    {
        $methods = $options['methods'] ?? ['email'];
        $sent = false;

        foreach ($methods as $method) {
            switch ($method) {
                case 'email':
                    $sent = $this->sendByEmail($invoice, $options) || $sent;
                    break;
                case 'whatsapp':
                    $sent = $this->sendByWhatsApp($invoice, $options) || $sent;
                    break;
                case 'sms':
                    $sent = $this->sendBySMS($invoice, $options) || $sent;
                    break;
            }
        }

        if ($sent) {
            $invoice->update([
                'sent_at' => now(),
                'sent_methods' => $methods,
            ]);
        }

        return $sent;
    }

    /**
     * إرسال بالبريد الإلكتروني
     */
    protected function sendByEmail(Invoice $invoice, array $options): bool
    {
        try {
            $invoice->customer->notify(new \App\Notifications\InvoiceGenerated($invoice));
            return true;
        } catch (\Exception $e) {
            Log::error('فشل في إرسال الفاتورة بالبريد', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * إرسال عبر WhatsApp
     */
    protected function sendByWhatsApp(Invoice $invoice, array $options): bool
    {
        // تطبيق إرسال عبر WhatsApp API
        return false;
    }

    /**
     * إرسال عبر SMS
     */
    protected function sendBySMS(Invoice $invoice, array $options): bool
    {
        // تطبيق إرسال عبر SMS
        return false;
    }

    // دوال التحميل والإعداد
    protected function loadEInvoiceProviders(): void
    {
        $this->eInvoiceProviders = [
            'morocco' => [
                'endpoint' => config('tax.morocco.endpoint'),
                'api_key' => config('tax.morocco.api_key'),
            ],
            'saudi' => [
                'endpoint' => config('tax.saudi.zatca_endpoint'),
                'api_key' => config('tax.saudi.zatca_api_key'),
            ],
        ];
    }

    protected function loadDigitalSignatureProviders(): void
    {
        $this->digitalSignatureProviders = [
            'default' => [
                'algorithm' => 'SHA256withRSA',
                'private_key' => config('signature.private_key'),
                'certificate' => config('signature.certificate'),
            ],
        ];
    }

    protected function loadTaxCompliance(): void
    {
        $this->taxCompliance = [
            'MA' => [
                'vat_rate' => 20,
                'required_fields' => ['ice_number', 'rc_number'],
            ],
            'SA' => [
                'vat_rate' => 15,
                'required_fields' => ['vat_number', 'cr_number'],
            ],
            'AE' => [
                'vat_rate' => 5,
                'required_fields' => ['trn_number'],
            ],
        ];
    }
}
