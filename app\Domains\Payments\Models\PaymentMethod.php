<?php

namespace App\Domains\Payments\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * نموذج طريقة الدفع
 * يمثل طرق الدفع المحفوظة للعملاء
 */
class PaymentMethod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'gateway_id',
        'type',
        'provider',
        'token',
        'last_four',
        'brand',
        'exp_month',
        'exp_year',
        'holder_name',
        'billing_address',
        'metadata',
        'is_default',
        'is_verified',
        'status',
        'fingerprint',
        'country',
        'funding',
        'wallet_type',
        'bank_name',
        'account_type',
        'routing_number',
        'account_number_last_four',
        'iban_last_four',
        'swift_code',
        'verification_data',
        'risk_indicators',
        'usage_count',
        'last_used_at',
        'expires_at',
    ];

    protected $casts = [
        'billing_address' => 'array',
        'metadata' => 'array',
        'is_default' => 'boolean',
        'is_verified' => 'boolean',
        'verification_data' => 'array',
        'risk_indicators' => 'array',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'exp_month' => 'integer',
        'exp_year' => 'integer',
    ];

    protected $hidden = [
        'token',
        'routing_number',
    ];

    /**
     * أنواع طرق الدفع
     */
    const TYPES = [
        'card' => 'بطاقة ائتمانية',
        'bank_account' => 'حساب بنكي',
        'digital_wallet' => 'محفظة رقمية',
        'mobile_payment' => 'دفع عبر الهاتف',
        'cryptocurrency' => 'عملة رقمية',
        'buy_now_pay_later' => 'اشتري الآن وادفع لاحقاً',
    ];

    /**
     * حالات طريقة الدفع
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'expired' => 'منتهي الصلاحية',
        'blocked' => 'محظور',
        'pending_verification' => 'في انتظار التحقق',
        'verification_failed' => 'فشل التحقق',
    ];

    /**
     * أنواع التمويل للبطاقات
     */
    const FUNDING_TYPES = [
        'credit' => 'ائتمانية',
        'debit' => 'خصم مباشر',
        'prepaid' => 'مدفوعة مسبقاً',
        'unknown' => 'غير معروف',
    ];

    /**
     * أنواع الحسابات البنكية
     */
    const ACCOUNT_TYPES = [
        'checking' => 'حساب جاري',
        'savings' => 'حساب توفير',
        'business' => 'حساب تجاري',
        'investment' => 'حساب استثماري',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع بوابة الدفع
     */
    public function gateway(): BelongsTo
    {
        return $this->belongsTo(PaymentGateway::class, 'gateway_id');
    }

    /**
     * العلاقة مع المعاملات
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'payment_method_id');
    }

    /**
     * التحقق من انتهاء صلاحية البطاقة
     */
    public function isExpired(): bool
    {
        if ($this->type !== 'card' || !$this->exp_month || !$this->exp_year) {
            return false;
        }

        $expiryDate = \Carbon\Carbon::createFromDate($this->exp_year, $this->exp_month, 1)->endOfMonth();
        return $expiryDate->isPast();
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function isExpiringSoon(int $months = 2): bool
    {
        if ($this->type !== 'card' || !$this->exp_month || !$this->exp_year) {
            return false;
        }

        $expiryDate = \Carbon\Carbon::createFromDate($this->exp_year, $this->exp_month, 1)->endOfMonth();
        return $expiryDate->isBefore(now()->addMonths($months));
    }

    /**
     * التحقق من إمكانية الاستخدام
     */
    public function isUsable(): bool
    {
        return $this->status === 'active' && 
               $this->is_verified && 
               !$this->isExpired();
    }

    /**
     * الحصول على اسم العلامة التجارية للبطاقة
     */
    public function getBrandDisplayName(): string
    {
        return match ($this->brand) {
            'visa' => 'Visa',
            'mastercard' => 'Mastercard',
            'amex' => 'American Express',
            'discover' => 'Discover',
            'diners' => 'Diners Club',
            'jcb' => 'JCB',
            'unionpay' => 'UnionPay',
            'mada' => 'Mada',
            'meeza' => 'Meeza',
            default => ucfirst($this->brand ?? 'Unknown'),
        };
    }

    /**
     * الحصول على أيقونة العلامة التجارية
     */
    public function getBrandIcon(): string
    {
        return match ($this->brand) {
            'visa' => '💳',
            'mastercard' => '💳',
            'amex' => '💳',
            'mada' => '🏧',
            'apple_pay' => '🍎',
            'google_pay' => '🔍',
            'paypal' => '💰',
            'stc_pay' => '📱',
            'fawry' => '🏪',
            default => '💳',
        };
    }

    /**
     * الحصول على النص المعروض للعميل
     */
    public function getDisplayText(): string
    {
        return match ($this->type) {
            'card' => $this->getBrandDisplayName() . ' •••• ' . $this->last_four,
            'bank_account' => ($this->bank_name ?? 'Bank') . ' •••• ' . $this->account_number_last_four,
            'digital_wallet' => $this->provider . ' Wallet',
            'mobile_payment' => $this->provider . ' Mobile',
            default => $this->provider ?? 'Payment Method',
        };
    }

    /**
     * تحديث عداد الاستخدام
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * تعيين كطريقة دفع افتراضية
     */
    public function setAsDefault(): void
    {
        // إلغاء تعيين الطرق الأخرى كافتراضية
        static::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // تعيين هذه الطريقة كافتراضية
        $this->update(['is_default' => true]);
    }

    /**
     * التحقق من طريقة الدفع
     */
    public function verify(array $verificationData = []): bool
    {
        // تنفيذ عملية التحقق حسب نوع طريقة الدفع
        $verified = $this->performVerification($verificationData);

        $this->update([
            'is_verified' => $verified,
            'verification_data' => $verificationData,
            'status' => $verified ? 'active' : 'verification_failed',
        ]);

        return $verified;
    }

    /**
     * تنفيذ عملية التحقق
     */
    protected function performVerification(array $data): bool
    {
        // هذه دالة مبسطة - في الواقع ستتصل بـ API البوابة للتحقق
        return match ($this->type) {
            'card' => $this->verifyCard($data),
            'bank_account' => $this->verifyBankAccount($data),
            'digital_wallet' => $this->verifyDigitalWallet($data),
            default => true,
        };
    }

    /**
     * التحقق من البطاقة
     */
    protected function verifyCard(array $data): bool
    {
        // التحقق من CVV أو 3D Secure
        return !empty($data['cvv']) || !empty($data['three_d_secure']);
    }

    /**
     * التحقق من الحساب البنكي
     */
    protected function verifyBankAccount(array $data): bool
    {
        // التحقق من المبالغ الصغيرة أو التحقق الفوري
        return !empty($data['micro_deposits']) || !empty($data['instant_verification']);
    }

    /**
     * التحقق من المحفظة الرقمية
     */
    protected function verifyDigitalWallet(array $data): bool
    {
        // التحقق من رمز OTP أو البصمة
        return !empty($data['otp']) || !empty($data['biometric']);
    }

    /**
     * حساب درجة المخاطر
     */
    public function calculateRiskScore(): int
    {
        $score = 0;

        // عوامل المخاطر
        if ($this->isExpired()) {
            $score += 30;
        }

        if ($this->isExpiringSoon()) {
            $score += 15;
        }

        if (!$this->is_verified) {
            $score += 25;
        }

        if ($this->usage_count === 0) {
            $score += 20;
        }

        // مؤشرات المخاطر المخزنة
        $riskIndicators = $this->risk_indicators ?? [];
        if (!empty($riskIndicators['high_risk_country'])) {
            $score += 20;
        }

        if (!empty($riskIndicators['suspicious_activity'])) {
            $score += 40;
        }

        return min($score, 100);
    }

    /**
     * تحديث مؤشرات المخاطر
     */
    public function updateRiskIndicators(array $indicators): void
    {
        $currentIndicators = $this->risk_indicators ?? [];
        $updatedIndicators = array_merge($currentIndicators, $indicators);

        $this->update(['risk_indicators' => $updatedIndicators]);
    }

    /**
     * Scope للطرق النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope للطرق المتحقق منها
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope للطرق القابلة للاستخدام
     */
    public function scopeUsable($query)
    {
        return $query->active()->verified();
    }

    /**
     * Scope للطرق الافتراضية
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope للطرق حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope للطرق منتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('type', 'card')
            ->whereRaw('LAST_DAY(MAKEDATE(exp_year, 1) + INTERVAL (exp_month - 1) MONTH) < CURDATE()');
    }

    /**
     * Scope للطرق قريبة انتهاء الصلاحية
     */
    public function scopeExpiringSoon($query, int $months = 2)
    {
        return $query->where('type', 'card')
            ->whereRaw('LAST_DAY(MAKEDATE(exp_year, 1) + INTERVAL (exp_month - 1) MONTH) < DATE_ADD(CURDATE(), INTERVAL ? MONTH)', [$months]);
    }
}
