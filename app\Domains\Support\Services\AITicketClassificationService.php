<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\Ticket;
use App\Domains\Support\Models\TicketCategory;
use App\Domains\Support\Models\KnowledgeBaseArticle;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة التصنيف الذكي للتذاكر - AI Ticket Classification Service
 * تستخدم الذكاء الاصطناعي لتصنيف وتحليل التذاكر تلقائياً
 */
class AITicketClassificationService
{
    protected array $priorityKeywords = [
        'critical' => [
            'عاجل', 'طارئ', 'حرج', 'متوقف', 'لا يعمل', 'خطأ فادح',
            'urgent', 'emergency', 'critical', 'down', 'not working', 'severe error',
            'urgent', 'urgence', 'critique', 'panne', 'ne fonctionne pas'
        ],
        'high' => [
            'مهم', 'سريع', 'مشكلة كبيرة', 'تأثير عالي',
            'important', 'high priority', 'major issue', 'high impact',
            'important', 'priorité élevée', 'problème majeur'
        ],
        'medium' => [
            'متوسط', 'عادي', 'مشكلة متوسطة',
            'medium', 'normal', 'moderate issue',
            'moyen', 'normal', 'problème modéré'
        ],
        'low' => [
            'بسيط', 'غير مهم', 'استفسار', 'سؤال',
            'low', 'minor', 'question', 'inquiry',
            'faible', 'mineur', 'question', 'demande'
        ]
    ];

    protected array $categoryKeywords = [
        'technical' => [
            'خطأ', 'مشكلة تقنية', 'لا يعمل', 'بطء', 'تعطل',
            'error', 'bug', 'technical issue', 'not working', 'slow', 'crash',
            'erreur', 'problème technique', 'ne fonctionne pas', 'lent', 'plantage'
        ],
        'billing' => [
            'فاتورة', 'دفع', 'رسوم', 'اشتراك', 'تجديد', 'استرداد',
            'billing', 'payment', 'invoice', 'subscription', 'refund', 'charge',
            'facturation', 'paiement', 'facture', 'abonnement', 'remboursement'
        ],
        'account' => [
            'حساب', 'تسجيل دخول', 'كلمة مرور', 'ملف شخصي', 'إعدادات',
            'account', 'login', 'password', 'profile', 'settings', 'access',
            'compte', 'connexion', 'mot de passe', 'profil', 'paramètres'
        ],
        'feature_request' => [
            'طلب ميزة', 'اقتراح', 'تحسين', 'إضافة',
            'feature request', 'suggestion', 'enhancement', 'improvement',
            'demande de fonctionnalité', 'suggestion', 'amélioration'
        ]
    ];

    protected array $sentimentKeywords = [
        'positive' => [
            'ممتاز', 'رائع', 'شكراً', 'مفيد', 'سعيد', 'راضي',
            'excellent', 'great', 'thank you', 'helpful', 'happy', 'satisfied',
            'excellent', 'formidable', 'merci', 'utile', 'heureux', 'satisfait'
        ],
        'negative' => [
            'سيء', 'فظيع', 'غاضب', 'محبط', 'مشكلة', 'لا أستطيع',
            'bad', 'terrible', 'angry', 'frustrated', 'problem', 'cannot',
            'mauvais', 'terrible', 'en colère', 'frustré', 'problème', 'ne peut pas'
        ],
        'neutral' => [
            'سؤال', 'استفسار', 'كيف', 'متى', 'أين',
            'question', 'inquiry', 'how', 'when', 'where',
            'question', 'demande', 'comment', 'quand', 'où'
        ]
    ];

    /**
     * تصنيف التذكرة باستخدام الذكاء الاصطناعي
     */
    public function classifyTicket(string $subject, string $description): array
    {
        $content = $subject . ' ' . $description;
        
        return [
            'category_id' => $this->predictCategory($content),
            'priority' => $this->predictPriority($content),
            'sentiment' => $this->analyzeSentiment($content),
            'confidence' => $this->calculateConfidence($content),
            'suggested_solutions' => $this->findSuggestedSolutions($content),
            'tags' => $this->extractTags($content),
            'language' => $this->detectLanguage($content),
            'urgency_score' => $this->calculateUrgencyScore($content),
            'complexity_level' => $this->assessComplexity($content),
        ];
    }

    /**
     * تحليل رد التذكرة
     */
    public function analyzeReply(string $content): array
    {
        return [
            'sentiment' => $this->analyzeSentiment($content),
            'confidence' => $this->calculateConfidence($content),
            'suggestions' => $this->generateReplySuggestions($content),
            'quality_score' => $this->assessReplyQuality($content),
            'resolution_indicators' => $this->detectResolutionIndicators($content),
            'escalation_triggers' => $this->detectEscalationTriggers($content),
        ];
    }

    /**
     * توقع فئة التذكرة
     */
    protected function predictCategory(string $content): ?int
    {
        $content = strtolower($content);
        $scores = [];

        // حساب النقاط لكل فئة بناءً على الكلمات المفتاحية
        foreach ($this->categoryKeywords as $categoryType => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                if (str_contains($content, strtolower($keyword))) {
                    $score += 1;
                }
            }
            $scores[$categoryType] = $score;
        }

        // العثور على أعلى نقاط
        $maxScore = max($scores);
        if ($maxScore === 0) {
            return null;
        }

        $predictedType = array_search($maxScore, $scores);
        
        // البحث عن الفئة المطابقة في قاعدة البيانات
        $category = TicketCategory::where('name', 'LIKE', "%{$predictedType}%")
                                 ->orWhere('ai_keywords', 'LIKE', "%{$predictedType}%")
                                 ->first();

        return $category?->id;
    }

    /**
     * توقع أولوية التذكرة
     */
    protected function predictPriority(string $content): string
    {
        $content = strtolower($content);
        $scores = [];

        foreach ($this->priorityKeywords as $priority => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                if (str_contains($content, strtolower($keyword))) {
                    $score += 1;
                }
            }
            $scores[$priority] = $score;
        }

        // إضافة عوامل إضافية للأولوية
        $urgencyFactors = [
            'عدد علامات التعجب' => substr_count($content, '!'),
            'كلمات بأحرف كبيرة' => preg_match_all('/[A-Z]{3,}/', $content),
            'كلمات الوقت' => $this->countTimeWords($content),
        ];

        // تعديل النقاط بناءً على العوامل الإضافية
        if ($urgencyFactors['عدد علامات التعجب'] >= 3) {
            $scores['critical'] += 2;
        }

        if ($urgencyFactors['كلمات بأحرف كبيرة'] >= 2) {
            $scores['high'] += 1;
        }

        $maxScore = max($scores);
        if ($maxScore === 0) {
            return 'medium';
        }

        return array_search($maxScore, $scores);
    }

    /**
     * تحليل المشاعر
     */
    protected function analyzeSentiment(string $content): array
    {
        $content = strtolower($content);
        $scores = [];

        foreach ($this->sentimentKeywords as $sentiment => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                if (str_contains($content, strtolower($keyword))) {
                    $score += 1;
                }
            }
            $scores[$sentiment] = $score;
        }

        $maxScore = max($scores);
        $dominantSentiment = $maxScore > 0 ? array_search($maxScore, $scores) : 'neutral';

        return [
            'label' => $dominantSentiment,
            'confidence' => $maxScore > 0 ? min($maxScore / 5, 1.0) : 0.5,
            'scores' => $scores,
        ];
    }

    /**
     * حساب مستوى الثقة
     */
    protected function calculateConfidence(string $content): float
    {
        $factors = [
            'length' => min(strlen($content) / 500, 1.0),
            'keywords' => $this->countTotalKeywords($content) / 10,
            'structure' => $this->assessContentStructure($content),
        ];

        return min(array_sum($factors) / count($factors), 1.0);
    }

    /**
     * البحث عن حلول مقترحة
     */
    protected function findSuggestedSolutions(string $content): array
    {
        $cacheKey = 'suggested_solutions_' . md5($content);
        
        return Cache::remember($cacheKey, 3600, function () use ($content) {
            // البحث في قاعدة المعرفة
            $articles = KnowledgeBaseArticle::published()
                ->where(function ($query) use ($content) {
                    $keywords = $this->extractKeywords($content);
                    foreach ($keywords as $keyword) {
                        $query->orWhere('title', 'LIKE', "%{$keyword}%")
                              ->orWhere('content', 'LIKE', "%{$keyword}%")
                              ->orWhere('tags', 'LIKE', "%{$keyword}%");
                    }
                })
                ->orderBy('view_count', 'desc')
                ->limit(5)
                ->get();

            return $articles->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->localized_title,
                    'url' => $article->permalink,
                    'relevance_score' => $this->calculateRelevanceScore($article),
                ];
            })->toArray();
        });
    }

    /**
     * استخراج الوسوم
     */
    protected function extractTags(string $content): array
    {
        $keywords = $this->extractKeywords($content);
        $tags = [];

        // تصفية الكلمات المفتاحية وتحويلها لوسوم
        foreach ($keywords as $keyword) {
            if (strlen($keyword) >= 3 && !in_array($keyword, ['the', 'and', 'or', 'في', 'من', 'إلى'])) {
                $tags[] = $keyword;
            }
        }

        return array_unique(array_slice($tags, 0, 10));
    }

    /**
     * كشف اللغة
     */
    protected function detectLanguage(string $content): string
    {
        // كشف بسيط للغة بناءً على الأحرف
        $arabicChars = preg_match_all('/[\x{0600}-\x{06FF}]/u', $content);
        $englishChars = preg_match_all('/[a-zA-Z]/', $content);
        $frenchChars = preg_match_all('/[àâäéèêëïîôöùûüÿç]/i', $content);

        if ($arabicChars > $englishChars && $arabicChars > $frenchChars) {
            return 'ar';
        } elseif ($frenchChars > $englishChars) {
            return 'fr';
        } else {
            return 'en';
        }
    }

    /**
     * حساب نقاط الإلحاح
     */
    protected function calculateUrgencyScore(string $content): int
    {
        $score = 0;
        $content = strtolower($content);

        // كلمات الإلحاح
        $urgencyWords = ['عاجل', 'فوري', 'urgent', 'asap', 'immediately', 'urgence'];
        foreach ($urgencyWords as $word) {
            if (str_contains($content, $word)) {
                $score += 3;
            }
        }

        // علامات التعجب
        $score += substr_count($content, '!');

        // كلمات الوقت
        $timeWords = ['اليوم', 'الآن', 'today', 'now', 'aujourd\'hui', 'maintenant'];
        foreach ($timeWords as $word) {
            if (str_contains($content, $word)) {
                $score += 2;
            }
        }

        return min($score, 10);
    }

    /**
     * تقييم مستوى التعقيد
     */
    protected function assessComplexity(string $content): string
    {
        $factors = [
            'length' => strlen($content),
            'technical_terms' => $this->countTechnicalTerms($content),
            'multiple_issues' => $this->detectMultipleIssues($content),
        ];

        $complexityScore = 0;

        if ($factors['length'] > 1000) $complexityScore += 2;
        if ($factors['technical_terms'] > 5) $complexityScore += 2;
        if ($factors['multiple_issues']) $complexityScore += 3;

        return match (true) {
            $complexityScore >= 5 => 'high',
            $complexityScore >= 3 => 'medium',
            default => 'low',
        };
    }

    /**
     * توليد اقتراحات للرد
     */
    protected function generateReplySuggestions(string $content): array
    {
        // تحليل محتوى الرد وتوليد اقتراحات
        $suggestions = [];

        if ($this->containsQuestion($content)) {
            $suggestions[] = 'يبدو أن العميل يطرح سؤالاً. تأكد من الإجابة عليه بوضوح.';
        }

        if ($this->containsComplaint($content)) {
            $suggestions[] = 'العميل يبدو غير راضي. فكر في تقديم اعتذار أو حل بديل.';
        }

        if ($this->containsTechnicalTerms($content)) {
            $suggestions[] = 'المحتوى يحتوي على مصطلحات تقنية. تأكد من شرحها بطريقة مبسطة.';
        }

        return $suggestions;
    }

    /**
     * تقييم جودة الرد
     */
    protected function assessReplyQuality(string $content): int
    {
        $score = 50; // نقطة البداية

        // طول الرد
        $length = strlen($content);
        if ($length < 50) $score -= 20;
        elseif ($length > 200) $score += 10;

        // وجود تحية
        if ($this->containsGreeting($content)) $score += 5;

        // وجود شكر
        if ($this->containsThank($content)) $score += 5;

        // وجود حل أو خطوات
        if ($this->containsSolution($content)) $score += 15;

        // الأدب والاحترام
        if ($this->containsPoliteLanguage($content)) $score += 10;

        return min(max($score, 0), 100);
    }

    /**
     * كشف مؤشرات الحل
     */
    protected function detectResolutionIndicators(string $content): array
    {
        $indicators = [];
        $content = strtolower($content);

        $resolutionWords = [
            'حل', 'تم الحل', 'مُحل', 'solved', 'resolved', 'fixed', 'résolu'
        ];

        foreach ($resolutionWords as $word) {
            if (str_contains($content, $word)) {
                $indicators[] = $word;
            }
        }

        return $indicators;
    }

    /**
     * كشف محفزات التصعيد
     */
    protected function detectEscalationTriggers(string $content): array
    {
        $triggers = [];
        $content = strtolower($content);

        $escalationWords = [
            'مدير', 'مشرف', 'شكوى', 'manager', 'supervisor', 'complaint', 'responsable'
        ];

        foreach ($escalationWords as $word) {
            if (str_contains($content, $word)) {
                $triggers[] = $word;
            }
        }

        return $triggers;
    }

    // دوال مساعدة
    protected function countTimeWords(string $content): int
    {
        $timeWords = ['فوراً', 'الآن', 'اليوم', 'غداً', 'now', 'today', 'tomorrow', 'maintenant'];
        $count = 0;
        foreach ($timeWords as $word) {
            if (str_contains(strtolower($content), strtolower($word))) {
                $count++;
            }
        }
        return $count;
    }

    protected function countTotalKeywords(string $content): int
    {
        $total = 0;
        foreach (array_merge($this->priorityKeywords, $this->categoryKeywords, $this->sentimentKeywords) as $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains(strtolower($content), strtolower($keyword))) {
                    $total++;
                }
            }
        }
        return $total;
    }

    protected function assessContentStructure(string $content): float
    {
        $score = 0;
        
        // وجود علامات ترقيم
        if (preg_match('/[.!?]/', $content)) $score += 0.2;
        
        // وجود فقرات متعددة
        if (substr_count($content, "\n") >= 2) $score += 0.3;
        
        // طول مناسب
        if (strlen($content) >= 100 && strlen($content) <= 1000) $score += 0.5;
        
        return min($score, 1.0);
    }

    protected function extractKeywords(string $content): array
    {
        // استخراج الكلمات المفتاحية
        $words = preg_split('/\s+/', strtolower($content));
        return array_filter($words, function ($word) {
            return strlen($word) >= 3;
        });
    }

    protected function calculateRelevanceScore($article): float
    {
        // حساب نقاط الصلة
        return ($article->view_count * 0.3) + ($article->helpful_count * 0.7);
    }

    protected function countTechnicalTerms(string $content): int
    {
        $technicalTerms = ['API', 'database', 'server', 'error', 'bug', 'خطأ', 'خادم', 'قاعدة بيانات'];
        $count = 0;
        foreach ($technicalTerms as $term) {
            if (str_contains(strtolower($content), strtolower($term))) {
                $count++;
            }
        }
        return $count;
    }

    protected function detectMultipleIssues(string $content): bool
    {
        $issueIndicators = ['أيضاً', 'كذلك', 'also', 'additionally', 'aussi', 'également'];
        foreach ($issueIndicators as $indicator) {
            if (str_contains(strtolower($content), strtolower($indicator))) {
                return true;
            }
        }
        return false;
    }

    protected function containsQuestion(string $content): bool
    {
        return str_contains($content, '?') || 
               str_contains($content, 'كيف') || 
               str_contains($content, 'متى') ||
               str_contains($content, 'how') ||
               str_contains($content, 'when');
    }

    protected function containsComplaint(string $content): bool
    {
        $complaintWords = ['مشكلة', 'شكوى', 'غير راضي', 'problem', 'complaint', 'unsatisfied'];
        foreach ($complaintWords as $word) {
            if (str_contains(strtolower($content), strtolower($word))) {
                return true;
            }
        }
        return false;
    }

    protected function containsTechnicalTerms(string $content): bool
    {
        return $this->countTechnicalTerms($content) > 0;
    }

    protected function containsGreeting(string $content): bool
    {
        $greetings = ['مرحبا', 'أهلا', 'hello', 'hi', 'bonjour', 'salut'];
        foreach ($greetings as $greeting) {
            if (str_contains(strtolower($content), strtolower($greeting))) {
                return true;
            }
        }
        return false;
    }

    protected function containsThank(string $content): bool
    {
        $thankWords = ['شكرا', 'thank', 'merci'];
        foreach ($thankWords as $word) {
            if (str_contains(strtolower($content), strtolower($word))) {
                return true;
            }
        }
        return false;
    }

    protected function containsSolution(string $content): bool
    {
        $solutionWords = ['حل', 'خطوات', 'solution', 'steps', 'solution', 'étapes'];
        foreach ($solutionWords as $word) {
            if (str_contains(strtolower($content), strtolower($word))) {
                return true;
            }
        }
        return false;
    }

    protected function containsPoliteLanguage(string $content): bool
    {
        $politeWords = ['من فضلك', 'لو سمحت', 'please', 's\'il vous plaît'];
        foreach ($politeWords as $word) {
            if (str_contains(strtolower($content), strtolower($word))) {
                return true;
            }
        }
        return false;
    }
}
