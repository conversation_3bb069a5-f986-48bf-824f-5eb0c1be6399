<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Exceptions\ECommerceMappingException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

/**
 * خدمة تخطيط البيانات من منصات التجارة الإلكترونية
 * تخطط الحقول الخارجية إلى حقول النظام الداخلي
 */
class ECommerceMappingService
{
    /**
     * تخطيط بيانات المنتج
     */
    public function mapProduct(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            $mapping = $integration->field_mapping['products'] ?? [];
            $defaultMapping = $this->getDefaultProductMapping($integration->platform->slug);
            
            // دمج التخطيط المخصص مع التخطيط الافتراضي
            $finalMapping = array_merge($defaultMapping, $mapping);
            
            return $this->applyMapping($externalData, $finalMapping);
            
        } catch (\Exception $e) {
            Log::error('Product mapping failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceMappingException(
                'Failed to map product data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تخطيط بيانات الطلب
     */
    public function mapOrder(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            $mapping = $integration->field_mapping['orders'] ?? [];
            $defaultMapping = $this->getDefaultOrderMapping($integration->platform->slug);
            
            // دمج التخطيط المخصص مع التخطيط الافتراضي
            $finalMapping = array_merge($defaultMapping, $mapping);
            
            return $this->applyMapping($externalData, $finalMapping);
            
        } catch (\Exception $e) {
            Log::error('Order mapping failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceMappingException(
                'Failed to map order data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تخطيط بيانات العميل
     */
    public function mapCustomer(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            $mapping = $integration->field_mapping['customers'] ?? [];
            $defaultMapping = $this->getDefaultCustomerMapping($integration->platform->slug);
            
            // دمج التخطيط المخصص مع التخطيط الافتراضي
            $finalMapping = array_merge($defaultMapping, $mapping);
            
            return $this->applyMapping($externalData, $finalMapping);
            
        } catch (\Exception $e) {
            Log::error('Customer mapping failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceMappingException(
                'Failed to map customer data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تخطيط بيانات عنصر الطلب
     */
    public function mapOrderItem(array $externalData, ECommerceIntegration $integration): array
    {
        try {
            $mapping = $integration->field_mapping['order_items'] ?? [];
            $defaultMapping = $this->getDefaultOrderItemMapping($integration->platform->slug);
            
            // دمج التخطيط المخصص مع التخطيط الافتراضي
            $finalMapping = array_merge($defaultMapping, $mapping);
            
            return $this->applyMapping($externalData, $finalMapping);
            
        } catch (\Exception $e) {
            Log::error('Order item mapping failed', [
                'integration_id' => $integration->id,
                'external_data' => $externalData,
                'error' => $e->getMessage(),
            ]);
            
            throw new ECommerceMappingException(
                'Failed to map order item data: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * تطبيق التخطيط
     */
    protected function applyMapping(array $externalData, array $mapping): array
    {
        $mappedData = [];

        foreach ($mapping as $internalField => $externalField) {
            if (is_string($externalField)) {
                // تخطيط بسيط
                $value = Arr::get($externalData, $externalField);
                if ($value !== null) {
                    $mappedData[$internalField] = $value;
                }
            } elseif (is_array($externalField)) {
                // تخطيط معقد
                $value = $this->applyComplexMapping($externalData, $externalField);
                if ($value !== null) {
                    $mappedData[$internalField] = $value;
                }
            }
        }

        return $mappedData;
    }

    /**
     * تطبيق التخطيط المعقد
     */
    protected function applyComplexMapping(array $externalData, array $mappingConfig): mixed
    {
        $type = $mappingConfig['type'] ?? 'direct';
        $source = $mappingConfig['source'] ?? null;
        $default = $mappingConfig['default'] ?? null;

        switch ($type) {
            case 'direct':
                return Arr::get($externalData, $source, $default);

            case 'concatenate':
                return $this->concatenateFields($externalData, $mappingConfig);

            case 'conditional':
                return $this->conditionalMapping($externalData, $mappingConfig);

            case 'transform':
                return $this->transformValue($externalData, $mappingConfig);

            case 'nested':
                return $this->nestedMapping($externalData, $mappingConfig);

            case 'array_map':
                return $this->arrayMapping($externalData, $mappingConfig);

            case 'calculate':
                return $this->calculateValue($externalData, $mappingConfig);

            default:
                return Arr::get($externalData, $source, $default);
        }
    }

    /**
     * دمج الحقول
     */
    protected function concatenateFields(array $externalData, array $config): ?string
    {
        $fields = $config['fields'] ?? [];
        $separator = $config['separator'] ?? ' ';
        $values = [];

        foreach ($fields as $field) {
            $value = Arr::get($externalData, $field);
            if ($value !== null && $value !== '') {
                $values[] = $value;
            }
        }

        return !empty($values) ? implode($separator, $values) : null;
    }

    /**
     * التخطيط الشرطي
     */
    protected function conditionalMapping(array $externalData, array $config): mixed
    {
        $conditions = $config['conditions'] ?? [];
        $default = $config['default'] ?? null;

        foreach ($conditions as $condition) {
            $field = $condition['field'] ?? null;
            $operator = $condition['operator'] ?? '=';
            $value = $condition['value'] ?? null;
            $result = $condition['result'] ?? null;

            if ($field && $this->evaluateCondition($externalData, $field, $operator, $value)) {
                if (is_string($result)) {
                    return Arr::get($externalData, $result);
                } else {
                    return $result;
                }
            }
        }

        return $default;
    }

    /**
     * تحويل القيمة
     */
    protected function transformValue(array $externalData, array $config): mixed
    {
        $source = $config['source'] ?? null;
        $transformer = $config['transformer'] ?? null;
        $parameters = $config['parameters'] ?? [];

        if (!$source || !$transformer) {
            return null;
        }

        $value = Arr::get($externalData, $source);
        if ($value === null) {
            return null;
        }

        switch ($transformer) {
            case 'string_to_float':
                return (float) str_replace(',', '', $value);

            case 'string_to_int':
                return (int) preg_replace('/[^0-9]/', '', $value);

            case 'boolean':
                return in_array(strtolower($value), ['true', '1', 'yes', 'on', 'enabled']);

            case 'date':
                $format = $parameters['format'] ?? 'Y-m-d H:i:s';
                try {
                    return \Carbon\Carbon::parse($value)->format($format);
                } catch (\Exception $e) {
                    return null;
                }

            case 'currency':
                $rate = $parameters['rate'] ?? 1;
                return (float) $value * $rate;

            case 'map_value':
                $mapping = $parameters['mapping'] ?? [];
                return $mapping[$value] ?? $value;

            case 'extract_number':
                preg_match('/[\d.]+/', $value, $matches);
                return isset($matches[0]) ? (float) $matches[0] : 0;

            case 'clean_html':
                return strip_tags(html_entity_decode($value));

            case 'slug':
                return \Illuminate\Support\Str::slug($value);

            default:
                return $value;
        }
    }

    /**
     * التخطيط المتداخل
     */
    protected function nestedMapping(array $externalData, array $config): array
    {
        $source = $config['source'] ?? null;
        $mapping = $config['mapping'] ?? [];

        if (!$source) {
            return [];
        }

        $nestedData = Arr::get($externalData, $source, []);
        if (!is_array($nestedData)) {
            return [];
        }

        return $this->applyMapping($nestedData, $mapping);
    }

    /**
     * تخطيط المصفوفة
     */
    protected function arrayMapping(array $externalData, array $config): array
    {
        $source = $config['source'] ?? null;
        $mapping = $config['mapping'] ?? [];

        if (!$source) {
            return [];
        }

        $arrayData = Arr::get($externalData, $source, []);
        if (!is_array($arrayData)) {
            return [];
        }

        $result = [];
        foreach ($arrayData as $item) {
            if (is_array($item)) {
                $result[] = $this->applyMapping($item, $mapping);
            }
        }

        return $result;
    }

    /**
     * حساب القيمة
     */
    protected function calculateValue(array $externalData, array $config): float
    {
        $expression = $config['expression'] ?? '';
        $variables = $config['variables'] ?? [];

        // استبدال المتغيرات
        foreach ($variables as $variable => $field) {
            $value = Arr::get($externalData, $field, 0);
            $numericValue = is_numeric($value) ? $value : 0;
            $expression = str_replace("{{$variable}}", $numericValue, $expression);
        }

        // تقييم التعبير
        try {
            return eval("return $expression;");
        } catch (\Exception $e) {
            Log::warning('Calculation failed', [
                'expression' => $expression,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    /**
     * تقييم الشرط
     */
    protected function evaluateCondition(array $data, string $field, string $operator, mixed $value): bool
    {
        $fieldValue = Arr::get($data, $field);

        switch ($operator) {
            case '=':
            case '==':
                return $fieldValue == $value;
            case '!=':
                return $fieldValue != $value;
            case '>':
                return $fieldValue > $value;
            case '>=':
                return $fieldValue >= $value;
            case '<':
                return $fieldValue < $value;
            case '<=':
                return $fieldValue <= $value;
            case 'in':
                return in_array($fieldValue, (array) $value);
            case 'not_in':
                return !in_array($fieldValue, (array) $value);
            case 'contains':
                return str_contains($fieldValue, $value);
            case 'starts_with':
                return str_starts_with($fieldValue, $value);
            case 'ends_with':
                return str_ends_with($fieldValue, $value);
            case 'regex':
                return preg_match($value, $fieldValue);
            case 'exists':
                return $fieldValue !== null;
            case 'not_exists':
                return $fieldValue === null;
            case 'empty':
                return empty($fieldValue);
            case 'not_empty':
                return !empty($fieldValue);
            default:
                return true;
        }
    }

    /**
     * الحصول على التخطيط الافتراضي للمنتج
     */
    protected function getDefaultProductMapping(string $platform): array
    {
        $mappings = [
            'shopify' => [
                'external_id' => 'id',
                'name' => 'title',
                'description' => 'body_html',
                'sku' => 'variants.0.sku',
                'price' => 'variants.0.price',
                'compare_price' => 'variants.0.compare_at_price',
                'inventory_quantity' => 'variants.0.inventory_quantity',
                'weight' => 'variants.0.weight',
                'weight_unit' => 'variants.0.weight_unit',
                'images' => 'images',
                'featured_image' => 'image.src',
                'vendor' => 'vendor',
                'product_type' => 'product_type',
                'tags' => 'tags',
                'status' => 'status',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
            'woocommerce' => [
                'external_id' => 'id',
                'name' => 'name',
                'description' => 'description',
                'short_description' => 'short_description',
                'sku' => 'sku',
                'price' => 'price',
                'regular_price' => 'regular_price',
                'sale_price' => 'sale_price',
                'stock_quantity' => 'stock_quantity',
                'weight' => 'weight',
                'dimensions' => 'dimensions',
                'images' => 'images',
                'categories' => 'categories',
                'tags' => 'tags',
                'status' => 'status',
                'date_created' => 'date_created',
                'date_modified' => 'date_modified',
            ],
            'magento' => [
                'external_id' => 'id',
                'name' => 'name',
                'description' => 'custom_attributes.description.value',
                'sku' => 'sku',
                'price' => 'price',
                'weight' => 'weight',
                'status' => 'status',
                'visibility' => 'visibility',
                'type_id' => 'type_id',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
        ];

        return $mappings[$platform] ?? [];
    }

    /**
     * الحصول على التخطيط الافتراضي للطلب
     */
    protected function getDefaultOrderMapping(string $platform): array
    {
        $mappings = [
            'shopify' => [
                'external_id' => 'id',
                'order_number' => 'order_number',
                'email' => 'email',
                'total_price' => 'total_price',
                'subtotal_price' => 'subtotal_price',
                'total_tax' => 'total_tax',
                'total_discounts' => 'total_discounts',
                'currency' => 'currency',
                'financial_status' => 'financial_status',
                'fulfillment_status' => 'fulfillment_status',
                'billing_address' => 'billing_address',
                'shipping_address' => 'shipping_address',
                'line_items' => 'line_items',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
            'woocommerce' => [
                'external_id' => 'id',
                'order_number' => 'number',
                'status' => 'status',
                'currency' => 'currency',
                'total' => 'total',
                'total_tax' => 'total_tax',
                'shipping_total' => 'shipping_total',
                'discount_total' => 'discount_total',
                'billing_address' => 'billing',
                'shipping_address' => 'shipping',
                'line_items' => 'line_items',
                'date_created' => 'date_created',
                'date_modified' => 'date_modified',
            ],
            'magento' => [
                'external_id' => 'entity_id',
                'increment_id' => 'increment_id',
                'status' => 'status',
                'state' => 'state',
                'currency_code' => 'order_currency_code',
                'grand_total' => 'grand_total',
                'subtotal' => 'subtotal',
                'tax_amount' => 'tax_amount',
                'shipping_amount' => 'shipping_amount',
                'discount_amount' => 'discount_amount',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
        ];

        return $mappings[$platform] ?? [];
    }

    /**
     * الحصول على التخطيط الافتراضي للعميل
     */
    protected function getDefaultCustomerMapping(string $platform): array
    {
        $mappings = [
            'shopify' => [
                'external_id' => 'id',
                'email' => 'email',
                'first_name' => 'first_name',
                'last_name' => 'last_name',
                'phone' => 'phone',
                'total_spent' => 'total_spent',
                'orders_count' => 'orders_count',
                'state' => 'state',
                'tags' => 'tags',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
            'woocommerce' => [
                'external_id' => 'id',
                'email' => 'email',
                'first_name' => 'first_name',
                'last_name' => 'last_name',
                'username' => 'username',
                'billing_address' => 'billing',
                'shipping_address' => 'shipping',
                'date_created' => 'date_created',
                'date_modified' => 'date_modified',
            ],
            'magento' => [
                'external_id' => 'id',
                'email' => 'email',
                'firstname' => 'firstname',
                'lastname' => 'lastname',
                'gender' => 'gender',
                'dob' => 'dob',
                'group_id' => 'group_id',
                'created_at' => 'created_at',
                'updated_at' => 'updated_at',
            ],
        ];

        return $mappings[$platform] ?? [];
    }

    /**
     * الحصول على التخطيط الافتراضي لعنصر الطلب
     */
    protected function getDefaultOrderItemMapping(string $platform): array
    {
        $mappings = [
            'shopify' => [
                'external_id' => 'id',
                'external_product_id' => 'product_id',
                'external_variant_id' => 'variant_id',
                'name' => 'name',
                'title' => 'title',
                'sku' => 'sku',
                'quantity' => 'quantity',
                'price' => 'price',
                'total_discount' => 'total_discount',
            ],
            'woocommerce' => [
                'external_id' => 'id',
                'external_product_id' => 'product_id',
                'name' => 'name',
                'sku' => 'sku',
                'quantity' => 'quantity',
                'price' => 'price',
                'total' => 'total',
                'subtotal' => 'subtotal',
                'total_tax' => 'total_tax',
            ],
            'magento' => [
                'external_id' => 'item_id',
                'external_product_id' => 'product_id',
                'name' => 'name',
                'sku' => 'sku',
                'qty_ordered' => 'qty_ordered',
                'price' => 'price',
                'row_total' => 'row_total',
                'tax_amount' => 'tax_amount',
            ],
        ];

        return $mappings[$platform] ?? [];
    }
}
