<?php

return [
    /*
    |--------------------------------------------------------------------------
    | CRM Compliance Configuration
    |--------------------------------------------------------------------------
    |
    | إعدادات الامتثال لنظام CRM حسب الدول والقوانين المحلية
    |
    */

    'default_country' => env('CRM_DEFAULT_COUNTRY', 'SA'),

    /*
    |--------------------------------------------------------------------------
    | Country-Specific Compliance Settings
    |--------------------------------------------------------------------------
    */
    'countries' => [
        // المملكة العربية السعودية
        'SA' => [
            'name' => 'المملكة العربية السعودية',
            'currency' => 'SAR',
            'timezone' => 'Asia/Riyadh',
            'language' => 'ar',
            'date_format' => 'd/m/Y',
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimals' => 2,
            ],
            'gdpr_required' => false,
            'data_localization' => true,
            'tax_requirements' => [
                'vat_enabled' => true,
                'vat_rate' => 15,
                'tax_number_required' => true,
                'tax_number_format' => '/^3[0-9]{14}$/',
                'e_invoicing_required' => true,
            ],
            'privacy_laws' => [
                'pdpl' => true, // Personal Data Protection Law
                'consent_required' => true,
                'data_retention_max_years' => 7,
                'cross_border_transfer_restricted' => true,
            ],
            'business_requirements' => [
                'commercial_registration_required' => true,
                'cr_number_format' => '/^[0-9]{10}$/',
                'industry_classification_required' => true,
                'authorized_signatory_required' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['whatsapp', 'sms', 'email'],
                'business_hours' => [
                    'start' => '08:00',
                    'end' => '17:00',
                    'timezone' => 'Asia/Riyadh',
                    'weekend' => ['friday', 'saturday'],
                ],
                'ramadan_adjustments' => true,
                'national_holidays' => [
                    'national_day' => '09-23',
                    'founding_day' => '02-22',
                    'eid_al_fitr' => 'variable',
                    'eid_al_adha' => 'variable',
                ],
            ],
        ],

        // الإمارات العربية المتحدة
        'AE' => [
            'name' => 'الإمارات العربية المتحدة',
            'currency' => 'AED',
            'timezone' => 'Asia/Dubai',
            'language' => 'ar',
            'date_format' => 'd/m/Y',
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimals' => 2,
            ],
            'gdpr_required' => false,
            'data_localization' => false,
            'tax_requirements' => [
                'vat_enabled' => true,
                'vat_rate' => 5,
                'tax_number_required' => true,
                'tax_number_format' => '/^[0-9]{15}$/',
                'e_invoicing_required' => false,
            ],
            'privacy_laws' => [
                'uae_data_protection_law' => true,
                'consent_required' => true,
                'data_retention_max_years' => 5,
                'cross_border_transfer_restricted' => false,
            ],
            'business_requirements' => [
                'trade_license_required' => true,
                'license_number_format' => '/^[A-Z0-9]{6,12}$/',
                'emirate_specific_rules' => true,
                'free_zone_considerations' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['whatsapp', 'email', 'sms'],
                'business_hours' => [
                    'start' => '09:00',
                    'end' => '18:00',
                    'timezone' => 'Asia/Dubai',
                    'weekend' => ['saturday', 'sunday'],
                ],
                'ramadan_adjustments' => true,
            ],
        ],

        // مصر
        'EG' => [
            'name' => 'جمهورية مصر العربية',
            'currency' => 'EGP',
            'timezone' => 'Africa/Cairo',
            'language' => 'ar',
            'date_format' => 'd/m/Y',
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimals' => 2,
            ],
            'gdpr_required' => false,
            'data_localization' => true,
            'tax_requirements' => [
                'vat_enabled' => true,
                'vat_rate' => 14,
                'tax_number_required' => true,
                'tax_number_format' => '/^[0-9]{9}$/',
                'e_invoicing_required' => true,
            ],
            'privacy_laws' => [
                'egypt_data_protection_law' => true,
                'consent_required' => true,
                'data_retention_max_years' => 5,
                'cross_border_transfer_restricted' => true,
            ],
            'business_requirements' => [
                'commercial_registration_required' => true,
                'tax_card_required' => true,
                'industry_license_required' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['whatsapp', 'facebook', 'sms'],
                'business_hours' => [
                    'start' => '09:00',
                    'end' => '17:00',
                    'timezone' => 'Africa/Cairo',
                    'weekend' => ['friday', 'saturday'],
                ],
                'ramadan_adjustments' => true,
            ],
        ],

        // المغرب
        'MA' => [
            'name' => 'المملكة المغربية',
            'currency' => 'MAD',
            'timezone' => 'Africa/Casablanca',
            'language' => 'ar',
            'date_format' => 'd/m/Y',
            'number_format' => [
                'decimal_separator' => ',',
                'thousands_separator' => ' ',
                'decimals' => 2,
            ],
            'gdpr_required' => false,
            'data_localization' => false,
            'tax_requirements' => [
                'vat_enabled' => true,
                'vat_rate' => 20,
                'tax_number_required' => true,
                'tax_number_format' => '/^[0-9]{8}$/',
                'e_invoicing_required' => false,
            ],
            'privacy_laws' => [
                'morocco_data_protection_law' => true,
                'consent_required' => true,
                'data_retention_max_years' => 5,
                'cross_border_transfer_restricted' => false,
            ],
            'business_requirements' => [
                'commercial_registration_required' => true,
                'professional_tax_required' => true,
                'cnss_registration_required' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['whatsapp', 'email', 'phone'],
                'business_hours' => [
                    'start' => '08:30',
                    'end' => '17:30',
                    'timezone' => 'Africa/Casablanca',
                    'weekend' => ['saturday', 'sunday'],
                ],
                'ramadan_adjustments' => true,
                'languages' => ['ar', 'fr'],
            ],
        ],

        // الاتحاد الأوروبي (GDPR)
        'EU' => [
            'name' => 'الاتحاد الأوروبي',
            'currency' => 'EUR',
            'timezone' => 'Europe/Brussels',
            'language' => 'en',
            'date_format' => 'd/m/Y',
            'number_format' => [
                'decimal_separator' => ',',
                'thousands_separator' => '.',
                'decimals' => 2,
            ],
            'gdpr_required' => true,
            'data_localization' => true,
            'tax_requirements' => [
                'vat_enabled' => true,
                'vat_rate' => 'variable', // يختلف حسب الدولة
                'tax_number_required' => true,
                'e_invoicing_required' => 'variable',
            ],
            'privacy_laws' => [
                'gdpr' => true,
                'consent_required' => true,
                'explicit_consent_required' => true,
                'right_to_be_forgotten' => true,
                'data_portability' => true,
                'data_retention_max_years' => 'purpose_limited',
                'cross_border_transfer_restricted' => true,
                'dpo_required' => true, // Data Protection Officer
                'privacy_impact_assessment' => true,
            ],
            'business_requirements' => [
                'vat_number_required' => true,
                'eori_number_for_trade' => true,
                'country_specific_licenses' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['email', 'phone', 'linkedin'],
                'opt_in_required' => true,
                'unsubscribe_required' => true,
                'cookie_consent_required' => true,
            ],
        ],

        // الولايات المتحدة الأمريكية
        'US' => [
            'name' => 'الولايات المتحدة الأمريكية',
            'currency' => 'USD',
            'timezone' => 'America/New_York',
            'language' => 'en',
            'date_format' => 'm/d/Y',
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimals' => 2,
            ],
            'gdpr_required' => false,
            'data_localization' => false,
            'tax_requirements' => [
                'sales_tax_enabled' => true,
                'sales_tax_rate' => 'variable', // يختلف حسب الولاية
                'tax_id_required' => true,
                'tax_id_format' => '/^[0-9]{2}-[0-9]{7}$/', // EIN format
            ],
            'privacy_laws' => [
                'ccpa' => true, // California Consumer Privacy Act
                'coppa' => true, // Children's Online Privacy Protection Act
                'can_spam' => true,
                'tcpa' => true, // Telephone Consumer Protection Act
                'consent_required' => 'state_dependent',
                'opt_out_required' => true,
            ],
            'business_requirements' => [
                'ein_required' => true,
                'state_registration_required' => true,
                'industry_specific_licenses' => true,
            ],
            'communication_preferences' => [
                'preferred_channels' => ['email', 'phone', 'sms'],
                'business_hours' => [
                    'start' => '09:00',
                    'end' => '17:00',
                    'timezone' => 'America/New_York',
                    'weekend' => ['saturday', 'sunday'],
                ],
                'do_not_call_registry' => true,
                'can_spam_compliance' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Global Compliance Rules
    |--------------------------------------------------------------------------
    */
    'global_rules' => [
        'data_encryption' => [
            'at_rest' => true,
            'in_transit' => true,
            'key_rotation' => true,
        ],
        'audit_trail' => [
            'enabled' => true,
            'retention_years' => 7,
            'immutable' => true,
        ],
        'access_control' => [
            'role_based' => true,
            'two_factor_auth' => false,
            'session_timeout' => 120, // minutes
        ],
        'data_backup' => [
            'frequency' => 'daily',
            'retention_days' => 90,
            'geographic_distribution' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Industry-Specific Compliance
    |--------------------------------------------------------------------------
    */
    'industries' => [
        'financial_services' => [
            'additional_requirements' => [
                'kyc_required' => true,
                'aml_screening' => true,
                'pci_dss_compliance' => true,
                'enhanced_due_diligence' => true,
            ],
        ],
        'healthcare' => [
            'additional_requirements' => [
                'hipaa_compliance' => true,
                'patient_consent_required' => true,
                'medical_data_encryption' => true,
            ],
        ],
        'education' => [
            'additional_requirements' => [
                'ferpa_compliance' => true,
                'student_privacy_protection' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'compliance_checks' => [
            'frequency' => 'daily',
            'automated_reports' => true,
            'alert_thresholds' => [
                'data_breach_detection' => true,
                'unusual_access_patterns' => true,
                'consent_expiry_warnings' => 30, // days
            ],
        ],
        'reporting' => [
            'compliance_dashboard' => true,
            'regulatory_reports' => true,
            'audit_reports' => true,
        ],
    ],
];
