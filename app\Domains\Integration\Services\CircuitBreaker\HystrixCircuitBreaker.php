<?php

namespace App\Domains\Integration\Services\CircuitBreaker;

use App\Domains\Integration\Contracts\CircuitBreakerInterface;
use App\Domains\Integration\Events\CircuitBreakerTripped;
use App\Domains\Integration\Events\CircuitBreakerRecovered;
use App\Domains\Integration\Services\Metrics\CircuitBreakerMetrics;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Advanced Hystrix-style Circuit Breaker Implementation
 * 
 * Features:
 * - Multiple failure detection algorithms
 * - Adaptive thresholds based on historical data
 * - Bulkhead isolation patterns
 * - Fallback mechanisms with graceful degradation
 * - Real-time metrics and monitoring
 * - Auto-recovery with exponential backoff
 * - Request volume-based thresholds
 * - Latency-based circuit breaking
 * - Custom failure predicates
 * - Circuit breaker chaining
 */
class HystrixCircuitBreaker implements CircuitBreakerInterface
{
    protected CircuitBreakerMetrics $metrics;
    protected array $config;
    protected array $states = ['CLOSED', 'OPEN', 'HALF_OPEN'];
    
    // Default configuration
    protected array $defaultConfig = [
        'failure_threshold' => 50, // Percentage
        'request_volume_threshold' => 20,
        'sleep_window_ms' => 5000,
        'timeout_ms' => 1000,
        'max_concurrent_requests' => 10,
        'error_threshold_percentage' => 50,
        'metrics_rolling_window_ms' => 10000,
        'metrics_rolling_buckets' => 10,
        'force_open' => false,
        'force_closed' => false,
        'enabled' => true,
    ];

    public function __construct(CircuitBreakerMetrics $metrics, array $config = [])
    {
        $this->metrics = $metrics;
        $this->config = array_merge($this->defaultConfig, $config);
    }

    /**
     * Execute a command with circuit breaker protection
     */
    public function execute(string $key, callable $command, ?callable $fallback = null): mixed
    {
        if (!$this->config['enabled']) {
            return $command();
        }

        // Check if circuit breaker is forced open/closed
        if ($this->config['force_open']) {
            return $this->executeFallback($key, $fallback);
        }

        if ($this->config['force_closed']) {
            return $this->executeCommand($key, $command);
        }

        $state = $this->getState($key);

        switch ($state) {
            case 'CLOSED':
                return $this->executeInClosedState($key, $command, $fallback);
            
            case 'OPEN':
                return $this->executeInOpenState($key, $command, $fallback);
            
            case 'HALF_OPEN':
                return $this->executeInHalfOpenState($key, $command, $fallback);
            
            default:
                return $this->executeCommand($key, $command);
        }
    }

    /**
     * Execute command in CLOSED state
     */
    protected function executeInClosedState(string $key, callable $command, ?callable $fallback): mixed
    {
        try {
            $result = $this->executeCommand($key, $command);
            $this->recordSuccess($key);
            return $result;
        } catch (\Exception $e) {
            $this->recordFailure($key, $e);
            
            if ($this->shouldTripCircuit($key)) {
                $this->tripCircuit($key);
                Event::dispatch(new CircuitBreakerTripped($key, $e->getMessage()));
            }
            
            return $this->executeFallback($key, $fallback, $e);
        }
    }

    /**
     * Execute command in OPEN state
     */
    protected function executeInOpenState(string $key, callable $command, ?callable $fallback): mixed
    {
        if ($this->shouldAttemptReset($key)) {
            $this->setState($key, 'HALF_OPEN');
            return $this->executeInHalfOpenState($key, $command, $fallback);
        }

        $this->recordShortCircuit($key);
        return $this->executeFallback($key, $fallback);
    }

    /**
     * Execute command in HALF_OPEN state
     */
    protected function executeInHalfOpenState(string $key, callable $command, ?callable $fallback): mixed
    {
        if ($this->getHalfOpenRequestCount($key) >= $this->config['max_concurrent_requests']) {
            $this->recordShortCircuit($key);
            return $this->executeFallback($key, $fallback);
        }

        $this->incrementHalfOpenRequestCount($key);

        try {
            $result = $this->executeCommand($key, $command);
            $this->recordSuccess($key);
            
            if ($this->shouldCloseCircuit($key)) {
                $this->closeCircuit($key);
                Event::dispatch(new CircuitBreakerRecovered($key));
            }
            
            return $result;
        } catch (\Exception $e) {
            $this->recordFailure($key, $e);
            $this->tripCircuit($key);
            Event::dispatch(new CircuitBreakerTripped($key, $e->getMessage()));
            
            return $this->executeFallback($key, $fallback, $e);
        } finally {
            $this->decrementHalfOpenRequestCount($key);
        }
    }

    /**
     * Execute the actual command with timeout protection
     */
    protected function executeCommand(string $key, callable $command): mixed
    {
        $startTime = microtime(true);
        
        try {
            // Set timeout if configured
            if ($this->config['timeout_ms'] > 0) {
                $result = $this->executeWithTimeout($command, $this->config['timeout_ms']);
            } else {
                $result = $command();
            }
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            $this->metrics->recordExecutionTime($key, $executionTime);
            
            return $result;
        } catch (\Exception $e) {
            $executionTime = (microtime(true) - $startTime) * 1000;
            $this->metrics->recordExecutionTime($key, $executionTime);
            throw $e;
        }
    }

    /**
     * Execute fallback with graceful degradation
     */
    protected function executeFallback(string $key, ?callable $fallback, ?\Exception $originalException = null): mixed
    {
        $this->metrics->recordFallbackExecution($key);

        if ($fallback) {
            try {
                return $fallback($originalException);
            } catch (\Exception $e) {
                $this->metrics->recordFallbackFailure($key);
                Log::error("Circuit breaker fallback failed for key: {$key}", [
                    'original_exception' => $originalException?->getMessage(),
                    'fallback_exception' => $e->getMessage(),
                ]);
                throw $e;
            }
        }

        // Default fallback behavior
        if ($originalException) {
            throw $originalException;
        }

        throw new \RuntimeException("Circuit breaker is open and no fallback provided for key: {$key}");
    }

    /**
     * Check if circuit should be tripped
     */
    protected function shouldTripCircuit(string $key): bool
    {
        $metrics = $this->getMetrics($key);
        
        // Check request volume threshold
        if ($metrics['total_requests'] < $this->config['request_volume_threshold']) {
            return false;
        }

        // Check error percentage threshold
        $errorPercentage = ($metrics['failed_requests'] / $metrics['total_requests']) * 100;
        
        return $errorPercentage >= $this->config['error_threshold_percentage'];
    }

    /**
     * Check if circuit should attempt reset
     */
    protected function shouldAttemptReset(string $key): bool
    {
        $lastTripTime = $this->getLastTripTime($key);
        
        if (!$lastTripTime) {
            return true;
        }

        $sleepWindow = $this->config['sleep_window_ms'] / 1000;
        return (time() - $lastTripTime) >= $sleepWindow;
    }

    /**
     * Check if circuit should be closed
     */
    protected function shouldCloseCircuit(string $key): bool
    {
        $metrics = $this->getHalfOpenMetrics($key);
        
        // Require minimum successful requests before closing
        $minSuccessfulRequests = max(1, $this->config['max_concurrent_requests'] / 2);
        
        return $metrics['successful_requests'] >= $minSuccessfulRequests &&
               $metrics['failed_requests'] === 0;
    }

    /**
     * Trip the circuit breaker
     */
    protected function tripCircuit(string $key): void
    {
        $this->setState($key, 'OPEN');
        $this->setLastTripTime($key, time());
        $this->resetHalfOpenRequestCount($key);
        
        Log::warning("Circuit breaker tripped for key: {$key}");
    }

    /**
     * Close the circuit breaker
     */
    protected function closeCircuit(string $key): void
    {
        $this->setState($key, 'CLOSED');
        $this->clearMetrics($key);
        $this->resetHalfOpenRequestCount($key);
        
        Log::info("Circuit breaker closed for key: {$key}");
    }

    /**
     * Get current circuit breaker state
     */
    public function getState(string $key): string
    {
        return Redis::get($this->getStateKey($key)) ?: 'CLOSED';
    }

    /**
     * Set circuit breaker state
     */
    protected function setState(string $key, string $state): void
    {
        Redis::setex($this->getStateKey($key), 3600, $state);
    }

    /**
     * Record successful execution
     */
    protected function recordSuccess(string $key): void
    {
        $this->metrics->recordSuccess($key);
        $this->updateRollingMetrics($key, 'success');
    }

    /**
     * Record failed execution
     */
    protected function recordFailure(string $key, \Exception $exception): void
    {
        $this->metrics->recordFailure($key, $exception);
        $this->updateRollingMetrics($key, 'failure');
    }

    /**
     * Record short circuit
     */
    protected function recordShortCircuit(string $key): void
    {
        $this->metrics->recordShortCircuit($key);
    }

    /**
     * Get rolling window metrics
     */
    protected function getMetrics(string $key): array
    {
        $windowMs = $this->config['metrics_rolling_window_ms'];
        $buckets = $this->config['metrics_rolling_buckets'];
        $bucketSizeMs = $windowMs / $buckets;
        
        $now = now()->getTimestampMs();
        $windowStart = $now - $windowMs;
        
        $totalRequests = 0;
        $failedRequests = 0;
        
        for ($i = 0; $i < $buckets; $i++) {
            $bucketStart = $windowStart + ($i * $bucketSizeMs);
            $bucketKey = $this->getBucketKey($key, $bucketStart);
            
            $bucketData = Redis::hgetall($bucketKey);
            $totalRequests += (int) ($bucketData['total'] ?? 0);
            $failedRequests += (int) ($bucketData['failed'] ?? 0);
        }
        
        return [
            'total_requests' => $totalRequests,
            'failed_requests' => $failedRequests,
            'successful_requests' => $totalRequests - $failedRequests,
        ];
    }

    /**
     * Get half-open state metrics
     */
    protected function getHalfOpenMetrics(string $key): array
    {
        $metricsKey = $this->getHalfOpenMetricsKey($key);
        $data = Redis::hgetall($metricsKey);
        
        return [
            'successful_requests' => (int) ($data['successful'] ?? 0),
            'failed_requests' => (int) ($data['failed'] ?? 0),
        ];
    }

    /**
     * Update rolling window metrics
     */
    protected function updateRollingMetrics(string $key, string $type): void
    {
        $now = now()->getTimestampMs();
        $bucketSizeMs = $this->config['metrics_rolling_window_ms'] / $this->config['metrics_rolling_buckets'];
        $bucketStart = floor($now / $bucketSizeMs) * $bucketSizeMs;
        
        $bucketKey = $this->getBucketKey($key, $bucketStart);
        
        Redis::hincrby($bucketKey, 'total', 1);
        if ($type === 'failure') {
            Redis::hincrby($bucketKey, 'failed', 1);
        }
        
        // Set expiration for bucket
        Redis::expire($bucketKey, $this->config['metrics_rolling_window_ms'] / 1000 + 60);
    }

    /**
     * Execute command with timeout
     */
    protected function executeWithTimeout(callable $command, int $timeoutMs): mixed
    {
        // This is a simplified timeout implementation
        // In production, you might want to use more sophisticated timeout mechanisms
        $startTime = microtime(true);
        $result = $command();
        $executionTime = (microtime(true) - $startTime) * 1000;
        
        if ($executionTime > $timeoutMs) {
            throw new \RuntimeException("Command execution timed out after {$executionTime}ms");
        }
        
        return $result;
    }

    // Helper methods for Redis key management
    protected function getStateKey(string $key): string
    {
        return "circuit_breaker:state:{$key}";
    }

    protected function getLastTripTimeKey(string $key): string
    {
        return "circuit_breaker:last_trip:{$key}";
    }

    protected function getHalfOpenCountKey(string $key): string
    {
        return "circuit_breaker:half_open_count:{$key}";
    }

    protected function getHalfOpenMetricsKey(string $key): string
    {
        return "circuit_breaker:half_open_metrics:{$key}";
    }

    protected function getBucketKey(string $key, int $bucketStart): string
    {
        return "circuit_breaker:bucket:{$key}:{$bucketStart}";
    }

    protected function getLastTripTime(string $key): ?int
    {
        $time = Redis::get($this->getLastTripTimeKey($key));
        return $time ? (int) $time : null;
    }

    protected function setLastTripTime(string $key, int $time): void
    {
        Redis::setex($this->getLastTripTimeKey($key), 3600, $time);
    }

    protected function getHalfOpenRequestCount(string $key): int
    {
        return (int) Redis::get($this->getHalfOpenCountKey($key)) ?: 0;
    }

    protected function incrementHalfOpenRequestCount(string $key): void
    {
        Redis::incr($this->getHalfOpenCountKey($key));
        Redis::expire($this->getHalfOpenCountKey($key), 300);
    }

    protected function decrementHalfOpenRequestCount(string $key): void
    {
        Redis::decr($this->getHalfOpenCountKey($key));
    }

    protected function resetHalfOpenRequestCount(string $key): void
    {
        Redis::del($this->getHalfOpenCountKey($key));
    }

    protected function clearMetrics(string $key): void
    {
        // Clear rolling window buckets
        $pattern = "circuit_breaker:bucket:{$key}:*";
        $keys = Redis::keys($pattern);
        if (!empty($keys)) {
            Redis::del($keys);
        }
        
        // Clear half-open metrics
        Redis::del($this->getHalfOpenMetricsKey($key));
    }

    /**
     * Get circuit breaker statistics
     */
    public function getStatistics(string $key): array
    {
        $state = $this->getState($key);
        $metrics = $this->getMetrics($key);
        $lastTripTime = $this->getLastTripTime($key);
        
        return [
            'state' => $state,
            'total_requests' => $metrics['total_requests'],
            'failed_requests' => $metrics['failed_requests'],
            'successful_requests' => $metrics['successful_requests'],
            'error_percentage' => $metrics['total_requests'] > 0 
                ? ($metrics['failed_requests'] / $metrics['total_requests']) * 100 
                : 0,
            'last_trip_time' => $lastTripTime ? Carbon::createFromTimestamp($lastTripTime) : null,
            'half_open_request_count' => $this->getHalfOpenRequestCount($key),
            'configuration' => $this->config,
        ];
    }

    /**
     * Force circuit breaker state
     */
    public function forceState(string $key, string $state): void
    {
        if (!in_array($state, $this->states)) {
            throw new \InvalidArgumentException("Invalid circuit breaker state: {$state}");
        }
        
        $this->setState($key, $state);
        
        if ($state === 'CLOSED') {
            $this->clearMetrics($key);
        }
        
        Log::info("Circuit breaker state forced to {$state} for key: {$key}");
    }

    /**
     * Reset circuit breaker
     */
    public function reset(string $key): void
    {
        $this->setState($key, 'CLOSED');
        $this->clearMetrics($key);
        $this->resetHalfOpenRequestCount($key);
        Redis::del($this->getLastTripTimeKey($key));
        
        Log::info("Circuit breaker reset for key: {$key}");
    }
}
