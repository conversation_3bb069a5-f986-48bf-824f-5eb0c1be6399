<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasFiles;
use Laravel\Scout\Searchable;

/**
 * نموذج مقال قاعدة المعرفة - Knowledge Base Article
 * يدعم متعدد اللغات والبحث الذكي والذكاء الاصطناعي
 */
class KnowledgeBaseArticle extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasFiles, Searchable;

    protected $fillable = [
        'title',
        'title_ar',
        'title_fr',
        'title_en',
        'slug',
        'content',
        'content_ar',
        'content_fr',
        'content_en',
        'excerpt',
        'excerpt_ar',
        'excerpt_fr',
        'excerpt_en',
        'category_id',
        'author_id',
        'status',
        'visibility',
        'featured',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'reading_time',
        'difficulty_level',
        'target_audience',
        'related_products',
        'related_modules',
        'tags',
        'ai_generated',
        'ai_source_tickets',
        'ai_confidence',
        'seo_score',
        'view_count',
        'helpful_count',
        'not_helpful_count',
        'last_reviewed_at',
        'last_reviewed_by',
        'next_review_date',
        'version',
        'changelog',
        'external_links',
        'video_url',
        'estimated_read_time',
        'prerequisites',
        'learning_objectives',
        'metadata',
    ];

    protected $casts = [
        'featured' => 'boolean',
        'sort_order' => 'integer',
        'reading_time' => 'integer',
        'related_products' => 'array',
        'related_modules' => 'array',
        'tags' => 'array',
        'ai_generated' => 'boolean',
        'ai_source_tickets' => 'array',
        'ai_confidence' => 'decimal:2',
        'seo_score' => 'integer',
        'view_count' => 'integer',
        'helpful_count' => 'integer',
        'not_helpful_count' => 'integer',
        'last_reviewed_at' => 'datetime',
        'next_review_date' => 'date',
        'version' => 'decimal:2',
        'changelog' => 'array',
        'external_links' => 'array',
        'estimated_read_time' => 'integer',
        'prerequisites' => 'array',
        'learning_objectives' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع فئة قاعدة المعرفة
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBaseCategory::class, 'category_id');
    }

    /**
     * العلاقة مع المؤلف
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'author_id');
    }

    /**
     * العلاقة مع المراجع الأخير
     */
    public function lastReviewer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'last_reviewed_by');
    }

    /**
     * العلاقة مع التقييمات
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(KnowledgeBaseRating::class, 'article_id');
    }

    /**
     * العلاقة مع المشاهدات
     */
    public function views(): HasMany
    {
        return $this->hasMany(KnowledgeBaseView::class, 'article_id');
    }

    /**
     * العلاقة مع التعليقات
     */
    public function comments(): HasMany
    {
        return $this->hasMany(KnowledgeBaseComment::class, 'article_id');
    }

    /**
     * العلاقة مع المقالات ذات الصلة
     */
    public function relatedArticles(): BelongsToMany
    {
        return $this->belongsToMany(
            self::class,
            'knowledge_base_related_articles',
            'article_id',
            'related_article_id'
        );
    }

    /**
     * العلاقة مع الوسوم
     */
    public function articleTags(): BelongsToMany
    {
        return $this->belongsToMany(KnowledgeBaseTag::class, 'knowledge_base_article_tags');
    }

    /**
     * الحصول على العنوان بناءً على اللغة
     */
    public function getLocalizedTitleAttribute(): string
    {
        $locale = app()->getLocale();
        
        return match ($locale) {
            'ar' => $this->title_ar ?? $this->title,
            'fr' => $this->title_fr ?? $this->title,
            'en' => $this->title_en ?? $this->title,
            default => $this->title,
        };
    }

    /**
     * الحصول على المحتوى بناءً على اللغة
     */
    public function getLocalizedContentAttribute(): string
    {
        $locale = app()->getLocale();
        
        return match ($locale) {
            'ar' => $this->content_ar ?? $this->content,
            'fr' => $this->content_fr ?? $this->content,
            'en' => $this->content_en ?? $this->content,
            default => $this->content,
        };
    }

    /**
     * الحصول على المقتطف بناءً على اللغة
     */
    public function getLocalizedExcerptAttribute(): string
    {
        $locale = app()->getLocale();
        
        return match ($locale) {
            'ar' => $this->excerpt_ar ?? $this->excerpt,
            'fr' => $this->excerpt_fr ?? $this->excerpt,
            'en' => $this->excerpt_en ?? $this->excerpt,
            default => $this->excerpt,
        } ?? \Str::limit(strip_tags($this->localized_content), 200);
    }

    /**
     * الحصول على الرابط الدائم
     */
    public function getPermalinkAttribute(): string
    {
        return route('knowledge-base.show', ['slug' => $this->slug]);
    }

    /**
     * الحصول على نسبة المفيد
     */
    public function getHelpfulPercentageAttribute(): float
    {
        $total = $this->helpful_count + $this->not_helpful_count;
        
        if ($total === 0) {
            return 0;
        }

        return round(($this->helpful_count / $total) * 100, 1);
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRatingAttribute(): ?float
    {
        $average = $this->ratings()->avg('rating');
        return $average ? round($average, 1) : null;
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getRatingsCountAttribute(): int
    {
        return $this->ratings()->count();
    }

    /**
     * الحصول على مستوى الشعبية
     */
    public function getPopularityScoreAttribute(): float
    {
        $viewWeight = 1;
        $helpfulWeight = 5;
        $ratingWeight = 3;

        $score = ($this->view_count * $viewWeight) +
                ($this->helpful_count * $helpfulWeight) +
                (($this->average_rating ?? 0) * $this->ratings_count * $ratingWeight);

        return round($score, 2);
    }

    /**
     * التحقق من الحاجة للمراجعة
     */
    public function getNeedsReviewAttribute(): bool
    {
        return $this->next_review_date && now()->isAfter($this->next_review_date);
    }

    /**
     * الحصول على حالة المقال
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'مسودة',
            'review' => 'قيد المراجعة',
            'published' => 'منشور',
            'archived' => 'مؤرشف',
            'outdated' => 'قديم',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على مستوى الصعوبة
     */
    public function getDifficultyLabelAttribute(): string
    {
        return match ($this->difficulty_level) {
            'beginner' => 'مبتدئ',
            'intermediate' => 'متوسط',
            'advanced' => 'متقدم',
            'expert' => 'خبير',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على الجمهور المستهدف
     */
    public function getTargetAudienceLabelAttribute(): string
    {
        return match ($this->target_audience) {
            'customers' => 'العملاء',
            'agents' => 'الوكلاء',
            'admins' => 'المديرين',
            'developers' => 'المطورين',
            'all' => 'الجميع',
            default => 'غير محدد',
        };
    }

    /**
     * تسجيل مشاهدة
     */
    public function recordView(int $userId = null, string $ipAddress = null): void
    {
        // تجنب تسجيل مشاهدات متكررة من نفس المستخدم في نفس اليوم
        $today = now()->startOfDay();
        
        $existingView = $this->views()
            ->where('user_id', $userId)
            ->where('created_at', '>=', $today)
            ->first();

        if (!$existingView) {
            $this->views()->create([
                'user_id' => $userId,
                'ip_address' => $ipAddress ?? request()->ip(),
                'user_agent' => request()->userAgent(),
                'viewed_at' => now(),
            ]);

            $this->increment('view_count');
        }
    }

    /**
     * تقييم المقال كمفيد
     */
    public function markAsHelpful(int $userId = null): bool
    {
        $rating = $this->ratings()->updateOrCreate(
            ['user_id' => $userId ?? auth()->id()],
            [
                'is_helpful' => true,
                'rated_at' => now(),
            ]
        );

        if ($rating->wasRecentlyCreated || $rating->wasChanged()) {
            $this->recalculateHelpfulCounts();
        }

        return true;
    }

    /**
     * تقييم المقال كغير مفيد
     */
    public function markAsNotHelpful(int $userId = null, string $feedback = null): bool
    {
        $rating = $this->ratings()->updateOrCreate(
            ['user_id' => $userId ?? auth()->id()],
            [
                'is_helpful' => false,
                'feedback' => $feedback,
                'rated_at' => now(),
            ]
        );

        if ($rating->wasRecentlyCreated || $rating->wasChanged()) {
            $this->recalculateHelpfulCounts();
        }

        return true;
    }

    /**
     * إعادة حساب عدادات المفيد
     */
    protected function recalculateHelpfulCounts(): void
    {
        $helpfulCount = $this->ratings()->where('is_helpful', true)->count();
        $notHelpfulCount = $this->ratings()->where('is_helpful', false)->count();

        $this->update([
            'helpful_count' => $helpfulCount,
            'not_helpful_count' => $notHelpfulCount,
        ]);
    }

    /**
     * تحديث تاريخ المراجعة
     */
    public function markAsReviewed(int $reviewerId, \Carbon\Carbon $nextReviewDate = null): bool
    {
        return $this->update([
            'last_reviewed_at' => now(),
            'last_reviewed_by' => $reviewerId,
            'next_review_date' => $nextReviewDate ?? now()->addMonths(6),
        ]);
    }

    /**
     * إنشاء إصدار جديد
     */
    public function createNewVersion(array $changes): bool
    {
        $currentVersion = $this->version ?? 1.0;
        $newVersion = $currentVersion + 0.1;

        $changelog = $this->changelog ?? [];
        $changelog[] = [
            'version' => $newVersion,
            'changes' => $changes,
            'updated_at' => now()->toISOString(),
            'updated_by' => auth()->user()->name ?? 'النظام',
        ];

        return $this->update([
            'version' => $newVersion,
            'changelog' => $changelog,
        ]);
    }

    /**
     * ربط مقالات ذات صلة
     */
    public function addRelatedArticle(int $relatedArticleId): void
    {
        $this->relatedArticles()->syncWithoutDetaching([$relatedArticleId]);
        
        // إضافة العلاقة العكسية
        self::find($relatedArticleId)?->relatedArticles()->syncWithoutDetaching([$this->id]);
    }

    /**
     * إزالة مقال ذي صلة
     */
    public function removeRelatedArticle(int $relatedArticleId): void
    {
        $this->relatedArticles()->detach($relatedArticleId);
        
        // إزالة العلاقة العكسية
        self::find($relatedArticleId)?->relatedArticles()->detach($this->id);
    }

    /**
     * توليد slug تلقائي
     */
    public function generateSlug(): string
    {
        $baseSlug = \Str::slug($this->title);
        $slug = $baseSlug;
        $counter = 1;

        while (self::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * فلترة المقالات المنشورة
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * فلترة المقالات المميزة
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeInCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * فلترة حسب الجمهور المستهدف
     */
    public function scopeForAudience($query, string $audience)
    {
        return $query->where('target_audience', $audience);
    }

    /**
     * فلترة حسب مستوى الصعوبة
     */
    public function scopeWithDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * فلترة المقالات التي تحتاج مراجعة
     */
    public function scopeNeedsReview($query)
    {
        return $query->where('next_review_date', '<=', now());
    }

    /**
     * ترتيب حسب الشعبية
     */
    public function scopePopular($query)
    {
        return $query->orderByRaw('(view_count + helpful_count * 5) DESC');
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * البحث في المقالات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('title_ar', 'LIKE', "%{$search}%")
              ->orWhere('title_fr', 'LIKE', "%{$search}%")
              ->orWhere('title_en', 'LIKE', "%{$search}%")
              ->orWhere('content', 'LIKE', "%{$search}%")
              ->orWhere('tags', 'LIKE', "%{$search}%");
        });
    }

    /**
     * إعدادات البحث للـ Scout
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'title_ar' => $this->title_ar,
            'title_fr' => $this->title_fr,
            'title_en' => $this->title_en,
            'content' => strip_tags($this->content),
            'excerpt' => $this->excerpt,
            'tags' => $this->tags,
            'category_name' => $this->category->name ?? '',
            'difficulty_level' => $this->difficulty_level,
            'target_audience' => $this->target_audience,
        ];
    }
}
