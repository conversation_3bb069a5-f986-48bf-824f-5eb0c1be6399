<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Models\Company;
use Carbon\Carbon;

/**
 * نموذج الإقرارات الضريبية المتقدم
 * يدير جميع أنواع الإقرارات الضريبية لجميع الدول
 */
class TaxReturn extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'return_id',
        'company_id',
        'user_id',
        'tax_configuration_id',
        'country_id',
        'return_type',
        'tax_period_type',
        'tax_year',
        'period_start',
        'period_end',
        'due_date',
        'filing_date',
        'extension_date',
        'status',
        'filing_method',
        'return_data',
        'calculations',
        'schedules',
        'attachments',
        'amendments',
        'total_income',
        'taxable_income',
        'exempt_income',
        'deductions',
        'tax_liability',
        'tax_paid',
        'tax_due',
        'refund_due',
        'penalties',
        'interest',
        'currency',
        'exchange_rates',
        'submission_reference',
        'government_reference',
        'acknowledgment_reference',
        'assessment_reference',
        'preparer_info',
        'reviewer_info',
        'approver_info',
        'digital_signature',
        'certification_details',
        'audit_trail',
        'compliance_notes',
        'risk_assessment',
        'supporting_documents',
        'government_response',
        'payment_instructions',
        'metadata',
        'submitted_at',
        'acknowledged_at',
        'processed_at',
        'assessed_at',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'due_date' => 'date',
        'filing_date' => 'date',
        'extension_date' => 'date',
        'return_data' => 'array',
        'calculations' => 'array',
        'schedules' => 'array',
        'attachments' => 'array',
        'amendments' => 'array',
        'total_income' => 'decimal:4',
        'taxable_income' => 'decimal:4',
        'exempt_income' => 'decimal:4',
        'deductions' => 'decimal:4',
        'tax_liability' => 'decimal:4',
        'tax_paid' => 'decimal:4',
        'tax_due' => 'decimal:4',
        'refund_due' => 'decimal:4',
        'penalties' => 'decimal:4',
        'interest' => 'decimal:4',
        'exchange_rates' => 'array',
        'preparer_info' => 'array',
        'reviewer_info' => 'array',
        'approver_info' => 'array',
        'digital_signature' => 'array',
        'certification_details' => 'array',
        'audit_trail' => 'array',
        'compliance_notes' => 'array',
        'risk_assessment' => 'array',
        'supporting_documents' => 'array',
        'government_response' => 'array',
        'payment_instructions' => 'array',
        'metadata' => 'array',
        'submitted_at' => 'datetime',
        'acknowledged_at' => 'datetime',
        'processed_at' => 'datetime',
        'assessed_at' => 'datetime',
    ];

    /**
     * أنواع الإقرارات الضريبية
     */
    const RETURN_TYPES = [
        // المغرب
        'ma_tva_monthly' => 'إقرار TVA الشهري - المغرب',
        'ma_is_annual' => 'إقرار IS السنوي - المغرب',
        'ma_ir_annual' => 'إقرار IR السنوي - المغرب',
        
        // السعودية
        'sa_vat_quarterly' => 'إقرار ضريبة القيمة المضافة الربع سنوي - السعودية',
        'sa_zakat_annual' => 'إقرار الزكاة السنوي - السعودية',
        'sa_withholding_monthly' => 'إقرار ضريبة الاستقطاع الشهري - السعودية',
        
        // الإمارات
        'ae_vat_quarterly' => 'إقرار ضريبة القيمة المضافة الربع سنوي - الإمارات',
        'ae_excise_monthly' => 'إقرار ضريبة السلع الانتقائية الشهري - الإمارات',
        'ae_corporate_annual' => 'إقرار ضريبة الشركات السنوي - الإمارات',
        
        // مصر
        'eg_vat_monthly' => 'إقرار ضريبة القيمة المضافة الشهري - مصر',
        'eg_corporate_annual' => 'إقرار ضريبة الشركات السنوي - مصر',
        'eg_salary_monthly' => 'إقرار ضريبة الراتب الشهري - مصر',
        
        // الكويت
        'kw_vat_quarterly' => 'إقرار ضريبة القيمة المضافة الربع سنوي - الكويت',
        'kw_corporate_annual' => 'إقرار ضريبة الشركات السنوي - الكويت',
        
        // قطر
        'qa_vat_quarterly' => 'إقرار ضريبة القيمة المضافة الربع سنوي - قطر',
        'qa_corporate_annual' => 'إقرار ضريبة الشركات السنوي - قطر',
        
        // الأردن
        'jo_gst_monthly' => 'إقرار ضريبة المبيعات الشهري - الأردن',
        'jo_income_annual' => 'إقرار ضريبة الدخل السنوي - الأردن',
        
        // تونس
        'tn_tva_monthly' => 'إقرار TVA الشهري - تونس',
        'tn_is_annual' => 'إقرار IS السنوي - تونس',
        
        // الجزائر
        'dz_tva_monthly' => 'إقرار TVA الشهري - الجزائر',
        'dz_is_annual' => 'إقرار IS السنوي - الجزائر',
    ];

    /**
     * أنواع الفترات الضريبية
     */
    const PERIOD_TYPES = [
        'monthly' => 'شهري',
        'quarterly' => 'ربع سنوي',
        'semi_annual' => 'نصف سنوي',
        'annual' => 'سنوي',
        'biennial' => 'كل سنتين',
    ];

    /**
     * حالات الإقرار
     */
    const STATUSES = [
        'draft' => 'مسودة',
        'in_preparation' => 'قيد الإعداد',
        'under_review' => 'قيد المراجعة',
        'approved' => 'معتمد',
        'submitted' => 'مقدم',
        'acknowledged' => 'مستلم',
        'under_processing' => 'قيد المعالجة',
        'assessed' => 'مقيم',
        'accepted' => 'مقبول',
        'rejected' => 'مرفوض',
        'amended' => 'معدل',
        'under_audit' => 'تحت المراجعة',
        'finalized' => 'نهائي',
        'archived' => 'مؤرشف',
    ];

    /**
     * طرق التقديم
     */
    const FILING_METHODS = [
        'electronic' => 'إلكتروني',
        'paper' => 'ورقي',
        'hybrid' => 'مختلط',
        'agent_submission' => 'عبر وكيل',
        'third_party' => 'طرف ثالث',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع تكوين الضريبة
     */
    public function taxConfiguration(): BelongsTo
    {
        return $this->belongsTo(TaxConfiguration::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع المعاملات الضريبية
     */
    public function taxTransactions(): HasMany
    {
        return $this->hasMany(TaxTransaction::class, 'tax_return_id');
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(TaxPayment::class, 'tax_return_id');
    }

    /**
     * العلاقة مع التعديلات
     */
    public function returnAmendments(): HasMany
    {
        return $this->hasMany(TaxReturnAmendment::class);
    }

    /**
     * إنشاء إقرار ضريبي جديد
     */
    public static function createForPeriod(
        Company $company,
        TaxConfiguration $taxConfig,
        Carbon $periodStart,
        Carbon $periodEnd
    ): self {
        $returnType = self::determineReturnType($taxConfig, $periodStart, $periodEnd);
        $dueDate = self::calculateDueDate($taxConfig, $periodEnd);

        return self::create([
            'return_id' => self::generateReturnId($company, $taxConfig, $periodStart),
            'company_id' => $company->id,
            'tax_configuration_id' => $taxConfig->id,
            'country_id' => $taxConfig->country_id,
            'return_type' => $returnType,
            'tax_period_type' => $taxConfig->filing_frequency,
            'tax_year' => $periodEnd->year,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'due_date' => $dueDate,
            'status' => 'draft',
            'filing_method' => 'electronic',
            'currency' => $taxConfig->country->currency,
        ]);
    }

    /**
     * حساب الإقرار الضريبي
     */
    public function calculateReturn(): array
    {
        // جمع المعاملات للفترة
        $transactions = $this->gatherTransactionsForPeriod();
        
        // حساب الإجماليات
        $calculations = $this->performCalculations($transactions);
        
        // إعداد الجداول المطلوبة
        $schedules = $this->prepareSchedules($transactions, $calculations);
        
        // تحديث الإقرار
        $this->update([
            'return_data' => $this->prepareReturnData($transactions),
            'calculations' => $calculations,
            'schedules' => $schedules,
            'total_income' => $calculations['total_income'],
            'taxable_income' => $calculations['taxable_income'],
            'exempt_income' => $calculations['exempt_income'],
            'deductions' => $calculations['total_deductions'],
            'tax_liability' => $calculations['tax_liability'],
            'tax_paid' => $calculations['tax_paid'],
            'tax_due' => max(0, $calculations['tax_liability'] - $calculations['tax_paid']),
            'refund_due' => max(0, $calculations['tax_paid'] - $calculations['tax_liability']),
            'status' => 'in_preparation',
        ]);

        return $calculations;
    }

    /**
     * جمع المعاملات للفترة
     */
    protected function gatherTransactionsForPeriod(): \Illuminate\Support\Collection
    {
        return TaxTransaction::where('company_id', $this->company_id)
            ->where('tax_configuration_id', $this->tax_configuration_id)
            ->whereBetween('transaction_date', [$this->period_start, $this->period_end])
            ->where('status', '!=', 'cancelled')
            ->get();
    }

    /**
     * تنفيذ الحسابات
     */
    protected function performCalculations($transactions): array
    {
        $countryCode = $this->country->code;
        
        return match ($countryCode) {
            'MA' => $this->calculateMoroccanReturn($transactions),
            'SA' => $this->calculateSaudiReturn($transactions),
            'AE' => $this->calculateUAEReturn($transactions),
            'EG' => $this->calculateEgyptianReturn($transactions),
            'KW' => $this->calculateKuwaitiReturn($transactions),
            'QA' => $this->calculateQatariReturn($transactions),
            'JO' => $this->calculateJordanianReturn($transactions),
            'TN' => $this->calculateTunisianReturn($transactions),
            'DZ' => $this->calculateAlgerianReturn($transactions),
            default => $this->calculateGenericReturn($transactions),
        };
    }

    /**
     * حساب الإقرار المغربي
     */
    protected function calculateMoroccanReturn($transactions): array
    {
        $taxType = $this->taxConfiguration->tax_type;
        
        return match ($taxType) {
            'vat' => $this->calculateMoroccanTVA($transactions),
            'corporate_tax' => $this->calculateMoroccanIS($transactions),
            'income_tax' => $this->calculateMoroccanIR($transactions),
            default => $this->calculateGenericReturn($transactions),
        };
    }

    /**
     * حساب TVA المغربي
     */
    protected function calculateMoroccanTVA($transactions): array
    {
        $outputTax = $transactions->where('transaction_type', 'vat_output')->sum('tax_amount');
        $inputTax = $transactions->where('transaction_type', 'vat_input')->sum('tax_amount');
        $netTax = $outputTax - $inputTax;

        return [
            'total_income' => $transactions->sum('base_amount'),
            'taxable_income' => $transactions->sum('taxable_amount'),
            'exempt_income' => $transactions->sum('exempt_amount'),
            'total_deductions' => $inputTax,
            'output_tax' => $outputTax,
            'input_tax' => $inputTax,
            'net_tax' => $netTax,
            'tax_liability' => max(0, $netTax),
            'tax_paid' => $transactions->sum('paid_amount'),
            'breakdown_by_rate' => $this->calculateTVABreakdownByRate($transactions),
        ];
    }

    /**
     * حساب الإقرار السعودي
     */
    protected function calculateSaudiReturn($transactions): array
    {
        $taxType = $this->taxConfiguration->tax_type;
        
        return match ($taxType) {
            'vat' => $this->calculateSaudiVAT($transactions),
            'zakat' => $this->calculateSaudiZakat($transactions),
            'withholding_tax' => $this->calculateSaudiWithholding($transactions),
            default => $this->calculateGenericReturn($transactions),
        };
    }

    /**
     * حساب VAT السعودي
     */
    protected function calculateSaudiVAT($transactions): array
    {
        $outputTax = $transactions->where('transaction_type', 'vat_output')->sum('tax_amount');
        $inputTax = $transactions->where('transaction_type', 'vat_input')->sum('tax_amount');
        $netTax = $outputTax - $inputTax;

        return [
            'total_income' => $transactions->sum('base_amount'),
            'taxable_income' => $transactions->sum('taxable_amount'),
            'exempt_income' => $transactions->sum('exempt_amount'),
            'zero_rated_income' => $transactions->where('tax_rate', 0)->sum('base_amount'),
            'total_deductions' => $inputTax,
            'output_tax' => $outputTax,
            'input_tax' => $inputTax,
            'net_tax' => $netTax,
            'tax_liability' => max(0, $netTax),
            'tax_paid' => $transactions->sum('paid_amount'),
            'adjustments' => $this->calculateSaudiVATAdjustments($transactions),
        ];
    }

    /**
     * تحضير الجداول المطلوبة
     */
    protected function prepareSchedules($transactions, $calculations): array
    {
        $countryCode = $this->country->code;
        
        return match ($countryCode) {
            'MA' => $this->prepareMoroccanSchedules($transactions, $calculations),
            'SA' => $this->prepareSaudiSchedules($transactions, $calculations),
            'AE' => $this->prepareUAESchedules($transactions, $calculations),
            'EG' => $this->prepareEgyptianSchedules($transactions, $calculations),
            default => $this->prepareGenericSchedules($transactions, $calculations),
        };
    }

    /**
     * تحضير الجداول المغربية
     */
    protected function prepareMoroccanSchedules($transactions, $calculations): array
    {
        return [
            'schedule_a' => $this->prepareMoroccanScheduleA($transactions), // العمليات الخاضعة للضريبة
            'schedule_b' => $this->prepareMoroccanScheduleB($transactions), // العمليات المعفاة
            'schedule_c' => $this->prepareMoroccanScheduleC($transactions), // المشتريات والخدمات
            'schedule_d' => $this->prepareMoroccanScheduleD($calculations), // حساب الضريبة المستحقة
        ];
    }

    /**
     * تقديم الإقرار
     */
    public function submitReturn(): array
    {
        if ($this->status !== 'approved') {
            throw new \Exception('يجب اعتماد الإقرار قبل التقديم');
        }

        // تحضير بيانات التقديم
        $submissionData = $this->prepareSubmissionData();
        
        // إضافة التوقيع الرقمي
        $submissionData = $this->addDigitalSignature($submissionData);
        
        // إرسال للسلطة المختصة
        $response = $this->sendToTaxAuthority($submissionData);

        if ($response['success']) {
            $this->update([
                'status' => 'submitted',
                'submission_reference' => $response['reference'],
                'government_reference' => $response['government_reference'] ?? null,
                'submitted_at' => now(),
            ]);

            $this->logAuditTrail('submitted', 'تم تقديم الإقرار للسلطة الضريبية', $response);
        }

        return $response;
    }

    /**
     * تحضير بيانات التقديم
     */
    protected function prepareSubmissionData(): array
    {
        return [
            'return_id' => $this->return_id,
            'company_tax_number' => $this->company->tax_number,
            'return_type' => $this->return_type,
            'tax_year' => $this->tax_year,
            'period_start' => $this->period_start->format('Y-m-d'),
            'period_end' => $this->period_end->format('Y-m-d'),
            'filing_date' => now()->format('Y-m-d'),
            'return_data' => $this->return_data,
            'calculations' => $this->calculations,
            'schedules' => $this->schedules,
            'total_income' => $this->total_income,
            'taxable_income' => $this->taxable_income,
            'tax_liability' => $this->tax_liability,
            'tax_due' => $this->tax_due,
            'refund_due' => $this->refund_due,
            'currency' => $this->currency,
            'supporting_documents' => $this->supporting_documents,
            'preparer_info' => $this->preparer_info,
        ];
    }

    /**
     * إضافة التوقيع الرقمي
     */
    protected function addDigitalSignature(array $data): array
    {
        $country = $this->country;
        
        if ($country->isDigitalSignatureRequired()) {
            $signature = $this->generateDigitalSignature($data);
            $data['digital_signature'] = $signature;
            
            $this->update(['digital_signature' => $signature]);
        }

        return $data;
    }

    /**
     * توليد التوقيع الرقمي
     */
    protected function generateDigitalSignature(array $data): array
    {
        // منطق توليد التوقيع الرقمي
        return [
            'algorithm' => 'RSA-SHA256',
            'signature' => base64_encode(hash('sha256', json_encode($data))),
            'certificate_serial' => $this->company->digital_certificate_serial ?? 'CERT_' . uniqid(),
            'timestamp' => now()->toISOString(),
            'signer' => auth()->user()?->name ?? 'System',
        ];
    }

    /**
     * إرسال للسلطة الضريبية
     */
    protected function sendToTaxAuthority(array $data): array
    {
        $country = $this->country;
        $taxAuthority = $country->getTaxAuthority();
        
        // محاكاة إرسال API
        return [
            'success' => true,
            'reference' => 'RET_' . strtoupper(uniqid()),
            'government_reference' => $taxAuthority . '_' . strtoupper(uniqid()),
            'status' => 'received',
            'message' => 'تم استلام الإقرار بنجاح',
            'processing_time' => '2-5 أيام عمل',
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * تسجيل سجل المراجعة
     */
    public function logAuditTrail(string $action, string $description, array $data = []): void
    {
        $auditTrail = $this->audit_trail ?? [];
        
        $auditTrail[] = [
            'action' => $action,
            'description' => $description,
            'user_id' => auth()->id(),
            'user_name' => auth()->user()?->name,
            'timestamp' => now()->toISOString(),
            'ip_address' => request()->ip(),
            'data' => $data,
        ];

        $this->update(['audit_trail' => $auditTrail]);
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeByCountry($query, string $countryCode)
    {
        return $query->whereHas('country', fn($q) => $q->where('code', $countryCode));
    }

    public function scopeByTaxYear($query, int $year)
    {
        return $query->where('tax_year', $year);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['submitted', 'acknowledged', 'finalized']);
    }

    public function scopePendingSubmission($query)
    {
        return $query->where('status', 'approved')
                    ->whereNull('submitted_at');
    }

    // طرق مساعدة
    protected static function determineReturnType(TaxConfiguration $taxConfig, Carbon $periodStart, Carbon $periodEnd): string
    {
        $countryCode = $taxConfig->country->code;
        $taxType = $taxConfig->tax_type;
        $frequency = $taxConfig->filing_frequency;
        
        return "{$countryCode}_{$taxType}_{$frequency}";
    }

    protected static function calculateDueDate(TaxConfiguration $taxConfig, Carbon $periodEnd): Carbon
    {
        $deadlines = $taxConfig->filing_deadlines ?? [];
        $daysAfterPeriod = $deadlines['days_after_period'] ?? 20;
        
        return $periodEnd->copy()->addDays($daysAfterPeriod);
    }

    protected static function generateReturnId(Company $company, TaxConfiguration $taxConfig, Carbon $periodStart): string
    {
        $countryCode = $taxConfig->country->code;
        $taxType = strtoupper(substr($taxConfig->tax_type, 0, 3));
        $period = $periodStart->format('Ym');
        $companyCode = substr($company->tax_number ?? $company->id, -4);
        
        return "{$countryCode}_{$taxType}_{$period}_{$companyCode}";
    }

    // طرق حساب إضافية لكل دولة
    protected function calculateTVABreakdownByRate($transactions): array
    {
        return $transactions->groupBy('tax_rate')->map(function ($group, $rate) {
            return [
                'rate' => $rate,
                'taxable_amount' => $group->sum('taxable_amount'),
                'tax_amount' => $group->sum('tax_amount'),
                'transaction_count' => $group->count(),
            ];
        })->values()->toArray();
    }

    protected function calculateSaudiVATAdjustments($transactions): array
    {
        // حسابات التعديلات الخاصة بالسعودية
        return [
            'bad_debt_relief' => 0,
            'capital_goods_adjustment' => 0,
            'error_corrections' => 0,
        ];
    }

    protected function calculateGenericReturn($transactions): array
    {
        return [
            'total_income' => $transactions->sum('base_amount'),
            'taxable_income' => $transactions->sum('taxable_amount'),
            'exempt_income' => $transactions->sum('exempt_amount'),
            'total_deductions' => 0,
            'tax_liability' => $transactions->sum('tax_amount'),
            'tax_paid' => $transactions->sum('paid_amount'),
        ];
    }

    protected function prepareReturnData($transactions): array
    {
        return [
            'transaction_count' => $transactions->count(),
            'transaction_summary' => $transactions->groupBy('transaction_type')->map->count(),
            'period_summary' => [
                'start_date' => $this->period_start->format('Y-m-d'),
                'end_date' => $this->period_end->format('Y-m-d'),
                'days_in_period' => $this->period_start->diffInDays($this->period_end) + 1,
            ],
        ];
    }

    protected function prepareGenericSchedules($transactions, $calculations): array
    {
        return [
            'transaction_summary' => $transactions->groupBy('transaction_type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('base_amount'),
                    'total_tax' => $group->sum('tax_amount'),
                ];
            }),
        ];
    }

    // طرق إضافية لحسابات الدول الأخرى
    protected function calculateUAEReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateEgyptianReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateKuwaitiReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateQatariReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateJordanianReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateTunisianReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateAlgerianReturn($transactions): array { return $this->calculateGenericReturn($transactions); }
    
    protected function calculateMoroccanIS($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateMoroccanIR($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateSaudiZakat($transactions): array { return $this->calculateGenericReturn($transactions); }
    protected function calculateSaudiWithholding($transactions): array { return $this->calculateGenericReturn($transactions); }
    
    protected function prepareSaudiSchedules($transactions, $calculations): array { return $this->prepareGenericSchedules($transactions, $calculations); }
    protected function prepareUAESchedules($transactions, $calculations): array { return $this->prepareGenericSchedules($transactions, $calculations); }
    protected function prepareEgyptianSchedules($transactions, $calculations): array { return $this->prepareGenericSchedules($transactions, $calculations); }
    
    protected function prepareMoroccanScheduleA($transactions): array { return ['transactions' => $transactions->toArray()]; }
    protected function prepareMoroccanScheduleB($transactions): array { return ['exempt_transactions' => []]; }
    protected function prepareMoroccanScheduleC($transactions): array { return ['purchases' => []]; }
    protected function prepareMoroccanScheduleD($calculations): array { return $calculations; }
}
