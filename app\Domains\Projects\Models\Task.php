<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use App\Domains\Shared\Traits\HasComments;
use App\Domains\Shared\Traits\HasFiles;
use App\Domains\Shared\Traits\HasActivities;

/**
 * نموذج المهمة - Enterprise Grade
 * يدعم إدارة المهام المعقدة مع جميع الميزات المتقدمة
 */
class Task extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, HasComments, HasFiles, HasActivities;

    protected $fillable = [
        'project_id',
        'parent_id',
        'milestone_id',
        'title',
        'description',
        'task_number',
        'status',
        'priority',
        'type',
        'category',
        'start_date',
        'due_date',
        'completed_at',
        'estimated_hours',
        'actual_hours',
        'story_points',
        'progress_percentage',
        'quality_score',
        'complexity_level',
        'risk_level',
        'assignee_id',
        'reporter_id',
        'reviewer_id',
        'sprint_id',
        'epic_id',
        'labels',
        'custom_fields',
        'is_billable',
        'hourly_rate',
        'is_recurring',
        'recurrence_pattern',
        'position',
        'kanban_column',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'estimated_hours' => 'decimal:2',
        'actual_hours' => 'decimal:2',
        'story_points' => 'integer',
        'progress_percentage' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'is_billable' => 'boolean',
        'is_recurring' => 'boolean',
        'labels' => 'array',
        'custom_fields' => 'array',
        'recurrence_pattern' => 'array',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع المشروع
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المهمة الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * العلاقة مع المهام الفرعية
     */
    public function subtasks(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * العلاقة مع المعلم
     */
    public function milestone(): BelongsTo
    {
        return $this->belongsTo(Milestone::class);
    }

    /**
     * العلاقة مع المكلف بالمهمة
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assignee_id');
    }

    /**
     * العلاقة مع مُبلغ المهمة
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'reporter_id');
    }

    /**
     * العلاقة مع مراجع المهمة
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'reviewer_id');
    }

    /**
     * العلاقة مع المكلفين الإضافيين
     */
    public function assignees(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Domains\HR\Models\Employee::class,
            'task_assignees',
            'task_id',
            'employee_id'
        )->withPivot(['role', 'assigned_at', 'is_primary'])->withTimestamps();
    }

    /**
     * العلاقة مع التبعيات
     */
    public function dependencies(): BelongsToMany
    {
        return $this->belongsToMany(
            self::class,
            'task_dependencies',
            'task_id',
            'depends_on_task_id'
        )->withPivot(['dependency_type', 'lag_time'])->withTimestamps();
    }

    /**
     * العلاقة مع المهام التابعة
     */
    public function dependents(): BelongsToMany
    {
        return $this->belongsToMany(
            self::class,
            'task_dependencies',
            'depends_on_task_id',
            'task_id'
        )->withPivot(['dependency_type', 'lag_time'])->withTimestamps();
    }

    /**
     * العلاقة مع تسجيلات الوقت
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * العلاقة مع قائمة المراجعة
     */
    public function checklistItems(): HasMany
    {
        return $this->hasMany(TaskChecklistItem::class);
    }

    /**
     * العلاقة مع التقييمات
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(TaskReview::class);
    }

    /**
     * العلاقة مع السبرنت
     */
    public function sprint(): BelongsTo
    {
        return $this->belongsTo(Sprint::class);
    }

    /**
     * العلاقة مع الملحمة
     */
    public function epic(): BelongsTo
    {
        return $this->belongsTo(Epic::class);
    }

    /**
     * الحصول على إجمالي الساعات المسجلة
     */
    public function getTotalLoggedHoursAttribute(): float
    {
        return $this->timeEntries()->sum('hours');
    }

    /**
     * الحصول على التكلفة المحسوبة
     */
    public function getCalculatedCostAttribute(): float
    {
        return $this->timeEntries()
                    ->selectRaw('SUM(hours * COALESCE(hourly_rate, 0)) as total_cost')
                    ->value('total_cost') ?? 0;
    }

    /**
     * الحصول على حالة التأخير
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['COMPLETED', 'CANCELLED']);
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        return $this->due_date ? now()->diffInDays($this->due_date, false) : 0;
    }

    /**
     * الحصول على نسبة الإنجاز المحسوبة من المهام الفرعية
     */
    public function getCalculatedProgressAttribute(): float
    {
        $subtasksCount = $this->subtasks()->count();
        
        if ($subtasksCount === 0) {
            return $this->progress_percentage;
        }

        $completedSubtasks = $this->subtasks()->where('status', 'COMPLETED')->count();
        return ($completedSubtasks / $subtasksCount) * 100;
    }

    /**
     * الحصول على نسبة إكمال قائمة المراجعة
     */
    public function getChecklistProgressAttribute(): float
    {
        $totalItems = $this->checklistItems()->count();
        
        if ($totalItems === 0) {
            return 100;
        }

        $completedItems = $this->checklistItems()->where('is_completed', true)->count();
        return ($completedItems / $totalItems) * 100;
    }

    /**
     * التحقق من إمكانية البدء في المهمة
     */
    public function canStart(): bool
    {
        // التحقق من اكتمال التبعيات
        foreach ($this->dependencies as $dependency) {
            if (!in_array($dependency->status, ['COMPLETED', 'CANCELLED'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * بدء المهمة
     */
    public function start(): bool
    {
        if (!$this->canStart()) {
            return false;
        }

        $this->update([
            'status' => 'IN_PROGRESS',
            'start_date' => now(),
        ]);

        $this->logActivity('task_started');
        
        // إشعار المكلفين
        $this->notifyAssignees('task_started');

        return true;
    }

    /**
     * إكمال المهمة
     */
    public function complete(): bool
    {
        // التحقق من اكتمال قائمة المراجعة
        if ($this->checklistItems()->where('is_completed', false)->exists()) {
            return false;
        }

        $this->update([
            'status' => 'COMPLETED',
            'completed_at' => now(),
            'progress_percentage' => 100,
        ]);

        $this->logActivity('task_completed');
        
        // تحديث تقدم المشروع
        $this->project->updateProgress();
        
        // إشعار الفريق
        $this->notifyAssignees('task_completed');

        // التحقق من إمكانية بدء المهام التابعة
        $this->checkDependentTasks();

        return true;
    }

    /**
     * التحقق من المهام التابعة وإشعارها
     */
    protected function checkDependentTasks(): void
    {
        foreach ($this->dependents as $dependentTask) {
            if ($dependentTask->canStart() && $dependentTask->status === 'TODO') {
                $dependentTask->assignee?->notify(
                    new \App\Notifications\TaskReadyToStartNotification($dependentTask)
                );
            }
        }
    }

    /**
     * إضافة تبعية
     */
    public function addDependency(int $dependsOnTaskId, string $type = 'FINISH_TO_START', int $lagTime = 0): void
    {
        $this->dependencies()->attach($dependsOnTaskId, [
            'dependency_type' => $type,
            'lag_time' => $lagTime,
        ]);

        $this->logActivity('dependency_added', [
            'depends_on_task_id' => $dependsOnTaskId,
            'dependency_type' => $type,
        ]);
    }

    /**
     * إزالة تبعية
     */
    public function removeDependency(int $dependsOnTaskId): void
    {
        $this->dependencies()->detach($dependsOnTaskId);
        
        $this->logActivity('dependency_removed', [
            'depends_on_task_id' => $dependsOnTaskId,
        ]);
    }

    /**
     * تعيين المهمة لموظف
     */
    public function assignTo(int $employeeId, bool $isPrimary = false): void
    {
        if ($isPrimary) {
            $this->update(['assignee_id' => $employeeId]);
        }

        $this->assignees()->syncWithoutDetaching([
            $employeeId => [
                'role' => $isPrimary ? 'PRIMARY' : 'SECONDARY',
                'assigned_at' => now(),
                'is_primary' => $isPrimary,
            ]
        ]);

        $this->logActivity('task_assigned', ['employee_id' => $employeeId]);
        
        // إشعار الموظف المكلف
        $employee = \App\Domains\HR\Models\Employee::find($employeeId);
        $employee?->notify(new \App\Notifications\TaskAssignedNotification($this));
    }

    /**
     * إلغاء تعيين المهمة
     */
    public function unassignFrom(int $employeeId): void
    {
        $this->assignees()->detach($employeeId);
        
        if ($this->assignee_id === $employeeId) {
            $this->update(['assignee_id' => null]);
        }

        $this->logActivity('task_unassigned', ['employee_id' => $employeeId]);
    }

    /**
     * تحديث التقدم
     */
    public function updateProgress(float $percentage): void
    {
        $oldProgress = $this->progress_percentage;
        
        $this->update(['progress_percentage' => $percentage]);

        // تحديث الحالة تلقائياً
        if ($percentage === 100 && $this->status !== 'COMPLETED') {
            $this->complete();
        } elseif ($percentage > 0 && $this->status === 'TODO') {
            $this->update(['status' => 'IN_PROGRESS']);
        }

        $this->logActivity('progress_updated', [
            'old_progress' => $oldProgress,
            'new_progress' => $percentage,
        ]);
    }

    /**
     * إضافة عنصر لقائمة المراجعة
     */
    public function addChecklistItem(string $title, string $description = null): TaskChecklistItem
    {
        return $this->checklistItems()->create([
            'title' => $title,
            'description' => $description,
            'is_completed' => false,
            'position' => $this->checklistItems()->count() + 1,
        ]);
    }

    /**
     * نسخ المهمة
     */
    public function duplicate(int $newProjectId = null): self
    {
        $newTask = self::create([
            'project_id' => $newProjectId ?? $this->project_id,
            'title' => $this->title . ' (نسخة)',
            'description' => $this->description,
            'type' => $this->type,
            'category' => $this->category,
            'priority' => $this->priority,
            'estimated_hours' => $this->estimated_hours,
            'story_points' => $this->story_points,
            'complexity_level' => $this->complexity_level,
            'is_billable' => $this->is_billable,
            'hourly_rate' => $this->hourly_rate,
            'labels' => $this->labels,
            'custom_fields' => $this->custom_fields,
        ]);

        // نسخ قائمة المراجعة
        foreach ($this->checklistItems as $item) {
            $newTask->addChecklistItem($item->title, $item->description);
        }

        return $newTask;
    }

    /**
     * نسخ المهمة لمشروع آخر
     */
    public function duplicateToProject(int $projectId): self
    {
        return $this->duplicate($projectId);
    }

    /**
     * إشعار المكلفين
     */
    protected function notifyAssignees(string $event): void
    {
        $notification = match ($event) {
            'task_started' => new \App\Notifications\TaskStartedNotification($this),
            'task_completed' => new \App\Notifications\TaskCompletedNotification($this),
            'task_overdue' => new \App\Notifications\TaskOverdueNotification($this),
            default => null,
        };

        if ($notification) {
            foreach ($this->assignees as $assignee) {
                $assignee->notify($notification);
            }
        }
    }

    /**
     * حساب نقاط السرعة المكتملة
     */
    public function getCompletedStoryPointsAttribute(): int
    {
        return $this->status === 'COMPLETED' ? $this->story_points : 0;
    }

    /**
     * الحصول على مستوى الأولوية الرقمي
     */
    public function getPriorityLevelAttribute(): int
    {
        return match ($this->priority) {
            'CRITICAL' => 4,
            'HIGH' => 3,
            'MEDIUM' => 2,
            'LOW' => 1,
            default => 0,
        };
    }

    /**
     * البحث في المهام
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('task_number', 'LIKE', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب المكلف
     */
    public function scopeAssignedTo($query, int $employeeId)
    {
        return $query->where('assignee_id', $employeeId)
                    ->orWhereHas('assignees', function ($q) use ($employeeId) {
                        $q->where('employee_id', $employeeId);
                    });
    }

    /**
     * فلترة المهام المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة المهام الجاهزة للبدء
     */
    public function scopeReadyToStart($query)
    {
        return $query->where('status', 'TODO')
                    ->whereDoesntHave('dependencies', function ($q) {
                        $q->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
                    });
    }

    /**
     * ترتيب حسب الأولوية
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW')");
    }

    /**
     * ترتيب حسب تاريخ الاستحقاق
     */
    public function scopeOrderByDueDate($query)
    {
        return $query->orderBy('due_date');
    }
}
