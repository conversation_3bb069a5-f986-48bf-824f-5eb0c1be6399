<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج الحملة التسويقية - Marketing Campaign
 * إدارة الحملات التسويقية متعددة القنوات
 */
class MarketingCampaign extends Model
{
    use HasFactory, SoftDeletes, HasUuid, HasFiles;

    protected $fillable = [
        'name',
        'description',
        'type',
        'channel',
        'status',
        'objective',
        'target_audience',
        'budget',
        'currency',
        'start_date',
        'end_date',
        'created_by',
        'assigned_to',
        'template_id',
        'subject',
        'content',
        'sender_name',
        'sender_email',
        'reply_to_email',
        'tracking_enabled',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'send_time',
        'timezone',
        'frequency',
        'auto_follow_up',
        'follow_up_delay_days',
        'segmentation_rules',
        'personalization_fields',
        'a_b_test_enabled',
        'a_b_test_percentage',
        'a_b_variant_subject',
        'a_b_variant_content',
        'tags',
        'custom_fields',
        'settings',
        'metrics',
        'ai_insights',
        'metadata',
    ];

    protected $casts = [
        'budget' => 'decimal:2',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'tracking_enabled' => 'boolean',
        'send_time' => 'datetime',
        'auto_follow_up' => 'boolean',
        'follow_up_delay_days' => 'integer',
        'segmentation_rules' => 'array',
        'personalization_fields' => 'array',
        'a_b_test_enabled' => 'boolean',
        'a_b_test_percentage' => 'integer',
        'tags' => 'array',
        'custom_fields' => 'array',
        'settings' => 'array',
        'metrics' => 'array',
        'ai_insights' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع الحملات
     */
    const TYPES = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسائل نصية',
        'whatsapp' => 'واتساب',
        'social_media' => 'وسائل التواصل',
        'webinar' => 'ندوة إلكترونية',
        'event' => 'فعالية',
        'content' => 'محتوى',
        'advertisement' => 'إعلان',
        'direct_mail' => 'بريد مباشر',
        'telemarketing' => 'تسويق هاتفي',
        'mixed' => 'مختلط',
    ];

    /**
     * قنوات الحملات
     */
    const CHANNELS = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسائل نصية',
        'whatsapp' => 'واتساب',
        'facebook' => 'فيسبوك',
        'instagram' => 'إنستجرام',
        'linkedin' => 'لينكد إن',
        'twitter' => 'تويتر',
        'google_ads' => 'إعلانات جوجل',
        'youtube' => 'يوتيوب',
        'website' => 'موقع إلكتروني',
        'blog' => 'مدونة',
        'print' => 'مطبوعات',
        'radio' => 'راديو',
        'tv' => 'تلفزيون',
        'outdoor' => 'إعلانات خارجية',
    ];

    /**
     * حالات الحملة
     */
    const STATUSES = [
        'draft' => 'مسودة',
        'scheduled' => 'مجدولة',
        'running' => 'قيد التشغيل',
        'paused' => 'متوقفة مؤقتاً',
        'completed' => 'مكتملة',
        'cancelled' => 'ملغية',
        'failed' => 'فاشلة',
    ];

    /**
     * أهداف الحملة
     */
    const OBJECTIVES = [
        'lead_generation' => 'توليد عملاء محتملين',
        'brand_awareness' => 'زيادة الوعي بالعلامة التجارية',
        'customer_retention' => 'الاحتفاظ بالعملاء',
        'sales_conversion' => 'تحويل المبيعات',
        'product_launch' => 'إطلاق منتج',
        'event_promotion' => 'ترويج فعالية',
        'customer_feedback' => 'جمع آراء العملاء',
        're_engagement' => 'إعادة التفاعل',
        'upsell_cross_sell' => 'بيع إضافي ومتقاطع',
        'referral' => 'إحالات',
    ];

    /**
     * تكرار الإرسال
     */
    const FREQUENCIES = [
        'once' => 'مرة واحدة',
        'daily' => 'يومياً',
        'weekly' => 'أسبوعياً',
        'monthly' => 'شهرياً',
        'quarterly' => 'ربع سنوي',
        'custom' => 'مخصص',
    ];

    /**
     * العلاقة مع منشئ الحملة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع المسؤول عن الحملة
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'assigned_to');
    }

    /**
     * العلاقة مع قالب الحملة
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(CampaignTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع المستلمين
     */
    public function recipients(): BelongsToMany
    {
        return $this->belongsToMany(Customer::class, 'campaign_recipients')
                    ->withPivot([
                        'sent_at',
                        'delivered_at',
                        'opened_at',
                        'clicked_at',
                        'bounced_at',
                        'unsubscribed_at',
                        'status',
                        'variant',
                        'personalized_content',
                        'tracking_data',
                    ])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع الشرائح المستهدفة
     */
    public function targetSegments(): BelongsToMany
    {
        return $this->belongsToMany(CustomerSegment::class, 'campaign_segments');
    }

    /**
     * العلاقة مع الأنشطة
     */
    public function activities(): HasMany
    {
        return $this->hasMany(CampaignActivity::class);
    }

    /**
     * العلاقة مع الفرص المولدة
     */
    public function generatedOpportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class, 'source_campaign_id');
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية القناة
     */
    public function getChannelLabelAttribute(): string
    {
        return self::CHANNELS[$this->channel] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الهدف
     */
    public function getObjectiveLabelAttribute(): string
    {
        return self::OBJECTIVES[$this->objective] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية التكرار
     */
    public function getFrequencyLabelAttribute(): string
    {
        return self::FREQUENCIES[$this->frequency] ?? 'غير محدد';
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'draft' => '#6c757d',
            'scheduled' => '#17a2b8',
            'running' => '#28a745',
            'paused' => '#ffc107',
            'completed' => '#007bff',
            'cancelled' => '#dc3545',
            'failed' => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * التحقق من كون الحملة نشطة
     */
    public function getIsActiveAttribute(): bool
    {
        return in_array($this->status, ['scheduled', 'running']);
    }

    /**
     * التحقق من كون الحملة مكتملة
     */
    public function getIsCompletedAttribute(): bool
    {
        return in_array($this->status, ['completed', 'cancelled', 'failed']);
    }

    /**
     * التحقق من إمكانية التشغيل
     */
    public function getCanRunAttribute(): bool
    {
        return in_array($this->status, ['draft', 'scheduled', 'paused']);
    }

    /**
     * التحقق من إمكانية الإيقاف
     */
    public function getCanPauseAttribute(): bool
    {
        return $this->status === 'running';
    }

    /**
     * الحصول على عدد المستلمين
     */
    public function getRecipientsCountAttribute(): int
    {
        return $this->recipients()->count();
    }

    /**
     * الحصول على عدد الرسائل المرسلة
     */
    public function getSentCountAttribute(): int
    {
        return $this->recipients()->whereNotNull('campaign_recipients.sent_at')->count();
    }

    /**
     * الحصول على عدد الرسائل المفتوحة
     */
    public function getOpenedCountAttribute(): int
    {
        return $this->recipients()->whereNotNull('campaign_recipients.opened_at')->count();
    }

    /**
     * الحصول على عدد النقرات
     */
    public function getClickedCountAttribute(): int
    {
        return $this->recipients()->whereNotNull('campaign_recipients.clicked_at')->count();
    }

    /**
     * الحصول على عدد الرسائل المرتدة
     */
    public function getBouncedCountAttribute(): int
    {
        return $this->recipients()->whereNotNull('campaign_recipients.bounced_at')->count();
    }

    /**
     * الحصول على عدد إلغاء الاشتراك
     */
    public function getUnsubscribedCountAttribute(): int
    {
        return $this->recipients()->whereNotNull('campaign_recipients.unsubscribed_at')->count();
    }

    /**
     * الحصول على معدل الفتح
     */
    public function getOpenRateAttribute(): float
    {
        $sent = $this->sent_count;
        return $sent > 0 ? round(($this->opened_count / $sent) * 100, 2) : 0;
    }

    /**
     * الحصول على معدل النقر
     */
    public function getClickRateAttribute(): float
    {
        $sent = $this->sent_count;
        return $sent > 0 ? round(($this->clicked_count / $sent) * 100, 2) : 0;
    }

    /**
     * الحصول على معدل النقر للفتح
     */
    public function getClickToOpenRateAttribute(): float
    {
        $opened = $this->opened_count;
        return $opened > 0 ? round(($this->clicked_count / $opened) * 100, 2) : 0;
    }

    /**
     * الحصول على معدل الارتداد
     */
    public function getBounceRateAttribute(): float
    {
        $sent = $this->sent_count;
        return $sent > 0 ? round(($this->bounced_count / $sent) * 100, 2) : 0;
    }

    /**
     * الحصول على معدل إلغاء الاشتراك
     */
    public function getUnsubscribeRateAttribute(): float
    {
        $sent = $this->sent_count;
        return $sent > 0 ? round(($this->unsubscribed_count / $sent) * 100, 2) : 0;
    }

    /**
     * الحصول على معدل التحويل
     */
    public function getConversionRateAttribute(): float
    {
        $opportunities = $this->generatedOpportunities()->count();
        $sent = $this->sent_count;
        return $sent > 0 ? round(($opportunities / $sent) * 100, 2) : 0;
    }

    /**
     * الحصول على عائد الاستثمار
     */
    public function getRoiAttribute(): float
    {
        $revenue = $this->generatedOpportunities()->where('stage', 'won')->sum('value');
        $cost = $this->budget ?? 0;
        return $cost > 0 ? round((($revenue - $cost) / $cost) * 100, 2) : 0;
    }

    /**
     * الحصول على المدة
     */
    public function getDurationDaysAttribute(): int
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        return $this->start_date->diffInDays($this->end_date);
    }

    /**
     * الحصول على الأيام المتبقية
     */
    public function getDaysRemainingAttribute(): int
    {
        if (!$this->end_date || $this->is_completed) {
            return 0;
        }

        return max(0, now()->diffInDays($this->end_date, false));
    }

    /**
     * الحصول على نسبة التقدم
     */
    public function getProgressPercentageAttribute(): float
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        $totalDays = $this->start_date->diffInDays($this->end_date);
        $elapsedDays = $this->start_date->diffInDays(now());

        if ($totalDays <= 0) {
            return 100;
        }

        return min(100, round(($elapsedDays / $totalDays) * 100, 1));
    }

    /**
     * تشغيل الحملة
     */
    public function start(): bool
    {
        if (!$this->can_run) {
            return false;
        }

        return $this->update([
            'status' => 'running',
            'start_date' => $this->start_date ?? now(),
        ]);
    }

    /**
     * إيقاف الحملة مؤقتاً
     */
    public function pause(): bool
    {
        if (!$this->can_pause) {
            return false;
        }

        return $this->update(['status' => 'paused']);
    }

    /**
     * استئناف الحملة
     */
    public function resume(): bool
    {
        if ($this->status !== 'paused') {
            return false;
        }

        return $this->update(['status' => 'running']);
    }

    /**
     * إكمال الحملة
     */
    public function complete(): bool
    {
        return $this->update([
            'status' => 'completed',
            'end_date' => now(),
        ]);
    }

    /**
     * إلغاء الحملة
     */
    public function cancel(string $reason = null): bool
    {
        $updated = $this->update(['status' => 'cancelled']);

        if ($updated && $reason) {
            $this->activities()->create([
                'type' => 'cancelled',
                'description' => 'تم إلغاء الحملة',
                'notes' => $reason,
                'created_by' => auth()->id(),
            ]);
        }

        return $updated;
    }

    /**
     * إضافة مستلمين للحملة
     */
    public function addRecipients(array $customerIds, string $variant = 'A'): int
    {
        $recipientData = [];
        $now = now();

        foreach ($customerIds as $customerId) {
            $recipientData[$customerId] = [
                'variant' => $variant,
                'status' => 'pending',
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        $this->recipients()->syncWithoutDetaching($recipientData);

        return count($customerIds);
    }

    /**
     * إرسال الحملة لمستلم واحد
     */
    public function sendToRecipient(Customer $customer, string $variant = 'A'): bool
    {
        // منطق الإرسال حسب نوع القناة
        switch ($this->channel) {
            case 'email':
                return $this->sendEmail($customer, $variant);
            case 'sms':
                return $this->sendSms($customer, $variant);
            case 'whatsapp':
                return $this->sendWhatsApp($customer, $variant);
            default:
                return false;
        }
    }

    /**
     * إرسال بريد إلكتروني
     */
    protected function sendEmail(Customer $customer, string $variant): bool
    {
        // تنفيذ إرسال البريد الإلكتروني
        // سيتم تطوير هذا في خدمة منفصلة
        return true;
    }

    /**
     * إرسال رسالة نصية
     */
    protected function sendSms(Customer $customer, string $variant): bool
    {
        // تنفيذ إرسال SMS
        return true;
    }

    /**
     * إرسال رسالة واتساب
     */
    protected function sendWhatsApp(Customer $customer, string $variant): bool
    {
        // تنفيذ إرسال واتساب
        return true;
    }

    /**
     * تحديث المقاييس
     */
    public function updateMetrics(): void
    {
        $metrics = [
            'recipients_count' => $this->recipients_count,
            'sent_count' => $this->sent_count,
            'opened_count' => $this->opened_count,
            'clicked_count' => $this->clicked_count,
            'bounced_count' => $this->bounced_count,
            'unsubscribed_count' => $this->unsubscribed_count,
            'open_rate' => $this->open_rate,
            'click_rate' => $this->click_rate,
            'click_to_open_rate' => $this->click_to_open_rate,
            'bounce_rate' => $this->bounce_rate,
            'unsubscribe_rate' => $this->unsubscribe_rate,
            'conversion_rate' => $this->conversion_rate,
            'roi' => $this->roi,
            'updated_at' => now(),
        ];

        $this->update(['metrics' => $metrics]);
    }

    /**
     * فلترة الحملات النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['scheduled', 'running']);
    }

    /**
     * فلترة الحملات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->whereIn('status', ['completed', 'cancelled']);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب القناة
     */
    public function scopeOfChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * فلترة حسب المسؤول
     */
    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * فلترة حسب فترة زمنية
     */
    public function scopeBetweenDates($query, \DateTime $from, \DateTime $to)
    {
        return $query->whereBetween('start_date', [$from, $to]);
    }

    /**
     * البحث في الحملات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('subject', 'LIKE', "%{$search}%");
        });
    }
}
