<?php

namespace App\Domains\Support\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج فئة التذكرة - Ticket Category
 * يدعم التصنيف الهرمي والذكاء الاصطناعي
 */
class TicketCategory extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_ar',
        'name_fr',
        'name_en',
        'description',
        'description_ar',
        'description_fr',
        'description_en',
        'parent_id',
        'department_id',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'is_public',
        'auto_assign_to',
        'default_priority',
        'sla_response_time',
        'sla_resolution_time',
        'ai_keywords',
        'ai_classification_rules',
        'workflow_id',
        'template_id',
        'requires_approval',
        'billable_by_default',
        'estimated_hours',
        'metadata',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'is_public' => 'boolean',
        'sla_response_time' => 'integer',
        'sla_resolution_time' => 'integer',
        'ai_keywords' => 'array',
        'ai_classification_rules' => 'array',
        'requires_approval' => 'boolean',
        'billable_by_default' => 'boolean',
        'estimated_hours' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الفئة الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * العلاقة مع الفئات الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * العلاقة مع القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * العلاقة مع الوكيل المعين تلقائياً
     */
    public function autoAssignAgent(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'auto_assign_to');
    }

    /**
     * العلاقة مع سير العمل
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(TicketWorkflow::class, 'workflow_id');
    }

    /**
     * العلاقة مع قالب الرد
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(TicketTemplate::class, 'template_id');
    }

    /**
     * العلاقة مع التذاكر
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'category_id');
    }

    /**
     * الحصول على الاسم بناءً على اللغة
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        
        return match ($locale) {
            'ar' => $this->name_ar ?? $this->name,
            'fr' => $this->name_fr ?? $this->name,
            'en' => $this->name_en ?? $this->name,
            default => $this->name,
        };
    }

    /**
     * الحصول على الوصف بناءً على اللغة
     */
    public function getLocalizedDescriptionAttribute(): string
    {
        $locale = app()->getLocale();
        
        return match ($locale) {
            'ar' => $this->description_ar ?? $this->description,
            'fr' => $this->description_fr ?? $this->description,
            'en' => $this->description_en ?? $this->description,
            default => $this->description,
        };
    }

    /**
     * الحصول على المسار الكامل للفئة
     */
    public function getFullPathAttribute(): string
    {
        $path = collect();
        $current = $this;

        while ($current) {
            $path->prepend($current->localized_name);
            $current = $current->parent;
        }

        return $path->implode(' > ');
    }

    /**
     * الحصول على جميع الفئات الفرعية (بشكل تكراري)
     */
    public function getAllChildrenAttribute()
    {
        $children = collect();

        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->all_children);
        }

        return $children;
    }

    /**
     * التحقق من وجود فئات فرعية
     */
    public function getHasChildrenAttribute(): bool
    {
        return $this->children()->count() > 0;
    }

    /**
     * الحصول على عدد التذاكر النشطة
     */
    public function getActiveTicketsCountAttribute(): int
    {
        return $this->tickets()
                   ->whereNotIn('status', ['closed', 'resolved'])
                   ->count();
    }

    /**
     * الحصول على متوسط وقت الحل
     */
    public function getAverageResolutionTimeAttribute(): ?float
    {
        $resolvedTickets = $this->tickets()
                               ->whereNotNull('resolved_at')
                               ->get();

        if ($resolvedTickets->isEmpty()) {
            return null;
        }

        $totalMinutes = $resolvedTickets->sum(function ($ticket) {
            return $ticket->created_at->diffInMinutes($ticket->resolved_at);
        });

        return $totalMinutes / $resolvedTickets->count();
    }

    /**
     * تصنيف تذكرة باستخدام الذكاء الاصطناعي
     */
    public function matchesAiClassification(string $subject, string $description): float
    {
        if (empty($this->ai_keywords)) {
            return 0.0;
        }

        $content = strtolower($subject . ' ' . $description);
        $matchedKeywords = 0;
        $totalKeywords = count($this->ai_keywords);

        foreach ($this->ai_keywords as $keyword) {
            if (str_contains($content, strtolower($keyword))) {
                $matchedKeywords++;
            }
        }

        return $totalKeywords > 0 ? ($matchedKeywords / $totalKeywords) : 0.0;
    }

    /**
     * تطبيق قواعد التصنيف التلقائي
     */
    public function applyClassificationRules(array $ticketData): array
    {
        $rules = $this->ai_classification_rules ?? [];
        $modifications = [];

        foreach ($rules as $rule) {
            if ($this->evaluateRule($rule, $ticketData)) {
                $modifications = array_merge($modifications, $rule['actions'] ?? []);
            }
        }

        return $modifications;
    }

    /**
     * تقييم قاعدة التصنيف
     */
    protected function evaluateRule(array $rule, array $ticketData): bool
    {
        $conditions = $rule['conditions'] ?? [];

        foreach ($conditions as $condition) {
            $field = $condition['field'];
            $operator = $condition['operator'];
            $value = $condition['value'];
            $ticketValue = data_get($ticketData, $field);

            $result = match ($operator) {
                'contains' => str_contains(strtolower($ticketValue), strtolower($value)),
                'equals' => $ticketValue === $value,
                'not_equals' => $ticketValue !== $value,
                'greater_than' => $ticketValue > $value,
                'less_than' => $ticketValue < $value,
                'in' => in_array($ticketValue, $value),
                'not_in' => !in_array($ticketValue, $value),
                default => false,
            };

            if (!$result) {
                return false;
            }
        }

        return true;
    }

    /**
     * إنشاء فئة فرعية
     */
    public function createSubcategory(array $data): self
    {
        $data['parent_id'] = $this->id;
        $data['department_id'] = $data['department_id'] ?? $this->department_id;
        $data['sort_order'] = $data['sort_order'] ?? ($this->children()->max('sort_order') + 1);

        return self::create($data);
    }

    /**
     * نقل الفئة إلى فئة أب جديدة
     */
    public function moveTo(?int $newParentId): bool
    {
        return $this->update(['parent_id' => $newParentId]);
    }

    /**
     * تحديث ترتيب الفئات
     */
    public static function updateSortOrder(array $orderedIds): void
    {
        foreach ($orderedIds as $index => $id) {
            self::where('id', $id)->update(['sort_order' => $index + 1]);
        }
    }

    /**
     * فلترة الفئات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة الفئات العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * فلترة الفئات الرئيسية
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * فلترة الفئات الفرعية
     */
    public function scopeChildren($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * فلترة حسب القسم
     */
    public function scopeForDepartment($query, int $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * ترتيب حسب الترتيب المحدد
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * البحث في الفئات
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('name_ar', 'LIKE', "%{$search}%")
              ->orWhere('name_fr', 'LIKE', "%{$search}%")
              ->orWhere('name_en', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%");
        });
    }

    /**
     * الحصول على شجرة الفئات
     */
    public static function getTree(): \Illuminate\Support\Collection
    {
        return self::with('children.children.children')
                  ->parent()
                  ->active()
                  ->ordered()
                  ->get();
    }

    /**
     * الحصول على قائمة مسطحة للفئات مع المسارات
     */
    public static function getFlatList(): \Illuminate\Support\Collection
    {
        $categories = self::with('parent')->active()->ordered()->get();
        
        return $categories->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->localized_name,
                'full_path' => $category->full_path,
                'level' => $category->getLevel(),
            ];
        });
    }

    /**
     * الحصول على مستوى الفئة في الشجرة
     */
    public function getLevel(): int
    {
        $level = 0;
        $current = $this->parent;

        while ($current) {
            $level++;
            $current = $current->parent;
        }

        return $level;
    }
}
