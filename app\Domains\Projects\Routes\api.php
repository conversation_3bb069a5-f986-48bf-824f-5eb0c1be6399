<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Projects\Controllers\ProjectController;
use App\Domains\Projects\Controllers\TaskController;
use App\Domains\Projects\Controllers\TimeEntryController;
use App\Domains\Projects\Controllers\MilestoneController;
use App\Domains\Projects\Controllers\ResourceController;
use App\Domains\Projects\Controllers\BudgetController;
use App\Domains\Projects\Controllers\RiskController;
use App\Domains\Projects\Controllers\DocumentController;
use App\Domains\Projects\Controllers\ProjectAnalyticsController;

/*
|--------------------------------------------------------------------------
| Projects API Routes
|--------------------------------------------------------------------------
|
| مسارات API لنظام إدارة المشاريع
|
*/

// Project Management
Route::apiResource('projects', ProjectController::class);
Route::prefix('projects')->group(function () {
    Route::get('{project}/dashboard', [ProjectController::class, 'getDashboard']);
    Route::post('{project}/start', [ProjectController::class, 'startProject']);
    Route::post('{project}/pause', [ProjectController::class, 'pauseProject']);
    Route::post('{project}/resume', [ProjectController::class, 'resumeProject']);
    Route::post('{project}/complete', [ProjectController::class, 'completeProject']);
    Route::post('{project}/cancel', [ProjectController::class, 'cancelProject']);
    Route::post('{project}/archive', [ProjectController::class, 'archiveProject']);
    Route::get('{project}/progress', [ProjectController::class, 'getProgress']);
    Route::get('{project}/timeline', [ProjectController::class, 'getTimeline']);
    Route::get('{project}/gantt', [ProjectController::class, 'getGanttData']);
    Route::get('{project}/kanban', [ProjectController::class, 'getKanbanBoard']);
    Route::post('{project}/duplicate', [ProjectController::class, 'duplicateProject']);
    Route::get('{project}/reports', [ProjectController::class, 'getReports']);
    Route::get('templates', [ProjectController::class, 'getTemplates']);
    Route::post('from-template', [ProjectController::class, 'createFromTemplate']);
    Route::get('search', [ProjectController::class, 'search']);
    Route::get('by-status/{status}', [ProjectController::class, 'getByStatus']);
    Route::get('by-client/{client}', [ProjectController::class, 'getByClient']);
    Route::get('overdue', [ProjectController::class, 'getOverdueProjects']);
    Route::get('at-risk', [ProjectController::class, 'getAtRiskProjects']);
});

// Team Management
Route::prefix('projects/{project}/team')->group(function () {
    Route::get('/', [ProjectController::class, 'getTeamMembers']);
    Route::post('/', [ProjectController::class, 'addTeamMember']);
    Route::put('{member}', [ProjectController::class, 'updateTeamMember']);
    Route::delete('{member}', [ProjectController::class, 'removeTeamMember']);
    Route::get('{member}/workload', [ProjectController::class, 'getMemberWorkload']);
    Route::get('roles', [ProjectController::class, 'getTeamRoles']);
    Route::post('{member}/assign-role', [ProjectController::class, 'assignRole']);
    Route::get('availability', [ProjectController::class, 'getTeamAvailability']);
});

// Task Management
Route::apiResource('tasks', TaskController::class);
Route::prefix('tasks')->group(function () {
    Route::post('{task}/start', [TaskController::class, 'startTask']);
    Route::post('{task}/complete', [TaskController::class, 'completeTask']);
    Route::post('{task}/pause', [TaskController::class, 'pauseTask']);
    Route::post('{task}/resume', [TaskController::class, 'resumeTask']);
    Route::post('{task}/cancel', [TaskController::class, 'cancelTask']);
    Route::get('{task}/subtasks', [TaskController::class, 'getSubtasks']);
    Route::post('{task}/subtasks', [TaskController::class, 'createSubtask']);
    Route::get('{task}/dependencies', [TaskController::class, 'getDependencies']);
    Route::post('{task}/dependencies', [TaskController::class, 'addDependency']);
    Route::delete('{task}/dependencies/{dependency}', [TaskController::class, 'removeDependency']);
    Route::get('{task}/comments', [TaskController::class, 'getComments']);
    Route::post('{task}/comments', [TaskController::class, 'addComment']);
    Route::put('comments/{comment}', [TaskController::class, 'updateComment']);
    Route::delete('comments/{comment}', [TaskController::class, 'deleteComment']);
    Route::get('{task}/attachments', [TaskController::class, 'getAttachments']);
    Route::post('{task}/attachments', [TaskController::class, 'addAttachment']);
    Route::delete('{task}/attachments/{attachment}', [TaskController::class, 'removeAttachment']);
    Route::post('{task}/assign', [TaskController::class, 'assignTask']);
    Route::post('{task}/unassign', [TaskController::class, 'unassignTask']);
    Route::get('{task}/history', [TaskController::class, 'getTaskHistory']);
    Route::post('{task}/duplicate', [TaskController::class, 'duplicateTask']);
    Route::get('my-tasks', [TaskController::class, 'getMyTasks']);
    Route::get('overdue', [TaskController::class, 'getOverdueTasks']);
    Route::get('due-today', [TaskController::class, 'getTasksDueToday']);
    Route::get('by-project/{project}', [TaskController::class, 'getTasksByProject']);
    Route::get('by-assignee/{user}', [TaskController::class, 'getTasksByAssignee']);
    Route::get('by-status/{status}', [TaskController::class, 'getTasksByStatus']);
    Route::get('by-priority/{priority}', [TaskController::class, 'getTasksByPriority']);
    Route::post('bulk-update', [TaskController::class, 'bulkUpdateTasks']);
    Route::post('bulk-assign', [TaskController::class, 'bulkAssignTasks']);
});

// Time Tracking
Route::apiResource('time-entries', TimeEntryController::class);
Route::prefix('time-entries')->group(function () {
    Route::post('start', [TimeEntryController::class, 'startTimer']);
    Route::post('stop', [TimeEntryController::class, 'stopTimer']);
    Route::post('{entry}/approve', [TimeEntryController::class, 'approveEntry']);
    Route::post('{entry}/reject', [TimeEntryController::class, 'rejectEntry']);
    Route::get('active', [TimeEntryController::class, 'getActiveEntries']);
    Route::get('my-entries', [TimeEntryController::class, 'getMyEntries']);
    Route::get('project/{project}', [TimeEntryController::class, 'getProjectEntries']);
    Route::get('task/{task}', [TimeEntryController::class, 'getTaskEntries']);
    Route::get('user/{user}', [TimeEntryController::class, 'getUserEntries']);
    Route::get('pending-approval', [TimeEntryController::class, 'getPendingApproval']);
    Route::get('timesheet/{user}/{period}', [TimeEntryController::class, 'getTimesheet']);
    Route::post('timesheet/{user}/{period}/submit', [TimeEntryController::class, 'submitTimesheet']);
    Route::post('timesheet/{user}/{period}/approve', [TimeEntryController::class, 'approveTimesheet']);
    Route::get('reports/{period}', [TimeEntryController::class, 'getTimeReports']);
    Route::post('bulk-approve', [TimeEntryController::class, 'bulkApproveEntries']);
    Route::get('export/{period}', [TimeEntryController::class, 'exportTimeEntries']);
});

// Milestone Management
Route::apiResource('milestones', MilestoneController::class);
Route::prefix('milestones')->group(function () {
    Route::post('{milestone}/complete', [MilestoneController::class, 'completeMilestone']);
    Route::post('{milestone}/reopen', [MilestoneController::class, 'reopenMilestone']);
    Route::get('{milestone}/tasks', [MilestoneController::class, 'getMilestoneTasks']);
    Route::get('project/{project}', [MilestoneController::class, 'getProjectMilestones']);
    Route::get('upcoming', [MilestoneController::class, 'getUpcomingMilestones']);
    Route::get('overdue', [MilestoneController::class, 'getOverdueMilestones']);
    Route::get('completed', [MilestoneController::class, 'getCompletedMilestones']);
});

// Resource Management
Route::apiResource('resources', ResourceController::class);
Route::prefix('resources')->group(function () {
    Route::get('availability', [ResourceController::class, 'getAvailability']);
    Route::get('allocation', [ResourceController::class, 'getAllocation']);
    Route::post('allocate', [ResourceController::class, 'allocateResource']);
    Route::post('deallocate', [ResourceController::class, 'deallocateResource']);
    Route::get('conflicts', [ResourceController::class, 'getResourceConflicts']);
    Route::get('utilization', [ResourceController::class, 'getUtilization']);
    Route::get('capacity-planning', [ResourceController::class, 'getCapacityPlanning']);
    Route::get('skills-matrix', [ResourceController::class, 'getSkillsMatrix']);
    Route::post('skills-matching', [ResourceController::class, 'findSkillsMatch']);
    Route::get('cost-analysis', [ResourceController::class, 'getCostAnalysis']);
});

// Budget Management
Route::apiResource('budgets', BudgetController::class);
Route::prefix('budgets')->group(function () {
    Route::get('project/{project}', [BudgetController::class, 'getProjectBudget']);
    Route::post('project/{project}', [BudgetController::class, 'createProjectBudget']);
    Route::put('project/{project}', [BudgetController::class, 'updateProjectBudget']);
    Route::get('{budget}/variance', [BudgetController::class, 'getBudgetVariance']);
    Route::get('{budget}/forecast', [BudgetController::class, 'getBudgetForecast']);
    Route::get('{budget}/expenses', [BudgetController::class, 'getBudgetExpenses']);
    Route::post('{budget}/expenses', [BudgetController::class, 'addExpense']);
    Route::put('expenses/{expense}', [BudgetController::class, 'updateExpense']);
    Route::delete('expenses/{expense}', [BudgetController::class, 'deleteExpense']);
    Route::post('{budget}/approve', [BudgetController::class, 'approveBudget']);
    Route::post('{budget}/revise', [BudgetController::class, 'reviseBudget']);
    Route::get('alerts', [BudgetController::class, 'getBudgetAlerts']);
    Route::get('reports/{period}', [BudgetController::class, 'getBudgetReports']);
});

// Risk Management
Route::apiResource('risks', RiskController::class);
Route::prefix('risks')->group(function () {
    Route::get('project/{project}', [RiskController::class, 'getProjectRisks']);
    Route::post('{risk}/assess', [RiskController::class, 'assessRisk']);
    Route::post('{risk}/mitigate', [RiskController::class, 'mitigateRisk']);
    Route::post('{risk}/close', [RiskController::class, 'closeRisk']);
    Route::get('{risk}/mitigation-plans', [RiskController::class, 'getMitigationPlans']);
    Route::post('{risk}/mitigation-plans', [RiskController::class, 'createMitigationPlan']);
    Route::get('high-priority', [RiskController::class, 'getHighPriorityRisks']);
    Route::get('active', [RiskController::class, 'getActiveRisks']);
    Route::get('matrix', [RiskController::class, 'getRiskMatrix']);
    Route::get('reports', [RiskController::class, 'getRiskReports']);
});

// Document Management
Route::apiResource('documents', DocumentController::class);
Route::prefix('documents')->group(function () {
    Route::get('project/{project}', [DocumentController::class, 'getProjectDocuments']);
    Route::post('project/{project}/upload', [DocumentController::class, 'uploadDocument']);
    Route::get('{document}/versions', [DocumentController::class, 'getDocumentVersions']);
    Route::post('{document}/new-version', [DocumentController::class, 'createNewVersion']);
    Route::get('{document}/download', [DocumentController::class, 'downloadDocument']);
    Route::post('{document}/share', [DocumentController::class, 'shareDocument']);
    Route::post('{document}/approve', [DocumentController::class, 'approveDocument']);
    Route::post('{document}/reject', [DocumentController::class, 'rejectDocument']);
    Route::get('{document}/comments', [DocumentController::class, 'getDocumentComments']);
    Route::post('{document}/comments', [DocumentController::class, 'addDocumentComment']);
    Route::get('pending-approval', [DocumentController::class, 'getPendingApproval']);
    Route::get('recent', [DocumentController::class, 'getRecentDocuments']);
    Route::get('search', [DocumentController::class, 'searchDocuments']);
});

// Project Analytics
Route::prefix('analytics')->group(function () {
    Route::get('dashboard', [ProjectAnalyticsController::class, 'getDashboard']);
    Route::get('project/{project}/overview', [ProjectAnalyticsController::class, 'getProjectOverview']);
    Route::get('project/{project}/performance', [ProjectAnalyticsController::class, 'getProjectPerformance']);
    Route::get('project/{project}/timeline-analysis', [ProjectAnalyticsController::class, 'getTimelineAnalysis']);
    Route::get('project/{project}/cost-analysis', [ProjectAnalyticsController::class, 'getCostAnalysis']);
    Route::get('project/{project}/resource-utilization', [ProjectAnalyticsController::class, 'getResourceUtilization']);
    Route::get('project/{project}/risk-analysis', [ProjectAnalyticsController::class, 'getRiskAnalysis']);
    Route::get('portfolio-overview', [ProjectAnalyticsController::class, 'getPortfolioOverview']);
    Route::get('team-performance', [ProjectAnalyticsController::class, 'getTeamPerformance']);
    Route::get('productivity-metrics', [ProjectAnalyticsController::class, 'getProductivityMetrics']);
    Route::get('profitability-analysis', [ProjectAnalyticsController::class, 'getProfitabilityAnalysis']);
    Route::get('client-satisfaction', [ProjectAnalyticsController::class, 'getClientSatisfaction']);
    Route::get('delivery-metrics', [ProjectAnalyticsController::class, 'getDeliveryMetrics']);
    Route::get('quality-metrics', [ProjectAnalyticsController::class, 'getQualityMetrics']);
    Route::get('trend-analysis', [ProjectAnalyticsController::class, 'getTrendAnalysis']);
    Route::get('predictive-insights', [ProjectAnalyticsController::class, 'getPredictiveInsights']);
    Route::get('benchmarking', [ProjectAnalyticsController::class, 'getBenchmarkingData']);
});

// Agile/Scrum Features
Route::prefix('agile')->group(function () {
    Route::get('sprints', [ProjectController::class, 'getSprints']);
    Route::post('sprints', [ProjectController::class, 'createSprint']);
    Route::put('sprints/{sprint}', [ProjectController::class, 'updateSprint']);
    Route::delete('sprints/{sprint}', [ProjectController::class, 'deleteSprint']);
    Route::post('sprints/{sprint}/start', [ProjectController::class, 'startSprint']);
    Route::post('sprints/{sprint}/complete', [ProjectController::class, 'completeSprint']);
    Route::get('sprints/{sprint}/burndown', [ProjectController::class, 'getSprintBurndown']);
    Route::get('sprints/{sprint}/velocity', [ProjectController::class, 'getSprintVelocity']);
    Route::get('backlog', [TaskController::class, 'getBacklog']);
    Route::post('backlog/prioritize', [TaskController::class, 'prioritizeBacklog']);
    Route::get('user-stories', [TaskController::class, 'getUserStories']);
    Route::post('user-stories', [TaskController::class, 'createUserStory']);
    Route::get('epics', [TaskController::class, 'getEpics']);
    Route::post('epics', [TaskController::class, 'createEpic']);
    Route::get('retrospectives', [ProjectController::class, 'getRetrospectives']);
    Route::post('retrospectives', [ProjectController::class, 'createRetrospective']);
    Route::get('daily-standups', [ProjectController::class, 'getDailyStandups']);
    Route::post('daily-standups', [ProjectController::class, 'createDailyStandup']);
});

// Collaboration Features
Route::prefix('collaboration')->group(function () {
    Route::get('activity-feed', [ProjectController::class, 'getActivityFeed']);
    Route::get('notifications', [ProjectController::class, 'getNotifications']);
    Route::post('notifications/mark-read', [ProjectController::class, 'markNotificationsRead']);
    Route::get('discussions', [ProjectController::class, 'getDiscussions']);
    Route::post('discussions', [ProjectController::class, 'createDiscussion']);
    Route::get('discussions/{discussion}/messages', [ProjectController::class, 'getDiscussionMessages']);
    Route::post('discussions/{discussion}/messages', [ProjectController::class, 'addDiscussionMessage']);
    Route::get('announcements', [ProjectController::class, 'getAnnouncements']);
    Route::post('announcements', [ProjectController::class, 'createAnnouncement']);
    Route::get('calendar', [ProjectController::class, 'getProjectCalendar']);
    Route::post('meetings', [ProjectController::class, 'scheduleMeeting']);
    Route::get('meetings', [ProjectController::class, 'getMeetings']);
    Route::put('meetings/{meeting}', [ProjectController::class, 'updateMeeting']);
    Route::delete('meetings/{meeting}', [ProjectController::class, 'cancelMeeting']);
});

// Import/Export
Route::prefix('import-export')->group(function () {
    Route::post('import-projects', [ProjectController::class, 'importProjects']);
    Route::post('import-tasks', [TaskController::class, 'importTasks']);
    Route::post('import-time-entries', [TimeEntryController::class, 'importTimeEntries']);
    Route::get('export-projects', [ProjectController::class, 'exportProjects']);
    Route::get('export-tasks', [TaskController::class, 'exportTasks']);
    Route::get('export-time-entries', [TimeEntryController::class, 'exportTimeEntries']);
    Route::get('export-reports', [ProjectAnalyticsController::class, 'exportReports']);
    Route::post('backup-data', [ProjectAnalyticsController::class, 'backupProjectData']);
    Route::post('restore-data', [ProjectAnalyticsController::class, 'restoreProjectData']);
});

// Integration APIs
Route::prefix('integrations')->group(function () {
    Route::post('sync-with-crm', [ProjectController::class, 'syncWithCRM']);
    Route::post('sync-with-accounting', [BudgetController::class, 'syncWithAccounting']);
    Route::post('sync-with-hr', [ResourceController::class, 'syncWithHR']);
    Route::get('external-tools', [ProjectController::class, 'getExternalTools']);
    Route::post('connect-tool', [ProjectController::class, 'connectExternalTool']);
    Route::delete('disconnect-tool/{tool}', [ProjectController::class, 'disconnectExternalTool']);
    Route::post('webhook/{tool}', [ProjectController::class, 'handleWebhook']);
});
