<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج عقد الموظف
 * إدارة شاملة لعقود العمل مع دعم القوانين المحلية
 */
class EmployeeContract extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, LogsActivity;

    protected $fillable = [
        'employee_id',
        'contract_number',
        'contract_type',
        'employment_type',
        'start_date',
        'end_date',
        'probation_period_months',
        'notice_period_days',
        'working_hours_per_week',
        'working_days_per_week',
        'overtime_eligible',
        'remote_work_allowed',
        
        // Salary Information
        'basic_salary',
        'currency',
        'salary_frequency',
        'salary_review_frequency',
        'next_salary_review_date',
        
        // Benefits
        'health_insurance',
        'life_insurance',
        'annual_leave_days',
        'sick_leave_days',
        'maternity_leave_days',
        'paternity_leave_days',
        'hajj_leave_days',
        'study_leave_days',
        
        // Allowances
        'housing_allowance',
        'transportation_allowance',
        'meal_allowance',
        'phone_allowance',
        'internet_allowance',
        'education_allowance',
        'family_allowance',
        'overtime_rate',
        
        // Legal Compliance
        'gosi_applicable',
        'income_tax_applicable',
        'visa_sponsorship',
        'work_permit_required',
        'labor_law_country',
        
        // Contract Terms
        'confidentiality_clause',
        'non_compete_clause',
        'non_disclosure_clause',
        'intellectual_property_clause',
        'termination_conditions',
        'renewal_conditions',
        
        // Status
        'status',
        'is_active',
        'signed_date',
        'signed_by_employee',
        'signed_by_employer',
        'witness_name',
        'witness_signature',
        
        // Documents
        'contract_document_path',
        'addendum_documents',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'next_salary_review_date' => 'date',
        'signed_date' => 'date',
        'basic_salary' => 'decimal:2',
        'housing_allowance' => 'decimal:2',
        'transportation_allowance' => 'decimal:2',
        'meal_allowance' => 'decimal:2',
        'phone_allowance' => 'decimal:2',
        'internet_allowance' => 'decimal:2',
        'education_allowance' => 'decimal:2',
        'family_allowance' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'working_hours_per_week' => 'decimal:1',
        'working_days_per_week' => 'decimal:1',
        'probation_period_months' => 'integer',
        'notice_period_days' => 'integer',
        'annual_leave_days' => 'integer',
        'sick_leave_days' => 'integer',
        'maternity_leave_days' => 'integer',
        'paternity_leave_days' => 'integer',
        'hajj_leave_days' => 'integer',
        'study_leave_days' => 'integer',
        'overtime_eligible' => 'boolean',
        'remote_work_allowed' => 'boolean',
        'health_insurance' => 'boolean',
        'life_insurance' => 'boolean',
        'gosi_applicable' => 'boolean',
        'income_tax_applicable' => 'boolean',
        'visa_sponsorship' => 'boolean',
        'work_permit_required' => 'boolean',
        'confidentiality_clause' => 'boolean',
        'non_compete_clause' => 'boolean',
        'non_disclosure_clause' => 'boolean',
        'intellectual_property_clause' => 'boolean',
        'is_active' => 'boolean',
        'signed_by_employee' => 'boolean',
        'signed_by_employer' => 'boolean',
        'addendum_documents' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع العقود
     */
    const CONTRACT_TYPES = [
        'PERMANENT' => 'دائم',
        'TEMPORARY' => 'مؤقت',
        'FIXED_TERM' => 'محدد المدة',
        'PROBATIONARY' => 'تجريبي',
        'SEASONAL' => 'موسمي',
        'PROJECT_BASED' => 'مشروع محدد',
        'CONSULTANT' => 'استشاري',
        'INTERNSHIP' => 'تدريب',
    ];

    /**
     * أنواع التوظيف
     */
    const EMPLOYMENT_TYPES = [
        'FULL_TIME' => 'دوام كامل',
        'PART_TIME' => 'دوام جزئي',
        'REMOTE' => 'عن بُعد',
        'HYBRID' => 'مختلط',
        'SHIFT_WORK' => 'عمل بنظام الورديات',
        'FLEXIBLE' => 'مرن',
    ];

    /**
     * حالات العقد
     */
    const STATUSES = [
        'DRAFT' => 'مسودة',
        'PENDING_SIGNATURE' => 'في انتظار التوقيع',
        'ACTIVE' => 'نشط',
        'EXPIRED' => 'منتهي',
        'TERMINATED' => 'منهي',
        'SUSPENDED' => 'موقوف',
        'RENEWED' => 'مجدد',
        'CANCELLED' => 'ملغي',
    ];

    /**
     * تكرار الراتب
     */
    const SALARY_FREQUENCIES = [
        'MONTHLY' => 'شهري',
        'WEEKLY' => 'أسبوعي',
        'BIWEEKLY' => 'كل أسبوعين',
        'QUARTERLY' => 'ربع سنوي',
        'ANNUALLY' => 'سنوي',
    ];

    /**
     * الموظف
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * تعديلات العقد
     */
    public function amendments(): HasMany
    {
        return $this->hasMany(ContractAmendment::class);
    }

    /**
     * مراجعات الراتب
     */
    public function salaryReviews(): HasMany
    {
        return $this->hasMany(SalaryReview::class);
    }

    /**
     * حساب إجمالي الراتب
     */
    public function getTotalSalaryAttribute(): float
    {
        return $this->basic_salary + 
               $this->housing_allowance + 
               $this->transportation_allowance + 
               $this->meal_allowance + 
               $this->phone_allowance + 
               $this->internet_allowance + 
               $this->education_allowance + 
               $this->family_allowance;
    }

    /**
     * حساب إجمالي البدلات
     */
    public function getTotalAllowancesAttribute(): float
    {
        return $this->housing_allowance + 
               $this->transportation_allowance + 
               $this->meal_allowance + 
               $this->phone_allowance + 
               $this->internet_allowance + 
               $this->education_allowance + 
               $this->family_allowance;
    }

    /**
     * التحقق من انتهاء العقد
     */
    public function isExpired(): bool
    {
        return $this->end_date && now() > $this->end_date;
    }

    /**
     * التحقق من قرب انتهاء العقد
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->end_date && 
               now()->diffInDays($this->end_date, false) <= $days && 
               now() < $this->end_date;
    }

    /**
     * التحقق من فترة التجربة
     */
    public function isInProbationPeriod(): bool
    {
        if (!$this->probation_period_months) {
            return false;
        }

        $probationEndDate = $this->start_date->addMonths($this->probation_period_months);
        return now() <= $probationEndDate;
    }

    /**
     * حساب مدة العقد بالأشهر
     */
    public function getContractDurationInMonths(): ?int
    {
        if (!$this->end_date) {
            return null;
        }

        return $this->start_date->diffInMonths($this->end_date);
    }

    /**
     * تجديد العقد
     */
    public function renew(array $newTerms): self
    {
        // إنهاء العقد الحالي
        $this->update([
            'status' => 'RENEWED',
            'is_active' => false,
        ]);

        // إنشاء عقد جديد
        $newContract = $this->replicate();
        $newContract->fill($newTerms);
        $newContract->status = 'ACTIVE';
        $newContract->is_active = true;
        $newContract->save();

        return $newContract;
    }

    /**
     * إنهاء العقد
     */
    public function terminate(string $reason = null): bool
    {
        return $this->update([
            'status' => 'TERMINATED',
            'is_active' => false,
            'end_date' => now(),
            'notes' => $reason ? "تم الإنهاء: {$reason}" : $this->notes,
        ]);
    }

    /**
     * إضافة تعديل للعقد
     */
    public function addAmendment(array $changes, string $reason): ContractAmendment
    {
        return $this->amendments()->create([
            'amendment_date' => now(),
            'changes' => $changes,
            'reason' => $reason,
            'status' => 'PENDING',
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'status', 'basic_salary', 'start_date', 'end_date',
                'contract_type', 'employment_type'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * نطاق للعقود النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'ACTIVE');
    }

    /**
     * نطاق للعقود المنتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    /**
     * نطاق للعقود قريبة الانتهاء
     */
    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->whereBetween('end_date', [now(), now()->addDays($days)]);
    }

    /**
     * نطاق حسب نوع العقد
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('contract_type', $type);
    }

    /**
     * نطاق للعقود في فترة التجربة
     */
    public function scopeInProbation($query)
    {
        return $query->whereNotNull('probation_period_months')
            ->whereRaw('DATE_ADD(start_date, INTERVAL probation_period_months MONTH) >= ?', [now()]);
    }
}
