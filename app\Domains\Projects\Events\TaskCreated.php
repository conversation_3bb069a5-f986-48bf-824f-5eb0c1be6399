<?php

namespace App\Domains\Projects\Events;

use App\Domains\Projects\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث إنشاء مهمة جديدة
 */
class TaskCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Task $task;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Task $task)
    {
        $this->task = $task;
    }

    /**
     * الحصول على القنوات التي يجب بث الحدث عليها
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('project.' . $this->task->project_id),
            new PrivateChannel('tasks'),
        ];

        // إضافة قناة المكلف إذا كان موجوداً
        if ($this->task->assignee_id) {
            $channels[] = new PrivateChannel('user.' . $this->task->assignee_id);
        }

        // إضافة قناة مدير المشروع
        if ($this->task->project->project_manager_id) {
            $channels[] = new PrivateChannel('user.' . $this->task->project->project_manager_id);
        }

        return $channels;
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'task.created';
    }

    /**
     * البيانات المرسلة مع الحدث
     */
    public function broadcastWith(): array
    {
        return [
            'task' => [
                'id' => $this->task->id,
                'title' => $this->task->title,
                'task_number' => $this->task->task_number,
                'status' => $this->task->status,
                'priority' => $this->task->priority,
                'project_id' => $this->task->project_id,
                'project_name' => $this->task->project->name,
                'assignee' => $this->task->assignee?->name,
                'due_date' => $this->task->due_date?->toISOString(),
                'created_at' => $this->task->created_at->toISOString(),
            ],
            'message' => "تم إنشاء مهمة جديدة: {$this->task->title}",
            'timestamp' => now()->toISOString(),
        ];
    }
}
