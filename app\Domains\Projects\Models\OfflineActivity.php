<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج النشاط دون اتصال - Offline Activity
 */
class OfflineActivity extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'session_id',
        'activity_type',
        'activity_data',
        'recorded_at',
        'sync_status',
        'local_id',
        'synced_at',
        'server_id',
        'sync_error',
    ];

    protected $casts = [
        'activity_data' => 'array',
        'recorded_at' => 'datetime',
        'synced_at' => 'datetime',
    ];

    public function session(): BelongsTo
    {
        return $this->belongsTo(RemoteSession::class, 'session_id');
    }
}
