<?php

namespace App\Domains\ECommerce\Jobs;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use App\Domains\ECommerce\Services\ECommerceSyncService;
use App\Domains\ECommerce\Factories\ECommercePlatformDriverFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * مهمة مزامنة بيانات التجارة الإلكترونية
 */
class SyncECommerceDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ECommerceIntegration $integration;
    protected string $syncType;
    protected string $entityType;
    protected array $options;
    protected ?ECommerceSyncLog $syncLog = null;

    /**
     * عدد المحاولات المسموحة
     */
    public int $tries = 3;

    /**
     * مهلة تنفيذ المهمة (بالثواني)
     */
    public int $timeout = 300; // 5 دقائق

    /**
     * إنشاء مثيل جديد من المهمة
     */
    public function __construct(
        ECommerceIntegration $integration,
        string $syncType,
        string $entityType,
        array $options = []
    ) {
        $this->integration = $integration;
        $this->syncType = $syncType;
        $this->entityType = $entityType;
        $this->options = $options;
        
        // تعيين الطابور حسب أولوية المزامنة
        $this->onQueue($this->getSyncQueue());
    }

    /**
     * تنفيذ المهمة
     */
    public function handle(ECommerceSyncService $syncService): void
    {
        $startTime = microtime(true);
        
        try {
            // إنشاء سجل المزامنة
            $this->syncLog = $this->createSyncLog();
            
            // تحديث حالة التكامل
            $this->integration->update(['status' => 'syncing']);
            
            Log::info('بدء مزامنة التجارة الإلكترونية', [
                'integration_id' => $this->integration->id,
                'platform' => $this->integration->platform->name,
                'sync_type' => $this->syncType,
                'entity_type' => $this->entityType,
            ]);

            // الحصول على برنامج التشغيل
            $driver = ECommercePlatformDriverFactory::createFromIntegration($this->integration);
            
            // تنفيذ المزامنة حسب نوع الكيان
            $result = $this->performSync($driver, $syncService);
            
            // حساب مدة التنفيذ
            $duration = round((microtime(true) - $startTime) * 1000); // بالميلي ثانية
            
            // تحديث سجل المزامنة بالنتائج
            $this->updateSyncLog($result, $duration, true);
            
            // تحديث حالة التكامل
            $this->integration->update([
                'status' => 'active',
                'last_sync_at' => now(),
                'last_sync_status' => 'success',
            ]);
            
            Log::info('تمت مزامنة التجارة الإلكترونية بنجاح', [
                'integration_id' => $this->integration->id,
                'entity_type' => $this->entityType,
                'records_processed' => $result['processed'] ?? 0,
                'duration_ms' => $duration,
            ]);

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000);
            
            // تسجيل الخطأ
            Log::error('فشل في مزامنة التجارة الإلكترونية', [
                'integration_id' => $this->integration->id,
                'entity_type' => $this->entityType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // تحديث سجل المزامنة بالخطأ
            if ($this->syncLog) {
                $this->updateSyncLog([], $duration, false, $e->getMessage());
            }
            
            // تحديث حالة التكامل
            $this->integration->update([
                'status' => 'error',
                'last_sync_status' => 'failed',
            ]);
            
            // إعادة رمي الاستثناء للمحاولة مرة أخرى
            throw $e;
        }
    }

    /**
     * تنفيذ المزامنة حسب نوع الكيان
     */
    protected function performSync($driver, ECommerceSyncService $syncService): array
    {
        return match ($this->entityType) {
            'products' => $this->syncProducts($driver, $syncService),
            'orders' => $this->syncOrders($driver, $syncService),
            'customers' => $this->syncCustomers($driver, $syncService),
            'categories' => $this->syncCategories($driver, $syncService),
            'inventory' => $this->syncInventory($driver, $syncService),
            'all' => $this->syncAll($driver, $syncService),
            default => throw new \InvalidArgumentException("نوع الكيان غير مدعوم: {$this->entityType}"),
        };
    }

    /**
     * مزامنة المنتجات
     */
    protected function syncProducts($driver, ECommerceSyncService $syncService): array
    {
        $options = array_merge([
            'limit' => $this->integration->sync_config['batch_size'] ?? 50,
            'page' => 1,
        ], $this->options);

        if ($this->syncType === 'incremental') {
            $options['updated_since'] = $this->integration->last_sync_at?->toISOString();
        }

        return $driver->syncProducts($this->integration, $options);
    }

    /**
     * مزامنة الطلبات
     */
    protected function syncOrders($driver, ECommerceSyncService $syncService): array
    {
        $options = array_merge([
            'limit' => $this->integration->sync_config['batch_size'] ?? 50,
            'page' => 1,
        ], $this->options);

        if ($this->syncType === 'incremental') {
            $options['created_since'] = $this->integration->last_sync_at?->toISOString();
        }

        return $driver->syncOrders($this->integration, $options);
    }

    /**
     * مزامنة العملاء
     */
    protected function syncCustomers($driver, ECommerceSyncService $syncService): array
    {
        $options = array_merge([
            'limit' => $this->integration->sync_config['batch_size'] ?? 50,
            'page' => 1,
        ], $this->options);

        if ($this->syncType === 'incremental') {
            $options['updated_since'] = $this->integration->last_sync_at?->toISOString();
        }

        return $driver->syncCustomers($this->integration, $options);
    }

    /**
     * مزامنة الفئات
     */
    protected function syncCategories($driver, ECommerceSyncService $syncService): array
    {
        $options = array_merge([
            'limit' => 100, // الفئات عادة أقل عدداً
        ], $this->options);

        return $driver->syncCategories($this->integration, $options);
    }

    /**
     * مزامنة المخزون
     */
    protected function syncInventory($driver, ECommerceSyncService $syncService): array
    {
        $options = array_merge([
            'limit' => $this->integration->sync_config['batch_size'] ?? 50,
        ], $this->options);

        return $driver->syncInventory($this->integration, $options);
    }

    /**
     * مزامنة جميع البيانات
     */
    protected function syncAll($driver, ECommerceSyncService $syncService): array
    {
        $results = [
            'total' => 0,
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'details' => [],
        ];

        $entities = ['categories', 'products', 'customers', 'orders'];
        
        foreach ($entities as $entity) {
            if ($this->integration->sync_config["sync_{$entity}"] ?? true) {
                try {
                    $entityResult = $this->performSync($driver, $syncService);
                    $results['details'][$entity] = $entityResult;
                    $results['total'] += $entityResult['total'] ?? 0;
                    $results['processed'] += $entityResult['processed'] ?? 0;
                    $results['successful'] += $entityResult['successful'] ?? 0;
                    $results['failed'] += $entityResult['failed'] ?? 0;
                } catch (\Exception $e) {
                    $results['details'][$entity] = [
                        'error' => $e->getMessage(),
                        'total' => 0,
                        'processed' => 0,
                        'successful' => 0,
                        'failed' => 1,
                    ];
                    $results['failed']++;
                }
            }
        }

        return $results;
    }

    /**
     * إنشاء سجل المزامنة
     */
    protected function createSyncLog(): ECommerceSyncLog
    {
        return ECommerceSyncLog::create([
            'integration_id' => $this->integration->id,
            'entity_type' => $this->entityType,
            'operation' => $this->syncType,
            'status' => 'running',
            'started_at' => now(),
            'options' => $this->options,
        ]);
    }

    /**
     * تحديث سجل المزامنة
     */
    protected function updateSyncLog(array $result, int $duration, bool $isSuccessful, ?string $errorMessage = null): void
    {
        if (!$this->syncLog) {
            return;
        }

        $this->syncLog->update([
            'is_successful' => $isSuccessful,
            'records_processed' => $result['processed'] ?? 0,
            'records_successful' => $result['successful'] ?? 0,
            'records_failed' => $result['failed'] ?? 0,
            'duration' => $duration,
            'error_message' => $errorMessage,
            'result_data' => $result,
            'completed_at' => now(),
            'status' => $isSuccessful ? 'completed' : 'failed',
        ]);
    }

    /**
     * الحصول على اسم الطابور المناسب
     */
    protected function getSyncQueue(): string
    {
        return match ($this->syncType) {
            'full' => 'sync-full',
            'incremental' => 'sync-incremental',
            'real-time' => 'sync-realtime',
            default => 'sync-default',
        };
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('فشل نهائي في مزامنة التجارة الإلكترونية', [
            'integration_id' => $this->integration->id,
            'entity_type' => $this->entityType,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
        ]);

        // تحديث حالة التكامل
        $this->integration->update([
            'status' => 'error',
            'last_sync_status' => 'failed',
        ]);

        // تحديث سجل المزامنة إذا كان موجوداً
        if ($this->syncLog) {
            $this->syncLog->update([
                'is_successful' => false,
                'status' => 'failed',
                'error_message' => $exception->getMessage(),
                'completed_at' => now(),
            ]);
        }
    }

    /**
     * الحصول على معرف فريد للمهمة
     */
    public function uniqueId(): string
    {
        return "sync-{$this->integration->id}-{$this->entityType}-{$this->syncType}";
    }
}
