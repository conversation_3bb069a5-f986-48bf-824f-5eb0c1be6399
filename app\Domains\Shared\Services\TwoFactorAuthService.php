<?php

namespace App\Domains\Shared\Services;

use App\Models\User;
use Illuminate\Support\Facades\Crypt;
// use PragmaRX\Google2FA\Google2FA;

/**
 * خدمة المصادقة الثنائية
 */
class TwoFactorAuthService
{
    protected Google2FA $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * تفعيل المصادقة الثنائية للمستخدم
     */
    public function enable(User $user): array
    {
        $secretKey = $this->google2fa->generateSecretKey();
        $qrCodeUrl = $this->google2fa->getQRCodeUrl(
            config('app.name'),
            $user->email,
            $secretKey
        );

        $user->update([
            'two_factor_secret' => Crypt::encrypt($secretKey),
            'two_factor_enabled' => false, // سيتم تفعيله بعد التحقق
        ]);

        return [
            'secret' => $secretKey,
            'qr_code' => $qrCodeUrl,
        ];
    }

    /**
     * تأكيد تفعيل المصادقة الثنائية
     */
    public function confirm(User $user, string $code): bool
    {
        $secretKey = Crypt::decrypt($user->two_factor_secret);

        if ($this->google2fa->verifyKey($secretKey, $code)) {
            $user->update([
                'two_factor_enabled' => true,
                'two_factor_recovery_codes' => $this->generateRecoveryCodes(),
            ]);

            return true;
        }

        return false;
    }

    /**
     * التحقق من رمز المصادقة الثنائية
     */
    public function verify(User $user, string $code): bool
    {
        if (!$user->hasTwoFactorEnabled()) {
            return true; // إذا لم تكن مفعلة، السماح بالمرور
        }

        $secretKey = Crypt::decrypt($user->two_factor_secret);
        return $this->google2fa->verifyKey($secretKey, $code);
    }

    /**
     * التحقق من رمز الاسترداد
     */
    public function verifyRecoveryCode(User $user, string $code): bool
    {
        $recoveryCodes = $user->two_factor_recovery_codes ?? [];

        if (in_array($code, $recoveryCodes)) {
            // إزالة الرمز المستخدم
            $recoveryCodes = array_diff($recoveryCodes, [$code]);
            $user->update(['two_factor_recovery_codes' => array_values($recoveryCodes)]);

            return true;
        }

        return false;
    }

    /**
     * إلغاء تفعيل المصادقة الثنائية
     */
    public function disable(User $user): bool
    {
        $user->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ]);

        return true;
    }

    /**
     * توليد رموز الاسترداد
     */
    protected function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        }

        return $codes;
    }

    /**
     * إعادة توليد رموز الاسترداد
     */
    public function regenerateRecoveryCodes(User $user): array
    {
        $codes = $this->generateRecoveryCodes();

        $user->update([
            'two_factor_recovery_codes' => $codes,
        ]);

        return $codes;
    }
}
