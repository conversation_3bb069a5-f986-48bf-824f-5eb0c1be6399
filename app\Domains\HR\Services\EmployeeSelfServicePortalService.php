<?php

namespace App\Domains\HR\Services;

use App\Domains\HR\Models\Employee;
use App\Domains\HR\Models\LeaveRequest;
use App\Domains\HR\Models\Payslip;
use App\Domains\HR\Models\AttendanceRecord;
use App\Domains\HR\Models\PerformanceReview;
use App\Domains\HR\Models\TrainingProgram;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

/**
 * خدمة بوابة الموظف الذكية - Employee Self-Service Portal
 * بوابة شاملة للموظف مع دعم الجوال والذكاء الاصطناعي
 */
class EmployeeSelfServicePortalService
{
    /**
     * الحصول على لوحة تحكم الموظف
     */
    public function getEmployeeDashboard(Employee $employee): array
    {
        return [
            'employee_info' => $this->getEmployeeBasicInfo($employee),
            'quick_stats' => $this->getQuickStats($employee),
            'recent_activities' => $this->getRecentActivities($employee),
            'pending_actions' => $this->getPendingActions($employee),
            'upcoming_events' => $this->getUpcomingEvents($employee),
            'notifications' => $this->getNotifications($employee),
            'performance_summary' => $this->getPerformanceSummary($employee),
            'leave_summary' => $this->getLeaveSummary($employee),
            'training_progress' => $this->getTrainingProgress($employee),
        ];
    }

    /**
     * الحصول على المعلومات الأساسية للموظف
     */
    protected function getEmployeeBasicInfo(Employee $employee): array
    {
        return [
            'name' => $employee->full_name,
            'employee_number' => $employee->employee_number,
            'position' => $employee->position?->title,
            'department' => $employee->department?->name,
            'manager' => $employee->manager?->full_name,
            'hire_date' => $employee->hire_date,
            'years_of_service' => $employee->years_of_service,
            'profile_picture' => $employee->profile_picture,
            'contact_info' => [
                'email' => $employee->email,
                'phone' => $employee->phone,
                'mobile' => $employee->mobile,
            ],
        ];
    }

    /**
     * الحصول على الإحصائيات السريعة
     */
    protected function getQuickStats(Employee $employee): array
    {
        $currentMonth = now()->startOfMonth();
        $currentYear = now()->startOfYear();

        return [
            'attendance_this_month' => [
                'present_days' => AttendanceRecord::forEmployee($employee->id)
                    ->forPeriod($currentMonth, now())
                    ->where('status', 'PRESENT')
                    ->count(),
                'late_days' => AttendanceRecord::forEmployee($employee->id)
                    ->forPeriod($currentMonth, now())
                    ->where('is_late', true)
                    ->count(),
                'overtime_hours' => AttendanceRecord::forEmployee($employee->id)
                    ->forPeriod($currentMonth, now())
                    ->sum('overtime_hours'),
            ],
            'leave_balance' => $this->getLeaveBalanceSummary($employee),
            'pending_requests' => [
                'leave_requests' => LeaveRequest::forEmployee($employee->id)
                    ->pending()
                    ->count(),
                'expense_claims' => 0, // يمكن إضافة نموذج المصاريف لاحقاً
            ],
            'performance_score' => $this->getLatestPerformanceScore($employee),
            'training_completion' => $this->getTrainingCompletionRate($employee),
        ];
    }

    /**
     * الحصول على الأنشطة الحديثة
     */
    protected function getRecentActivities(Employee $employee): array
    {
        $activities = [];

        // آخر سجلات الحضور
        $recentAttendance = AttendanceRecord::forEmployee($employee->id)
            ->orderBy('date', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentAttendance as $record) {
            $activities[] = [
                'type' => 'attendance',
                'title' => 'تسجيل حضور',
                'description' => "حضور يوم {$record->date->format('Y-m-d')}",
                'date' => $record->date,
                'status' => $record->status,
                'icon' => 'clock',
            ];
        }

        // طلبات الإجازة الحديثة
        $recentLeaves = LeaveRequest::forEmployee($employee->id)
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentLeaves as $leave) {
            $activities[] = [
                'type' => 'leave',
                'title' => 'طلب إجازة',
                'description' => "إجازة {$leave->leaveType->name} من {$leave->start_date->format('Y-m-d')}",
                'date' => $leave->created_at,
                'status' => $leave->status,
                'icon' => 'calendar',
            ];
        }

        // ترتيب حسب التاريخ
        usort($activities, function ($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * الحصول على الإجراءات المعلقة
     */
    protected function getPendingActions(Employee $employee): array
    {
        $actions = [];

        // تقييمات الأداء المطلوبة
        $pendingReviews = PerformanceReview::forEmployee($employee->id)
            ->whereIn('status', ['SELF_ASSESSMENT', 'MANAGER_REVIEW'])
            ->get();

        foreach ($pendingReviews as $review) {
            $actions[] = [
                'type' => 'performance_review',
                'title' => 'تقييم أداء مطلوب',
                'description' => "تقييم {$review->review_type} - {$review->review_cycle}",
                'due_date' => $review->due_date,
                'priority' => 'HIGH',
                'url' => route('employee.performance.review', $review->id),
            ];
        }

        // تدريبات إلزامية
        $mandatoryTrainings = TrainingProgram::mandatory()
            ->whereDoesntHave('enrollments', function ($query) use ($employee) {
                $query->where('employee_id', $employee->id)
                      ->where('status', 'COMPLETED');
            })
            ->get();

        foreach ($mandatoryTrainings as $training) {
            $actions[] = [
                'type' => 'mandatory_training',
                'title' => 'تدريب إلزامي',
                'description' => $training->title,
                'due_date' => null,
                'priority' => 'MEDIUM',
                'url' => route('employee.training.enroll', $training->id),
            ];
        }

        // وثائق منتهية الصلاحية
        $expiringDocs = $employee->getExpiringDocuments();
        foreach ($expiringDocs as $doc) {
            $actions[] = [
                'type' => 'document_renewal',
                'title' => 'تجديد وثيقة',
                'description' => "تجديد {$doc['name']}",
                'due_date' => $doc['expiry_date'],
                'priority' => $doc['days_remaining'] <= 7 ? 'HIGH' : 'MEDIUM',
                'url' => route('employee.documents.update'),
            ];
        }

        return $actions;
    }

    /**
     * الحصول على الأحداث القادمة
     */
    protected function getUpcomingEvents(Employee $employee): array
    {
        $events = [];

        // إجازات معتمدة قادمة
        $upcomingLeaves = LeaveRequest::forEmployee($employee->id)
            ->approved()
            ->where('start_date', '>', now())
            ->orderBy('start_date')
            ->limit(5)
            ->get();

        foreach ($upcomingLeaves as $leave) {
            $events[] = [
                'type' => 'leave',
                'title' => "إجازة {$leave->leaveType->name}",
                'start_date' => $leave->start_date,
                'end_date' => $leave->end_date,
                'all_day' => true,
                'color' => '#f39c12',
            ];
        }

        // تدريبات مسجل بها
        $upcomingTrainings = $employee->trainings()
            ->whereHas('trainingProgram.sessions', function ($query) {
                $query->where('start_date', '>', now());
            })
            ->with('trainingProgram.sessions')
            ->get();

        foreach ($upcomingTrainings as $enrollment) {
            foreach ($enrollment->trainingProgram->sessions as $session) {
                if ($session->start_date > now()) {
                    $events[] = [
                        'type' => 'training',
                        'title' => $enrollment->trainingProgram->title,
                        'start_date' => $session->start_date,
                        'end_date' => $session->end_date,
                        'location' => $session->location,
                        'color' => '#3498db',
                    ];
                }
            }
        }

        return $events;
    }

    /**
     * طلب إجازة
     */
    public function requestLeave(Employee $employee, array $leaveData): LeaveRequest
    {
        DB::beginTransaction();
        try {
            $leaveRequest = LeaveRequest::create([
                'employee_id' => $employee->id,
                'leave_type_id' => $leaveData['leave_type_id'],
                'start_date' => $leaveData['start_date'],
                'end_date' => $leaveData['end_date'],
                'reason' => $leaveData['reason'],
                'emergency_contact' => $leaveData['emergency_contact'] ?? null,
                'emergency_phone' => $leaveData['emergency_phone'] ?? null,
                'replacement_employee_id' => $leaveData['replacement_employee_id'] ?? null,
                'handover_notes' => $leaveData['handover_notes'] ?? null,
                'requested_at' => now(),
                'status' => 'PENDING',
            ]);

            // حساب الأيام
            $leaveRequest->calculateDays();
            $leaveRequest->save();

            // إرسال إشعار للمدير
            if ($employee->manager) {
                $employee->manager->notify(new \App\Notifications\LeaveRequestSubmitted($leaveRequest));
            }

            DB::commit();
            return $leaveRequest;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * تحديث البيانات الشخصية
     */
    public function updatePersonalInfo(Employee $employee, array $data): bool
    {
        $allowedFields = [
            'phone', 'mobile', 'address_line_1', 'address_line_2',
            'city', 'postal_code', 'emergency_contact_name',
            'emergency_contact_relationship', 'emergency_contact_phone',
            'bank_name', 'bank_account_number', 'iban'
        ];

        $updateData = array_intersect_key($data, array_flip($allowedFields));
        
        return $employee->update($updateData);
    }

    /**
     * رفع صورة شخصية
     */
    public function uploadProfilePicture(Employee $employee, $file): string
    {
        $path = $file->store("employees/{$employee->id}/profile", 'public');
        
        $employee->update(['profile_picture' => $path]);
        
        return $path;
    }

    /**
     * الحصول على كشوف الرواتب
     */
    public function getPayslips(Employee $employee, int $year = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = Payslip::forEmployee($employee->id)
            ->approved()
            ->orderBy('pay_period_start', 'desc');

        if ($year) {
            $query->forTaxYear($year);
        }

        return $query->get();
    }

    /**
     * تحميل كشف راتب
     */
    public function downloadPayslip(Employee $employee, string $payslipId): ?string
    {
        $payslip = Payslip::where('id', $payslipId)
            ->where('employee_id', $employee->id)
            ->approved()
            ->first();

        if (!$payslip) {
            return null;
        }

        if (!$payslip->pdf_path) {
            $payslip->generatePDF();
        }

        return $payslip->pdf_path;
    }

    /**
     * طلب شهادة عمل
     */
    public function requestWorkCertificate(Employee $employee, array $data): array
    {
        $certificateService = app(\App\Domains\HR\Services\CertificateGenerationService::class);
        
        return $certificateService->generateWorkCertificate($employee, [
            'type' => $data['type'] ?? 'EMPLOYMENT',
            'language' => $data['language'] ?? 'ar',
            'purpose' => $data['purpose'] ?? 'عام',
            'include_salary' => $data['include_salary'] ?? false,
        ]);
    }

    /**
     * تقديم شكوى أو اقتراح
     */
    public function submitComplaint(Employee $employee, array $data): array
    {
        $complaint = [
            'id' => uniqid('COMP_'),
            'employee_id' => $employee->id,
            'type' => $data['type'], // COMPLAINT, SUGGESTION, FEEDBACK
            'category' => $data['category'],
            'subject' => $data['subject'],
            'description' => $data['description'],
            'priority' => $data['priority'] ?? 'NORMAL',
            'anonymous' => $data['anonymous'] ?? false,
            'submitted_at' => now(),
            'status' => 'SUBMITTED',
        ];

        // حفظ في قاعدة البيانات (يمكن إنشاء نموذج منفصل)
        // أو إرسال للنظام المناسب

        // إرسال إشعار لقسم الموارد البشرية
        $hrTeam = Employee::whereHas('position', function ($query) {
            $query->where('department_id', function ($subQuery) {
                $subQuery->select('id')
                    ->from('departments')
                    ->where('name', 'LIKE', '%HR%')
                    ->orWhere('name', 'LIKE', '%موارد بشرية%');
            });
        })->get();

        foreach ($hrTeam as $hrEmployee) {
            $hrEmployee->notify(new \App\Notifications\EmployeeComplaintSubmitted($complaint));
        }

        return $complaint;
    }

    /**
     * الحصول على ملخص رصيد الإجازات
     */
    protected function getLeaveBalanceSummary(Employee $employee): array
    {
        $leaveService = app(\App\Domains\HR\Services\LeaveManagementService::class);
        return $leaveService->getEmployeeLeaveBalance($employee);
    }

    /**
     * الحصول على آخر نقاط أداء
     */
    protected function getLatestPerformanceScore(Employee $employee): ?float
    {
        $latestReview = PerformanceReview::forEmployee($employee->id)
            ->where('status', 'COMPLETED')
            ->orderBy('completed_date', 'desc')
            ->first();

        return $latestReview?->overall_score;
    }

    /**
     * الحصول على معدل إكمال التدريب
     */
    protected function getTrainingCompletionRate(Employee $employee): float
    {
        $totalEnrollments = $employee->trainings()->count();
        $completedEnrollments = $employee->trainings()
            ->where('status', 'COMPLETED')
            ->count();

        return $totalEnrollments > 0 ? ($completedEnrollments / $totalEnrollments) * 100 : 0;
    }

    /**
     * الحصول على الإشعارات
     */
    protected function getNotifications(Employee $employee): array
    {
        return $employee->notifications()
            ->unread()
            ->limit(10)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * الحصول على ملخص الأداء
     */
    protected function getPerformanceSummary(Employee $employee): array
    {
        $reviews = PerformanceReview::forEmployee($employee->id)
            ->where('status', 'COMPLETED')
            ->orderBy('completed_date', 'desc')
            ->limit(3)
            ->get();

        return [
            'latest_score' => $reviews->first()?->overall_score,
            'trend' => $this->calculatePerformanceTrend($reviews),
            'reviews_count' => $reviews->count(),
            'last_review_date' => $reviews->first()?->completed_date,
        ];
    }

    /**
     * حساب اتجاه الأداء
     */
    protected function calculatePerformanceTrend(\Illuminate\Database\Eloquent\Collection $reviews): string
    {
        if ($reviews->count() < 2) {
            return 'STABLE';
        }

        $latest = $reviews->first()->overall_score;
        $previous = $reviews->skip(1)->first()->overall_score;

        if ($latest > $previous + 0.5) {
            return 'IMPROVING';
        } elseif ($latest < $previous - 0.5) {
            return 'DECLINING';
        }

        return 'STABLE';
    }

    /**
     * الحصول على ملخص الإجازات
     */
    protected function getLeaveSummary(Employee $employee): array
    {
        $currentYear = now()->year;
        
        return [
            'total_taken' => LeaveRequest::forEmployee($employee->id)
                ->approved()
                ->whereYear('start_date', $currentYear)
                ->sum('working_days'),
            'pending_requests' => LeaveRequest::forEmployee($employee->id)
                ->pending()
                ->count(),
            'upcoming_leaves' => LeaveRequest::forEmployee($employee->id)
                ->approved()
                ->where('start_date', '>', now())
                ->count(),
        ];
    }

    /**
     * الحصول على تقدم التدريب
     */
    protected function getTrainingProgress(Employee $employee): array
    {
        $enrollments = $employee->trainings()->with('trainingProgram')->get();
        
        return [
            'total_enrolled' => $enrollments->count(),
            'completed' => $enrollments->where('status', 'COMPLETED')->count(),
            'in_progress' => $enrollments->where('status', 'IN_PROGRESS')->count(),
            'completion_rate' => $this->getTrainingCompletionRate($employee),
            'certificates_earned' => $enrollments->where('status', 'COMPLETED')
                ->where('certificate_issued', true)->count(),
        ];
    }
}
