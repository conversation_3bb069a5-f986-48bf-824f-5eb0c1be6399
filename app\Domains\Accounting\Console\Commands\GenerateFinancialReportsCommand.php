<?php

namespace App\Domains\Accounting\Console\Commands;

use Illuminate\Console\Command;

class GenerateFinancialReportsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:generate-reports {--period=monthly}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate financial reports for the specified period';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating financial reports...');

        // Implementation will be added later

        $this->info('Financial reports generated successfully.');

        return self::SUCCESS;
    }
}
