<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceWebhook;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث معالجة Webhook
 * يتم إطلاقه عند اكتمال معالجة webhook بنجاح
 */
class WebhookProcessed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceWebhook $webhook;
    public ECommerceIntegration $integration;
    public array $processingResult;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        array $processingResult = []
    ) {
        $this->webhook = $webhook;
        $this->integration = $integration;
        $this->processingResult = $processingResult;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'webhook_id' => $this->webhook->id,
            'external_webhook_id' => $this->webhook->webhook_id,
            'event_type' => $this->webhook->event_type,
            'event_name' => $this->webhook->event_name,
            'topic' => $this->webhook->topic,
            'resource' => $this->webhook->resource,
            'resource_id' => $this->webhook->resource_id,
            'action' => $this->webhook->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'received_at' => $this->webhook->received_at,
            'processed_at' => $this->webhook->processed_at,
            'processing_time' => $this->webhook->processing_time,
            'total_time' => $this->webhook->total_time,
            'attempt_number' => $this->webhook->attempt_number,
            'processing_result' => $this->processingResult,
        ];
    }

    /**
     * الحصول على نتيجة المعالجة
     */
    public function getProcessingResult(): array
    {
        return $this->processingResult;
    }

    /**
     * تحديد ما إذا كانت المعالجة ناجحة
     */
    public function isSuccessful(): bool
    {
        return $this->webhook->isSuccessful();
    }

    /**
     * الحصول على وقت المعالجة
     */
    public function getProcessingTime(): int
    {
        return $this->webhook->getProcessingTime();
    }

    /**
     * الحصول على الوقت الإجمالي
     */
    public function getTotalTime(): int
    {
        return $this->webhook->getTotalTime();
    }

    /**
     * تحديد ما إذا كانت المعالجة سريعة
     */
    public function isFastProcessing(): bool
    {
        return $this->getProcessingTime() < 1000; // أقل من ثانية واحدة
    }

    /**
     * تحديد ما إذا كانت المعالجة بطيئة
     */
    public function isSlowProcessing(): bool
    {
        return $this->getProcessingTime() > 5000; // أكثر من 5 ثوان
    }
}
