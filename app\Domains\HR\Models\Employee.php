<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج الموظف - إدارة دورة حياة الموظف الكاملة
 * يدعم الأنظمة متعددة الجنسيات مع الامتثال القانوني
 */
class Employee extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable, LogsActivity;

    protected $fillable = [
        'employee_number',
        'user_id',
        'department_id',
        'position_id',
        'manager_id',
        'company_id',
        'branch_id',

        // Personal Information
        'first_name',
        'middle_name',
        'last_name',
        'first_name_ar',
        'middle_name_ar',
        'last_name_ar',
        'email',
        'phone',
        'mobile',
        'date_of_birth',
        'gender',
        'marital_status',
        'nationality',
        'national_id',
        'passport_number',
        'passport_expiry',
        'visa_number',
        'visa_expiry',
        'iqama_number',
        'iqama_expiry',

        // Address Information
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',

        // Employment Information
        'hire_date',
        'probation_end_date',
        'contract_type',
        'employment_type',
        'work_location',
        'status',
        'termination_date',
        'termination_reason',
        'rehire_eligible',

        // Salary Information
        'basic_salary',
        'currency',
        'salary_frequency',
        'bank_name',
        'bank_account_number',
        'iban',

        // Emergency Contact
        'emergency_contact_name',
        'emergency_contact_relationship',
        'emergency_contact_phone',

        // System Fields
        'profile_picture',
        'is_active',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'probation_end_date' => 'date',
        'passport_expiry' => 'date',
        'visa_expiry' => 'date',
        'iqama_expiry' => 'date',
        'termination_date' => 'date',
        'basic_salary' => 'decimal:2',
        'is_active' => 'boolean',
        'rehire_eligible' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * حالات الموظف
     */
    public const STATUSES = [
        'ACTIVE' => 'نشط',
        'PROBATION' => 'تحت التجربة',
        'SUSPENDED' => 'موقوف',
        'TERMINATED' => 'منتهي الخدمة',
        'RESIGNED' => 'مستقيل',
        'RETIRED' => 'متقاعد',
        'ON_LEAVE' => 'في إجازة',
    ];

    /**
     * أنواع العقود
     */
    public const CONTRACT_TYPES = [
        'PERMANENT' => 'دائم',
        'TEMPORARY' => 'مؤقت',
        'CONTRACT' => 'تعاقد',
        'PART_TIME' => 'دوام جزئي',
        'INTERNSHIP' => 'تدريب',
        'CONSULTANT' => 'استشاري',
    ];

    /**
     * أنواع التوظيف
     */
    public const EMPLOYMENT_TYPES = [
        'FULL_TIME' => 'دوام كامل',
        'PART_TIME' => 'دوام جزئي',
        'REMOTE' => 'عن بُعد',
        'HYBRID' => 'مختلط',
        'FREELANCE' => 'عمل حر',
    ];

    /**
     * الجنس
     */
    public const GENDERS = [
        'MALE' => 'ذكر',
        'FEMALE' => 'أنثى',
    ];

    /**
     * الحالة الاجتماعية
     */
    public const MARITAL_STATUSES = [
        'SINGLE' => 'أعزب',
        'MARRIED' => 'متزوج',
        'DIVORCED' => 'مطلق',
        'WIDOWED' => 'أرمل',
    ];

    /**
     * المستخدم المرتبط
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * القسم
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * المنصب
     */
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    /**
     * المدير المباشر
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    /**
     * المرؤوسين
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'manager_id');
    }

    /**
     * الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * الفرع
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * العقد الحالي
     */
    public function currentContract(): HasOne
    {
        return $this->hasOne(EmployeeContract::class)->where('is_active', true);
    }

    /**
     * جميع العقود
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(EmployeeContract::class);
    }

    /**
     * سجلات الحضور
     */
    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceRecord::class);
    }

    /**
     * طلبات الإجازة
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * كشوف الرواتب
     */
    public function payslips(): HasMany
    {
        return $this->hasMany(Payslip::class);
    }

    /**
     * تقييمات الأداء
     */
    public function performanceReviews(): HasMany
    {
        return $this->hasMany(PerformanceReview::class);
    }

    /**
     * التدريبات
     */
    public function trainings(): HasMany
    {
        return $this->hasMany(EmployeeTraining::class);
    }

    /**
     * المستندات
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(\App\Domains\Shared\Models\Document::class, 'documentable');
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->middle_name} {$this->last_name}");
    }

    /**
     * الحصول على الاسم الكامل بالعربية
     */
    public function getFullNameArAttribute(): string
    {
        return trim("{$this->first_name_ar} {$this->middle_name_ar} {$this->last_name_ar}");
    }

    /**
     * الحصول على العمر
     */
    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * الحصول على سنوات الخدمة
     */
    public function getYearsOfServiceAttribute(): float
    {
        if (!$this->hire_date) {
            return 0;
        }

        $endDate = $this->termination_date ?? now();
        return $this->hire_date->diffInYears($endDate, true);
    }

    /**
     * التحقق من انتهاء فترة التجربة
     */
    public function isProbationPeriodCompleted(): bool
    {
        return $this->probation_end_date && now() > $this->probation_end_date;
    }

    /**
     * التحقق من انتهاء صلاحية الوثائق
     */
    public function getExpiringDocuments(): array
    {
        $expiring = [];
        $warningDays = 30; // تحذير قبل 30 يوم

        $documents = [
            'passport' => ['date' => $this->passport_expiry, 'name' => 'جواز السفر'],
            'visa' => ['date' => $this->visa_expiry, 'name' => 'الفيزا'],
            'iqama' => ['date' => $this->iqama_expiry, 'name' => 'الإقامة'],
        ];

        foreach ($documents as $type => $doc) {
            if ($doc['date'] && $doc['date']->diffInDays(now()) <= $warningDays) {
                $expiring[] = [
                    'type' => $type,
                    'name' => $doc['name'],
                    'expiry_date' => $doc['date'],
                    'days_remaining' => $doc['date']->diffInDays(now()),
                ];
            }
        }

        return $expiring;
    }

    /**
     * حساب الراتب الإجمالي
     */
    public function calculateGrossSalary(): float
    {
        $payroll = app(\App\Domains\HR\Services\PayrollCalculationService::class);
        return $payroll->calculateGrossSalary($this);
    }

    /**
     * حساب الراتب الصافي
     */
    public function calculateNetSalary(): float
    {
        $payroll = app(\App\Domains\HR\Services\PayrollCalculationService::class);
        return $payroll->calculateNetSalary($this);
    }

    /**
     * الحصول على رصيد الإجازات
     */
    public function getLeaveBalance(string $leaveType = null): array
    {
        $leaveService = app(\App\Domains\HR\Services\LeaveManagementService::class);
        return $leaveService->getEmployeeLeaveBalance($this, $leaveType);
    }

    /**
     * إنهاء الخدمة
     */
    public function terminate(string $reason, \Carbon\Carbon $terminationDate = null): bool
    {
        $this->update([
            'status' => 'TERMINATED',
            'termination_date' => $terminationDate ?? now(),
            'termination_reason' => $reason,
            'is_active' => false,
        ]);

        // إنهاء العقد الحالي
        if ($this->currentContract) {
            $this->currentContract->update([
                'end_date' => $this->termination_date,
                'is_active' => false,
            ]);
        }

        // حساب مستحقات نهاية الخدمة
        $endOfServiceService = app(\App\Domains\HR\Services\EndOfServiceCalculationService::class);
        $endOfServiceService->calculateEndOfServiceBenefits($this);

        return true;
    }

    /**
     * إعادة تفعيل الموظف
     */
    public function reactivate(): bool
    {
        if (!$this->rehire_eligible) {
            return false;
        }

        $this->update([
            'status' => 'ACTIVE',
            'termination_date' => null,
            'termination_reason' => null,
            'is_active' => true,
        ]);

        return true;
    }

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'status', 'department_id', 'position_id', 'manager_id',
                'basic_salary', 'contract_type', 'employment_type'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * نطاق للموظفين النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'ACTIVE');
    }

    /**
     * نطاق حسب القسم
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * نطاق حسب المنصب
     */
    public function scopeInPosition($query, $positionId)
    {
        return $query->where('position_id', $positionId);
    }

    /**
     * نطاق للموظفين تحت التجربة
     */
    public function scopeOnProbation($query)
    {
        return $query->where('status', 'PROBATION')
            ->where('probation_end_date', '>', now());
    }

    /**
     * نطاق للموظفين المنتهية فترة تجربتهم
     */
    public function scopeProbationExpired($query)
    {
        return $query->where('status', 'PROBATION')
            ->where('probation_end_date', '<=', now());
    }

    /**
     * نطاق للوثائق المنتهية الصلاحية
     */
    public function scopeWithExpiringDocuments($query, int $days = 30)
    {
        return $query->where(function ($q) use ($days) {
            $q->where('passport_expiry', '<=', now()->addDays($days))
              ->orWhere('visa_expiry', '<=', now()->addDays($days))
              ->orWhere('iqama_expiry', '<=', now()->addDays($days));
        });
    }

    /**
     * نطاق حسب الجنسية
     */
    public function scopeByNationality($query, string $nationality)
    {
        return $query->where('nationality', $nationality);
    }

    /**
     * نطاق حسب نوع العقد
     */
    public function scopeByContractType($query, string $contractType)
    {
        return $query->where('contract_type', $contractType);
    }
}
