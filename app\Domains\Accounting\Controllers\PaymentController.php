<?php

namespace App\Domains\Accounting\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Routing\Controllers\HasMiddleware;
use App\Domains\Accounting\Models\Payment;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use App\Domains\Accounting\Requests\StorePaymentRequest;
use App\Domains\Accounting\Requests\UpdatePaymentRequest;
use App\Domains\Accounting\Resources\PaymentResource;
use App\Domains\Accounting\Resources\PaymentCollection;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * Payment Controller
 * تحكم في المدفوعات
 */
class PaymentController extends Controller implements HasMiddleware
{
    use AuthorizesRequests;

    protected AdvancedAccountingEngine $accountingEngine;

    public function __construct(AdvancedAccountingEngine $accountingEngine)
    {
        $this->accountingEngine = $accountingEngine;
    }

    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    /**
     * عرض قائمة المدفوعات
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Payment::class);

        $query = Payment::with(['payable', 'bankAccount', 'creator', 'approver'])
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('payment_number', 'like', "%{$search}%")
                          ->orWhere('reference', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            })
            ->when($request->payment_method, function ($q, $method) {
                $q->where('payment_method', $method);
            })
            ->when($request->date_from, function ($q, $dateFrom) {
                $q->where('payment_date', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($q, $dateTo) {
                $q->where('payment_date', '<=', $dateTo);
            })
            ->when($request->bank_account_id, function ($q, $bankAccountId) {
                $q->where('bank_account_id', $bankAccountId);
            });

        $payments = $query->orderBy('payment_date', 'desc')
                         ->orderBy('payment_number', 'desc')
                         ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new PaymentCollection($payments),
            'message' => 'تم جلب المدفوعات بنجاح'
        ]);
    }

    /**
     * إنشاء دفعة جديدة
     */
    public function store(StorePaymentRequest $request): JsonResponse
    {
        $this->authorize('create', Payment::class);

        DB::beginTransaction();
        try {
            $payment = Payment::create([
                ...$request->validated(),
                'payment_number' => $this->generatePaymentNumber(),
                'created_by' => Auth::id(),
                'status' => 'pending',
            ]);

            // إنشاء قيد المحاسبي
            if ($request->auto_post) {
                $this->createPaymentJournalEntry($payment);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new PaymentResource($payment->load(['payable', 'bankAccount'])),
                'message' => 'تم إنشاء الدفعة بنجاح'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل دفعة محددة
     */
    public function show(Payment $payment): JsonResponse
    {
        $this->authorize('view', $payment);

        $payment->load([
            'payable',
            'bankAccount',
            'journalEntry.lines.account',
            'creator',
            'updater',
            'approver'
        ]);

        return response()->json([
            'success' => true,
            'data' => new PaymentResource($payment),
            'message' => 'تم جلب تفاصيل الدفعة بنجاح'
        ]);
    }

    /**
     * تحديث دفعة
     */
    public function update(UpdatePaymentRequest $request, Payment $payment): JsonResponse
    {
        $this->authorize('update', $payment);

        if (in_array($payment->status, ['completed', 'cancelled'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل دفعة مكتملة أو ملغية'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $payment->update([
                ...$request->validated(),
                'updated_by' => Auth::id(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new PaymentResource($payment->load(['payable', 'bankAccount'])),
                'message' => 'تم تحديث الدفعة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف دفعة
     */
    public function destroy(Payment $payment): JsonResponse
    {
        $this->authorize('delete', $payment);

        if ($payment->status === 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف دفعة مكتملة'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // حذف القيد المحاسبي إن وجد
            if ($payment->journal_entry_id) {
                $payment->journalEntry->delete();
            }

            $payment->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الدفعة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اعتماد دفعة
     */
    public function approvePayment(Payment $payment): JsonResponse
    {
        $this->authorize('approve', $payment);

        if ($payment->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'يمكن اعتماد الدفعات المعلقة فقط'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $payment->update([
                'status' => 'approved',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
            ]);

            // إنشاء القيد المحاسبي إذا لم يكن موجوداً
            if (!$payment->journal_entry_id) {
                $this->createPaymentJournalEntry($payment);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new PaymentResource($payment->fresh(['payable', 'bankAccount', 'approver'])),
                'message' => 'تم اعتماد الدفعة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء اعتماد الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * رفض دفعة
     */
    public function rejectPayment(Request $request, Payment $payment): JsonResponse
    {
        $this->authorize('approve', $payment);

        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        if ($payment->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'يمكن رفض الدفعات المعلقة فقط'
            ], 422);
        }

        $payment->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => Auth::id(),
            'rejection_reason' => $request->rejection_reason,
        ]);

        return response()->json([
            'success' => true,
            'data' => new PaymentResource($payment->fresh()),
            'message' => 'تم رفض الدفعة بنجاح'
        ]);
    }

    /**
     * إلغاء دفعة
     */
    public function voidPayment(Request $request, Payment $payment): JsonResponse
    {
        $this->authorize('void', $payment);

        $request->validate([
            'void_reason' => 'required|string|max:500',
        ]);

        if (!in_array($payment->status, ['approved', 'completed'])) {
            return response()->json([
                'success' => false,
                'message' => 'يمكن إلغاء الدفعات المعتمدة أو المكتملة فقط'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // عكس القيد المحاسبي
            if ($payment->journal_entry_id) {
                $this->reversePaymentJournalEntry($payment, $request->void_reason);
            }

            $payment->update([
                'status' => 'voided',
                'voided_at' => now(),
                'voided_by' => Auth::id(),
                'void_reason' => $request->void_reason,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => new PaymentResource($payment->fresh()),
                'message' => 'تم إلغاء الدفعة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الدفعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على الدفعات المعلقة
     */
    public function getPendingPayments(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Payment::class);

        $payments = Payment::with(['payable', 'bankAccount', 'creator'])
            ->where('status', 'pending')
            ->orderBy('created_at')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new PaymentCollection($payments),
            'message' => 'تم جلب الدفعات المعلقة بنجاح'
        ]);
    }

    /**
     * الحصول على الدفعات حسب طريقة الدفع
     */
    public function getPaymentsByMethod(Request $request, string $method): JsonResponse
    {
        $this->authorize('viewAny', Payment::class);

        $payments = Payment::with(['payable', 'bankAccount'])
            ->where('payment_method', $method)
            ->when($request->date_from, function ($q, $dateFrom) {
                $q->where('payment_date', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($q, $dateTo) {
                $q->where('payment_date', '<=', $dateTo);
            })
            ->orderBy('payment_date', 'desc')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new PaymentCollection($payments),
            'message' => 'تم جلب الدفعات حسب طريقة الدفع بنجاح'
        ]);
    }

    /**
     * اعتماد دفعات متعددة
     */
    public function bulkApprovePayments(Request $request): JsonResponse
    {
        $this->authorize('approve', Payment::class);

        $request->validate([
            'payment_ids' => 'required|array',
            'payment_ids.*' => 'exists:payments,id',
        ]);

        $results = [];
        $successCount = 0;
        $failureCount = 0;

        DB::beginTransaction();
        try {
            foreach ($request->payment_ids as $paymentId) {
                $payment = Payment::find($paymentId);

                if ($payment->status === 'pending') {
                    $payment->update([
                        'status' => 'approved',
                        'approved_at' => now(),
                        'approved_by' => Auth::id(),
                    ]);

                    if (!$payment->journal_entry_id) {
                        $this->createPaymentJournalEntry($payment);
                    }

                    $results[] = [
                        'payment_id' => $paymentId,
                        'status' => 'approved',
                    ];
                    $successCount++;
                } else {
                    $results[] = [
                        'payment_id' => $paymentId,
                        'status' => 'skipped',
                        'reason' => 'الدفعة ليست في حالة معلقة',
                    ];
                    $failureCount++;
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($request->payment_ids),
                        'approved' => $successCount,
                        'skipped' => $failureCount,
                    ],
                ],
                'message' => "تم اعتماد {$successCount} دفعة بنجاح"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء الاعتماد المجمع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على بيانات التسوية
     */
    public function getReconciliationData(Request $request): JsonResponse
    {
        $this->authorize('reconcile', Payment::class);

        $bankAccountId = $request->bank_account_id;
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $query = Payment::with(['payable'])
            ->whereBetween('payment_date', [$dateFrom, $dateTo])
            ->where('status', 'completed');

        if ($bankAccountId) {
            $query->where('bank_account_id', $bankAccountId);
        }

        $payments = $query->orderBy('payment_date')->get();

        $reconciliationData = [
            'period' => ['from' => $dateFrom, 'to' => $dateTo],
            'bank_account_id' => $bankAccountId,
            'payments' => PaymentResource::collection($payments),
            'summary' => [
                'total_payments' => $payments->count(),
                'total_amount' => $payments->sum('amount'),
                'reconciled_count' => $payments->where('is_reconciled', true)->count(),
                'unreconciled_count' => $payments->where('is_reconciled', false)->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $reconciliationData,
            'message' => 'تم جلب بيانات التسوية بنجاح'
        ]);
    }

    /**
     * مطابقة تلقائية للدفعات
     */
    public function autoMatchPayments(Request $request): JsonResponse
    {
        $this->authorize('reconcile', Payment::class);

        $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        try {
            $matchedPayments = $this->accountingEngine->autoMatchPayments(
                $request->bank_account_id,
                $request->date_from,
                $request->date_to
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'matched_count' => count($matchedPayments),
                    'matched_payments' => PaymentResource::collection($matchedPayments),
                ],
                'message' => 'تم إجراء المطابقة التلقائية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء المطابقة التلقائية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء قيد محاسبي للدفعة
     */
    protected function createPaymentJournalEntry(Payment $payment): void
    {
        $entryData = [
            'entry_date' => $payment->payment_date,
            'description' => "دفعة رقم: {$payment->payment_number}",
            'reference' => $payment->reference,
            'source_type' => 'PAYMENT',
            'source_id' => $payment->id,
            'lines' => [
                [
                    'account_id' => $this->getPaymentAccount($payment),
                    'debit_amount' => $payment->amount,
                    'credit_amount' => 0,
                    'description' => $payment->description,
                ],
                [
                    'account_id' => $payment->bankAccount->account_id,
                    'debit_amount' => 0,
                    'credit_amount' => $payment->amount,
                    'description' => 'دفعة من: ' . $payment->bankAccount->account_name,
                ],
            ],
        ];

        $journalEntry = $this->accountingEngine->createJournalEntry($entryData);

        $payment->update(['journal_entry_id' => $journalEntry->id]);
    }

    /**
     * عكس قيد محاسبي للدفعة
     */
    protected function reversePaymentJournalEntry(Payment $payment, string $reason): void
    {
        if ($payment->journalEntry) {
            $this->accountingEngine->reverseJournalEntry($payment->journalEntry, $reason);
        }
    }

    /**
     * الحصول على حساب الدفعة
     */
    protected function getPaymentAccount(Payment $payment): int
    {
        // تحديد الحساب بناءً على نوع الدفعة
        if ($payment->payable_type === 'App\Domains\Accounting\Models\Invoice') {
            return $this->getAccountsReceivableAccount();
        } elseif ($payment->payable_type === 'App\Domains\Accounting\Models\Bill') {
            return $this->getAccountsPayableAccount();
        }

        return $this->getGeneralPaymentAccount();
    }

    /**
     * إنشاء رقم دفعة
     */
    protected function generatePaymentNumber(): string
    {
        $lastPayment = Payment::whereYear('created_at', now()->year)
                             ->orderBy('id', 'desc')
                             ->first();

        $sequence = $lastPayment ? (int) substr($lastPayment->payment_number, -6) + 1 : 1;

        return 'PAY-' . now()->year . '-' . str_pad($sequence, 6, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على حساب الحسابات المدينة
     */
    protected function getAccountsReceivableAccount(): int
    {
        // يجب تحديد حساب الحسابات المدينة من الإعدادات
        return 1; // مؤقت
    }

    /**
     * الحصول على حساب الحسابات الدائنة
     */
    protected function getAccountsPayableAccount(): int
    {
        // يجب تحديد حساب الحسابات الدائنة من الإعدادات
        return 2; // مؤقت
    }

    /**
     * الحصول على حساب الدفعات العام
     */
    protected function getGeneralPaymentAccount(): int
    {
        // يجب تحديد حساب الدفعات العام من الإعدادات
        return 3; // مؤقت
    }
}
