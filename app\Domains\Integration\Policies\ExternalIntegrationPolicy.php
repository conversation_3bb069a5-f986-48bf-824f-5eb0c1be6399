<?php

namespace App\Domains\Integration\Policies;

use App\Domains\HR\Models\Employee;
use App\Domains\Integration\Models\ExternalIntegration;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * External Integration Policy
 * 
 * Defines authorization rules for External Integration operations
 */
class ExternalIntegrationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any integrations.
     */
    public function viewAny(Employee $user): bool
    {
        return $user->hasPermissionTo('view_external_integrations') || 
               $user->hasRole(['admin', 'integration_manager', 'developer']);
    }

    /**
     * Determine whether the user can view the integration.
     */
    public function view(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can view all integrations
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view integrations in their company
        if ($integration->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_external_integrations');
        }

        // Developers can view integrations they have access to
        if ($user->hasRole('developer')) {
            return $this->hasIntegrationAccess($user, $integration);
        }

        return false;
    }

    /**
     * Determine whether the user can create integrations.
     */
    public function create(Employee $user): bool
    {
        return $user->hasPermissionTo('create_external_integrations') || 
               $user->hasRole(['admin', 'integration_manager']);
    }

    /**
     * Determine whether the user can update the integration.
     */
    public function update(Employee $user, ExternalIntegration $integration): bool
    {
        // Prevent updating system integrations
        if ($integration->is_system_integration && !$user->hasRole('admin')) {
            return false;
        }

        // Admins can update all integrations
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can update integrations in their company
        if ($user->hasRole('integration_manager') && $integration->company_id === $user->company_id) {
            return true;
        }

        // Users with specific permission can update integrations they own
        if ($integration->created_by === $user->id) {
            return $user->hasPermissionTo('update_external_integrations');
        }

        return false;
    }

    /**
     * Determine whether the user can delete the integration.
     */
    public function delete(Employee $user, ExternalIntegration $integration): bool
    {
        // Prevent deleting system integrations
        if ($integration->is_system_integration) {
            return false;
        }

        // Prevent deleting integrations with active syncs
        if ($integration->hasActiveSyncs()) {
            return false;
        }

        // Only admins and integration managers can delete integrations
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can activate/deactivate the integration.
     */
    public function toggleStatus(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage integration configuration.
     */
    public function manageConfiguration(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins can manage all configurations
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage configurations in their company
        if ($user->hasRole('integration_manager') && $integration->company_id === $user->company_id) {
            return true;
        }

        // Integration owners can manage basic configurations
        if ($integration->created_by === $user->id) {
            return $user->hasPermissionTo('manage_integration_config');
        }

        return false;
    }

    /**
     * Determine whether the user can manage integration credentials.
     */
    public function manageCredentials(Employee $user, ExternalIntegration $integration): bool
    {
        // Only admins and integration managers can manage credentials
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can test the integration connection.
     */
    public function testConnection(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager', 'developer']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can sync integration data.
     */
    public function syncData(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can sync all integrations
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return $integration->company_id === $user->company_id;
        }

        // Users with sync permission can sync integrations they have access to
        if ($user->hasPermissionTo('sync_external_integrations')) {
            return $this->hasIntegrationAccess($user, $integration);
        }

        return false;
    }

    /**
     * Determine whether the user can schedule sync operations.
     */
    public function scheduleSync(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can view integration logs.
     */
    public function viewLogs(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can view all logs
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view logs for integrations in their company
        if ($integration->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_integration_logs');
        }

        return false;
    }

    /**
     * Determine whether the user can view integration statistics.
     */
    public function viewStatistics(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can view all statistics
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view statistics for integrations in their company
        if ($integration->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_integration_statistics');
        }

        return false;
    }

    /**
     * Determine whether the user can manage integration webhooks.
     */
    public function manageWebhooks(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins can manage all webhooks
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage webhooks in their company
        if ($user->hasRole('integration_manager') && $integration->company_id === $user->company_id) {
            return true;
        }

        // Integration owners can manage webhooks
        if ($integration->created_by === $user->id) {
            return $user->hasPermissionTo('manage_webhooks');
        }

        return false;
    }

    /**
     * Determine whether the user can manage field mappings.
     */
    public function manageFieldMappings(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins can manage all field mappings
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage field mappings in their company
        if ($user->hasRole('integration_manager') && $integration->company_id === $user->company_id) {
            return true;
        }

        // Developers with access can manage field mappings
        if ($user->hasRole('developer') && $this->hasIntegrationAccess($user, $integration)) {
            return $user->hasPermissionTo('manage_field_mappings');
        }

        return false;
    }

    /**
     * Determine whether the user can manage data transformations.
     */
    public function manageTransformations(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins can manage all transformations
        if ($user->hasRole('admin')) {
            return true;
        }

        // Integration managers can manage transformations in their company
        if ($user->hasRole('integration_manager') && $integration->company_id === $user->company_id) {
            return true;
        }

        // Developers with access can manage transformations
        if ($user->hasRole('developer') && $this->hasIntegrationAccess($user, $integration)) {
            return $user->hasPermissionTo('manage_transformations');
        }

        return false;
    }

    /**
     * Determine whether the user can import/export data.
     */
    public function importExportData(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can restart the integration.
     */
    public function restart(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can perform health checks.
     */
    public function performHealthCheck(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager', 'developer']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can view sync history.
     */
    public function viewSyncHistory(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can view all sync history
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view sync history for integrations in their company
        if ($integration->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_sync_history');
        }

        return false;
    }

    /**
     * Determine whether the user can cancel sync operations.
     */
    public function cancelSync(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can manage integration monitoring.
     */
    public function manageMonitoring(Employee $user, ExternalIntegration $integration): bool
    {
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Check if user has access to specific integration
     */
    protected function hasIntegrationAccess(Employee $user, ExternalIntegration $integration): bool
    {
        // Check if user is assigned to integration
        return $integration->assignedUsers()->where('user_id', $user->id)->exists() ||
               $integration->created_by === $user->id;
    }

    /**
     * Determine whether the user can manage integration security.
     */
    public function manageSecurity(Employee $user, ExternalIntegration $integration): bool
    {
        // Only admins and integration managers can manage security
        return $user->hasRole(['admin', 'integration_manager']) && 
               $integration->company_id === $user->company_id;
    }

    /**
     * Determine whether the user can view integration errors.
     */
    public function viewErrors(Employee $user, ExternalIntegration $integration): bool
    {
        // Admins and integration managers can view all errors
        if ($user->hasRole(['admin', 'integration_manager'])) {
            return true;
        }

        // Users can view errors for integrations in their company
        if ($integration->company_id === $user->company_id) {
            return $user->hasPermissionTo('view_integration_errors');
        }

        return false;
    }
}
