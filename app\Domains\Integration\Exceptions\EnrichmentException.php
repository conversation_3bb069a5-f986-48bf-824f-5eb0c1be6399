<?php

namespace App\Domains\Integration\Exceptions;

use Exception;

/**
 * Enrichment Exception
 * Thrown when data enrichment fails
 */
class EnrichmentException extends Exception
{
    protected array $context;
    protected string $enricherName;

    public function __construct(
        string $message = 'Data enrichment failed',
        string $enricherName = '',
        array $context = [],
        int $code = 0,
        Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->enricherName = $enricherName;
        $this->context = $context;
    }

    /**
     * Get enricher name that failed
     */
    public function getEnricherName(): string
    {
        return $this->enricherName;
    }

    /**
     * Get enrichment context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'enricher' => $this->enricherName,
            'context' => $this->context,
            'code' => $this->getCode(),
        ];
    }
}
