<?php

namespace App\Domains\Accounting\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Store Invoice Request
 * طلب إنشاء فاتورة جديدة
 */
class StoreInvoiceRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Domains\Accounting\Models\Invoice::class);
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            'customer_id' => [
                'required',
                'integer',
                'exists:customers,id',
            ],
            'invoice_date' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'due_date' => [
                'required',
                'date',
                'after_or_equal:invoice_date',
            ],
            'reference_number' => [
                'nullable',
                'string',
                'max:100',
                'unique:invoices,reference_number',
            ],
            'po_number' => [
                'nullable',
                'string',
                'max:100',
            ],
            'currency' => [
                'required',
                'string',
                'size:3',
                Rule::in(['SAR', 'USD', 'EUR', 'GBP', 'AED', 'EGP']),
            ],
            'exchange_rate' => [
                'nullable',
                'numeric',
                'min:0.01',
            ],
            'payment_terms' => [
                'nullable',
                'string',
                'max:255',
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'terms_conditions' => [
                'nullable',
                'string',
                'max:2000',
            ],
            'discount_type' => [
                'nullable',
                Rule::in(['percentage', 'fixed']),
            ],
            'discount_value' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'tax_inclusive' => [
                'boolean',
            ],
            'is_recurring' => [
                'boolean',
            ],
            'recurring_frequency' => [
                'nullable',
                'required_if:is_recurring,true',
                Rule::in(['weekly', 'monthly', 'quarterly', 'yearly']),
            ],
            'recurring_end_date' => [
                'nullable',
                'required_if:is_recurring,true',
                'date',
                'after:due_date',
            ],
            'project_id' => [
                'nullable',
                'integer',
                'exists:projects,id',
            ],
            'cost_center_id' => [
                'nullable',
                'integer',
                'exists:cost_centers,id',
            ],
            
            // بنود الفاتورة
            'items' => [
                'required',
                'array',
                'min:1',
            ],
            'items.*.product_id' => [
                'nullable',
                'integer',
                'exists:products,id',
            ],
            'items.*.description' => [
                'required',
                'string',
                'max:500',
            ],
            'items.*.quantity' => [
                'required',
                'numeric',
                'min:0.01',
            ],
            'items.*.unit_price' => [
                'required',
                'numeric',
                'min:0',
            ],
            'items.*.discount_percentage' => [
                'nullable',
                'numeric',
                'min:0',
                'max:100',
            ],
            'items.*.tax_rate' => [
                'nullable',
                'numeric',
                'min:0',
                'max:100',
            ],
            'items.*.account_id' => [
                'nullable',
                'integer',
                'exists:accounts,id',
            ],
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'customer_id.required' => 'العميل مطلوب',
            'customer_id.exists' => 'العميل غير موجود',
            
            'invoice_date.required' => 'تاريخ الفاتورة مطلوب',
            'invoice_date.date' => 'تاريخ الفاتورة غير صحيح',
            'invoice_date.before_or_equal' => 'تاريخ الفاتورة لا يمكن أن يكون في المستقبل',
            
            'due_date.required' => 'تاريخ الاستحقاق مطلوب',
            'due_date.date' => 'تاريخ الاستحقاق غير صحيح',
            'due_date.after_or_equal' => 'تاريخ الاستحقاق يجب أن يكون بعد أو يساوي تاريخ الفاتورة',
            
            'reference_number.unique' => 'رقم المرجع موجود مسبقاً',
            'reference_number.max' => 'رقم المرجع يجب ألا يزيد عن 100 حرف',
            
            'po_number.max' => 'رقم أمر الشراء يجب ألا يزيد عن 100 حرف',
            
            'currency.required' => 'العملة مطلوبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',
            'currency.in' => 'العملة غير مدعومة',
            
            'exchange_rate.numeric' => 'سعر الصرف يجب أن يكون رقم',
            'exchange_rate.min' => 'سعر الصرف يجب أن يكون أكبر من صفر',
            
            'payment_terms.max' => 'شروط الدفع يجب ألا تزيد عن 255 حرف',
            'notes.max' => 'الملاحظات يجب ألا تزيد عن 1000 حرف',
            'terms_conditions.max' => 'الشروط والأحكام يجب ألا تزيد عن 2000 حرف',
            
            'discount_type.in' => 'نوع الخصم غير صحيح',
            'discount_value.numeric' => 'قيمة الخصم يجب أن تكون رقم',
            'discount_value.min' => 'قيمة الخصم يجب أن تكون موجبة أو صفر',
            
            'recurring_frequency.required_if' => 'تكرار الفاتورة مطلوب عند تفعيل الفواتير المتكررة',
            'recurring_frequency.in' => 'تكرار الفاتورة غير صحيح',
            
            'recurring_end_date.required_if' => 'تاريخ انتهاء التكرار مطلوب عند تفعيل الفواتير المتكررة',
            'recurring_end_date.after' => 'تاريخ انتهاء التكرار يجب أن يكون بعد تاريخ الاستحقاق',
            
            'project_id.exists' => 'المشروع غير موجود',
            'cost_center_id.exists' => 'مركز التكلفة غير موجود',
            
            'items.required' => 'بنود الفاتورة مطلوبة',
            'items.array' => 'بنود الفاتورة يجب أن تكون مصفوفة',
            'items.min' => 'يجب إضافة بند واحد على الأقل',
            
            'items.*.product_id.exists' => 'المنتج غير موجود',
            'items.*.description.required' => 'وصف البند مطلوب',
            'items.*.description.max' => 'وصف البند يجب ألا يزيد عن 500 حرف',
            
            'items.*.quantity.required' => 'الكمية مطلوبة',
            'items.*.quantity.numeric' => 'الكمية يجب أن تكون رقم',
            'items.*.quantity.min' => 'الكمية يجب أن تكون أكبر من صفر',
            
            'items.*.unit_price.required' => 'سعر الوحدة مطلوب',
            'items.*.unit_price.numeric' => 'سعر الوحدة يجب أن يكون رقم',
            'items.*.unit_price.min' => 'سعر الوحدة يجب أن يكون موجب أو صفر',
            
            'items.*.discount_percentage.numeric' => 'نسبة الخصم يجب أن تكون رقم',
            'items.*.discount_percentage.min' => 'نسبة الخصم يجب أن تكون موجبة أو صفر',
            'items.*.discount_percentage.max' => 'نسبة الخصم يجب ألا تزيد عن 100%',
            
            'items.*.tax_rate.numeric' => 'معدل الضريبة يجب أن يكون رقم',
            'items.*.tax_rate.min' => 'معدل الضريبة يجب أن يكون موجب أو صفر',
            'items.*.tax_rate.max' => 'معدل الضريبة يجب ألا يزيد عن 100%',
            
            'items.*.account_id.exists' => 'الحساب غير موجود',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'customer_id' => 'العميل',
            'invoice_date' => 'تاريخ الفاتورة',
            'due_date' => 'تاريخ الاستحقاق',
            'reference_number' => 'رقم المرجع',
            'po_number' => 'رقم أمر الشراء',
            'currency' => 'العملة',
            'exchange_rate' => 'سعر الصرف',
            'payment_terms' => 'شروط الدفع',
            'notes' => 'الملاحظات',
            'terms_conditions' => 'الشروط والأحكام',
            'discount_type' => 'نوع الخصم',
            'discount_value' => 'قيمة الخصم',
            'tax_inclusive' => 'شامل الضريبة',
            'is_recurring' => 'فاتورة متكررة',
            'recurring_frequency' => 'تكرار الفاتورة',
            'recurring_end_date' => 'تاريخ انتهاء التكرار',
            'project_id' => 'المشروع',
            'cost_center_id' => 'مركز التكلفة',
            'items' => 'بنود الفاتورة',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'tax_inclusive' => $this->boolean('tax_inclusive', false),
            'is_recurring' => $this->boolean('is_recurring', false),
            'currency' => $this->currency ?? 'SAR',
            'exchange_rate' => $this->exchange_rate ?? 1.0,
        ]);

        // تنظيف بيانات البنود
        if ($this->has('items')) {
            $items = collect($this->items)->map(function ($item) {
                return array_merge($item, [
                    'quantity' => (float) ($item['quantity'] ?? 0),
                    'unit_price' => (float) ($item['unit_price'] ?? 0),
                    'discount_percentage' => (float) ($item['discount_percentage'] ?? 0),
                    'tax_rate' => (float) ($item['tax_rate'] ?? 0),
                ]);
            })->toArray();

            $this->merge(['items' => $items]);
        }
    }

    /**
     * التحقق من صحة البيانات بعد التحقق الأساسي
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من أن إجمالي الفاتورة أكبر من صفر
            if ($this->has('items')) {
                $total = 0;
                foreach ($this->items as $item) {
                    $lineTotal = ($item['quantity'] ?? 0) * ($item['unit_price'] ?? 0);
                    $lineTotal -= $lineTotal * (($item['discount_percentage'] ?? 0) / 100);
                    $total += $lineTotal;
                }

                if ($total <= 0) {
                    $validator->errors()->add('items', 'إجمالي الفاتورة يجب أن يكون أكبر من صفر');
                }
            }

            // التحقق من صحة الخصم
            if ($this->discount_type === 'percentage' && $this->discount_value > 100) {
                $validator->errors()->add('discount_value', 'نسبة الخصم يجب ألا تزيد عن 100%');
            }

            // التحقق من سعر الصرف للعملات الأجنبية
            if ($this->currency !== 'SAR' && !$this->exchange_rate) {
                $validator->errors()->add('exchange_rate', 'سعر الصرف مطلوب للعملات الأجنبية');
            }

            // التحقق من تواريخ الفواتير المتكررة
            if ($this->is_recurring) {
                if (!$this->recurring_frequency) {
                    $validator->errors()->add('recurring_frequency', 'تكرار الفاتورة مطلوب');
                }
                if (!$this->recurring_end_date) {
                    $validator->errors()->add('recurring_end_date', 'تاريخ انتهاء التكرار مطلوب');
                }
            }
        });
    }
}
