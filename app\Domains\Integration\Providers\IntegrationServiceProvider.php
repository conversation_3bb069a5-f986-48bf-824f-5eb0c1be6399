<?php

namespace App\Domains\Integration\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use App\Domains\Integration\Models\ApiGateway;
use App\Domains\Integration\Models\ExternalIntegration;
use App\Domains\Integration\Models\ApiKey;
use App\Domains\Integration\Policies\ApiGatewayPolicy;
use App\Domains\Integration\Policies\ExternalIntegrationPolicy;
use App\Domains\Integration\Policies\ApiKeyPolicy;
use App\Domains\Integration\Events\RequestProcessed;
use App\Domains\Integration\Events\SecurityThreatDetected;
use App\Domains\Integration\Events\PerformanceThresholdExceeded;
use App\Domains\Integration\Events\SystemHealthDegraded;
use App\Domains\Integration\Listeners\RequestProcessedListener;
use App\Domains\Integration\Listeners\SecurityThreatListener;
use App\Domains\Integration\Listeners\PerformanceThresholdListener;
use App\Domains\Integration\Listeners\SystemHealthListener;
use App\Domains\Integration\Services\ApiGatewayService;
use App\Domains\Integration\Services\ExternalIntegrationService;
use App\Domains\Integration\Services\McpService;
use App\Domains\Integration\Services\LoadBalancer\AdvancedLoadBalancer;
use App\Domains\Integration\Services\Security\AdvancedSecurityManager;
use App\Domains\Integration\Services\Monitoring\RealTimeMonitor;
use App\Domains\Integration\Services\Analytics\AdvancedAnalytics;
use App\Domains\Integration\Services\Transformation\DataTransformationPipeline;
use App\Domains\Integration\Services\Deployment\BlueGreenDeployment;
use App\Domains\Integration\Services\Deployment\CanaryDeployment;
use App\Domains\Integration\Services\AutoScaling\AutoScalingManager;
use App\Domains\Integration\Services\CircuitBreaker\CircuitBreakerManager;
use App\Domains\Integration\Services\RateLimiter\AdvancedRateLimiter;

/**
 * Integration Service Provider
 *
 * Registers all integration domain services, policies, events, and listeners
 */
class IntegrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register core services
        $this->registerCoreServices();

        // Register advanced services
        $this->registerAdvancedServices();

        // Register monitoring services
        $this->registerMonitoringServices();

        // Register deployment services
        $this->registerDeploymentServices();

        // Register configuration
        $this->registerConfiguration();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register policies
        $this->registerPolicies();

        // Register event listeners
        $this->registerEventListeners();

        // Register queue configurations
        $this->registerQueueConfigurations();

        // Register middleware
        $this->registerMiddleware();

        // Register commands
        $this->registerCommands();

        // Load routes
        $this->loadRoutes();

        // Load migrations
        $this->loadMigrations();

        // Publish assets
        $this->publishAssets();
    }

    /**
     * Register core services
     */
    protected function registerCoreServices(): void
    {
        $this->app->singleton(ApiGatewayService::class, function ($app) {
            return new ApiGatewayService(
                $app->make(AdvancedLoadBalancer::class),
                $app->make(AdvancedSecurityManager::class),
                $app->make(DataTransformationPipeline::class),
                $app->make(RealTimeMonitor::class),
                $app->make(CircuitBreakerManager::class),
                $app->make(AdvancedRateLimiter::class)
            );
        });

        $this->app->singleton(ExternalIntegrationService::class, function ($app) {
            return new ExternalIntegrationService(
                $app->make(DataTransformationPipeline::class),
                $app->make(RealTimeMonitor::class),
                $app->make(AdvancedSecurityManager::class)
            );
        });

        $this->app->singleton(McpService::class, function ($app) {
            return new McpService(
                $app->make(AdvancedSecurityManager::class),
                $app->make(RealTimeMonitor::class)
            );
        });
    }

    /**
     * Register advanced services
     */
    protected function registerAdvancedServices(): void
    {
        $this->app->singleton(AdvancedLoadBalancer::class, function ($app) {
            return new AdvancedLoadBalancer(
                $app->make(RealTimeMonitor::class),
                config('integration.load_balancer', [])
            );
        });

        $this->app->singleton(AdvancedSecurityManager::class, function ($app) {
            return new AdvancedSecurityManager(
                config('integration.security', [])
            );
        });

        $this->app->singleton(DataTransformationPipeline::class, function ($app) {
            return new DataTransformationPipeline(
                config('integration.transformation', [])
            );
        });

        $this->app->singleton(CircuitBreakerManager::class, function ($app) {
            return new CircuitBreakerManager(
                config('integration.circuit_breaker', [])
            );
        });

        $this->app->singleton(AdvancedRateLimiter::class, function ($app) {
            return new AdvancedRateLimiter(
                config('integration.rate_limiter', [])
            );
        });

        $this->app->singleton(AutoScalingManager::class, function ($app) {
            return new AutoScalingManager(
                $app->make(RealTimeMonitor::class),
                config('integration.auto_scaling', [])
            );
        });
    }

    /**
     * Register monitoring services
     */
    protected function registerMonitoringServices(): void
    {
        $this->app->singleton(RealTimeMonitor::class, function ($app) {
            return new RealTimeMonitor(
                config('integration.monitoring', [])
            );
        });

        $this->app->singleton(AdvancedAnalytics::class, function ($app) {
            return new AdvancedAnalytics(
                $app->make(\App\Domains\Integration\Services\Analytics\Collectors\DataCollector::class),
                $app->make(\App\Domains\Integration\Services\Analytics\Processors\MetricsProcessor::class),
                $app->make(\App\Domains\Integration\Services\Analytics\Predictors\TrendPredictor::class)
            );
        });
    }

    /**
     * Register deployment services
     */
    protected function registerDeploymentServices(): void
    {
        $this->app->singleton(BlueGreenDeployment::class, function ($app) {
            return new BlueGreenDeployment(
                $app->make(RealTimeMonitor::class),
                $app->make(AdvancedLoadBalancer::class)
            );
        });

        $this->app->singleton(CanaryDeployment::class, function ($app) {
            return new CanaryDeployment(
                $app->make(RealTimeMonitor::class),
                $app->make(AdvancedLoadBalancer::class)
            );
        });
    }

    /**
     * Register configuration
     */
    protected function registerConfiguration(): void
    {
        $this->mergeConfigFrom(
            __DIR__ . '/../../../config/integration.php',
            'integration'
        );
    }

    /**
     * Register policies
     */
    protected function registerPolicies(): void
    {
        Gate::policy(ApiGateway::class, ApiGatewayPolicy::class);
        Gate::policy(ExternalIntegration::class, ExternalIntegrationPolicy::class);
        Gate::policy(ApiKey::class, ApiKeyPolicy::class);
    }

    /**
     * Register event listeners
     */
    protected function registerEventListeners(): void
    {
        Event::listen(RequestProcessed::class, RequestProcessedListener::class);
        Event::listen(SecurityThreatDetected::class, SecurityThreatListener::class);
        Event::listen(PerformanceThresholdExceeded::class, PerformanceThresholdListener::class);
        Event::listen(SystemHealthDegraded::class, SystemHealthListener::class);
    }

    /**
     * Register queue configurations
     */
    protected function registerQueueConfigurations(): void
    {
        Queue::before(function ($event) {
            // Log queue job start
            if (method_exists($event->job, 'resolveName')) {
                $jobName = $event->job->resolveName();
                if (str_contains($jobName, 'Integration\\Jobs\\')) {
                    logger()->info('Integration job started', [
                        'job' => $jobName,
                        'queue' => $event->job->getQueue(),
                        'attempts' => $event->job->attempts(),
                    ]);
                }
            }
        });

        Queue::after(function ($event) {
            // Log queue job completion
            if (method_exists($event->job, 'resolveName')) {
                $jobName = $event->job->resolveName();
                if (str_contains($jobName, 'Integration\\Jobs\\')) {
                    logger()->info('Integration job completed', [
                        'job' => $jobName,
                        'queue' => $event->job->getQueue(),
                    ]);
                }
            }
        });

        Queue::failing(function ($event) {
            // Log queue job failure
            if (method_exists($event->job, 'resolveName')) {
                $jobName = $event->job->resolveName();
                if (str_contains($jobName, 'Integration\\Jobs\\')) {
                    logger()->error('Integration job failed', [
                        'job' => $jobName,
                        'queue' => $event->job->getQueue(),
                        'exception' => $event->exception->getMessage(),
                    ]);
                }
            }
        });
    }

    /**
     * Register middleware
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];

        // Register integration-specific middleware
        $router->aliasMiddleware('integration.auth', \App\Domains\Integration\Middleware\IntegrationAuth::class);
        $router->aliasMiddleware('integration.rate_limit', \App\Domains\Integration\Middleware\IntegrationRateLimit::class);
        $router->aliasMiddleware('integration.security', \App\Domains\Integration\Middleware\IntegrationSecurity::class);
        $router->aliasMiddleware('mcp.auth', \App\Domains\Integration\Middleware\McpAuth::class);
    }

    /**
     * Register commands
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Domains\Integration\Commands\SyncExternalIntegrations::class,
                \App\Domains\Integration\Commands\MonitorGatewayHealth::class,
                \App\Domains\Integration\Commands\CleanupIntegrationLogs::class,
                \App\Domains\Integration\Commands\GenerateIntegrationReport::class,
                \App\Domains\Integration\Commands\TestIntegrationConnections::class,
            ]);
        }
    }

    /**
     * Load routes
     */
    protected function loadRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');
    }

    /**
     * Load migrations
     */
    protected function loadMigrations(): void
    {
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations');
    }

    /**
     * Publish assets
     */
    protected function publishAssets(): void
    {
        if ($this->app->runningInConsole()) {
            // Publish configuration
            $this->publishes([
                __DIR__ . '/../../../config/integration.php' => config_path('integration.php'),
            ], 'integration-config');

            // Publish migrations
            $this->publishes([
                __DIR__ . '/../../../database/migrations' => database_path('migrations'),
            ], 'integration-migrations');

            // Publish views
            $this->publishes([
                __DIR__ . '/../Resources/views' => resource_path('views/integration'),
            ], 'integration-views');
        }
    }
}
