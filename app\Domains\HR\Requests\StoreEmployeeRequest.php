<?php

namespace App\Domains\HR\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\HR\Models\Employee;

/**
 * طلب إنشاء موظف جديد
 * تحقق شامل من البيانات مع دعم القوانين المحلية
 */
class StoreEmployeeRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return $this->user()->can('create-employees');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'employee_number' => [
                'nullable',
                'string',
                'max:50',
                'unique:employees,employee_number'
            ],
            'user_id' => [
                'nullable',
                'exists:users,id',
                'unique:employees,user_id'
            ],
            'department_id' => 'required|exists:departments,id',
            'position_id' => 'required|exists:positions,id',
            'manager_id' => 'nullable|exists:employees,id',
            'company_id' => 'nullable|exists:companies,id',
            'branch_id' => 'nullable|exists:branches,id',

            // Personal Information
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'first_name_ar' => 'nullable|string|max:100',
            'middle_name_ar' => 'nullable|string|max:100',
            'last_name_ar' => 'nullable|string|max:100',
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:employees,email'
            ],
            'phone' => 'nullable|string|max:20',
            'mobile' => 'required|string|max:20',
            'date_of_birth' => 'required|date|before:today',
            'gender' => ['required', Rule::in(array_keys(Employee::GENDERS))],
            'marital_status' => ['required', Rule::in(array_keys(Employee::MARITAL_STATUSES))],
            'nationality' => 'required|string|max:100',
            'national_id' => [
                'required',
                'string',
                'max:50',
                'unique:employees,national_id'
            ],
            'passport_number' => 'nullable|string|max:50',
            'passport_expiry' => 'nullable|date|after:today',
            'visa_number' => 'nullable|string|max:50',
            'visa_expiry' => 'nullable|date|after:today',
            'iqama_number' => 'nullable|string|max:50',
            'iqama_expiry' => 'nullable|date|after:today',

            // Address Information
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',

            // Employment Information
            'hire_date' => 'required|date|before_or_equal:today',
            'probation_end_date' => 'nullable|date|after:hire_date',
            'contract_type' => ['required', Rule::in(array_keys(Employee::CONTRACT_TYPES))],
            'employment_type' => ['required', Rule::in(array_keys(Employee::EMPLOYMENT_TYPES))],
            'work_location' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(array_keys(Employee::STATUSES))],

            // Salary Information
            'basic_salary' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'salary_frequency' => 'nullable|string|in:MONTHLY,WEEKLY,BIWEEKLY,QUARTERLY,ANNUALLY',
            'bank_name' => 'nullable|string|max:100',
            'bank_account_number' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',

            // Emergency Contact
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_relationship' => 'required|string|max:100',
            'emergency_contact_phone' => 'required|string|max:20',

            // System Fields
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // Basic Information
            'employee_number.unique' => 'رقم الموظف مستخدم مسبقاً',
            'user_id.exists' => 'المستخدم المحدد غير موجود',
            'user_id.unique' => 'المستخدم مرتبط بموظف آخر',
            'department_id.required' => 'القسم مطلوب',
            'department_id.exists' => 'القسم المحدد غير موجود',
            'position_id.required' => 'المنصب مطلوب',
            'position_id.exists' => 'المنصب المحدد غير موجود',
            'manager_id.exists' => 'المدير المحدد غير موجود',

            // Personal Information
            'first_name.required' => 'الاسم الأول مطلوب',
            'first_name.max' => 'الاسم الأول لا يجب أن يتجاوز 100 حرف',
            'last_name.required' => 'اسم العائلة مطلوب',
            'last_name.max' => 'اسم العائلة لا يجب أن يتجاوز 100 حرف',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            'mobile.required' => 'رقم الجوال مطلوب',
            'date_of_birth.required' => 'تاريخ الميلاد مطلوب',
            'date_of_birth.before' => 'تاريخ الميلاد يجب أن يكون قبل اليوم',
            'gender.required' => 'الجنس مطلوب',
            'gender.in' => 'الجنس المحدد غير صحيح',
            'marital_status.required' => 'الحالة الاجتماعية مطلوبة',
            'marital_status.in' => 'الحالة الاجتماعية المحددة غير صحيحة',
            'nationality.required' => 'الجنسية مطلوبة',
            'national_id.required' => 'رقم الهوية مطلوب',
            'national_id.unique' => 'رقم الهوية مستخدم مسبقاً',
            'passport_expiry.after' => 'تاريخ انتهاء جواز السفر يجب أن يكون في المستقبل',
            'visa_expiry.after' => 'تاريخ انتهاء الفيزا يجب أن يكون في المستقبل',
            'iqama_expiry.after' => 'تاريخ انتهاء الإقامة يجب أن يكون في المستقبل',

            // Address Information
            'address_line_1.required' => 'العنوان مطلوب',
            'city.required' => 'المدينة مطلوبة',
            'country.required' => 'الدولة مطلوبة',

            // Employment Information
            'hire_date.required' => 'تاريخ التوظيف مطلوب',
            'hire_date.before_or_equal' => 'تاريخ التوظيف لا يمكن أن يكون في المستقبل',
            'probation_end_date.after' => 'تاريخ انتهاء فترة التجربة يجب أن يكون بعد تاريخ التوظيف',
            'contract_type.required' => 'نوع العقد مطلوب',
            'contract_type.in' => 'نوع العقد المحدد غير صحيح',
            'employment_type.required' => 'نوع التوظيف مطلوب',
            'employment_type.in' => 'نوع التوظيف المحدد غير صحيح',

            // Salary Information
            'basic_salary.required' => 'الراتب الأساسي مطلوب',
            'basic_salary.numeric' => 'الراتب الأساسي يجب أن يكون رقماً',
            'basic_salary.min' => 'الراتب الأساسي لا يمكن أن يكون سالباً',
            'currency.required' => 'العملة مطلوبة',
            'currency.size' => 'رمز العملة يجب أن يكون 3 أحرف',

            // Emergency Contact
            'emergency_contact_name.required' => 'اسم جهة الاتصال في الطوارئ مطلوب',
            'emergency_contact_relationship.required' => 'صلة القرابة مطلوبة',
            'emergency_contact_phone.required' => 'رقم هاتف الطوارئ مطلوب',

            // System Fields
            'profile_picture.image' => 'الملف يجب أن يكون صورة',
            'profile_picture.mimes' => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg',
            'profile_picture.max' => 'حجم الصورة لا يجب أن يتجاوز 2 ميجابايت',
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف البيانات
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower(trim($this->email))
            ]);
        }

        if ($this->has('national_id')) {
            $this->merge([
                'national_id' => preg_replace('/[^0-9]/', '', $this->national_id)
            ]);
        }

        if ($this->has('mobile')) {
            $this->merge([
                'mobile' => preg_replace('/[^0-9+]/', '', $this->mobile)
            ]);
        }

        if ($this->has('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+]/', '', $this->phone)
            ]);
        }

        // تعيين القيم الافتراضية
        if (!$this->has('status')) {
            $this->merge(['status' => 'ACTIVE']);
        }

        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('currency')) {
            $this->merge(['currency' => 'SAR']);
        }

        if (!$this->has('salary_frequency')) {
            $this->merge(['salary_frequency' => 'MONTHLY']);
        }
    }

    /**
     * قواعد التحقق الإضافية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من عدم تعيين الموظف كمدير لنفسه
            if ($this->manager_id && $this->user_id && $this->manager_id == $this->user_id) {
                $validator->errors()->add('manager_id', 'لا يمكن للموظف أن يكون مديراً لنفسه');
            }

            // التحقق من صحة تاريخ انتهاء فترة التجربة
            if ($this->probation_end_date && $this->hire_date) {
                $hireDate = \Carbon\Carbon::parse($this->hire_date);
                $probationEndDate = \Carbon\Carbon::parse($this->probation_end_date);
                
                if ($probationEndDate->diffInMonths($hireDate) > 6) {
                    $validator->errors()->add('probation_end_date', 'فترة التجربة لا يمكن أن تتجاوز 6 أشهر');
                }
            }

            // التحقق من صحة العمر
            if ($this->date_of_birth) {
                $age = \Carbon\Carbon::parse($this->date_of_birth)->age;
                if ($age < 18) {
                    $validator->errors()->add('date_of_birth', 'عمر الموظف يجب أن يكون 18 سنة على الأقل');
                }
                if ($age > 65) {
                    $validator->errors()->add('date_of_birth', 'عمر الموظف لا يمكن أن يتجاوز 65 سنة');
                }
            }

            // التحقق من متطلبات الوثائق للأجانب
            if ($this->nationality && $this->nationality !== 'Saudi') {
                if (!$this->passport_number) {
                    $validator->errors()->add('passport_number', 'رقم جواز السفر مطلوب للموظفين الأجانب');
                }
                if (!$this->passport_expiry) {
                    $validator->errors()->add('passport_expiry', 'تاريخ انتهاء جواز السفر مطلوب للموظفين الأجانب');
                }
            }

            // التحقق من الراتب الأدنى
            if ($this->basic_salary && $this->basic_salary < 1000) {
                $validator->errors()->add('basic_salary', 'الراتب الأساسي لا يمكن أن يكون أقل من 1000');
            }
        });
    }

    /**
     * الحصول على البيانات المنظفة
     */
    public function getCleanedData(): array
    {
        $data = $this->validated();

        // إزالة الحقول الفارغة
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }
}
