<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\HasFiles;

/**
 * نموذج جهة الاتصال - Contact Person
 * أشخاص الاتصال داخل الشركات العميلة
 */
class Contact extends Model
{
    use HasFactory, SoftDeletes, HasUuid, HasFiles;

    protected $fillable = [
        'customer_id',
        'first_name',
        'last_name',
        'title',
        'department',
        'email',
        'phone',
        'mobile',
        'whatsapp',
        'linkedin',
        'twitter',
        'facebook',
        'skype',
        'is_primary',
        'is_decision_maker',
        'authority_level',
        'influence_score',
        'relationship_strength',
        'communication_preference',
        'language',
        'timezone',
        'birthday',
        'anniversary',
        'interests',
        'notes',
        'tags',
        'custom_fields',
        'last_contact_at',
        'contact_frequency',
        'preferred_contact_time',
        'do_not_contact',
        'email_opt_out',
        'sms_opt_out',
        'marketing_consent',
        'gdpr_consent',
        'status',
        'metadata',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'is_decision_maker' => 'boolean',
        'authority_level' => 'integer',
        'influence_score' => 'integer',
        'relationship_strength' => 'integer',
        'birthday' => 'date',
        'anniversary' => 'date',
        'interests' => 'array',
        'tags' => 'array',
        'custom_fields' => 'array',
        'last_contact_at' => 'datetime',
        'do_not_contact' => 'boolean',
        'email_opt_out' => 'boolean',
        'sms_opt_out' => 'boolean',
        'marketing_consent' => 'boolean',
        'gdpr_consent' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * مستويات السلطة
     */
    const AUTHORITY_LEVELS = [
        1 => 'موظف',
        2 => 'مشرف',
        3 => 'مدير',
        4 => 'مدير عام',
        5 => 'مدير تنفيذي',
        6 => 'رئيس مجلس إدارة',
    ];

    /**
     * تفضيلات التواصل
     */
    const COMMUNICATION_PREFERENCES = [
        'email' => 'بريد إلكتروني',
        'phone' => 'هاتف',
        'whatsapp' => 'واتساب',
        'sms' => 'رسالة نصية',
        'linkedin' => 'لينكد إن',
        'in_person' => 'شخصياً',
    ];

    /**
     * حالات جهة الاتصال
     */
    const STATUSES = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'left_company' => 'ترك الشركة',
        'promoted' => 'تمت ترقيته',
        'transferred' => 'تم نقله',
        'retired' => 'متقاعد',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع التفاعلات
     */
    public function interactions(): HasMany
    {
        return $this->hasMany(CustomerInteraction::class);
    }

    /**
     * العلاقة مع الفرص التجارية
     */
    public function opportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class);
    }

    /**
     * العلاقة مع المواعيد
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * الحصول على الاسم مع المنصب
     */
    public function getNameWithTitleAttribute(): string
    {
        $name = $this->full_name;
        
        if ($this->title) {
            $name .= ' - ' . $this->title;
        }

        return $name;
    }

    /**
     * الحصول على تسمية مستوى السلطة
     */
    public function getAuthorityLevelLabelAttribute(): string
    {
        return self::AUTHORITY_LEVELS[$this->authority_level] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية تفضيل التواصل
     */
    public function getCommunicationPreferenceLabelAttribute(): string
    {
        return self::COMMUNICATION_PREFERENCES[$this->communication_preference] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية الحالة
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على لون مستوى السلطة
     */
    public function getAuthorityColorAttribute(): string
    {
        return match ($this->authority_level) {
            1, 2 => '#6c757d',
            3 => '#17a2b8',
            4 => '#ffc107',
            5 => '#fd7e14',
            6 => '#dc3545',
            default => '#6c757d',
        };
    }

    /**
     * الحصول على لون قوة العلاقة
     */
    public function getRelationshipColorAttribute(): string
    {
        if ($this->relationship_strength >= 8) return '#28a745';
        if ($this->relationship_strength >= 6) return '#ffc107';
        if ($this->relationship_strength >= 4) return '#fd7e14';
        return '#dc3545';
    }

    /**
     * التحقق من كون جهة الاتصال أساسية
     */
    public function getIsPrimaryContactAttribute(): bool
    {
        return $this->is_primary;
    }

    /**
     * التحقق من كون جهة الاتصال صانع قرار
     */
    public function getIsDecisionMakerContactAttribute(): bool
    {
        return $this->is_decision_maker;
    }

    /**
     * التحقق من إمكانية التواصل
     */
    public function getCanContactAttribute(): bool
    {
        return !$this->do_not_contact && $this->status === 'active';
    }

    /**
     * التحقق من إمكانية إرسال بريد إلكتروني
     */
    public function getCanEmailAttribute(): bool
    {
        return $this->can_contact && !$this->email_opt_out && $this->email;
    }

    /**
     * التحقق من إمكانية إرسال SMS
     */
    public function getCanSmsAttribute(): bool
    {
        return $this->can_contact && !$this->sms_opt_out && $this->mobile;
    }

    /**
     * الحصول على العمر
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birthday ? $this->birthday->age : null;
    }

    /**
     * الحصول على عدد الأيام منذ آخر تواصل
     */
    public function getDaysSinceLastContactAttribute(): int
    {
        return $this->last_contact_at ? $this->last_contact_at->diffInDays(now()) : 0;
    }

    /**
     * الحصول على آخر تفاعل
     */
    public function getLastInteractionAttribute(): ?CustomerInteraction
    {
        return $this->interactions()->latest('occurred_at')->first();
    }

    /**
     * الحصول على عدد التفاعلات
     */
    public function getTotalInteractionsAttribute(): int
    {
        return $this->interactions()->count();
    }

    /**
     * الحصول على عدد التفاعلات الأخيرة
     */
    public function getRecentInteractionsCountAttribute(): int
    {
        return $this->interactions()
                   ->where('occurred_at', '>=', now()->subDays(30))
                   ->count();
    }

    /**
     * الحصول على متوسط تكرار التواصل
     */
    public function getContactFrequencyScoreAttribute(): float
    {
        $interactions = $this->interactions()
                            ->where('occurred_at', '>=', now()->subMonths(6))
                            ->count();

        return round($interactions / 6, 1); // متوسط شهري
    }

    /**
     * الحصول على نقاط التأثير
     */
    public function getInfluenceScoreCalculatedAttribute(): int
    {
        $score = 0;

        // نقاط بناءً على مستوى السلطة
        $score += $this->authority_level * 15;

        // نقاط إضافية لصانع القرار
        if ($this->is_decision_maker) {
            $score += 20;
        }

        // نقاط إضافية لجهة الاتصال الأساسية
        if ($this->is_primary) {
            $score += 10;
        }

        // نقاط بناءً على قوة العلاقة
        $score += $this->relationship_strength * 5;

        // نقاط بناءً على تكرار التواصل
        $score += min($this->contact_frequency_score * 2, 20);

        return min(100, $score);
    }

    /**
     * تحديث آخر تاريخ تواصل
     */
    public function updateLastContact(): void
    {
        $this->update(['last_contact_at' => now()]);
    }

    /**
     * تحديث قوة العلاقة
     */
    public function updateRelationshipStrength(): void
    {
        $recentInteractions = $this->interactions()
                                  ->where('occurred_at', '>=', now()->subMonths(3))
                                  ->get();

        $strength = 1; // قيمة أساسية

        // زيادة بناءً على عدد التفاعلات
        $strength += min($recentInteractions->count(), 5);

        // زيادة بناءً على نوع التفاعلات
        $meetingCount = $recentInteractions->where('type', 'meeting')->count();
        $callCount = $recentInteractions->where('type', 'call')->count();
        
        $strength += $meetingCount * 2;
        $strength += $callCount;

        // زيادة بناءً على المشاعر الإيجابية
        $positiveInteractions = $recentInteractions->whereIn('sentiment', ['positive', 'very_positive'])->count();
        $strength += $positiveInteractions;

        // تقليل بناءً على المشاعر السلبية
        $negativeInteractions = $recentInteractions->whereIn('sentiment', ['negative', 'very_negative'])->count();
        $strength -= $negativeInteractions;

        $this->update(['relationship_strength' => max(1, min(10, $strength))]);
    }

    /**
     * إضافة تفاعل جديد
     */
    public function addInteraction(array $interactionData): CustomerInteraction
    {
        $interactionData['contact_id'] = $this->id;
        $interactionData['customer_id'] = $this->customer_id;
        
        $interaction = CustomerInteraction::create($interactionData);
        
        $this->updateLastContact();
        $this->updateRelationshipStrength();
        
        return $interaction;
    }

    /**
     * تعيين كجهة اتصال أساسية
     */
    public function makePrimary(): bool
    {
        // إزالة الأساسية من جهات الاتصال الأخرى
        $this->customer->contacts()
             ->where('id', '!=', $this->id)
             ->update(['is_primary' => false]);

        return $this->update(['is_primary' => true]);
    }

    /**
     * إلغاء الاشتراك من البريد الإلكتروني
     */
    public function optOutFromEmail(): bool
    {
        return $this->update(['email_opt_out' => true]);
    }

    /**
     * إلغاء الاشتراك من SMS
     */
    public function optOutFromSms(): bool
    {
        return $this->update(['sms_opt_out' => true]);
    }

    /**
     * منع التواصل
     */
    public function doNotContact(string $reason = null): bool
    {
        return $this->update([
            'do_not_contact' => true,
            'notes' => $this->notes . "\n\nمنع التواصل: " . $reason,
        ]);
    }

    /**
     * فلترة جهات الاتصال النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * فلترة جهات الاتصال الأساسية
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * فلترة صناع القرار
     */
    public function scopeDecisionMakers($query)
    {
        return $query->where('is_decision_maker', true);
    }

    /**
     * فلترة حسب مستوى السلطة
     */
    public function scopeWithAuthorityLevel($query, int $level)
    {
        return $query->where('authority_level', '>=', $level);
    }

    /**
     * فلترة القابلين للتواصل
     */
    public function scopeContactable($query)
    {
        return $query->where('do_not_contact', false)
                    ->where('status', 'active');
    }

    /**
     * فلترة القابلين لإرسال البريد
     */
    public function scopeEmailable($query)
    {
        return $query->contactable()
                    ->where('email_opt_out', false)
                    ->whereNotNull('email');
    }

    /**
     * فلترة القابلين لإرسال SMS
     */
    public function scopeSmsable($query)
    {
        return $query->contactable()
                    ->where('sms_opt_out', false)
                    ->whereNotNull('mobile');
    }

    /**
     * فلترة حسب تفضيل التواصل
     */
    public function scopePrefersCommunication($query, string $method)
    {
        return $query->where('communication_preference', $method);
    }

    /**
     * فلترة الذين لم يتم التواصل معهم مؤخراً
     */
    public function scopeNotContactedSince($query, int $days)
    {
        return $query->where('last_contact_at', '<', now()->subDays($days))
                    ->orWhereNull('last_contact_at');
    }

    /**
     * فلترة حسب قوة العلاقة
     */
    public function scopeWithStrongRelationship($query, int $minStrength = 7)
    {
        return $query->where('relationship_strength', '>=', $minStrength);
    }

    /**
     * البحث في جهات الاتصال
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'LIKE', "%{$search}%")
              ->orWhere('last_name', 'LIKE', "%{$search}%")
              ->orWhere('email', 'LIKE', "%{$search}%")
              ->orWhere('phone', 'LIKE', "%{$search}%")
              ->orWhere('title', 'LIKE', "%{$search}%")
              ->orWhere('department', 'LIKE', "%{$search}%");
        });
    }

    /**
     * ترتيب حسب الأهمية
     */
    public function scopeOrderByImportance($query)
    {
        return $query->orderByDesc('is_primary')
                    ->orderByDesc('is_decision_maker')
                    ->orderByDesc('authority_level')
                    ->orderByDesc('relationship_strength');
    }
}
