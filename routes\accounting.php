<?php

use Illuminate\Support\Facades\Route;
use App\Domains\Accounting\Controllers\AccountController;
use App\Domains\Accounting\Controllers\InvoiceController;
use App\Domains\Accounting\Controllers\JournalEntryController;
use App\Domains\Accounting\Controllers\PaymentController;
use App\Domains\Accounting\Controllers\ReportController;
use App\Domains\Accounting\Controllers\DashboardController;
use App\Domains\Accounting\Controllers\Web\AccountController as WebAccountController;
use App\Domains\Accounting\Middleware\AccountingPermissionMiddleware;

/*
|--------------------------------------------------------------------------
| Accounting Routes
|--------------------------------------------------------------------------
|
| مسارات نظام المحاسبة
|
*/

// API Routes
Route::prefix('api/accounting')->name('api.accounting.')->middleware(['api', 'auth:sanctum', AccountingPermissionMiddleware::class])->group(function () {
    
    // Dashboard Routes
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        Route::get('/', [DashboardController::class, 'index'])->name('index');
        Route::get('/analytics', [DashboardController::class, 'getAdvancedAnalytics'])->name('analytics');
        Route::get('/forecasts', [DashboardController::class, 'getFinancialForecasts'])->name('forecasts');
        Route::get('/performance-comparison', [DashboardController::class, 'getPerformanceComparison'])->name('performance-comparison');
        Route::post('/refresh', [DashboardController::class, 'refreshDashboard'])->name('refresh');
        Route::post('/customize', [DashboardController::class, 'customizeDashboard'])->name('customize');
    });

    // Account Routes
    Route::prefix('accounts')->name('accounts.')->group(function () {
        Route::get('/', [AccountController::class, 'index'])->name('index');
        Route::post('/', [AccountController::class, 'store'])->name('store');
        Route::get('/chart', [AccountController::class, 'getChartOfAccounts'])->name('chart');
        Route::get('/search', [AccountController::class, 'search'])->name('search');
        Route::get('/types', [AccountController::class, 'getAccountTypes'])->name('types');
        Route::get('/categories', [AccountController::class, 'getAccountCategories'])->name('categories');
        Route::get('/trial-balance', [AccountController::class, 'getTrialBalance'])->name('trial-balance');
        Route::post('/import', [AccountController::class, 'import'])->name('import');
        Route::get('/export', [AccountController::class, 'export'])->name('export');
        
        Route::prefix('{account}')->group(function () {
            Route::get('/', [AccountController::class, 'show'])->name('show');
            Route::put('/', [AccountController::class, 'update'])->name('update');
            Route::delete('/', [AccountController::class, 'destroy'])->name('destroy');
            Route::get('/balance', [AccountController::class, 'getBalance'])->name('balance');
            Route::get('/history', [AccountController::class, 'getHistory'])->name('history');
            Route::post('/adjust-balance', [AccountController::class, 'adjustBalance'])->name('adjust-balance');
            Route::post('/activate', [AccountController::class, 'activate'])->name('activate');
            Route::post('/deactivate', [AccountController::class, 'deactivate'])->name('deactivate');
        });
    });

    // Invoice Routes
    Route::prefix('invoices')->name('invoices.')->group(function () {
        Route::get('/', [InvoiceController::class, 'index'])->name('index');
        Route::post('/', [InvoiceController::class, 'store'])->name('store');
        Route::get('/overdue', [InvoiceController::class, 'getOverdueInvoices'])->name('overdue');
        Route::get('/recurring', [InvoiceController::class, 'getRecurringInvoices'])->name('recurring');
        Route::get('/analytics', [InvoiceController::class, 'getInvoiceAnalytics'])->name('analytics');
        Route::post('/bulk-send', [InvoiceController::class, 'bulkSendInvoices'])->name('bulk-send');
        
        Route::prefix('{invoice}')->group(function () {
            Route::get('/', [InvoiceController::class, 'show'])->name('show');
            Route::put('/', [InvoiceController::class, 'update'])->name('update');
            Route::delete('/', [InvoiceController::class, 'destroy'])->name('destroy');
            Route::post('/send', [InvoiceController::class, 'sendInvoice'])->name('send');
            Route::post('/approve', [InvoiceController::class, 'approveInvoice'])->name('approve');
            Route::post('/cancel', [InvoiceController::class, 'cancelInvoice'])->name('cancel');
            Route::post('/duplicate', [InvoiceController::class, 'duplicateInvoice'])->name('duplicate');
            Route::get('/pdf', [InvoiceController::class, 'generatePDF'])->name('pdf');
            Route::get('/payments', [InvoiceController::class, 'getInvoicePayments'])->name('payments');
            Route::post('/payments', [InvoiceController::class, 'recordPayment'])->name('record-payment');
        });
    });

    // Journal Entry Routes
    Route::prefix('journal-entries')->name('journal-entries.')->group(function () {
        Route::get('/', [JournalEntryController::class, 'index'])->name('index');
        Route::post('/', [JournalEntryController::class, 'store'])->name('store');
        Route::get('/unposted', [JournalEntryController::class, 'getUnpostedEntries'])->name('unposted');
        Route::get('/pending-approval', [JournalEntryController::class, 'getPendingApprovalEntries'])->name('pending-approval');
        Route::get('/templates', [JournalEntryController::class, 'getEntryTemplates'])->name('templates');
        Route::post('/from-template', [JournalEntryController::class, 'createFromTemplate'])->name('from-template');
        Route::post('/auto-generate', [JournalEntryController::class, 'autoGenerateEntries'])->name('auto-generate');
        Route::get('/audit-trail', [JournalEntryController::class, 'getAuditTrail'])->name('audit-trail');
        
        Route::prefix('{journalEntry}')->group(function () {
            Route::get('/', [JournalEntryController::class, 'show'])->name('show');
            Route::put('/', [JournalEntryController::class, 'update'])->name('update');
            Route::delete('/', [JournalEntryController::class, 'destroy'])->name('destroy');
            Route::post('/post', [JournalEntryController::class, 'postEntry'])->name('post');
            Route::post('/reverse', [JournalEntryController::class, 'reverseEntry'])->name('reverse');
            Route::post('/approve', [JournalEntryController::class, 'approveEntry'])->name('approve');
        });
    });

    // Payment Routes
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', [PaymentController::class, 'index'])->name('index');
        Route::post('/', [PaymentController::class, 'store'])->name('store');
        Route::get('/pending', [PaymentController::class, 'getPendingPayments'])->name('pending');
        Route::get('/by-method/{method}', [PaymentController::class, 'getPaymentsByMethod'])->name('by-method');
        Route::get('/reconciliation', [PaymentController::class, 'getReconciliationData'])->name('reconciliation');
        Route::post('/auto-match', [PaymentController::class, 'autoMatchPayments'])->name('auto-match');
        Route::post('/bulk-approve', [PaymentController::class, 'bulkApprovePayments'])->name('bulk-approve');
        
        Route::prefix('{payment}')->group(function () {
            Route::get('/', [PaymentController::class, 'show'])->name('show');
            Route::put('/', [PaymentController::class, 'update'])->name('update');
            Route::delete('/', [PaymentController::class, 'destroy'])->name('destroy');
            Route::post('/approve', [PaymentController::class, 'approvePayment'])->name('approve');
            Route::post('/reject', [PaymentController::class, 'rejectPayment'])->name('reject');
            Route::post('/void', [PaymentController::class, 'voidPayment'])->name('void');
        });
    });

    // Report Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/balance-sheet', [ReportController::class, 'balanceSheet'])->name('balance-sheet');
        Route::get('/income-statement', [ReportController::class, 'incomeStatement'])->name('income-statement');
        Route::get('/cash-flow', [ReportController::class, 'cashFlowStatement'])->name('cash-flow');
        Route::get('/trial-balance', [ReportController::class, 'trialBalance'])->name('trial-balance');
        Route::get('/general-ledger', [ReportController::class, 'generalLedger'])->name('general-ledger');
        Route::get('/accounts-receivable', [ReportController::class, 'accountsReceivable'])->name('accounts-receivable');
        Route::get('/accounts-payable', [ReportController::class, 'accountsPayable'])->name('accounts-payable');
        Route::get('/aged-receivables', [ReportController::class, 'agedReceivables'])->name('aged-receivables');
        Route::get('/aged-payables', [ReportController::class, 'agedPayables'])->name('aged-payables');
        Route::get('/profit-loss', [ReportController::class, 'profitLoss'])->name('profit-loss');
        Route::get('/budget-variance', [ReportController::class, 'budgetVariance'])->name('budget-variance');
        Route::post('/custom', [ReportController::class, 'customReport'])->name('custom');
        Route::post('/schedule', [ReportController::class, 'scheduleReport'])->name('schedule');
        Route::get('/scheduled', [ReportController::class, 'getScheduledReports'])->name('scheduled');
        Route::post('/export', [ReportController::class, 'exportReport'])->name('export');
        Route::get('/kpis', [ReportController::class, 'getFinancialKPIs'])->name('kpis');
        Route::get('/trends', [ReportController::class, 'getTrendAnalysis'])->name('trends');
    });
});

// Web Routes
Route::prefix('accounting')->name('accounting.')->middleware(['web', 'auth', AccountingPermissionMiddleware::class])->group(function () {
    
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    
    // Accounts
    Route::prefix('accounts')->name('accounts.')->group(function () {
        Route::get('/', [WebAccountController::class, 'index'])->name('index');
        Route::get('/create', [WebAccountController::class, 'create'])->name('create');
        Route::post('/', [WebAccountController::class, 'store'])->name('store');
        Route::get('/chart', [WebAccountController::class, 'chartView'])->name('chart');
        Route::post('/import', [WebAccountController::class, 'import'])->name('import');
        Route::get('/export', [WebAccountController::class, 'export'])->name('export');
        
        Route::prefix('{account}')->group(function () {
            Route::get('/', [WebAccountController::class, 'show'])->name('show');
            Route::get('/edit', [WebAccountController::class, 'edit'])->name('edit');
            Route::put('/', [WebAccountController::class, 'update'])->name('update');
            Route::delete('/', [WebAccountController::class, 'destroy'])->name('destroy');
            Route::get('/history', [WebAccountController::class, 'history'])->name('history');
            Route::get('/balance', [WebAccountController::class, 'balance'])->name('balance');
            Route::get('/adjust-balance', [WebAccountController::class, 'adjustBalanceForm'])->name('adjust-balance-form');
            Route::post('/adjust-balance', [WebAccountController::class, 'adjustBalance'])->name('adjust-balance');
        });
    });
    
    // Invoices
    Route::resource('invoices', InvoiceController::class);
    Route::prefix('invoices/{invoice}')->name('invoices.')->group(function () {
        Route::post('/send', [InvoiceController::class, 'sendInvoice'])->name('send');
        Route::post('/approve', [InvoiceController::class, 'approveInvoice'])->name('approve');
        Route::post('/cancel', [InvoiceController::class, 'cancelInvoice'])->name('cancel');
        Route::get('/pdf', [InvoiceController::class, 'generatePDF'])->name('pdf');
        Route::get('/duplicate', [InvoiceController::class, 'duplicateInvoice'])->name('duplicate');
    });
    
    // Journal Entries
    Route::resource('journal-entries', JournalEntryController::class);
    Route::prefix('journal-entries/{journalEntry}')->name('journal-entries.')->group(function () {
        Route::post('/post', [JournalEntryController::class, 'postEntry'])->name('post');
        Route::post('/reverse', [JournalEntryController::class, 'reverseEntry'])->name('reverse');
        Route::post('/approve', [JournalEntryController::class, 'approveEntry'])->name('approve');
    });
    
    // Payments
    Route::resource('payments', PaymentController::class);
    Route::prefix('payments/{payment}')->name('payments.')->group(function () {
        Route::post('/approve', [PaymentController::class, 'approvePayment'])->name('approve');
        Route::post('/reject', [PaymentController::class, 'rejectPayment'])->name('reject');
        Route::post('/void', [PaymentController::class, 'voidPayment'])->name('void');
    });
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/balance-sheet', [ReportController::class, 'balanceSheet'])->name('balance-sheet');
        Route::get('/income-statement', [ReportController::class, 'incomeStatement'])->name('income-statement');
        Route::get('/cash-flow', [ReportController::class, 'cashFlowStatement'])->name('cash-flow');
        Route::get('/trial-balance', [ReportController::class, 'trialBalance'])->name('trial-balance');
        Route::get('/general-ledger', [ReportController::class, 'generalLedger'])->name('general-ledger');
        Route::get('/accounts-receivable', [ReportController::class, 'accountsReceivable'])->name('accounts-receivable');
        Route::get('/accounts-payable', [ReportController::class, 'accountsPayable'])->name('accounts-payable');
    });
});

// Public Routes (for customer invoice viewing, etc.)
Route::prefix('public/accounting')->name('public.accounting.')->group(function () {
    Route::get('/invoices/{invoice}/view/{token}', [InvoiceController::class, 'publicView'])->name('invoices.public-view');
    Route::get('/invoices/{invoice}/pdf/{token}', [InvoiceController::class, 'publicPDF'])->name('invoices.public-pdf');
    Route::post('/invoices/{invoice}/payment/{token}', [InvoiceController::class, 'publicPayment'])->name('invoices.public-payment');
});

// Webhook Routes (for payment gateways, bank integrations, etc.)
Route::prefix('webhooks/accounting')->name('webhooks.accounting.')->group(function () {
    Route::post('/zatca/callback', [InvoiceController::class, 'zatcaCallback'])->name('zatca-callback');
    Route::post('/payment-gateway/callback', [PaymentController::class, 'paymentGatewayCallback'])->name('payment-gateway-callback');
    Route::post('/bank/transaction-update', [PaymentController::class, 'bankTransactionUpdate'])->name('bank-transaction-update');
});
