<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج القسم
 */
class Department extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'code',
        'description',
        'parent_id',
        'manager_id',
        'budget_amount',
        'cost_center_code',
        'location',
        'phone',
        'email',
        'is_active',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'budget_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * القسم الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * الأقسام الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    /**
     * مدير القسم
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'manager_id');
    }

    /**
     * موظفي القسم
     */
    public function employees(): HasMany
    {
        return $this->hasMany(\App\Domains\HR\Models\Employee::class);
    }

    /**
     * ميزانيات القسم
     */
    public function budgets(): HasMany
    {
        return $this->hasMany(\App\Domains\Accounting\Models\Budget::class);
    }

    /**
     * من أنشأ القسم
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * من حدث القسم
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * فحص إذا كان القسم نشط
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * فحص إذا كان القسم رئيسي
     */
    public function isParent(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * فحص إذا كان القسم فرعي
     */
    public function isChild(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * فحص إذا كان القسم له أقسام فرعية
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * الحصول على المسار الكامل للقسم
     */
    public function getFullPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * الحصول على عدد الموظفين
     */
    public function getEmployeesCountAttribute(): int
    {
        return $this->employees()->count();
    }

    /**
     * الحصول على عدد الموظفين النشطين
     */
    public function getActiveEmployeesCountAttribute(): int
    {
        return $this->employees()->where('is_active', true)->count();
    }

    /**
     * الحصول على إجمالي الرواتب
     */
    public function getTotalSalariesAttribute(): float
    {
        return $this->employees()
            ->where('is_active', true)
            ->sum('salary');
    }

    /**
     * الحصول على نسبة استخدام الميزانية
     */
    public function getBudgetUtilizationAttribute(): float
    {
        if (!$this->budget_amount || $this->budget_amount == 0) {
            return 0;
        }

        $totalSpent = $this->getTotalSalariesAttribute();
        return ($totalSpent / $this->budget_amount) * 100;
    }

    /**
     * تفعيل القسم
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * إلغاء تفعيل القسم
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * تحديث الميزانية
     */
    public function updateBudget(float $newBudget): void
    {
        $this->update(['budget_amount' => $newBudget]);
    }

    /**
     * تعيين مدير جديد
     */
    public function assignManager(int $managerId): void
    {
        $this->update(['manager_id' => $managerId]);
    }

    /**
     * إزالة المدير
     */
    public function removeManager(): void
    {
        $this->update(['manager_id' => null]);
    }

    /**
     * الحصول على جميع الأقسام الفرعية (بشكل تكراري)
     */
    public function getAllChildren(): \Illuminate\Database\Eloquent\Collection
    {
        $children = collect();
        
        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }
        
        return $children;
    }

    /**
     * الحصول على جميع الموظفين (بما في ذلك الأقسام الفرعية)
     */
    public function getAllEmployees(): \Illuminate\Database\Eloquent\Collection
    {
        $employees = $this->employees;
        
        foreach ($this->getAllChildren() as $child) {
            $employees = $employees->merge($child->employees);
        }
        
        return $employees;
    }

    /**
     * Scope للأقسام النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للأقسام غير النشطة
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope للأقسام الرئيسية
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope للأقسام الفرعية
     */
    public function scopeChildren($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope للبحث
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Scope للأقسام التي لها ميزانية
     */
    public function scopeWithBudget($query)
    {
        return $query->whereNotNull('budget_amount')
            ->where('budget_amount', '>', 0);
    }

    /**
     * Scope للأقسام التي تجاوزت الميزانية
     */
    public function scopeOverBudget($query)
    {
        return $query->whereNotNull('budget_amount')
            ->whereHas('employees', function ($q) {
                $q->where('is_active', true)
                  ->havingRaw('SUM(salary) > departments.budget_amount');
            });
    }
}
