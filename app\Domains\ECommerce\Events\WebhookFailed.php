<?php

namespace App\Domains\ECommerce\Events;

use App\Domains\ECommerce\Models\ECommerceWebhook;
use App\Domains\ECommerce\Models\ECommerceIntegration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث فشل معالجة Webhook
 * يتم إطلاقه عند فشل معالجة webhook
 */
class WebhookFailed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ECommerceWebhook $webhook;
    public ECommerceIntegration $integration;
    public \Exception $exception;
    public array $errorContext;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(
        ECommerceWebhook $webhook,
        ECommerceIntegration $integration,
        \Exception $exception,
        array $errorContext = []
    ) {
        $this->webhook = $webhook;
        $this->integration = $integration;
        $this->exception = $exception;
        $this->errorContext = $errorContext;
    }

    /**
     * الحصول على بيانات الحدث
     */
    public function getEventData(): array
    {
        return [
            'webhook_id' => $this->webhook->id,
            'external_webhook_id' => $this->webhook->webhook_id,
            'event_type' => $this->webhook->event_type,
            'event_name' => $this->webhook->event_name,
            'topic' => $this->webhook->topic,
            'resource' => $this->webhook->resource,
            'resource_id' => $this->webhook->resource_id,
            'action' => $this->webhook->action,
            'integration_id' => $this->integration->id,
            'platform_name' => $this->integration->platform->name,
            'store_name' => $this->integration->store->name,
            'company_id' => $this->integration->company_id,
            'received_at' => $this->webhook->received_at,
            'failed_at' => $this->webhook->failed_at,
            'processing_time' => $this->webhook->processing_time,
            'total_time' => $this->webhook->total_time,
            'attempt_number' => $this->webhook->attempt_number,
            'retry_count' => $this->webhook->retry_count,
            'error_message' => $this->exception->getMessage(),
            'error_code' => $this->exception->getCode(),
            'error_type' => get_class($this->exception),
            'error_context' => $this->errorContext,
        ];
    }

    /**
     * الحصول على رسالة الخطأ
     */
    public function getErrorMessage(): string
    {
        return $this->exception->getMessage();
    }

    /**
     * الحصول على رمز الخطأ
     */
    public function getErrorCode(): int
    {
        return $this->exception->getCode();
    }

    /**
     * الحصول على نوع الخطأ
     */
    public function getErrorType(): string
    {
        return get_class($this->exception);
    }

    /**
     * تحديد ما إذا كان الخطأ قابل للإعادة
     */
    public function isRetryable(): bool
    {
        return $this->webhook->canRetry();
    }

    /**
     * تحديد ما إذا كان الخطأ مؤقت
     */
    public function isTemporary(): bool
    {
        $temporaryErrors = [
            'timeout',
            'connection',
            'network',
            'rate limit',
            'server error',
            '5xx',
            'temporary',
        ];

        $errorMessage = strtolower($this->getErrorMessage());
        
        foreach ($temporaryErrors as $temporaryError) {
            if (str_contains($errorMessage, $temporaryError)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تحديد ما إذا كان الخطأ دائم
     */
    public function isPermanent(): bool
    {
        return !$this->isTemporary();
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتوقيع
     */
    public function isSignatureError(): bool
    {
        return str_contains(strtolower($this->getErrorMessage()), 'signature') ||
               str_contains(strtolower($this->getErrorMessage()), 'verification') ||
               str_contains(strtolower($this->getErrorMessage()), 'authentication');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالتحليل
     */
    public function isParsingError(): bool
    {
        return str_contains(strtolower($this->getErrorMessage()), 'parsing') ||
               str_contains(strtolower($this->getErrorMessage()), 'json') ||
               str_contains(strtolower($this->getErrorMessage()), 'format') ||
               str_contains(strtolower($this->getErrorMessage()), 'decode');
    }

    /**
     * تحديد ما إذا كان الخطأ متعلق بالمعالجة
     */
    public function isProcessingError(): bool
    {
        return !$this->isSignatureError() && !$this->isParsingError();
    }

    /**
     * الحصول على عدد المحاولات
     */
    public function getAttemptNumber(): int
    {
        return $this->webhook->getAttemptNumber();
    }

    /**
     * الحصول على عدد إعادة المحاولة
     */
    public function getRetryCount(): int
    {
        return $this->webhook->getRetryCount();
    }

    /**
     * تحديد ما إذا كان يمكن إعادة المحاولة
     */
    public function canRetry(): bool
    {
        return $this->webhook->canRetry() && $this->isRetryable();
    }

    /**
     * تحديد ما إذا كان يجب إعادة المحاولة
     */
    public function shouldRetry(): bool
    {
        return $this->webhook->shouldRetry() && $this->isTemporary();
    }
}
