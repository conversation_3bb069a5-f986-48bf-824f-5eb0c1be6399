<?php

namespace App\Domains\Compliance\Policies;

use App\Domains\Compliance\Models\ComplianceRule;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * سياسة قواعد الامتثال
 */
class ComplianceRulePolicy
{
    use HandlesAuthorization;

    /**
     * عرض جميع القواعد
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('compliance.rules.view');
    }

    /**
     * عرض قاعدة محددة
     */
    public function view(User $user, ComplianceRule $rule): bool
    {
        return $user->hasPermissionTo('compliance.rules.view') &&
               $this->canAccessCountry($user, $rule->country->code);
    }

    /**
     * إنشاء قاعدة جديدة
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('compliance.rules.create');
    }

    /**
     * تحديث قاعدة
     */
    public function update(User $user, ComplianceRule $rule): bool
    {
        return $user->hasPermissionTo('compliance.rules.update') &&
               $this->canAccessCountry($user, $rule->country->code);
    }

    /**
     * حذف قاعدة
     */
    public function delete(User $user, ComplianceRule $rule): bool
    {
        return $user->hasPermissionTo('compliance.rules.delete') &&
               $this->canAccessCountry($user, $rule->country->code) &&
               !$rule->is_system_rule; // لا يمكن حذف القواعد النظامية
    }

    /**
     * تفعيل/إلغاء تفعيل قاعدة
     */
    public function toggleStatus(User $user, ComplianceRule $rule): bool
    {
        return $user->hasPermissionTo('compliance.rules.manage') &&
               $this->canAccessCountry($user, $rule->country->code);
    }

    /**
     * التحقق من إمكانية الوصول للدولة
     */
    protected function canAccessCountry(User $user, string $countryCode): bool
    {
        // إذا كان المستخدم مدير عام، يمكنه الوصول لجميع الدول
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // التحقق من صلاحيات الدولة المحددة
        return $user->hasPermissionTo("compliance.countries.{$countryCode}");
    }
}
