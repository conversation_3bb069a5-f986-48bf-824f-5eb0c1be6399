<?php

namespace App\Domains\HR\Events;

use App\Domains\HR\Models\AttendanceRecord;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * حدث تسجيل حضور الموظف
 */
class EmployeeCheckedIn
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public AttendanceRecord $attendanceRecord;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(AttendanceRecord $attendanceRecord)
    {
        $this->attendanceRecord = $attendanceRecord;
    }
}
