<?php

namespace App\Domains\Accounting\Events;

use App\Domains\Accounting\Models\Account;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Account Created Event
 * حدث إنشاء حساب جديد
 */
class AccountCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Account $account;
    public ?int $userId;

    /**
     * إنشاء مثيل جديد من الحدث
     */
    public function __construct(Account $account, ?int $userId = null)
    {
        $this->account = $account;
        $this->userId = $userId ?? auth()->id();
    }

    /**
     * الحصول على البيانات للبث
     */
    public function broadcastWith(): array
    {
        return [
            'account' => [
                'id' => $this->account->id,
                'account_code' => $this->account->account_code,
                'account_name' => $this->account->account_name,
                'account_type' => $this->account->account_type,
                'is_active' => $this->account->is_active,
            ],
            'user_id' => $this->userId,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * اسم القناة للبث
     */
    public function broadcastOn(): array
    {
        return [
            'accounting.accounts',
            "user.{$this->userId}",
        ];
    }

    /**
     * اسم الحدث للبث
     */
    public function broadcastAs(): string
    {
        return 'account.created';
    }
}
