<?php

namespace App\Domains\Compliance\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * نموذج التكوين الضريبي المتقدم
 * يدير جميع أنواع الضرائب والمعدلات والقواعد لكل دولة
 */
class TaxConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'country_id',
        'tax_type',
        'tax_name_ar',
        'tax_name_en',
        'tax_name_fr',
        'tax_code',
        'tax_authority',
        'tax_rates',
        'tax_brackets',
        'exemptions',
        'deductions',
        'calculation_method',
        'filing_frequency',
        'filing_deadlines',
        'penalty_structure',
        'interest_rates',
        'minimum_thresholds',
        'maximum_limits',
        'special_rules',
        'industry_specific_rules',
        'seasonal_adjustments',
        'compliance_requirements',
        'documentation_requirements',
        'audit_requirements',
        'appeal_process',
        'payment_methods',
        'electronic_filing_mandatory',
        'api_integration_config',
        'validation_rules',
        'reporting_templates',
        'is_active',
        'effective_from',
        'effective_until',
        'last_updated_by_authority',
    ];

    protected $casts = [
        'tax_rates' => 'array',
        'tax_brackets' => 'array',
        'exemptions' => 'array',
        'deductions' => 'array',
        'filing_deadlines' => 'array',
        'penalty_structure' => 'array',
        'interest_rates' => 'array',
        'minimum_thresholds' => 'array',
        'maximum_limits' => 'array',
        'special_rules' => 'array',
        'industry_specific_rules' => 'array',
        'seasonal_adjustments' => 'array',
        'compliance_requirements' => 'array',
        'documentation_requirements' => 'array',
        'audit_requirements' => 'array',
        'appeal_process' => 'array',
        'payment_methods' => 'array',
        'api_integration_config' => 'array',
        'validation_rules' => 'array',
        'reporting_templates' => 'array',
        'electronic_filing_mandatory' => 'boolean',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_until' => 'date',
        'last_updated_by_authority' => 'datetime',
    ];

    /**
     * أنواع الضرائب المدعومة
     */
    const TAX_TYPES = [
        'vat' => 'ضريبة القيمة المضافة',
        'sales_tax' => 'ضريبة المبيعات',
        'corporate_tax' => 'ضريبة الشركات',
        'income_tax' => 'ضريبة الدخل',
        'zakat' => 'الزكاة',
        'excise_tax' => 'ضريبة السلع الانتقائية',
        'customs_duty' => 'الرسوم الجمركية',
        'property_tax' => 'ضريبة العقارات',
        'capital_gains_tax' => 'ضريبة أرباح رؤوس الأموال',
        'withholding_tax' => 'ضريبة الاستقطاع',
        'stamp_duty' => 'رسم الطابع',
        'municipal_tax' => 'الضريبة البلدية',
    ];

    /**
     * طرق الحساب
     */
    const CALCULATION_METHODS = [
        'percentage' => 'نسبة مئوية',
        'progressive' => 'تدريجي',
        'flat_rate' => 'معدل ثابت',
        'tiered' => 'متدرج',
        'compound' => 'مركب',
        'reverse_charge' => 'الشحن العكسي',
    ];

    /**
     * تكرار التقديم
     */
    const FILING_FREQUENCIES = [
        'monthly' => 'شهري',
        'quarterly' => 'ربع سنوي',
        'semi_annual' => 'نصف سنوي',
        'annual' => 'سنوي',
        'on_demand' => 'عند الطلب',
        'real_time' => 'فوري',
    ];

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع حسابات الضرائب
     */
    public function taxAccounts(): HasMany
    {
        return $this->hasMany(TaxAccount::class);
    }

    /**
     * العلاقة مع إقرارات الضرائب
     */
    public function taxReturns(): HasMany
    {
        return $this->hasMany(TaxReturn::class);
    }

    /**
     * العلاقة مع معاملات الضرائب
     */
    public function taxTransactions(): HasMany
    {
        return $this->hasMany(TaxTransaction::class);
    }

    /**
     * حساب الضريبة بناءً على المبلغ والقواعد
     */
    public function calculateTax(float $amount, array $context = []): array
    {
        $calculation = [
            'base_amount' => $amount,
            'tax_amount' => 0,
            'effective_rate' => 0,
            'breakdown' => [],
            'exemptions_applied' => [],
            'deductions_applied' => [],
            'method_used' => $this->calculation_method,
        ];

        // تطبيق الإعفاءات
        $exemptionResult = $this->applyExemptions($amount, $context);
        if ($exemptionResult['is_exempt']) {
            $calculation['exemptions_applied'] = $exemptionResult['exemptions'];
            return $calculation;
        }

        // تطبيق الخصومات
        $deductionResult = $this->applyDeductions($amount, $context);
        $taxableAmount = $deductionResult['taxable_amount'];
        $calculation['deductions_applied'] = $deductionResult['deductions'];

        // حساب الضريبة حسب الطريقة
        switch ($this->calculation_method) {
            case 'percentage':
                $calculation = $this->calculatePercentageTax($taxableAmount, $calculation, $context);
                break;
            case 'progressive':
                $calculation = $this->calculateProgressiveTax($taxableAmount, $calculation, $context);
                break;
            case 'tiered':
                $calculation = $this->calculateTieredTax($taxableAmount, $calculation, $context);
                break;
            case 'flat_rate':
                $calculation = $this->calculateFlatRateTax($taxableAmount, $calculation, $context);
                break;
            default:
                $calculation = $this->calculatePercentageTax($taxableAmount, $calculation, $context);
        }

        // تطبيق الحدود الدنيا والعليا
        $calculation = $this->applyLimits($calculation);

        return $calculation;
    }

    /**
     * تطبيق الإعفاءات
     */
    protected function applyExemptions(float $amount, array $context): array
    {
        $exemptions = $this->exemptions ?? [];
        $appliedExemptions = [];
        $isExempt = false;

        foreach ($exemptions as $exemption) {
            if ($this->checkExemptionConditions($exemption, $amount, $context)) {
                $appliedExemptions[] = $exemption;
                if ($exemption['type'] === 'full_exemption') {
                    $isExempt = true;
                    break;
                }
            }
        }

        return [
            'is_exempt' => $isExempt,
            'exemptions' => $appliedExemptions,
        ];
    }

    /**
     * تطبيق الخصومات
     */
    protected function applyDeductions(float $amount, array $context): array
    {
        $deductions = $this->deductions ?? [];
        $appliedDeductions = [];
        $totalDeduction = 0;

        foreach ($deductions as $deduction) {
            if ($this->checkDeductionConditions($deduction, $amount, $context)) {
                $deductionAmount = $this->calculateDeductionAmount($deduction, $amount);
                $appliedDeductions[] = array_merge($deduction, ['amount' => $deductionAmount]);
                $totalDeduction += $deductionAmount;
            }
        }

        return [
            'taxable_amount' => max(0, $amount - $totalDeduction),
            'total_deduction' => $totalDeduction,
            'deductions' => $appliedDeductions,
        ];
    }

    /**
     * حساب الضريبة بالنسبة المئوية
     */
    protected function calculatePercentageTax(float $amount, array $calculation, array $context): array
    {
        $rate = $this->getApplicableRate($context);
        $taxAmount = $amount * ($rate / 100);

        $calculation['tax_amount'] = $taxAmount;
        $calculation['effective_rate'] = $rate;
        $calculation['breakdown'] = [
            'rate_applied' => $rate,
            'calculation' => "{$amount} × {$rate}% = {$taxAmount}",
        ];

        return $calculation;
    }

    /**
     * حساب الضريبة التدريجية
     */
    protected function calculateProgressiveTax(float $amount, array $calculation, array $context): array
    {
        $brackets = $this->tax_brackets ?? [];
        $totalTax = 0;
        $breakdown = [];
        $remainingAmount = $amount;

        foreach ($brackets as $bracket) {
            if ($remainingAmount <= 0) break;

            $bracketMin = $bracket['min'] ?? 0;
            $bracketMax = $bracket['max'] ?? PHP_FLOAT_MAX;
            $bracketRate = $bracket['rate'] ?? 0;

            if ($amount > $bracketMin) {
                $taxableInBracket = min($remainingAmount, $bracketMax - $bracketMin);
                $taxInBracket = $taxableInBracket * ($bracketRate / 100);
                $totalTax += $taxInBracket;

                $breakdown[] = [
                    'bracket' => "{$bracketMin} - {$bracketMax}",
                    'rate' => $bracketRate,
                    'taxable_amount' => $taxableInBracket,
                    'tax_amount' => $taxInBracket,
                ];

                $remainingAmount -= $taxableInBracket;
            }
        }

        $calculation['tax_amount'] = $totalTax;
        $calculation['effective_rate'] = $amount > 0 ? ($totalTax / $amount) * 100 : 0;
        $calculation['breakdown'] = $breakdown;

        return $calculation;
    }

    /**
     * حساب الضريبة المتدرجة
     */
    protected function calculateTieredTax(float $amount, array $calculation, array $context): array
    {
        $tiers = $this->tax_rates['tiers'] ?? [];
        $applicableTier = null;

        foreach ($tiers as $tier) {
            $min = $tier['min'] ?? 0;
            $max = $tier['max'] ?? PHP_FLOAT_MAX;

            if ($amount >= $min && $amount <= $max) {
                $applicableTier = $tier;
                break;
            }
        }

        if ($applicableTier) {
            $rate = $applicableTier['rate'];
            $taxAmount = $amount * ($rate / 100);

            $calculation['tax_amount'] = $taxAmount;
            $calculation['effective_rate'] = $rate;
            $calculation['breakdown'] = [
                'tier_applied' => $applicableTier,
                'calculation' => "{$amount} × {$rate}% = {$taxAmount}",
            ];
        }

        return $calculation;
    }

    /**
     * حساب الضريبة بالمعدل الثابت
     */
    protected function calculateFlatRateTax(float $amount, array $calculation, array $context): array
    {
        $flatRate = $this->tax_rates['flat_rate'] ?? 0;
        $taxAmount = $flatRate;

        $calculation['tax_amount'] = $taxAmount;
        $calculation['effective_rate'] = $amount > 0 ? ($taxAmount / $amount) * 100 : 0;
        $calculation['breakdown'] = [
            'flat_rate' => $flatRate,
            'calculation' => "معدل ثابت = {$flatRate}",
        ];

        return $calculation;
    }

    /**
     * الحصول على المعدل المطبق
     */
    protected function getApplicableRate(array $context): float
    {
        $rates = $this->tax_rates;

        // تحديد المعدل بناءً على السياق
        if (isset($context['category']) && isset($rates[$context['category']])) {
            return $rates[$context['category']];
        }

        if (isset($context['industry']) && isset($this->industry_specific_rules[$context['industry']])) {
            return $this->industry_specific_rules[$context['industry']]['rate'] ?? $rates['standard'];
        }

        return $rates['standard'] ?? 0;
    }

    /**
     * تطبيق الحدود
     */
    protected function applyLimits(array $calculation): array
    {
        $minThreshold = $this->minimum_thresholds['tax_amount'] ?? 0;
        $maxLimit = $this->maximum_limits['tax_amount'] ?? PHP_FLOAT_MAX;

        if ($calculation['tax_amount'] < $minThreshold) {
            $calculation['tax_amount'] = 0;
            $calculation['limit_applied'] = 'minimum_threshold';
        } elseif ($calculation['tax_amount'] > $maxLimit) {
            $calculation['tax_amount'] = $maxLimit;
            $calculation['limit_applied'] = 'maximum_limit';
        }

        return $calculation;
    }

    /**
     * التحقق من شروط الإعفاء
     */
    protected function checkExemptionConditions(array $exemption, float $amount, array $context): bool
    {
        $conditions = $exemption['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if (!$this->evaluateCondition($condition, $amount, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * التحقق من شروط الخصم
     */
    protected function checkDeductionConditions(array $deduction, float $amount, array $context): bool
    {
        $conditions = $deduction['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if (!$this->evaluateCondition($condition, $amount, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * تقييم الشرط
     */
    protected function evaluateCondition(array $condition, float $amount, array $context): bool
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];

        $actualValue = match ($field) {
            'amount' => $amount,
            'date' => now(),
            default => $context[$field] ?? null,
        };

        return match ($operator) {
            '=' => $actualValue == $value,
            '!=' => $actualValue != $value,
            '>' => $actualValue > $value,
            '>=' => $actualValue >= $value,
            '<' => $actualValue < $value,
            '<=' => $actualValue <= $value,
            'in' => in_array($actualValue, (array) $value),
            'not_in' => !in_array($actualValue, (array) $value),
            'contains' => str_contains($actualValue, $value),
            default => false,
        };
    }

    /**
     * حساب مبلغ الخصم
     */
    protected function calculateDeductionAmount(array $deduction, float $amount): float
    {
        $type = $deduction['type'] ?? 'percentage';
        $value = $deduction['value'] ?? 0;

        return match ($type) {
            'percentage' => $amount * ($value / 100),
            'fixed' => $value,
            'progressive' => $this->calculateProgressiveDeduction($deduction, $amount),
            default => 0,
        };
    }

    /**
     * حساب الخصم التدريجي
     */
    protected function calculateProgressiveDeduction(array $deduction, float $amount): float
    {
        $brackets = $deduction['brackets'] ?? [];
        $totalDeduction = 0;

        foreach ($brackets as $bracket) {
            $min = $bracket['min'] ?? 0;
            $max = $bracket['max'] ?? PHP_FLOAT_MAX;
            $rate = $bracket['rate'] ?? 0;

            if ($amount > $min) {
                $deductibleAmount = min($amount - $min, $max - $min);
                $totalDeduction += $deductibleAmount * ($rate / 100);
            }
        }

        return $totalDeduction;
    }

    /**
     * التحقق من موعد التقديم القادم
     */
    public function getNextFilingDeadline(): ?Carbon
    {
        $deadlines = $this->filing_deadlines ?? [];
        $frequency = $this->filing_frequency;

        $now = now();
        $nextDeadline = null;

        switch ($frequency) {
            case 'monthly':
                $nextDeadline = $now->copy()->addMonth()->day($deadlines['day'] ?? 20);
                break;
            case 'quarterly':
                $nextQuarter = $now->copy()->addMonths(3 - ($now->month % 3));
                $nextDeadline = $nextQuarter->day($deadlines['day'] ?? 20);
                break;
            case 'annual':
                $nextDeadline = $now->copy()->addYear()->month($deadlines['month'] ?? 3)->day($deadlines['day'] ?? 31);
                break;
        }

        return $nextDeadline;
    }

    /**
     * التحقق من التأخير في التقديم
     */
    public function isFilingOverdue(): bool
    {
        $deadline = $this->getNextFilingDeadline();
        return $deadline && $deadline->isPast();
    }

    /**
     * حساب الغرامة والفوائد
     */
    public function calculatePenaltyAndInterest(float $taxAmount, Carbon $dueDate): array
    {
        $penaltyStructure = $this->penalty_structure ?? [];
        $interestRates = $this->interest_rates ?? [];

        $daysLate = max(0, now()->diffInDays($dueDate));
        $penalty = 0;
        $interest = 0;

        if ($daysLate > 0) {
            // حساب الغرامة
            $penaltyRate = $penaltyStructure['rate'] ?? 0;
            $penaltyMin = $penaltyStructure['minimum'] ?? 0;
            $penaltyMax = $penaltyStructure['maximum'] ?? PHP_FLOAT_MAX;

            $penalty = max($penaltyMin, min($penaltyMax, $taxAmount * ($penaltyRate / 100)));

            // حساب الفوائد
            $interestRate = $interestRates['annual_rate'] ?? 0;
            $dailyRate = $interestRate / 365 / 100;
            $interest = $taxAmount * $dailyRate * $daysLate;
        }

        return [
            'penalty' => $penalty,
            'interest' => $interest,
            'total_additional' => $penalty + $interest,
            'days_late' => $daysLate,
        ];
    }

    /**
     * التحقق من صحة البيانات الضريبية
     */
    public function validateTaxData(array $data): array
    {
        $validationRules = $this->validation_rules ?? [];
        $errors = [];

        foreach ($validationRules as $field => $rules) {
            $value = $data[$field] ?? null;

            foreach ($rules as $rule) {
                if (!$this->validateRule($value, $rule)) {
                    $errors[$field][] = $rule['message'] ?? "فشل في التحقق من {$field}";
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * التحقق من قاعدة واحدة
     */
    protected function validateRule($value, array $rule): bool
    {
        $type = $rule['type'];

        return match ($type) {
            'required' => !empty($value),
            'numeric' => is_numeric($value),
            'min' => $value >= $rule['value'],
            'max' => $value <= $rule['value'],
            'regex' => preg_match($rule['pattern'], $value),
            'in' => in_array($value, $rule['values']),
            default => true,
        };
    }

    /**
     * Scope للضرائب النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('effective_until')
                  ->orWhere('effective_until', '>=', now());
            });
    }

    /**
     * Scope للضرائب حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('tax_type', $type);
    }

    /**
     * Scope للضرائب المطلوب تقديمها
     */
    public function scopeDueForFiling($query)
    {
        return $query->active()
            ->where('electronic_filing_mandatory', true);
    }
}
