<?php

namespace App\Domains\Compliance\Services;

use App\Domains\Compliance\Models\Country;
use App\Domains\Compliance\Models\SocialSecurityConfiguration;
use App\Domains\Compliance\Models\SocialSecurityContribution;
use App\Domains\Compliance\Models\SocialSecurityReport;
use App\Models\Company;
use App\Models\Employee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة الضمان الاجتماعي المتقدمة
 * تدير جميع عمليات الضمان الاجتماعي لجميع الدول
 */
class SocialSecurityService
{
    /**
     * حساب مساهمات الضمان الاجتماعي للموظف
     */
    public function calculateEmployeeContributions(
        Employee $employee,
        float $salary,
        Carbon $month,
        array $additionalData = []
    ): array {
        $country = Country::where('code', $employee->country_code)->first();
        if (!$country) {
            throw new \Exception('الدولة غير مدعومة');
        }

        $socialSecuritySystems = $country->socialSecuritySystems()->active()->get();
        $totalContributions = [];
        $employerTotal = 0;
        $employeeTotal = 0;

        foreach ($socialSecuritySystems as $system) {
            $employeeData = array_merge([
                'employee_id' => $employee->id,
                'nationality' => $employee->nationality,
                'employment_type' => $employee->employment_type,
                'years_of_service' => $employee->years_of_service,
                'age' => $employee->age,
                'gender' => $employee->gender,
                'marital_status' => $employee->marital_status,
                'dependents_count' => $employee->dependents_count,
            ], $additionalData);

            $contribution = $system->calculateContributions($salary, $employeeData);
            
            if ($contribution['total_contribution'] > 0) {
                $totalContributions[$system->system_code] = $contribution;
                $employerTotal += $contribution['employer_contribution'];
                $employeeTotal += $contribution['employee_contribution'];

                // حفظ المساهمة في قاعدة البيانات
                $this->saveSocialSecurityContribution(
                    $employee,
                    $system,
                    $month,
                    $salary,
                    $contribution
                );
            }
        }

        return [
            'employee_id' => $employee->id,
            'month' => $month->format('Y-m'),
            'gross_salary' => $salary,
            'total_employer_contribution' => $employerTotal,
            'total_employee_contribution' => $employeeTotal,
            'total_contribution' => $employerTotal + $employeeTotal,
            'contributions_by_system' => $totalContributions,
            'currency' => $country->currency,
            'calculated_at' => now(),
        ];
    }

    /**
     * حساب مساهمات جميع الموظفين للشهر
     */
    public function calculateMonthlyContributions(
        Company $company,
        Carbon $month,
        array $filters = []
    ): array {
        $employees = $company->employees()
            ->active()
            ->when(isset($filters['department']), fn($q) => $q->where('department', $filters['department']))
            ->when(isset($filters['country']), fn($q) => $q->where('country_code', $filters['country']))
            ->get();

        $results = [];
        $summary = [
            'total_employees' => 0,
            'total_gross_salary' => 0,
            'total_employer_contribution' => 0,
            'total_employee_contribution' => 0,
            'total_contribution' => 0,
            'breakdown_by_country' => [],
            'breakdown_by_system' => [],
        ];

        foreach ($employees as $employee) {
            $salary = $this->getEmployeeSalaryForMonth($employee, $month);
            if ($salary > 0) {
                $contribution = $this->calculateEmployeeContributions($employee, $salary, $month);
                $results[] = $contribution;

                // تحديث الملخص
                $summary['total_employees']++;
                $summary['total_gross_salary'] += $salary;
                $summary['total_employer_contribution'] += $contribution['total_employer_contribution'];
                $summary['total_employee_contribution'] += $contribution['total_employee_contribution'];
                $summary['total_contribution'] += $contribution['total_contribution'];

                // تجميع حسب الدولة
                $countryCode = $employee->country_code;
                if (!isset($summary['breakdown_by_country'][$countryCode])) {
                    $summary['breakdown_by_country'][$countryCode] = [
                        'employee_count' => 0,
                        'total_salary' => 0,
                        'total_contribution' => 0,
                    ];
                }
                $summary['breakdown_by_country'][$countryCode]['employee_count']++;
                $summary['breakdown_by_country'][$countryCode]['total_salary'] += $salary;
                $summary['breakdown_by_country'][$countryCode]['total_contribution'] += $contribution['total_contribution'];

                // تجميع حسب النظام
                foreach ($contribution['contributions_by_system'] as $systemCode => $systemContribution) {
                    if (!isset($summary['breakdown_by_system'][$systemCode])) {
                        $summary['breakdown_by_system'][$systemCode] = [
                            'employee_count' => 0,
                            'total_contribution' => 0,
                            'employer_contribution' => 0,
                            'employee_contribution' => 0,
                        ];
                    }
                    $summary['breakdown_by_system'][$systemCode]['employee_count']++;
                    $summary['breakdown_by_system'][$systemCode]['total_contribution'] += $systemContribution['total_contribution'];
                    $summary['breakdown_by_system'][$systemCode]['employer_contribution'] += $systemContribution['employer_contribution'];
                    $summary['breakdown_by_system'][$systemCode]['employee_contribution'] += $systemContribution['employee_contribution'];
                }
            }
        }

        return [
            'company_id' => $company->id,
            'month' => $month->format('Y-m'),
            'summary' => $summary,
            'employee_contributions' => $results,
            'calculated_at' => now(),
        ];
    }

    /**
     * إنشاء تقرير الضمان الاجتماعي
     */
    public function generateSocialSecurityReport(
        Company $company,
        SocialSecurityConfiguration $system,
        Carbon $month,
        string $reportType = 'monthly'
    ): SocialSecurityReport {
        $contributions = SocialSecurityContribution::where('company_id', $company->id)
            ->where('social_security_configuration_id', $system->id)
            ->where('contribution_month', $month->format('Y-m'))
            ->with('employee')
            ->get();

        $reportData = $this->prepareReportData($contributions, $system, $reportType);
        $reportContent = $this->generateReportContent($reportData, $system, $reportType);

        $report = SocialSecurityReport::create([
            'report_id' => $this->generateReportId($company, $system, $month, $reportType),
            'company_id' => $company->id,
            'social_security_configuration_id' => $system->id,
            'country_id' => $system->country_id,
            'report_type' => $reportType,
            'period_start' => $month->startOfMonth(),
            'period_end' => $month->endOfMonth(),
            'due_date' => $this->calculateReportDueDate($system, $month),
            'employee_count' => $contributions->count(),
            'total_salary' => $contributions->sum('gross_salary'),
            'total_employer_contribution' => $contributions->sum('employer_contribution'),
            'total_employee_contribution' => $contributions->sum('employee_contribution'),
            'total_contribution' => $contributions->sum('total_contribution'),
            'currency' => $system->country->currency,
            'report_data' => $reportData,
            'report_content' => $reportContent,
            'status' => 'generated',
        ]);

        return $report;
    }

    /**
     * تقديم تقرير الضمان الاجتماعي
     */
    public function submitSocialSecurityReport(SocialSecurityReport $report): array
    {
        if ($report->status !== 'approved') {
            throw new \Exception('يجب اعتماد التقرير قبل التقديم');
        }

        $system = $report->socialSecurityConfiguration;
        $submissionData = $this->prepareSubmissionData($report);

        // إرسال للسلطة المختصة
        $response = $this->sendToSocialSecurityAuthority($system, $submissionData);

        if ($response['success']) {
            $report->update([
                'status' => 'submitted',
                'submission_reference' => $response['reference'],
                'government_reference' => $response['government_reference'] ?? null,
                'submitted_at' => now(),
            ]);

            $this->logReportActivity($report, 'submitted', $response);
        }

        return $response;
    }

    /**
     * فحص أهلية الاستفادة
     */
    public function checkBenefitEligibility(
        Employee $employee,
        string $benefitType,
        array $additionalData = []
    ): array {
        $country = Country::where('code', $employee->country_code)->first();
        $socialSecuritySystems = $country->socialSecuritySystems()
            ->withCoverage($benefitType)
            ->active()
            ->get();

        $eligibilityResults = [];

        foreach ($socialSecuritySystems as $system) {
            $employeeData = array_merge([
                'employee_id' => $employee->id,
                'years_of_service' => $employee->years_of_service,
                'age' => $employee->age,
                'contribution_history' => $this->getEmployeeContributionHistory($employee, $system),
                'current_employment_status' => $employee->employment_status,
            ], $additionalData);

            $eligibility = $system->checkBenefitEligibility($employeeData, $benefitType);
            $eligibilityResults[$system->system_code] = $eligibility;
        }

        return [
            'employee_id' => $employee->id,
            'benefit_type' => $benefitType,
            'eligibility_by_system' => $eligibilityResults,
            'overall_eligible' => collect($eligibilityResults)->contains('is_eligible', true),
            'checked_at' => now(),
        ];
    }

    /**
     * حساب مبلغ الاستفادة
     */
    public function calculateBenefitAmount(
        Employee $employee,
        string $benefitType,
        array $additionalData = []
    ): array {
        $eligibility = $this->checkBenefitEligibility($employee, $benefitType, $additionalData);
        
        if (!$eligibility['overall_eligible']) {
            return [
                'eligible' => false,
                'message' => 'الموظف غير مؤهل لهذه الاستفادة',
            ];
        }

        $country = Country::where('code', $employee->country_code)->first();
        $socialSecuritySystems = $country->socialSecuritySystems()
            ->withCoverage($benefitType)
            ->active()
            ->get();

        $benefitCalculations = [];
        $totalBenefit = 0;

        foreach ($socialSecuritySystems as $system) {
            if ($eligibility['eligibility_by_system'][$system->system_code]['is_eligible']) {
                $employeeData = array_merge([
                    'salary' => $employee->current_salary,
                    'years_of_service' => $employee->years_of_service,
                    'contribution_history' => $this->getEmployeeContributionHistory($employee, $system),
                ], $additionalData);

                $benefitCalculation = $system->calculateBenefitAmount($employeeData, $benefitType);
                $benefitCalculations[$system->system_code] = $benefitCalculation;
                $totalBenefit += $benefitCalculation['amount'];
            }
        }

        return [
            'employee_id' => $employee->id,
            'benefit_type' => $benefitType,
            'eligible' => true,
            'total_benefit_amount' => $totalBenefit,
            'currency' => $country->currency,
            'benefit_breakdown' => $benefitCalculations,
            'calculated_at' => now(),
        ];
    }

    /**
     * الحصول على ملخص الضمان الاجتماعي للدولة
     */
    public function getSummary(Country $country): array
    {
        $cacheKey = "social_security_summary_{$country->code}";
        
        return Cache::remember($cacheKey, 1800, function () use ($country) {
            $systems = $country->socialSecuritySystems()->active()->get();
            $summary = [];

            foreach ($systems as $system) {
                $recentContributions = SocialSecurityContribution::where('social_security_configuration_id', $system->id)
                    ->where('contribution_month', '>=', now()->subMonths(3)->format('Y-m'))
                    ->get();

                $summary[$system->system_code] = [
                    'system_name' => $system->system_name_ar,
                    'total_rate' => $system->contribution_rates['total_rate'] ?? 0,
                    'employer_rate' => $system->contribution_rates['employer_rate'] ?? 0,
                    'employee_rate' => $system->contribution_rates['employee_rate'] ?? 0,
                    'recent_statistics' => [
                        'total_employees' => $recentContributions->unique('employee_id')->count(),
                        'total_contributions' => $recentContributions->sum('total_contribution'),
                        'average_salary' => $recentContributions->avg('gross_salary'),
                        'last_month_contributions' => $recentContributions
                            ->where('contribution_month', now()->subMonth()->format('Y-m'))
                            ->sum('total_contribution'),
                    ],
                    'coverage_types' => $system->coverage_types,
                    'next_filing_deadline' => $system->getNextFilingDeadline(),
                    'compliance_status' => $this->getSystemComplianceStatus($system),
                ];
            }

            return [
                'country' => $country->name_ar,
                'systems' => $summary,
                'overall_statistics' => $this->calculateOverallStatistics($country),
                'upcoming_deadlines' => $this->getUpcomingDeadlines($country),
                'compliance_alerts' => $this->getComplianceAlerts($country),
            ];
        });
    }

    /**
     * الحصول على إجمالي المساهمات
     */
    public function getTotalContributions(string $countryCode, string $systemCode = null): float
    {
        $query = SocialSecurityContribution::whereHas('socialSecurityConfiguration', function ($q) use ($countryCode) {
            $q->whereHas('country', fn($q2) => $q2->where('code', $countryCode));
        });

        if ($systemCode) {
            $query->whereHas('socialSecurityConfiguration', fn($q) => $q->where('system_code', $systemCode));
        }

        return $query->sum('total_contribution');
    }

    /**
     * الحصول على عدد الموظفين النشطين
     */
    public function getActiveEmployeeCount(string $countryCode): int
    {
        return SocialSecurityContribution::whereHas('socialSecurityConfiguration', function ($q) use ($countryCode) {
            $q->whereHas('country', fn($q2) => $q2->where('code', $countryCode));
        })
        ->where('contribution_month', now()->format('Y-m'))
        ->distinct('employee_id')
        ->count();
    }

    /**
     * الحصول على موعد التقديم القادم
     */
    public function getNextFilingDeadline(string $countryCode, string $systemCode): ?Carbon
    {
        $system = SocialSecurityConfiguration::whereHas('country', fn($q) => $q->where('code', $countryCode))
            ->where('system_code', $systemCode)
            ->first();

        return $system?->getNextFilingDeadline();
    }

    /**
     * حفظ مساهمة الضمان الاجتماعي
     */
    protected function saveSocialSecurityContribution(
        Employee $employee,
        SocialSecurityConfiguration $system,
        Carbon $month,
        float $salary,
        array $contribution
    ): SocialSecurityContribution {
        return SocialSecurityContribution::updateOrCreate([
            'employee_id' => $employee->id,
            'company_id' => $employee->company_id,
            'social_security_configuration_id' => $system->id,
            'contribution_month' => $month->format('Y-m'),
        ], [
            'gross_salary' => $salary,
            'contributory_salary' => $contribution['contributory_salary'],
            'employer_contribution' => $contribution['employer_contribution'],
            'employee_contribution' => $contribution['employee_contribution'],
            'total_contribution' => $contribution['total_contribution'],
            'contribution_breakdown' => $contribution['breakdown'],
            'exemptions_applied' => $contribution['exemptions_applied'],
            'caps_applied' => $contribution['caps_applied'],
            'currency' => $system->country->currency,
            'status' => 'calculated',
        ]);
    }

    /**
     * الحصول على راتب الموظف للشهر
     */
    protected function getEmployeeSalaryForMonth(Employee $employee, Carbon $month): float
    {
        // منطق الحصول على الراتب من نظام الرواتب
        return $employee->current_salary ?? 0;
    }

    /**
     * تحضير بيانات التقرير
     */
    protected function prepareReportData(Collection $contributions, SocialSecurityConfiguration $system, string $reportType): array
    {
        $countryCode = $system->country->code;
        
        return match ($countryCode) {
            'MA' => $this->prepareMoroccanReportData($contributions, $system, $reportType),
            'SA' => $this->prepareSaudiReportData($contributions, $system, $reportType),
            'AE' => $this->prepareUAEReportData($contributions, $system, $reportType),
            default => $this->prepareGenericReportData($contributions, $system, $reportType),
        };
    }

    /**
     * تحضير بيانات التقرير المغربي
     */
    protected function prepareMoroccanReportData(Collection $contributions, SocialSecurityConfiguration $system, string $reportType): array
    {
        if ($system->system_code === 'CNSS') {
            return [
                'employer_info' => [
                    'cnss_number' => $contributions->first()?->company->cnss_number,
                    'company_name' => $contributions->first()?->company->name,
                    'activity_code' => $contributions->first()?->company->activity_code,
                ],
                'employees' => $contributions->map(function ($contribution) {
                    return [
                        'employee_number' => $contribution->employee->employee_number,
                        'cin' => $contribution->employee->national_id,
                        'full_name' => $contribution->employee->full_name,
                        'gross_salary' => $contribution->gross_salary,
                        'cnss_salary' => $contribution->contributory_salary,
                        'cnss_contribution' => $contribution->total_contribution,
                        'days_worked' => $contribution->days_worked ?? 30,
                    ];
                })->toArray(),
                'summary' => [
                    'total_employees' => $contributions->count(),
                    'total_salary' => $contributions->sum('gross_salary'),
                    'total_cnss_salary' => $contributions->sum('contributory_salary'),
                    'total_contribution' => $contributions->sum('total_contribution'),
                ],
            ];
        }

        return $this->prepareGenericReportData($contributions, $system, $reportType);
    }

    /**
     * توليد محتوى التقرير
     */
    protected function generateReportContent(array $reportData, SocialSecurityConfiguration $system, string $reportType): array
    {
        $countryCode = $system->country->code;
        
        return match ($countryCode) {
            'MA' => $this->generateMoroccanReportContent($reportData, $system, $reportType),
            'SA' => $this->generateSaudiReportContent($reportData, $system, $reportType),
            default => $this->generateGenericReportContent($reportData, $system, $reportType),
        };
    }

    /**
     * حساب تاريخ استحقاق التقرير
     */
    protected function calculateReportDueDate(SocialSecurityConfiguration $system, Carbon $month): Carbon
    {
        $deadlines = $system->filing_deadlines ?? [];
        $daysAfterMonth = $deadlines['days_after_month'] ?? 15;
        
        return $month->copy()->endOfMonth()->addDays($daysAfterMonth);
    }

    /**
     * توليد معرف التقرير
     */
    protected function generateReportId(Company $company, SocialSecurityConfiguration $system, Carbon $month, string $reportType): string
    {
        $countryCode = $system->country->code;
        $systemCode = $system->system_code;
        $period = $month->format('Ym');
        $companyCode = substr($company->registration_number ?? $company->id, -4);
        
        return "{$countryCode}_{$systemCode}_{$reportType}_{$period}_{$companyCode}";
    }

    /**
     * تحضير بيانات الإرسال
     */
    protected function prepareSubmissionData(SocialSecurityReport $report): array
    {
        return [
            'report_id' => $report->report_id,
            'company_registration' => $report->company->registration_number,
            'period_start' => $report->period_start->format('Y-m-d'),
            'period_end' => $report->period_end->format('Y-m-d'),
            'employee_count' => $report->employee_count,
            'total_salary' => $report->total_salary,
            'total_contribution' => $report->total_contribution,
            'report_data' => $report->report_data,
            'currency' => $report->currency,
        ];
    }

    /**
     * إرسال للسلطة المختصة
     */
    protected function sendToSocialSecurityAuthority(SocialSecurityConfiguration $system, array $data): array
    {
        // محاكاة إرسال API
        return [
            'success' => true,
            'reference' => 'SS_' . strtoupper(uniqid()),
            'government_reference' => $system->system_code . '_' . strtoupper(uniqid()),
            'status' => 'received',
            'message' => 'تم استلام التقرير بنجاح',
            'processing_time' => '3-7 أيام عمل',
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * تسجيل نشاط التقرير
     */
    protected function logReportActivity(SocialSecurityReport $report, string $activity, array $data = []): void
    {
        // منطق تسجيل النشاط
    }

    /**
     * الحصول على تاريخ مساهمات الموظف
     */
    protected function getEmployeeContributionHistory(Employee $employee, SocialSecurityConfiguration $system): array
    {
        return SocialSecurityContribution::where('employee_id', $employee->id)
            ->where('social_security_configuration_id', $system->id)
            ->orderBy('contribution_month', 'desc')
            ->limit(12)
            ->get()
            ->map(function ($contribution) {
                return [
                    'month' => $contribution->contribution_month,
                    'salary' => $contribution->gross_salary,
                    'contribution' => $contribution->total_contribution,
                ];
            })
            ->toArray();
    }

    // طرق مساعدة إضافية
    protected function prepareSaudiReportData(Collection $contributions, SocialSecurityConfiguration $system, string $reportType): array
    {
        return $this->prepareGenericReportData($contributions, $system, $reportType);
    }

    protected function prepareUAEReportData(Collection $contributions, SocialSecurityConfiguration $system, string $reportType): array
    {
        return $this->prepareGenericReportData($contributions, $system, $reportType);
    }

    protected function prepareGenericReportData(Collection $contributions, SocialSecurityConfiguration $system, string $reportType): array
    {
        return [
            'employees' => $contributions->toArray(),
            'summary' => [
                'total_employees' => $contributions->count(),
                'total_salary' => $contributions->sum('gross_salary'),
                'total_contribution' => $contributions->sum('total_contribution'),
            ],
        ];
    }

    protected function generateMoroccanReportContent(array $reportData, SocialSecurityConfiguration $system, string $reportType): array
    {
        return ['content' => 'Moroccan report content'];
    }

    protected function generateSaudiReportContent(array $reportData, SocialSecurityConfiguration $system, string $reportType): array
    {
        return ['content' => 'Saudi report content'];
    }

    protected function generateGenericReportContent(array $reportData, SocialSecurityConfiguration $system, string $reportType): array
    {
        return ['content' => 'Generic report content'];
    }

    protected function getSystemComplianceStatus(SocialSecurityConfiguration $system): string
    {
        return 'compliant'; // منطق فحص الامتثال
    }

    protected function calculateOverallStatistics(Country $country): array
    {
        return []; // منطق حساب الإحصائيات العامة
    }

    protected function getUpcomingDeadlines(Country $country): array
    {
        return []; // منطق المواعيد القادمة
    }

    protected function getComplianceAlerts(Country $country): array
    {
        return []; // منطق تنبيهات الامتثال
    }
}
