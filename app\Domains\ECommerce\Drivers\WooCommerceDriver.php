<?php

namespace App\Domains\ECommerce\Drivers;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Exceptions\ECommerceApiException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * برنامج تشغيل WooCommerce
 * يدير التكامل مع منصة WooCommerce
 */
class WooCommerceDriver extends AbstractECommercePlatformDriver
{
    protected string $platformName = 'woocommerce';
    protected string $apiVersion = 'v3';
    protected int $maxPageSize = 100;
    protected int $defaultPageSize = 20;
    protected int $maxRequestsPerSecond = 10;
    protected int $maxRequestsPerMinute = 600;

    /**
     * الحصول على نقطة نهاية الاختبار
     */
    protected function getTestEndpoint(): string
    {
        return 'system_status';
    }

    /**
     * الحصول على URL الأساسي للAPI
     */
    public function getApiBaseUrl(ECommerceIntegration $integration): string
    {
        $siteUrl = $integration->authentication_config['site_url'] ?? '';
        return rtrim($siteUrl, '/') . '/wp-json/wc/' . $this->apiVersion;
    }

    /**
     * الحصول على headers المصادقة
     */
    protected function getAuthHeaders(ECommerceIntegration $integration): array
    {
        $consumerKey = $integration->authentication_config['consumer_key'] ?? '';
        $consumerSecret = $integration->authentication_config['consumer_secret'] ?? '';

        return [
            'Authorization' => 'Basic ' . base64_encode($consumerKey . ':' . $consumerSecret),
        ];
    }

    /**
     * الحصول على معلومات المتجر
     */
    public function getStoreInfo(ECommerceIntegration $integration): array
    {
        $response = $this->makeApiRequest('GET', 'system_status', [], $integration);
        return $response['settings'] ?? [];
    }

    /**
     * جلب المنتجات من WooCommerce
     */
    public function getProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'per_page' => $options['limit'] ?? $this->defaultPageSize,
            'status' => $options['status'] ?? 'any',
            'orderby' => 'date',
            'order' => 'desc',
        ];

        if (isset($options['page'])) {
            $params['page'] = $options['page'];
        }

        if (isset($options['search'])) {
            $params['search'] = $options['search'];
        }

        if (isset($options['category'])) {
            $params['category'] = $options['category'];
        }

        if (isset($options['modified_after'])) {
            $params['modified_after'] = $options['modified_after'];
        }

        $response = $this->makeApiRequest('GET', 'products', $params, $integration);
        return $response;
    }

    /**
     * جلب منتج واحد من WooCommerce
     */
    public function getProduct(ECommerceIntegration $integration, string $productId): array
    {
        $response = $this->makeApiRequest('GET', "products/{$productId}", [], $integration);
        return $response;
    }

    /**
     * إنشاء منتج في WooCommerce
     */
    public function createProduct(ECommerceIntegration $integration, array $productData): array
    {
        $data = $this->transformToExternalFormat($productData, 'product');
        $response = $this->makeApiRequest('POST', 'products', $data, $integration);
        return $response;
    }

    /**
     * تحديث منتج في WooCommerce
     */
    public function updateProduct(ECommerceIntegration $integration, string $productId, array $productData): array
    {
        $data = $this->transformToExternalFormat($productData, 'product');
        $response = $this->makeApiRequest('PUT', "products/{$productId}", $data, $integration);
        return $response;
    }

    /**
     * حذف منتج من WooCommerce
     */
    public function deleteProduct(ECommerceIntegration $integration, string $productId): array
    {
        $response = $this->makeApiRequest('DELETE', "products/{$productId}", ['force' => true], $integration);
        return $response;
    }

    /**
     * جلب الطلبات من WooCommerce
     */
    public function getOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'per_page' => $options['limit'] ?? $this->defaultPageSize,
            'status' => $options['status'] ?? 'any',
            'orderby' => 'date',
            'order' => 'desc',
        ];

        if (isset($options['page'])) {
            $params['page'] = $options['page'];
        }

        if (isset($options['after'])) {
            $params['after'] = $options['after'];
        }

        if (isset($options['before'])) {
            $params['before'] = $options['before'];
        }

        if (isset($options['modified_after'])) {
            $params['modified_after'] = $options['modified_after'];
        }

        $response = $this->makeApiRequest('GET', 'orders', $params, $integration);
        return $response;
    }

    /**
     * جلب طلب واحد من WooCommerce
     */
    public function getOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $response = $this->makeApiRequest('GET', "orders/{$orderId}", [], $integration);
        return $response;
    }

    /**
     * إنشاء طلب في WooCommerce
     */
    public function createOrder(ECommerceIntegration $integration, array $orderData): array
    {
        $data = $this->transformToExternalFormat($orderData, 'order');
        $response = $this->makeApiRequest('POST', 'orders', $data, $integration);
        return $response;
    }

    /**
     * تحديث طلب في WooCommerce
     */
    public function updateOrder(ECommerceIntegration $integration, string $orderId, array $orderData): array
    {
        $data = $this->transformToExternalFormat($orderData, 'order');
        $response = $this->makeApiRequest('PUT', "orders/{$orderId}", $data, $integration);
        return $response;
    }

    /**
     * إلغاء طلب في WooCommerce
     */
    public function cancelOrder(ECommerceIntegration $integration, string $orderId): array
    {
        $data = ['status' => 'cancelled'];
        $response = $this->makeApiRequest('PUT', "orders/{$orderId}", $data, $integration);
        return $response;
    }

    /**
     * جلب العملاء من WooCommerce
     */
    public function getCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'per_page' => $options['limit'] ?? $this->defaultPageSize,
            'orderby' => 'registered_date',
            'order' => 'desc',
        ];

        if (isset($options['page'])) {
            $params['page'] = $options['page'];
        }

        if (isset($options['search'])) {
            $params['search'] = $options['search'];
        }

        if (isset($options['email'])) {
            $params['email'] = $options['email'];
        }

        if (isset($options['role'])) {
            $params['role'] = $options['role'];
        }

        $response = $this->makeApiRequest('GET', 'customers', $params, $integration);
        return $response;
    }

    /**
     * جلب عميل واحد من WooCommerce
     */
    public function getCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $response = $this->makeApiRequest('GET', "customers/{$customerId}", [], $integration);
        return $response;
    }

    /**
     * إنشاء عميل في WooCommerce
     */
    public function createCustomer(ECommerceIntegration $integration, array $customerData): array
    {
        $data = $this->transformToExternalFormat($customerData, 'customer');
        $response = $this->makeApiRequest('POST', 'customers', $data, $integration);
        return $response;
    }

    /**
     * تحديث عميل في WooCommerce
     */
    public function updateCustomer(ECommerceIntegration $integration, string $customerId, array $customerData): array
    {
        $data = $this->transformToExternalFormat($customerData, 'customer');
        $response = $this->makeApiRequest('PUT', "customers/{$customerId}", $data, $integration);
        return $response;
    }

    /**
     * حذف عميل من WooCommerce
     */
    public function deleteCustomer(ECommerceIntegration $integration, string $customerId): array
    {
        $response = $this->makeApiRequest('DELETE', "customers/{$customerId}", ['force' => true], $integration);
        return $response;
    }

    /**
     * جلب الفئات من WooCommerce
     */
    public function getCategories(ECommerceIntegration $integration, array $options = []): array
    {
        $params = [
            'per_page' => $options['limit'] ?? 100,
            'orderby' => 'name',
            'order' => 'asc',
        ];

        if (isset($options['parent'])) {
            $params['parent'] = $options['parent'];
        }

        if (isset($options['search'])) {
            $params['search'] = $options['search'];
        }

        $response = $this->makeApiRequest('GET', 'products/categories', $params, $integration);
        return $response;
    }

    /**
     * جلب فئة واحدة من WooCommerce
     */
    public function getCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $response = $this->makeApiRequest('GET', "products/categories/{$categoryId}", [], $integration);
        return $response;
    }

    /**
     * إنشاء فئة في WooCommerce
     */
    public function createCategory(ECommerceIntegration $integration, array $categoryData): array
    {
        $data = $this->transformCategoryToWooCommerce($categoryData);
        $response = $this->makeApiRequest('POST', 'products/categories', $data, $integration);
        return $response;
    }

    /**
     * تحديث فئة في WooCommerce
     */
    public function updateCategory(ECommerceIntegration $integration, string $categoryId, array $categoryData): array
    {
        $data = $this->transformCategoryToWooCommerce($categoryData);
        $response = $this->makeApiRequest('PUT', "products/categories/{$categoryId}", $data, $integration);
        return $response;
    }

    /**
     * حذف فئة من WooCommerce
     */
    public function deleteCategory(ECommerceIntegration $integration, string $categoryId): array
    {
        $response = $this->makeApiRequest('DELETE', "products/categories/{$categoryId}", ['force' => true], $integration);
        return $response;
    }

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(ECommerceIntegration $integration, array $options = []): array
    {
        $products = $this->getProducts($integration, $options);

        return [
            'total' => count($products),
            'processed' => count($products),
            'successful' => count($products),
            'failed' => 0,
            'data' => $products,
        ];
    }

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(ECommerceIntegration $integration, array $options = []): array
    {
        $orders = $this->getOrders($integration, $options);

        return [
            'total' => count($orders),
            'processed' => count($orders),
            'successful' => count($orders),
            'failed' => 0,
            'data' => $orders,
        ];
    }

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(ECommerceIntegration $integration, array $options = []): array
    {
        $customers = $this->getCustomers($integration, $options);

        return [
            'total' => count($customers),
            'processed' => count($customers),
            'successful' => count($customers),
            'failed' => 0,
            'data' => $customers,
        ];
    }

    /**
     * مزامنة الفئات
     */
    public function syncCategories(ECommerceIntegration $integration, array $options = []): array
    {
        $categories = $this->getCategories($integration, $options);

        return [
            'total' => count($categories),
            'processed' => count($categories),
            'successful' => count($categories),
            'failed' => 0,
            'data' => $categories,
        ];
    }

    /**
     * معالجة webhook من WooCommerce
     */
    public function processWebhook(ECommerceIntegration $integration, array $payload, array $headers = []): array
    {
        $event = $headers['x-wc-webhook-event'][0] ?? '';
        $resource = $headers['x-wc-webhook-resource'][0] ?? '';

        return [
            'success' => true,
            'event' => $event,
            'resource' => $resource,
            'data' => $payload,
        ];
    }

    /**
     * التحقق من توقيع webhook
     */
    public function verifyWebhookSignature(Request $request, ECommerceIntegration $integration): bool
    {
        $signature = $request->header('X-WC-Webhook-Signature');
        $webhookSecret = $integration->authentication_config['webhook_secret'] ?? '';

        if (!$signature || !$webhookSecret) {
            return false;
        }

        $calculatedSignature = base64_encode(hash_hmac('sha256', $request->getContent(), $webhookSecret, true));

        return hash_equals($signature, $calculatedSignature);
    }

    /**
     * تحويل البيانات إلى تنسيق WooCommerce
     */
    public function transformToExternalFormat(array $data, string $entityType): array
    {
        return match ($entityType) {
            'product' => $this->transformProductToWooCommerce($data),
            'order' => $this->transformOrderToWooCommerce($data),
            'customer' => $this->transformCustomerToWooCommerce($data),
            'category' => $this->transformCategoryToWooCommerce($data),
            default => $data,
        };
    }

    /**
     * تحويل البيانات من تنسيق WooCommerce
     */
    public function transformFromExternalFormat(array $data, string $entityType): array
    {
        return match ($entityType) {
            'product' => $this->transformProductFromWooCommerce($data),
            'order' => $this->transformOrderFromWooCommerce($data),
            'customer' => $this->transformCustomerFromWooCommerce($data),
            'category' => $this->transformCategoryFromWooCommerce($data),
            default => $data,
        };
    }

    /**
     * تحويل المنتج إلى تنسيق WooCommerce
     */
    protected function transformProductToWooCommerce(array $data): array
    {
        return [
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? '',
            'short_description' => $data['short_description'] ?? '',
            'sku' => $data['sku'] ?? '',
            'regular_price' => (string) ($data['price'] ?? 0),
            'sale_price' => $data['sale_price'] ? (string) $data['sale_price'] : '',
            'manage_stock' => $data['manage_stock'] ?? true,
            'stock_quantity' => $data['inventory_quantity'] ?? 0,
            'weight' => (string) ($data['weight'] ?? 0),
            'dimensions' => $data['dimensions'] ?? [],
            'categories' => $data['categories'] ?? [],
            'tags' => $data['tags'] ?? [],
            'images' => $data['images'] ?? [],
            'attributes' => $data['attributes'] ?? [],
            'status' => $data['status'] ?? 'publish',
            'catalog_visibility' => $data['visibility'] ?? 'visible',
            'featured' => $data['featured'] ?? false,
            'virtual' => $data['virtual'] ?? false,
            'downloadable' => $data['downloadable'] ?? false,
            'meta_data' => $data['meta_data'] ?? [],
        ];
    }

    /**
     * تحويل المنتج من تنسيق WooCommerce
     */
    protected function transformProductFromWooCommerce(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? '',
            'short_description' => $data['short_description'] ?? '',
            'sku' => $data['sku'] ?? '',
            'price' => (float) ($data['regular_price'] ?? 0),
            'regular_price' => (float) ($data['regular_price'] ?? 0),
            'sale_price' => $data['sale_price'] ? (float) $data['sale_price'] : null,
            'inventory_quantity' => (int) ($data['stock_quantity'] ?? 0),
            'manage_stock' => $data['manage_stock'] ?? false,
            'in_stock' => $data['in_stock'] ?? true,
            'weight' => (float) ($data['weight'] ?? 0),
            'dimensions' => $data['dimensions'] ?? [],
            'categories' => $data['categories'] ?? [],
            'tags' => $data['tags'] ?? [],
            'images' => $data['images'] ?? [],
            'gallery_images' => array_slice($data['images'] ?? [], 1),
            'featured_image' => isset($data['images'][0]) ? $data['images'][0]['src'] : null,
            'attributes' => $data['attributes'] ?? [],
            'variations' => $data['variations'] ?? [],
            'meta_data' => $data['meta_data'] ?? [],
            'status' => $data['status'] ?? 'publish',
            'visibility' => $data['catalog_visibility'] ?? 'visible',
            'featured' => $data['featured'] ?? false,
            'virtual' => $data['virtual'] ?? false,
            'downloadable' => $data['downloadable'] ?? false,
            'purchase_note' => $data['purchase_note'] ?? '',
            'shipping_class' => $data['shipping_class'] ?? '',
            'tax_class' => $data['tax_class'] ?? '',
            'tax_status' => $data['tax_status'] ?? 'taxable',
            'upsell_ids' => $data['upsell_ids'] ?? [],
            'cross_sell_ids' => $data['cross_sell_ids'] ?? [],
            'parent_id' => $data['parent_id'] ?? 0,
            'grouped_products' => $data['grouped_products'] ?? [],
            'external_url' => $data['external_url'] ?? '',
            'button_text' => $data['button_text'] ?? '',
            'menu_order' => $data['menu_order'] ?? 0,
            'reviews_allowed' => $data['reviews_allowed'] ?? true,
            'rating_average' => (float) ($data['average_rating'] ?? 0),
            'rating_count' => (int) ($data['rating_count'] ?? 0),
            'created_at' => $data['date_created'] ?? null,
            'updated_at' => $data['date_modified'] ?? null,
        ];
    }

    /**
     * تحويل الطلب من تنسيق WooCommerce
     */
    protected function transformOrderFromWooCommerce(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'order_number' => $data['number'] ?? $data['id'],
            'order_key' => $data['order_key'] ?? '',
            'parent_id' => $data['parent_id'] ?? 0,
            'status' => $data['status'] ?? '',
            'currency' => $data['currency'] ?? 'USD',
            'version' => $data['version'] ?? '',
            'prices_include_tax' => $data['prices_include_tax'] ?? false,
            'order_date' => $data['date_created'] ?? null,
            'customer_ip_address' => $data['customer_ip_address'] ?? '',
            'customer_user_agent' => $data['customer_user_agent'] ?? '',
            'customer_note' => $data['customer_note'] ?? '',
            'billing_address' => $data['billing'] ?? [],
            'shipping_address' => $data['shipping'] ?? [],
            'payment_method' => $data['payment_method'] ?? '',
            'payment_method_title' => $data['payment_method_title'] ?? '',
            'transaction_id' => $data['transaction_id'] ?? '',
            'date_paid' => $data['date_paid'] ?? null,
            'date_completed' => $data['date_completed'] ?? null,
            'cart_hash' => $data['cart_hash'] ?? '',
            'line_items' => $data['line_items'] ?? [],
            'shipping_lines' => $data['shipping_lines'] ?? [],
            'tax_lines' => $data['tax_lines'] ?? [],
            'fee_lines' => $data['fee_lines'] ?? [],
            'coupon_lines' => $data['coupon_lines'] ?? [],
            'refunds' => $data['refunds'] ?? [],
            'meta_data' => $data['meta_data'] ?? [],
            'subtotal' => (float) ($data['total'] ?? 0) - (float) ($data['total_tax'] ?? 0) - (float) ($data['shipping_total'] ?? 0),
            'subtotal_tax' => (float) ($data['total_tax'] ?? 0),
            'shipping_total' => (float) ($data['shipping_total'] ?? 0),
            'shipping_tax' => (float) ($data['shipping_tax'] ?? 0),
            'discount_total' => (float) ($data['discount_total'] ?? 0),
            'discount_tax' => (float) ($data['discount_tax'] ?? 0),
            'cart_tax' => (float) ($data['cart_tax'] ?? 0),
            'total' => (float) ($data['total'] ?? 0),
            'total_tax' => (float) ($data['total_tax'] ?? 0),
            'created_at' => $data['date_created'] ?? null,
            'updated_at' => $data['date_modified'] ?? null,
        ];
    }

    /**
     * تحويل العميل من تنسيق WooCommerce
     */
    protected function transformCustomerFromWooCommerce(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'username' => $data['username'] ?? '',
            'email' => $data['email'] ?? '',
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'display_name' => trim(($data['first_name'] ?? '') . ' ' . ($data['last_name'] ?? '')),
            'role' => $data['role'] ?? 'customer',
            'avatar_url' => $data['avatar_url'] ?? '',
            'billing_address' => $data['billing'] ?? [],
            'shipping_address' => $data['shipping'] ?? [],
            'meta_data' => $data['meta_data'] ?? [],
            'created_at' => $data['date_created'] ?? null,
            'updated_at' => $data['date_modified'] ?? null,
        ];
    }

    /**
     * تحويل الفئة إلى تنسيق WooCommerce
     */
    protected function transformCategoryToWooCommerce(array $data): array
    {
        return [
            'name' => $data['name'] ?? '',
            'slug' => $data['slug'] ?? '',
            'parent' => $data['parent_id'] ?? 0,
            'description' => $data['description'] ?? '',
            'display' => $data['display'] ?? 'default',
            'image' => $data['image'] ?? null,
            'menu_order' => $data['menu_order'] ?? 0,
        ];
    }

    /**
     * تحويل الفئة من تنسيق WooCommerce
     */
    protected function transformCategoryFromWooCommerce(array $data): array
    {
        return [
            'external_id' => (string) $data['id'],
            'name' => $data['name'] ?? '',
            'slug' => $data['slug'] ?? '',
            'parent_id' => $data['parent'] ?? 0,
            'description' => $data['description'] ?? '',
            'display' => $data['display'] ?? 'default',
            'image' => $data['image'] ?? null,
            'menu_order' => $data['menu_order'] ?? 0,
            'count' => $data['count'] ?? 0,
        ];
    }

    /**
     * الحصول على الحقول المطلوبة
     */
    public function getRequiredFields(): array
    {
        return [
            'site_url',
            'consumer_key',
            'consumer_secret',
        ];
    }

    /**
     * الحصول على الحقول الاختيارية
     */
    public function getOptionalFields(): array
    {
        return [
            'webhook_secret',
            'api_version',
        ];
    }

    /**
     * الحصول على العمليات المدعومة
     */
    public function getSupportedOperations(): array
    {
        return [
            'products.read', 'products.write',
            'orders.read', 'orders.write',
            'customers.read', 'customers.write',
            'categories.read', 'categories.write',
            'coupons.read', 'coupons.write',
            'reports.read',
            'webhooks.read', 'webhooks.write',
        ];
    }

    /**
     * الحصول على أنواع المزامنة المدعومة
     */
    public function getSupportedSyncTypes(): array
    {
        return ['full', 'incremental', 'real-time'];
    }

    /**
     * الحصول على أنواع الأحداث المدعومة
     */
    public function getSupportedEventTypes(): array
    {
        return [
            'product.created', 'product.updated', 'product.deleted',
            'order.created', 'order.updated', 'order.deleted',
            'customer.created', 'customer.updated', 'customer.deleted',
            'coupon.created', 'coupon.updated', 'coupon.deleted',
        ];
    }

    /**
     * الحصول على تنسيقات البيانات المدعومة
     */
    public function getSupportedDataFormats(): array
    {
        return ['json'];
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    public function getDefaultConfiguration(): array
    {
        return [
            'api_version' => $this->apiVersion,
            'timeout' => 30,
            'max_retries' => 3,
            'page_size' => $this->defaultPageSize,
        ];
    }

    // تنفيذ باقي الطرق المطلوبة بشكل أساسي
    public function getInventory(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function updateInventory(ECommerceIntegration $integration, string $productId, int $quantity): array { return []; }
    public function getCoupons(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function createCoupon(ECommerceIntegration $integration, array $couponData): array { return []; }
    public function updateCoupon(ECommerceIntegration $integration, string $couponId, array $couponData): array { return []; }
    public function deleteCoupon(ECommerceIntegration $integration, string $couponId): array { return []; }
    public function getReports(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function getSalesReport(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function getProductsReport(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function getCustomersReport(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function syncInventory(ECommerceIntegration $integration, array $options = []): array { return []; }
    public function createWebhook(ECommerceIntegration $integration, array $webhookData): array { return []; }
    public function updateWebhook(ECommerceIntegration $integration, string $webhookId, array $webhookData): array { return []; }
    public function deleteWebhook(ECommerceIntegration $integration, string $webhookId): array { return []; }
    public function getWebhooks(ECommerceIntegration $integration): array { return []; }
    public function getApiLimits(ECommerceIntegration $integration): array { return []; }
    public function getApiUsage(ECommerceIntegration $integration): array { return []; }
    public function refreshAccessToken(ECommerceIntegration $integration): array { return []; }
    public function revokeAccess(ECommerceIntegration $integration): array { return []; }
    public function getAppInfo(ECommerceIntegration $integration): array { return []; }
    public function updateAppSettings(ECommerceIntegration $integration, array $settings): array { return []; }
    public function getStoreStats(ECommerceIntegration $integration): array { return []; }
    public function getPlanInfo(ECommerceIntegration $integration): array { return []; }
    public function getSupportedCountries(ECommerceIntegration $integration): array { return []; }
    public function getSupportedCurrencies(ECommerceIntegration $integration): array { return []; }
    public function getSupportedLanguages(ECommerceIntegration $integration): array { return []; }
    public function getSupportedPaymentMethods(ECommerceIntegration $integration): array { return []; }
    public function getSupportedShippingMethods(ECommerceIntegration $integration): array { return []; }
    public function getSupportedTaxes(ECommerceIntegration $integration): array { return []; }
    public function validateDataForExport(array $data, string $entityType): array { return ['valid' => true]; }
    public function validateDataForImport(array $data, string $entityType): array { return ['valid' => true]; }
    public function getRequiredHeaders(ECommerceIntegration $integration): array { return []; }
    public function getRequiredQueryParams(ECommerceIntegration $integration): array { return []; }
    public function prepareApiRequest(string $method, string $endpoint, array $data = []): array { return []; }
    public function buildNextPageUrl(array $pagination): ?string { return null; }
    public function buildPreviousPageUrl(array $pagination): ?string { return null; }
}
