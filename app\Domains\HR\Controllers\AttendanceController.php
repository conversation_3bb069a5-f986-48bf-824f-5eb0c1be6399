<?php

namespace App\Domains\HR\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\HR\Models\AttendanceRecord;
use App\Domains\HR\Models\Employee;
use App\Domains\HR\Services\AttendanceManagementService;
use App\Domains\HR\Resources\AttendanceRecordResource;
use App\Domains\HR\Resources\AttendanceRecordCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

/**
 * متحكم الحضور والانصراف
 * إدارة شاملة لنظام الحضور والانصراف
 */
class AttendanceController extends Controller
{
    protected AttendanceManagementService $attendanceService;

    public function __construct(AttendanceManagementService $attendanceService)
    {
        $this->attendanceService = $attendanceService;
    }

    /**
     * عرض قائمة سجلات الحضور
     */
    public function index(Request $request): JsonResponse
    {
        $query = AttendanceRecord::with(['employee.department', 'employee.position', 'shift']);

        // التصفية حسب الموظف
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        // التصفية حسب القسم
        if ($request->filled('department_id')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }

        // التصفية حسب التاريخ
        if ($request->filled('date')) {
            $query->whereDate('date', $request->date);
        }

        // التصفية حسب الفترة
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('date', [$request->start_date, $request->end_date]);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية للمتأخرين
        if ($request->boolean('late_only')) {
            $query->where('late_minutes', '>', 0);
        }

        // التصفية للعمل الإضافي
        if ($request->boolean('overtime_only')) {
            $query->where('overtime_hours', '>', 0);
        }

        // التصفية للسجلات غير المعتمدة
        if ($request->boolean('pending_approval')) {
            $query->where('is_approved', false)->whereNull('rejection_reason');
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $records = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new AttendanceRecordCollection($records),
            'meta' => [
                'total' => $records->total(),
                'per_page' => $records->perPage(),
                'current_page' => $records->currentPage(),
                'last_page' => $records->lastPage(),
            ],
        ]);
    }

    /**
     * تسجيل حضور الموظف
     */
    public function checkIn(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'method' => 'nullable|string|in:BIOMETRIC,CARD,MOBILE_APP,WEB_PORTAL,FACE_RECOGNITION,QR_CODE,MANUAL,GPS',
            'device' => 'nullable|string|max:100',
            'location' => 'nullable|array',
            'location.latitude' => 'nullable|numeric|between:-90,90',
            'location.longitude' => 'nullable|numeric|between:-180,180',
            'biometric' => 'boolean',
            'face_recognition' => 'boolean',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $attendanceRecord = $this->attendanceService->checkIn(
                $request->employee_id,
                $request->only(['method', 'device', 'location', 'biometric', 'face_recognition', 'notes'])
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الحضور بنجاح',
                'data' => new AttendanceRecordResource($attendanceRecord),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * تسجيل انصراف الموظف
     */
    public function checkOut(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'method' => 'nullable|string|in:BIOMETRIC,CARD,MOBILE_APP,WEB_PORTAL,FACE_RECOGNITION,QR_CODE,MANUAL,GPS',
            'device' => 'nullable|string|max:100',
            'location' => 'nullable|array',
            'location.latitude' => 'nullable|numeric|between:-90,90',
            'location.longitude' => 'nullable|numeric|between:-180,180',
            'biometric' => 'boolean',
            'face_recognition' => 'boolean',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $attendanceRecord = $this->attendanceService->checkOut(
                $request->employee_id,
                $request->only(['method', 'device', 'location', 'biometric', 'face_recognition', 'notes'])
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الانصراف بنجاح',
                'data' => new AttendanceRecordResource($attendanceRecord),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * تسجيل بداية الاستراحة
     */
    public function startBreak(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
        ]);

        try {
            $attendanceRecord = $this->attendanceService->startBreak($request->employee_id);

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل بداية الاستراحة بنجاح',
                'data' => new AttendanceRecordResource($attendanceRecord),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * تسجيل نهاية الاستراحة
     */
    public function endBreak(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
        ]);

        try {
            $attendanceRecord = $this->attendanceService->endBreak($request->employee_id);

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل نهاية الاستراحة بنجاح',
                'data' => new AttendanceRecordResource($attendanceRecord),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * عرض تفاصيل سجل حضور محدد
     */
    public function show(int $id): JsonResponse
    {
        $record = AttendanceRecord::with(['employee.department', 'employee.position', 'shift', 'approver'])
                                 ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => new AttendanceRecordResource($record),
        ]);
    }

    /**
     * تحديث سجل الحضور
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'actual_check_in' => 'nullable|date',
            'actual_check_out' => 'nullable|date',
            'break_start' => 'nullable|date',
            'break_end' => 'nullable|date',
            'status' => 'nullable|string|in:PRESENT,ABSENT,LATE,EARLY_DEPARTURE,HALF_DAY,OVERTIME,REMOTE',
            'is_remote_work' => 'boolean',
            'is_half_day' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $record = AttendanceRecord::findOrFail($id);
            $record->update($request->validated());

            // إعادة حساب الساعات إذا تم تحديث أوقات الحضور/الانصراف
            if ($request->has(['actual_check_in', 'actual_check_out'])) {
                $record->update([
                    'total_hours' => $record->calculateTotalHours(),
                    'regular_hours' => min($record->calculateTotalHours(), $record->getRegularWorkingHours()),
                    'overtime_hours' => $record->calculateOvertimeHours(),
                    'late_minutes' => $record->calculateLateMinutes(),
                    'early_departure_minutes' => $record->calculateEarlyDepartureMinutes(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث سجل الحضور بنجاح',
                'data' => new AttendanceRecordResource($record),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث سجل الحضور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الموافقة على سجل الحضور
     */
    public function approve(Request $request, int $id): JsonResponse
    {
        try {
            $record = AttendanceRecord::findOrFail($id);
            $record->approve(auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'تم اعتماد سجل الحضور بنجاح',
                'data' => new AttendanceRecordResource($record),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء اعتماد سجل الحضور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * رفض سجل الحضور
     */
    public function reject(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $record = AttendanceRecord::findOrFail($id);
            $record->reject($request->reason, auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'تم رفض سجل الحضور',
                'data' => new AttendanceRecordResource($record),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء رفض سجل الحضور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على تقرير الحضور للموظف
     */
    public function getEmployeeReport(Request $request, int $employeeId): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $employee = Employee::findOrFail($employeeId);
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $report = $this->attendanceService->getAttendanceReport($employeeId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * الحصول على إحصائيات الحضور
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $filters = $request->only(['department_id', 'start_date', 'end_date']);
        $statistics = $this->attendanceService->getAttendanceStatistics($filters);

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * إنشاء جدول حضور للفترة المحددة
     */
    public function generateSchedule(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $startDate = Carbon::parse($request->start_date);
            $endDate = Carbon::parse($request->end_date);

            $generated = $this->attendanceService->generateAttendanceSchedule(
                $request->employee_id,
                $startDate,
                $endDate
            );

            return response()->json([
                'success' => true,
                'message' => "تم إنشاء {$generated} سجل حضور",
                'generated_records' => $generated,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء جدول الحضور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إعادة حساب سجلات الحضور
     */
    public function recalculate(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $startDate = Carbon::parse($request->start_date);
            $endDate = Carbon::parse($request->end_date);

            $updated = $this->attendanceService->recalculateAttendance($startDate, $endDate);

            return response()->json([
                'success' => true,
                'message' => "تم إعادة حساب {$updated} سجل حضور",
                'updated_records' => $updated,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة حساب سجلات الحضور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على حالة الحضور الحالية للموظف
     */
    public function getCurrentStatus(int $employeeId): JsonResponse
    {
        $today = now()->format('Y-m-d');
        
        $record = AttendanceRecord::where('employee_id', $employeeId)
                                 ->whereDate('date', $today)
                                 ->first();

        $status = [
            'employee_id' => $employeeId,
            'date' => $today,
            'is_checked_in' => $record && $record->actual_check_in ? true : false,
            'is_checked_out' => $record && $record->actual_check_out ? true : false,
            'is_on_break' => $record && $record->break_start && !$record->break_end ? true : false,
            'check_in_time' => $record?->actual_check_in?->format('H:i'),
            'check_out_time' => $record?->actual_check_out?->format('H:i'),
            'break_start_time' => $record?->break_start?->format('H:i'),
            'break_end_time' => $record?->break_end?->format('H:i'),
            'total_hours' => $record?->total_hours,
            'status' => $record?->status,
            'late_minutes' => $record?->late_minutes,
        ];

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }

    /**
     * الحصول على سجلات الحضور للموظف في الأسبوع الحالي
     */
    public function getWeeklyAttendance(int $employeeId): JsonResponse
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        $records = AttendanceRecord::where('employee_id', $employeeId)
                                  ->whereBetween('date', [$startOfWeek, $endOfWeek])
                                  ->orderBy('date')
                                  ->get();

        return response()->json([
            'success' => true,
            'data' => new AttendanceRecordCollection($records),
            'summary' => [
                'total_days' => $records->count(),
                'present_days' => $records->whereIn('status', ['PRESENT', 'LATE', 'EARLY_DEPARTURE', 'OVERTIME'])->count(),
                'total_hours' => $records->sum('total_hours'),
                'overtime_hours' => $records->sum('overtime_hours'),
                'late_days' => $records->where('late_minutes', '>', 0)->count(),
            ],
        ]);
    }
}
