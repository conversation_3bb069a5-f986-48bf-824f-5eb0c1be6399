<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج شريحة العملاء - Customer Segment
 * تقسيم العملاء حسب معايير مختلفة للاستهداف التسويقي
 */
class CustomerSegment extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'description',
        'type',
        'criteria',
        'conditions',
        'is_dynamic',
        'is_active',
        'created_by',
        'color',
        'icon',
        'priority',
        'target_size',
        'actual_size',
        'last_calculated_at',
        'calculation_frequency',
        'tags',
        'metadata',
    ];

    protected $casts = [
        'criteria' => 'array',
        'conditions' => 'array',
        'is_dynamic' => 'boolean',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'target_size' => 'integer',
        'actual_size' => 'integer',
        'last_calculated_at' => 'datetime',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع الشرائح
     */
    const TYPES = [
        'demographic' => 'ديموغرافية',
        'behavioral' => 'سلوكية',
        'geographic' => 'جغرافية',
        'psychographic' => 'نفسية',
        'value_based' => 'قائمة على القيمة',
        'lifecycle' => 'دورة الحياة',
        'engagement' => 'مستوى التفاعل',
        'custom' => 'مخصصة',
    ];

    /**
     * تكرار الحساب
     */
    const CALCULATION_FREQUENCIES = [
        'real_time' => 'فوري',
        'hourly' => 'كل ساعة',
        'daily' => 'يومي',
        'weekly' => 'أسبوعي',
        'monthly' => 'شهري',
        'manual' => 'يدوي',
    ];

    /**
     * العلاقة مع العملاء
     */
    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(Customer::class, 'customer_segment_members')
                    ->withPivot(['added_at', 'score', 'metadata'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع منشئ الشريحة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع الحملات التسويقية
     */
    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(MarketingCampaign::class, 'campaign_segments');
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية تكرار الحساب
     */
    public function getCalculationFrequencyLabelAttribute(): string
    {
        return self::CALCULATION_FREQUENCIES[$this->calculation_frequency] ?? 'غير محدد';
    }

    /**
     * التحقق من كون الشريحة ديناميكية
     */
    public function getIsDynamicSegmentAttribute(): bool
    {
        return $this->is_dynamic;
    }

    /**
     * التحقق من الحاجة لإعادة الحساب
     */
    public function getNeedsRecalculationAttribute(): bool
    {
        if (!$this->is_dynamic || $this->calculation_frequency === 'manual') {
            return false;
        }

        if (!$this->last_calculated_at) {
            return true;
        }

        $interval = match ($this->calculation_frequency) {
            'real_time' => 0,
            'hourly' => 1,
            'daily' => 24,
            'weekly' => 168,
            'monthly' => 720,
            default => 24,
        };

        return $this->last_calculated_at->addHours($interval)->isPast();
    }

    /**
     * الحصول على نسبة الوصول للهدف
     */
    public function getTargetAchievementPercentageAttribute(): float
    {
        if (!$this->target_size || $this->target_size == 0) {
            return 0;
        }

        return round(($this->actual_size / $this->target_size) * 100, 1);
    }

    /**
     * الحصول على معدل النمو
     */
    public function getGrowthRateAttribute(): float
    {
        $previousSize = $this->metadata['previous_size'] ?? $this->actual_size;
        
        if ($previousSize == 0) {
            return 0;
        }

        return round((($this->actual_size - $previousSize) / $previousSize) * 100, 1);
    }

    /**
     * إعادة حساب الشريحة
     */
    public function recalculate(): int
    {
        if (!$this->is_dynamic) {
            return $this->actual_size;
        }

        $query = Customer::query();

        // تطبيق المعايير
        foreach ($this->criteria as $criterion) {
            $query = $this->applyCriterion($query, $criterion);
        }

        // تطبيق الشروط
        if (!empty($this->conditions)) {
            $query = $this->applyConditions($query, $this->conditions);
        }

        $customerIds = $query->pluck('id')->toArray();
        $newSize = count($customerIds);

        // حفظ الحجم السابق
        $previousSize = $this->actual_size;
        
        // تحديث الشريحة
        $this->update([
            'actual_size' => $newSize,
            'last_calculated_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'previous_size' => $previousSize,
                'last_recalculation' => now()->toISOString(),
            ]),
        ]);

        // مزامنة العملاء
        $this->customers()->sync($customerIds);

        return $newSize;
    }

    /**
     * تطبيق معيار واحد
     */
    protected function applyCriterion($query, array $criterion)
    {
        $field = $criterion['field'];
        $operator = $criterion['operator'];
        $value = $criterion['value'];

        switch ($operator) {
            case 'equals':
                return $query->where($field, $value);
            case 'not_equals':
                return $query->where($field, '!=', $value);
            case 'greater_than':
                return $query->where($field, '>', $value);
            case 'less_than':
                return $query->where($field, '<', $value);
            case 'greater_than_or_equal':
                return $query->where($field, '>=', $value);
            case 'less_than_or_equal':
                return $query->where($field, '<=', $value);
            case 'contains':
                return $query->where($field, 'LIKE', "%{$value}%");
            case 'not_contains':
                return $query->where($field, 'NOT LIKE', "%{$value}%");
            case 'starts_with':
                return $query->where($field, 'LIKE', "{$value}%");
            case 'ends_with':
                return $query->where($field, 'LIKE', "%{$value}");
            case 'in':
                return $query->whereIn($field, is_array($value) ? $value : [$value]);
            case 'not_in':
                return $query->whereNotIn($field, is_array($value) ? $value : [$value]);
            case 'between':
                return $query->whereBetween($field, $value);
            case 'not_between':
                return $query->whereNotBetween($field, $value);
            case 'is_null':
                return $query->whereNull($field);
            case 'is_not_null':
                return $query->whereNotNull($field);
            case 'date_equals':
                return $query->whereDate($field, $value);
            case 'date_greater_than':
                return $query->whereDate($field, '>', $value);
            case 'date_less_than':
                return $query->whereDate($field, '<', $value);
            default:
                return $query;
        }
    }

    /**
     * تطبيق الشروط المعقدة
     */
    protected function applyConditions($query, array $conditions)
    {
        $logic = $conditions['logic'] ?? 'and'; // and, or
        $rules = $conditions['rules'] ?? [];

        if ($logic === 'and') {
            foreach ($rules as $rule) {
                if (isset($rule['rules'])) {
                    // شرط مجموعة
                    $query->where(function ($subQuery) use ($rule) {
                        $this->applyConditions($subQuery, $rule);
                    });
                } else {
                    // شرط مفرد
                    $query = $this->applyCriterion($query, $rule);
                }
            }
        } else { // or
            $query->where(function ($subQuery) use ($rules) {
                foreach ($rules as $rule) {
                    if (isset($rule['rules'])) {
                        // شرط مجموعة
                        $subQuery->orWhere(function ($subSubQuery) use ($rule) {
                            $this->applyConditions($subSubQuery, $rule);
                        });
                    } else {
                        // شرط مفرد
                        $subQuery = $this->applyOrCriterion($subQuery, $rule);
                    }
                }
            });
        }

        return $query;
    }

    /**
     * تطبيق معيار مع OR
     */
    protected function applyOrCriterion($query, array $criterion)
    {
        $field = $criterion['field'];
        $operator = $criterion['operator'];
        $value = $criterion['value'];

        switch ($operator) {
            case 'equals':
                return $query->orWhere($field, $value);
            case 'not_equals':
                return $query->orWhere($field, '!=', $value);
            case 'greater_than':
                return $query->orWhere($field, '>', $value);
            case 'contains':
                return $query->orWhere($field, 'LIKE', "%{$value}%");
            case 'in':
                return $query->orWhereIn($field, is_array($value) ? $value : [$value]);
            // ... باقي العمليات
            default:
                return $query;
        }
    }

    /**
     * إضافة عميل للشريحة
     */
    public function addCustomer(Customer $customer, float $score = null): void
    {
        $this->customers()->syncWithoutDetaching([
            $customer->id => [
                'added_at' => now(),
                'score' => $score,
                'metadata' => [],
            ]
        ]);

        $this->increment('actual_size');
    }

    /**
     * إزالة عميل من الشريحة
     */
    public function removeCustomer(Customer $customer): void
    {
        $this->customers()->detach($customer->id);
        $this->decrement('actual_size');
    }

    /**
     * فلترة الشرائح النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة الشرائح الديناميكية
     */
    public function scopeDynamic($query)
    {
        return $query->where('is_dynamic', true);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة الشرائح التي تحتاج إعادة حساب
     */
    public function scopeNeedsRecalculation($query)
    {
        return $query->where('is_dynamic', true)
                    ->where('calculation_frequency', '!=', 'manual')
                    ->where(function ($q) {
                        $q->whereNull('last_calculated_at')
                          ->orWhere('last_calculated_at', '<', now()->subHour());
                    });
    }

    /**
     * البحث في الشرائح
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%");
        });
    }
}
