<?php

namespace App\Domains\Projects\Services;

use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\TimeEntry;
use App\Domains\Projects\Models\ProjectChat;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة إدارة المشاريع المتقدمة - Enterprise Grade
 * تدعم جميع جوانب إدارة المشاريع المعقدة
 */
class AdvancedProjectManagementService
{
    protected array $viewModes = ['kanban', 'gantt', 'list', 'calendar'];
    protected array $methodologies = ['AGILE', 'WATERFALL', 'SCRUM', 'KANBAN', 'HYBRID'];

    /**
     * إنشاء مشروع متقدم
     */
    public function createAdvancedProject(array $projectData): Project
    {
        try {
            DB::beginTransaction();

            // إنشاء المشروع الأساسي
            $project = Project::create([
                'name' => $projectData['name'],
                'description' => $projectData['description'],
                'code' => $this->generateProjectCode($projectData['name']),
                'client_id' => $projectData['client_id'] ?? null,
                'project_manager_id' => $projectData['project_manager_id'],
                'status' => 'PLANNING',
                'priority' => $projectData['priority'] ?? 'MEDIUM',
                'start_date' => $projectData['start_date'],
                'end_date' => $projectData['end_date'],
                'planned_start_date' => $projectData['start_date'],
                'planned_end_date' => $projectData['end_date'],
                'budget' => $projectData['budget'] ?? 0,
                'currency' => $projectData['currency'] ?? 'MAD',
                'methodology' => $projectData['methodology'] ?? 'AGILE',
                'category' => $projectData['category'] ?? 'GENERAL',
                'is_billable' => $projectData['is_billable'] ?? true,
                'hourly_rate' => $projectData['hourly_rate'] ?? null,
                'visibility' => $projectData['visibility'] ?? 'TEAM',
                'settings' => $this->getDefaultProjectSettings($projectData['methodology'] ?? 'AGILE'),
                'custom_fields' => $projectData['custom_fields'] ?? [],
            ]);

            // إضافة أعضاء الفريق
            if (!empty($projectData['team_members'])) {
                $this->addTeamMembers($project, $projectData['team_members']);
            }

            // إنشاء المعالم الافتراضية
            if ($projectData['create_default_milestones'] ?? true) {
                $this->createDefaultMilestones($project);
            }

            // إنشاء المحادثة العامة
            ProjectChat::createGeneralChat($project->id, $project->project_manager_id);

            // إنشاء مراكز التكلفة
            if ($projectData['create_cost_centers'] ?? true) {
                $this->createDefaultCostCenters($project);
            }

            // تطبيق القالب إذا تم تحديده
            if (!empty($projectData['template_id'])) {
                $this->applyProjectTemplate($project, $projectData['template_id']);
            }

            DB::commit();

            // إرسال إشعارات
            $this->sendProjectCreationNotifications($project);

            return $project;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في إنشاء المشروع المتقدم', [
                'error' => $e->getMessage(),
                'data' => $projectData,
            ]);
            throw $e;
        }
    }

    /**
     * الحصول على لوحة تحكم المشروع
     */
    public function getProjectDashboard(int $projectId, array $filters = []): array
    {
        $project = Project::with([
            'tasks',
            'teamMembers',
            'timeEntries',
            'milestones',
            'risks',
            'issues'
        ])->findOrFail($projectId);

        return [
            'project_overview' => $this->getProjectOverview($project),
            'progress_metrics' => $this->getProgressMetrics($project),
            'team_performance' => $this->getTeamPerformance($project, $filters),
            'time_analytics' => $this->getTimeAnalytics($project, $filters),
            'budget_analysis' => $this->getBudgetAnalysis($project),
            'risk_assessment' => $this->getRiskAssessment($project),
            'upcoming_deadlines' => $this->getUpcomingDeadlines($project),
            'recent_activities' => $this->getRecentActivities($project),
            'quality_metrics' => $this->getQualityMetrics($project),
            'client_satisfaction' => $this->getClientSatisfactionMetrics($project),
        ];
    }

    /**
     * إدارة عرض المهام حسب النمط
     */
    public function getTasksView(int $projectId, string $viewMode, array $filters = []): array
    {
        $project = Project::findOrFail($projectId);

        return match ($viewMode) {
            'kanban' => $this->getKanbanView($project, $filters),
            'gantt' => $this->getGanttView($project, $filters),
            'list' => $this->getListView($project, $filters),
            'calendar' => $this->getCalendarView($project, $filters),
            default => throw new \InvalidArgumentException("نمط العرض غير مدعوم: {$viewMode}"),
        };
    }

    /**
     * عرض كانبان
     */
    protected function getKanbanView(Project $project, array $filters): array
    {
        $tasks = $project->tasks()
                        ->with(['assignees', 'labels', 'timeEntries'])
                        ->when($filters['assignee_id'] ?? null, function ($query, $assigneeId) {
                            $query->where('assignee_id', $assigneeId);
                        })
                        ->when($filters['priority'] ?? null, function ($query, $priority) {
                            $query->where('priority', $priority);
                        })
                        ->get();

        $columns = [
            'TODO' => ['name' => 'قائمة المهام', 'tasks' => []],
            'IN_PROGRESS' => ['name' => 'قيد التنفيذ', 'tasks' => []],
            'REVIEW' => ['name' => 'قيد المراجعة', 'tasks' => []],
            'TESTING' => ['name' => 'قيد الاختبار', 'tasks' => []],
            'COMPLETED' => ['name' => 'مكتملة', 'tasks' => []],
        ];

        foreach ($tasks as $task) {
            $status = $task->status;
            if (isset($columns[$status])) {
                $columns[$status]['tasks'][] = [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->description,
                    'priority' => $task->priority,
                    'assignee' => $task->assignee,
                    'due_date' => $task->due_date,
                    'story_points' => $task->story_points,
                    'labels' => $task->labels,
                    'progress' => $task->progress_percentage,
                    'time_logged' => $task->total_logged_hours,
                    'is_overdue' => $task->is_overdue,
                ];
            }
        }

        return [
            'view_type' => 'kanban',
            'columns' => $columns,
            'statistics' => $this->getKanbanStatistics($tasks),
        ];
    }

    /**
     * عرض جانت
     */
    protected function getGanttView(Project $project, array $filters): array
    {
        $tasks = $project->tasks()
                        ->with(['dependencies', 'assignee'])
                        ->orderBy('start_date')
                        ->get();

        $ganttData = [];
        foreach ($tasks as $task) {
            $ganttData[] = [
                'id' => $task->id,
                'text' => $task->title,
                'start_date' => $task->start_date?->format('Y-m-d'),
                'end_date' => $task->due_date?->format('Y-m-d'),
                'duration' => $task->estimated_hours ?? 0,
                'progress' => $task->progress_percentage / 100,
                'parent' => $task->parent_id,
                'type' => $task->type,
                'assignee' => $task->assignee?->name,
                'priority' => $task->priority,
                'dependencies' => $task->dependencies->pluck('id')->toArray(),
            ];
        }

        return [
            'view_type' => 'gantt',
            'tasks' => $ganttData,
            'project_timeline' => [
                'start_date' => $project->start_date,
                'end_date' => $project->end_date,
                'current_date' => now(),
            ],
            'critical_path' => $this->calculateCriticalPath($tasks),
        ];
    }

    /**
     * عرض القائمة
     */
    protected function getListView(Project $project, array $filters): array
    {
        $query = $project->tasks()->with(['assignee', 'milestone', 'timeEntries']);

        // تطبيق المرشحات
        if ($filters['status'] ?? null) {
            $query->where('status', $filters['status']);
        }

        if ($filters['assignee_id'] ?? null) {
            $query->where('assignee_id', $filters['assignee_id']);
        }

        if ($filters['priority'] ?? null) {
            $query->where('priority', $filters['priority']);
        }

        if ($filters['due_date_from'] ?? null) {
            $query->where('due_date', '>=', $filters['due_date_from']);
        }

        if ($filters['due_date_to'] ?? null) {
            $query->where('due_date', '<=', $filters['due_date_to']);
        }

        // الترتيب
        $sortBy = $filters['sort_by'] ?? 'due_date';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        $tasks = $query->paginate($filters['per_page'] ?? 25);

        return [
            'view_type' => 'list',
            'tasks' => $tasks,
            'filters_applied' => $filters,
            'available_filters' => $this->getAvailableFilters($project),
        ];
    }

    /**
     * عرض التقويم
     */
    protected function getCalendarView(Project $project, array $filters): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth();
        $endDate = $filters['end_date'] ?? now()->endOfMonth();

        $tasks = $project->tasks()
                        ->whereBetween('due_date', [$startDate, $endDate])
                        ->with(['assignee'])
                        ->get();

        $events = [];
        foreach ($tasks as $task) {
            $events[] = [
                'id' => $task->id,
                'title' => $task->title,
                'start' => $task->start_date?->format('Y-m-d'),
                'end' => $task->due_date?->format('Y-m-d'),
                'color' => $this->getTaskColor($task),
                'assignee' => $task->assignee?->name,
                'status' => $task->status,
                'priority' => $task->priority,
            ];
        }

        // إضافة المعالم
        $milestones = $project->milestones()
                             ->whereBetween('due_date', [$startDate, $endDate])
                             ->get();

        foreach ($milestones as $milestone) {
            $events[] = [
                'id' => 'milestone_' . $milestone->id,
                'title' => '🎯 ' . $milestone->name,
                'start' => $milestone->due_date->format('Y-m-d'),
                'end' => $milestone->due_date->format('Y-m-d'),
                'color' => '#ff6b6b',
                'type' => 'milestone',
            ];
        }

        return [
            'view_type' => 'calendar',
            'events' => $events,
            'calendar_settings' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'default_view' => 'month',
            ],
        ];
    }

    /**
     * إدارة الموارد والجدولة
     */
    public function getResourceManagement(int $projectId, array $filters = []): array
    {
        $project = Project::with(['teamMembers', 'tasks.assignees'])->findOrFail($projectId);

        return [
            'team_workload' => $this->calculateTeamWorkload($project, $filters),
            'resource_allocation' => $this->getResourceAllocation($project),
            'capacity_planning' => $this->getCapacityPlanning($project, $filters),
            'skill_matrix' => $this->getSkillMatrix($project),
            'availability_calendar' => $this->getAvailabilityCalendar($project, $filters),
            'workload_balance' => $this->analyzeWorkloadBalance($project),
        ];
    }

    /**
     * حساب حمولة الفريق
     */
    protected function calculateTeamWorkload(Project $project, array $filters): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfWeek();
        $endDate = $filters['end_date'] ?? now()->endOfWeek()->addWeeks(3);

        $workload = [];

        foreach ($project->teamMembers as $member) {
            $memberTasks = $member->tasks()
                                 ->where('project_id', $project->id)
                                 ->whereBetween('due_date', [$startDate, $endDate])
                                 ->get();

            $totalHours = $memberTasks->sum('estimated_hours');
            $workingDays = $startDate->diffInWeekdays($endDate);
            $dailyCapacity = 8; // 8 ساعات يومياً
            $totalCapacity = $workingDays * $dailyCapacity;

            $workload[] = [
                'employee' => $member,
                'total_hours' => $totalHours,
                'total_capacity' => $totalCapacity,
                'utilization_percentage' => $totalCapacity > 0 ? ($totalHours / $totalCapacity) * 100 : 0,
                'tasks_count' => $memberTasks->count(),
                'overloaded' => $totalHours > $totalCapacity,
                'tasks' => $memberTasks->map(function ($task) {
                    return [
                        'id' => $task->id,
                        'title' => $task->title,
                        'estimated_hours' => $task->estimated_hours,
                        'due_date' => $task->due_date,
                        'priority' => $task->priority,
                    ];
                }),
            ];
        }

        return $workload;
    }

    /**
     * تحليل الأداء والتقارير
     */
    public function generateProjectReport(int $projectId, string $reportType, array $parameters = []): array
    {
        $project = Project::with([
            'tasks',
            'teamMembers',
            'timeEntries',
            'milestones'
        ])->findOrFail($projectId);

        return match ($reportType) {
            'progress' => $this->generateProgressReport($project, $parameters),
            'time_tracking' => $this->generateTimeTrackingReport($project, $parameters),
            'budget' => $this->generateBudgetReport($project, $parameters),
            'team_performance' => $this->generateTeamPerformanceReport($project, $parameters),
            'quality' => $this->generateQualityReport($project, $parameters),
            'executive_summary' => $this->generateExecutiveSummary($project, $parameters),
            default => throw new \InvalidArgumentException("نوع التقرير غير مدعوم: {$reportType}"),
        };
    }

    /**
     * إدارة المخاطر والمشكلات
     */
    public function manageProjectRisks(int $projectId): array
    {
        $project = Project::with(['risks', 'issues'])->findOrFail($projectId);

        return [
            'risk_matrix' => $this->generateRiskMatrix($project),
            'active_risks' => $project->risks()->where('status', 'ACTIVE')->get(),
            'mitigation_plans' => $this->getMitigationPlans($project),
            'risk_trends' => $this->analyzeRiskTrends($project),
            'issue_tracking' => $this->getIssueTracking($project),
            'escalation_alerts' => $this->getEscalationAlerts($project),
        ];
    }

    /**
     * التكامل مع الأدوات الخارجية
     */
    public function integrateWithExternalTools(int $projectId, string $tool, array $config): array
    {
        $project = Project::findOrFail($projectId);

        return match ($tool) {
            'slack' => $this->integrateWithSlack($project, $config),
            'teams' => $this->integrateWithTeams($project, $config),
            'google_calendar' => $this->integrateWithGoogleCalendar($project, $config),
            'zoom' => $this->integrateWithZoom($project, $config),
            default => throw new \InvalidArgumentException("الأداة غير مدعومة: {$tool}"),
        };
    }

    // دوال مساعدة
    protected function generateProjectCode(string $name): string
    {
        $prefix = strtoupper(substr($name, 0, 3));
        $timestamp = now()->format('ymd');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$timestamp}-{$random}";
    }

    protected function getDefaultProjectSettings(string $methodology): array
    {
        return [
            'methodology' => $methodology,
            'sprint_duration' => $methodology === 'SCRUM' ? 14 : null,
            'story_points_enabled' => in_array($methodology, ['AGILE', 'SCRUM']),
            'time_tracking_required' => true,
            'approval_workflow_enabled' => true,
            'notifications' => [
                'task_assignments' => true,
                'deadline_reminders' => true,
                'status_updates' => true,
                'budget_alerts' => true,
            ],
            'automation' => [
                'auto_assign_tasks' => false,
                'auto_update_progress' => true,
                'auto_generate_reports' => false,
            ],
        ];
    }

    protected function addTeamMembers(Project $project, array $teamMembers): void
    {
        foreach ($teamMembers as $memberData) {
            $project->addTeamMember($memberData['employee_id'], [
                'role' => $memberData['role'] ?? 'MEMBER',
                'permissions' => $memberData['permissions'] ?? ['VIEW', 'COMMENT'],
                'hourly_rate' => $memberData['hourly_rate'] ?? null,
                'allocation_percentage' => $memberData['allocation_percentage'] ?? 100,
            ]);
        }
    }

    protected function createDefaultMilestones(Project $project): void
    {
        $milestones = [
            ['name' => 'بداية المشروع', 'due_date' => $project->start_date, 'type' => 'START'],
            ['name' => 'مراجعة منتصف المشروع', 'due_date' => $project->start_date->copy()->addDays($project->start_date->diffInDays($project->end_date) / 2), 'type' => 'REVIEW'],
            ['name' => 'انتهاء المشروع', 'due_date' => $project->end_date, 'type' => 'END'],
        ];

        foreach ($milestones as $milestoneData) {
            $project->milestones()->create($milestoneData);
        }
    }

    protected function createDefaultCostCenters(Project $project): void
    {
        $costCenters = [
            ['name' => 'تطوير', 'code' => 'DEV', 'budget_allocation' => 60],
            ['name' => 'تصميم', 'code' => 'DES', 'budget_allocation' => 20],
            ['name' => 'اختبار', 'code' => 'TEST', 'budget_allocation' => 15],
            ['name' => 'إدارة', 'code' => 'MGT', 'budget_allocation' => 5],
        ];

        foreach ($costCenters as $centerData) {
            $project->costCenters()->create(array_merge($centerData, [
                'budget_amount' => ($project->budget * $centerData['budget_allocation']) / 100,
            ]));
        }
    }

    protected function sendProjectCreationNotifications(Project $project): void
    {
        // إشعار مدير المشروع
        $project->projectManager->notify(new \App\Notifications\ProjectCreatedNotification($project));

        // إشعار أعضاء الفريق
        foreach ($project->teamMembers as $member) {
            $member->notify(new \App\Notifications\ProjectTeamInvitationNotification($project));
        }
    }

    // دوال إضافية للتقارير والتحليلات
    protected function getProjectOverview(Project $project): array { return []; }
    protected function getProgressMetrics(Project $project): array { return []; }
    protected function getTeamPerformance(Project $project, array $filters): array { return []; }
    protected function getTimeAnalytics(Project $project, array $filters): array { return []; }
    protected function getBudgetAnalysis(Project $project): array { return []; }
    protected function getRiskAssessment(Project $project): array { return []; }
    protected function getUpcomingDeadlines(Project $project): array { return []; }
    protected function getRecentActivities(Project $project): array { return []; }
    protected function getQualityMetrics(Project $project): array { return []; }
    protected function getClientSatisfactionMetrics(Project $project): array { return []; }
    protected function getKanbanStatistics($tasks): array { return []; }
    protected function calculateCriticalPath($tasks): array { return []; }
    protected function getAvailableFilters(Project $project): array { return []; }
    protected function getTaskColor(Task $task): string { return '#007bff'; }
    protected function getResourceAllocation(Project $project): array { return []; }
    protected function getCapacityPlanning(Project $project, array $filters): array { return []; }
    protected function getSkillMatrix(Project $project): array { return []; }
    protected function getAvailabilityCalendar(Project $project, array $filters): array { return []; }
    protected function analyzeWorkloadBalance(Project $project): array { return []; }
}
