<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج انحراف الميزانية
 */
class BudgetVariance extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'budget_id',
        'budget_item_id',
        'account_id',
        'period_start',
        'period_end',
        'budgeted_amount',
        'actual_amount',
        'variance_amount',
        'variance_percentage',
        'variance_type',
        'analysis',
        'action_required',
        'responsible_person',
        'status',
        'notes',
        'created_by',
        'reviewed_by',
        'reviewed_at',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'budgeted_amount' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'variance_amount' => 'decimal:2',
        'variance_percentage' => 'decimal:2',
        'action_required' => 'boolean',
        'reviewed_at' => 'datetime',
    ];

    /**
     * أنواع الانحراف
     */
    public const VARIANCE_TYPES = [
        'FAVORABLE' => 'مؤات',
        'UNFAVORABLE' => 'غير مؤات',
        'NEUTRAL' => 'محايد',
    ];

    /**
     * حالات الانحراف
     */
    public const STATUSES = [
        'IDENTIFIED' => 'محدد',
        'UNDER_REVIEW' => 'قيد المراجعة',
        'ACTION_PLANNED' => 'مخطط للعمل',
        'IN_PROGRESS' => 'قيد التنفيذ',
        'RESOLVED' => 'محلول',
        'ACCEPTED' => 'مقبول',
    ];

    /**
     * الميزانية
     */
    public function budget(): BelongsTo
    {
        return $this->belongsTo(Budget::class);
    }

    /**
     * بند الميزانية
     */
    public function budgetItem(): BelongsTo
    {
        return $this->belongsTo(BudgetItem::class);
    }

    /**
     * الحساب
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * من أنشأ التحليل
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * من راجع التحليل
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'reviewed_by');
    }

    /**
     * الحصول على اسم نوع الانحراف
     */
    public function getVarianceTypeNameAttribute(): string
    {
        return self::VARIANCE_TYPES[$this->variance_type] ?? $this->variance_type;
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * فحص إذا كان الانحراف مؤات
     */
    public function isFavorable(): bool
    {
        return $this->variance_type === 'FAVORABLE';
    }

    /**
     * فحص إذا كان الانحراف غير مؤات
     */
    public function isUnfavorable(): bool
    {
        return $this->variance_type === 'UNFAVORABLE';
    }

    /**
     * فحص إذا كان الانحراف كبير
     */
    public function isSignificant(float $threshold = 10.0): bool
    {
        return abs($this->variance_percentage) >= $threshold;
    }

    /**
     * حساب الانحراف
     */
    public static function calculateVariance(float $budgeted, float $actual): array
    {
        $variance = $actual - $budgeted;
        $percentage = $budgeted != 0 ? ($variance / $budgeted) * 100 : 0;

        // تحديد نوع الانحراف
        $type = 'NEUTRAL';
        if ($variance > 0) {
            $type = 'UNFAVORABLE'; // للمصروفات، الزيادة غير مؤاتية
        } elseif ($variance < 0) {
            $type = 'FAVORABLE'; // للمصروفات، النقص مؤات
        }

        return [
            'variance_amount' => $variance,
            'variance_percentage' => round($percentage, 2),
            'variance_type' => $type,
        ];
    }

    /**
     * تحديث حالة الانحراف
     */
    public function updateStatus(string $status, ?string $notes = null): void
    {
        $this->update([
            'status' => $status,
            'notes' => $notes ? "{$this->notes}\n{$notes}" : $this->notes,
        ]);
    }

    /**
     * مراجعة الانحراف
     */
    public function review(int $reviewedBy, string $analysis): void
    {
        $this->update([
            'reviewed_by' => $reviewedBy,
            'reviewed_at' => now(),
            'analysis' => $analysis,
            'status' => 'UNDER_REVIEW',
        ]);
    }

    /**
     * تحديد إجراء مطلوب
     */
    public function requireAction(string $actionDescription): void
    {
        $this->update([
            'action_required' => true,
            'notes' => "{$this->notes}\nإجراء مطلوب: {$actionDescription}",
            'status' => 'ACTION_PLANNED',
        ]);
    }

    /**
     * قبول الانحراف
     */
    public function accept(string $reason): void
    {
        $this->update([
            'status' => 'ACCEPTED',
            'action_required' => false,
            'notes' => "{$this->notes}\nمقبول: {$reason}",
        ]);
    }

    /**
     * حل الانحراف
     */
    public function resolve(string $resolution): void
    {
        $this->update([
            'status' => 'RESOLVED',
            'action_required' => false,
            'notes' => "{$this->notes}\nتم الحل: {$resolution}",
        ]);
    }

    /**
     * Scope للانحرافات المؤاتية
     */
    public function scopeFavorable($query)
    {
        return $query->where('variance_type', 'FAVORABLE');
    }

    /**
     * Scope للانحرافات غير المؤاتية
     */
    public function scopeUnfavorable($query)
    {
        return $query->where('variance_type', 'UNFAVORABLE');
    }

    /**
     * Scope للانحرافات الكبيرة
     */
    public function scopeSignificant($query, float $threshold = 10.0)
    {
        return $query->where(function ($q) use ($threshold) {
            $q->where('variance_percentage', '>=', $threshold)
              ->orWhere('variance_percentage', '<=', -$threshold);
        });
    }

    /**
     * Scope للانحرافات التي تحتاج إجراء
     */
    public function scopeRequiringAction($query)
    {
        return $query->where('action_required', true);
    }

    /**
     * Scope للانحرافات في فترة معينة
     */
    public function scopeInPeriod($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('period_start', [$startDate, $endDate]);
    }
}
