<?php

namespace App\Domains\Accounting\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;
use Carbon\Carbon;

/**
 * نموذج الفترة المحاسبية
 * يدير السنوات والفترات المحاسبية
 */
class AccountingPeriod extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'year',
        'start_date',
        'end_date',
        'status',
        'is_current',
        'description',
        'fiscal_year_type',
        'currency',
        'exchange_rate',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_current' => 'boolean',
        'exchange_rate' => 'decimal:6',
        'metadata' => 'array',
    ];

    /**
     * حالات الفترة المحاسبية
     */
    public const STATUSES = [
        'OPEN' => 'مفتوحة',
        'CLOSED' => 'مغلقة',
        'LOCKED' => 'مقفلة',
        'ARCHIVED' => 'مؤرشفة',
    ];

    /**
     * أنواع السنة المالية
     */
    public const FISCAL_YEAR_TYPES = [
        'CALENDAR' => 'سنة ميلادية',
        'FISCAL' => 'سنة مالية',
        'CUSTOM' => 'مخصصة',
    ];

    /**
     * القيود المحاسبية في هذه الفترة
     */
    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class, 'period_id');
    }

    /**
     * التحقق من كون الفترة مفتوحة
     */
    public function isOpen(): bool
    {
        return $this->status === 'OPEN';
    }

    /**
     * التحقق من كون الفترة مغلقة
     */
    public function isClosed(): bool
    {
        return $this->status === 'CLOSED';
    }

    /**
     * التحقق من كون الفترة مقفلة
     */
    public function isLocked(): bool
    {
        return $this->status === 'LOCKED';
    }

    /**
     * التحقق من كون الفترة الحالية
     */
    public function isCurrent(): bool
    {
        return $this->is_current;
    }

    /**
     * إغلاق الفترة المحاسبية
     */
    public function close(): bool
    {
        if (!$this->isOpen()) {
            return false;
        }

        // التحقق من توازن جميع القيود
        $unbalancedEntries = $this->journalEntries()
            ->selectRaw('entry_number, SUM(debit_amount) as total_debits, SUM(credit_amount) as total_credits')
            ->groupBy('entry_number')
            ->havingRaw('ABS(SUM(debit_amount) - SUM(credit_amount)) > 0.01')
            ->count();

        if ($unbalancedEntries > 0) {
            throw new \Exception('لا يمكن إغلاق الفترة: توجد قيود غير متوازنة');
        }

        $this->status = 'CLOSED';
        return $this->save();
    }

    /**
     * قفل الفترة المحاسبية
     */
    public function lock(): bool
    {
        if (!$this->isClosed()) {
            return false;
        }

        $this->status = 'LOCKED';
        return $this->save();
    }

    /**
     * إعادة فتح الفترة المحاسبية
     */
    public function reopen(): bool
    {
        if ($this->isLocked()) {
            return false;
        }

        $this->status = 'OPEN';
        return $this->save();
    }

    /**
     * تعيين كفترة حالية
     */
    public function setCurrent(): bool
    {
        // إلغاء تعيين الفترات الأخرى كحالية
        static::where('is_current', true)->update(['is_current' => false]);

        $this->is_current = true;
        return $this->save();
    }

    /**
     * الحصول على الفترة الحالية
     */
    public static function current(): ?self
    {
        return static::where('is_current', true)->first();
    }

    /**
     * إنشاء فترة محاسبية جديدة
     */
    public static function createForYear(int $year, string $type = 'CALENDAR'): self
    {
        $startDate = $type === 'CALENDAR'
            ? Carbon::create($year, 1, 1)
            : Carbon::create($year, 7, 1); // السنة المالية تبدأ في يوليو

        $endDate = $startDate->copy()->addYear()->subDay();

        return static::create([
            'name' => "السنة المالية {$year}",
            'year' => $year,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'OPEN',
            'fiscal_year_type' => $type,
            'currency' => 'MAD',
            'exchange_rate' => 1.0,
        ]);
    }

    /**
     * التحقق من وجود تاريخ ضمن الفترة
     */
    public function containsDate(Carbon $date): bool
    {
        return $date->between($this->start_date, $this->end_date);
    }

    /**
     * الحصول على عدد الأيام في الفترة
     */
    public function getDaysCount(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * نطاق للفترات المفتوحة
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'OPEN');
    }

    /**
     * نطاق للفترات المغلقة
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'CLOSED');
    }

    /**
     * نطاق للفترة الحالية
     */
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    /**
     * نطاق حسب السنة
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }
}
