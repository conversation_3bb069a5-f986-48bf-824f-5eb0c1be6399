<?php

namespace App\Domains\Projects\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج الاجتماع الافتراضي - Virtual Meeting
 */
class VirtualMeeting extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'platform',
        'scheduled_at',
        'duration_minutes',
        'meeting_url',
        'meeting_id',
        'password',
        'is_recurring',
        'recurrence_pattern',
        'max_participants',
        'waiting_room_enabled',
        'recording_enabled',
        'created_by',
        'settings',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'duration_minutes' => 'integer',
        'is_recurring' => 'boolean',
        'max_participants' => 'integer',
        'waiting_room_enabled' => 'boolean',
        'recording_enabled' => 'boolean',
        'settings' => 'array',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }
}
