<?php

namespace App\Domains\Integration\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Scaling Event Model
 * 
 * Tracks auto-scaling events for API gateways and endpoints
 */
class ScalingEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_gateway_id',
        'api_endpoint_id',
        'event_type',
        'trigger_reason',
        'trigger_metrics',
        'scaling_action',
        'instances_before',
        'instances_after',
        'target_instances',
        'scaling_config',
        'status',
        'started_at',
        'completed_at',
        'duration_seconds',
        'success',
        'error_message',
        'cost_impact',
        'performance_impact',
        'metadata',
    ];

    protected $casts = [
        'trigger_metrics' => 'array',
        'scaling_config' => 'array',
        'metadata' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'duration_seconds' => 'integer',
        'instances_before' => 'integer',
        'instances_after' => 'integer',
        'target_instances' => 'integer',
        'success' => 'boolean',
        'cost_impact' => 'decimal:2',
        'performance_impact' => 'decimal:2',
    ];

    /**
     * Relationship with API Gateway
     */
    public function apiGateway(): BelongsTo
    {
        return $this->belongsTo(ApiGateway::class);
    }

    /**
     * Relationship with API Endpoint
     */
    public function apiEndpoint(): BelongsTo
    {
        return $this->belongsTo(ApiEndpoint::class);
    }

    /**
     * Mark scaling event as completed
     */
    public function markAsCompleted(int $finalInstances, array $metadata = []): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'instances_after' => $finalInstances,
            'duration_seconds' => $this->started_at ? now()->diffInSeconds($this->started_at) : 0,
            'success' => true,
            'metadata' => array_merge($this->metadata ?? [], $metadata),
        ]);
    }

    /**
     * Mark scaling event as failed
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'duration_seconds' => $this->started_at ? now()->diffInSeconds($this->started_at) : 0,
            'success' => false,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Calculate cost impact
     */
    public function calculateCostImpact(): float
    {
        $instanceDiff = $this->instances_after - $this->instances_before;
        $costPerInstance = $this->scaling_config['cost_per_instance_hour'] ?? 0.10;
        $durationHours = $this->duration_seconds / 3600;
        
        return $instanceDiff * $costPerInstance * $durationHours;
    }

    /**
     * Calculate performance impact
     */
    public function calculatePerformanceImpact(): float
    {
        // This would calculate the performance improvement/degradation
        // based on metrics before and after scaling
        return 0.0;
    }
}
