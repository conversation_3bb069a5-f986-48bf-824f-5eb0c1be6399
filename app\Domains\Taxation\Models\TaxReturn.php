<?php

namespace App\Domains\Taxation\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج الإقرار الضريبي
 * يدعم جميع أنواع الإقرارات الضريبية مع الإيداع الإلكتروني
 */
class TaxReturn extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'tax_system_id',
        'company_id',
        'return_type',
        'tax_period_type',
        'tax_year',
        'period_start_date',
        'period_end_date',
        'due_date',
        'filing_date',
        'status',
        'total_revenue',
        'total_expenses',
        'taxable_income',
        'tax_calculated',
        'tax_paid',
        'tax_due',
        'penalties',
        'interest',
        'total_amount_due',
        'payment_status',
        'submission_method',
        'reference_number',
        'acknowledgment_number',
        'digital_signature',
        'attachments',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'period_start_date' => 'date',
        'period_end_date' => 'date',
        'due_date' => 'date',
        'filing_date' => 'datetime',
        'total_revenue' => 'decimal:2',
        'total_expenses' => 'decimal:2',
        'taxable_income' => 'decimal:2',
        'tax_calculated' => 'decimal:2',
        'tax_paid' => 'decimal:2',
        'tax_due' => 'decimal:2',
        'penalties' => 'decimal:2',
        'interest' => 'decimal:2',
        'total_amount_due' => 'decimal:2',
        'attachments' => 'array',
        'metadata' => 'array',
    ];

    /**
     * أنواع الإقرارات
     */
    public const RETURN_TYPES = [
        'VAT_MONTHLY' => 'ضريبة القيمة المضافة - شهري',
        'VAT_QUARTERLY' => 'ضريبة القيمة المضافة - ربع سنوي',
        'CORPORATE_ANNUAL' => 'ضريبة الشركات - سنوي',
        'INCOME_ANNUAL' => 'ضريبة الدخل - سنوي',
        'WITHHOLDING_MONTHLY' => 'ضريبة الاستقطاع - شهري',
        'SOCIAL_SECURITY_MONTHLY' => 'الضمان الاجتماعي - شهري',
        'PAYROLL_MONTHLY' => 'كشف الرواتب - شهري',
        'ANNUAL_DECLARATION' => 'الإقرار السنوي',
    ];

    /**
     * أنواع الفترات الضريبية
     */
    public const PERIOD_TYPES = [
        'MONTHLY' => 'شهري',
        'QUARTERLY' => 'ربع سنوي',
        'SEMI_ANNUAL' => 'نصف سنوي',
        'ANNUAL' => 'سنوي',
    ];

    /**
     * حالات الإقرار
     */
    public const STATUSES = [
        'DRAFT' => 'مسودة',
        'CALCULATED' => 'محسوب',
        'REVIEWED' => 'مراجع',
        'APPROVED' => 'معتمد',
        'SUBMITTED' => 'مقدم',
        'ACCEPTED' => 'مقبول',
        'REJECTED' => 'مرفوض',
        'AMENDED' => 'معدل',
        'PAID' => 'مدفوع',
        'OVERDUE' => 'متأخر',
    ];

    /**
     * حالات الدفع
     */
    public const PAYMENT_STATUSES = [
        'NOT_REQUIRED' => 'غير مطلوب',
        'PENDING' => 'في الانتظار',
        'PARTIAL' => 'جزئي',
        'PAID' => 'مدفوع',
        'OVERDUE' => 'متأخر',
        'REFUND_DUE' => 'استرداد مستحق',
    ];

    /**
     * طرق التقديم
     */
    public const SUBMISSION_METHODS = [
        'ELECTRONIC' => 'إلكتروني',
        'PAPER' => 'ورقي',
        'HYBRID' => 'مختلط',
    ];

    /**
     * النظام الضريبي
     */
    public function taxSystem(): BelongsTo
    {
        return $this->belongsTo(TaxSystem::class);
    }

    /**
     * الشركة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * بنود الإقرار
     */
    public function items(): HasMany
    {
        return $this->hasMany(TaxReturnItem::class);
    }

    /**
     * المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(TaxPayment::class);
    }

    /**
     * حساب الضرائب
     */
    public function calculateTaxes(): void
    {
        $totalTax = 0;
        $breakdown = [];

        foreach ($this->items as $item) {
            $taxCalculation = $item->calculateTax();
            $totalTax += $taxCalculation['tax_amount'];
            $breakdown[] = $taxCalculation;
        }

        $this->tax_calculated = $totalTax;
        $this->tax_due = max(0, $totalTax - $this->tax_paid);
        $this->total_amount_due = $this->tax_due + $this->penalties + $this->interest;

        $this->metadata = array_merge($this->metadata ?? [], [
            'tax_breakdown' => $breakdown,
            'calculated_at' => now()->toISOString(),
        ]);

        $this->save();
    }

    /**
     * حساب الغرامات والفوائد
     */
    public function calculatePenaltiesAndInterest(): void
    {
        if ($this->status === 'OVERDUE' && $this->due_date < now()) {
            $daysLate = now()->diffInDays($this->due_date);

            // حساب الغرامة (مثال: 1% من المبلغ المستحق لكل شهر تأخير)
            $monthsLate = ceil($daysLate / 30);
            $penaltyRate = 0.01; // 1%
            $this->penalties = $this->tax_due * $penaltyRate * $monthsLate;

            // حساب الفوائد (مثال: 0.5% شهرياً)
            $interestRate = 0.005; // 0.5%
            $this->interest = $this->tax_due * $interestRate * $monthsLate;

            $this->total_amount_due = $this->tax_due + $this->penalties + $this->interest;
            $this->save();
        }
    }

    /**
     * تقديم الإقرار إلكترونياً
     */
    public function submitElectronically(): array
    {
        try {
            // التحقق من اكتمال البيانات
            $validation = $this->validateForSubmission();
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                ];
            }

            // إنشاء التوقيع الرقمي
            $this->digital_signature = $this->generateDigitalSignature();

            // تحضير البيانات للإرسال
            $submissionData = $this->prepareSubmissionData();

            // إرسال للهيئة الضريبية
            $response = $this->sendToTaxAuthority($submissionData);

            if ($response['success']) {
                $this->status = 'SUBMITTED';
                $this->filing_date = now();
                $this->submission_method = 'ELECTRONIC';
                $this->reference_number = $response['reference_number'];
                $this->acknowledgment_number = $response['acknowledgment_number'];
                $this->save();

                return [
                    'success' => true,
                    'reference_number' => $this->reference_number,
                    'acknowledgment_number' => $this->acknowledgment_number,
                ];
            }

            return [
                'success' => false,
                'errors' => $response['errors'] ?? ['فشل في الإرسال'],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * التحقق من صحة البيانات للتقديم
     */
    protected function validateForSubmission(): array
    {
        $errors = [];

        if (!$this->company_id) {
            $errors[] = 'معرف الشركة مطلوب';
        }

        if (!$this->taxable_income && $this->taxable_income !== 0) {
            $errors[] = 'الدخل الخاضع للضريبة مطلوب';
        }

        if (!$this->tax_calculated && $this->tax_calculated !== 0) {
            $errors[] = 'يجب حساب الضريبة أولاً';
        }

        if ($this->items()->count() === 0) {
            $errors[] = 'يجب إضافة بنود للإقرار';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * توليد التوقيع الرقمي
     */
    protected function generateDigitalSignature(): string
    {
        $data = [
            'return_type' => $this->return_type,
            'tax_year' => $this->tax_year,
            'company_id' => $this->company_id,
            'taxable_income' => $this->taxable_income,
            'tax_calculated' => $this->tax_calculated,
            'timestamp' => now()->timestamp,
        ];

        return hash('sha256', json_encode($data) . config('app.key'));
    }

    /**
     * تحضير بيانات التقديم
     */
    protected function prepareSubmissionData(): array
    {
        return [
            'return_info' => [
                'type' => $this->return_type,
                'tax_year' => $this->tax_year,
                'period_start' => $this->period_start_date->format('Y-m-d'),
                'period_end' => $this->period_end_date->format('Y-m-d'),
            ],
            'company_info' => [
                'id' => $this->company_id,
                'tax_id' => $this->company->tax_id ?? '',
            ],
            'financial_data' => [
                'total_revenue' => $this->total_revenue,
                'total_expenses' => $this->total_expenses,
                'taxable_income' => $this->taxable_income,
                'tax_calculated' => $this->tax_calculated,
                'tax_paid' => $this->tax_paid,
                'tax_due' => $this->tax_due,
            ],
            'items' => $this->items->map(function ($item) {
                return $item->toSubmissionArray();
            })->toArray(),
            'signature' => $this->digital_signature,
        ];
    }

    /**
     * إرسال للهيئة الضريبية
     */
    protected function sendToTaxAuthority(array $data): array
    {
        // هذه دالة وهمية - يجب تنفيذها حسب API الهيئة الضريبية
        // في المغرب، سيكون هذا تكامل مع منصة الإدارة العامة للضرائب

        $endpoints = $this->taxSystem->getAPIEndpoints();
        $endpoint = $endpoints['tax_return_submission'] ?? null;

        if (!$endpoint) {
            return [
                'success' => false,
                'errors' => ['نقطة النهاية للإرسال غير محددة'],
            ];
        }

        // محاكاة الإرسال
        return [
            'success' => true,
            'reference_number' => 'REF-' . now()->format('YmdHis'),
            'acknowledgment_number' => 'ACK-' . uniqid(),
        ];
    }

    /**
     * تحديث حالة الدفع
     */
    public function updatePaymentStatus(): void
    {
        $totalPaid = $this->payments()->where('status', 'COMPLETED')->sum('amount');
        $this->tax_paid = $totalPaid;

        if ($totalPaid == 0) {
            $this->payment_status = 'PENDING';
        } elseif ($totalPaid < $this->total_amount_due) {
            $this->payment_status = 'PARTIAL';
        } elseif ($totalPaid >= $this->total_amount_due) {
            $this->payment_status = 'PAID';
            if ($this->status === 'SUBMITTED') {
                $this->status = 'PAID';
            }
        }

        $this->save();
    }

    /**
     * إنشاء إقرار معدل
     */
    public function createAmendment(): self
    {
        $amendment = $this->replicate();
        $amendment->status = 'DRAFT';
        $amendment->filing_date = null;
        $amendment->reference_number = null;
        $amendment->acknowledgment_number = null;
        $amendment->digital_signature = null;
        $amendment->metadata = array_merge($amendment->metadata ?? [], [
            'amended_from' => $this->id,
            'amendment_reason' => '',
        ]);
        $amendment->save();

        // نسخ البنود
        foreach ($this->items as $item) {
            $newItem = $item->replicate();
            $newItem->tax_return_id = $amendment->id;
            $newItem->save();
        }

        return $amendment;
    }

    /**
     * نطاق للإقرارات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereIn('status', ['DRAFT', 'CALCULATED', 'REVIEWED']);
    }

    /**
     * نطاق حسب نوع الإقرار
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('return_type', $type);
    }

    /**
     * نطاق حسب السنة الضريبية
     */
    public function scopeByTaxYear($query, int $year)
    {
        return $query->where('tax_year', $year);
    }

    /**
     * نطاق حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }
}
