<?php

namespace App\Domains\Taxation\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Taxation\Models\TaxRule;
use App\Domains\Taxation\Models\TaxSystem;
use App\Domains\Taxation\Services\DynamicTaxEngine;
use App\Domains\Taxation\Requests\StoreTaxRuleRequest;
use App\Domains\Taxation\Requests\UpdateTaxRuleRequest;
use App\Domains\Taxation\Resources\TaxRuleResource;
use App\Domains\Taxation\Resources\TaxRuleCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * متحكم القواعد الضريبية
 * إدارة شاملة للقواعد الضريبية المعقدة
 */
class TaxRuleController extends Controller
{
    protected DynamicTaxEngine $taxEngine;

    public function __construct(DynamicTaxEngine $taxEngine)
    {
        $this->taxEngine = $taxEngine;
    }

    /**
     * عرض قائمة القواعد الضريبية
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', TaxRule::class);

        $query = TaxRule::with(['taxSystem']);

        // التصفية حسب النظام الضريبي
        if ($request->filled('tax_system_id')) {
            $query->where('tax_system_id', $request->tax_system_id);
        }

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_ar', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // التصفية حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // التصفية حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // التصفية حسب الفئة
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // التصفية حسب تاريخ السريان
        if ($request->filled('effective_date')) {
            $query->where('effective_from', '<=', $request->effective_date)
                  ->where(function ($q) use ($request) {
                      $q->whereNull('effective_to')
                        ->orWhere('effective_to', '>=', $request->effective_date);
                  });
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $taxRules = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new TaxRuleCollection($taxRules),
            'meta' => [
                'total' => $taxRules->total(),
                'per_page' => $taxRules->perPage(),
                'current_page' => $taxRules->currentPage(),
                'last_page' => $taxRules->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء قاعدة ضريبية جديدة
     */
    public function store(StoreTaxRuleRequest $request): JsonResponse
    {
        $this->authorize('create', TaxRule::class);

        DB::beginTransaction();

        try {
            $taxRule = TaxRule::create($request->validated());

            // تحديث الكاش
            Cache::forget("tax_rules_{$taxRule->tax_system_id}");
            Cache::forget("tax_rules_{$taxRule->type}");

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء القاعدة الضريبية بنجاح',
                'data' => new TaxRuleResource($taxRule->load('taxSystem')),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل قاعدة ضريبية محددة
     */
    public function show(int $id): JsonResponse
    {
        $taxRule = TaxRule::with(['taxSystem', 'calculations'])->findOrFail($id);
        $this->authorize('view', $taxRule);

        // إضافة معلومات إضافية
        $additionalData = [
            'usage_statistics' => $this->getRuleUsageStatistics($taxRule),
            'validation_results' => $this->validateRule($taxRule),
            'similar_rules' => $this->getSimilarRules($taxRule),
            'calculation_examples' => $this->getCalculationExamples($taxRule),
        ];

        return response()->json([
            'success' => true,
            'data' => new TaxRuleResource($taxRule),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث القاعدة الضريبية
     */
    public function update(UpdateTaxRuleRequest $request, int $id): JsonResponse
    {
        $taxRule = TaxRule::findOrFail($id);
        $this->authorize('update', $taxRule);

        DB::beginTransaction();

        try {
            $oldData = $taxRule->toArray();
            $taxRule->update($request->validated());

            // تسجيل التغييرات
            $this->logRuleChanges($taxRule, $oldData, $request->validated());

            // تحديث الكاش
            Cache::forget("tax_rules_{$taxRule->tax_system_id}");
            Cache::forget("tax_rules_{$taxRule->type}");

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث القاعدة الضريبية بنجاح',
                'data' => new TaxRuleResource($taxRule->load('taxSystem')),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف القاعدة الضريبية
     */
    public function destroy(int $id): JsonResponse
    {
        $taxRule = TaxRule::findOrFail($id);
        $this->authorize('delete', $taxRule);

        try {
            // التحقق من عدم وجود حسابات مرتبطة
            if ($taxRule->calculations()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف القاعدة الضريبية لوجود حسابات مرتبطة بها',
                ], 422);
            }

            $taxSystemId = $taxRule->tax_system_id;
            $type = $taxRule->type;

            $taxRule->delete();

            // تحديث الكاش
            Cache::forget("tax_rules_{$taxSystemId}");
            Cache::forget("tax_rules_{$type}");

            return response()->json([
                'success' => true,
                'message' => 'تم حذف القاعدة الضريبية بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تفعيل/إلغاء تفعيل القاعدة الضريبية
     */
    public function toggleStatus(int $id): JsonResponse
    {
        $taxRule = TaxRule::findOrFail($id);
        $this->authorize('update', $taxRule);

        try {
            $taxRule->update(['is_active' => !$taxRule->is_active]);

            // تحديث الكاش
            Cache::forget("tax_rules_{$taxRule->tax_system_id}");
            Cache::forget("tax_rules_{$taxRule->type}");

            $status = $taxRule->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

            return response()->json([
                'success' => true,
                'message' => "{$status} القاعدة الضريبية بنجاح",
                'data' => new TaxRuleResource($taxRule),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير حالة القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * اختبار القاعدة الضريبية
     */
    public function testRule(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'context' => 'nullable|array',
        ]);

        $taxRule = TaxRule::findOrFail($id);
        $this->authorize('view', $taxRule);

        try {
            $result = $taxRule->calculateTax($request->amount, $request->context ?? []);

            return response()->json([
                'success' => true,
                'message' => 'تم اختبار القاعدة الضريبية بنجاح',
                'data' => [
                    'rule' => new TaxRuleResource($taxRule),
                    'test_input' => [
                        'amount' => $request->amount,
                        'context' => $request->context ?? [],
                    ],
                    'calculation_result' => $result,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في اختبار القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * نسخ القاعدة الضريبية
     */
    public function duplicate(int $id): JsonResponse
    {
        $taxRule = TaxRule::findOrFail($id);
        $this->authorize('create', TaxRule::class);

        try {
            $newRule = $taxRule->replicate();
            $newRule->name = $taxRule->name . ' (نسخة)';
            $newRule->name_ar = $taxRule->name_ar . ' (نسخة)';
            $newRule->is_active = false;
            $newRule->save();

            return response()->json([
                'success' => true,
                'message' => 'تم نسخ القاعدة الضريبية بنجاح',
                'data' => new TaxRuleResource($newRule->load('taxSystem')),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء نسخ القاعدة الضريبية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات استخدام القاعدة
     */
    protected function getRuleUsageStatistics(TaxRule $taxRule): array
    {
        return [
            'total_calculations' => $taxRule->calculations()->count(),
            'calculations_this_month' => $taxRule->calculations()
                ->whereMonth('created_at', now()->month)
                ->count(),
            'total_tax_calculated' => $taxRule->calculations()->sum('tax_amount'),
            'average_tax_amount' => $taxRule->calculations()->avg('tax_amount'),
            'last_used' => $taxRule->calculations()->latest()->first()?->created_at,
        ];
    }

    /**
     * التحقق من صحة القاعدة
     */
    protected function validateRule(TaxRule $taxRule): array
    {
        $issues = [];

        // التحقق من التواريخ
        if ($taxRule->effective_to && $taxRule->effective_to <= $taxRule->effective_from) {
            $issues[] = 'تاريخ انتهاء السريان يجب أن يكون بعد تاريخ بداية السريان';
        }

        // التحقق من المعدل
        if ($taxRule->rate < 0 || $taxRule->rate > 100) {
            $issues[] = 'معدل الضريبة يجب أن يكون بين 0 و 100';
        }

        // التحقق من الشروط
        if (!empty($taxRule->conditions)) {
            foreach ($taxRule->conditions as $condition) {
                if (!isset($condition['field']) || !isset($condition['operator']) || !isset($condition['value'])) {
                    $issues[] = 'شرط غير صحيح في القاعدة';
                    break;
                }
            }
        }

        return [
            'is_valid' => empty($issues),
            'issues' => $issues,
            'last_validation' => now(),
        ];
    }

    /**
     * الحصول على قواعد مشابهة
     */
    protected function getSimilarRules(TaxRule $taxRule): array
    {
        return TaxRule::where('id', '!=', $taxRule->id)
            ->where('type', $taxRule->type)
            ->where('tax_system_id', $taxRule->tax_system_id)
            ->where('is_active', true)
            ->limit(5)
            ->get(['id', 'name', 'name_ar', 'rate', 'category'])
            ->toArray();
    }

    /**
     * الحصول على أمثلة الحسابات
     */
    protected function getCalculationExamples(TaxRule $taxRule): array
    {
        $examples = [];
        $testAmounts = [100, 1000, 10000, 50000];

        foreach ($testAmounts as $amount) {
            try {
                $result = $taxRule->calculateTax($amount);
                $examples[] = [
                    'amount' => $amount,
                    'tax_amount' => $result['tax_amount'],
                    'total_amount' => $result['total_amount'],
                    'effective_rate' => $result['effective_rate'],
                ];
            } catch (\Exception $e) {
                // تجاهل الأخطاء في الأمثلة
            }
        }

        return $examples;
    }

    /**
     * تسجيل التغييرات في القاعدة
     */
    protected function logRuleChanges(TaxRule $taxRule, array $oldData, array $newData): void
    {
        $changes = [];
        foreach ($newData as $key => $value) {
            if (isset($oldData[$key]) && $oldData[$key] !== $value) {
                $changes[$key] = [
                    'old' => $oldData[$key],
                    'new' => $value,
                ];
            }
        }

        if (!empty($changes)) {
            activity()
                ->performedOn($taxRule)
                ->causedBy(auth()->user())
                ->withProperties([
                    'changes' => $changes,
                    'rule_name' => $taxRule->name,
                    'rule_type' => $taxRule->type,
                ])
                ->log('تم تحديث القاعدة الضريبية');
        }
    }
}
