<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckUserStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();

            // التحقق من كون المستخدم نشط
            if (!$user->isActive()) {
                Auth::logout();

                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => 'تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة.',
                        'error' => 'Account Deactivated'
                    ], 403);
                }

                return redirect()->route('login')
                    ->withErrors(['email' => 'تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة.']);
            }

            // تحديث وقت آخر تسجيل دخول
            $user->update(['last_login_at' => now()]);
        }

        return $next($request);
    }
}
