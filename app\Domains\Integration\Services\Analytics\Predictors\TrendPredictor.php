<?php

namespace App\Domains\Integration\Services\Analytics\Predictors;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Trend Predictor for Analytics
 * 
 * Predicts future trends based on historical data using various algorithms
 */
class TrendPredictor
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'prediction_window' => 7, // days
            'min_data_points' => 10,
            'confidence_threshold' => 0.7,
            'smoothing_factor' => 0.3,
        ], $config);
    }

    /**
     * Predict future trends for given metrics
     */
    public function predictTrends(string $gatewayId, array $metrics, int $daysAhead = 7): array
    {
        try {
            $predictions = [];

            foreach ($metrics as $metricName => $historicalData) {
                $prediction = $this->predictMetric($gatewayId, $metricName, $historicalData, $daysAhead);
                $predictions[$metricName] = $prediction;
            }

            return [
                'gateway_id' => $gatewayId,
                'prediction_date' => now()->toISOString(),
                'days_ahead' => $daysAhead,
                'predictions' => $predictions,
                'confidence_level' => $this->calculateOverallConfidence($predictions),
            ];

        } catch (\Exception $e) {
            Log::error('Trend prediction failed', [
                'gateway_id' => $gatewayId,
                'error' => $e->getMessage(),
            ]);

            return [
                'error' => 'Prediction failed: ' . $e->getMessage(),
                'gateway_id' => $gatewayId,
            ];
        }
    }

    /**
     * Predict individual metric
     */
    protected function predictMetric(string $gatewayId, string $metricName, array $historicalData, int $daysAhead): array
    {
        if (count($historicalData) < $this->config['min_data_points']) {
            return [
                'error' => 'Insufficient data points',
                'required' => $this->config['min_data_points'],
                'available' => count($historicalData),
            ];
        }

        // Prepare time series data
        $timeSeries = $this->prepareTimeSeries($historicalData);
        
        // Apply different prediction algorithms
        $predictions = [
            'linear_regression' => $this->linearRegressionPredict($timeSeries, $daysAhead),
            'exponential_smoothing' => $this->exponentialSmoothingPredict($timeSeries, $daysAhead),
            'moving_average' => $this->movingAveragePredict($timeSeries, $daysAhead),
            'seasonal_decomposition' => $this->seasonalDecompositionPredict($timeSeries, $daysAhead),
        ];

        // Ensemble prediction (weighted average)
        $ensemblePrediction = $this->ensemblePredict($predictions);

        // Calculate confidence intervals
        $confidenceIntervals = $this->calculateConfidenceIntervals($timeSeries, $ensemblePrediction);

        return [
            'metric' => $metricName,
            'algorithm_predictions' => $predictions,
            'ensemble_prediction' => $ensemblePrediction,
            'confidence_intervals' => $confidenceIntervals,
            'trend_analysis' => $this->analyzeTrend($timeSeries),
            'seasonality' => $this->detectSeasonality($timeSeries),
            'anomalies' => $this->detectAnomalies($timeSeries),
        ];
    }

    /**
     * Prepare time series data
     */
    protected function prepareTimeSeries(array $historicalData): array
    {
        $timeSeries = [];
        
        foreach ($historicalData as $dataPoint) {
            $timestamp = isset($dataPoint['timestamp']) ? 
                Carbon::parse($dataPoint['timestamp']) : 
                now()->subDays(count($historicalData) - array_search($dataPoint, $historicalData));
                
            $timeSeries[] = [
                'timestamp' => $timestamp,
                'value' => (float) ($dataPoint['value'] ?? $dataPoint),
                'day_of_week' => $timestamp->dayOfWeek,
                'hour_of_day' => $timestamp->hour,
            ];
        }

        // Sort by timestamp
        usort($timeSeries, function ($a, $b) {
            return $a['timestamp']->timestamp <=> $b['timestamp']->timestamp;
        });

        return $timeSeries;
    }

    /**
     * Linear regression prediction
     */
    protected function linearRegressionPredict(array $timeSeries, int $daysAhead): array
    {
        $n = count($timeSeries);
        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;

        foreach ($timeSeries as $i => $point) {
            $x = $i;
            $y = $point['value'];
            
            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        $predictions = [];
        for ($i = 0; $i < $daysAhead; $i++) {
            $x = $n + $i;
            $predictedValue = $slope * $x + $intercept;
            $predictions[] = [
                'day' => $i + 1,
                'value' => max(0, $predictedValue), // Ensure non-negative
                'date' => now()->addDays($i + 1)->toDateString(),
            ];
        }

        return [
            'algorithm' => 'linear_regression',
            'predictions' => $predictions,
            'parameters' => ['slope' => $slope, 'intercept' => $intercept],
            'r_squared' => $this->calculateRSquared($timeSeries, $slope, $intercept),
        ];
    }

    /**
     * Exponential smoothing prediction
     */
    protected function exponentialSmoothingPredict(array $timeSeries, int $daysAhead): array
    {
        $alpha = $this->config['smoothing_factor'];
        $smoothedValues = [];
        
        // Initialize with first value
        $smoothedValues[0] = $timeSeries[0]['value'];
        
        // Calculate smoothed values
        for ($i = 1; $i < count($timeSeries); $i++) {
            $smoothedValues[$i] = $alpha * $timeSeries[$i]['value'] + (1 - $alpha) * $smoothedValues[$i - 1];
        }

        // Predict future values
        $lastSmoothed = end($smoothedValues);
        $predictions = [];
        
        for ($i = 0; $i < $daysAhead; $i++) {
            $predictions[] = [
                'day' => $i + 1,
                'value' => max(0, $lastSmoothed),
                'date' => now()->addDays($i + 1)->toDateString(),
            ];
        }

        return [
            'algorithm' => 'exponential_smoothing',
            'predictions' => $predictions,
            'parameters' => ['alpha' => $alpha],
            'smoothed_values' => $smoothedValues,
        ];
    }

    /**
     * Moving average prediction
     */
    protected function movingAveragePredict(array $timeSeries, int $daysAhead): array
    {
        $windowSize = min(7, count($timeSeries)); // 7-day moving average or less
        $recentValues = array_slice($timeSeries, -$windowSize);
        $average = array_sum(array_column($recentValues, 'value')) / count($recentValues);

        $predictions = [];
        for ($i = 0; $i < $daysAhead; $i++) {
            $predictions[] = [
                'day' => $i + 1,
                'value' => max(0, $average),
                'date' => now()->addDays($i + 1)->toDateString(),
            ];
        }

        return [
            'algorithm' => 'moving_average',
            'predictions' => $predictions,
            'parameters' => ['window_size' => $windowSize, 'average' => $average],
        ];
    }

    /**
     * Seasonal decomposition prediction
     */
    protected function seasonalDecompositionPredict(array $timeSeries, int $daysAhead): array
    {
        // Simplified seasonal decomposition
        $weeklyPattern = $this->extractWeeklyPattern($timeSeries);
        $trend = $this->extractTrend($timeSeries);
        
        $predictions = [];
        for ($i = 0; $i < $daysAhead; $i++) {
            $futureDate = now()->addDays($i + 1);
            $dayOfWeek = $futureDate->dayOfWeek;
            $seasonalComponent = $weeklyPattern[$dayOfWeek] ?? 1.0;
            $trendComponent = $trend + ($i * 0.01); // Simple linear trend
            
            $predictedValue = $trendComponent * $seasonalComponent;
            
            $predictions[] = [
                'day' => $i + 1,
                'value' => max(0, $predictedValue),
                'date' => $futureDate->toDateString(),
                'seasonal_factor' => $seasonalComponent,
                'trend_component' => $trendComponent,
            ];
        }

        return [
            'algorithm' => 'seasonal_decomposition',
            'predictions' => $predictions,
            'parameters' => [
                'weekly_pattern' => $weeklyPattern,
                'base_trend' => $trend,
            ],
        ];
    }

    /**
     * Ensemble prediction (weighted average of algorithms)
     */
    protected function ensemblePredict(array $algorithmPredictions): array
    {
        $weights = [
            'linear_regression' => 0.3,
            'exponential_smoothing' => 0.3,
            'moving_average' => 0.2,
            'seasonal_decomposition' => 0.2,
        ];

        $ensemblePredictions = [];
        $maxDays = 0;

        // Find maximum prediction days
        foreach ($algorithmPredictions as $prediction) {
            if (isset($prediction['predictions'])) {
                $maxDays = max($maxDays, count($prediction['predictions']));
            }
        }

        for ($day = 0; $day < $maxDays; $day++) {
            $weightedSum = 0;
            $totalWeight = 0;

            foreach ($algorithmPredictions as $algorithm => $prediction) {
                if (isset($prediction['predictions'][$day]['value'])) {
                    $weight = $weights[$algorithm] ?? 0.25;
                    $weightedSum += $prediction['predictions'][$day]['value'] * $weight;
                    $totalWeight += $weight;
                }
            }

            $ensembleValue = $totalWeight > 0 ? $weightedSum / $totalWeight : 0;

            $ensemblePredictions[] = [
                'day' => $day + 1,
                'value' => max(0, $ensembleValue),
                'date' => now()->addDays($day + 1)->toDateString(),
            ];
        }

        return [
            'algorithm' => 'ensemble',
            'predictions' => $ensemblePredictions,
            'weights' => $weights,
        ];
    }

    /**
     * Calculate confidence intervals
     */
    protected function calculateConfidenceIntervals(array $timeSeries, array $prediction): array
    {
        $values = array_column($timeSeries, 'value');
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function ($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);
        $stdDev = sqrt($variance);

        $confidenceIntervals = [];
        foreach ($prediction['predictions'] as $pred) {
            $confidenceIntervals[] = [
                'day' => $pred['day'],
                'date' => $pred['date'],
                'lower_bound' => max(0, $pred['value'] - 1.96 * $stdDev), // 95% confidence
                'upper_bound' => $pred['value'] + 1.96 * $stdDev,
                'confidence_level' => 0.95,
            ];
        }

        return $confidenceIntervals;
    }

    /**
     * Analyze trend direction and strength
     */
    protected function analyzeTrend(array $timeSeries): array
    {
        if (count($timeSeries) < 2) {
            return ['direction' => 'unknown', 'strength' => 0];
        }

        $values = array_column($timeSeries, 'value');
        $firstHalf = array_slice($values, 0, floor(count($values) / 2));
        $secondHalf = array_slice($values, floor(count($values) / 2));

        $firstAvg = array_sum($firstHalf) / count($firstHalf);
        $secondAvg = array_sum($secondHalf) / count($secondHalf);

        $change = $secondAvg - $firstAvg;
        $changePercent = $firstAvg > 0 ? ($change / $firstAvg) * 100 : 0;

        $direction = 'stable';
        if ($changePercent > 5) {
            $direction = 'increasing';
        } elseif ($changePercent < -5) {
            $direction = 'decreasing';
        }

        return [
            'direction' => $direction,
            'strength' => abs($changePercent),
            'change_percent' => round($changePercent, 2),
        ];
    }

    /**
     * Detect seasonality patterns
     */
    protected function detectSeasonality(array $timeSeries): array
    {
        $weeklyPattern = $this->extractWeeklyPattern($timeSeries);
        $hourlyPattern = $this->extractHourlyPattern($timeSeries);

        return [
            'weekly_seasonality' => $this->calculateSeasonalityStrength($weeklyPattern),
            'hourly_seasonality' => $this->calculateSeasonalityStrength($hourlyPattern),
            'weekly_pattern' => $weeklyPattern,
            'hourly_pattern' => $hourlyPattern,
        ];
    }

    /**
     * Detect anomalies in time series
     */
    protected function detectAnomalies(array $timeSeries): array
    {
        $values = array_column($timeSeries, 'value');
        $mean = array_sum($values) / count($values);
        $stdDev = sqrt(array_sum(array_map(function ($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values));

        $threshold = 2 * $stdDev; // 2 standard deviations
        $anomalies = [];

        foreach ($timeSeries as $i => $point) {
            if (abs($point['value'] - $mean) > $threshold) {
                $anomalies[] = [
                    'index' => $i,
                    'timestamp' => $point['timestamp']->toISOString(),
                    'value' => $point['value'],
                    'deviation' => abs($point['value'] - $mean),
                    'severity' => abs($point['value'] - $mean) > 3 * $stdDev ? 'high' : 'medium',
                ];
            }
        }

        return $anomalies;
    }

    // Helper methods
    protected function calculateOverallConfidence(array $predictions): float
    {
        // Simplified confidence calculation
        return 0.75; // 75% confidence
    }

    protected function calculateRSquared(array $timeSeries, float $slope, float $intercept): float
    {
        $actualValues = array_column($timeSeries, 'value');
        $mean = array_sum($actualValues) / count($actualValues);
        
        $ssTotal = 0;
        $ssRes = 0;
        
        foreach ($timeSeries as $i => $point) {
            $predicted = $slope * $i + $intercept;
            $actual = $point['value'];
            
            $ssTotal += pow($actual - $mean, 2);
            $ssRes += pow($actual - $predicted, 2);
        }
        
        return $ssTotal > 0 ? 1 - ($ssRes / $ssTotal) : 0;
    }

    protected function extractWeeklyPattern(array $timeSeries): array
    {
        $weeklyData = array_fill(0, 7, []);
        
        foreach ($timeSeries as $point) {
            $dayOfWeek = $point['day_of_week'];
            $weeklyData[$dayOfWeek][] = $point['value'];
        }
        
        $pattern = [];
        for ($day = 0; $day < 7; $day++) {
            $pattern[$day] = !empty($weeklyData[$day]) ? 
                array_sum($weeklyData[$day]) / count($weeklyData[$day]) : 0;
        }
        
        return $pattern;
    }

    protected function extractHourlyPattern(array $timeSeries): array
    {
        $hourlyData = array_fill(0, 24, []);
        
        foreach ($timeSeries as $point) {
            $hour = $point['hour_of_day'];
            $hourlyData[$hour][] = $point['value'];
        }
        
        $pattern = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $pattern[$hour] = !empty($hourlyData[$hour]) ? 
                array_sum($hourlyData[$hour]) / count($hourlyData[$hour]) : 0;
        }
        
        return $pattern;
    }

    protected function extractTrend(array $timeSeries): float
    {
        $values = array_column($timeSeries, 'value');
        return array_sum($values) / count($values);
    }

    protected function calculateSeasonalityStrength(array $pattern): float
    {
        if (empty($pattern)) {
            return 0;
        }
        
        $mean = array_sum($pattern) / count($pattern);
        $variance = array_sum(array_map(function ($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $pattern)) / count($pattern);
        
        return $mean > 0 ? sqrt($variance) / $mean : 0;
    }
}
