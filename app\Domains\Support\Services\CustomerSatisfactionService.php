<?php

namespace App\Domains\Support\Services;

use App\Domains\Support\Models\Ticket;
use App\Domains\Support\Models\LiveChat;
use App\Domains\Support\Models\SatisfactionSurvey;
use App\Domains\CRM\Models\Customer;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

/**
 * خدمة رضا العملاء - Customer Satisfaction Service
 */
class CustomerSatisfactionService
{
    /**
     * إرسال استبيان الرضا للتذكرة
     */
    public function sendTicketSatisfactionSurvey(Ticket $ticket): void
    {
        if (!config('support.satisfaction.enabled') || !config('support.satisfaction.send_after_resolution')) {
            return;
        }

        // التحقق من عدم إرسال استبيان سابق
        if ($this->hasPendingSurvey($ticket)) {
            return;
        }

        $survey = $this->createSurvey([
            'type' => 'ticket_satisfaction',
            'ticket_id' => $ticket->id,
            'customer_id' => $ticket->customer_id,
            'questions' => $this->getTicketSatisfactionQuestions(),
            'expires_at' => now()->addDays(7),
        ]);

        $this->sendSurveyNotification($survey);
    }

    /**
     * إرسال استبيان الرضا للدردشة
     */
    public function sendChatSatisfactionSurvey(LiveChat $chat): void
    {
        if (!config('support.satisfaction.enabled') || !config('support.satisfaction.send_after_chat_end')) {
            return;
        }

        $survey = $this->createSurvey([
            'type' => 'chat_satisfaction',
            'chat_id' => $chat->id,
            'customer_id' => $chat->customer_id,
            'questions' => $this->getChatSatisfactionQuestions(),
            'expires_at' => now()->addDays(3),
        ]);

        $this->sendSurveyNotification($survey);
    }

    /**
     * إرسال استبيان NPS
     */
    public function sendNPSSurvey(Customer $customer): void
    {
        if (!config('support.satisfaction.nps_enabled')) {
            return;
        }

        // التحقق من آخر استبيان NPS
        $lastNPS = SatisfactionSurvey::where('customer_id', $customer->id)
            ->where('type', 'nps')
            ->latest()
            ->first();

        $daysSinceLastNPS = $lastNPS ? $lastNPS->created_at->diffInDays(now()) : 999;
        $npsFrequency = config('support.satisfaction.nps_frequency_days', 90);

        if ($daysSinceLastNPS < $npsFrequency) {
            return;
        }

        $survey = $this->createSurvey([
            'type' => 'nps',
            'customer_id' => $customer->id,
            'questions' => $this->getNPSQuestions(),
            'expires_at' => now()->addDays(14),
        ]);

        $this->sendSurveyNotification($survey);
    }

    /**
     * معالجة إجابة الاستبيان
     */
    public function processSurveyResponse(SatisfactionSurvey $survey, array $responses): void
    {
        $survey->update([
            'responses' => $responses,
            'completed_at' => now(),
            'status' => 'completed',
        ]);

        // تحديث تقييم التذكرة أو الدردشة
        $this->updateRelatedEntityRating($survey, $responses);

        // تحليل الاستجابة
        $analysis = $this->analyzeResponse($survey, $responses);

        // إجراءات المتابعة للتقييمات المنخفضة
        if ($analysis['requires_followup']) {
            $this->triggerLowRatingFollowup($survey, $analysis);
        }

        // تحديث ملف العميل
        $this->updateCustomerSatisfactionProfile($survey->customer, $responses);
    }

    /**
     * الحصول على مقاييس الرضا
     */
    public function getMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        return [
            'overview' => $this->getOverviewMetrics($dateFrom, $dateTo),
            'csat_metrics' => $this->getCSATMetrics($dateFrom, $dateTo),
            'nps_metrics' => $this->getNPSMetrics($dateFrom, $dateTo),
            'trends' => $this->getSatisfactionTrends($dateFrom, $dateTo),
            'by_channel' => $this->getMetricsByChannel($dateFrom, $dateTo),
            'by_agent' => $this->getMetricsByAgent($dateFrom, $dateTo),
            'feedback_analysis' => $this->analyzeFeedbackText($dateFrom, $dateTo),
        ];
    }

    /**
     * إنشاء استبيان جديد
     */
    protected function createSurvey(array $data): SatisfactionSurvey
    {
        return SatisfactionSurvey::create(array_merge($data, [
            'survey_token' => $this->generateSurveyToken(),
            'status' => 'sent',
            'sent_at' => now(),
        ]));
    }

    /**
     * التحقق من وجود استبيان معلق
     */
    protected function hasPendingSurvey(Ticket $ticket): bool
    {
        return SatisfactionSurvey::where('ticket_id', $ticket->id)
            ->whereIn('status', ['sent', 'reminded'])
            ->exists();
    }

    /**
     * الحصول على أسئلة استبيان التذكرة
     */
    protected function getTicketSatisfactionQuestions(): array
    {
        return [
            [
                'id' => 'overall_satisfaction',
                'type' => 'rating',
                'question' => 'كيف تقيم مستوى الخدمة المقدمة؟',
                'question_en' => 'How would you rate the service provided?',
                'question_fr' => 'Comment évalueriez-vous le service fourni?',
                'scale' => 5,
                'required' => true,
            ],
            [
                'id' => 'response_time',
                'type' => 'rating',
                'question' => 'كيف تقيم سرعة الاستجابة؟',
                'question_en' => 'How would you rate the response time?',
                'question_fr' => 'Comment évalueriez-vous le temps de réponse?',
                'scale' => 5,
                'required' => true,
            ],
            [
                'id' => 'solution_quality',
                'type' => 'rating',
                'question' => 'كيف تقيم جودة الحل المقدم؟',
                'question_en' => 'How would you rate the quality of the solution?',
                'question_fr' => 'Comment évalueriez-vous la qualité de la solution?',
                'scale' => 5,
                'required' => true,
            ],
            [
                'id' => 'agent_professionalism',
                'type' => 'rating',
                'question' => 'كيف تقيم احترافية الوكيل؟',
                'question_en' => 'How would you rate the agent\'s professionalism?',
                'question_fr' => 'Comment évalueriez-vous le professionnalisme de l\'agent?',
                'scale' => 5,
                'required' => false,
            ],
            [
                'id' => 'feedback',
                'type' => 'text',
                'question' => 'أي تعليقات أو اقتراحات إضافية؟',
                'question_en' => 'Any additional comments or suggestions?',
                'question_fr' => 'Des commentaires ou suggestions supplémentaires?',
                'required' => false,
            ],
        ];
    }

    /**
     * الحصول على أسئلة استبيان الدردشة
     */
    protected function getChatSatisfactionQuestions(): array
    {
        return [
            [
                'id' => 'chat_experience',
                'type' => 'rating',
                'question' => 'كيف تقيم تجربة الدردشة الحية؟',
                'question_en' => 'How would you rate your live chat experience?',
                'question_fr' => 'Comment évalueriez-vous votre expérience de chat en direct?',
                'scale' => 5,
                'required' => true,
            ],
            [
                'id' => 'bot_helpfulness',
                'type' => 'rating',
                'question' => 'كم كان المساعد الذكي مفيداً؟',
                'question_en' => 'How helpful was the AI assistant?',
                'question_fr' => 'Dans quelle mesure l\'assistant IA était-il utile?',
                'scale' => 5,
                'required' => false,
            ],
            [
                'id' => 'issue_resolved',
                'type' => 'boolean',
                'question' => 'هل تم حل مشكلتك؟',
                'question_en' => 'Was your issue resolved?',
                'question_fr' => 'Votre problème a-t-il été résolu?',
                'required' => true,
            ],
            [
                'id' => 'feedback',
                'type' => 'text',
                'question' => 'كيف يمكننا تحسين خدمة الدردشة؟',
                'question_en' => 'How can we improve our chat service?',
                'question_fr' => 'Comment pouvons-nous améliorer notre service de chat?',
                'required' => false,
            ],
        ];
    }

    /**
     * الحصول على أسئلة NPS
     */
    protected function getNPSQuestions(): array
    {
        return [
            [
                'id' => 'nps_score',
                'type' => 'rating',
                'question' => 'ما مدى احتمالية أن توصي بخدماتنا لصديق أو زميل؟',
                'question_en' => 'How likely are you to recommend our services to a friend or colleague?',
                'question_fr' => 'Dans quelle mesure recommanderiez-vous nos services à un ami ou collègue?',
                'scale' => 10,
                'required' => true,
            ],
            [
                'id' => 'nps_reason',
                'type' => 'text',
                'question' => 'ما السبب وراء هذا التقييم؟',
                'question_en' => 'What is the reason for this rating?',
                'question_fr' => 'Quelle est la raison de cette évaluation?',
                'required' => false,
            ],
        ];
    }

    /**
     * إرسال إشعار الاستبيان
     */
    protected function sendSurveyNotification(SatisfactionSurvey $survey): void
    {
        // إرسال بريد إلكتروني
        Mail::to($survey->customer->email)->send(
            new \App\Mail\SatisfactionSurveyMail($survey)
        );

        // إرسال إشعار داخل التطبيق
        $survey->customer->notify(
            new \App\Notifications\SatisfactionSurveyNotification($survey)
        );
    }

    /**
     * تحديث تقييم الكيان المرتبط
     */
    protected function updateRelatedEntityRating(SatisfactionSurvey $survey, array $responses): void
    {
        $overallRating = $responses['overall_satisfaction'] ?? $responses['chat_experience'] ?? null;

        if ($survey->ticket_id && $overallRating) {
            $survey->ticket->update([
                'satisfaction_rating' => $overallRating,
                'satisfaction_feedback' => $responses['feedback'] ?? null,
            ]);
        }

        if ($survey->chat_id && $overallRating) {
            $survey->chat->update([
                'customer_satisfaction' => $overallRating,
                'feedback' => $responses['feedback'] ?? null,
            ]);
        }
    }

    /**
     * تحليل الاستجابة
     */
    protected function analyzeResponse(SatisfactionSurvey $survey, array $responses): array
    {
        $analysis = [
            'requires_followup' => false,
            'sentiment' => 'neutral',
            'key_issues' => [],
            'satisfaction_level' => 'medium',
        ];

        // تحليل التقييمات
        $ratings = array_filter($responses, function ($value, $key) {
            return is_numeric($value);
        }, ARRAY_FILTER_USE_BOTH);

        if (!empty($ratings)) {
            $avgRating = array_sum($ratings) / count($ratings);
            $lowRatingThreshold = config('support.satisfaction.low_rating_threshold', 3);

            if ($avgRating <= $lowRatingThreshold) {
                $analysis['requires_followup'] = true;
                $analysis['satisfaction_level'] = 'low';
                $analysis['sentiment'] = 'negative';
            } elseif ($avgRating >= 4) {
                $analysis['satisfaction_level'] = 'high';
                $analysis['sentiment'] = 'positive';
            }
        }

        // تحليل النص (مبسط)
        if (!empty($responses['feedback'])) {
            $feedback = strtolower($responses['feedback']);
            $negativeWords = ['سيء', 'فظيع', 'بطيء', 'bad', 'terrible', 'slow', 'mauvais'];
            
            foreach ($negativeWords as $word) {
                if (str_contains($feedback, $word)) {
                    $analysis['requires_followup'] = true;
                    $analysis['sentiment'] = 'negative';
                    break;
                }
            }
        }

        return $analysis;
    }

    /**
     * تفعيل متابعة التقييمات المنخفضة
     */
    protected function triggerLowRatingFollowup(SatisfactionSurvey $survey, array $analysis): void
    {
        // إشعار المشرف
        event(new \App\Events\LowSatisfactionRatingReceived($survey, $analysis));

        // إنشاء مهمة متابعة
        if ($survey->ticket_id) {
            $survey->ticket->update(['requires_followup' => true]);
        }
    }

    /**
     * تحديث ملف رضا العميل
     */
    protected function updateCustomerSatisfactionProfile(Customer $customer, array $responses): void
    {
        $satisfactionHistory = $customer->satisfaction_history ?? [];
        
        $satisfactionHistory[] = [
            'date' => now()->toDateString(),
            'rating' => $responses['overall_satisfaction'] ?? $responses['chat_experience'] ?? null,
            'type' => 'support_interaction',
        ];

        // الاحتفاظ بآخر 50 تقييم فقط
        if (count($satisfactionHistory) > 50) {
            $satisfactionHistory = array_slice($satisfactionHistory, -50);
        }

        $customer->update(['satisfaction_history' => $satisfactionHistory]);
    }

    /**
     * توليد رمز الاستبيان
     */
    protected function generateSurveyToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    // دوال المقاييس
    protected function getOverviewMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        $surveys = SatisfactionSurvey::whereBetween('completed_at', [$dateFrom, $dateTo])
            ->where('status', 'completed')
            ->get();

        return [
            'total_surveys_sent' => SatisfactionSurvey::whereBetween('sent_at', [$dateFrom, $dateTo])->count(),
            'total_responses' => $surveys->count(),
            'response_rate' => $this->calculateResponseRate($dateFrom, $dateTo),
            'average_rating' => $this->calculateAverageRating($surveys),
            'satisfaction_distribution' => $this->getSatisfactionDistribution($surveys),
        ];
    }

    protected function getCSATMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        $ticketSurveys = SatisfactionSurvey::where('type', 'ticket_satisfaction')
            ->whereBetween('completed_at', [$dateFrom, $dateTo])
            ->where('status', 'completed')
            ->get();

        return [
            'total_responses' => $ticketSurveys->count(),
            'average_csat' => $this->calculateAverageRating($ticketSurveys),
            'csat_trend' => $this->getCSATTrend($dateFrom, $dateTo),
        ];
    }

    protected function getNPSMetrics(Carbon $dateFrom, Carbon $dateTo): array
    {
        $npsResponses = SatisfactionSurvey::where('type', 'nps')
            ->whereBetween('completed_at', [$dateFrom, $dateTo])
            ->where('status', 'completed')
            ->get();

        return [
            'total_responses' => $npsResponses->count(),
            'nps_score' => $this->calculateNPSScore($npsResponses),
            'promoters' => $this->countPromoters($npsResponses),
            'detractors' => $this->countDetractors($npsResponses),
            'passives' => $this->countPassives($npsResponses),
        ];
    }

    protected function calculateResponseRate(Carbon $dateFrom, Carbon $dateTo): float
    {
        $sent = SatisfactionSurvey::whereBetween('sent_at', [$dateFrom, $dateTo])->count();
        $completed = SatisfactionSurvey::whereBetween('completed_at', [$dateFrom, $dateTo])
            ->where('status', 'completed')->count();

        return $sent > 0 ? round(($completed / $sent) * 100, 2) : 0;
    }

    protected function calculateAverageRating($surveys): float
    {
        if ($surveys->isEmpty()) return 0;

        $totalRating = $surveys->sum(function ($survey) {
            $responses = $survey->responses ?? [];
            return $responses['overall_satisfaction'] ?? $responses['chat_experience'] ?? 0;
        });

        return round($totalRating / $surveys->count(), 2);
    }

    protected function getSatisfactionDistribution($surveys): array
    {
        $distribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];

        foreach ($surveys as $survey) {
            $rating = $survey->responses['overall_satisfaction'] ?? $survey->responses['chat_experience'] ?? 0;
            if ($rating >= 1 && $rating <= 5) {
                $distribution[$rating]++;
            }
        }

        return $distribution;
    }

    protected function calculateNPSScore($npsResponses): int
    {
        if ($npsResponses->isEmpty()) return 0;

        $promoters = $this->countPromoters($npsResponses);
        $detractors = $this->countDetractors($npsResponses);
        $total = $npsResponses->count();

        return round((($promoters - $detractors) / $total) * 100);
    }

    protected function countPromoters($npsResponses): int
    {
        return $npsResponses->filter(function ($survey) {
            $score = $survey->responses['nps_score'] ?? 0;
            return $score >= 9;
        })->count();
    }

    protected function countDetractors($npsResponses): int
    {
        return $npsResponses->filter(function ($survey) {
            $score = $survey->responses['nps_score'] ?? 0;
            return $score <= 6;
        })->count();
    }

    protected function countPassives($npsResponses): int
    {
        return $npsResponses->filter(function ($survey) {
            $score = $survey->responses['nps_score'] ?? 0;
            return $score >= 7 && $score <= 8;
        })->count();
    }

    protected function getSatisfactionTrends(Carbon $dateFrom, Carbon $dateTo): array { return []; }
    protected function getMetricsByChannel(Carbon $dateFrom, Carbon $dateTo): array { return []; }
    protected function getMetricsByAgent(Carbon $dateFrom, Carbon $dateTo): array { return []; }
    protected function analyzeFeedbackText(Carbon $dateFrom, Carbon $dateTo): array { return []; }
    protected function getCSATTrend(Carbon $dateFrom, Carbon $dateTo): array { return []; }
}
