<?php

namespace App\Domains\Projects\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Projects\Models\Task;
use App\Domains\Projects\Models\Project;
use App\Domains\Projects\Services\TaskManagementService;
use App\Domains\Projects\Requests\StoreTaskRequest;
use App\Domains\Projects\Requests\UpdateTaskRequest;
use App\Domains\Projects\Resources\TaskResource;
use App\Domains\Projects\Resources\TaskCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * متحكم المهام الاحترافي
 * إدارة شاملة للمهام مع جميع الميزات المتقدمة
 */
class TaskController extends Controller
{
    protected TaskManagementService $taskService;

    public function __construct(TaskManagementService $taskService)
    {
        $this->taskService = $taskService;
    }

    /**
     * عرض قائمة المهام
     */
    public function index(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Task::class);

        $query = Task::with([
            'project', 'assignee', 'reporter', 'parent', 'children', 'milestone', 'sprint'
        ]);

        // التصفية حسب المشروع
        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('task_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب الأولوية
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // التصفية حسب المكلف
        if ($request->filled('assignee_id')) {
            $query->where('assignee_id', $request->assignee_id);
        }

        // التصفية حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // التصفية حسب الفئة
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // التصفية حسب التاريخ المستحق
        if ($request->filled('due_date_from') && $request->filled('due_date_to')) {
            $query->whereBetween('due_date', [$request->due_date_from, $request->due_date_to]);
        }

        // التصفية للمهام المتأخرة
        if ($request->boolean('overdue_only')) {
            $query->where('due_date', '<', now())
                  ->whereNotIn('status', ['COMPLETED', 'CANCELLED']);
        }

        // التصفية للمهام المستحقة اليوم
        if ($request->boolean('due_today')) {
            $query->whereDate('due_date', today());
        }

        // التصفية حسب التقدم
        if ($request->filled('min_progress')) {
            $query->where('progress_percentage', '>=', $request->min_progress);
        }

        // التصفية حسب مستوى المخاطر
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        // التصفية حسب Sprint
        if ($request->filled('sprint_id')) {
            $query->where('sprint_id', $request->sprint_id);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // الصفحات
        $perPage = $request->get('per_page', 15);
        $tasks = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => new TaskCollection($tasks),
            'meta' => [
                'total' => $tasks->total(),
                'per_page' => $tasks->perPage(),
                'current_page' => $tasks->currentPage(),
                'last_page' => $tasks->lastPage(),
            ],
        ]);
    }

    /**
     * إنشاء مهمة جديدة
     */
    public function store(StoreTaskRequest $request): JsonResponse
    {
        $this->authorize('create', Task::class);

        DB::beginTransaction();

        try {
            $task = $this->taskService->createTask($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المهمة بنجاح',
                'data' => new TaskResource($task->load([
                    'project', 'assignee', 'reporter', 'parent', 'milestone'
                ])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * عرض تفاصيل مهمة محددة
     */
    public function show(int $id): JsonResponse
    {
        $task = Task::with([
            'project',
            'assignee',
            'reporter',
            'reviewer',
            'parent',
            'children',
            'milestone',
            'sprint',
            'epic',
            'timeEntries.user',
            'comments.user',
            'activities.user',
            'attachments',
            'dependencies',
            'dependents',
            'watchers',
        ])->findOrFail($id);

        $this->authorize('view', $task);

        // إضافة معلومات إضافية
        $additionalData = [
            'time_tracking' => $this->taskService->getTimeTrackingData($task),
            'progress_history' => $this->taskService->getProgressHistory($task),
            'related_tasks' => $this->taskService->getRelatedTasks($task),
            'blocking_issues' => $this->taskService->getBlockingIssues($task),
            'performance_metrics' => $this->taskService->getPerformanceMetrics($task),
        ];

        return response()->json([
            'success' => true,
            'data' => new TaskResource($task),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * تحديث المهمة
     */
    public function update(UpdateTaskRequest $request, int $id): JsonResponse
    {
        $task = Task::findOrFail($id);
        $this->authorize('update', $task);

        DB::beginTransaction();

        try {
            $task = $this->taskService->updateTask($task, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث المهمة بنجاح',
                'data' => new TaskResource($task->load([
                    'project', 'assignee', 'reporter'
                ])),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف المهمة
     */
    public function destroy(int $id): JsonResponse
    {
        $task = Task::findOrFail($id);
        $this->authorize('delete', $task);

        try {
            // التحقق من وجود مهام فرعية
            if ($task->children()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف المهمة لوجود مهام فرعية',
                ], 422);
            }

            $task->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المهمة بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تغيير حالة المهمة
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:TODO,IN_PROGRESS,IN_REVIEW,TESTING,DONE,CANCELLED',
            'comment' => 'nullable|string|max:1000',
        ]);

        $task = Task::findOrFail($id);
        $this->authorize('update', $task);

        try {
            $task = $this->taskService->changeTaskStatus(
                $task,
                $request->status,
                $request->comment
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تغيير حالة المهمة بنجاح',
                'data' => new TaskResource($task),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير حالة المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تعيين المهمة لمستخدم
     */
    public function assign(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'assignee_id' => 'required|exists:users,id',
            'comment' => 'nullable|string|max:500',
        ]);

        $task = Task::findOrFail($id);
        $this->authorize('update', $task);

        try {
            $task = $this->taskService->assignTask(
                $task,
                $request->assignee_id,
                $request->comment
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين المهمة بنجاح',
                'data' => new TaskResource($task),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعيين المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تحديث تقدم المهمة
     */
    public function updateProgress(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'progress_percentage' => 'required|integer|between:0,100',
            'comment' => 'nullable|string|max:500',
        ]);

        $task = Task::findOrFail($id);
        $this->authorize('update', $task);

        try {
            $task = $this->taskService->updateTaskProgress(
                $task,
                $request->progress_percentage,
                $request->comment
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث تقدم المهمة بنجاح',
                'data' => new TaskResource($task),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث تقدم المهمة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إضافة تبعية للمهمة
     */
    public function addDependency(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'dependent_task_id' => 'required|exists:tasks,id',
            'dependency_type' => 'required|string|in:BLOCKS,DEPENDS_ON,RELATES_TO',
        ]);

        $task = Task::findOrFail($id);
        $this->authorize('update', $task);

        try {
            $this->taskService->addTaskDependency(
                $task,
                $request->dependent_task_id,
                $request->dependency_type
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة التبعية بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة التبعية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على مهام Kanban Board
     */
    public function getKanbanBoard(Request $request): JsonResponse
    {
        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'sprint_id' => 'nullable|exists:sprints,id',
        ]);

        $project = Project::findOrFail($request->project_id);
        $this->authorize('view', $project);

        $kanbanData = $this->taskService->getKanbanBoardData(
            $request->project_id,
            $request->sprint_id
        );

        return response()->json([
            'success' => true,
            'data' => $kanbanData,
        ]);
    }

    /**
     * تحديث ترتيب المهام في Kanban
     */
    public function updateKanbanOrder(Request $request): JsonResponse
    {
        $request->validate([
            'tasks' => 'required|array',
            'tasks.*.id' => 'required|exists:tasks,id',
            'tasks.*.status' => 'required|string',
            'tasks.*.order' => 'required|integer',
        ]);

        try {
            $this->taskService->updateKanbanOrder($request->tasks);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث ترتيب المهام بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث ترتيب المهام',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
