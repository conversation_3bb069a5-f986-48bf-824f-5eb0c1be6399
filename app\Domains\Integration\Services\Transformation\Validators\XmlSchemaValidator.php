<?php

namespace App\Domains\Integration\Services\Transformation\Validators;

use App\Domains\Integration\Exceptions\ValidationException;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use LibXMLError;

/**
 * XML Schema Validator
 * Validates XML data against XSD schemas
 */
class XmlSchemaValidator
{
    protected array $config;
    protected array $schemas;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'schema_path' => storage_path('app/schemas/xml'),
            'strict_mode' => true,
            'validate_dtd' => false,
            'load_external_entities' => false,
        ], $config);
        
        $this->schemas = [];
    }

    /**
     * Validate XML data against XSD schema
     */
    public function validate(string $xmlData, string $xsdSchema): array
    {
        try {
            // Create DOM document
            $dom = new DOMDocument();
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            
            // Configure libxml settings
            $oldSetting = libxml_use_internal_errors(true);
            libxml_clear_errors();
            
            // Load XML
            if (!$dom->loadXML($xmlData)) {
                $errors = $this->getLibXmlErrors();
                return [
                    'valid' => false,
                    'errors' => $errors,
                ];
            }
            
            // Validate against XSD schema
            $isValid = $dom->schemaValidateSource($xsdSchema);
            
            $errors = [];
            if (!$isValid) {
                $errors = $this->getLibXmlErrors();
            }
            
            // Restore libxml settings
            libxml_use_internal_errors($oldSetting);
            
            return [
                'valid' => $isValid,
                'errors' => $errors,
            ];
            
        } catch (\Exception $e) {
            Log::error('XML schema validation failed', [
                'error' => $e->getMessage(),
            ]);
            
            return [
                'valid' => false,
                'errors' => ['Validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Validate XML file against XSD schema file
     */
    public function validateFile(string $xmlFile, string $xsdFile): array
    {
        if (!file_exists($xmlFile)) {
            return [
                'valid' => false,
                'errors' => ["XML file not found: {$xmlFile}"],
            ];
        }
        
        if (!file_exists($xsdFile)) {
            return [
                'valid' => false,
                'errors' => ["XSD file not found: {$xsdFile}"],
            ];
        }
        
        $xmlData = file_get_contents($xmlFile);
        $xsdSchema = file_get_contents($xsdFile);
        
        return $this->validate($xmlData, $xsdSchema);
    }

    /**
     * Validate XML against DTD
     */
    public function validateWithDtd(string $xmlData, string $dtdPath = null): array
    {
        try {
            $dom = new DOMDocument();
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            
            $oldSetting = libxml_use_internal_errors(true);
            libxml_clear_errors();
            
            // Load XML
            if (!$dom->loadXML($xmlData)) {
                $errors = $this->getLibXmlErrors();
                return [
                    'valid' => false,
                    'errors' => $errors,
                ];
            }
            
            // Validate with DTD
            $isValid = true;
            if ($dtdPath) {
                $isValid = $dom->validate();
            } else {
                // Use internal DTD if present
                $isValid = $dom->validate();
            }
            
            $errors = [];
            if (!$isValid) {
                $errors = $this->getLibXmlErrors();
            }
            
            libxml_use_internal_errors($oldSetting);
            
            return [
                'valid' => $isValid,
                'errors' => $errors,
            ];
            
        } catch (\Exception $e) {
            Log::error('XML DTD validation failed', [
                'error' => $e->getMessage(),
            ]);
            
            return [
                'valid' => false,
                'errors' => ['DTD validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Validate XML structure without schema
     */
    public function validateStructure(string $xmlData): array
    {
        try {
            $dom = new DOMDocument();
            
            $oldSetting = libxml_use_internal_errors(true);
            libxml_clear_errors();
            
            $isValid = $dom->loadXML($xmlData);
            
            $errors = [];
            if (!$isValid) {
                $errors = $this->getLibXmlErrors();
            }
            
            libxml_use_internal_errors($oldSetting);
            
            return [
                'valid' => $isValid,
                'errors' => $errors,
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['XML structure validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Get LibXML errors
     */
    protected function getLibXmlErrors(): array
    {
        $errors = [];
        $libxmlErrors = libxml_get_errors();
        
        foreach ($libxmlErrors as $error) {
            $level = match ($error->level) {
                LIBXML_ERR_WARNING => 'Warning',
                LIBXML_ERR_ERROR => 'Error',
                LIBXML_ERR_FATAL => 'Fatal Error',
                default => 'Unknown',
            };
            
            $errors[] = sprintf(
                '%s: %s (Line: %d, Column: %d)',
                $level,
                trim($error->message),
                $error->line,
                $error->column
            );
        }
        
        libxml_clear_errors();
        return $errors;
    }

    /**
     * Load XSD schema from file
     */
    public function loadSchema(string $schemaName): string
    {
        if (isset($this->schemas[$schemaName])) {
            return $this->schemas[$schemaName];
        }

        $schemaPath = $this->config['schema_path'] . "/{$schemaName}.xsd";
        
        if (!file_exists($schemaPath)) {
            throw new ValidationException("XSD schema file not found: {$schemaPath}");
        }

        $schemaContent = file_get_contents($schemaPath);
        $this->schemas[$schemaName] = $schemaContent;
        
        return $schemaContent;
    }

    /**
     * Validate using named schema
     */
    public function validateWithSchema(string $xmlData, string $schemaName): array
    {
        $schema = $this->loadSchema($schemaName);
        return $this->validate($xmlData, $schema);
    }

    /**
     * Validate XML namespace
     */
    public function validateNamespace(string $xmlData, array $expectedNamespaces = []): array
    {
        try {
            $dom = new DOMDocument();
            $dom->loadXML($xmlData);
            
            $errors = [];
            $xpath = new \DOMXPath($dom);
            
            foreach ($expectedNamespaces as $prefix => $uri) {
                $xpath->registerNamespace($prefix, $uri);
                
                // Check if namespace is used
                $nodes = $xpath->query("//*[namespace-uri()='{$uri}']");
                if ($nodes->length === 0) {
                    $errors[] = "Expected namespace '{$uri}' with prefix '{$prefix}' not found";
                }
            }
            
            return [
                'valid' => empty($errors),
                'errors' => $errors,
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['Namespace validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Extract and validate XML encoding
     */
    public function validateEncoding(string $xmlData, string $expectedEncoding = 'UTF-8'): array
    {
        try {
            // Extract encoding from XML declaration
            if (preg_match('/^<\?xml[^>]+encoding=["\']([^"\']+)["\']/', $xmlData, $matches)) {
                $actualEncoding = strtoupper($matches[1]);
                $expectedEncoding = strtoupper($expectedEncoding);
                
                if ($actualEncoding !== $expectedEncoding) {
                    return [
                        'valid' => false,
                        'errors' => ["Expected encoding '{$expectedEncoding}', found '{$actualEncoding}'"],
                    ];
                }
            }
            
            // Validate that the content is actually in the declared encoding
            if (!mb_check_encoding($xmlData, $expectedEncoding)) {
                return [
                    'valid' => false,
                    'errors' => ["Content is not valid {$expectedEncoding}"],
                ];
            }
            
            return [
                'valid' => true,
                'errors' => [],
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['Encoding validation error: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Validate XML against multiple schemas
     */
    public function validateMultipleSchemas(string $xmlData, array $schemas): array
    {
        $allErrors = [];
        $validCount = 0;
        
        foreach ($schemas as $schemaName => $schema) {
            $result = $this->validate($xmlData, $schema);
            
            if ($result['valid']) {
                $validCount++;
            } else {
                $allErrors[$schemaName] = $result['errors'];
            }
        }
        
        return [
            'valid' => $validCount > 0,
            'valid_schemas' => $validCount,
            'total_schemas' => count($schemas),
            'errors' => $allErrors,
        ];
    }

    /**
     * Generate XSD schema from XML sample
     */
    public function generateSchemaFromXml(string $xmlData): string
    {
        try {
            $dom = new DOMDocument();
            $dom->loadXML($xmlData);
            
            // This is a simplified schema generation
            // In a real implementation, you would use a more sophisticated approach
            $rootElement = $dom->documentElement;
            $rootName = $rootElement->nodeName;
            
            $xsd = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            $xsd .= '<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">' . "\n";
            $xsd .= "  <xs:element name=\"{$rootName}\">" . "\n";
            $xsd .= "    <xs:complexType>" . "\n";
            $xsd .= "      <xs:sequence>" . "\n";
            
            // Add child elements (simplified)
            foreach ($rootElement->childNodes as $child) {
                if ($child->nodeType === XML_ELEMENT_NODE) {
                    $xsd .= "        <xs:element name=\"{$child->nodeName}\" type=\"xs:string\" minOccurs=\"0\"/>" . "\n";
                }
            }
            
            $xsd .= "      </xs:sequence>" . "\n";
            $xsd .= "    </xs:complexType>" . "\n";
            $xsd .= "  </xs:element>" . "\n";
            $xsd .= "</xs:schema>";
            
            return $xsd;
            
        } catch (\Exception $e) {
            throw new ValidationException('Failed to generate XSD schema: ' . $e->getMessage());
        }
    }
}
