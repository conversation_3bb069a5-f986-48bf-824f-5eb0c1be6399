<?php

namespace App\Domains\Accounting\Jobs;

use App\Domains\Accounting\Models\BankAccount;
use App\Domains\Accounting\Services\AdvancedAccountingEngine;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

/**
 * Bank Reconciliation Job
 * مهمة التسوية البنكية التلقائية
 */
class BankReconciliationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 600; // 10 دقائق
    public int $tries = 2;

    protected int $bankAccountId;
    protected ?string $dateFrom;
    protected ?string $dateTo;
    protected bool $autoApprove;

    /**
     * إنشاء مثيل جديد من المهمة
     */
    public function __construct(
        int $bankAccountId,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        bool $autoApprove = false
    ) {
        $this->bankAccountId = $bankAccountId;
        $this->dateFrom = $dateFrom ?? now()->startOfMonth()->format('Y-m-d');
        $this->dateTo = $dateTo ?? now()->format('Y-m-d');
        $this->autoApprove = $autoApprove;
        
        $this->onQueue('accounting');
    }

    /**
     * تنفيذ المهمة
     */
    public function handle(AdvancedAccountingEngine $accountingEngine): void
    {
        Log::info('بدء مهمة التسوية البنكية التلقائية', [
            'bank_account_id' => $this->bankAccountId,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'auto_approve' => $this->autoApprove,
        ]);

        try {
            $bankAccount = BankAccount::findOrFail($this->bankAccountId);

            // التحقق من تفعيل التسوية التلقائية
            if (!$bankAccount->auto_reconciliation) {
                Log::warning('التسوية التلقائية غير مفعلة للحساب البنكي', [
                    'bank_account_id' => $this->bankAccountId,
                ]);
                return;
            }

            // تنفيذ التسوية التلقائية
            $reconciliationResult = $accountingEngine->autoReconcileBankTransactions($this->bankAccountId);

            // معالجة النتائج
            $this->processReconciliationResults($reconciliationResult, $bankAccount);

            Log::info('انتهاء مهمة التسوية البنكية التلقائية', [
                'bank_account_id' => $this->bankAccountId,
                'matched_automatically' => $reconciliationResult['matched_automatically'],
                'suggestions_count' => count($reconciliationResult['suggestions']),
                'total_processed' => $reconciliationResult['total_processed'],
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في مهمة التسوية البنكية التلقائية', [
                'bank_account_id' => $this->bankAccountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * معالجة نتائج التسوية
     */
    protected function processReconciliationResults(array $result, BankAccount $bankAccount): void
    {
        $matchedCount = $result['matched_automatically'];
        $suggestions = $result['suggestions'];
        $totalProcessed = $result['total_processed'];

        // إنشاء تقرير التسوية
        $reconciliationReport = [
            'bank_account_id' => $this->bankAccountId,
            'bank_account_name' => $bankAccount->account_name,
            'period' => [
                'from' => $this->dateFrom,
                'to' => $this->dateTo,
            ],
            'summary' => [
                'total_transactions' => $totalProcessed,
                'matched_automatically' => $matchedCount,
                'requiring_review' => count($suggestions),
                'match_rate' => $totalProcessed > 0 ? round(($matchedCount / $totalProcessed) * 100, 2) : 0,
            ],
            'suggestions' => $suggestions,
            'processed_at' => now(),
        ];

        // حفظ تقرير التسوية
        $this->saveReconciliationReport($reconciliationReport);

        // إرسال إشعارات
        $this->sendNotifications($reconciliationReport, $bankAccount);

        // معالجة الاقتراحات التلقائية إذا كان مفعلاً
        if ($this->autoApprove && !empty($suggestions)) {
            $this->processAutoApprovalSuggestions($suggestions);
        }
    }

    /**
     * حفظ تقرير التسوية
     */
    protected function saveReconciliationReport(array $report): void
    {
        try {
            // يمكن حفظ التقرير في قاعدة البيانات أو ملف
            $reportPath = storage_path("app/reconciliation_reports/bank_{$this->bankAccountId}/" . now()->format('Y-m-d_H-i-s') . '.json');
            
            // إنشاء المجلد إذا لم يكن موجوداً
            $directory = dirname($reportPath);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            Log::info('تم حفظ تقرير التسوية', [
                'report_path' => $reportPath,
                'bank_account_id' => $this->bankAccountId,
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في حفظ تقرير التسوية', [
                'bank_account_id' => $this->bankAccountId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * إرسال الإشعارات
     */
    protected function sendNotifications(array $report, BankAccount $bankAccount): void
    {
        try {
            // الحصول على المستخدمين المخولين
            $notifiableUsers = $this->getNotifiableUsers($bankAccount);

            if (empty($notifiableUsers)) {
                return;
            }

            // إنشاء الإشعار
            $notificationData = [
                'title' => 'تقرير التسوية البنكية التلقائية',
                'message' => $this->buildNotificationMessage($report),
                'type' => 'bank_reconciliation',
                'data' => $report,
            ];

            // إرسال الإشعار
            foreach ($notifiableUsers as $user) {
                // يمكن استخدام نظام الإشعارات في Laravel
                // Notification::send($user, new BankReconciliationNotification($notificationData));
            }

            Log::info('تم إرسال إشعارات التسوية البنكية', [
                'bank_account_id' => $this->bankAccountId,
                'recipients_count' => count($notifiableUsers),
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في إرسال إشعارات التسوية البنكية', [
                'bank_account_id' => $this->bankAccountId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * بناء رسالة الإشعار
     */
    protected function buildNotificationMessage(array $report): string
    {
        $summary = $report['summary'];
        
        $message = "تم إكمال التسوية البنكية التلقائية:\n";
        $message .= "• إجمالي المعاملات: {$summary['total_transactions']}\n";
        $message .= "• تم مطابقتها تلقائياً: {$summary['matched_automatically']}\n";
        $message .= "• تحتاج مراجعة: {$summary['requiring_review']}\n";
        $message .= "• معدل المطابقة: {$summary['match_rate']}%";

        return $message;
    }

    /**
     * الحصول على المستخدمين المخولين للإشعار
     */
    protected function getNotifiableUsers(BankAccount $bankAccount): array
    {
        // يمكن تحديد المستخدمين بناءً على الأذونات أو الأدوار
        // هذا مثال بسيط
        return [];
    }

    /**
     * معالجة اقتراحات الموافقة التلقائية
     */
    protected function processAutoApprovalSuggestions(array $suggestions): void
    {
        $autoApprovedCount = 0;

        foreach ($suggestions as $suggestion) {
            try {
                // التحقق من مستوى الثقة
                if (isset($suggestion['confidence_scores']) && 
                    max($suggestion['confidence_scores']) >= 0.95) { // 95% ثقة
                    
                    // موافقة تلقائية
                    $this->autoApproveSuggestion($suggestion);
                    $autoApprovedCount++;
                }
            } catch (\Exception $e) {
                Log::error('خطأ في الموافقة التلقائية على اقتراح', [
                    'suggestion' => $suggestion,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if ($autoApprovedCount > 0) {
            Log::info('تم اعتماد اقتراحات تلقائياً', [
                'bank_account_id' => $this->bankAccountId,
                'auto_approved_count' => $autoApprovedCount,
            ]);
        }
    }

    /**
     * موافقة تلقائية على اقتراح
     */
    protected function autoApproveSuggestion(array $suggestion): void
    {
        // تنفيذ منطق الموافقة التلقائية
        // هذا يعتمد على هيكل البيانات المحدد
    }

    /**
     * معالجة فشل المهمة
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('فشل في مهمة التسوية البنكية التلقائية', [
            'bank_account_id' => $this->bankAccountId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // إرسال تنبيه للمسؤولين
        // يمكن إضافة إشعار هنا
    }

    /**
     * تحديد عدد الثواني قبل انتهاء مهلة المهمة
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHour();
    }
}
