<?php

namespace App\Domains\ECommerce\Services;

use App\Domains\ECommerce\Models\ECommerceIntegration;
use App\Domains\ECommerce\Models\ECommercePlatform;
use App\Domains\ECommerce\Models\ECommerceStore;
use App\Domains\ECommerce\Models\ECommerceSyncLog;
use App\Domains\ECommerce\Contracts\ECommercePlatformInterface;
use App\Domains\ECommerce\Exceptions\ECommerceIntegrationException;
use App\Domains\ECommerce\Events\IntegrationConnected;
use App\Domains\ECommerce\Events\IntegrationDisconnected;
use App\Domains\ECommerce\Events\SyncStarted;
use App\Domains\ECommerce\Events\SyncCompleted;
use App\Domains\ECommerce\Events\SyncFailed;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * خدمة التكامل مع منصات التجارة الإلكترونية
 * تدير جميع عمليات التكامل والمزامنة
 */
class ECommerceIntegrationService
{
    protected array $platformDrivers = [];
    protected array $config = [];

    public function __construct()
    {
        $this->config = config('ecommerce', []);
        $this->loadPlatformDrivers();
    }

    /**
     * تحميل برامج تشغيل المنصات
     */
    protected function loadPlatformDrivers(): void
    {
        $platforms = $this->config['platforms'] ?? [];
        
        foreach ($platforms as $platform => $config) {
            if (isset($config['driver'])) {
                $this->platformDrivers[$platform] = app($config['driver']);
            }
        }
    }

    /**
     * إنشاء تكامل جديد
     */
    public function createIntegration(array $data): ECommerceIntegration
    {
        DB::beginTransaction();
        
        try {
            // التحقق من صحة البيانات
            $this->validateIntegrationData($data);
            
            // إنشاء التكامل
            $integration = ECommerceIntegration::create([
                'platform_id' => $data['platform_id'],
                'store_id' => $data['store_id'],
                'company_id' => $data['company_id'],
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'connection_config' => $data['connection_config'] ?? [],
                'authentication_config' => $data['authentication_config'] ?? [],
                'sync_config' => $data['sync_config'] ?? [],
                'mapping_config' => $data['mapping_config'] ?? [],
                'transformation_config' => $data['transformation_config'] ?? [],
                'validation_config' => $data['validation_config'] ?? [],
                'error_handling_config' => $data['error_handling_config'] ?? [],
                'retry_config' => $data['retry_config'] ?? [],
                'notification_config' => $data['notification_config'] ?? [],
                'logging_config' => $data['logging_config'] ?? [],
                'monitoring_config' => $data['monitoring_config'] ?? [],
                'performance_config' => $data['performance_config'] ?? [],
                'security_config' => $data['security_config'] ?? [],
                'compliance_config' => $data['compliance_config'] ?? [],
                'sync_frequency' => $data['sync_frequency'] ?? 60,
                'sync_mode' => $data['sync_mode'] ?? 'automatic',
                'sync_direction' => $data['sync_direction'] ?? 'bidirectional',
                'auto_sync_enabled' => $data['auto_sync_enabled'] ?? true,
                'real_time_sync_enabled' => $data['real_time_sync_enabled'] ?? false,
                'batch_sync_enabled' => $data['batch_sync_enabled'] ?? true,
                'webhook_enabled' => $data['webhook_enabled'] ?? true,
                'api_rate_limit' => $data['api_rate_limit'] ?? 1000,
                'enabled_features' => $data['enabled_features'] ?? [],
                'enabled_operations' => $data['enabled_operations'] ?? [],
                'enabled_sync_types' => $data['enabled_sync_types'] ?? [],
                'data_mapping' => $data['data_mapping'] ?? [],
                'field_mapping' => $data['field_mapping'] ?? [],
                'transformation_rules' => $data['transformation_rules'] ?? [],
                'validation_rules' => $data['validation_rules'] ?? [],
                'business_rules' => $data['business_rules'] ?? [],
                'sync_rules' => $data['sync_rules'] ?? [],
                'error_handling_rules' => $data['error_handling_rules'] ?? [],
                'retry_rules' => $data['retry_rules'] ?? [],
                'notification_rules' => $data['notification_rules'] ?? [],
                'is_active' => $data['is_active'] ?? true,
                'is_test_mode' => $data['is_test_mode'] ?? false,
                'is_sandbox' => $data['is_sandbox'] ?? true,
                'is_production' => $data['is_production'] ?? false,
                'created_by' => auth()->id(),
            ]);

            // اختبار الاتصال
            $connectionResult = $this->testConnection($integration);
            
            if ($connectionResult['success']) {
                $integration->update([
                    'is_connected' => true,
                    'connection_status' => 'connected',
                    'health_status' => 'healthy',
                ]);
                
                Event::dispatch(new IntegrationConnected($integration));
            } else {
                $integration->update([
                    'is_connected' => false,
                    'connection_status' => 'failed',
                    'health_status' => 'unhealthy',
                ]);
            }

            DB::commit();
            
            Log::info('E-commerce integration created successfully', [
                'integration_id' => $integration->id,
                'platform' => $integration->platform->name,
                'store' => $integration->store->name,
                'connection_status' => $integration->connection_status,
            ]);

            return $integration;
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create e-commerce integration', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
            
            throw new ECommerceIntegrationException(
                'Failed to create integration: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * اختبار الاتصال مع المنصة
     */
    public function testConnection(ECommerceIntegration $integration): array
    {
        try {
            $driver = $this->getPlatformDriver($integration->platform->slug);
            
            if (!$driver) {
                return [
                    'success' => false,
                    'message' => 'Platform driver not found',
                    'error_code' => 'DRIVER_NOT_FOUND',
                ];
            }

            $result = $driver->testConnection($integration);
            
            // تسجيل نتيجة الاختبار
            $this->logConnectionTest($integration, $result);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Connection test failed', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'CONNECTION_TEST_FAILED',
            ];
        }
    }

    /**
     * بدء عملية المزامنة
     */
    public function startSync(
        ECommerceIntegration $integration,
        string $syncType = 'full',
        array $options = []
    ): ECommerceSyncLog {
        if (!$integration->canSync()) {
            throw new ECommerceIntegrationException('Integration cannot sync at this time');
        }

        $syncLog = $this->createSyncLog($integration, $syncType, $options);
        
        try {
            Event::dispatch(new SyncStarted($integration, $syncLog));
            
            $driver = $this->getPlatformDriver($integration->platform->slug);
            
            if (!$driver) {
                throw new ECommerceIntegrationException('Platform driver not found');
            }

            // تحديث حالة التكامل
            $integration->update([
                'is_syncing' => true,
                'sync_status' => 'running',
                'last_sync_at' => now(),
            ]);

            // تنفيذ المزامنة
            $result = $driver->sync($integration, $syncType, $options);
            
            // تحديث سجل المزامنة
            $this->updateSyncLog($syncLog, $result);
            
            // تحديث حالة التكامل
            $this->updateIntegrationAfterSync($integration, $result);
            
            Event::dispatch(new SyncCompleted($integration, $syncLog, $result));
            
            return $syncLog;
            
        } catch (\Exception $e) {
            $this->handleSyncError($integration, $syncLog, $e);
            
            Event::dispatch(new SyncFailed($integration, $syncLog, $e));
            
            throw $e;
        }
    }

    /**
     * مزامنة المنتجات
     */
    public function syncProducts(
        ECommerceIntegration $integration,
        array $options = []
    ): array {
        $driver = $this->getPlatformDriver($integration->platform->slug);
        
        if (!$driver) {
            throw new ECommerceIntegrationException('Platform driver not found');
        }

        return $driver->syncProducts($integration, $options);
    }

    /**
     * مزامنة الطلبات
     */
    public function syncOrders(
        ECommerceIntegration $integration,
        array $options = []
    ): array {
        $driver = $this->getPlatformDriver($integration->platform->slug);
        
        if (!$driver) {
            throw new ECommerceIntegrationException('Platform driver not found');
        }

        return $driver->syncOrders($integration, $options);
    }

    /**
     * مزامنة العملاء
     */
    public function syncCustomers(
        ECommerceIntegration $integration,
        array $options = []
    ): array {
        $driver = $this->getPlatformDriver($integration->platform->slug);
        
        if (!$driver) {
            throw new ECommerceIntegrationException('Platform driver not found');
        }

        return $driver->syncCustomers($integration, $options);
    }

    /**
     * معالجة webhook
     */
    public function processWebhook(
        ECommerceIntegration $integration,
        array $payload,
        array $headers = []
    ): array {
        $driver = $this->getPlatformDriver($integration->platform->slug);
        
        if (!$driver) {
            throw new ECommerceIntegrationException('Platform driver not found');
        }

        return $driver->processWebhook($integration, $payload, $headers);
    }

    /**
     * الحصول على برنامج تشغيل المنصة
     */
    protected function getPlatformDriver(string $platform): ?ECommercePlatformInterface
    {
        return $this->platformDrivers[$platform] ?? null;
    }

    /**
     * التحقق من صحة بيانات التكامل
     */
    protected function validateIntegrationData(array $data): void
    {
        $required = ['platform_id', 'store_id', 'company_id', 'name'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ECommerceIntegrationException("Field {$field} is required");
            }
        }

        // التحقق من وجود المنصة
        $platform = ECommercePlatform::find($data['platform_id']);
        if (!$platform) {
            throw new ECommerceIntegrationException('Platform not found');
        }

        // التحقق من وجود المتجر
        $store = ECommerceStore::find($data['store_id']);
        if (!$store) {
            throw new ECommerceIntegrationException('Store not found');
        }

        // التحقق من توافق المنصة والمتجر
        if ($store->platform_id !== $platform->id) {
            throw new ECommerceIntegrationException('Store and platform mismatch');
        }
    }

    /**
     * إنشاء سجل مزامنة
     */
    protected function createSyncLog(
        ECommerceIntegration $integration,
        string $syncType,
        array $options
    ): ECommerceSyncLog {
        return ECommerceSyncLog::create([
            'integration_id' => $integration->id,
            'store_id' => $integration->store_id,
            'platform_id' => $integration->platform_id,
            'company_id' => $integration->company_id,
            'sync_type' => $syncType,
            'sync_direction' => $options['direction'] ?? 'pull',
            'sync_mode' => $options['mode'] ?? 'automatic',
            'entity_type' => $options['entity_type'] ?? 'all',
            'operation' => $options['operation'] ?? 'sync',
            'status' => 'running',
            'started_at' => now(),
            'context' => $options,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * تحديث سجل المزامنة
     */
    protected function updateSyncLog(ECommerceSyncLog $syncLog, array $result): void
    {
        $syncLog->update([
            'status' => $result['success'] ? 'completed' : 'failed',
            'completed_at' => now(),
            'duration' => now()->diffInSeconds($syncLog->started_at),
            'records_total' => $result['total'] ?? 0,
            'records_processed' => $result['processed'] ?? 0,
            'records_successful' => $result['successful'] ?? 0,
            'records_failed' => $result['failed'] ?? 0,
            'records_skipped' => $result['skipped'] ?? 0,
            'records_created' => $result['created'] ?? 0,
            'records_updated' => $result['updated'] ?? 0,
            'records_deleted' => $result['deleted'] ?? 0,
            'success_rate' => $this->calculateSuccessRate($result),
            'failure_rate' => $this->calculateFailureRate($result),
            'skip_rate' => $this->calculateSkipRate($result),
            'throughput' => $this->calculateThroughput($result, $syncLog),
            'response_data' => $result['data'] ?? [],
            'error_data' => $result['errors'] ?? [],
            'warning_data' => $result['warnings'] ?? [],
            'debug_data' => $result['debug'] ?? [],
            'is_successful' => $result['success'] ?? false,
            'is_failed' => !($result['success'] ?? false),
            'is_partial' => ($result['partial'] ?? false),
        ]);
    }

    /**
     * تحديث التكامل بعد المزامنة
     */
    protected function updateIntegrationAfterSync(
        ECommerceIntegration $integration,
        array $result
    ): void {
        $updates = [
            'is_syncing' => false,
            'sync_status' => $result['success'] ? 'completed' : 'failed',
        ];

        if ($result['success']) {
            $updates['last_successful_sync_at'] = now();
            $updates['next_sync_at'] = $this->calculateNextSyncTime($integration);
        } else {
            $updates['last_failed_sync_at'] = now();
        }

        $integration->update($updates);
    }

    /**
     * معالجة خطأ المزامنة
     */
    protected function handleSyncError(
        ECommerceIntegration $integration,
        ECommerceSyncLog $syncLog,
        \Exception $e
    ): void {
        $syncLog->update([
            'status' => 'failed',
            'completed_at' => now(),
            'duration' => now()->diffInSeconds($syncLog->started_at),
            'error_data' => [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ],
            'is_failed' => true,
            'is_successful' => false,
        ]);

        $integration->update([
            'is_syncing' => false,
            'sync_status' => 'failed',
            'last_failed_sync_at' => now(),
        ]);

        Log::error('Sync failed', [
            'integration_id' => $integration->id,
            'sync_log_id' => $syncLog->id,
            'error' => $e->getMessage(),
        ]);
    }

    /**
     * تسجيل اختبار الاتصال
     */
    protected function logConnectionTest(
        ECommerceIntegration $integration,
        array $result
    ): void {
        Log::info('Connection test completed', [
            'integration_id' => $integration->id,
            'platform' => $integration->platform->name,
            'success' => $result['success'],
            'message' => $result['message'] ?? null,
        ]);
    }

    /**
     * حساب معدل النجاح
     */
    protected function calculateSuccessRate(array $result): float
    {
        $total = $result['total'] ?? 0;
        $successful = $result['successful'] ?? 0;
        
        return $total > 0 ? ($successful / $total) * 100 : 0;
    }

    /**
     * حساب معدل الفشل
     */
    protected function calculateFailureRate(array $result): float
    {
        $total = $result['total'] ?? 0;
        $failed = $result['failed'] ?? 0;
        
        return $total > 0 ? ($failed / $total) * 100 : 0;
    }

    /**
     * حساب معدل التخطي
     */
    protected function calculateSkipRate(array $result): float
    {
        $total = $result['total'] ?? 0;
        $skipped = $result['skipped'] ?? 0;
        
        return $total > 0 ? ($skipped / $total) * 100 : 0;
    }

    /**
     * حساب الإنتاجية
     */
    protected function calculateThroughput(array $result, ECommerceSyncLog $syncLog): float
    {
        $processed = $result['processed'] ?? 0;
        $duration = now()->diffInSeconds($syncLog->started_at);
        
        return $duration > 0 ? $processed / $duration : 0;
    }

    /**
     * حساب وقت المزامنة التالية
     */
    protected function calculateNextSyncTime(ECommerceIntegration $integration): Carbon
    {
        $frequency = $integration->sync_frequency ?? 60; // minutes
        return now()->addMinutes($frequency);
    }

    /**
     * الحصول على إحصائيات التكامل
     */
    public function getIntegrationStats(ECommerceIntegration $integration): array
    {
        $cacheKey = "integration_stats_{$integration->id}";
        
        return Cache::remember($cacheKey, 300, function () use ($integration) {
            $syncLogs = $integration->syncLogs()
                ->where('created_at', '>=', now()->subDays(30))
                ->get();

            $totalSyncs = $syncLogs->count();
            $successfulSyncs = $syncLogs->where('is_successful', true)->count();
            $failedSyncs = $syncLogs->where('is_failed', true)->count();
            
            $avgDuration = $syncLogs->avg('duration') ?? 0;
            $avgThroughput = $syncLogs->avg('throughput') ?? 0;
            $avgSuccessRate = $syncLogs->avg('success_rate') ?? 0;

            return [
                'total_syncs' => $totalSyncs,
                'successful_syncs' => $successfulSyncs,
                'failed_syncs' => $failedSyncs,
                'success_rate' => $totalSyncs > 0 ? ($successfulSyncs / $totalSyncs) * 100 : 0,
                'failure_rate' => $totalSyncs > 0 ? ($failedSyncs / $totalSyncs) * 100 : 0,
                'avg_duration' => round($avgDuration, 2),
                'avg_throughput' => round($avgThroughput, 2),
                'avg_success_rate' => round($avgSuccessRate, 2),
                'health_score' => $integration->getHealthScore(),
                'last_sync' => $integration->last_sync_at,
                'last_successful_sync' => $integration->last_successful_sync_at,
                'last_failed_sync' => $integration->last_failed_sync_at,
                'next_sync' => $integration->next_sync_at,
                'api_usage' => $integration->getApiUsage(),
            ];
        });
    }

    /**
     * تنظيف البيانات القديمة
     */
    public function cleanup(ECommerceIntegration $integration, int $days = 30): int
    {
        $deletedCount = 0;
        
        // حذف سجلات المزامنة القديمة
        $deletedCount += $integration->syncLogs()
            ->where('created_at', '<', now()->subDays($days))
            ->delete();

        // حذف webhooks القديمة
        $deletedCount += $integration->webhooks()
            ->where('created_at', '<', now()->subDays($days))
            ->delete();

        Log::info('Integration cleanup completed', [
            'integration_id' => $integration->id,
            'deleted_records' => $deletedCount,
            'days' => $days,
        ]);

        return $deletedCount;
    }
}
