<?php

namespace App\Domains\CRM\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Shared\Traits\HasUuid;

/**
 * نموذج قالب الحملة التسويقية - Campaign Template
 * قوالب جاهزة للحملات التسويقية
 */
class CampaignTemplate extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'name',
        'description',
        'category',
        'type',
        'channel',
        'language',
        'subject',
        'content',
        'html_content',
        'variables',
        'design_settings',
        'is_active',
        'is_default',
        'usage_count',
        'created_by',
        'tags',
        'metadata',
    ];

    protected $casts = [
        'variables' => 'array',
        'design_settings' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'usage_count' => 'integer',
        'tags' => 'array',
        'metadata' => 'array',
    ];

    /**
     * فئات القوالب
     */
    const CATEGORIES = [
        'welcome' => 'ترحيب',
        'promotional' => 'ترويجية',
        'transactional' => 'معاملات',
        'newsletter' => 'نشرة إخبارية',
        'event' => 'فعاليات',
        'follow_up' => 'متابعة',
        'birthday' => 'أعياد ميلاد',
        'seasonal' => 'موسمية',
        'product_launch' => 'إطلاق منتج',
        'feedback' => 'تعليقات',
        'reactivation' => 'إعادة تفعيل',
        'upsell' => 'بيع إضافي',
        'cross_sell' => 'بيع متقاطع',
        'renewal' => 'تجديد',
        'win_back' => 'استرداد',
        'other' => 'أخرى',
    ];

    /**
     * أنواع القوالب
     */
    const TYPES = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسالة نصية',
        'whatsapp' => 'واتساب',
        'push_notification' => 'إشعار فوري',
        'social_media' => 'وسائل التواصل',
        'landing_page' => 'صفحة هبوط',
        'popup' => 'نافذة منبثقة',
        'banner' => 'بانر',
    ];

    /**
     * قنوات القوالب
     */
    const CHANNELS = [
        'email' => 'بريد إلكتروني',
        'sms' => 'رسائل نصية',
        'whatsapp' => 'واتساب',
        'facebook' => 'فيسبوك',
        'instagram' => 'إنستجرام',
        'linkedin' => 'لينكد إن',
        'twitter' => 'تويتر',
        'website' => 'موقع إلكتروني',
        'mobile_app' => 'تطبيق جوال',
    ];

    /**
     * اللغات المدعومة
     */
    const LANGUAGES = [
        'ar' => 'العربية',
        'en' => 'English',
        'fr' => 'Français',
        'es' => 'Español',
        'de' => 'Deutsch',
    ];

    /**
     * العلاقة مع منشئ القالب
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Domains\HR\Models\Employee::class, 'created_by');
    }

    /**
     * العلاقة مع الحملات التي تستخدم هذا القالب
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(MarketingCampaign::class, 'template_id');
    }

    /**
     * الحصول على تسمية الفئة
     */
    public function getCategoryLabelAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية النوع
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية القناة
     */
    public function getChannelLabelAttribute(): string
    {
        return self::CHANNELS[$this->channel] ?? 'غير محدد';
    }

    /**
     * الحصول على تسمية اللغة
     */
    public function getLanguageLabelAttribute(): string
    {
        return self::LANGUAGES[$this->language] ?? 'غير محدد';
    }

    /**
     * التحقق من كون القالب نشط
     */
    public function getIsActiveTemplateAttribute(): bool
    {
        return $this->is_active;
    }

    /**
     * التحقق من كون القالب افتراضي
     */
    public function getIsDefaultTemplateAttribute(): bool
    {
        return $this->is_default;
    }

    /**
     * الحصول على المتغيرات المتاحة
     */
    public function getAvailableVariablesAttribute(): array
    {
        $defaultVariables = [
            'customer_name' => 'اسم العميل',
            'customer_first_name' => 'الاسم الأول',
            'customer_last_name' => 'اسم العائلة',
            'customer_email' => 'البريد الإلكتروني',
            'customer_phone' => 'رقم الهاتف',
            'company_name' => 'اسم الشركة',
            'current_date' => 'التاريخ الحالي',
            'current_time' => 'الوقت الحالي',
            'unsubscribe_link' => 'رابط إلغاء الاشتراك',
            'website_url' => 'رابط الموقع',
        ];

        return array_merge($defaultVariables, $this->variables ?? []);
    }

    /**
     * الحصول على معاينة القالب
     */
    public function getPreviewAttribute(): string
    {
        $content = $this->html_content ?: $this->content;
        
        // استبدال المتغيرات بقيم تجريبية
        $sampleData = [
            '{{customer_name}}' => 'أحمد محمد',
            '{{customer_first_name}}' => 'أحمد',
            '{{customer_last_name}}' => 'محمد',
            '{{customer_email}}' => '<EMAIL>',
            '{{customer_phone}}' => '+966501234567',
            '{{company_name}}' => 'شركة المثال',
            '{{current_date}}' => now()->format('Y-m-d'),
            '{{current_time}}' => now()->format('H:i'),
            '{{website_url}}' => 'https://example.com',
            '{{unsubscribe_link}}' => '#unsubscribe',
        ];

        return str_replace(array_keys($sampleData), array_values($sampleData), $content);
    }

    /**
     * الحصول على إحصائيات الاستخدام
     */
    public function getUsageStatsAttribute(): array
    {
        return [
            'total_campaigns' => $this->campaigns()->count(),
            'active_campaigns' => $this->campaigns()->active()->count(),
            'completed_campaigns' => $this->campaigns()->completed()->count(),
            'total_recipients' => $this->campaigns()->sum('recipients_count'),
            'average_open_rate' => $this->campaigns()->avg('open_rate'),
            'average_click_rate' => $this->campaigns()->avg('click_rate'),
        ];
    }

    /**
     * إنشاء حملة من القالب
     */
    public function createCampaign(array $campaignData): MarketingCampaign
    {
        $campaignData = array_merge([
            'template_id' => $this->id,
            'type' => $this->type,
            'channel' => $this->channel,
            'subject' => $this->subject,
            'content' => $this->content,
            'html_content' => $this->html_content,
            'language' => $this->language,
        ], $campaignData);

        $campaign = MarketingCampaign::create($campaignData);

        // زيادة عداد الاستخدام
        $this->increment('usage_count');

        return $campaign;
    }

    /**
     * تخصيص المحتوى للعميل
     */
    public function personalizeContent(Customer $customer): array
    {
        $personalizedSubject = $this->personalizeText($this->subject, $customer);
        $personalizedContent = $this->personalizeText($this->content, $customer);
        $personalizedHtmlContent = $this->personalizeText($this->html_content, $customer);

        return [
            'subject' => $personalizedSubject,
            'content' => $personalizedContent,
            'html_content' => $personalizedHtmlContent,
        ];
    }

    /**
     * تخصيص النص
     */
    protected function personalizeText(?string $text, Customer $customer): ?string
    {
        if (!$text) {
            return $text;
        }

        $variables = [
            '{{customer_name}}' => $customer->full_name,
            '{{customer_first_name}}' => $customer->first_name,
            '{{customer_last_name}}' => $customer->last_name,
            '{{customer_email}}' => $customer->email,
            '{{customer_phone}}' => $customer->phone,
            '{{company_name}}' => $customer->company_name,
            '{{customer_tier}}' => $customer->tier_label,
            '{{total_spent}}' => number_format($customer->total_spent, 2),
            '{{last_purchase_date}}' => $customer->last_purchase_at?->format('Y-m-d'),
            '{{current_date}}' => now()->format('Y-m-d'),
            '{{current_time}}' => now()->format('H:i'),
            '{{website_url}}' => config('app.url'),
            '{{unsubscribe_link}}' => route('unsubscribe', ['token' => $customer->unsubscribe_token]),
        ];

        // إضافة متغيرات مخصصة
        if ($customer->custom_fields) {
            foreach ($customer->custom_fields as $key => $value) {
                $variables["{{custom_{$key}}}"] = $value;
            }
        }

        return str_replace(array_keys($variables), array_values($variables), $text);
    }

    /**
     * استنساخ القالب
     */
    public function duplicate(string $newName = null): CampaignTemplate
    {
        $newTemplate = $this->replicate();
        $newTemplate->name = $newName ?: $this->name . ' - نسخة';
        $newTemplate->is_default = false;
        $newTemplate->usage_count = 0;
        $newTemplate->created_by = auth()->id();
        $newTemplate->save();

        return $newTemplate;
    }

    /**
     * تعيين كقالب افتراضي
     */
    public function setAsDefault(): bool
    {
        // إزالة الافتراضية من القوالب الأخرى من نفس النوع والفئة
        self::where('type', $this->type)
            ->where('category', $this->category)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        return $this->update(['is_default' => true]);
    }

    /**
     * تحديث إعدادات التصميم
     */
    public function updateDesignSettings(array $settings): bool
    {
        return $this->update([
            'design_settings' => array_merge($this->design_settings ?? [], $settings)
        ]);
    }

    /**
     * التحقق من صحة القالب
     */
    public function validate(): array
    {
        $errors = [];

        // التحقق من وجود المحتوى
        if (empty($this->content) && empty($this->html_content)) {
            $errors[] = 'يجب أن يحتوي القالب على محتوى نصي أو HTML';
        }

        // التحقق من المتغيرات
        $content = $this->content . ' ' . $this->html_content;
        preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
        
        $usedVariables = $matches[1] ?? [];
        $availableVariables = array_keys($this->available_variables);
        
        foreach ($usedVariables as $variable) {
            if (!in_array($variable, $availableVariables)) {
                $errors[] = "متغير غير معرف: {{$variable}}";
            }
        }

        // التحقق من صحة HTML
        if ($this->html_content && $this->type === 'email') {
            if (!$this->isValidHTML($this->html_content)) {
                $errors[] = 'محتوى HTML غير صحيح';
            }
        }

        return $errors;
    }

    /**
     * التحقق من صحة HTML
     */
    protected function isValidHTML(string $html): bool
    {
        libxml_use_internal_errors(true);
        $doc = new \DOMDocument();
        $result = $doc->loadHTML($html);
        libxml_clear_errors();
        
        return $result !== false;
    }

    /**
     * فلترة القوالب النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة القوالب الافتراضية
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * فلترة حسب الفئة
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب القناة
     */
    public function scopeOfChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * فلترة حسب اللغة
     */
    public function scopeOfLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * ترتيب حسب الاستخدام
     */
    public function scopeOrderByUsage($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    /**
     * البحث في القوالب
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('subject', 'LIKE', "%{$search}%")
              ->orWhere('content', 'LIKE', "%{$search}%");
        });
    }
}
