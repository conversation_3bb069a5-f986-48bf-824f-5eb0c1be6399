<?php

namespace App\Domains\HR\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Domains\Shared\Traits\HasUuid;
use App\Domains\Shared\Traits\Auditable;

/**
 * نموذج أنواع الإجازات
 * إدارة شاملة لأنواع الإجازات مع دعم القوانين المحلية
 */
class LeaveType extends Model
{
    use HasFactory, SoftDeletes, HasUuid, Auditable;

    protected $fillable = [
        'name',
        'name_en',
        'code',
        'description',
        'category',
        'color',
        'icon',
        
        // Entitlement Rules
        'default_days_per_year',
        'max_days_per_year',
        'min_days_per_request',
        'max_days_per_request',
        'max_consecutive_days',
        'carry_forward_days',
        'carry_forward_expiry_months',
        
        // Eligibility Rules
        'minimum_service_months',
        'probation_eligible',
        'gender_restriction',
        'marital_status_restriction',
        'age_restriction_min',
        'age_restriction_max',
        'department_restriction',
        'position_restriction',
        
        // Approval Rules
        'requires_manager_approval',
        'requires_hr_approval',
        'requires_medical_certificate',
        'requires_supporting_documents',
        'auto_approve_threshold_days',
        'advance_notice_days',
        'emergency_allowed',
        
        // Financial Rules
        'is_paid',
        'salary_percentage',
        'affects_bonus',
        'affects_allowances',
        'affects_overtime',
        'deduct_from_salary',
        
        // Scheduling Rules
        'weekend_included',
        'holiday_included',
        'half_day_allowed',
        'hourly_leave_allowed',
        'can_be_split',
        'blackout_periods',
        'seasonal_restrictions',
        
        // Legal Compliance
        'labor_law_reference',
        'country_specific',
        'region_specific',
        'mandatory_leave',
        'statutory_leave',
        'religious_leave',
        
        // System Configuration
        'is_active',
        'sort_order',
        'effective_from',
        'effective_to',
        'created_by',
        'updated_by',
        'metadata',
    ];

    protected $casts = [
        'effective_from' => 'date',
        'effective_to' => 'date',
        'default_days_per_year' => 'decimal:1',
        'max_days_per_year' => 'decimal:1',
        'min_days_per_request' => 'decimal:1',
        'max_days_per_request' => 'decimal:1',
        'max_consecutive_days' => 'decimal:1',
        'carry_forward_days' => 'decimal:1',
        'carry_forward_expiry_months' => 'integer',
        'minimum_service_months' => 'integer',
        'age_restriction_min' => 'integer',
        'age_restriction_max' => 'integer',
        'advance_notice_days' => 'integer',
        'auto_approve_threshold_days' => 'integer',
        'salary_percentage' => 'decimal:2',
        'sort_order' => 'integer',
        'probation_eligible' => 'boolean',
        'requires_manager_approval' => 'boolean',
        'requires_hr_approval' => 'boolean',
        'requires_medical_certificate' => 'boolean',
        'requires_supporting_documents' => 'boolean',
        'emergency_allowed' => 'boolean',
        'is_paid' => 'boolean',
        'affects_bonus' => 'boolean',
        'affects_allowances' => 'boolean',
        'affects_overtime' => 'boolean',
        'deduct_from_salary' => 'boolean',
        'weekend_included' => 'boolean',
        'holiday_included' => 'boolean',
        'half_day_allowed' => 'boolean',
        'hourly_leave_allowed' => 'boolean',
        'can_be_split' => 'boolean',
        'mandatory_leave' => 'boolean',
        'statutory_leave' => 'boolean',
        'religious_leave' => 'boolean',
        'is_active' => 'boolean',
        'blackout_periods' => 'array',
        'seasonal_restrictions' => 'array',
        'department_restriction' => 'array',
        'position_restriction' => 'array',
        'metadata' => 'array',
    ];

    /**
     * فئات الإجازات
     */
    const CATEGORIES = [
        'ANNUAL' => 'إجازة سنوية',
        'SICK' => 'إجازة مرضية',
        'MATERNITY' => 'إجازة أمومة',
        'PATERNITY' => 'إجازة أبوة',
        'EMERGENCY' => 'إجازة طارئة',
        'BEREAVEMENT' => 'إجازة وفاة',
        'STUDY' => 'إجازة دراسية',
        'HAJJ' => 'إجازة حج',
        'MARRIAGE' => 'إجازة زواج',
        'UNPAID' => 'إجازة بدون راتب',
        'SABBATICAL' => 'إجازة تفرغ',
        'COMPENSATORY' => 'إجازة تعويضية',
        'RELIGIOUS' => 'إجازة دينية',
        'MILITARY' => 'إجازة عسكرية',
        'JURY_DUTY' => 'إجازة محلفين',
        'VOLUNTEER' => 'إجازة تطوع',
        'OTHER' => 'أخرى',
    ];

    /**
     * قيود الجنس
     */
    const GENDER_RESTRICTIONS = [
        'MALE' => 'ذكور فقط',
        'FEMALE' => 'إناث فقط',
        'ALL' => 'الجميع',
    ];

    /**
     * قيود الحالة الاجتماعية
     */
    const MARITAL_STATUS_RESTRICTIONS = [
        'SINGLE' => 'أعزب',
        'MARRIED' => 'متزوج',
        'DIVORCED' => 'مطلق',
        'WIDOWED' => 'أرمل',
        'ALL' => 'الجميع',
    ];

    /**
     * طلبات الإجازة
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * أرصدة الإجازات
     */
    public function leaveBalances(): HasMany
    {
        return $this->hasMany(LeaveBalance::class);
    }

    /**
     * التحقق من أهلية الموظف لهذا النوع من الإجازة
     */
    public function isEmployeeEligible(Employee $employee): bool
    {
        // التحقق من فترة الخدمة
        if ($this->minimum_service_months) {
            $serviceMonths = $employee->hire_date->diffInMonths(now());
            if ($serviceMonths < $this->minimum_service_months) {
                return false;
            }
        }

        // التحقق من فترة التجربة
        if (!$this->probation_eligible && $employee->isInProbationPeriod()) {
            return false;
        }

        // التحقق من قيود الجنس
        if ($this->gender_restriction && $this->gender_restriction !== 'ALL') {
            if ($employee->gender !== strtolower($this->gender_restriction)) {
                return false;
            }
        }

        // التحقق من قيود الحالة الاجتماعية
        if ($this->marital_status_restriction && $this->marital_status_restriction !== 'ALL') {
            if ($employee->marital_status !== strtolower($this->marital_status_restriction)) {
                return false;
            }
        }

        // التحقق من قيود العمر
        if ($this->age_restriction_min || $this->age_restriction_max) {
            $age = $employee->birth_date ? $employee->birth_date->age : null;
            if ($age) {
                if ($this->age_restriction_min && $age < $this->age_restriction_min) {
                    return false;
                }
                if ($this->age_restriction_max && $age > $this->age_restriction_max) {
                    return false;
                }
            }
        }

        // التحقق من قيود القسم
        if ($this->department_restriction && !empty($this->department_restriction)) {
            if (!in_array($employee->department_id, $this->department_restriction)) {
                return false;
            }
        }

        // التحقق من قيود المنصب
        if ($this->position_restriction && !empty($this->position_restriction)) {
            if (!in_array($employee->position_id, $this->position_restriction)) {
                return false;
            }
        }

        return true;
    }

    /**
     * حساب الأيام المستحقة للموظف
     */
    public function calculateEntitlement(Employee $employee): float
    {
        if (!$this->isEmployeeEligible($employee)) {
            return 0;
        }

        $entitlement = $this->default_days_per_year;

        // تعديل الاستحقاق حسب فترة الخدمة (يمكن تطوير هذا)
        $serviceYears = $employee->hire_date->diffInYears(now());
        
        // مثال: زيادة يوم واحد كل سنة خدمة (حتى الحد الأقصى)
        if ($this->category === 'ANNUAL') {
            $entitlement = min($entitlement + $serviceYears, $this->max_days_per_year);
        }

        return $entitlement;
    }

    /**
     * التحقق من إمكانية طلب الإجازة في التاريخ المحدد
     */
    public function canRequestOnDate(\Carbon\Carbon $date): bool
    {
        // التحقق من فترات المنع
        if ($this->blackout_periods) {
            foreach ($this->blackout_periods as $period) {
                $start = \Carbon\Carbon::parse($period['start']);
                $end = \Carbon\Carbon::parse($period['end']);
                if ($date->between($start, $end)) {
                    return false;
                }
            }
        }

        // التحقق من القيود الموسمية
        if ($this->seasonal_restrictions) {
            $month = $date->month;
            if (isset($this->seasonal_restrictions['blocked_months']) && 
                in_array($month, $this->seasonal_restrictions['blocked_months'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * حساب التأثير المالي للإجازة
     */
    public function calculateFinancialImpact(Employee $employee, float $days): array
    {
        $impact = [
            'salary_impact' => 0,
            'allowance_impact' => 0,
            'bonus_impact' => 0,
            'total_impact' => 0,
        ];

        if (!$this->is_paid) {
            $dailySalary = $employee->contract->basic_salary / 30;
            $impact['salary_impact'] = $dailySalary * $days * ($this->salary_percentage / 100);
        }

        if ($this->affects_allowances) {
            $dailyAllowances = $employee->contract->total_allowances / 30;
            $impact['allowance_impact'] = $dailyAllowances * $days;
        }

        if ($this->affects_bonus) {
            // حساب تأثير المكافآت (يمكن تطوير هذا حسب سياسة الشركة)
        }

        $impact['total_impact'] = $impact['salary_impact'] + 
                                 $impact['allowance_impact'] + 
                                 $impact['bonus_impact'];

        return $impact;
    }

    /**
     * الحصول على متطلبات الموافقة
     */
    public function getApprovalRequirements(float $requestedDays): array
    {
        $requirements = [];

        if ($this->requires_manager_approval) {
            $requirements[] = 'manager_approval';
        }

        if ($this->requires_hr_approval) {
            $requirements[] = 'hr_approval';
        }

        if ($this->requires_medical_certificate) {
            $requirements[] = 'medical_certificate';
        }

        if ($this->requires_supporting_documents) {
            $requirements[] = 'supporting_documents';
        }

        // موافقة تلقائية للطلبات الصغيرة
        if ($this->auto_approve_threshold_days && $requestedDays <= $this->auto_approve_threshold_days) {
            $requirements = ['auto_approve'];
        }

        return $requirements;
    }

    /**
     * إنشاء رصيد إجازة للموظف
     */
    public function createBalanceForEmployee(Employee $employee): LeaveBalance
    {
        $entitlement = $this->calculateEntitlement($employee);

        return LeaveBalance::create([
            'employee_id' => $employee->id,
            'leave_type_id' => $this->id,
            'year' => now()->year,
            'entitled_days' => $entitlement,
            'used_days' => 0,
            'remaining_days' => $entitlement,
            'carried_forward_days' => 0,
            'expires_at' => now()->endOfYear(),
        ]);
    }

    /**
     * نطاق للأنواع النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('effective_to')
                          ->orWhere('effective_to', '>=', now());
                    });
    }

    /**
     * نطاق حسب الفئة
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * نطاق للإجازات المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->where('is_paid', true);
    }

    /**
     * نطاق للإجازات غير المدفوعة
     */
    public function scopeUnpaid($query)
    {
        return $query->where('is_paid', false);
    }

    /**
     * نطاق للإجازات الإلزامية
     */
    public function scopeMandatory($query)
    {
        return $query->where('mandatory_leave', true);
    }

    /**
     * نطاق للإجازات القانونية
     */
    public function scopeStatutory($query)
    {
        return $query->where('statutory_leave', true);
    }

    /**
     * نطاق حسب الجنس
     */
    public function scopeForGender($query, string $gender)
    {
        return $query->where(function ($q) use ($gender) {
            $q->where('gender_restriction', 'ALL')
              ->orWhere('gender_restriction', strtoupper($gender));
        });
    }

    /**
     * نطاق للموظفين في فترة التجربة
     */
    public function scopeProbationEligible($query)
    {
        return $query->where('probation_eligible', true);
    }
}
